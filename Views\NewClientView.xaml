<UserControl
    x:Class="UFU2.Views.NewClientView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:newClient="clr-namespace:UFU2.Views.NewClient"
    d:DesignHeight="700"
    d:DesignWidth="900"
    AutomationProperties.HelpText="New client creation dialog"
    AutomationProperties.ItemType="Dialog"
    AutomationProperties.Name="New Client Dialog"
    FlowDirection="RightToLeft"
    Focusable="True"
    IsTabStop="True"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  Built-in WPF BooleanToVisibilityConverter  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!--  Custom InverseBooleanToVisibilityConverter  -->
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />

            <!--  Custom StringToVisibilityConverter  -->
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <!--  Nested DialogHost for sub-dialogs like PhoneNumbersEditor  -->
    <materialDesign:DialogHost
        materialDesign:TransitionAssist.DisableTransitions="True"
        CloseOnClickAway="False"
        Identifier="NewClientDialogHost"
        IsTabStop="False">
        <Grid Background="{DynamicResource SurfaceBrightBrush}">

            <!--  Main dialog card  -->
            <materialDesign:Card
                x:Name="MainCard"
                MinWidth="800"
                MinHeight="400"
                MaxHeight="785"
                Style="{StaticResource DialogBaseCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  Header section  -->
                    <materialDesign:Card
                        Grid.Row="0"
                        Margin="12,0,12,8"
                        Style="{DynamicResource HeaderCardStyle}">
                        <TextBlock
                            HorizontalAlignment="Right"
                            Style="{StaticResource HeadlineStyle}"
                            Text="إضافة عميل جديد" />
                    </materialDesign:Card>

                    <!--  Close button  -->
                    <Border
                        Grid.Row="0"
                        Width="40"
                        Height="40"
                        Margin="-5,-10"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Top"
                        Background="{DynamicResource BackBorderBackground}"
                        CornerRadius="20">
                        <Button
                            x:Name="CloseButton"
                            Command="{Binding CloseCommand}"
                            Style="{StaticResource BackButtonStyle}"
                            ToolTip="إغلاق النافذة">
                            <materialDesign:PackIcon Kind="ArrowRightBold" />
                        </Button>
                    </Border>

                    <!--  Main Content Area with ScrollViewer  -->
                    <ScrollViewer
                        Grid.Row="1"
                        Margin="12,0,12,8"
                        AutomationProperties.HelpText="Scrollable content area for client information"
                        AutomationProperties.Name="Content Area"
                        HorizontalScrollBarVisibility="Disabled"
                        Style="{StaticResource ContentScrollViewerStyle}"
                        VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!--  Personal Information Form Content  -->
                            <newClient:NPersonalView x:Name="PersonalInfoContentControl" />
                            <!--  Activity Tab Section  -->
                            <newClient:NActivityTabView x:Name="ActivityTabView" />
                            <!--  Activity Detail Form  -->
                            <newClient:NActivityDetailView x:Name="ActivityDetailView" />

                            <!--  File Check Information Form Content  -->
                            <newClient:NFileCheckView x:Name="FileCheckContentControl" />

                        </StackPanel>
                    </ScrollViewer>

                    <!--  Action Buttons  -->
                    <materialDesign:Card
                        Grid.Row="2"
                        Margin="12,8,12,0"
                        Style="{DynamicResource FooterCardStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  Action Buttons (Right side in RTL)  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">
                                <Button
                                    Command="{Binding SaveClientCommand}"
                                    IsEnabled="{Binding CanSave}"
                                    Style="{StaticResource PrimaryButtonStyle}">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon
                                                Margin="0,0,8,0"
                                                VerticalAlignment="Center"
                                                Kind="ContentSave"
                                                Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />
                                            <ProgressBar
                                                x:Name="LoadingSpinner"
                                                Width="16"
                                                Height="16"
                                                Margin="0,0,8,0"
                                                IsIndeterminate="True"
                                                Maximum="100"
                                                Minimum="0"
                                                Style="{StaticResource MaterialDesignCircularProgressBar}"
                                                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                Value="0" />
                                            <TextBlock VerticalAlignment="Center" Text="حفظ" />

                                        </StackPanel>
                                    </Button.Content>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </materialDesign:DialogHost>
</UserControl>
