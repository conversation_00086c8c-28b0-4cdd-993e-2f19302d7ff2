using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using UFU2.Common;

namespace UFU2.Controls
{
    /// <summary>
    /// Simplified crop rectangle control that displays a fixed-position visual crop guide with enhanced corner markers.
    /// Non-interactive control that allows mouse events to pass through to underlying elements for image dragging.
    /// Follows UFU2 patterns with comprehensive error handling and Arabic RTL support.
    /// </summary>
    public class InteractiveCropRectangle : Border
    {
        #region Dependency Properties

        /// <summary>
        /// Dependency property for the crop rectangle bounds
        /// </summary>
        public static readonly DependencyProperty CropRectProperty =
            DependencyProperty.Register(nameof(CropRect), typeof(Rect), typeof(InteractiveCropRectangle),
                new PropertyMetadata(new Rect(0, 0, 254, 290), OnCropRectChanged));

        /// <summary>
        /// Dependency property for enabling/disabling interaction (kept for compatibility)
        /// </summary>
        public static readonly DependencyProperty IsInteractiveProperty =
            DependencyProperty.Register(nameof(IsInteractive), typeof(bool), typeof(InteractiveCropRectangle),
                new PropertyMetadata(false));

        /// <summary>
        /// Dependency property for the minimum crop size (kept for compatibility)
        /// </summary>
        public static readonly DependencyProperty MinimumSizeProperty =
            DependencyProperty.Register(nameof(MinimumSize), typeof(Size), typeof(InteractiveCropRectangle),
                new PropertyMetadata(new Size(50, 50)));

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets the crop rectangle bounds
        /// </summary>
        public Rect CropRect
        {
            get => (Rect)GetValue(CropRectProperty);
            set => SetValue(CropRectProperty, value);
        }

        /// <summary>
        /// Gets or sets whether the crop rectangle is interactive (always false in this simplified version)
        /// </summary>
        public bool IsInteractive
        {
            get => (bool)GetValue(IsInteractiveProperty);
            set => SetValue(IsInteractiveProperty, false); // Always false for simplified version
        }

        /// <summary>
        /// Gets or sets the minimum size for the crop rectangle (kept for compatibility)
        /// </summary>
        public Size MinimumSize
        {
            get => (Size)GetValue(MinimumSizeProperty);
            set => SetValue(MinimumSizeProperty, value);
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the crop rectangle changes
        /// </summary>
        public event EventHandler<Rect>? CropRectChanged;

        #endregion

        #region Private Fields

        private Grid? _markerContainer;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the InteractiveCropRectangle
        /// </summary>
        public InteractiveCropRectangle()
        {
            try
            {
                InitializeComponent();
                LoggingService.LogDebug("InteractiveCropRectangle initialized as simplified visual guide", "InteractiveCropRectangle");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing InteractiveCropRectangle: {ex.Message}", "InteractiveCropRectangle");
            }
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes the visual components of the crop rectangle
        /// </summary>
        private void InitializeComponent()
        {
            try
            {
                // Make the control transparent to mouse events so image dragging works
                IsHitTestVisible = false;
                
                // Set up the border styling
                BorderBrush = new SolidColorBrush(Color.FromArgb(77, 255, 255, 255));
                BorderThickness = new Thickness(1);
                Background = new SolidColorBrush(Color.FromArgb(77, 0, 0, 0));
                CornerRadius = new CornerRadius(2);

                // Create the marker container
                _markerContainer = new Grid();
                Child = _markerContainer;

                // Create corner markers
                CreateCornerMarkers();

                // Update the visual representation
                UpdateVisualElements();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing component: {ex.Message}", "InteractiveCropRectangle");
            }
        }

        /// <summary>
        /// Creates the corner markers for the crop rectangle
        /// </summary>
        private void CreateCornerMarkers()
        {
            try
            {
                if (_markerContainer == null) return;

                var markerBrush = new SolidColorBrush(Colors.MediumPurple);
                var markerSizeL = 12.0;
                var markerSizeS = 2.0;

                // Create all 8 corner markers
                var topLeftMarkerTop = CreateMarker(markerSizeL, markerSizeS, markerBrush);
                var topLeftMarkerLeft = CreateMarker(markerSizeS, markerSizeL, markerBrush);
                var topCenterMarker = CreateMarker(markerSizeL, markerSizeS, markerBrush);
                var topRightMarkerTop = CreateMarker(markerSizeL, markerSizeS, markerBrush);
                var topRightMarkerRight = CreateMarker(markerSizeS, markerSizeL, markerBrush);
                var rightCenterMarker = CreateMarker(markerSizeS, markerSizeL, markerBrush);
                var bottomRightMarkerRight = CreateMarker(markerSizeS, markerSizeL, markerBrush);
                var bottomRightMarkerBottom = CreateMarker(markerSizeL, markerSizeS, markerBrush);
                var bottomCenterMarker = CreateMarker(markerSizeL, markerSizeS, markerBrush);
                var bottomLeftMarkerBottom = CreateMarker(markerSizeL, markerSizeS, markerBrush);
                var bottomLeftMarkerLeft = CreateMarker(markerSizeS, markerSizeL, markerBrush);
                var leftCenterMarker = CreateMarker(markerSizeS, markerSizeL, markerBrush);

                // Position markers - centered on the border edges
                var halfMarker = markerSizeS / 2;
                
                // Top-left-Top
                topLeftMarkerTop.HorizontalAlignment = HorizontalAlignment.Left;
                topLeftMarkerTop.VerticalAlignment = VerticalAlignment.Top;
                topLeftMarkerTop.Margin = new Thickness(halfMarker, halfMarker, 0, 0);

                // Top-left-Left
                topLeftMarkerLeft.HorizontalAlignment = HorizontalAlignment.Left;
                topLeftMarkerLeft.VerticalAlignment = VerticalAlignment.Top;
                topLeftMarkerLeft.Margin = new Thickness(halfMarker, halfMarker, 0, 0);

                // Top-center
                topCenterMarker.HorizontalAlignment = HorizontalAlignment.Center;
                topCenterMarker.VerticalAlignment = VerticalAlignment.Top;
                topCenterMarker.Margin = new Thickness(0, halfMarker, 0, 0);

                // Top-right-Top
                topRightMarkerTop.HorizontalAlignment = HorizontalAlignment.Right;
                topRightMarkerTop.VerticalAlignment = VerticalAlignment.Top;
                topRightMarkerTop.Margin = new Thickness(0, halfMarker, halfMarker, 0);

                // Top-right-Right
                topRightMarkerRight.HorizontalAlignment = HorizontalAlignment.Right;
                topRightMarkerRight.VerticalAlignment = VerticalAlignment.Top;
                topRightMarkerRight.Width = 3;
                topRightMarkerRight.Margin = new Thickness(0, halfMarker, halfMarker, 0);

                // Right-center
                rightCenterMarker.HorizontalAlignment = HorizontalAlignment.Right;
                rightCenterMarker.VerticalAlignment = VerticalAlignment.Center;
                rightCenterMarker.Width = 3;
                rightCenterMarker.Margin = new Thickness(0, 0, halfMarker, 0);

                // Bottom-right-Right
                bottomRightMarkerRight.HorizontalAlignment = HorizontalAlignment.Right;
                bottomRightMarkerRight.VerticalAlignment = VerticalAlignment.Bottom;
                bottomRightMarkerRight.Width = 3;
                bottomRightMarkerRight.Margin = new Thickness(0, 0, halfMarker, halfMarker);

                // Bottom-right-Bottom
                bottomRightMarkerBottom.HorizontalAlignment = HorizontalAlignment.Right;
                bottomRightMarkerBottom.VerticalAlignment = VerticalAlignment.Bottom;
                bottomRightMarkerBottom.Margin = new Thickness(0, 0, halfMarker, halfMarker);

                // Bottom-center
                bottomCenterMarker.HorizontalAlignment = HorizontalAlignment.Center;
                bottomCenterMarker.VerticalAlignment = VerticalAlignment.Bottom;
                bottomCenterMarker.Margin = new Thickness(0, 0, 0, halfMarker);

                // Bottom-left-Bottom
                bottomLeftMarkerBottom.HorizontalAlignment = HorizontalAlignment.Left;
                bottomLeftMarkerBottom.VerticalAlignment = VerticalAlignment.Bottom;
                bottomLeftMarkerBottom.Margin = new Thickness(halfMarker, 0, 0, halfMarker);

                // Bottom-left-Left
                bottomLeftMarkerLeft.HorizontalAlignment = HorizontalAlignment.Left;
                bottomLeftMarkerLeft.VerticalAlignment = VerticalAlignment.Bottom;
                bottomLeftMarkerLeft.Margin = new Thickness(halfMarker, 0, 0, halfMarker);


                // Left-center
                leftCenterMarker.HorizontalAlignment = HorizontalAlignment.Left;
                leftCenterMarker.VerticalAlignment = VerticalAlignment.Center;
                leftCenterMarker.Margin = new Thickness(halfMarker, 0, 0, 0);

                // Add all markers to the container
                _markerContainer.Children.Add(topLeftMarkerTop);
                _markerContainer.Children.Add(topLeftMarkerLeft);
                _markerContainer.Children.Add(topCenterMarker);
                _markerContainer.Children.Add(topRightMarkerTop);
                _markerContainer.Children.Add(topRightMarkerRight);
                _markerContainer.Children.Add(rightCenterMarker);
                _markerContainer.Children.Add(bottomRightMarkerRight);
                _markerContainer.Children.Add(bottomRightMarkerBottom);
                _markerContainer.Children.Add(bottomCenterMarker);

                _markerContainer.Children.Add(bottomLeftMarkerBottom);
                _markerContainer.Children.Add(bottomLeftMarkerLeft);

                _markerContainer.Children.Add(leftCenterMarker);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating corner markers: {ex.Message}", "InteractiveCropRectangle");
            }
        }

        /// <summary>
        /// Creates a single corner marker
        /// </summary>
        private Rectangle CreateMarker(double sizeH, double sizeW,Brush fill)
        {
            return new Rectangle
            {
                Width = sizeH,
                Height = sizeW,
                Fill = fill,
                IsHitTestVisible = false // Ensure markers don't interfere with mouse events
            };
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Property changed callback for CropRect
        /// </summary>
        private static void OnCropRectChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is InteractiveCropRectangle control)
            {
                control.UpdateVisualElements();
                control.CropRectChanged?.Invoke(control, (Rect)e.NewValue);
            }
        }

        #endregion

        #region Visual Updates

        /// <summary>
        /// Updates the visual elements based on the current crop rectangle
        /// </summary>
        private void UpdateVisualElements()
        {
            try
            {
                // Set the size based on CropRect
                Width = CropRect.Width;
                Height = CropRect.Height;
                
                // Position the control using Margin for absolute positioning within the parent Grid
                // The parent Grid is 500x320, so we position relative to that
                Margin = new Thickness(CropRect.X, CropRect.Y, 0, 0);
                
                // Set alignment to top-left for absolute positioning
                HorizontalAlignment = HorizontalAlignment.Left;
                VerticalAlignment = VerticalAlignment.Top;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating visual elements: {ex.Message}", "InteractiveCropRectangle");
            }
        }

        #endregion
    }
}
