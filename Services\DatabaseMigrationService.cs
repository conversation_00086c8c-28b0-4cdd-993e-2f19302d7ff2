using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Database migration service for UFU2 schema management.
    /// Handles schema creation, version tracking, and safe migration execution with comprehensive error handling.
    /// Integrates with UFU2's ErrorManager and LoggingService for consistent error handling and logging.
    /// </summary>
    public class DatabaseMigrationService
    {
        private readonly DatabaseService _databaseService;
        private const int CURRENT_SCHEMA_VERSION = 6;

        public DatabaseMigrationService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            LoggingService.LogDebug("DatabaseMigrationService initialized", "DatabaseMigrationService");
        }

        /// <summary>
        /// Initializes the database schema for new installations or applies migrations for existing databases.
        /// This method should be called during application startup to ensure database schema is current.
        /// All operations are wrapped in transactions with rollback capability for data safety.
        /// Uses connection pooling for enhanced performance.
        /// </summary>
        public async Task InitializeSchemaAsync()
        {
            LoggingService.LogDebug("Starting database schema initialization", "DatabaseMigrationService");

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Configure database with optimal settings first
                await DatabaseService.ConfigureDatabaseAsync(connection);
                LoggingService.LogDebug("Database configuration applied successfully", "DatabaseMigrationService");

                // Check current schema version
                int currentVersion = await GetCurrentSchemaVersionAsync(connection);
                LoggingService.LogDebug($"Current database schema version: {currentVersion}, target version: {CURRENT_SCHEMA_VERSION}", "DatabaseMigrationService");

                // Determine migration path
                if (currentVersion == 0)
                {
                    LoggingService.LogDebug("New database detected, creating initial schema", "DatabaseMigrationService");
                    await CreateInitialSchemaAsync(connection);
                    LoggingService.LogDebug("Initial database schema created successfully", "DatabaseMigrationService");
                }
                else if (currentVersion < CURRENT_SCHEMA_VERSION)
                {
                    LoggingService.LogDebug($"Database migration required from version {currentVersion} to {CURRENT_SCHEMA_VERSION}", "DatabaseMigrationService");
                    await ApplyMigrationsAsync(connection, currentVersion);
                    LoggingService.LogDebug("Database migration completed successfully", "DatabaseMigrationService");
                }
                else if (currentVersion > CURRENT_SCHEMA_VERSION)
                {
                    string errorMsg = $"Database schema version {currentVersion} is newer than application version {CURRENT_SCHEMA_VERSION}";
                    LoggingService.LogError(errorMsg, "DatabaseMigrationService");
                    throw new InvalidOperationException(errorMsg);
                }
                else
                {
                    LoggingService.LogDebug("Database schema is up to date", "DatabaseMigrationService");
                }

                // Validate schema integrity after initialization/migration
                bool isValid = await ValidateSchemaAsync();
                if (!isValid)
                {
                    throw new InvalidOperationException("Database schema validation failed after initialization");
                }

                LoggingService.LogDebug("Database schema initialization completed successfully", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                string arabicMessage = "فشل في تهيئة مخطط قاعدة البيانات";
                string englishTitle = "Database Schema Initialization Failed";

                ErrorManager.HandleErrorToast(ex, arabicMessage, englishTitle, LogLevel.Error, "DatabaseMigrationService");
                LoggingService.LogError($"Database schema initialization failed: {ex.Message}", "DatabaseMigrationService");
                throw new DatabaseMigrationException("Database schema initialization failed", ex);
            }
        }

        /// <summary>
        /// Gets the current schema version from the database.
        /// </summary>
        private async Task<int> GetCurrentSchemaVersionAsync(SqliteConnection connection)
        {
            try
            {
                string schemaVersionTableName = GetSchemaVersionTableName();

                // Check if schema version table exists
                string checkTableSql = $@"
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type='table' AND name='{schemaVersionTableName}'";

                int tableExists = await connection.ExecuteScalarAsync<int>(checkTableSql);

                if (tableExists == 0)
                {
                    return 0; // No schema version table means version 0 (new database)
                }

                // Get the latest version
                string getVersionSql = $"SELECT MAX(Version) FROM {schemaVersionTableName}";
                var version = await connection.ExecuteScalarAsync<int?>(getVersionSql);

                return version ?? 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Could not determine schema version: {ex.Message}", "DatabaseMigrationService");
                return 0;
            }
        }

        /// <summary>
        /// Creates the initial database schema from the SQL script.
        /// All operations are wrapped in a transaction with automatic rollback on failure.
        /// </summary>
        private async Task CreateInitialSchemaAsync(SqliteConnection connection)
        {
            LoggingService.LogDebug("Starting initial schema creation", "DatabaseMigrationService");

            using var transaction = connection.BeginTransaction();

            try
            {
                // Determine which schema file to use based on database type
                string schemaResourceName = GetSchemaResourceName();

                // Read the schema SQL from embedded resource
                LoggingService.LogDebug($"Reading schema from embedded resource: {schemaResourceName}", "DatabaseMigrationService");
                string schemaSql = ReadEmbeddedSqlResource(schemaResourceName);

                if (string.IsNullOrEmpty(schemaSql))
                {
                    string errorMsg = "Schema SQL content is empty or could not be read from embedded resource";
                    LoggingService.LogError(errorMsg, "DatabaseMigrationService");
                    throw new InvalidOperationException(errorMsg);
                }

                LoggingService.LogDebug("Schema SQL loaded from embedded resource successfully", "DatabaseMigrationService");

                // Split the SQL into individual statements and execute them
                var statements = SplitSqlStatements(schemaSql);
                LoggingService.LogDebug($"Executing {statements.Length} SQL statements", "DatabaseMigrationService");

                int executedStatements = 0;
                foreach (var statement in statements)
                {
                    if (!string.IsNullOrWhiteSpace(statement))
                    {
                        try
                        {
                            await connection.ExecuteAsync(statement, transaction: transaction);
                            executedStatements++;

                            // Log progress for long operations
                            if (executedStatements % 10 == 0)
                            {
                                LoggingService.LogDebug($"Executed {executedStatements}/{statements.Length} statements", "DatabaseMigrationService");
                            }
                        }
                        catch (Exception statementEx)
                        {
                            LoggingService.LogError($"Failed to execute SQL statement: {statement.Substring(0, Math.Min(100, statement.Length))}...", "DatabaseMigrationService");
                            LoggingService.LogError($"Statement error: {statementEx.Message}", "DatabaseMigrationService");
                            throw;
                        }
                    }
                }

                // Update schema version
                await UpdateSchemaVersionAsync(connection, transaction, CURRENT_SCHEMA_VERSION, "Initial UFU2 database schema");

                transaction.Commit();
                LoggingService.LogDebug($"Initial database schema applied successfully ({executedStatements} statements executed)", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                try
                {
                    transaction.Rollback();
                    LoggingService.LogDebug("Transaction rolled back successfully", "DatabaseMigrationService");
                }
                catch (Exception rollbackEx)
                {
                    LoggingService.LogError($"Failed to rollback transaction: {rollbackEx.Message}", "DatabaseMigrationService");
                }

                LoggingService.LogError($"Failed to create initial schema: {ex.Message}", "DatabaseMigrationService");
                throw new DatabaseMigrationException("Failed to create initial database schema", ex);
            }
        }

        /// <summary>
        /// Applies database migrations from the current version to the latest version.
        /// Each migration is wrapped in its own transaction for safe rollback capability.
        /// </summary>
        private async Task ApplyMigrationsAsync(SqliteConnection connection, int fromVersion)
        {
            if (fromVersion >= CURRENT_SCHEMA_VERSION)
            {
                LoggingService.LogDebug("Database schema is up to date", "DatabaseMigrationService");
                return;
            }

            LoggingService.LogDebug($"Applying migrations from version {fromVersion} to {CURRENT_SCHEMA_VERSION}", "DatabaseMigrationService");

            try
            {
                // Apply migrations sequentially from fromVersion to CURRENT_SCHEMA_VERSION
                for (int version = fromVersion + 1; version <= CURRENT_SCHEMA_VERSION; version++)
                {
                    LoggingService.LogDebug($"Applying migration to version {version}", "DatabaseMigrationService");

                    using var transaction = connection.BeginTransaction();

                    try
                    {
                        await ApplyMigrationToVersionAsync(connection, transaction, version);
                        transaction.Commit();

                        LoggingService.LogDebug($"Migration to version {version} completed successfully", "DatabaseMigrationService");
                    }
                    catch (Exception ex)
                    {
                        try
                        {
                            transaction.Rollback();
                            LoggingService.LogDebug($"Migration to version {version} rolled back", "DatabaseMigrationService");
                        }
                        catch (Exception rollbackEx)
                        {
                            LoggingService.LogError($"Failed to rollback migration to version {version}: {rollbackEx.Message}", "DatabaseMigrationService");
                        }

                        LoggingService.LogError($"Migration to version {version} failed: {ex.Message}", "DatabaseMigrationService");
                        throw new DatabaseMigrationException($"Migration to version {version} failed", ex);
                    }
                }

                LoggingService.LogDebug("All migrations applied successfully", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                string arabicMessage = "فشل في تطبيق ترقيات قاعدة البيانات";
                string englishTitle = "Database Migration Failed";

                ErrorManager.HandleErrorToast(ex, arabicMessage, englishTitle, LogLevel.Error, "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Applies a specific migration version.
        /// This method contains the logic for each individual migration.
        /// </summary>
        private async Task ApplyMigrationToVersionAsync(SqliteConnection connection, SqliteTransaction transaction, int targetVersion)
        {
            LoggingService.LogDebug($"Executing migration logic for version {targetVersion}", "DatabaseMigrationService");

            switch (targetVersion)
            {
                case 1:
                    // Version 1 is the initial schema, so if we're migrating TO version 1,
                    // it means we're upgrading from a pre-versioned database
                    await MigrateToVersion1Async(connection, transaction);
                    break;

                case 2:
                    // Version 2: Remove NationalId format constraint to allow flexible input
                    await MigrateToVersion2Async(connection, transaction);
                    break;

                case 3:
                    // Version 3: Add high-priority composite indexes for performance optimization
                    await MigrateToVersion3Async(connection, transaction);
                    break;

                case 4:
                    // Version 4: Add medium-priority composite indexes for enhanced performance optimization
                    await MigrateToVersion4Async(connection, transaction);
                    break;

                case 5:
                    // Version 5: Remove cross-database FK from CraftActivityCodes (client DB)
                    await MigrateToVersion5Async(connection, transaction);
                    break;

                case 6:
                    // Version 6: Fix CHECK constraints to allow empty strings for NIF, NIS, and ART numbers
                    await MigrateToVersion6Async(connection, transaction);
                    break;

                default:
                    throw new NotSupportedException($"Migration to version {targetVersion} is not supported");
            }

            // Update schema version tracking
            await UpdateSchemaVersionAsync(connection, transaction, targetVersion, GetMigrationDescription(targetVersion));
        }

        /// <summary>
        /// Migrates from pre-versioned database to version 1.
        /// This handles upgrading existing UFU2 installations to the new schema.
        /// </summary>
        private async Task MigrateToVersion1Async(SqliteConnection connection, SqliteTransaction transaction)
        {
            LoggingService.LogDebug("Applying migration to version 1 (initial versioned schema)", "DatabaseMigrationService");

            string schemaVersionTableName = GetSchemaVersionTableName();

            // Check if we need to create the schema version table
            string checkSchemaVersionTable = $@"
                SELECT COUNT(*) FROM sqlite_master
                WHERE type='table' AND name='{schemaVersionTableName}'";

            int schemaVersionTableExists = await connection.ExecuteScalarAsync<int>(checkSchemaVersionTable, transaction: transaction);

            if (schemaVersionTableExists == 0)
            {
                // Create schema version table with appropriate name for database type
                string createSchemaVersionTable = $@"
                    CREATE TABLE {schemaVersionTableName} (
                        Version INTEGER PRIMARY KEY,
                        AppliedAt TEXT DEFAULT (datetime('now')),
                        Description TEXT
                    )";

                await connection.ExecuteAsync(createSchemaVersionTable, transaction: transaction);
                LoggingService.LogDebug($"Created {schemaVersionTableName} table", "DatabaseMigrationService");
            }

            // For version 1, we assume the schema is already mostly in place
            // This migration mainly adds version tracking
            // Future versions would contain actual schema changes

            LoggingService.LogDebug("Version 1 migration completed", "DatabaseMigrationService");
        }

        /// <summary>
        /// Migrates the database to version 2.
        /// Removes the NationalId format constraint to allow flexible input as per database binding requirements.
        /// </summary>
        private async Task MigrateToVersion2Async(SqliteConnection connection, SqliteTransaction transaction)
        {
            LoggingService.LogDebug("Starting migration to version 2 - removing NationalId format constraint", "DatabaseMigrationService");

            try
            {
                // SQLite doesn't support dropping constraints directly, so we need to recreate the table
                // Step 1: Create a new table without the problematic constraint
                const string createNewClientsTable = @"
                    CREATE TABLE Clients_New (
                        Uid TEXT PRIMARY KEY,
                        NameFr TEXT NOT NULL,
                        NameAr TEXT,
                        BirthDate TEXT,
                        BirthPlace TEXT,
                        Gender INTEGER DEFAULT 0,
                        Address TEXT,
                        NationalId TEXT,
                        CreatedAt TEXT DEFAULT (datetime('now')),
                        UpdatedAt TEXT DEFAULT (datetime('now')),

                        -- Data validation constraints (NationalId format constraint removed)
                        CONSTRAINT chk_clients_gender CHECK (Gender IS NULL OR Gender IN (0, 1)),
                        CONSTRAINT chk_clients_name_fr_not_empty CHECK (length(trim(NameFr)) > 0)
                    )";

                await connection.ExecuteAsync(createNewClientsTable, transaction: transaction);
                LoggingService.LogDebug("Created new Clients table without NationalId format constraint", "DatabaseMigrationService");

                // Step 2: Copy data from old table to new table
                const string copyDataSql = @"
                    INSERT INTO Clients_New (Uid, NameFr, NameAr, BirthDate, BirthPlace, Gender, Address, NationalId, CreatedAt, UpdatedAt)
                    SELECT Uid, NameFr, NameAr, BirthDate, BirthPlace, Gender, Address, NationalId, CreatedAt, UpdatedAt
                    FROM Clients";

                await connection.ExecuteAsync(copyDataSql, transaction: transaction);
                LoggingService.LogDebug("Copied all data from old Clients table to new table", "DatabaseMigrationService");

                // Step 3: Drop the old table
                await connection.ExecuteAsync("DROP TABLE Clients", transaction: transaction);
                LoggingService.LogDebug("Dropped old Clients table", "DatabaseMigrationService");

                // Step 4: Rename the new table
                await connection.ExecuteAsync("ALTER TABLE Clients_New RENAME TO Clients", transaction: transaction);
                LoggingService.LogDebug("Renamed new table to Clients", "DatabaseMigrationService");

                // Step 5: Recreate indexes with updated unique constraint for NationalId
                const string recreateIndexesSql = @"
                    -- Core search indexes
                    CREATE INDEX IF NOT EXISTS idx_clients_name_fr ON Clients(NameFr);
                    CREATE INDEX IF NOT EXISTS idx_clients_name_ar ON Clients(NameAr) WHERE NameAr IS NOT NULL;

                    -- Conditional indexes for sparse data filtering
                    CREATE INDEX IF NOT EXISTS idx_clients_national_id ON Clients(NationalId) WHERE NationalId IS NOT NULL;

                    -- Updated unique constraint for NationalId (only for non-empty values)
                    CREATE UNIQUE INDEX IF NOT EXISTS idx_clients_national_id_unique ON Clients(NationalId) WHERE NationalId IS NOT NULL AND length(trim(NationalId)) > 0;";

                await connection.ExecuteAsync(recreateIndexesSql, transaction: transaction);
                LoggingService.LogDebug("Recreated indexes with updated NationalId unique constraint", "DatabaseMigrationService");

                // Step 6: Recreate triggers
                const string recreateTriggersSql = @"
                    -- Update timestamp trigger for Clients table
                    CREATE TRIGGER IF NOT EXISTS trg_clients_updated_at
                        AFTER UPDATE ON Clients
                        FOR EACH ROW
                    BEGIN
                        UPDATE Clients SET UpdatedAt = datetime('now') WHERE Uid = NEW.Uid;
                    END;";

                await connection.ExecuteAsync(recreateTriggersSql, transaction: transaction);
                LoggingService.LogDebug("Recreated triggers for Clients table", "DatabaseMigrationService");

                LoggingService.LogDebug("Version 2 migration completed successfully", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during version 2 migration: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Migrates the database to version 3.
        /// Adds high-priority composite indexes for performance optimization.
        /// Implements Arabic search optimization, activity type filtering, and phone number composite indexes.
        /// </summary>
        private async Task MigrateToVersion3Async(SqliteConnection connection, SqliteTransaction transaction)
        {
            LoggingService.LogDebug("Starting migration to version 3 - adding high-priority composite indexes", "DatabaseMigrationService");

            try
            {
                // Index 1: Composite Arabic Search Index for 40-50% improvement in Arabic name searches
                const string createArabicSearchIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_clients_arabic_search
                    ON Clients(NameAr, NationalId, Address)
                    WHERE NameAr IS NOT NULL";

                await connection.ExecuteAsync(createArabicSearchIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_clients_arabic_search composite index for Arabic name search optimization", "DatabaseMigrationService");

                // Index 2: Activity Type + Client Composite Index for 30-40% improvement in activity queries
                const string createActivityTypeClientIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_activities_type_client
                    ON Activities(ActivityType, ClientUid, CreatedAt)
                    WHERE ActivityType IS NOT NULL";

                await connection.ExecuteAsync(createActivityTypeClientIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_activities_type_client composite index for activity query optimization", "DatabaseMigrationService");

                // Index 3: Phone Number Composite Index for 25-35% improvement in contact operations
                const string createPhoneNumberCompositeIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_phone_numbers_composite
                    ON PhoneNumbers(ClientUid, IsPrimary, PhoneType)";

                await connection.ExecuteAsync(createPhoneNumberCompositeIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_phone_numbers_composite index for contact operation optimization", "DatabaseMigrationService");

                // Verify index creation by checking sqlite_master
                const string verifyIndexesSql = @"
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type='index' AND name IN ('idx_clients_arabic_search', 'idx_activities_type_client', 'idx_phone_numbers_composite')";

                int createdIndexCount = await connection.ExecuteScalarAsync<int>(verifyIndexesSql, transaction: transaction);

                if (createdIndexCount != 3)
                {
                    throw new InvalidOperationException($"Expected 3 indexes to be created, but only {createdIndexCount} were found");
                }

                LoggingService.LogDebug($"Version 3 migration completed successfully - {createdIndexCount} high-priority indexes created", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during version 3 migration: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Migrates the database to version 4.
        /// Adds medium-priority composite indexes for enhanced performance optimization.
        /// Implements specialized indexes for notes, file checks, date ranges, and address search.
        /// </summary>
        private async Task MigrateToVersion4Async(SqliteConnection connection, SqliteTransaction transaction)
        {
            LoggingService.LogDebug("Starting migration to version 4 - adding medium-priority composite indexes", "DatabaseMigrationService");

            try
            {
                // Index 1: Notes Priority + Content Composite Index
                const string createNotesPriorityContentIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_notes_priority_content
                    ON Notes(ActivityUid, Priority, CreatedAt)
                    WHERE Priority IS NOT NULL";

                await connection.ExecuteAsync(createNotesPriorityContentIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_notes_priority_content composite index", "DatabaseMigrationService");

                // Index 2: File Check States Optimization Index
                const string createFileCheckStatusIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_file_check_status_activity
                    ON FileCheckStates(IsChecked, ActivityUid, FileCheckType)";

                await connection.ExecuteAsync(createFileCheckStatusIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_file_check_status_activity index", "DatabaseMigrationService");

                // Index 3: Activity Date Range Composite Index
                const string createActivityDateRangeIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_activities_date_range
                    ON Activities(ActivityStartDate, CreatedAt, ActivityType)
                    WHERE ActivityStartDate IS NOT NULL";

                await connection.ExecuteAsync(createActivityDateRangeIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_activities_date_range index", "DatabaseMigrationService");

                // Index 4: Client Address Search Enhancement Index
                const string createClientAddressSearchIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_clients_address_search
                    ON Clients(Address, NameAr, NationalId)
                    WHERE Address IS NOT NULL AND length(trim(Address)) > 0";

                await connection.ExecuteAsync(createClientAddressSearchIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_clients_address_search index", "DatabaseMigrationService");

                // Index 5: Activity Update Tracking Index
                const string createActivityUpdateTrackingIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_activities_update_tracking
                    ON Activities(ClientUid, UpdatedAt, ActivityUpdateDate)
                    WHERE ActivityUpdateDate IS NOT NULL";

                await connection.ExecuteAsync(createActivityUpdateTrackingIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_activities_update_tracking index", "DatabaseMigrationService");

                // Index 6: Phone Number Type Optimization Index
                const string createPhoneNumberTypeIndex = @"
                    CREATE INDEX IF NOT EXISTS idx_phone_numbers_type_optimization
                    ON PhoneNumbers(PhoneType, IsPrimary, ClientUid)";

                await connection.ExecuteAsync(createPhoneNumberTypeIndex, transaction: transaction);
                LoggingService.LogDebug("Created idx_phone_numbers_type_optimization index", "DatabaseMigrationService");

                // Verify index creation by checking sqlite_master
                const string verifyIndexesSql = @"
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type='index' AND name IN (
                        'idx_notes_priority_content',
                        'idx_file_check_status_activity',
                        'idx_activities_date_range',
                        'idx_clients_address_search',
                        'idx_activities_update_tracking',
                        'idx_phone_numbers_type_optimization'
                    )";

                int createdIndexCount = await connection.ExecuteScalarAsync<int>(verifyIndexesSql, transaction: transaction);

                if (createdIndexCount != 6)
                {
                    throw new InvalidOperationException($"Expected 6 indexes to be created, but only {createdIndexCount} were found");
                }

                LoggingService.LogDebug($"Version 4 migration completed successfully - {createdIndexCount} indexes created", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during version 4 migration: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Migrates the database to version 5.
        /// Removes the cross-database foreign key from CraftActivityCodes to CraftTypeBase.
        /// Recreates CraftActivityCodes without the FK while preserving data and indexes.
        /// </summary>
        private async Task MigrateToVersion5Async(SqliteConnection connection, SqliteTransaction transaction)
        {
            if (_databaseService.DatabaseType != DatabaseType.ClientData)
            {
                // Only applicable to client database; reference/archive DBs skip
                LoggingService.LogDebug("Skipping v5 migration for non-client database", "DatabaseMigrationService");
                return;
            }

            LoggingService.LogDebug("Starting migration to version 5 - remove cross-DB FK on CraftActivityCodes", "DatabaseMigrationService");

            try
            {
                // 1. Check if CraftActivityCodes table exists
                const string checkTableSql = @"SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='CraftActivityCodes'";
                int tableExists = await connection.ExecuteScalarAsync<int>(checkTableSql, transaction: transaction);
                if (tableExists == 0)
                {
                    LoggingService.LogWarning("CraftActivityCodes table not found; nothing to migrate for v5", "DatabaseMigrationService");
                    return;
                }

                // 2. Create new table without FK to CraftTypeBase
                const string createNewTableSql = @"
                    CREATE TABLE CraftActivityCodes_New (
                        Uid TEXT PRIMARY KEY,
                        ActivityUid TEXT NOT NULL,
                        CraftCode TEXT NOT NULL,
                        FOREIGN KEY (ActivityUid) REFERENCES Activities(Uid) ON DELETE CASCADE,
                        CONSTRAINT chk_craft_code_format CHECK (CraftCode GLOB '[0-9][0-9]-[0-9][0-9]-[0-9][0-9][0-9]'),
                        CONSTRAINT uq_craft_activity_uid UNIQUE (ActivityUid)
                    );";

                await connection.ExecuteAsync(createNewTableSql, transaction: transaction);

                // 3. Copy data from old to new
                const string copyDataSql = @"INSERT INTO CraftActivityCodes_New (Uid, ActivityUid, CraftCode)
                                             SELECT Uid, ActivityUid, CraftCode FROM CraftActivityCodes";
                await connection.ExecuteAsync(copyDataSql, transaction: transaction);

                // 4. Drop old table
                await connection.ExecuteAsync("DROP TABLE CraftActivityCodes", transaction: transaction);

                // 5. Rename new table
                await connection.ExecuteAsync("ALTER TABLE CraftActivityCodes_New RENAME TO CraftActivityCodes", transaction: transaction);

                // 6. Recreate indexes
                const string recreateIndexesSql = @"
                    CREATE INDEX IF NOT EXISTS idx_craft_activity_codes_activity_uid ON CraftActivityCodes(ActivityUid);
                    CREATE INDEX IF NOT EXISTS idx_craft_activity_codes_craft_code ON CraftActivityCodes(CraftCode);";
                await connection.ExecuteAsync(recreateIndexesSql, transaction: transaction);

                LoggingService.LogDebug("Version 5 migration completed successfully", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during version 5 migration: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Migrates the database to version 6.
        /// Fixes CHECK constraints for NIF, NIS, and ART numbers to allow empty strings.
        /// Recreates the Activities table with updated constraints while preserving all data.
        /// </summary>
        private async Task MigrateToVersion6Async(SqliteConnection connection, SqliteTransaction transaction)
        {
            if (_databaseService.DatabaseType != DatabaseType.ClientData)
            {
                // Only applicable to client database; reference/archive DBs skip
                LoggingService.LogDebug("Skipping v6 migration for non-client database", "DatabaseMigrationService");
                return;
            }

            LoggingService.LogDebug("Starting migration to version 6 - fix CHECK constraints for NIF/NIS/ART numbers", "DatabaseMigrationService");

            try
            {
                // 1. Check if Activities table exists
                const string checkTableSql = @"SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='Activities'";
                int tableExists = await connection.ExecuteScalarAsync<int>(checkTableSql, transaction: transaction);
                if (tableExists == 0)
                {
                    LoggingService.LogWarning("Activities table not found; nothing to migrate for v6", "DatabaseMigrationService");
                    return;
                }

                // 2. Create new Activities table with fixed CHECK constraints
                const string createNewTableSql = @"
                    CREATE TABLE Activities_New (
                        Uid TEXT PRIMARY KEY,
                        ClientUid TEXT NOT NULL,
                        ActivityType TEXT,
                        ActivityStatus TEXT,
                        ActivityLocation TEXT,
                        CommercialRegister TEXT,
                        NifNumber TEXT,
                        NisNumber TEXT,
                        ArtNumber TEXT,
                        CpiDaira TEXT,
                        CpiWilaya TEXT,
                        ActivityStartDate TEXT,
                        ActivityUpdateDate TEXT,
                        ActivityUpdateNote TEXT,
                        CreatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),
                        UpdatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),

                        -- Foreign key relationships
                        FOREIGN KEY (ClientUid) REFERENCES Clients(Uid) ON DELETE CASCADE,

                        -- Data validation constraints (fixed to allow empty strings)
                        CONSTRAINT chk_activity_type CHECK (ActivityType IS NULL OR ActivityType IN ('MainCommercial', 'SecondaryCommercial', 'Craft', 'Professional')),
                        CONSTRAINT chk_nif_number_format CHECK (NifNumber IS NULL OR length(trim(NifNumber)) = 0 OR length(trim(NifNumber)) >= 8),
                        CONSTRAINT chk_nis_number_format CHECK (NisNumber IS NULL OR length(trim(NisNumber)) = 0 OR length(trim(NisNumber)) >= 8),
                        CONSTRAINT chk_art_number_format CHECK (ArtNumber IS NULL OR length(trim(ArtNumber)) = 0 OR length(trim(ArtNumber)) >= 6)
                    );";

                await connection.ExecuteAsync(createNewTableSql, transaction: transaction);

                // 3. Copy all data from old to new table
                const string copyDataSql = @"
                    INSERT INTO Activities_New (
                        Uid, ClientUid, ActivityType, ActivityStatus, ActivityLocation, CommercialRegister,
                        NifNumber, NisNumber, ArtNumber, CpiDaira, CpiWilaya, ActivityStartDate,
                        ActivityUpdateDate, ActivityUpdateNote, CreatedAt, UpdatedAt
                    )
                    SELECT
                        Uid, ClientUid, ActivityType, ActivityStatus, ActivityLocation, CommercialRegister,
                        NifNumber, NisNumber, ArtNumber, CpiDaira, CpiWilaya, ActivityStartDate,
                        ActivityUpdateDate, ActivityUpdateNote, CreatedAt, UpdatedAt
                    FROM Activities";

                await connection.ExecuteAsync(copyDataSql, transaction: transaction);

                // 4. Drop old table
                await connection.ExecuteAsync("DROP TABLE Activities", transaction: transaction);

                // 5. Rename new table
                await connection.ExecuteAsync("ALTER TABLE Activities_New RENAME TO Activities", transaction: transaction);

                // 6. Recreate all indexes for Activities table
                const string recreateIndexesSql = @"
                    CREATE INDEX IF NOT EXISTS idx_activities_client_uid ON Activities(ClientUid);
                    CREATE INDEX IF NOT EXISTS idx_activities_activity_type ON Activities(ActivityType);
                    CREATE INDEX IF NOT EXISTS idx_activities_activity_status ON Activities(ActivityStatus);
                    CREATE INDEX IF NOT EXISTS idx_activities_nif_number ON Activities(NifNumber) WHERE NifNumber IS NOT NULL AND NifNumber != '';
                    CREATE INDEX IF NOT EXISTS idx_activities_nis_number ON Activities(NisNumber) WHERE NisNumber IS NOT NULL AND NisNumber != '';
                    CREATE INDEX IF NOT EXISTS idx_activities_art_number ON Activities(ArtNumber) WHERE ArtNumber IS NOT NULL AND ArtNumber != '';
                    CREATE INDEX IF NOT EXISTS idx_activities_cpi_location ON Activities(CpiWilaya, CpiDaira);
                    CREATE INDEX IF NOT EXISTS idx_activities_start_date ON Activities(ActivityStartDate);";

                await connection.ExecuteAsync(recreateIndexesSql, transaction: transaction);

                LoggingService.LogDebug("Version 6 migration completed successfully", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during version 6 migration: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Gets the description for a specific migration version.
        /// </summary>
        private string GetMigrationDescription(int version)
        {
            return version switch
            {
                1 => "Initial UFU2 database schema with version tracking",
                2 => "Remove NationalId format constraint to allow flexible input",
                3 => "Add high-priority composite indexes for performance optimization",
                4 => "Add medium-priority composite indexes for enhanced performance optimization",
                5 => "Remove cross-database FK from CraftActivityCodes (client DB)",
                6 => "Fix CHECK constraints to allow empty strings for NIF, NIS, and ART numbers",
                _ => $"Migration to version {version}"
            };
        }

        /// <summary>
        /// Updates the schema version tracking table.
        /// </summary>
        private async Task UpdateSchemaVersionAsync(SqliteConnection connection, SqliteTransaction transaction, int version, string description)
        {
            string schemaVersionTableName = GetSchemaVersionTableName();

            string updateVersionSql = $@"
                INSERT OR REPLACE INTO {schemaVersionTableName} (Version, AppliedAt, Description)
                VALUES (@Version, datetime('now'), @Description)";

            await connection.ExecuteAsync(updateVersionSql,
                new { Version = version, Description = description },
                transaction: transaction);

            LoggingService.LogDebug($"Updated schema version to {version}: {description}", "DatabaseMigrationService");
        }

        /// <summary>
        /// Gets the appropriate schema resource name based on the database type.
        /// </summary>
        /// <returns>Resource name for the schema SQL file</returns>
        private string GetSchemaResourceName()
        {
            return _databaseService.DatabaseType switch
            {
                DatabaseType.ClientData => "UFU2.Database.UFU2_Schema.sql",
                DatabaseType.ReferenceData => "UFU2.Database.APP_Schema.sql",
                DatabaseType.ArchiveData => "UFU2.Database.Archive_Schema.sql",
                _ => throw new ArgumentException($"Unsupported database type: {_databaseService.DatabaseType}")
            };
        }

        /// <summary>
        /// Gets the appropriate schema version table name based on the database type.
        /// </summary>
        /// <returns>Schema version table name for the database type</returns>
        private string GetSchemaVersionTableName()
        {
            return _databaseService.DatabaseType switch
            {
                DatabaseType.ClientData => "SchemaVersion",
                DatabaseType.ReferenceData => "ReferenceSchemaVersion",
                DatabaseType.ArchiveData => "ArchiveSchemaVersion",
                _ => throw new ArgumentException($"Unsupported database type: {_databaseService.DatabaseType}")
            };
        }

        /// <summary>
        /// Gets the required tables for validation based on the database type.
        /// </summary>
        /// <returns>Array of required table names for the database type</returns>
        private string[] GetRequiredTablesForDatabaseType()
        {
            return _databaseService.DatabaseType switch
            {
                DatabaseType.ClientData => new[]
                {
                    "Clients", "PhoneNumbers", "Activities", "CommercialActivityCodes",
                    "ProfessionNames", "FileCheckStates", "Notes",
                    "G12Check", "BisCheck", "UidSequences", "SchemaVersion"
                },
                DatabaseType.ReferenceData => new[]
                {
                    "ActivityTypeBase", "CraftTypeBase", "CpiWilayas", "CpiDairas", "ReferenceSchemaVersion"
                },
                DatabaseType.ArchiveData => new[]
                {
                    "AddedEntities", "UpdatedEntities", "DeletedEntities", "ArchiveSchemaVersion"
                },
                _ => throw new ArgumentException($"Unsupported database type: {_databaseService.DatabaseType}")
            };
        }

        /// <summary>
        /// Gets the critical indexes for validation based on the database type.
        /// </summary>
        /// <returns>Array of critical index names for the database type</returns>
        private string[] GetCriticalIndexesForDatabaseType()
        {
            return _databaseService.DatabaseType switch
            {
                DatabaseType.ClientData => new[]
                {
                    "idx_clients_name_fr", "idx_phone_numbers_client_uid", "idx_activities_client_uid",
                    "idx_file_check_states_activity_uid", "idx_notes_activity_uid",
                    "idx_clients_arabic_search", "idx_activities_type_client", "idx_phone_numbers_composite"
                },
                DatabaseType.ReferenceData => new[]
                {
                    // Reference database typically doesn't have complex indexes, just basic ones
                    "idx_activity_type_code", "idx_craft_type_code", "idx_cpi_wilayas_code", "idx_cpi_dairas_code"
                },
                DatabaseType.ArchiveData => new[]
                {
                    // Archive database indexes for efficient audit trail queries
                    "idx_added_entities_type_id", "idx_updated_entities_type_id", "idx_deleted_entities_type_id"
                },
                _ => new string[0] // Return empty array for unsupported database types
            };
        }

        /// <summary>
        /// Reads SQL schema from embedded resource.
        /// </summary>
        /// <param name="resourceName">Full name of the embedded resource</param>
        /// <returns>SQL content as string</returns>
        private string ReadEmbeddedSqlResource(string resourceName)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();

                using (Stream stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream == null)
                    {
                        LoggingService.LogError($"Embedded resource not found: {resourceName}", "DatabaseMigrationService");

                        // Log available resources for debugging
                        var availableResources = assembly.GetManifestResourceNames();
                        LoggingService.LogDebug($"Available embedded resources: {string.Join(", ", availableResources)}", "DatabaseMigrationService");

                        return null;
                    }

                    using (StreamReader reader = new StreamReader(stream))
                    {
                        string content = reader.ReadToEnd();
                        LoggingService.LogDebug($"Successfully read embedded resource: {resourceName} ({content.Length} characters)", "DatabaseMigrationService");
                        return content;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error reading embedded resource {resourceName}: {ex.Message}", "DatabaseMigrationService");
                return null;
            }
        }

        /// <summary>
        /// Splits SQL script into individual statements for execution.
        /// Handles complex statements like triggers that contain semicolons within BEGIN...END blocks.
        /// </summary>
        private string[] SplitSqlStatements(string sql)
        {
            var statements = new List<string>();
            var lines = sql.Split('\n');
            var currentStatement = new StringBuilder();
            int beginEndDepth = 0;
            bool inTrigger = false;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                // Skip comment lines and empty lines
                if (trimmedLine.StartsWith("--") || string.IsNullOrWhiteSpace(trimmedLine))
                {
                    continue;
                }

                // Check if we're starting a trigger
                if (trimmedLine.StartsWith("CREATE TRIGGER", StringComparison.OrdinalIgnoreCase))
                {
                    inTrigger = true;
                }

                // Track BEGIN/END depth for triggers and other complex statements
                if (trimmedLine.Equals("BEGIN", StringComparison.OrdinalIgnoreCase))
                {
                    beginEndDepth++;
                }
                else if (trimmedLine.Equals("END;", StringComparison.OrdinalIgnoreCase) ||
                         trimmedLine.Equals("END", StringComparison.OrdinalIgnoreCase))
                {
                    beginEndDepth--;
                }

                currentStatement.AppendLine(line);

                // Check if statement is complete
                bool isStatementComplete = false;

                if (inTrigger)
                {
                    // For triggers, statement is complete when we reach END; and depth is 0
                    if (beginEndDepth == 0 && (trimmedLine.Equals("END;", StringComparison.OrdinalIgnoreCase) ||
                                               trimmedLine.EndsWith("END;", StringComparison.OrdinalIgnoreCase)))
                    {
                        isStatementComplete = true;
                        inTrigger = false;
                    }
                }
                else
                {
                    // For regular statements, complete when line ends with semicolon and not inside BEGIN/END
                    if (trimmedLine.EndsWith(";") && beginEndDepth == 0)
                    {
                        isStatementComplete = true;
                    }
                }

                if (isStatementComplete)
                {
                    var statement = currentStatement.ToString().Trim();
                    if (!string.IsNullOrWhiteSpace(statement))
                    {
                        statements.Add(statement);
                    }
                    currentStatement.Clear();
                }
            }

            // Add any remaining statement
            var finalStatement = currentStatement.ToString().Trim();
            if (!string.IsNullOrWhiteSpace(finalStatement))
            {
                statements.Add(finalStatement);
            }

            return statements.ToArray();
        }

        /// <summary>
        /// Validates the database schema integrity with comprehensive checks.
        /// Verifies table existence, constraints, indexes, and relationships.
        /// </summary>
        public async Task<bool> ValidateSchemaAsync()
        {
            LoggingService.LogDebug("Starting comprehensive database schema validation", "DatabaseMigrationService");

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var validationResults = new List<string>();
                bool isValid = true;

                // 1. Check that all required tables exist
                var requiredTables = GetRequiredTablesForDatabaseType();

                LoggingService.LogDebug($"Validating {requiredTables.Length} required tables", "DatabaseMigrationService");

                foreach (var tableName in requiredTables)
                {
                    const string checkTableSql = @"
                        SELECT COUNT(*) FROM sqlite_master
                        WHERE type='table' AND name=@TableName";

                    int tableExists = await connection.ExecuteScalarAsync<int>(checkTableSql, new { TableName = tableName });

                    if (tableExists == 0)
                    {
                        string errorMsg = $"Required table '{tableName}' is missing";
                        LoggingService.LogError(errorMsg, "DatabaseMigrationService");
                        validationResults.Add(errorMsg);
                        isValid = false;
                    }
                    else
                    {
                        LoggingService.LogDebug($"Table '{tableName}' exists", "DatabaseMigrationService");
                    }
                }

                // 2. Verify foreign key constraints are enabled
                var foreignKeysEnabled = await connection.ExecuteScalarAsync<int>("PRAGMA foreign_keys");
                if (foreignKeysEnabled != 1)
                {
                    string warningMsg = "Foreign key constraints are not enabled";
                    LoggingService.LogWarning(warningMsg, "DatabaseMigrationService");
                    validationResults.Add(warningMsg);
                    // This is a warning, not a validation failure
                }

                // 3. Check critical indexes exist (database-type-specific)
                var criticalIndexes = GetCriticalIndexesForDatabaseType();

                LoggingService.LogDebug($"Validating {criticalIndexes.Length} critical indexes for {_databaseService.DatabaseType} database", "DatabaseMigrationService");

                foreach (var indexName in criticalIndexes)
                {
                    const string checkIndexSql = @"
                        SELECT COUNT(*) FROM sqlite_master
                        WHERE type='index' AND name=@IndexName";

                    int indexExists = await connection.ExecuteScalarAsync<int>(checkIndexSql, new { IndexName = indexName });

                    if (indexExists == 0)
                    {
                        string warningMsg = $"Critical index '{indexName}' is missing";
                        LoggingService.LogWarning(warningMsg, "DatabaseMigrationService");
                        validationResults.Add(warningMsg);
                        // Missing indexes are warnings, not validation failures
                    }
                    else
                    {
                        LoggingService.LogDebug($"Index '{indexName}' exists", "DatabaseMigrationService");
                    }
                }

                // 4. Validate schema version
                int currentVersion = await GetCurrentSchemaVersionAsync(connection);
                if (currentVersion != CURRENT_SCHEMA_VERSION)
                {
                    string errorMsg = $"Schema version mismatch: expected {CURRENT_SCHEMA_VERSION}, found {currentVersion}";
                    LoggingService.LogError(errorMsg, "DatabaseMigrationService");
                    validationResults.Add(errorMsg);
                    isValid = false;
                }

                // 5. Test basic table operations (insert/select/delete)
                await ValidateBasicOperationsAsync(connection);

                // 6. Check for data integrity issues
                await ValidateDataIntegrityAsync(connection);

                if (isValid)
                {
                    LoggingService.LogDebug("Database schema validation completed successfully", "DatabaseMigrationService");
                }
                else
                {
                    LoggingService.LogError($"Database schema validation failed with {validationResults.Count} issues", "DatabaseMigrationService");
                    foreach (var result in validationResults)
                    {
                        LoggingService.LogError($"Validation issue: {result}", "DatabaseMigrationService");
                    }
                }

                return isValid;
            }
            catch (Exception ex)
            {
                string arabicMessage = "فشل في التحقق من صحة مخطط قاعدة البيانات";
                string englishTitle = "Schema Validation Failed";

                ErrorManager.HandleErrorToast(ex, arabicMessage, englishTitle, LogLevel.Error, "DatabaseMigrationService");
                LoggingService.LogError($"Schema validation exception: {ex.Message}", "DatabaseMigrationService");
                return false;
            }
        }

        /// <summary>
        /// Validates basic database operations to ensure the schema is functional.
        /// </summary>
        private async Task ValidateBasicOperationsAsync(SqliteConnection connection)
        {
            LoggingService.LogDebug("Validating basic database operations", "DatabaseMigrationService");

            try
            {
                string schemaVersionTableName = GetSchemaVersionTableName();

                // Test a simple query on database-specific critical tables
                switch (_databaseService.DatabaseType)
                {
                    case DatabaseType.ClientData:
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Clients");
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Activities");
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM PhoneNumbers");
                        break;
                    case DatabaseType.ReferenceData:
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM ActivityTypeBase");
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM CraftTypeBase");
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM CpiWilayas");
                        break;
                    case DatabaseType.ArchiveData:
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM AddedEntities");
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM UpdatedEntities");
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM DeletedEntities");
                        break;
                }

                // Test schema version table for all database types
                await connection.ExecuteScalarAsync<int>($"SELECT COUNT(*) FROM {schemaVersionTableName}");

                LoggingService.LogDebug("Basic database operations validated successfully", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Basic operations validation failed: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Validates data integrity by checking foreign key relationships specific to each database type.
        /// </summary>
        private async Task ValidateDataIntegrityAsync(SqliteConnection connection)
        {
            LoggingService.LogDebug($"Validating data integrity for {_databaseService.DatabaseType} database", "DatabaseMigrationService");

            try
            {
                // Perform database-specific integrity checks
                switch (_databaseService.DatabaseType)
                {
                    case DatabaseType.ClientData:
                        await ValidateClientDataIntegrityAsync(connection);
                        break;
                    case DatabaseType.ReferenceData:
                        await ValidateReferenceDataIntegrityAsync(connection);
                        break;
                    case DatabaseType.ArchiveData:
                        await ValidateArchiveDataIntegrityAsync(connection);
                        break;
                    default:
                        LoggingService.LogWarning($"No specific integrity validation for database type: {_databaseService.DatabaseType}", "DatabaseMigrationService");
                        break;
                }

                LoggingService.LogDebug($"Data integrity validation completed for {_databaseService.DatabaseType} database", "DatabaseMigrationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Data integrity validation failed for {_databaseService.DatabaseType} database: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Validates data integrity for client database (UFU2_Database.db).
        /// Checks for orphaned records in key relationships.
        /// </summary>
        private async Task ValidateClientDataIntegrityAsync(SqliteConnection connection)
        {
            LoggingService.LogDebug("Validating client data integrity", "DatabaseMigrationService");

            // Check for orphaned activities
            const string orphanedActivitiesSql = @"
                SELECT COUNT(*) FROM Activities a
                LEFT JOIN Clients c ON a.ClientUid = c.Uid
                WHERE c.Uid IS NULL";

            int orphanedActivities = await connection.ExecuteScalarAsync<int>(orphanedActivitiesSql);
            if (orphanedActivities > 0)
            {
                LoggingService.LogWarning($"Found {orphanedActivities} orphaned activities", "DatabaseMigrationService");
            }

            // Check for orphaned phone numbers
            const string orphanedPhonesSql = @"
                SELECT COUNT(*) FROM PhoneNumbers p
                LEFT JOIN Clients c ON p.ClientUid = c.Uid
                WHERE c.Uid IS NULL";

            int orphanedPhones = await connection.ExecuteScalarAsync<int>(orphanedPhonesSql);
            if (orphanedPhones > 0)
            {
                LoggingService.LogWarning($"Found {orphanedPhones} orphaned phone numbers", "DatabaseMigrationService");
            }

            // Check for orphaned notes
            const string orphanedNotesSql = @"
                SELECT COUNT(*) FROM Notes n
                LEFT JOIN Activities a ON n.ActivityUid = a.Uid
                WHERE a.Uid IS NULL";

            int orphanedNotes = await connection.ExecuteScalarAsync<int>(orphanedNotesSql);
            if (orphanedNotes > 0)
            {
                LoggingService.LogWarning($"Found {orphanedNotes} orphaned notes", "DatabaseMigrationService");
            }

            // Check for orphaned file check states
            const string orphanedFileChecksSql = @"
                SELECT COUNT(*) FROM FileCheckStates f
                LEFT JOIN Activities a ON f.ActivityUid = a.Uid
                WHERE a.Uid IS NULL";

            int orphanedFileChecks = await connection.ExecuteScalarAsync<int>(orphanedFileChecksSql);
            if (orphanedFileChecks > 0)
            {
                LoggingService.LogWarning($"Found {orphanedFileChecks} orphaned file check states", "DatabaseMigrationService");
            }

            LoggingService.LogDebug("Client data integrity validation completed", "DatabaseMigrationService");
        }

        /// <summary>
        /// Validates data integrity for reference database (APP_Database.db).
        /// Checks for data consistency in reference tables.
        /// </summary>
        private async Task ValidateReferenceDataIntegrityAsync(SqliteConnection connection)
        {
            LoggingService.LogDebug("Validating reference data integrity", "DatabaseMigrationService");

            // Check for empty activity types
            const string emptyActivityTypesSql = @"
                SELECT COUNT(*) FROM ActivityTypeBase
                WHERE Code IS NULL OR trim(Code) = '' OR Description IS NULL OR trim(Description) = ''";

            int emptyActivityTypes = await connection.ExecuteScalarAsync<int>(emptyActivityTypesSql);
            if (emptyActivityTypes > 0)
            {
                LoggingService.LogWarning($"Found {emptyActivityTypes} activity types with empty code or description", "DatabaseMigrationService");
            }

            // Check for empty craft types
            const string emptyCraftTypesSql = @"
                SELECT COUNT(*) FROM CraftTypeBase
                WHERE Code IS NULL OR trim(Code) = '' OR Description IS NULL OR trim(Description) = ''";

            int emptyCraftTypes = await connection.ExecuteScalarAsync<int>(emptyCraftTypesSql);
            if (emptyCraftTypes > 0)
            {
                LoggingService.LogWarning($"Found {emptyCraftTypes} craft types with empty code or description", "DatabaseMigrationService");
            }

            // Check for empty wilaya data
            const string emptyWilayasSql = @"
                SELECT COUNT(*) FROM CpiWilayas
                WHERE Code IS NULL OR trim(Code) = '' OR NameAr IS NULL OR trim(NameAr) = ''
                   OR NameFr IS NULL OR trim(NameFr) = ''";

            int emptyWilayas = await connection.ExecuteScalarAsync<int>(emptyWilayasSql);
            if (emptyWilayas > 0)
            {
                LoggingService.LogWarning($"Found {emptyWilayas} wilayas with empty required fields", "DatabaseMigrationService");
            }

            LoggingService.LogDebug("Reference data integrity validation completed", "DatabaseMigrationService");
        }

        /// <summary>
        /// Validates data integrity for archive database (Archive_Database.db).
        /// Checks for consistency in audit trail data.
        /// </summary>
        private async Task ValidateArchiveDataIntegrityAsync(SqliteConnection connection)
        {
            LoggingService.LogDebug("Validating archive data integrity", "DatabaseMigrationService");

            // Check for empty entity types in added entities
            const string emptyAddedEntitiesSql = @"
                SELECT COUNT(*) FROM AddedEntities
                WHERE EntityType IS NULL OR trim(EntityType) = ''
                   OR EntityId IS NULL OR trim(EntityId) = ''
                   OR DataName IS NULL OR trim(DataName) = ''";

            int emptyAddedEntities = await connection.ExecuteScalarAsync<int>(emptyAddedEntitiesSql);
            if (emptyAddedEntities > 0)
            {
                LoggingService.LogWarning($"Found {emptyAddedEntities} added entities with empty required fields", "DatabaseMigrationService");
            }

            // Check for empty entity types in updated entities
            const string emptyUpdatedEntitiesSql = @"
                SELECT COUNT(*) FROM UpdatedEntities
                WHERE EntityType IS NULL OR trim(EntityType) = ''
                   OR EntityId IS NULL OR trim(EntityId) = ''
                   OR DataName IS NULL OR trim(DataName) = ''";

            int emptyUpdatedEntities = await connection.ExecuteScalarAsync<int>(emptyUpdatedEntitiesSql);
            if (emptyUpdatedEntities > 0)
            {
                LoggingService.LogWarning($"Found {emptyUpdatedEntities} updated entities with empty required fields", "DatabaseMigrationService");
            }

            // Check for empty entity types in deleted entities
            const string emptyDeletedEntitiesSql = @"
                SELECT COUNT(*) FROM DeletedEntities
                WHERE EntityType IS NULL OR trim(EntityType) = ''
                   OR EntityId IS NULL OR trim(EntityId) = ''";

            int emptyDeletedEntities = await connection.ExecuteScalarAsync<int>(emptyDeletedEntitiesSql);
            if (emptyDeletedEntities > 0)
            {
                LoggingService.LogWarning($"Found {emptyDeletedEntities} deleted entities with empty required fields", "DatabaseMigrationService");
            }

            LoggingService.LogDebug("Archive data integrity validation completed", "DatabaseMigrationService");
        }

        /// <summary>
        /// Gets database statistics for monitoring and debugging.
        /// Provides comprehensive information about database state and performance specific to each database type.
        /// </summary>
        public async Task<DatabaseStats> GetDatabaseStatsAsync()
        {
            LoggingService.LogDebug($"Collecting database statistics for {_databaseService.DatabaseType} database", "DatabaseMigrationService");

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var stats = new DatabaseStats();

                // Get database-specific table counts
                await PopulateDatabaseSpecificStatsAsync(connection, stats);

                // Get database file size
                var dbPath = _databaseService.GetDatabasePath();
                if (File.Exists(dbPath))
                {
                    var fileInfo = new FileInfo(dbPath);
                    stats.DatabaseSizeBytes = fileInfo.Length;
                }

                // Get schema version
                stats.SchemaVersion = await GetCurrentSchemaVersionAsync(connection);

                // Get additional statistics
                stats.LastMigrationDate = await GetLastMigrationDateAsync(connection);
                stats.TableCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM sqlite_master WHERE type='table'");
                stats.IndexCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM sqlite_master WHERE type='index'");

                LoggingService.LogDebug($"Database stats collected for {_databaseService.DatabaseType}: {stats.DatabaseSizeMB} MB size", "DatabaseMigrationService");
                return stats;
            }
            catch (Exception ex)
            {
                string arabicMessage = "فشل في الحصول على إحصائيات قاعدة البيانات";
                string englishTitle = "Database Statistics Failed";

                ErrorManager.HandleErrorToast(ex, arabicMessage, englishTitle, LogLevel.Error, "DatabaseMigrationService");
                LoggingService.LogError($"Failed to collect database statistics: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Populates database-specific statistics based on the database type.
        /// Uses existing DatabaseStats properties and logs database-specific information.
        /// </summary>
        private async Task PopulateDatabaseSpecificStatsAsync(SqliteConnection connection, DatabaseStats stats)
        {
            switch (_databaseService.DatabaseType)
            {
                case DatabaseType.ClientData:
                    // Client database statistics - use all available properties
                    stats.ClientCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Clients");
                    stats.ActivityCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Activities");
                    stats.PhoneNumberCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM PhoneNumbers");
                    stats.NoteCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Notes");
                    stats.FileCheckStateCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM FileCheckStates");
                    LoggingService.LogDebug($"Client database stats: {stats.ClientCount} clients, {stats.ActivityCount} activities", "DatabaseMigrationService");
                    break;

                case DatabaseType.ReferenceData:
                    // Reference database statistics - use generic properties and log specific counts
                    int activityTypeCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM ActivityTypeBase");
                    int craftTypeCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM CraftTypeBase");
                    int wilayaCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM CpiWilayas");
                    int dairaCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM CpiDairas");

                    // Store in generic properties for compatibility
                    stats.ClientCount = activityTypeCount; // Repurpose for activity types
                    stats.ActivityCount = craftTypeCount; // Repurpose for craft types
                    stats.PhoneNumberCount = wilayaCount; // Repurpose for wilayas
                    stats.NoteCount = dairaCount; // Repurpose for dairas
                    stats.FileCheckStateCount = 0; // Not applicable for reference data

                    LoggingService.LogDebug($"Reference database stats: {activityTypeCount} activity types, {craftTypeCount} craft types, {wilayaCount} wilayas, {dairaCount} dairas", "DatabaseMigrationService");
                    break;

                case DatabaseType.ArchiveData:
                    // Archive database statistics - use generic properties and log specific counts
                    int addedEntitiesCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM AddedEntities");
                    int updatedEntitiesCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM UpdatedEntities");
                    int deletedEntitiesCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM DeletedEntities");

                    // Store in generic properties for compatibility
                    stats.ClientCount = addedEntitiesCount; // Repurpose for added entities
                    stats.ActivityCount = updatedEntitiesCount; // Repurpose for updated entities
                    stats.PhoneNumberCount = deletedEntitiesCount; // Repurpose for deleted entities
                    stats.NoteCount = 0; // Not applicable for archive data
                    stats.FileCheckStateCount = 0; // Not applicable for archive data

                    LoggingService.LogDebug($"Archive database stats: {addedEntitiesCount} added, {updatedEntitiesCount} updated, {deletedEntitiesCount} deleted", "DatabaseMigrationService");
                    break;

                default:
                    LoggingService.LogWarning($"No specific statistics collection for database type: {_databaseService.DatabaseType}", "DatabaseMigrationService");
                    // Set all counts to 0 for unknown database types
                    stats.ClientCount = 0;
                    stats.ActivityCount = 0;
                    stats.PhoneNumberCount = 0;
                    stats.NoteCount = 0;
                    stats.FileCheckStateCount = 0;
                    break;
            }
        }

        /// <summary>
        /// Gets the date of the last migration applied to the database.
        /// </summary>
        private async Task<DateTime?> GetLastMigrationDateAsync(SqliteConnection connection)
        {
            try
            {
                string schemaVersionTableName = GetSchemaVersionTableName();

                string getLastMigrationSql = $@"
                    SELECT AppliedAt FROM {schemaVersionTableName}
                    ORDER BY Version DESC LIMIT 1";

                string lastMigrationStr = await connection.ExecuteScalarAsync<string>(getLastMigrationSql);

                if (DateTime.TryParse(lastMigrationStr, out DateTime lastMigration))
                {
                    return lastMigration;
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Performs database maintenance operations including VACUUM and ANALYZE.
        /// Should be called periodically to maintain optimal database performance.
        /// </summary>
        public async Task PerformMaintenanceAsync()
        {
            LoggingService.LogInfo("Starting database maintenance operations", "DatabaseMigrationService");

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Get database size before maintenance
                var statsBefore = await GetDatabaseStatsAsync();

                LoggingService.LogDebug("Performing VACUUM operation", "DatabaseMigrationService");
                await connection.ExecuteAsync("VACUUM");

                LoggingService.LogDebug("Performing ANALYZE operation", "DatabaseMigrationService");
                await connection.ExecuteAsync("ANALYZE");

                // Get database size after maintenance
                var statsAfter = await GetDatabaseStatsAsync();

                long spaceSaved = statsBefore.DatabaseSizeBytes - statsAfter.DatabaseSizeBytes;
                if (spaceSaved > 0)
                {
                    LoggingService.LogInfo($"Database maintenance completed. Space saved: {spaceSaved / (1024.0 * 1024.0):F2} MB", "DatabaseMigrationService");
                }
                else
                {
                    LoggingService.LogInfo("Database maintenance completed", "DatabaseMigrationService");
                }
            }
            catch (Exception ex)
            {
                string arabicMessage = "فشل في صيانة قاعدة البيانات";
                string englishTitle = "Database Maintenance Failed";

                ErrorManager.HandleErrorToast(ex, arabicMessage, englishTitle, LogLevel.Error, "DatabaseMigrationService");
                LoggingService.LogError($"Database maintenance failed: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }

        /// <summary>
        /// Creates a backup of the current database.
        /// Useful before applying major migrations or maintenance operations.
        /// </summary>
        public async Task<string> CreateBackupAsync(string backupPath = null)
        {
            LoggingService.LogInfo("Creating database backup", "DatabaseMigrationService");

            try
            {
                string sourcePath = _databaseService.GetDatabasePath();

                if (backupPath == null)
                {
                    string backupDir = Path.Combine(Path.GetDirectoryName(sourcePath), "Backups");
                    Directory.CreateDirectory(backupDir);

                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    backupPath = Path.Combine(backupDir, $"UFU2_Database_Backup_{timestamp}.db");
                }

                // Ensure the backup directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(backupPath));

                // Copy the database file
                File.Copy(sourcePath, backupPath, overwrite: true);

                // Verify the backup
                var backupInfo = new FileInfo(backupPath);
                if (backupInfo.Exists && backupInfo.Length > 0)
                {
                    LoggingService.LogInfo($"Database backup created successfully: {backupPath} ({backupInfo.Length / (1024.0 * 1024.0):F2} MB)", "DatabaseMigrationService");
                    return backupPath;
                }
                else
                {
                    throw new InvalidOperationException("Backup file was not created properly");
                }
            }
            catch (Exception ex)
            {
                string arabicMessage = "فشل في إنشاء نسخة احتياطية من قاعدة البيانات";
                string englishTitle = "Database Backup Failed";

                ErrorManager.HandleErrorToast(ex, arabicMessage, englishTitle, LogLevel.Error, "DatabaseMigrationService");
                LoggingService.LogError($"Database backup failed: {ex.Message}", "DatabaseMigrationService");
                throw;
            }
        }
    }

    /// <summary>
    /// Database statistics for monitoring and debugging.
    /// Provides comprehensive information about database state and performance.
    /// </summary>
    public class DatabaseStats
    {
        public int ClientCount { get; set; }
        public int ActivityCount { get; set; }
        public int PhoneNumberCount { get; set; }
        public int NoteCount { get; set; }
        public int FileCheckStateCount { get; set; }
        public long DatabaseSizeBytes { get; set; }
        public int SchemaVersion { get; set; }
        public DateTime? LastMigrationDate { get; set; }
        public int TableCount { get; set; }
        public int IndexCount { get; set; }

        public string DatabaseSizeMB => $"{DatabaseSizeBytes / (1024.0 * 1024.0):F2} MB";
        public string LastMigrationDateFormatted => LastMigrationDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Unknown";
    }

    /// <summary>
    /// Exception thrown when database migration operations fail.
    /// Provides specific context for migration-related errors.
    /// </summary>
    public class DatabaseMigrationException : Exception
    {
        public DatabaseMigrationException(string message) : base(message)
        {
        }

        public DatabaseMigrationException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
