using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that controls visibility of multiple activities management features.
    /// Shows elements only for MainCommercial and SecondaryCommercial activity types.
    /// Returns Visible for supported activity types, Collapsed for others.
    /// </summary>
    public class MultipleActivitiesVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts activity type to visibility for multiple activities features.
        /// </summary>
        /// <param name="value">The activity type string</param>
        /// <param name="targetType">The target type (Visibility)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info</param>
        /// <returns>Visible for MainCommercial/SecondaryCommercial, Collapsed for others</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string activityType)
            {
                return activityType switch
                {
                    "MainCommercial" => Visibility.Visible,
                    "SecondaryCommercial" => Visibility.Visible,
                    _ => Visibility.Collapsed
                };
            }

            return Visibility.Collapsed;
        }

        /// <summary>
        /// Converts back from visibility to activity type (not implemented).
        /// </summary>
        /// <param name="value">The visibility value</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">Optional parameter</param>
        /// <param name="culture">The culture info</param>
        /// <returns>Not implemented</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ConvertBack is not supported for MultipleActivitiesVisibilityConverter");
        }
    }
}
