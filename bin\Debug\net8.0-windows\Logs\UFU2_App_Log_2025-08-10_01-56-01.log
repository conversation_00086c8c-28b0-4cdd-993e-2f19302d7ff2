=== UFU2 Application Session Started at 2025-08-10 01:56:01 ===
[2025-08-10 01:56:01.495]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-10 01:56:01.498]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-10 01:56:01.502]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-10 01:56:01.504]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-10 01:56:01.513]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-10 01:56:01.516]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-10 01:56:01.519]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-10 01:56:01.524]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-10 01:56:01.527]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-10 01:56:01.530]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-10 01:56:01.532]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-10 01:56:01.535]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-10 01:56:01.538]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-10 01:56:01.541]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-10 01:56:01.543]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-10 01:56:01.546]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-10 01:56:01.548]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-10 01:56:01.552]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-10 01:56:01.561]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-10 01:56:01.564]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-10 01:56:01.568]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-10 01:56:01.571]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-10 01:56:01.574]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-10 01:56:01.578]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-10 01:56:01.582]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-10 01:56:01.586]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-10 01:56:01.589]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-10 01:56:01.593]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-10 01:56:01.596]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-10 01:56:01.599]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-10 01:56:01.602]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-10 01:56:01.605]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-10 01:56:01.609]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-10 01:56:01.613]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-10 01:56:01.655]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-10 01:56:01.673]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-10 01:56:01.677]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-10 01:56:01.681]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-10 01:56:01.695]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 104.06MB working set
[2025-08-10 01:56:01.699]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-10 01:56:01.702]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-10 01:56:01.705]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-10 01:56:01.709]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-10 01:56:01.712]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-10 01:56:01.717]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-10 01:56:01.739]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-10 01:56:01.744]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-10 01:56:01.747]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-10 01:56:02.038]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-10 01:56:02.042]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-10 01:56:02.046]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-10 01:56:02.049]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-10 01:56:02.056]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638903841620546920 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-10 01:56:02.084]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-10 01:56:02.088]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:02.092]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-10 01:56:02.102]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-10 01:56:02.107]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-10 01:56:02.111]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-10 01:56:02.114]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-10 01:56:02.118]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-10 01:56:02.122]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-10 01:56:02.125]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-10 01:56:02.128]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-10 01:56:02.131]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-10 01:56:02.136]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-10 01:56:02.141]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-10 01:56:02.144]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-10 01:56:02.148]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-10 01:56:02.151]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-10 01:56:02.155]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-10 01:56:02.159]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-10 01:56:02.163]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-10 01:56:02.167]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-10 01:56:02.172]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-10 01:56:02.176]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-10 01:56:02.179]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-10 01:56:02.183]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-10 01:56:02.187]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-10 01:56:02.192]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-10 01:56:02.196]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-10 01:56:02.200]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-10 01:56:02.204]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-10 01:56:02.207]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-10 01:56:02.211]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-10 01:56:02.215]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-10 01:56:02.219]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-10 01:56:02.222]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-10 01:56:02.225]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-10 01:56:02.229]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-10 01:56:02.232]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-10 01:56:02.236]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-10 01:56:02.239]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-10 01:56:02.243]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-10 01:56:02.247]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-10 01:56:02.251]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-10 01:56:02.255]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-10 01:56:02.258]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-10 01:56:02.262]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-10 01:56:02.265]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-10 01:56:02.269]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-10 01:56:02.272]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-10 01:56:02.275]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-10 01:56:02.278]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-10 01:56:02.282]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-10 01:56:02.286]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-10 01:56:02.289]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-10 01:56:02.294]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-10 01:56:02.298]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-10 01:56:02.301]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-10 01:56:02.307]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-10 01:56:02.311]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-10 01:56:02.314]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-10 01:56:02.318]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-10 01:56:02.326]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-10 01:56:02.331]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-10 01:56:02.334]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-10 01:56:02.341]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 624ms
[2025-08-10 01:56:02.345]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-10 01:56:02.351]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-10 01:56:02.366]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-10 01:56:02.370]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-10 01:56:02.374]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-10 01:56:02.377]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-10 01:56:02.382]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-10 01:56:02.387]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-10 01:56:02.391]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 01:56:02.395]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-10 01:56:02.399]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 01:56:02.404]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-10 01:56:02.408]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 01:56:02.412]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-10 01:56:02.418]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-10 01:56:02.423]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 01:56:02.464]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 01:56:02.470]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-10 01:56:02.469]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 01:56:02.464]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 01:56:02.471]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 01:56:02.500]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 01:56:02.516]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 01:56:02.486]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 01:56:02.476]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 01:56:02.481]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 01:56:02.529]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 01:56:02.533]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 01:56:02.541]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 01:56:02.547]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 01:56:02.556]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 01:56:02.560]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 01:56:02.563]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 01:56:02.567]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 01:56:02.573]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 01:56:02.583]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 6
[2025-08-10 01:56:02.587]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 01:56:02.592]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 01:56:02.599]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.UFU2_Schema.sql
[2025-08-10 01:56:02.605]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.UFU2_Schema.sql (19219 characters)
[2025-08-10 01:56:02.611]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 01:56:02.617]  	[DEBUG]		[DatabaseMigrationService]	Executing 55 SQL statements
[2025-08-10 01:56:02.623]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/55 statements
[2025-08-10 01:56:02.628]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/55 statements
[2025-08-10 01:56:02.635]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/55 statements
[2025-08-10 01:56:02.640]  	[DEBUG]		[DatabaseMigrationService]	Executed 40/55 statements
[2025-08-10 01:56:02.645]  	[DEBUG]		[DatabaseMigrationService]	Executed 50/55 statements
[2025-08-10 01:56:02.670]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 6: Initial UFU2 database schema
[2025-08-10 01:56:02.688]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (55 statements executed)
[2025-08-10 01:56:02.693]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 01:56:02.698]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 01:56:02.703]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:02.707]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 01:56:02.712]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 01:56:02.715]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 01:56:02.719]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 01:56:02.723]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 01:56:02.727]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 01:56:02.730]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 01:56:02.734]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 01:56:02.738]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 01:56:02.742]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 01:56:02.746]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 01:56:02.750]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 01:56:02.754]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 01:56:02.760]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 01:56:02.763]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 01:56:02.767]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 01:56:02.772]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 01:56:02.776]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 01:56:02.780]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 01:56:02.784]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 01:56:02.787]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 01:56:02.794]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 01:56:02.798]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 01:56:02.803]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 01:56:02.808]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 01:56:02.812]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 01:56:02.816]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 01:56:02.819]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 01:56:02.825]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 01:56:02.830]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:02.833]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-10 01:56:02.837]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 01:56:02.842]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:02.845]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 01:56:02.849]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 01:56:02.852]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 01:56:02.856]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 01:56:02.860]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 01:56:02.863]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 01:56:02.867]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 01:56:02.871]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 01:56:02.877]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 6
[2025-08-10 01:56:02.880]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 01:56:02.885]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 01:56:02.889]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.APP_Schema.sql
[2025-08-10 01:56:02.893]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.APP_Schema.sql (9406 characters)
[2025-08-10 01:56:02.897]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 01:56:02.901]  	[DEBUG]		[DatabaseMigrationService]	Executing 30 SQL statements
[2025-08-10 01:56:02.907]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/30 statements
[2025-08-10 01:56:02.913]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/30 statements
[2025-08-10 01:56:02.918]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/30 statements
[2025-08-10 01:56:02.923]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 6: Initial UFU2 database schema
[2025-08-10 01:56:02.940]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (30 statements executed)
[2025-08-10 01:56:02.944]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 01:56:02.948]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 01:56:02.953]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:02.963]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-10 01:56:02.984]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-10 01:56:02.993]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-10 01:56:03.008]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-10 01:56:03.019]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-10 01:56:03.030]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-10 01:56:03.034]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-10 01:56:03.039]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-10 01:56:03.043]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-10 01:56:03.047]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-10 01:56:03.052]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-10 01:56:03.056]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 01:56:03.060]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 01:56:03.064]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-10 01:56:03.070]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-10 01:56:03.074]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-10 01:56:03.078]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-10 01:56:03.082]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 01:56:03.086]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 01:56:03.089]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:03.093]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-10 01:56:03.097]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 01:56:03.100]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:03.104]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 01:56:03.107]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 01:56:03.111]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 01:56:03.115]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 01:56:03.119]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 01:56:03.122]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 01:56:03.126]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 01:56:03.130]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 01:56:03.136]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 6
[2025-08-10 01:56:03.141]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 01:56:03.145]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 01:56:03.149]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.Archive_Schema.sql
[2025-08-10 01:56:03.153]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.Archive_Schema.sql (7159 characters)
[2025-08-10 01:56:03.157]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 01:56:03.162]  	[DEBUG]		[DatabaseMigrationService]	Executing 16 SQL statements
[2025-08-10 01:56:03.167]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/16 statements
[2025-08-10 01:56:03.172]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 6: Initial UFU2 database schema
[2025-08-10 01:56:03.189]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (16 statements executed)
[2025-08-10 01:56:03.193]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 01:56:03.197]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 01:56:03.201]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:03.205]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-10 01:56:03.210]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-10 01:56:03.214]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-10 01:56:03.218]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-10 01:56:03.222]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-10 01:56:03.226]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-10 01:56:03.231]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-10 01:56:03.235]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-10 01:56:03.239]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-10 01:56:03.243]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 01:56:03.247]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 01:56:03.251]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-10 01:56:03.256]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-10 01:56:03.260]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-10 01:56:03.264]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-10 01:56:03.268]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 01:56:03.271]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 01:56:03.275]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:03.279]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 01:56:03.283]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:03.287]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 01:56:03.293]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 01:56:03.297]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 01:56:03.301]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 01:56:03.305]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 01:56:03.310]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 01:56:03.316]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 01:56:03.321]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 01:56:03.326]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 01:56:03.331]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 01:56:03.335]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 01:56:03.341]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 01:56:03.348]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 01:56:03.353]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 01:56:03.359]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 01:56:03.364]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 01:56:03.369]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 01:56:03.373]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 01:56:03.377]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 01:56:03.382]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 01:56:03.386]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 01:56:03.391]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 01:56:03.396]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 01:56:03.400]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 01:56:03.405]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 01:56:03.410]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 01:56:03.415]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 01:56:03.419]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 01:56:03.424]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-10 01:56:03.428]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-10 01:56:03.433]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-10 01:56:03.437]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-10 01:56:03.442]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-10 01:56:03.452]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-10 01:56:03.457]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-10 01:56:03.462]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-10 01:56:03.469]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-10 01:56:03.473]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-10 01:56:03.477]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-10 01:56:03.481]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-10 01:56:03.488]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-10 01:56:03.492]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-10 01:56:03.496]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-10 01:56:03.501]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-10 01:56:03.505]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-10 01:56:03.509]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-10 01:56:03.514]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-10 01:56:03.518]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-10 01:56:03.523]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-10 01:56:03.528]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-10 01:56:03.533]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:03.537]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-10 01:56:03.542]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-10 01:56:03.546]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-10 01:56:03.550]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-10 01:56:03.554]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-10 01:56:03.558]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-10 01:56:03.562]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-10 01:56:03.566]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-10 01:56:03.570]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-10 01:56:03.574]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-10 01:56:03.579]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-10 01:56:03.584]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-10 01:56:03.588]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-10 01:56:03.593]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:03.597]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-10 01:56:03.601]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:03.606]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:03.610]  	[INFO]		[ServiceLocator]	Seeding activity type data from embedded resource
[2025-08-10 01:56:03.615]  	[DEBUG]		[ActivityTypeBaseService]	Starting import from embedded JSON resource
[2025-08-10 01:56:03.697]  	[INFO]		[ActivityTypeBaseService]	Parsed 1028 activity types from JSON
[2025-08-10 01:56:03.701]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:03.716]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 1/11 (100 records)
[2025-08-10 01:56:03.731]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 2/11 (100 records)
[2025-08-10 01:56:03.745]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 3/11 (100 records)
[2025-08-10 01:56:03.761]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 4/11 (100 records)
[2025-08-10 01:56:03.775]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 5/11 (100 records)
[2025-08-10 01:56:03.790]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 6/11 (100 records)
[2025-08-10 01:56:03.805]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 7/11 (100 records)
[2025-08-10 01:56:03.824]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 8/11 (100 records)
[2025-08-10 01:56:03.840]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 9/11 (100 records)
[2025-08-10 01:56:03.855]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 10/11 (100 records)
[2025-08-10 01:56:03.864]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 11/11 (28 records)
[2025-08-10 01:56:03.868]  	[INFO]		[ActivityTypeBaseService]	Successfully imported 1028 activity types
[2025-08-10 01:56:03.872]  	[INFO]		[ActivityTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-10 01:56:03.880]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-10 01:56:04.027]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 01:56:04.090]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 01:56:04.101]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-10 01:56:04.113]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-10 01:56:04.156]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-10 01:56:04.164]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 01:56:04.169]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-10 01:56:04.174]  	[INFO]		[ServiceLocator]	Successfully imported 1028 activity types
[2025-08-10 01:56:04.180]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.185]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-10 01:56:04.189]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.194]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:04.199]  	[INFO]		[ServiceLocator]	Seeding craft type data from embedded resource
[2025-08-10 01:56:04.204]  	[DEBUG]		[CraftTypeBaseService]	Starting import from embedded JSON resource
[2025-08-10 01:56:04.240]  	[INFO]		[CraftTypeBaseService]	Found 337 craft types to import
[2025-08-10 01:56:04.244]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.310]  	[INFO]		[CraftTypeBaseService]	Successfully imported 337 craft types
[2025-08-10 01:56:04.314]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.318]  	[INFO]		[CraftTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-10 01:56:04.322]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-10 01:56:04.332]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-10 01:56:04.338]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 01:56:04.342]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-10 01:56:04.346]  	[INFO]		[ServiceLocator]	Successfully imported 337 craft types
[2025-08-10 01:56:04.351]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-10 01:56:04.357]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-10 01:56:04.375]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-10 01:56:04.381]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:04.401]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 01:56:04.405]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 01:56:04.411]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:04.426]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 01:56:04.430]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 01:56:04.435]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-10 01:56:04.439]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-10 01:56:04.443]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 01:56:04.447]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 01:56:04.457]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-10 01:56:04.465]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 01:56:04.472]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-10 01:56:04.476]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-10 01:56:04.481]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-10 01:56:04.485]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:04.492]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-10 01:56:04.501]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.00 MB MB size
[2025-08-10 01:56:04.505]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 6, Tables: 12, Indexes: 52
[2025-08-10 01:56:04.509]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-10 01:56:04.515]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-10 01:56:04.519]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-10 01:56:04.523]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-10 01:56:04.527]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-10 01:56:04.531]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-10 01:56:04.535]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-10 01:56:04.539]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-10 01:56:04.543]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-10 01:56:04.548]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-10 01:56:04.552]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-10 01:56:04.556]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-10 01:56:04.560]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-10 01:56:04.564]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-10 01:56:04.568]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-10 01:56:04.575]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-10 01:56:04.580]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-10 01:56:04.586]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.611]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-10 01:56:04.615]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.638]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.648]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-10 01:56:04.652]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.656]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.660]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-10 01:56:04.664]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.668]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.673]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-10 01:56:04.677]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.681]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.686]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-10 01:56:04.690]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.694]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.698]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-10 01:56:04.702]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.706]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.711]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-10 01:56:04.715]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.719]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-10 01:56:04.724]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 144ms
[2025-08-10 01:56:04.729]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-10 01:56:04.741]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-10 01:56:04.745]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 17ms
[2025-08-10 01:56:04.750]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-10 01:56:04.756]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-10 01:56:04.760]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 11ms
[2025-08-10 01:56:04.765]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-10 01:56:04.770]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:04.775]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-10 01:56:04.781]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:04.789]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-10 01:56:04.793]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:04.797]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-10 01:56:04.801]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 01:56:04.806]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-10 01:56:04.809]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-10 01:56:04.814]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 50ms
[2025-08-10 01:56:04.819]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-10 01:56:04.824]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.831]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-10 01:56:04.835]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.841]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.847]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-10 01:56:04.851]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.855]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.859]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-10 01:56:04.863]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.868]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.872]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-10 01:56:04.876]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.881]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.885]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-10 01:56:04.889]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.894]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.898]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-10 01:56:04.903]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.906]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.911]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-10 01:56:04.915]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.919]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.923]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-10 01:56:04.927]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.931]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.936]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-10 01:56:04.941]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.946]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 01:56:04.951]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-10 01:56:04.956]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 01:56:04.960]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-10 01:56:04.965]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 148ms
[2025-08-10 01:56:04.971]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-10 01:56:04.976]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-10 01:56:04.981]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-10 01:56:04.985]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-10 01:56:04.989]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-10 01:56:04.993]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-10 01:56:04.997]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-10 01:56:05.001]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-10 01:56:05.005]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-10 01:56:05.009]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-10 01:56:05.013]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-10 01:56:05.017]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-10 01:56:05.021]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-10 01:56:05.025]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-10 01:56:05.029]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-10 01:56:05.033]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-10 01:56:05.038]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-10 01:56:05.044]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 909%
[2025-08-10 01:56:05.049]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 1,818%
[2025-08-10 01:56:05.053]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 2,727%
[2025-08-10 01:56:05.057]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 3,636%
[2025-08-10 01:56:05.061]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 4,545%
[2025-08-10 01:56:05.065]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 5,455%
[2025-08-10 01:56:05.069]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 6,364%
[2025-08-10 01:56:05.073]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 7,273%
[2025-08-10 01:56:05.077]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 8,182%
[2025-08-10 01:56:05.081]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 9,091%
[2025-08-10 01:56:05.085]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 10,000%
[2025-08-10 01:56:05.089]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 0%
[2025-08-10 01:56:05.093]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 01:56:05.097]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 01:56:05.100]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 01:56:05.105]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 01:56:05.108]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 01:56:05.112]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 01:56:05.116]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 01:56:05.121]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 01:56:05.125]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 01:56:05.129]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 01:56:05.136]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 01:56:05.141]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 01:56:05.147]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 01:56:05.153]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 01:56:05.159]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 01:56:05.169]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 01:56:05.174]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 01:56:05.181]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 01:56:05.187]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 01:56:05.192]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 01:56:05.199]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 01:56:05.206]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 01:56:05.210]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 01:56:05.214]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 01:56:05.218]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 01:56:05.222]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 01:56:05.226]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 01:56:05.230]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 01:56:05.234]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 01:56:05.237]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 01:56:05.241]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 01:56:05.245]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 01:56:05.249]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 01:56:05.252]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 01:56:05.256]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 01:56:05.260]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 01:56:05.264]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 01:56:05.267]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 01:56:05.271]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 01:56:05.275]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 01:56:05.278]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 01:56:05.282]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 01:56:05.286]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 01:56:05.290]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 01:56:05.293]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 01:56:05.297]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 01:56:05.301]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 01:56:05.305]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 01:56:05.309]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 01:56:05.313]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 01:56:05.316]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 01:56:05.321]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-10 01:56:05.321]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 01:56:05.326]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-10 01:56:05.328]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 01:56:05.332]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-10 01:56:05.336]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 01:56:05.346]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 01:56:05.350]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 01:56:05.354]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 01:56:05.358]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 01:56:05.363]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 01:56:05.367]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 01:56:05.373]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 01:56:05.383]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 01:56:05.387]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 01:56:05.390]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 01:56:05.394]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 01:56:05.398]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 01:56:05.401]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-10 01:56:05.402]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 01:56:05.406]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-10 01:56:05.409]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 01:56:05.413]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 12ms
[2025-08-10 01:56:05.417]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 01:56:05.424]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 01:56:05.428]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 01:56:05.432]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 01:56:05.436]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 01:56:05.439]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 01:56:05.443]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 01:56:05.446]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 01:56:05.450]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 01:56:05.454]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 01:56:05.457]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 01:56:05.461]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 01:56:05.465]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 01:56:05.469]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 01:56:05.473]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 01:56:05.476]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 01:56:05.480]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 01:56:05.484]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 01:56:05.487]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 01:56:05.491]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 01:56:05.495]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 01:56:05.498]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 01:56:05.502]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 01:56:05.506]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 01:56:05.509]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 01:56:05.513]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 01:56:05.517]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 01:56:05.521]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 01:56:05.525]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 01:56:05.528]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 01:56:05.532]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 01:56:05.536]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 01:56:05.539]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 01:56:05.544]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 01:56:05.548]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 01:56:05.553]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 01:56:05.558]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 01:56:05.563]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 01:56:05.567]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 01:56:05.574]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 01:56:05.580]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 01:56:05.586]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 01:56:05.592]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 01:56:05.596]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 01:56:05.600]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 01:56:05.605]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 01:56:05.609]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 01:56:05.613]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 01:56:05.617]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 01:56:05.621]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 01:56:05.625]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 01:56:05.629]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 01:56:05.634]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 01:56:05.637]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 01:56:05.641]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 01:56:05.646]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 01:56:05.650]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 01:56:05.654]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 01:56:05.658]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 01:56:05.662]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 01:56:05.666]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 01:56:05.670]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 01:56:05.674]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 01:56:05.678]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 01:56:05.682]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 01:56:05.686]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 01:56:05.690]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 01:56:05.694]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 01:56:05.698]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 01:56:05.702]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 01:56:05.706]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 01:56:05.710]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 01:56:05.714]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 01:56:05.717]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 01:56:05.721]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 01:56:05.725]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 01:56:05.728]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 01:56:05.732]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 01:56:05.736]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 01:56:05.739]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 01:56:05.743]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 01:56:05.747]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 01:56:05.750]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 01:56:05.754]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 01:56:05.758]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 01:56:05.762]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 01:56:05.766]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 01:56:05.769]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 01:56:05.773]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 01:56:05.777]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 01:56:05.780]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 01:56:05.784]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 01:56:05.788]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 01:56:05.791]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 01:56:05.795]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 01:56:05.799]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 01:56:05.802]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 01:56:05.806]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 01:56:05.809]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 01:56:05.813]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 01:56:05.817]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 01:56:05.821]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 01:56:05.825]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 01:56:05.829]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 01:56:05.834]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 01:56:05.839]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 01:56:05.843]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 01:56:05.847]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 01:56:05.852]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 01:56:05.855]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 01:56:05.859]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 01:56:05.864]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 01:56:05.867]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 01:56:05.871]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 01:56:05.875]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 01:56:05.879]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 01:56:05.884]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 01:56:05.888]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 01:56:05.892]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 01:56:05.896]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 01:56:05.900]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 01:56:05.904]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 01:56:05.908]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 01:56:05.912]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 01:56:05.916]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 01:56:05.920]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 01:56:05.924]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 01:56:05.928]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 01:56:05.932]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 01:56:05.935]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 01:56:05.940]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 01:56:05.944]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 01:56:05.947]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 01:56:05.951]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 01:56:05.955]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 01:56:05.959]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 01:56:05.963]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 01:56:05.967]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 01:56:05.971]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 01:56:05.975]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 01:56:05.979]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 01:56:05.984]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 01:56:05.988]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 01:56:05.991]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 01:56:05.995]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 01:56:05.999]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 01:56:06.003]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 01:56:06.007]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 01:56:06.011]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 01:56:06.015]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 01:56:06.019]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 01:56:06.023]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 01:56:06.027]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 01:56:06.031]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 01:56:06.035]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 01:56:06.039]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 01:56:06.043]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 01:56:06.048]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 01:56:06.052]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 01:56:06.056]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 01:56:06.059]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 01:56:06.064]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 01:56:06.068]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 01:56:06.073]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 01:56:06.077]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 01:56:06.082]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 01:56:06.086]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 01:56:06.090]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 01:56:06.094]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 01:56:06.098]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 01:56:06.102]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 01:56:06.106]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 01:56:06.110]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 01:56:06.114]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 01:56:06.118]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 01:56:06.122]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 01:56:06.125]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 01:56:06.129]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 01:56:06.133]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 01:56:06.137]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 01:56:06.141]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 01:56:06.145]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 01:56:06.150]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 01:56:06.154]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 01:56:06.157]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 01:56:06.162]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 01:56:06.165]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 01:56:06.169]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 01:56:06.173]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 01:56:06.177]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 01:56:06.181]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 01:56:06.185]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 01:56:06.188]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 01:56:06.192]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 01:56:06.196]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 01:56:06.200]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 01:56:06.205]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 01:56:06.209]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 01:56:06.215]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 01:56:06.226]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 01:56:06.237]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 01:56:06.249]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 01:56:06.264]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 01:56:06.278]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 01:56:06.285]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 01:56:06.290]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 01:56:06.294]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 01:56:06.298]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 01:56:06.302]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 01:56:06.306]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 01:56:06.310]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 01:56:06.314]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 01:56:06.318]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 01:56:06.322]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 01:56:06.326]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 01:56:06.330]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 01:56:06.334]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 01:56:06.338]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 01:56:06.342]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 01:56:06.345]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 01:56:06.349]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 01:56:06.353]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 01:56:06.357]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 01:56:06.361]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 01:56:06.365]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 01:56:06.369]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 01:56:06.373]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 01:56:06.377]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 01:56:06.381]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 01:56:06.386]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 01:56:06.391]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 01:56:06.395]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 01:56:06.399]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 01:56:06.403]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 01:56:06.407]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 01:56:06.411]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 01:56:06.415]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 01:56:06.419]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 01:56:06.423]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 01:56:06.427]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 01:56:06.430]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 01:56:06.434]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 01:56:06.438]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 01:56:06.442]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 01:56:06.446]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 01:56:06.450]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 01:56:06.454]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 01:56:06.458]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 01:56:06.462]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 01:56:06.466]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 01:56:06.470]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 01:56:06.474]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 01:56:06.478]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 01:56:06.481]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 01:56:06.485]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 01:56:06.489]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 01:56:06.493]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 01:56:06.497]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 01:56:06.501]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 01:56:06.505]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 01:56:06.509]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 01:56:06.513]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 01:56:06.517]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 01:56:06.521]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 01:56:06.525]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 01:56:06.529]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 01:56:06.533]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-10 01:56:06.537]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-10 01:56:06.545]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-10 01:56:06.649]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-10 01:56:06.653]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-10 01:56:06.657]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-10 01:56:06.661]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-10 01:56:06.668]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 01:56:06.672]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:06.678]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 01:56:06.682]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:06.688]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 01:56:06.692]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:06.762]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-10 01:56:06.766]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-10 01:56:06.999]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:07.003]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:07.006]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:07.010]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:07.014]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:07.018]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:07.096]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-10 01:56:07.152]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-10 01:56:07.334]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-10 01:56:07.462]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-10 01:56:09.478]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:09.482]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:09.486]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:09.490]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:09.495]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:09.500]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:09.562]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-10 01:56:09.569]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-10 01:56:09.574]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-10 01:56:09.602]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-10 01:56:09.607]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_1003178_638903841696075347 (BaseViewModel) for NPersonalViewModel
[2025-08-10 01:56:09.611]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-10 01:56:09.615]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:09.619]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_9028608_638903841696195325 (BaseViewModel) for PersonalInformationViewModel
[2025-08-10 01:56:09.623]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-10 01:56:09.626]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:09.630]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-10 01:56:09.634]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_14148614_638903841696341128 (BaseViewModel) for ContactInformationViewModel
[2025-08-10 01:56:09.637]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-10 01:56:09.641]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:09.644]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-10 01:56:09.648]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-10 01:56:09.661]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-10 01:56:09.711]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-10 01:56:09.747]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_52610097_638903841697476715 (BaseViewModel) for NewClientViewModel
[2025-08-10 01:56:09.751]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-10 01:56:09.755]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:09.759]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_3728830_638903841697594750 (BaseViewModel) for PersonalInformationViewModel
[2025-08-10 01:56:09.763]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-10 01:56:09.766]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:09.770]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-10 01:56:09.774]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_33559471_638903841697744741 (BaseViewModel) for ContactInformationViewModel
[2025-08-10 01:56:09.778]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-10 01:56:09.781]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:09.785]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-10 01:56:09.789]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-10 01:56:09.794]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_33599791_638903841697941021 (BaseViewModel) for ActivityManagementViewModel
[2025-08-10 01:56:09.797]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-10 01:56:09.801]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:09.806]  	[DEBUG]		[ActivityManagementViewModel]	Initialized default activity status for all activity types
[2025-08-10 01:56:09.810]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-10 01:56:09.814]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-10 01:56:09.819]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-10 01:56:09.823]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_33962670_638903841698238099 (BaseViewModel) for NotesManagementViewModel
[2025-08-10 01:56:09.827]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-10 01:56:09.831]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:09.836]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-10 01:56:09.840]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-10 01:56:09.845]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-10 01:56:09.850]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-10 01:56:09.854]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-10 01:56:09.859]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-10 01:56:09.864]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-10 01:56:09.868]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-10 01:56:09.872]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-10 01:56:09.876]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-10 01:56:09.881]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-10 01:56:09.885]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-10 01:56:09.889]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-10 01:56:09.894]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 319ms (Success: True)
[2025-08-10 01:56:09.898]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 330ms (Success: True)
[2025-08-10 01:56:09.903]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x560 (Height-based width calculation)
[2025-08-10 01:56:09.926]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-10 01:56:09.940]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 2, misses: 1)
[2025-08-10 01:56:09.942]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-10 01:56:09.944]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-10 01:56:09.949]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-10 01:56:09.948]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:09.952]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 13ms
[2025-08-10 01:56:09.956]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 17ms
[2025-08-10 01:56:10.348]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-10 01:56:10.356]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-10 01:56:10.507]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:10.511]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:10.517]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:10.524]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:10.529]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:10.534]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:10.577]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:10.592]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:10.606]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:10.621]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:10.632]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:10.639]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:10.767]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1064.3157ms
[2025-08-10 01:56:10.772]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:10.776]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1156.6103ms
[2025-08-10 01:56:10.781]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:10.815]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1181.457ms
[2025-08-10 01:56:10.819]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:10.907]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1159.3267ms
[2025-08-10 01:56:10.911]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:10.968]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1209.2201ms
[2025-08-10 01:56:10.972]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:10.976]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1202.1877ms
[2025-08-10 01:56:10.980]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:10.984]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1190.3095ms
[2025-08-10 01:56:10.988]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:10.992]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1168.3713ms
[2025-08-10 01:56:10.996]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:11.722]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:11.742]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:11.749]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:11.753]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:11.757]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:11.761]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:11.785]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2083.7477ms
[2025-08-10 01:56:11.791]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:11.795]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2175.9103ms
[2025-08-10 01:56:11.799]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:11.803]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:11.808]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:11.813]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:11.817]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:11.821]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:11.825]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:11.835]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2201.0917ms
[2025-08-10 01:56:11.840]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:11.984]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2236.7928ms
[2025-08-10 01:56:11.988]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:11.993]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2233.6353ms
[2025-08-10 01:56:11.997]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:12.045]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2271.0534ms
[2025-08-10 01:56:12.049]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:12.054]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2260.3551ms
[2025-08-10 01:56:12.058]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:12.062]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2238.7564ms
[2025-08-10 01:56:12.066]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:13.415]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:13.460]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:13.507]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:13.511]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:13.515]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:13.519]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:13.522]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:13.527]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:13.771]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:13.775]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:13.779]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:13.783]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:13.788]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'D'
[2025-08-10 01:56:13.802]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 387.5605ms
[2025-08-10 01:56:13.806]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:13.818]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:13.823]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:13.827]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:13.831]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:13.835]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:13.840]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 461.5533ms
[2025-08-10 01:56:13.845]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:13.882]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:13.910]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:13.929]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:13.987]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 151.593ms
[2025-08-10 01:56:13.991]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:13.995]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 224.3731ms
[2025-08-10 01:56:13.999]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 01:56:14.023]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:14.027]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:14.031]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:14.035]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:14.039]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:14.043]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:14.241]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:14.245]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:14.249]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:14.253]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:14.257]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DR'
[2025-08-10 01:56:14.262]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:14.265]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:14.269]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:14.274]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:14.278]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:14.286]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:14.624]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(2), PhoneNumbersCollection(1)
[2025-08-10 01:56:14.654]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 4, Batched: 4, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(4)
[2025-08-10 01:56:14.708]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:14.747]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:14.762]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 3, Batched: 2, Immediate: 1, Efficiency: 66.7%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(2)
[2025-08-10 01:56:14.767]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 2, Batched: 2, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(2)
[2025-08-10 01:56:14.933]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:14.939]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:14.948]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:14.958]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:14.971]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:14.978]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:15.056]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:15.060]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:15.064]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:15.068]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:15.071]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRI'
[2025-08-10 01:56:15.085]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:15.090]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:15.095]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:15.099]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:15.102]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:15.118]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:15.382]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:15.412]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:15.566]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:15.570]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:15.574]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:15.578]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:15.582]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:15.587]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:15.736]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:15.740]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:15.744]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:15.747]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:15.752]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRID'
[2025-08-10 01:56:15.756]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:15.760]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:15.764]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:15.768]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:15.772]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:15.783]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:16.048]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:16.056]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:16.218]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:16.222]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:16.226]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:16.230]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:16.233]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:16.237]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:16.373]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:16.377]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:16.381]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:16.384]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:16.388]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI'
[2025-08-10 01:56:16.392]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:16.396]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:16.400]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:16.403]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:16.407]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:16.436]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:16.963]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:16.994]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:17.081]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.086]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:17.089]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.094]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:17.098]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.102]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:17.242]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:17.257]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:17.351]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.355]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:17.358]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.362]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:17.366]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.370]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:17.539]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:17.555]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:17.726]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.730]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:17.734]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.737]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:17.741]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:17.745]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:17.896]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:17.900]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:17.904]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:17.908]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:17.911]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI YA'
[2025-08-10 01:56:17.916]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:17.920]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:17.924]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:17.928]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:17.932]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:17.935]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:17.939]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:17.958]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:18.036]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.039]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:18.043]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.047]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:18.051]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.054]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:18.128]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 01:56:18.206]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:18.237]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:18.330]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.334]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:18.337]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.341]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:18.345]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.349]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:18.457]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:18.486]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:18.611]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:18.640]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:18.644]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 01:56:18.713]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.717]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:18.721]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.725]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:18.729]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.733]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:18.764]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.768]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:18.772]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.776]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:18.779]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:18.783]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:18.950]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:18.954]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:18.958]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:18.962]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:18.966]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI YACIDE'
[2025-08-10 01:56:18.969]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:18.973]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:18.977]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:18.981]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:18.985]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:18.989]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:19.182]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:19.213]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:19.291]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:19.295]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:19.300]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:19.305]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:19.311]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:19.318]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:19.413]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:19.431]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:19.539]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:19.543]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:19.547]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:19.550]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:19.554]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:19.558]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:19.632]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 12, Batched: 12, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(12)
[2025-08-10 01:56:19.663]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 21, Batched: 21, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(21)
[2025-08-10 01:56:19.740]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:19.744]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:19.747]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:19.751]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:19.755]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI YACI'
[2025-08-10 01:56:19.759]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:19.762]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:19.766]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:19.770]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:19.774]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:19.778]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(6)
[2025-08-10 01:56:19.782]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(6)
[2025-08-10 01:56:19.786]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:20.668]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:20.683]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:20.745]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:20.750]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:20.754]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:20.758]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:20.761]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:20.766]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:20.839]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:20.870]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:20.948]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:20.952]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:20.956]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:20.959]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:20.963]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:20.967]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:21.180]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:21.184]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:21.188]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:21.191]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:21.195]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI YACINE'
[2025-08-10 01:56:21.210]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:21.214]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:21.217]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:21.221]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:21.225]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:21.242]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:21.334]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:21.365]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:21.676]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:21.680]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:21.684]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:21.688]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:21.692]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI YACIN'
[2025-08-10 01:56:21.707]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:21.711]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:21.714]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:21.718]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:21.722]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:21.737]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:21.845]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:21.865]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:21.874]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:22.190]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:22.193]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:22.197]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:22.201]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:22.205]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI YACI'
[2025-08-10 01:56:22.215]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:22.219]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:22.223]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:22.226]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:22.230]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:22.236]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:22.709]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:22.714]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:22.717]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:22.721]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:22.725]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:22.729]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:23.189]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:23.193]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:23.197]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:23.201]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:23.204]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:23.209]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:23.512]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:23.517]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:23.521]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:23.525]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:23.529]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:23.533]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:23.807]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:23.838]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.147]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:24.151]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:24.155]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:24.160]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:24.164]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'RIDI YACI'
[2025-08-10 01:56:24.168]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:24.172]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:24.177]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:24.181]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:24.185]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.209]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.302]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.333]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.340]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.379]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.415]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.420]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.458]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.488]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.494]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.502]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.534]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.542]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.582]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.613]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.619]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.627]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.645]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 11, Batched: 11, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(11)
[2025-08-10 01:56:24.660]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.676]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.681]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 26, Batched: 26, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(26)
[2025-08-10 01:56:24.718]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.725]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.769]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.784]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.789]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.799]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.831]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:24.862]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 4, Batched: 4, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(4)
[2025-08-10 01:56:24.878]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 4, Batched: 4, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(4)
[2025-08-10 01:56:25.158]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-10 01:56:25.162]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: ''
[2025-08-10 01:56:25.173]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-10 01:56:25.177]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:25.204]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:25.559]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:25.563]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:25.567]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:25.570]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:25.574]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:25.578]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:26.413]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:26.425]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:26.596]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:26.600]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:26.604]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:26.607]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:26.611]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:26.615]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:26.750]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:26.754]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:26.758]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:26.761]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:26.765]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'D'
[2025-08-10 01:56:26.769]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-10 01:56:26.773]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-10 01:56:26.777]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-10 01:56:26.781]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-10 01:56:26.786]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:26.812]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:27.515]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:27.545]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:27.637]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:27.641]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:27.645]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:27.649]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:27.653]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:27.656]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:27.854]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-10 01:56:27.858]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: ''
[2025-08-10 01:56:27.863]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-10 01:56:27.867]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:27.885]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 01:56:29.558]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:29.562]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:29.566]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:29.569]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:29.573]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:29.577]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:29.621]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:29.624]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:29.628]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:29.632]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:29.637]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-10 01:56:29.641]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:29.645]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-10 01:56:29.651]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-10 01:56:29.656]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-10 01:56:29.661]  	[INFO]		[MainWindow]	Application closing
[2025-08-10 01:56:29.666]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_58894414_638903841896663342 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-10 01:56:29.670]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-10 01:56:29.674]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 01:56:29.678]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 01:56:29.682]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 01:56:29.687]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-10 01:56:29.696]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-10 01:56:29.703]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-10 01:56:29.707]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-10 01:56:29.786]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-10 01:56:29.808]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-10 01:56:29.867]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 5, Batched: 5, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(5)
[2025-08-10 01:56:29.883]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 9, Batched: 9, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(9)
[2025-08-10 01:56:29.898]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 6, Batched: 3, Immediate: 3, Efficiency: 50.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(3)
[2025-08-10 01:56:29.913]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(3)
[2025-08-10 01:56:29.929]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2414.3533ms
[2025-08-10 01:56:29.933]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:29.961]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2465.5611ms
[2025-08-10 01:56:29.966]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:29.982]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:29.986]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:29.990]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:29.996]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 01:56:30.042]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 01:56:30.052]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 01:56:30.164]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2296.7071ms
[2025-08-10 01:56:30.174]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 01:56:30.195]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2340.094ms
[2025-08-10 01:56:30.201]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
