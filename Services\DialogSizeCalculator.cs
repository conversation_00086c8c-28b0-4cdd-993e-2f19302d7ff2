using System;
using System.Collections.Concurrent;
using System.Windows;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// High-performance dialog size calculation service with caching and optimization.
    /// Provides pre-calculated dialog dimensions to eliminate UI thread calculations
    /// and reduce dialog opening latency.
    /// </summary>
    public static class DialogSizeCalculator
    {
        #region Constants
        /// <summary>
        /// Default dialog size constraints for different dialog types
        /// </summary>
        public static class SizeConstraints
        {
            public static class NewClientDialog
            {
                public const double MinWidth = 900;
                public const double MinHeight = 550; // Reduced from 600 to accommodate small screens
                public const double MaxWidth = 1600;
                public const double MaxHeight = 1300;
                public const double HeightRatio = 0.8;
                public const double WidthToHeightMultiplier = 1.2; // Width = Height × 1.2
            }

            public static class ConfirmationDialog
            {
                public const double MinWidth = 400;
                public const double MinHeight = 200;
                public const double MaxWidth = 600;
                public const double MaxHeight = 400;
                public const double WidthRatio = 0.4;
                public const double HeightRatio = 0.3;
            }
        }
        #endregion

        #region Private Fields
        /// <summary>
        /// Cache for pre-calculated dialog sizes to avoid repeated calculations
        /// </summary>
        private static readonly ConcurrentDictionary<string, DialogSize> _sizeCache = new();
        
        /// <summary>
        /// Lock object for thread-safe operations
        /// </summary>
        private static readonly object _calculationLock = new object();
        
        /// <summary>
        /// Last known parent window dimensions for cache invalidation
        /// </summary>
        private static Size _lastParentSize = Size.Empty;
        #endregion

        #region Public Methods
        /// <summary>
        /// Gets optimized dialog size for NewClientView with caching
        /// </summary>
        /// <param name="parentWindow">Parent window for size calculations</param>
        /// <returns>Optimized dialog size</returns>
        public static DialogSize GetNewClientDialogSize(Window parentWindow)
        {
            if (parentWindow == null)
                throw new ArgumentNullException(nameof(parentWindow));

            return GetOptimizedDialogSizeWithHeightBasedWidth(
                parentWindow,
                "NewClientDialog",
                SizeConstraints.NewClientDialog.HeightRatio,
                SizeConstraints.NewClientDialog.WidthToHeightMultiplier,
                SizeConstraints.NewClientDialog.MinWidth,
                SizeConstraints.NewClientDialog.MinHeight,
                SizeConstraints.NewClientDialog.MaxWidth,
                SizeConstraints.NewClientDialog.MaxHeight
            );
        }

        /// <summary>
        /// Gets optimized dialog size for ConfirmationDialog with caching
        /// </summary>
        /// <param name="parentWindow">Parent window for size calculations</param>
        /// <returns>Optimized dialog size</returns>
        public static DialogSize GetConfirmationDialogSize(Window parentWindow)
        {
            if (parentWindow == null)
                throw new ArgumentNullException(nameof(parentWindow));

            return GetOptimizedDialogSize(
                parentWindow,
                "ConfirmationDialog",
                SizeConstraints.ConfirmationDialog.WidthRatio,
                SizeConstraints.ConfirmationDialog.HeightRatio,
                SizeConstraints.ConfirmationDialog.MinWidth,
                SizeConstraints.ConfirmationDialog.MinHeight,
                SizeConstraints.ConfirmationDialog.MaxWidth,
                SizeConstraints.ConfirmationDialog.MaxHeight
            );
        }

        /// <summary>
        /// Pre-calculates and caches dialog sizes for improved performance
        /// </summary>
        /// <param name="parentWindow">Parent window for calculations</param>
        public static void PreCalculateDialogSizes(Window parentWindow)
        {
            if (parentWindow == null) return;

            try
            {
                // Pre-calculate common dialog sizes
                GetNewClientDialogSize(parentWindow);
                GetConfirmationDialogSize(parentWindow);

                LoggingService.LogDebug("Dialog sizes pre-calculated and cached", "DialogSizeCalculator");
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Error pre-calculating dialog sizes: {ex.Message}", "DialogSizeCalculator");
            }
        }

        /// <summary>
        /// Clears the size cache to force recalculation
        /// </summary>
        public static void ClearCache()
        {
            lock (_calculationLock)
            {
                _sizeCache.Clear();
                _lastParentSize = Size.Empty;
                LoggingService.LogDebug("Dialog size cache cleared", "DialogSizeCalculator");
            }
        }

        /// <summary>
        /// Gets cache statistics for performance monitoring
        /// </summary>
        /// <returns>Cache statistics</returns>
        public static CacheStatistics GetCacheStatistics()
        {
            lock (_calculationLock)
            {
                return new CacheStatistics
                {
                    CachedItemCount = _sizeCache.Count,
                    LastParentSize = _lastParentSize
                };
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Core optimized dialog size calculation with caching
        /// </summary>
        private static DialogSize GetOptimizedDialogSize(
            Window parentWindow,
            string dialogType,
            double widthRatio,
            double heightRatio,
            double minWidth,
            double minHeight,
            double maxWidth,
            double maxHeight)
        {
            lock (_calculationLock)
            {
                try
                {
                    var parentSize = new Size(parentWindow.ActualWidth, parentWindow.ActualHeight);
                    var cacheKey = $"{dialogType}_{parentSize.Width}x{parentSize.Height}";

                    // Check if parent window size changed (invalidate cache)
                    if (_lastParentSize != parentSize)
                    {
                        _sizeCache.Clear();
                        _lastParentSize = parentSize;
                    }

                    // Try to get from cache first
                    if (_sizeCache.TryGetValue(cacheKey, out var cachedSize))
                    {
                        return cachedSize;
                    }

                    // Calculate new size
                    var calculatedSize = CalculateDialogSize(
                        parentSize,
                        widthRatio,
                        heightRatio,
                        minWidth,
                        minHeight,
                        maxWidth,
                        maxHeight
                    );

                    // Cache the result
                    _sizeCache[cacheKey] = calculatedSize;

                    return calculatedSize;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error calculating dialog size: {ex.Message}", "DialogSizeCalculator");

                    // Return fallback size
                    return new DialogSize
                    {
                        Width = minWidth,
                        Height = minHeight,
                        IsOptimized = false
                    };
                }
            }
        }

        /// <summary>
        /// Core optimized dialog size calculation with height-based width calculation and caching
        /// </summary>
        private static DialogSize GetOptimizedDialogSizeWithHeightBasedWidth(
            Window parentWindow,
            string dialogType,
            double heightRatio,
            double widthToHeightMultiplier,
            double minWidth,
            double minHeight,
            double maxWidth,
            double maxHeight)
        {
            lock (_calculationLock)
            {
                try
                {
                    var parentSize = new Size(parentWindow.ActualWidth, parentWindow.ActualHeight);
                    var cacheKey = $"{dialogType}_HeightBased_{parentSize.Width}x{parentSize.Height}";

                    // Check if parent window size changed (invalidate cache)
                    if (_lastParentSize != parentSize)
                    {
                        _sizeCache.Clear();
                        _lastParentSize = parentSize;
                    }

                    // Try to get from cache first
                    if (_sizeCache.TryGetValue(cacheKey, out var cachedSize))
                    {
                        return cachedSize;
                    }

                    // Calculate new size using height-based width calculation
                    var calculatedSize = CalculateDialogSizeWithHeightBasedWidth(
                        parentSize,
                        heightRatio,
                        widthToHeightMultiplier,
                        minWidth,
                        minHeight,
                        maxWidth,
                        maxHeight
                    );

                    // Cache the result
                    _sizeCache[cacheKey] = calculatedSize;

                    return calculatedSize;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error calculating dialog size with height-based width: {ex.Message}", "DialogSizeCalculator");

                    // Return fallback size
                    return new DialogSize
                    {
                        Width = minWidth,
                        Height = minHeight,
                        IsOptimized = false
                    };
                }
            }
        }

        /// <summary>
        /// Performs the actual dialog size calculation with pixel-perfect rounding and screen constraints
        /// </summary>
        private static DialogSize CalculateDialogSize(
            Size parentSize,
            double widthRatio,
            double heightRatio,
            double minWidth,
            double minHeight,
            double maxWidth,
            double maxHeight)
        {
            // Calculate dialog size as percentage of parent window
            double dialogWidth = parentSize.Width * widthRatio;
            double dialogHeight = parentSize.Height * heightRatio;

            // For small screens, ensure we leave enough space for taskbar and window chrome
            // Subtract 48px for taskbar and window decorations on small screens
            if (parentSize.Height <= 768)
            {
                double availableHeight = parentSize.Height - 48;
                dialogHeight = Math.Min(dialogHeight, availableHeight);

                // On very small screens, prioritize footer visibility by using a smaller minimum
                if (availableHeight < minHeight)
                {
                    minHeight = Math.Max(500, availableHeight * 0.9); // Use 90% of available height, minimum 500px
                }
            }

            // Apply size constraints
            dialogWidth = Math.Max(minWidth, Math.Min(maxWidth, dialogWidth));
            dialogHeight = Math.Max(minHeight, Math.Min(maxHeight, dialogHeight));

            // Round to whole pixel values to ensure crisp rendering and eliminate sub-pixel positioning issues
            dialogWidth = Math.Round(dialogWidth);
            dialogHeight = Math.Round(dialogHeight);

            return new DialogSize
            {
                Width = dialogWidth,
                Height = dialogHeight,
                IsOptimized = true
            };
        }

        /// <summary>
        /// Performs dialog size calculation with height-based width calculation (Width = Height × multiplier)
        /// </summary>
        private static DialogSize CalculateDialogSizeWithHeightBasedWidth(
            Size parentSize,
            double heightRatio,
            double widthToHeightMultiplier,
            double minWidth,
            double minHeight,
            double maxWidth,
            double maxHeight)
        {
            // Calculate dialog height as percentage of parent window height
            double dialogHeight = parentSize.Height * heightRatio;

            // For small screens, ensure we leave enough space for taskbar and window chrome
            // Subtract 48px for taskbar and window decorations on small screens
            if (parentSize.Height <= 768)
            {
                double availableHeight = parentSize.Height - 48;
                dialogHeight = Math.Min(dialogHeight, availableHeight);

                // On very small screens, prioritize footer visibility by using a smaller minimum
                if (availableHeight < minHeight)
                {
                    minHeight = Math.Max(500, availableHeight * 0.9); // Use 90% of available height, minimum 500px
                }
            }

            // Apply height constraints first
            dialogHeight = Math.Max(minHeight, Math.Min(maxHeight, dialogHeight));

            // Calculate width based on height using the multiplier (Width = Height × 1.2)
            double dialogWidth = dialogHeight * widthToHeightMultiplier;

            // Apply width constraints
            dialogWidth = Math.Max(minWidth, Math.Min(maxWidth, dialogWidth));

            // Round to whole pixel values to ensure crisp rendering and eliminate sub-pixel positioning issues
            dialogWidth = Math.Round(dialogWidth);
            dialogHeight = Math.Round(dialogHeight);

            return new DialogSize
            {
                Width = dialogWidth,
                Height = dialogHeight,
                IsOptimized = true
            };
        }
        #endregion
    }

    #region Supporting Classes
    /// <summary>
    /// Represents optimized dialog size information
    /// </summary>
    public class DialogSize
    {
        /// <summary>
        /// Calculated dialog width
        /// </summary>
        public double Width { get; set; }

        /// <summary>
        /// Calculated dialog height
        /// </summary>
        public double Height { get; set; }

        /// <summary>
        /// Indicates if the size was optimized or is a fallback
        /// </summary>
        public bool IsOptimized { get; set; }

        /// <summary>
        /// Applies the calculated size to a FrameworkElement
        /// </summary>
        /// <param name="element">Element to apply size to</param>
        public void ApplyTo(FrameworkElement element)
        {
            if (element == null) return;

            element.Width = Width;
            element.Height = Height;
        }
    }

    /// <summary>
    /// Cache statistics for performance monitoring
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// Number of cached dialog sizes
        /// </summary>
        public int CachedItemCount { get; set; }

        /// <summary>
        /// Last known parent window size
        /// </summary>
        public Size LastParentSize { get; set; }
    }
    #endregion
}
