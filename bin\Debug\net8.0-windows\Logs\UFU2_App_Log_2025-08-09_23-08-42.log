=== UFU2 Application Session Started at 2025-08-09 23:08:42 ===
[2025-08-09 23:08:42.729]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 23:08:42.734]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 23:08:42.738]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 23:08:42.741]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 23:08:42.749]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 23:08:42.752]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 23:08:42.755]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 23:08:42.759]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 23:08:42.763]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 23:08:42.766]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 23:08:42.769]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 23:08:42.772]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 23:08:42.774]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 23:08:42.777]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 23:08:42.780]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 23:08:42.783]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 23:08:42.785]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 23:08:42.789]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 23:08:42.797]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 23:08:42.801]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 23:08:42.806]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 23:08:42.810]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 23:08:42.813]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 23:08:42.817]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 23:08:42.821]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 23:08:42.825]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 23:08:42.828]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 23:08:42.832]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 23:08:42.836]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 23:08:42.839]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 23:08:42.842]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 23:08:42.846]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 23:08:42.849]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 23:08:42.853]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 23:08:42.856]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 23:08:42.859]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 23:08:42.863]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 23:08:42.866]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 23:08:42.879]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 104.11MB working set
[2025-08-09 23:08:42.883]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 23:08:42.886]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 23:08:42.890]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 23:08:42.893]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 23:08:42.897]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 23:08:42.902]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 23:08:42.925]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 23:08:42.929]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 23:08:42.932]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 23:08:43.159]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 23:08:43.162]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 23:08:43.165]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 23:08:43.169]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 23:08:43.176]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638903741231745657 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 23:08:43.179]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 23:08:43.183]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:43.186]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 23:08:43.197]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 23:08:43.202]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 23:08:43.206]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 23:08:43.210]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 23:08:43.214]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 23:08:43.217]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 23:08:43.221]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 23:08:43.225]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 23:08:43.228]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 23:08:43.231]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 23:08:43.236]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 23:08:43.240]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 23:08:43.243]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 23:08:43.247]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 23:08:43.250]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 23:08:43.257]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 23:08:43.262]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 23:08:43.267]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 23:08:43.272]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 23:08:43.282]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 23:08:43.301]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 23:08:43.311]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 23:08:43.322]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 23:08:43.327]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 23:08:43.337]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 23:08:43.341]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 23:08:43.345]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 23:08:43.350]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 23:08:43.353]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 23:08:43.357]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 23:08:43.361]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 23:08:43.364]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 23:08:43.367]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 23:08:43.371]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 23:08:43.375]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 23:08:43.379]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 23:08:43.383]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 23:08:43.386]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 23:08:43.390]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 23:08:43.395]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 23:08:43.399]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 23:08:43.402]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 23:08:43.406]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 23:08:43.410]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 23:08:43.413]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 23:08:43.417]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 23:08:43.420]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 23:08:43.447]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 23:08:43.451]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 23:08:43.455]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 23:08:43.458]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 23:08:43.462]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 23:08:43.467]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 23:08:43.471]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 23:08:43.477]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 23:08:43.481]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 23:08:43.484]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 23:08:43.489]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 23:08:43.497]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 23:08:43.501]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 23:08:43.505]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 23:08:43.512]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 610ms
[2025-08-09 23:08:43.516]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 23:08:43.522]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 23:08:43.538]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 23:08:43.541]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 23:08:43.545]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 23:08:43.549]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 23:08:43.553]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 23:08:43.557]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 23:08:43.561]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 23:08:43.595]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 23:08:43.600]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 23:08:43.603]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 23:08:43.607]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 23:08:43.611]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 23:08:43.614]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 23:08:43.619]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 23:08:43.643]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:08:43.647]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:08:43.649]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:08:43.649]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:08:43.650]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:08:43.663]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 23:08:43.655]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:08:43.659]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-09 23:08:43.651]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:08:43.678]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 23:08:43.676]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 23:08:43.670]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 23:08:43.686]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 23:08:43.693]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 23:08:43.697]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 23:08:43.700]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 23:08:43.704]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 23:08:43.707]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 23:08:43.711]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 23:08:43.720]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-09 23:08:43.724]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-09 23:08:43.729]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-09 23:08:43.736]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.UFU2_Schema.sql
[2025-08-09 23:08:43.740]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.UFU2_Schema.sql (19126 characters)
[2025-08-09 23:08:43.744]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-09 23:08:43.748]  	[DEBUG]		[DatabaseMigrationService]	Executing 55 SQL statements
[2025-08-09 23:08:43.754]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/55 statements
[2025-08-09 23:08:43.759]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/55 statements
[2025-08-09 23:08:43.763]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/55 statements
[2025-08-09 23:08:43.767]  	[DEBUG]		[DatabaseMigrationService]	Executed 40/55 statements
[2025-08-09 23:08:43.773]  	[DEBUG]		[DatabaseMigrationService]	Executed 50/55 statements
[2025-08-09 23:08:43.798]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-09 23:08:43.819]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (55 statements executed)
[2025-08-09 23:08:43.822]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-09 23:08:43.828]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 23:08:43.832]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:43.836]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 23:08:43.841]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 23:08:43.845]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 23:08:43.849]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 23:08:43.853]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 23:08:43.858]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 23:08:43.862]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 23:08:43.866]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 23:08:43.870]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 23:08:43.874]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 23:08:43.880]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 23:08:43.884]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 23:08:43.889]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 23:08:43.894]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 23:08:43.898]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 23:08:43.903]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 23:08:43.907]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 23:08:43.911]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 23:08:43.914]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 23:08:43.918]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 23:08:43.922]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 23:08:43.929]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 23:08:43.933]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 23:08:43.938]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 23:08:43.944]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 23:08:43.949]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 23:08:43.953]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 23:08:43.958]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 23:08:43.970]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 23:08:43.982]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:44.011]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 23:08:44.028]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 23:08:44.061]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:44.214]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 23:08:44.319]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 23:08:44.339]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 23:08:44.345]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 23:08:44.352]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 23:08:44.357]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 23:08:44.366]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 23:08:44.377]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 23:08:44.393]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-09 23:08:44.407]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-09 23:08:44.423]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-09 23:08:44.429]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.APP_Schema.sql
[2025-08-09 23:08:44.433]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.APP_Schema.sql (9406 characters)
[2025-08-09 23:08:44.438]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-09 23:08:44.442]  	[DEBUG]		[DatabaseMigrationService]	Executing 30 SQL statements
[2025-08-09 23:08:44.448]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/30 statements
[2025-08-09 23:08:44.456]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/30 statements
[2025-08-09 23:08:44.462]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/30 statements
[2025-08-09 23:08:44.468]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-09 23:08:44.487]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (30 statements executed)
[2025-08-09 23:08:44.502]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-09 23:08:44.516]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 23:08:44.520]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:44.524]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 23:08:44.529]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 23:08:44.534]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 23:08:44.539]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 23:08:44.543]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 23:08:44.548]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 23:08:44.552]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 23:08:44.557]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 23:08:44.562]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 23:08:44.566]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 23:08:44.571]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 23:08:44.575]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 23:08:44.580]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 23:08:44.585]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 23:08:44.590]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 23:08:44.595]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 23:08:44.599]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 23:08:44.603]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 23:08:44.609]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 23:08:44.618]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:44.623]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 23:08:44.629]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 23:08:44.634]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:44.638]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 23:08:44.642]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 23:08:44.646]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 23:08:44.650]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 23:08:44.655]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 23:08:44.661]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 23:08:44.667]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 23:08:44.672]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 23:08:44.679]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-09 23:08:44.684]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-09 23:08:44.695]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-09 23:08:44.704]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.Archive_Schema.sql
[2025-08-09 23:08:44.709]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.Archive_Schema.sql (7159 characters)
[2025-08-09 23:08:44.714]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-09 23:08:44.720]  	[DEBUG]		[DatabaseMigrationService]	Executing 16 SQL statements
[2025-08-09 23:08:44.726]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/16 statements
[2025-08-09 23:08:44.733]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-09 23:08:44.752]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (16 statements executed)
[2025-08-09 23:08:44.756]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-09 23:08:44.760]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 23:08:44.765]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:44.770]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 23:08:44.775]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 23:08:44.780]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 23:08:44.785]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 23:08:44.789]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 23:08:44.794]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 23:08:44.799]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 23:08:44.809]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 23:08:44.816]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 23:08:44.824]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 23:08:44.830]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 23:08:44.834]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 23:08:44.840]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 23:08:44.845]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 23:08:44.850]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 23:08:44.854]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 23:08:44.860]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 23:08:44.864]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:44.869]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 23:08:44.874]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:44.878]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 23:08:44.883]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 23:08:44.887]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 23:08:44.891]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 23:08:44.896]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 23:08:44.900]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 23:08:44.905]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 23:08:44.909]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 23:08:44.914]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 23:08:44.919]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 23:08:44.924]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 23:08:44.928]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 23:08:44.933]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 23:08:44.939]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 23:08:44.943]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 23:08:44.947]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 23:08:44.952]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 23:08:44.956]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 23:08:44.961]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 23:08:44.965]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 23:08:44.970]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 23:08:44.974]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 23:08:44.979]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 23:08:44.983]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 23:08:44.987]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 23:08:44.992]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 23:08:44.996]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 23:08:45.001]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 23:08:45.005]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 23:08:45.010]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 23:08:45.015]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 23:08:45.019]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 23:08:45.024]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 23:08:45.028]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 23:08:45.034]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 23:08:45.038]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 23:08:45.043]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 23:08:45.048]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 23:08:45.052]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 23:08:45.057]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 23:08:45.062]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 23:08:45.066]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 23:08:45.071]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 23:08:45.076]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 23:08:45.080]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 23:08:45.085]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 23:08:45.090]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 23:08:45.094]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 23:08:45.099]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 23:08:45.104]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 23:08:45.108]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:45.114]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 23:08:45.118]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 23:08:45.123]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 23:08:45.127]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 23:08:45.132]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 23:08:45.137]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 23:08:45.141]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 23:08:45.145]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 23:08:45.150]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 23:08:45.154]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 23:08:45.159]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 23:08:45.163]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 23:08:45.168]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 23:08:45.173]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:45.178]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 23:08:45.182]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:45.187]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:45.192]  	[INFO]		[ServiceLocator]	Seeding activity type data from embedded resource
[2025-08-09 23:08:45.198]  	[DEBUG]		[ActivityTypeBaseService]	Starting import from embedded JSON resource
[2025-08-09 23:08:45.338]  	[INFO]		[ActivityTypeBaseService]	Parsed 1028 activity types from JSON
[2025-08-09 23:08:45.342]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:45.358]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 1/11 (100 records)
[2025-08-09 23:08:45.373]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 2/11 (100 records)
[2025-08-09 23:08:45.389]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 3/11 (100 records)
[2025-08-09 23:08:45.404]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 4/11 (100 records)
[2025-08-09 23:08:45.419]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 5/11 (100 records)
[2025-08-09 23:08:45.435]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 6/11 (100 records)
[2025-08-09 23:08:45.451]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 7/11 (100 records)
[2025-08-09 23:08:45.472]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 8/11 (100 records)
[2025-08-09 23:08:45.494]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 9/11 (100 records)
[2025-08-09 23:08:45.511]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 10/11 (100 records)
[2025-08-09 23:08:45.521]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 11/11 (28 records)
[2025-08-09 23:08:45.526]  	[INFO]		[ActivityTypeBaseService]	Successfully imported 1028 activity types
[2025-08-09 23:08:45.530]  	[INFO]		[ActivityTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-09 23:08:45.540]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 23:08:45.685]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 23:08:45.739]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 23:08:45.743]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 23:08:45.749]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-09 23:08:45.794]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-09 23:08:45.803]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 23:08:45.807]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-09 23:08:45.811]  	[INFO]		[ServiceLocator]	Successfully imported 1028 activity types
[2025-08-09 23:08:45.818]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:45.823]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 23:08:45.827]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:45.833]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:45.838]  	[INFO]		[ServiceLocator]	Seeding craft type data from embedded resource
[2025-08-09 23:08:45.843]  	[DEBUG]		[CraftTypeBaseService]	Starting import from embedded JSON resource
[2025-08-09 23:08:45.883]  	[INFO]		[CraftTypeBaseService]	Found 337 craft types to import
[2025-08-09 23:08:45.888]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:45.958]  	[INFO]		[CraftTypeBaseService]	Successfully imported 337 craft types
[2025-08-09 23:08:45.962]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:45.967]  	[INFO]		[CraftTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-09 23:08:45.971]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-09 23:08:45.982]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-09 23:08:45.989]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 23:08:45.994]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-09 23:08:45.998]  	[INFO]		[ServiceLocator]	Successfully imported 337 craft types
[2025-08-09 23:08:46.003]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 23:08:46.010]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 23:08:46.036]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 23:08:46.042]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:46.067]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 23:08:46.071]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 23:08:46.078]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:46.094]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 23:08:46.098]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 23:08:46.103]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 23:08:46.107]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 23:08:46.111]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 23:08:46.115]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 23:08:46.129]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 23:08:46.137]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 23:08:46.142]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 23:08:46.146]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 23:08:46.152]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 23:08:46.156]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:46.162]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-09 23:08:46.174]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.00 MB MB size
[2025-08-09 23:08:46.178]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 23:08:46.183]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 23:08:46.188]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 23:08:46.193]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 23:08:46.197]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 23:08:46.201]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 23:08:46.205]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 23:08:46.209]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 23:08:46.215]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 23:08:46.219]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 23:08:46.223]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 23:08:46.228]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 23:08:46.231]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 23:08:46.236]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 23:08:46.240]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 23:08:46.244]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 23:08:46.250]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 23:08:46.256]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 23:08:46.262]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.286]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-09 23:08:46.292]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.309]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.318]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 23:08:46.323]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.327]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.332]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 23:08:46.336]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.340]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.345]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 23:08:46.349]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.353]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.358]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 23:08:46.362]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.366]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.371]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 23:08:46.375]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.379]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.384]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 23:08:46.388]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.392]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-09 23:08:46.398]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 142ms
[2025-08-09 23:08:46.403]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 23:08:46.415]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 23:08:46.419]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 17ms
[2025-08-09 23:08:46.424]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 23:08:46.429]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 23:08:46.433]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 11ms
[2025-08-09 23:08:46.438]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 23:08:46.443]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:46.449]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 23:08:46.454]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:46.460]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 23:08:46.464]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:46.468]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 23:08:46.472]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:08:46.476]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 23:08:46.480]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 23:08:46.484]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 47ms
[2025-08-09 23:08:46.488]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 23:08:46.489]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 23:08:46.494]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 23:08:46.498]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.501]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 23:08:46.508]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 23:08:46.515]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.524]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.531]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 23:08:46.541]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.556]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.564]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 23:08:46.576]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.582]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.588]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 23:08:46.592]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.596]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.598]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-09 23:08:46.601]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 23:08:46.605]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 23:08:46.608]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.613]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 15ms
[2025-08-09 23:08:46.616]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.625]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 23:08:46.628]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.632]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.637]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 23:08:46.640]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.644]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.649]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 23:08:46.653]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.657]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.661]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 23:08:46.665]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.669]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:46.673]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 23:08:46.677]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:46.681]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 23:08:46.685]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 196ms
[2025-08-09 23:08:46.689]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 23:08:46.693]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 23:08:46.697]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 23:08:46.701]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 23:08:46.706]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 23:08:46.709]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 23:08:46.713]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 23:08:46.717]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 23:08:46.721]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 23:08:46.725]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 23:08:46.729]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 23:08:46.732]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 23:08:46.737]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 23:08:46.742]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 23:08:46.746]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 23:08:46.751]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 23:08:46.755]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 23:08:46.760]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 909%
[2025-08-09 23:08:46.765]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 1,818%
[2025-08-09 23:08:46.769]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 2,727%
[2025-08-09 23:08:46.773]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 3,636%
[2025-08-09 23:08:46.778]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 4,545%
[2025-08-09 23:08:46.781]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 5,455%
[2025-08-09 23:08:46.785]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 6,364%
[2025-08-09 23:08:46.789]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 7,273%
[2025-08-09 23:08:46.794]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 8,182%
[2025-08-09 23:08:46.798]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 9,091%
[2025-08-09 23:08:46.802]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 10,000%
[2025-08-09 23:08:46.806]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 0%
[2025-08-09 23:08:46.810]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-09 23:08:46.814]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-09 23:08:46.818]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-09 23:08:46.822]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-09 23:08:46.827]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-09 23:08:46.831]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-09 23:08:46.835]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-09 23:08:46.839]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-09 23:08:46.843]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-09 23:08:46.847]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-09 23:08:46.852]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-09 23:08:46.856]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-09 23:08:46.860]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-09 23:08:46.864]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-09 23:08:46.868]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-09 23:08:46.873]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-09 23:08:46.877]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-09 23:08:46.881]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-09 23:08:46.885]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-09 23:08:46.889]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-09 23:08:46.893]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-09 23:08:46.897]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-09 23:08:46.900]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-09 23:08:46.904]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-09 23:08:46.908]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-09 23:08:46.912]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-09 23:08:46.916]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-09 23:08:46.920]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-09 23:08:46.923]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-09 23:08:46.927]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-09 23:08:46.931]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-09 23:08:46.935]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-09 23:08:46.938]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-09 23:08:46.942]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-09 23:08:46.946]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-09 23:08:46.950]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-09 23:08:46.954]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-09 23:08:46.957]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-09 23:08:46.961]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-09 23:08:46.975]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-09 23:08:46.984]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-09 23:08:46.990]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-09 23:08:47.007]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-09 23:08:47.015]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-09 23:08:47.022]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-09 23:08:47.026]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-09 23:08:47.030]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-09 23:08:47.034]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-09 23:08:47.038]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-09 23:08:47.044]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-09 23:08:47.048]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-09 23:08:47.052]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-09 23:08:47.056]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-09 23:08:47.060]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-09 23:08:47.065]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-09 23:08:47.069]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-09 23:08:47.073]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-09 23:08:47.077]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-09 23:08:47.081]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-09 23:08:47.085]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-09 23:08:47.089]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-09 23:08:47.093]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-09 23:08:47.097]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-09 23:08:47.101]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-09 23:08:47.105]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-09 23:08:47.115]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-09 23:08:47.124]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-09 23:08:47.130]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-09 23:08:47.135]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-09 23:08:47.140]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-09 23:08:47.145]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-09 23:08:47.150]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-09 23:08:47.155]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-09 23:08:47.160]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-09 23:08:47.165]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-09 23:08:47.170]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-09 23:08:47.174]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-09 23:08:47.178]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-09 23:08:47.182]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-09 23:08:47.186]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-09 23:08:47.190]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-09 23:08:47.194]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-09 23:08:47.199]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-09 23:08:47.203]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-09 23:08:47.207]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-09 23:08:47.211]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-09 23:08:47.215]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-09 23:08:47.219]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-09 23:08:47.223]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-09 23:08:47.227]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-09 23:08:47.231]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-09 23:08:47.235]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-09 23:08:47.239]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-09 23:08:47.244]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-09 23:08:47.249]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-09 23:08:47.253]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-09 23:08:47.257]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-09 23:08:47.261]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-09 23:08:47.265]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-09 23:08:47.269]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-09 23:08:47.273]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-09 23:08:47.278]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-09 23:08:47.282]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-09 23:08:47.286]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-09 23:08:47.290]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-09 23:08:47.294]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-09 23:08:47.298]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-09 23:08:47.302]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-09 23:08:47.307]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-09 23:08:47.311]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-09 23:08:47.315]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-09 23:08:47.319]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-09 23:08:47.323]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-09 23:08:47.327]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-09 23:08:47.331]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-09 23:08:47.335]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-09 23:08:47.339]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-09 23:08:47.343]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-09 23:08:47.346]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-09 23:08:47.350]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-09 23:08:47.354]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-09 23:08:47.358]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-09 23:08:47.362]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-09 23:08:47.365]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-09 23:08:47.369]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-09 23:08:47.374]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-09 23:08:47.377]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-09 23:08:47.381]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-09 23:08:47.385]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-09 23:08:47.389]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-09 23:08:47.392]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-09 23:08:47.396]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-09 23:08:47.400]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-09 23:08:47.404]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-09 23:08:47.408]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-09 23:08:47.411]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-09 23:08:47.415]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-09 23:08:47.419]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-09 23:08:47.423]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-09 23:08:47.426]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-09 23:08:47.430]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-09 23:08:47.434]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-09 23:08:47.437]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-09 23:08:47.441]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-09 23:08:47.445]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-09 23:08:47.449]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-09 23:08:47.453]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-09 23:08:47.457]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-09 23:08:47.461]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-09 23:08:47.465]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-09 23:08:47.469]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-09 23:08:47.475]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-09 23:08:47.479]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-09 23:08:47.483]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-09 23:08:47.487]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-09 23:08:47.491]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-09 23:08:47.495]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-09 23:08:47.499]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-09 23:08:47.504]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-09 23:08:47.508]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-09 23:08:47.513]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-09 23:08:47.517]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-09 23:08:47.525]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-09 23:08:47.529]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-09 23:08:47.534]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-09 23:08:47.538]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-09 23:08:47.543]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-09 23:08:47.547]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-09 23:08:47.551]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-09 23:08:47.555]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-09 23:08:47.558]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-09 23:08:47.562]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-09 23:08:47.566]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-09 23:08:47.570]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-09 23:08:47.574]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-09 23:08:47.578]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-09 23:08:47.582]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-09 23:08:47.587]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-09 23:08:47.592]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-09 23:08:47.597]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-09 23:08:47.605]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-09 23:08:47.618]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-09 23:08:47.630]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-09 23:08:47.648]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-09 23:08:47.660]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-09 23:08:47.664]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-09 23:08:47.668]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-09 23:08:47.672]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-09 23:08:47.678]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-09 23:08:47.682]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-09 23:08:47.686]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-09 23:08:47.690]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-09 23:08:47.694]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-09 23:08:47.698]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-09 23:08:47.702]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-09 23:08:47.706]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-09 23:08:47.710]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-09 23:08:47.714]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-09 23:08:47.718]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-09 23:08:47.722]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-09 23:08:47.726]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-09 23:08:47.730]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-09 23:08:47.734]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-09 23:08:47.740]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-09 23:08:47.751]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-09 23:08:47.765]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-09 23:08:47.770]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-09 23:08:47.783]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-09 23:08:47.788]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-09 23:08:47.794]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-09 23:08:47.799]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-09 23:08:47.804]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-09 23:08:47.808]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-09 23:08:47.812]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-09 23:08:47.817]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-09 23:08:47.822]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-09 23:08:47.826]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-09 23:08:47.833]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-09 23:08:47.837]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-09 23:08:47.841]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-09 23:08:47.850]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-09 23:08:47.855]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-09 23:08:47.860]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-09 23:08:47.865]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-09 23:08:47.869]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-09 23:08:47.874]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-09 23:08:47.879]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-09 23:08:47.884]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-09 23:08:47.899]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-09 23:08:47.906]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-09 23:08:47.912]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-09 23:08:47.917]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-09 23:08:47.921]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-09 23:08:47.927]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-09 23:08:47.932]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-09 23:08:47.936]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-09 23:08:47.941]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-09 23:08:47.946]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-09 23:08:47.950]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-09 23:08:47.955]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-09 23:08:47.959]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-09 23:08:47.963]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-09 23:08:47.967]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-09 23:08:47.972]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-09 23:08:47.976]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-09 23:08:47.980]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-09 23:08:47.984]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-09 23:08:47.989]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-09 23:08:47.993]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-09 23:08:47.997]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-09 23:08:48.001]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-09 23:08:48.006]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-09 23:08:48.010]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-09 23:08:48.014]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-09 23:08:48.018]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-09 23:08:48.022]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-09 23:08:48.026]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-09 23:08:48.030]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-09 23:08:48.034]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-09 23:08:48.039]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-09 23:08:48.043]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-09 23:08:48.047]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-09 23:08:48.051]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-09 23:08:48.055]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-09 23:08:48.058]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-09 23:08:48.062]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-09 23:08:48.066]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-09 23:08:48.070]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-09 23:08:48.074]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-09 23:08:48.078]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-09 23:08:48.082]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-09 23:08:48.086]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-09 23:08:48.090]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-09 23:08:48.094]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-09 23:08:48.098]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-09 23:08:48.102]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-09 23:08:48.105]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-09 23:08:48.109]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-09 23:08:48.113]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-09 23:08:48.117]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-09 23:08:48.121]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-09 23:08:48.124]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-09 23:08:48.128]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-09 23:08:48.132]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-09 23:08:48.136]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-09 23:08:48.139]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-09 23:08:48.143]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-09 23:08:48.147]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-09 23:08:48.151]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-09 23:08:48.155]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-09 23:08:48.158]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-09 23:08:48.162]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-09 23:08:48.166]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-09 23:08:48.170]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-09 23:08:48.174]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-09 23:08:48.178]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-09 23:08:48.182]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-09 23:08:48.186]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-09 23:08:48.190]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-09 23:08:48.195]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-09 23:08:48.199]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-09 23:08:48.203]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-09 23:08:48.207]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-09 23:08:48.212]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-09 23:08:48.216]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-09 23:08:48.220]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-09 23:08:48.224]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-09 23:08:48.228]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-09 23:08:48.232]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-09 23:08:48.236]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-09 23:08:48.240]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-09 23:08:48.244]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-09 23:08:48.248]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-09 23:08:48.252]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-09 23:08:48.256]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-09 23:08:48.260]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-09 23:08:48.263]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-09 23:08:48.267]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-09 23:08:48.271]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-09 23:08:48.275]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-09 23:08:48.278]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-09 23:08:48.282]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-09 23:08:48.285]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-09 23:08:48.289]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-09 23:08:48.293]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-09 23:08:48.297]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-09 23:08:48.300]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-09 23:08:48.304]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-09 23:08:48.308]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-09 23:08:48.311]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-09 23:08:48.315]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-09 23:08:48.319]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-09 23:08:48.322]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-09 23:08:48.326]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-09 23:08:48.330]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-09 23:08:48.333]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-09 23:08:48.339]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:08:48.441]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 23:08:48.445]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 23:08:48.449]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 23:08:48.453]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 23:08:48.459]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 23:08:48.464]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:48.469]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 23:08:48.473]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:48.479]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 23:08:48.483]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:48.546]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 23:08:48.550]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 23:08:48.788]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 23:08:48.795]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-09 23:08:48.815]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:48.819]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:48.823]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:48.828]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:48.832]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:48.837]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:48.978]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-09 23:08:49.143]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 23:08:52.251]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:52.255]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:52.259]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:52.262]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:52.266]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:52.270]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:52.331]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 23:08:52.337]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-09 23:08:52.342]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-09 23:08:52.361]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 23:08:52.365]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_1003178_638903741323656491 (BaseViewModel) for NPersonalViewModel
[2025-08-09 23:08:52.369]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-09 23:08:52.373]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:52.376]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_9028608_638903741323767983 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 23:08:52.380]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 23:08:52.384]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:52.387]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 23:08:52.391]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_14148614_638903741323917523 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 23:08:52.395]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 23:08:52.399]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:52.402]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 23:08:52.406]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 23:08:52.420]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-09 23:08:52.494]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 23:08:52.532]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_52610097_638903741325328118 (BaseViewModel) for NewClientViewModel
[2025-08-09 23:08:52.537]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-09 23:08:52.541]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:52.545]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_3728830_638903741325450722 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 23:08:52.549]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 23:08:52.552]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:52.556]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 23:08:52.561]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_33559471_638903741325610269 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 23:08:52.564]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 23:08:52.569]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:52.573]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 23:08:52.577]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 23:08:52.582]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_33599791_638903741325827375 (BaseViewModel) for ActivityManagementViewModel
[2025-08-09 23:08:52.586]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-09 23:08:52.590]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:52.595]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 23:08:52.600]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-09 23:08:52.604]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 23:08:52.608]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-09 23:08:52.612]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_33962670_638903741326124455 (BaseViewModel) for NotesManagementViewModel
[2025-08-09 23:08:52.616]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-09 23:08:52.619]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:08:52.623]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 23:08:52.628]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-09 23:08:52.632]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-09 23:08:52.636]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 23:08:52.640]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-09 23:08:52.645]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 23:08:52.650]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 23:08:52.653]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 23:08:52.657]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 23:08:52.661]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-09 23:08:52.665]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-09 23:08:52.669]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-09 23:08:52.673]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-09 23:08:52.678]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 335ms (Success: True)
[2025-08-09 23:08:52.682]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 346ms (Success: True)
[2025-08-09 23:08:52.687]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x560 (Height-based width calculation)
[2025-08-09 23:08:52.708]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 2, misses: 1)
[2025-08-09 23:08:52.710]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 23:08:52.712]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-09 23:08:52.712]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-09 23:08:52.716]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-09 23:08:52.724]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 16ms
[2025-08-09 23:08:52.728]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 20ms
[2025-08-09 23:08:52.738]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:08:53.187]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-09 23:08:53.195]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 23:08:53.353]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:53.358]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:53.362]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:53.369]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:53.375]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:53.381]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:53.403]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 918.2494ms
[2025-08-09 23:08:53.409]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:08:53.413]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1036.7897ms
[2025-08-09 23:08:53.417]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:08:53.421]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1029.71ms
[2025-08-09 23:08:53.425]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:08:53.469]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:53.485]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:53.490]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:53.495]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:53.499]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:53.509]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:53.641]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1109.1631ms
[2025-08-09 23:08:53.646]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:08:53.650]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1105.5433ms
[2025-08-09 23:08:53.654]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:08:53.659]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1098.8138ms
[2025-08-09 23:08:53.670]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:08:53.679]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1096.8244ms
[2025-08-09 23:08:53.688]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:08:53.719]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1106.605ms
[2025-08-09 23:08:53.723]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:08:54.424]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2047.9702ms
[2025-08-09 23:08:54.429]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:08:54.447]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2055.6995ms
[2025-08-09 23:08:54.452]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:08:54.457]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.461]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:54.465]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.469]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:54.473]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.477]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:54.703]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2170.2794ms
[2025-08-09 23:08:54.707]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:08:54.734]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2189.4072ms
[2025-08-09 23:08:54.738]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:08:54.744]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2183.2832ms
[2025-08-09 23:08:54.748]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:08:54.781]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2198.8ms
[2025-08-09 23:08:54.785]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:08:54.790]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2178.3961ms
[2025-08-09 23:08:54.794]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:08:54.921]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.925]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:54.928]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.932]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:54.936]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.941]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:54.952]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.956]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:54.961]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.965]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:54.969]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:54.973]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:55.466]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2981.6814ms
[2025-08-09 23:08:55.471]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:08:56.800]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:56.813]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:56.819]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:56.823]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:56.827]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:56.831]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:56.880]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:56.921]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:56.962]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:56.982]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:57.037]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:57.077]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:57.375]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbersCollection(1)
[2025-08-09 23:08:57.700]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:57.705]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:57.708]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:57.712]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:57.716]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:57.720]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:57.921]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:57.925]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:57.928]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:57.932]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:57.936]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:57.940]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:58.276]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.281]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:58.285]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.288]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:58.292]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.296]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:58.621]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.627]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:58.631]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.635]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:58.639]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.643]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:58.886]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.891]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:58.896]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.919]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:58.946]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:58.964]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:08:59.092]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:08:59.100]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:08:59.109]  	[DEBUG]		[ActivityManagementViewModel]	Added activity to multiple activities collection: 107107
[2025-08-09 23:08:59.114]  	[DEBUG]		[ActivityManagementViewModel]	Activity description found for code 107107: صناعة تغذية الأطفال
[2025-08-09 23:08:59.214]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:59.220]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:59.224]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:59.228]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:08:59.232]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:08:59.236]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:01.388]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:01.392]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:01.396]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:01.400]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:01.404]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:01.407]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:01.454]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for Craft
[2025-08-09 23:09:01.458]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: Craft
[2025-08-09 23:09:01.462]  	[INFO]		[NewClientViewModel]	Switched to activity tab: Craft
[2025-08-09 23:09:01.468]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:01.473]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:01.474]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:01.480]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:01.485]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:01.489]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:01.515]  	[DEBUG]		[ActivityManagementViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 23:09:01.534]  	[DEBUG]		[NewClientViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 23:09:01.675]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: Craft
[2025-08-09 23:09:01.721]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 9, Time since interaction: 205.7811ms
[2025-08-09 23:09:01.727]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:09:01.861]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 402.8047ms
[2025-08-09 23:09:01.865]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:09:02.341]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:02.373]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:02.413]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:02.442]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:02.448]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:02.465]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:02.507]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:02.515]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:02.520]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:02.524]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:02.529]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:02.533]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:02.557]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 9, Batched: 9, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: G12DisplayText(2), BISDisplayText(2), SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1)
[2025-08-09 23:09:02.610]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 7, Batched: 7, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1)
[2025-08-09 23:09:03.249]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:03.253]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:03.257]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:03.260]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:03.264]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:03.268]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:03.481]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:03.485]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:03.489]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:03.493]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:03.496]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:03.500]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:03.730]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 9, Time since interaction: 2214.7786ms
[2025-08-09 23:09:03.734]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:09:03.871]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 7, Time since interaction: 2413.0596ms
[2025-08-09 23:09:03.944]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:09:04.179]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.184]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:04.188]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.192]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:04.196]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.202]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:04.444]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.449]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:04.453]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.456]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:04.460]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.464]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:04.742]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.746]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:04.750]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.754]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:04.758]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.762]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:04.945]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.949]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:04.953]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.957]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:04.961]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:04.965]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:05.116]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:09:05.122]  	[DEBUG]		[CraftTypeBaseService]	Retrieved craft type for code: 01-01-001 (Cache hits: 0, misses: 2)
[2025-08-09 23:09:05.125]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:09:05.134]  	[DEBUG]		[ActivityManagementViewModel]	Craft description found for code 01-01-001: حرفي مكرر لزيت الزيتون (عصر و تكرير تقليدي للزيتون).
[2025-08-09 23:09:05.219]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:05.235]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:05.243]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:05.249]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:05.253]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:05.257]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:06.734]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:06.739]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:06.744]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:06.748]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:06.752]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:06.757]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:06.810]  	[INFO]		[NewClientViewModel]	Craft information dialog requested
[2025-08-09 23:09:06.816]  	[INFO]		[ActivityManagementViewModel]	Opening craft information dialog for code: 01-01-001
[2025-08-09 23:09:06.820]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft type for code: 01-01-001 (Cache hits: 1, misses: 2)
[2025-08-09 23:09:06.834]  	[DEBUG]		[CraftInformationDialog]	Craft information loaded for: 01-01-001 - حرفي مكرر لزيت الزيتون (عصر و تكرير تقليدي للزيتون).
[2025-08-09 23:09:07.000]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:07.013]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:07.024]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:07.029]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:07.037]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:07.042]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:07.091]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:07.101]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:07.107]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:07.112]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:07.116]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:07.121]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:09.640]  	[DEBUG]		[CraftInformationDialog]	Craft information dialog closed
[2025-08-09 23:09:09.698]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:09.703]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:09.706]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:09.711]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:09.717]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:09.722]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:10.346]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.351]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:10.355]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.359]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:10.362]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.367]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:10.426]  	[INFO]		[NewClientViewModel]	Craft types search dialog requested
[2025-08-09 23:09:10.432]  	[INFO]		[ActivityManagementViewModel]	Opening craft types search dialog
[2025-08-09 23:09:10.452]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 2, misses: 2)
[2025-08-09 23:09:10.457]  	[DEBUG]		[CraftSearchDialog]	Loaded 20 initial craft types
[2025-08-09 23:09:10.727]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.751]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:10.764]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.769]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:10.773]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.777]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:10.867]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.872]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:10.877]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.881]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:10.886]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:10.891]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:11.431]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:11.436]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:11.441]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:11.445]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:11.450]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:11.454]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:11.524]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:11.529]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:11.538]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:11.551]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:11.557]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:11.561]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:13.131]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.137]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:13.142]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.149]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:13.157]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.167]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:13.211]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.216]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:13.220]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.224]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:13.229]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.235]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:13.335]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.340]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:13.344]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.348]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:13.352]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:13.356]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:13.575]  	[DEBUG]		[CraftSearchDialog]	Search for 'ح' returned 0 results
[2025-08-09 23:09:14.986]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:14.990]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:14.995]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:15.000]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:15.004]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:15.008]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:15.184]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 3, misses: 2)
[2025-08-09 23:09:15.253]  	[DEBUG]		[WordFrequencySearchService]	Word frequency search completed for 'حر': 50 results in 40.9ms
[2025-08-09 23:09:15.269]  	[DEBUG]		[CraftTypeBaseService]	Exact prefix craft search completed for 'حر': 50 results with Arabic analysis
[2025-08-09 23:09:15.275]  	[DEBUG]		[CraftSearchDialog]	Search for 'حر' returned 50 results
[2025-08-09 23:09:15.407]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:15.411]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:15.415]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:15.419]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:15.424]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:15.428]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:15.654]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:15.658]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:15.662]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:15.666]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:15.670]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:15.674]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:15.685]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 4, misses: 2)
[2025-08-09 23:09:15.706]  	[DEBUG]		[WordFrequencySearchService]	Word frequency search completed for 'حرفي': 50 results in 17.1ms
[2025-08-09 23:09:15.710]  	[DEBUG]		[CraftTypeBaseService]	Exact prefix craft search completed for 'حرفي': 50 results with Arabic analysis
[2025-08-09 23:09:15.716]  	[DEBUG]		[CraftSearchDialog]	Search for 'حرفي' returned 50 results
[2025-08-09 23:09:16.556]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:16.560]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:16.565]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:16.569]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:16.573]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:16.577]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:16.619]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 5, misses: 2)
[2025-08-09 23:09:16.624]  	[DEBUG]		[CraftTypeBaseService]	Exact prefix craft search completed for 'حرفي': 50 results with Arabic analysis
[2025-08-09 23:09:16.628]  	[DEBUG]		[CraftSearchDialog]	Search for 'حرفي' returned 50 results
[2025-08-09 23:09:29.877]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:29.881]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:29.885]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:29.889]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:29.893]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:29.897]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:30.141]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:30.145]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:30.150]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:30.154]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:30.158]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:30.161]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:30.282]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 6, misses: 2)
[2025-08-09 23:09:30.300]  	[DEBUG]		[CraftTypeBaseService]	Exact prefix craft search completed for 'حرفي سن': 1 results with Arabic analysis
[2025-08-09 23:09:30.305]  	[DEBUG]		[CraftSearchDialog]	Search for 'حرفي سن' returned 1 results
[2025-08-09 23:09:31.112]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 7, misses: 2)
[2025-08-09 23:09:31.118]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:31.123]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:31.127]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:31.132]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:31.135]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:31.139]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:31.146]  	[DEBUG]		[CraftTypeBaseService]	Exact prefix craft search completed for 'حرفي سنا': 1 results with Arabic analysis
[2025-08-09 23:09:31.151]  	[DEBUG]		[CraftSearchDialog]	Search for 'حرفي سنا' returned 1 results
[2025-08-09 23:09:31.782]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 8, misses: 2)
[2025-08-09 23:09:31.788]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:31.792]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:31.796]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:31.801]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:31.804]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:31.808]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:31.815]  	[DEBUG]		[CraftTypeBaseService]	Exact prefix craft search completed for 'حرفي سنان': 1 results with Arabic analysis
[2025-08-09 23:09:31.820]  	[DEBUG]		[CraftSearchDialog]	Search for 'حرفي سنان' returned 1 results
[2025-08-09 23:09:33.058]  	[DEBUG]		[CraftSearchDialog]	Craft selected: 03-20-006 - حرفي سنان. (السن).
[2025-08-09 23:09:33.091]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:33.100]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:33.106]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:33.110]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:33.115]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:33.120]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:33.148]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:33.153]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:33.158]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:33.161]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:33.167]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:33.171]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:34.317]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:34.321]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:34.326]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:34.332]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:34.336]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:34.340]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:34.395]  	[INFO]		[CraftSearchDialog]	Craft selected for use: 03-20-006 - حرفي سنان. (السن).
[2025-08-09 23:09:34.400]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:09:34.405]  	[DEBUG]		[CraftTypeBaseService]	Retrieved craft type for code: 03-20-006 (Cache hits: 8, misses: 3)
[2025-08-09 23:09:34.409]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:09:34.418]  	[DEBUG]		[ActivityManagementViewModel]	Craft description found for code 03-20-006: حرفي سنان. (السن).
[2025-08-09 23:09:34.423]  	[INFO]		[ActivityManagementViewModel]	Craft selected: 03-20-006 - حرفي سنان. (السن).
[2025-08-09 23:09:34.471]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:34.475]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:34.479]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:34.483]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:34.488]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:34.492]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:36.700]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:36.710]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:36.719]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:36.725]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:36.732]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:36.739]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:36.824]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: MainCommercial
[2025-08-09 23:09:36.828]  	[INFO]		[NewClientViewModel]	Switched to activity tab: MainCommercial
[2025-08-09 23:09:36.874]  	[DEBUG]		[ActivityManagementViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 23:09:36.910]  	[DEBUG]		[NewClientViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 23:09:36.917]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:36.921]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:36.925]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:36.930]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:36.936]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:36.940]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:37.124]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 9, Time since interaction: 249.5789ms
[2025-08-09 23:09:37.128]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:09:37.155]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: MainCommercial
[2025-08-09 23:09:37.487]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 662.2945ms
[2025-08-09 23:09:37.491]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:09:37.690]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 9, Batched: 9, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: G12DisplayText(2), BISDisplayText(2), SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1)
[2025-08-09 23:09:37.694]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 7, Batched: 7, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1)
[2025-08-09 23:09:38.033]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.038]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:38.042]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.046]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:38.050]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.054]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:38.127]  	[INFO]		[NewClientViewModel]	Multiple activities management requested
[2025-08-09 23:09:38.137]  	[INFO]		[ActivityManagementViewModel]	Opening multiple activities management dialog
[2025-08-09 23:09:38.164]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 23:09:38.171]  	[DEBUG]		[ResourceManager]	Registered resource: MultipleActivitiesDialogViewModel_35081372_638903741781712470 (BaseViewModel) for MultipleActivitiesDialogViewModel
[2025-08-09 23:09:38.175]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	BaseViewModel memory management initialized for MultipleActivitiesDialogViewModel
[2025-08-09 23:09:38.179]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:09:38.184]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel initialized
[2025-08-09 23:09:38.188]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel initialized with IDisposable support
[2025-08-09 23:09:38.193]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Loaded 1 existing activities
[2025-08-09 23:09:38.354]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:38.441]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.457]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:38.480]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.491]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:38.500]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.507]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:38.563]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.576]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:38.585]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.600]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:38.604]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:38.608]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:39.202]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 1008.4395ms
[2025-08-09 23:09:39.206]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:09:39.413]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 9, Time since interaction: 2539.3632ms
[2025-08-09 23:09:39.417]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:09:39.539]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 7, Time since interaction: 2714.2793ms
[2025-08-09 23:09:39.542]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:09:40.089]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:40.167]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.171]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:40.175]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.178]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:40.183]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.187]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:40.284]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:40.446]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.450]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:40.454]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.458]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:40.461]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.466]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:40.587]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:40.741]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.745]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:40.750]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.753]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:40.757]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:40.761]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:40.878]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:41.037]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.093]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:41.186]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.233]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:41.271]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.276]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:41.299]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:41.338]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.342]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:41.346]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.350]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:41.354]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.358]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:41.395]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:09:41.400]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:09:41.404]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Added activity 108108: الغزل الصناعي للقطن الممزوج بمواد نسيجية أخرى
[2025-08-09 23:09:41.415]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:09:41.540]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.544]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:41.548]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.552]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:41.557]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:41.561]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:43.187]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	PropertyChanged Performance - Total: 10, Batched: 10, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ManualActivityCode(7), HasActivities(3)
[2025-08-09 23:09:43.545]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:43.640]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:43.644]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:43.648]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:43.652]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:43.656]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:43.660]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:43.795]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:43.937]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:43.941]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:43.945]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:43.950]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:43.954]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:43.957]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:44.122]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:44.246]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.251]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:44.257]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.261]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:44.266]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.270]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:44.389]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:44.501]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.505]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:44.509]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.512]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:44.517]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.521]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:44.612]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:44.751]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.755]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:44.759]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.762]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:44.766]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:44.770]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:44.938]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:09:44.943]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:09:44.947]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Added activity 109109: الإنتاج الصناعي للمنتجات الخزفية غير الصحية ، للصناعة والبناء
[2025-08-09 23:09:44.958]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:09:45.033]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:45.037]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:45.041]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:45.045]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:45.049]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:45.053]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:46.241]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 282.1MB, System: 30.0%, Pressure: Normal
[2025-08-09 23:09:46.615]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:46.642]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:46.656]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:46.659]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:46.664]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:46.667]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:46.713]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:46.718]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:46.722]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:46.726]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:46.730]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:46.734]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:47.361]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 4, Time since interaction: 2409.5282ms
[2025-08-09 23:09:47.366]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:09:48.212]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	PropertyChanged Performance - Total: 8, Batched: 8, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ManualActivityCode(7), HasActivities(1)
[2025-08-09 23:09:48.542]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.551]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:48.556]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.561]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:48.565]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.570]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:48.588]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.592]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:48.597]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.601]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:48.605]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.608]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:48.667]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:48.745]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.782]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:48.798]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.802]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:48.807]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:48.811]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:49.376]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 13.8599ms
[2025-08-09 23:09:49.381]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:09:49.408]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:49.500]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.504]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:49.507]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.511]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:49.515]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.518]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:49.859]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.868]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:49.878]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.890]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:49.896]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.901]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:49.935]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.939]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:49.943]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.946]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:49.951]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:49.955]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:50.338]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:50.398]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:50.437]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:50.481]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:50.486]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:50.490]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:50.494]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:50.500]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:50.504]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:51.922]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:51.926]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:51.930]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:51.965]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:52.075]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:52.080]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:52.084]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:52.088]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:52.092]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:52.095]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:52.236]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 3, misses: 1)
[2025-08-09 23:09:52.256]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تج': 10 results with Arabic analysis
[2025-08-09 23:09:52.261]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تج' returned 10 results
[2025-08-09 23:09:52.295]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:09:53.114]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:53.145]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:53.157]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:53.222]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:53.239]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	PropertyChanged Performance - Total: 9, Batched: 9, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SearchText(5), IsSearching(3), SearchResults(1)
[2025-08-09 23:09:53.249]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:53.257]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:53.261]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:53.265]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:53.271]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:53.276]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:53.462]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 4, misses: 1)
[2025-08-09 23:09:53.483]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجا': 10 results with Arabic analysis
[2025-08-09 23:09:53.490]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجا' returned 10 results
[2025-08-09 23:09:53.509]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:09:53.738]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:53.745]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:53.750]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:53.784]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:53.848]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:53.852]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:53.856]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:53.860]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:53.864]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:53.869]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:53.906]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:53.910]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:53.915]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:54.021]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:54.025]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:54.028]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:54.033]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:54.037]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:54.041]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:54.082]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:54.220]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 5, misses: 1)
[2025-08-09 23:09:54.246]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة': 10 results with Arabic analysis
[2025-08-09 23:09:54.251]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة' returned 10 results
[2025-08-09 23:09:54.257]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:54.314]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:54.318]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:54.322]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:54.361]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:54.422]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:54.426]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:54.429]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:54.433]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:54.437]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:54.441]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:54.643]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 6, misses: 1)
[2025-08-09 23:09:54.671]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة ': 10 results with Arabic analysis
[2025-08-09 23:09:54.676]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة ' returned 10 results
[2025-08-09 23:09:54.711]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:09:55.954]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:55.958]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:55.962]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:55.993]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:56.070]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:56.074]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:56.078]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:56.082]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:56.086]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:56.090]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:56.227]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:56.233]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:56.236]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:56.287]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:56.346]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:56.432]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:56.448]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:56.457]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:56.505]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:56.508]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:56.512]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:56.516]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:56.520]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:56.524]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:56.552]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:56.754]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 7, misses: 1)
[2025-08-09 23:09:56.781]  	[DEBUG]		[WordFrequencySearchService]	Word frequency search completed for 'تجارة بال': 10 results in 22.9ms
[2025-08-09 23:09:56.784]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بال': 10 results with Arabic analysis
[2025-08-09 23:09:56.788]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بال' returned 10 results
[2025-08-09 23:09:56.806]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:57.010]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:57.015]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:57.019]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:57.036]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:57.081]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:57.085]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:57.089]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:57.092]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:57.096]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:57.102]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:57.324]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 8, misses: 1)
[2025-08-09 23:09:57.351]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالت': 10 results with Arabic analysis
[2025-08-09 23:09:57.355]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالت' returned 10 results
[2025-08-09 23:09:57.367]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:09:57.722]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:57.726]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:57.730]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:57.737]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:57.833]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:57.837]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:57.841]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:57.845]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:57.850]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:57.853]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:58.034]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 9, misses: 1)
[2025-08-09 23:09:58.062]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالتج': 10 results with Arabic analysis
[2025-08-09 23:09:58.066]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالتج' returned 10 results
[2025-08-09 23:09:58.096]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:09:58.249]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	PropertyChanged Performance - Total: 27, Batched: 27, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: IsSearching(13), SearchText(8), SearchResults(6)
[2025-08-09 23:09:58.474]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:58.478]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:58.483]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:58.502]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:58.570]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:58.575]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:58.580]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:58.585]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:58.589]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:58.594]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:58.786]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 10, misses: 1)
[2025-08-09 23:09:58.814]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالتجز': 10 results with Arabic analysis
[2025-08-09 23:09:58.818]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالتجز' returned 10 results
[2025-08-09 23:09:58.842]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:09:58.922]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:58.927]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:58.931]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:58.962]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:59.004]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:59.008]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:59.012]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:59.016]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:59.020]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:59.024]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:59.082]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:09:59.087]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:09:59.091]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:09:59.172]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:59.176]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:59.180]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:59.184]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:09:59.189]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:09:59.193]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:09:59.238]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:09:59.393]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 11, misses: 1)
[2025-08-09 23:09:59.424]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالتجزئة': 10 results with Arabic analysis
[2025-08-09 23:09:59.428]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالتجزئة' returned 10 results
[2025-08-09 23:09:59.460]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:00.890]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:10:00.894]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:10:00.898]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:10:00.921]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:01.012]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:01.017]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:01.020]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:01.024]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:01.027]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:01.031]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:01.201]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 12, misses: 1)
[2025-08-09 23:10:01.233]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالتجزئة ': 10 results with Arabic analysis
[2025-08-09 23:10:01.237]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالتجزئة ' returned 10 results
[2025-08-09 23:10:01.267]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:10:02.138]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:10:02.150]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:10:02.156]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:10:02.177]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:02.332]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:02.336]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:02.340]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:02.343]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:02.347]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:02.351]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:02.362]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:10:02.366]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:10:02.370]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:10:02.427]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:02.432]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:02.436]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:02.439]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:02.443]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:02.448]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:02.488]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:02.673]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 13, misses: 1)
[2025-08-09 23:10:02.708]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالتجزئة لل': 10 results with Arabic analysis
[2025-08-09 23:10:02.713]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالتجزئة لل' returned 10 results
[2025-08-09 23:10:02.753]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:03.058]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:10:03.101]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:10:03.105]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:10:03.112]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:03.152]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.156]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:03.160]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.164]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:03.168]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.172]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:03.186]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.190]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:03.193]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.197]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:03.201]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.205]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:03.253]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	PropertyChanged Performance - Total: 20, Batched: 20, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: IsSearching(9), SearchText(7), SearchResults(4)
[2025-08-09 23:10:03.410]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 14, misses: 1)
[2025-08-09 23:10:03.442]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالتجزئة للأ': 10 results with Arabic analysis
[2025-08-09 23:10:03.446]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالتجزئة للأ' returned 10 results
[2025-08-09 23:10:03.461]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:10:03.496]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:10:03.501]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:10:03.504]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:10:03.537]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:03.572]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.576]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:03.580]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.583]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:03.588]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:03.591]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:03.809]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 15, misses: 1)
[2025-08-09 23:10:03.843]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالتجزئة للأل': 10 results with Arabic analysis
[2025-08-09 23:10:03.847]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالتجزئة للأل' returned 10 results
[2025-08-09 23:10:03.875]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:10:04.834]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 23:10:04.838]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 23:10:04.842]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:10:04.863]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:04.956]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:04.960]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:04.964]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:04.968]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:04.972]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:04.976]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:05.144]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 16, misses: 1)
[2025-08-09 23:10:05.183]  	[DEBUG]		[ActivityTypeBaseService]	Exact prefix activity search completed for 'تجارة بالتجزئة للألب': 6 results with Arabic analysis
[2025-08-09 23:10:05.188]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Database search for 'تجارة بالتجزئة للألب' returned 6 results
[2025-08-09 23:10:05.210]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:10:07.649]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 4, Time since interaction: 2451.413ms
[2025-08-09 23:10:07.653]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:10:08.251]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	PropertyChanged Performance - Total: 10, Batched: 10, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: IsSearching(5), SearchResults(3), SearchText(2)
[2025-08-09 23:10:08.775]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Added activity 502102: تجارة بالتجزئة للألبسة ، الأحذية و الأنسجة
[2025-08-09 23:10:08.822]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:10:08.862]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:08.866]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:08.869]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:08.873]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:08.877]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:08.881]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:09.692]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 912.3188ms
[2025-08-09 23:10:09.696]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:10:10.026]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:10.030]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:10.033]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:10.037]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:10.040]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:10.044]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:10.096]  	[INFO]		[SaveCancelButtonsControl]	Save button clicked in SaveCancelButtonsControl
[2025-08-09 23:10:10.101]  	[INFO]		[MultipleActivitiesDialog]	Multiple activities dialog saved with 4 activities
[2025-08-09 23:10:10.108]  	[INFO]		[ActivityManagementViewModel]	Multiple activities updated: 4 activities
[2025-08-09 23:10:10.151]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:10.155]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:10.160]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:10.164]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:10.169]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:10.174]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:11.698]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2918.442ms
[2025-08-09 23:10:11.722]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 23:10:11.760]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:11.772]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:11.778]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:11.782]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:11.786]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:11.790]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:11.852]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:11.857]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:11.861]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:11.866]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:11.874]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-09 23:10:11.878]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:11.882]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-09 23:10:11.887]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-09 23:10:11.891]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-09 23:10:11.896]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 23:10:11.901]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_1694433_638903742119010261 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-09 23:10:11.904]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-09 23:10:11.908]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:10:11.912]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 23:10:11.916]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 23:10:11.921]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-09 23:10:11.930]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 23:10:11.935]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 23:10:11.940]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 23:10:12.008]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 23:10:12.028]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 23:10:12.083]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:12.087]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:12.092]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:12.096]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:12.101]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:12.105]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:12.916]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 995.032ms
[2025-08-09 23:10:12.921]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 23:10:13.204]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:13.208]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:13.212]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:13.216]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:10:13.220]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:10:13.224]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:10:13.254]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SearchText(2), HasActivities(1)
[2025-08-09 23:10:13.285]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-09 23:10:13.291]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 23:10:13.295]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.305]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.309]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:13.312]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 23:10:13.317]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 23:10:13.321]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:13.324]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 23:10:13.328]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 23:10:13.332]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_1694433_638903742119010261
[2025-08-09 23:10:13.336]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:13.339]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 23:10:13.343]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 23:10:13.347]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:13.351]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 23:10:13.354]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 23:10:13.358]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 23:10:13.371]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-09 23:10:13.375]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-09 23:10:13.379]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 23:10:13.382]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-09 23:10:13.386]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.390]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.394]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:13.398]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 23:10:13.401]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 23:10:13.405]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:13.409]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 23:10:13.413]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 23:10:13.417]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_28090709_638903741231745657
[2025-08-09 23:10:13.420]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:13.424]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 23:10:13.428]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 23:10:13.431]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:13.435]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 23:10:13.439]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 23:10:13.443]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-09 23:10:13.447]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-09 23:10:13.450]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-09 23:10:13.454]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-09 23:10:13.458]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 23:10:13.462]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 23:10:13.471]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-09 23:10:13.475]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 23:10:13.479]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 23:10:13.482]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 23:10:13.499]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.505]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.511]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.526]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.530]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.534]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.540]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.544]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.548]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 23:10:13.557]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 23:10:13.561]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-09 23:10:13.567]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 23:10:13.571]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-09 23:10:13.576]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-09 23:10:13.580]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-09 23:10:13.585]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 23:10:13.590]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 23:10:13.594]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 23:10:13.598]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 23:10:13.603]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 23:10:13.608]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-09 23:10:13.612]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 23:10:13.616]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-09 23:10:13.620]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 23:10:13.625]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-09 23:10:13.629]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-09 23:10:13.633]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-09 23:10:13.637]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-09 23:10:13.643]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-09 23:10:13.647]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-09 23:10:13.651]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-09 23:10:13.657]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 50.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-09 23:10:13.661]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-09 23:10:13.666]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 72.7%
[2025-08-09 23:10:13.670]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-09 23:10:13.674]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 94.1%
[2025-08-09 23:10:13.678]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-09 23:10:13.682]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 23:10:13.686]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 23:10:13.691]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 23:10:13.694]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-09 23:10:13.698]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-09 23:10:13.702]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-09 23:10:13.706]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-09 23:10:13.710]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-09 23:10:13.714]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-09 23:10:13.718]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-09 23:10:13.722]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-09 23:10:13.726]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 23:10:13.730]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 23:10:13.750]  	[INFO]		[ResourceManager]	Generated memory leak report: 9 alive resources, 0 dead resources
[2025-08-09 23:10:13.763]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 2
[2025-08-09 23:10:13.767]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 2 potential leaks detected
[2025-08-09 23:10:13.771]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-09 23:10:13.775]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-09 23:10:13.779]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 23:10:13.784]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 23:10:13.789]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-09 23:10:13.793]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-09 23:10:13.799]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 23:10:13.805]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 23:10:13.811]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:13.816]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.820]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:13.826]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.830]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:13.835]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.841]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 23:10:13.846]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 23:10:13.850]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 23:10:13.855]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:13.860]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:13.866]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_3728830_638903741325450722
[2025-08-09 23:10:13.871]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 23:10:13.879]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 23:10:13.888]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 23:10:13.898]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:13.930]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:13.941]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_9028608_638903741323767983
[2025-08-09 23:10:13.945]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 23:10:13.950]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 23:10:13.954]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 23:10:13.958]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:13.963]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:13.968]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:13.972]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.977]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:13.981]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.985]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:13.990]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 23:10:13.994]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 23:10:13.998]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 23:10:14.002]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.006]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.010]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:14.014]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_14148614_638903741323917523
[2025-08-09 23:10:14.018]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 23:10:14.022]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 23:10:14.026]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.030]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.034]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:14.038]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_33559471_638903741325610269
[2025-08-09 23:10:14.041]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 23:10:14.045]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 23:10:14.049]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.053]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.057]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:14.060]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.065]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:14.069]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:14.072]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.076]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.080]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 23:10:14.084]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.088]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.091]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 23:10:14.096]  	[DEBUG]		[ResourceManager]	Unregistered resource: NPersonalViewModel_1003178_638903741323656491
[2025-08-09 23:10:14.100]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.103]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 23:10:14.107]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.111]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.114]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 23:10:14.118]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 23:10:14.122]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 23:10:14.126]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.130]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 23:10:14.134]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 23:10:14.137]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.142]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.148]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 23:10:14.152]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.157]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.162]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 23:10:14.166]  	[DEBUG]		[ResourceManager]	Unregistered resource: NotesManagementViewModel_33962670_638903741326124455
[2025-08-09 23:10:14.171]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.183]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 23:10:14.189]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.193]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.198]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 23:10:14.202]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:14.207]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:14.211]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 23:10:14.215]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.220]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 23:10:14.225]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.229]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.234]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 23:10:14.238]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.242]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.245]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 23:10:14.249]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityManagementViewModel_33599791_638903741325827375
[2025-08-09 23:10:14.253]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.257]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 23:10:14.261]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.265]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.269]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 23:10:14.273]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 23:10:14.276]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 23:10:14.280]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:14.289]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.294]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 23:10:14.300]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 23:10:14.304]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 23:10:14.308]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 23:10:14.313]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 23:10:14.317]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.321]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.325]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 23:10:14.329]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.333]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.337]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 23:10:14.341]  	[DEBUG]		[ResourceManager]	Unregistered resource: NewClientViewModel_52610097_638903741325328118
[2025-08-09 23:10:14.383]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.447]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 23:10:14.451]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.456]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.491]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 23:10:14.495]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Unsubscribed from AddedActivities.CollectionChanged event
[2025-08-09 23:10:14.502]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared AddedActivities collection
[2025-08-09 23:10:14.505]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared SearchResults collection
[2025-08-09 23:10:14.510]  	[DEBUG]		[ActivityTypeBaseService]	All caches cleared and recreated
[2025-08-09 23:10:14.514]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared ActivityTypeBaseService caches
[2025-08-09 23:10:14.518]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel disposed successfully
[2025-08-09 23:10:14.522]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:14.525]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.529]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared AddedActivities collection
[2025-08-09 23:10:14.533]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared SearchResults collection
[2025-08-09 23:10:14.537]  	[DEBUG]		[ActivityTypeBaseService]	All caches cleared and recreated
[2025-08-09 23:10:14.540]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared ActivityTypeBaseService caches
[2025-08-09 23:10:14.544]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel disposed successfully
[2025-08-09 23:10:14.548]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 23:10:14.552]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 23:10:14.555]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: MultipleActivitiesDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.559]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: MultipleActivitiesDialogViewModel (0 handlers)
[2025-08-09 23:10:14.562]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.566]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.570]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel disposed
[2025-08-09 23:10:14.573]  	[DEBUG]		[ResourceManager]	Unregistered resource: MultipleActivitiesDialogViewModel_35081372_638903741781712470
[2025-08-09 23:10:14.577]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: MultipleActivitiesDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:10:14.580]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: MultipleActivitiesDialogViewModel (0 handlers)
[2025-08-09 23:10:14.584]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Memory management cleanup completed
[2025-08-09 23:10:14.587]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:10:14.592]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel disposed
[2025-08-09 23:10:14.596]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 11 tracked, 15 disposed, 1 cleanups
[2025-08-09 23:10:14.600]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-09 23:10:14.604]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 23:10:14.607]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-09 23:10:14.611]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 23:10:14.615]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-09 23:10:14.618]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 23:10:14.622]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-09 23:10:14.628]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 23:10:14.632]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-09 23:10:14.636]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-09 23:10:14.640]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-09 23:10:14.643]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-09 23:10:14.650]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-09 23:10:14.654]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-09 23:10:14.657]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 23:10:14.661]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-09 23:10:14.665]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-09 23:10:14.669]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-09 23:10:14.673]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-09 23:10:14.676]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 23:10:14 ===
