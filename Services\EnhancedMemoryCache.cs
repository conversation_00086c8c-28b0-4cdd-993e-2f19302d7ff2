using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using UFU2.Common;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Enhanced memory cache with LRU eviction, size limits, and memory pressure handling.
    /// Implements Day 3 Task 3.3 requirements for optimized cache memory usage.
    /// </summary>
    public class EnhancedMemoryCache : IDisposable
    {
        #region Private Fields

        private readonly MemoryCache _cache;
        private readonly ConcurrentDictionary<string, CacheEntry> _entries;
        private readonly ConcurrentDictionary<string, DateTime> _accessTimes;
        private readonly Timer _evictionTimer;
        private readonly Timer _memoryPressureTimer;
        private readonly SemaphoreSlim _evictionLock;
        private readonly string _cacheName;
        private bool _disposed = false;

        // Configuration
        private readonly long _maxMemoryBytes;
        private readonly int _maxItemCount;
        private readonly TimeSpan _defaultExpiration;
        private readonly double _evictionPercentage;
        private readonly TimeSpan _evictionInterval;
        private readonly TimeSpan _memoryPressureInterval;

        // Statistics
        private long _totalHits = 0;
        private long _totalMisses = 0;
        private long _totalEvictions = 0;
        private long _currentMemoryUsage = 0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the EnhancedMemoryCache.
        /// </summary>
        /// <param name="cacheName">Name of the cache for identification</param>
        /// <param name="maxMemoryMB">Maximum memory usage in MB (default: 50MB)</param>
        /// <param name="maxItemCount">Maximum number of items (default: 1000)</param>
        /// <param name="defaultExpirationMinutes">Default expiration time in minutes (default: 60)</param>
        public EnhancedMemoryCache(string cacheName, int maxMemoryMB = 50, int maxItemCount = 1000, int defaultExpirationMinutes = 60)
        {
            _cacheName = cacheName ?? throw new ArgumentNullException(nameof(cacheName));
            _maxMemoryBytes = maxMemoryMB * 1024L * 1024L; // Convert MB to bytes
            _maxItemCount = maxItemCount;
            _defaultExpiration = TimeSpan.FromMinutes(defaultExpirationMinutes);
            _evictionPercentage = 0.25; // Evict 25% when limits are reached
            _evictionInterval = TimeSpan.FromMinutes(5); // Check every 5 minutes
            _memoryPressureInterval = TimeSpan.FromMinutes(2); // Check memory pressure every 2 minutes

            // Initialize cache with options
            var options = new MemoryCacheOptions
            {
                SizeLimit = maxItemCount,
                CompactionPercentage = _evictionPercentage
            };
            _cache = new MemoryCache(options);

            _entries = new ConcurrentDictionary<string, CacheEntry>();
            _accessTimes = new ConcurrentDictionary<string, DateTime>();
            _evictionLock = new SemaphoreSlim(1, 1);

            // Start eviction timer
            _evictionTimer = new Timer(PerformEviction, null, _evictionInterval, _evictionInterval);

            // Start memory pressure monitoring timer
            _memoryPressureTimer = new Timer(CheckMemoryPressure, null, _memoryPressureInterval, _memoryPressureInterval);

            LoggingService.LogDebug($"EnhancedMemoryCache '{_cacheName}' initialized with {maxMemoryMB}MB limit, {maxItemCount} items max", "EnhancedMemoryCache");
        }

        #endregion

        #region Cache Operations

        /// <summary>
        /// Gets a value from the cache.
        /// </summary>
        /// <typeparam name="T">Type of the cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached value or default if not found</returns>
        public T? Get<T>(string key)
        {
            if (string.IsNullOrEmpty(key))
                return default(T);

            try
            {
                if (_cache.TryGetValue(key, out var value))
                {
                    // Update access time for LRU tracking
                    _accessTimes[key] = DateTime.UtcNow;
                    Interlocked.Increment(ref _totalHits);

                    if (value is T typedValue)
                    {
                        return typedValue;
                    }
                    else if (value is string serializedValue && typeof(T) != typeof(string))
                    {
                        // Attempt to deserialize
                        try
                        {
                            return JsonSerializer.Deserialize<T>(serializedValue);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogWarning($"Failed to deserialize cached value for key '{key}': {ex.Message}", "EnhancedMemoryCache");
                            Remove(key); // Remove corrupted entry
                            return default(T);
                        }
                    }
                }

                Interlocked.Increment(ref _totalMisses);
                return default(T);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting cache value for key '{key}': {ex.Message}", "EnhancedMemoryCache");
                Interlocked.Increment(ref _totalMisses);
                return default(T);
            }
        }

        /// <summary>
        /// Sets a value in the cache.
        /// </summary>
        /// <typeparam name="T">Type of the value to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="expiration">Optional expiration time (uses default if not specified)</param>
        public void Set<T>(string key, T value, TimeSpan? expiration = null)
        {
            if (string.IsNullOrEmpty(key) || value == null)
                return;

            try
            {
                var expirationTime = expiration ?? _defaultExpiration;
                var serializedValue = SerializeValue(value);
                var estimatedSize = EstimateSize(serializedValue);

                // Check if adding this item would exceed limits
                if (ShouldEvictBeforeAdd(estimatedSize))
                {
                    Task.Run(async () => await PerformLRUEvictionAsync());
                }

                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expirationTime,
                    Size = 1, // Each item counts as 1 for the size limit
                    PostEvictionCallbacks = { new PostEvictionCallbackRegistration
                    {
                        EvictionCallback = OnItemEvicted
                    }}
                };

                _cache.Set(key, serializedValue, options);

                // Track entry metadata
                var entry = new CacheEntry
                {
                    Key = key,
                    EstimatedSize = estimatedSize,
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.Add(expirationTime)
                };

                _entries[key] = entry;
                _accessTimes[key] = DateTime.UtcNow;
                Interlocked.Add(ref _currentMemoryUsage, estimatedSize);

                LoggingService.LogDebug($"Cached item '{key}' in '{_cacheName}' (size: {estimatedSize} bytes)", "EnhancedMemoryCache");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting cache value for key '{key}': {ex.Message}", "EnhancedMemoryCache");
            }
        }

        /// <summary>
        /// Removes a value from the cache.
        /// </summary>
        /// <param name="key">Cache key to remove</param>
        public void Remove(string key)
        {
            if (string.IsNullOrEmpty(key))
                return;

            try
            {
                _cache.Remove(key);
                
                if (_entries.TryRemove(key, out var entry))
                {
                    Interlocked.Add(ref _currentMemoryUsage, -entry.EstimatedSize);
                }
                
                _accessTimes.TryRemove(key, out _);

                LoggingService.LogDebug($"Removed item '{key}' from cache '{_cacheName}'", "EnhancedMemoryCache");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing cache value for key '{key}': {ex.Message}", "EnhancedMemoryCache");
            }
        }

        /// <summary>
        /// Clears all items from the cache.
        /// </summary>
        public void Clear()
        {
            try
            {
                _cache.Dispose();
                _entries.Clear();
                _accessTimes.Clear();
                Interlocked.Exchange(ref _currentMemoryUsage, 0);

                LoggingService.LogDebug($"Cleared all items from cache '{_cacheName}'", "EnhancedMemoryCache");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing cache '{_cacheName}': {ex.Message}", "EnhancedMemoryCache");
            }
        }

        #endregion

        #region Cache Statistics

        /// <summary>
        /// Gets cache statistics for monitoring.
        /// </summary>
        /// <returns>Dictionary containing cache statistics</returns>
        public Dictionary<string, object> GetStatistics()
        {
            var totalRequests = _totalHits + _totalMisses;
            var hitRatio = totalRequests > 0 ? (double)_totalHits / totalRequests : 0.0;

            return new Dictionary<string, object>
            {
                ["CacheName"] = _cacheName,
                ["ItemCount"] = _entries.Count,
                ["MemoryUsageBytes"] = _currentMemoryUsage,
                ["MemoryUsageMB"] = _currentMemoryUsage / 1024.0 / 1024.0,
                ["MaxMemoryBytes"] = _maxMemoryBytes,
                ["MaxItemCount"] = _maxItemCount,
                ["HitRatio"] = hitRatio,
                ["TotalHits"] = _totalHits,
                ["TotalMisses"] = _totalMisses,
                ["TotalEvictions"] = _totalEvictions,
                ["MemoryUtilization"] = (double)_currentMemoryUsage / _maxMemoryBytes,
                ["ItemUtilization"] = (double)_entries.Count / _maxItemCount
            };
        }

        /// <summary>
        /// Gets cache health information.
        /// </summary>
        /// <returns>Cache health information</returns>
        public CacheHealthInfo GetHealthInfo()
        {
            var stats = GetStatistics();
            var hitRatio = (double)stats["HitRatio"];
            var memoryUtilization = (double)stats["MemoryUtilization"];
            var itemUtilization = (double)stats["ItemUtilization"];

            var isHealthy = hitRatio >= 0.5 && memoryUtilization < 0.9 && itemUtilization < 0.9;
            var healthStatus = isHealthy ? "Healthy" : 
                $"Hit ratio: {hitRatio:P1}, Memory: {memoryUtilization:P1}, Items: {itemUtilization:P1}";

            return new CacheHealthInfo
            {
                HitRatio = hitRatio,
                ItemCount = (int)stats["ItemCount"],
                MemoryUsageBytes = (long)stats["MemoryUsageBytes"],
                IsHealthy = isHealthy,
                HealthStatus = healthStatus
            };
        }

        #endregion

        #region LRU Eviction and Memory Management

        /// <summary>
        /// Determines if eviction should occur before adding a new item.
        /// </summary>
        /// <param name="newItemSize">Size of the new item to be added</param>
        /// <returns>True if eviction should occur</returns>
        private bool ShouldEvictBeforeAdd(long newItemSize)
        {
            var currentMemory = _currentMemoryUsage;
            var currentItems = _entries.Count;

            // Check memory limit
            if (currentMemory + newItemSize > _maxMemoryBytes)
                return true;

            // Check item count limit
            if (currentItems >= _maxItemCount)
                return true;

            // Check if we're approaching limits (80% threshold)
            if (currentMemory + newItemSize > _maxMemoryBytes * 0.8)
                return true;

            if (currentItems >= _maxItemCount * 0.8)
                return true;

            return false;
        }

        /// <summary>
        /// Performs LRU eviction to free up space.
        /// </summary>
        private async Task PerformLRUEvictionAsync()
        {
            await _evictionLock.WaitAsync();
            try
            {
                var itemsToEvict = CalculateItemsToEvict();
                if (itemsToEvict <= 0)
                    return;

                LoggingService.LogDebug($"Performing LRU eviction of {itemsToEvict} items from cache '{_cacheName}'", "EnhancedMemoryCache");

                // Get items sorted by last access time (oldest first)
                var itemsToRemove = _accessTimes
                    .OrderBy(kvp => kvp.Value)
                    .Take(itemsToEvict)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in itemsToRemove)
                {
                    Remove(key);
                    Interlocked.Increment(ref _totalEvictions);
                }

                LoggingService.LogInfo($"Evicted {itemsToRemove.Count} items from cache '{_cacheName}' using LRU policy", "EnhancedMemoryCache");
            }
            finally
            {
                _evictionLock.Release();
            }
        }

        /// <summary>
        /// Calculates the number of items to evict based on current usage.
        /// </summary>
        /// <returns>Number of items to evict</returns>
        private int CalculateItemsToEvict()
        {
            var currentItems = _entries.Count;
            var currentMemory = _currentMemoryUsage;

            // Calculate eviction based on memory pressure
            var memoryEvictionCount = 0;
            if (currentMemory > _maxMemoryBytes * 0.8)
            {
                memoryEvictionCount = (int)(currentItems * _evictionPercentage);
            }

            // Calculate eviction based on item count
            var itemEvictionCount = 0;
            if (currentItems > _maxItemCount * 0.8)
            {
                itemEvictionCount = (int)(currentItems * _evictionPercentage);
            }

            return Math.Max(memoryEvictionCount, itemEvictionCount);
        }

        /// <summary>
        /// Performs eviction (timer callback).
        /// </summary>
        /// <param name="state">Timer state (not used)</param>
        private void PerformEviction(object? state)
        {
            try
            {
                // Remove expired items first
                RemoveExpiredItems();

                // Perform LRU eviction if needed
                if (ShouldEvictBeforeAdd(0))
                {
                    Task.Run(async () => await PerformLRUEvictionAsync());
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during eviction for cache '{_cacheName}': {ex.Message}", "EnhancedMemoryCache");
            }
        }

        /// <summary>
        /// Removes expired items from the cache.
        /// </summary>
        private void RemoveExpiredItems()
        {
            var now = DateTime.UtcNow;
            var expiredKeys = _entries
                .Where(kvp => kvp.Value.ExpiresAt <= now)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                Remove(key);
                Interlocked.Increment(ref _totalEvictions);
            }

            if (expiredKeys.Any())
            {
                LoggingService.LogDebug($"Removed {expiredKeys.Count} expired items from cache '{_cacheName}'", "EnhancedMemoryCache");
            }
        }

        /// <summary>
        /// Checks for memory pressure and performs aggressive eviction if needed (timer callback).
        /// </summary>
        /// <param name="state">Timer state (not used)</param>
        private void CheckMemoryPressure(object? state)
        {
            try
            {
                var memoryUtilization = (double)_currentMemoryUsage / _maxMemoryBytes;

                // If memory usage is very high (>90%), perform aggressive eviction
                if (memoryUtilization > 0.9)
                {
                    LoggingService.LogWarning($"High memory pressure detected in cache '{_cacheName}' ({memoryUtilization:P1}), performing aggressive eviction", "EnhancedMemoryCache");

                    Task.Run(async () =>
                    {
                        await _evictionLock.WaitAsync();
                        try
                        {
                            // Evict 50% of items under high memory pressure
                            var itemsToEvict = _entries.Count / 2;
                            var itemsToRemove = _accessTimes
                                .OrderBy(kvp => kvp.Value)
                                .Take(itemsToEvict)
                                .Select(kvp => kvp.Key)
                                .ToList();

                            foreach (var key in itemsToRemove)
                            {
                                Remove(key);
                                Interlocked.Increment(ref _totalEvictions);
                            }

                            LoggingService.LogWarning($"Aggressive eviction completed: removed {itemsToRemove.Count} items from cache '{_cacheName}'", "EnhancedMemoryCache");
                        }
                        finally
                        {
                            _evictionLock.Release();
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during memory pressure check for cache '{_cacheName}': {ex.Message}", "EnhancedMemoryCache");
            }
        }

        /// <summary>
        /// Callback for when an item is evicted from the underlying MemoryCache.
        /// </summary>
        /// <param name="key">Evicted key</param>
        /// <param name="value">Evicted value</param>
        /// <param name="reason">Eviction reason</param>
        /// <param name="state">State object</param>
        private void OnItemEvicted(object key, object? value, EvictionReason reason, object? state)
        {
            if (key is string stringKey)
            {
                if (_entries.TryRemove(stringKey, out var entry))
                {
                    Interlocked.Add(ref _currentMemoryUsage, -entry.EstimatedSize);
                }

                _accessTimes.TryRemove(stringKey, out _);

                if (reason != EvictionReason.Replaced)
                {
                    Interlocked.Increment(ref _totalEvictions);
                }

                LoggingService.LogDebug($"Item '{stringKey}' evicted from cache '{_cacheName}' (reason: {reason})", "EnhancedMemoryCache");
            }
        }

        #endregion

        #region Serialization and Size Estimation

        /// <summary>
        /// Serializes a value for caching with optimized serialization.
        /// </summary>
        /// <typeparam name="T">Type of the value</typeparam>
        /// <param name="value">Value to serialize</param>
        /// <returns>Serialized value</returns>
        private object SerializeValue<T>(T value)
        {
            if (value == null)
                return string.Empty;

            // For simple types, store directly
            if (IsSimpleType(typeof(T)))
            {
                return value;
            }

            // For complex types, use JSON serialization with optimized options
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = false,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                };

                return JsonSerializer.Serialize(value, options);
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to serialize value of type {typeof(T).Name}: {ex.Message}", "EnhancedMemoryCache");
                return value?.ToString() ?? string.Empty;
            }
        }

        /// <summary>
        /// Determines if a type is a simple type that doesn't need serialization.
        /// </summary>
        /// <param name="type">Type to check</param>
        /// <returns>True if the type is simple</returns>
        private bool IsSimpleType(Type type)
        {
            return type.IsPrimitive ||
                   type == typeof(string) ||
                   type == typeof(DateTime) ||
                   type == typeof(DateTimeOffset) ||
                   type == typeof(TimeSpan) ||
                   type == typeof(Guid) ||
                   type == typeof(decimal) ||
                   (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>) &&
                    IsSimpleType(type.GetGenericArguments()[0]));
        }

        /// <summary>
        /// Estimates the memory size of a cached value.
        /// </summary>
        /// <param name="value">Value to estimate</param>
        /// <returns>Estimated size in bytes</returns>
        private long EstimateSize(object value)
        {
            if (value == null)
                return 0;

            if (value is string str)
            {
                return str.Length * 2; // Unicode characters are 2 bytes each
            }

            if (value is byte[] bytes)
            {
                return bytes.Length;
            }

            // For other types, use a rough estimation
            var type = value.GetType();
            if (type.IsPrimitive)
            {
                return type == typeof(bool) || type == typeof(byte) || type == typeof(sbyte) ? 1 :
                       type == typeof(short) || type == typeof(ushort) || type == typeof(char) ? 2 :
                       type == typeof(int) || type == typeof(uint) || type == typeof(float) ? 4 :
                       type == typeof(long) || type == typeof(ulong) || type == typeof(double) ? 8 : 16;
            }

            // For complex objects, estimate based on JSON serialization length
            if (value is string jsonString)
            {
                return jsonString.Length * 2;
            }

            // Default estimation for unknown types
            return 1024; // 1KB default
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the enhanced memory cache and its resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;

                try
                {
                    _evictionTimer?.Dispose();
                    _memoryPressureTimer?.Dispose();
                    _cache?.Dispose();
                    _evictionLock?.Dispose();

                    LoggingService.LogDebug($"EnhancedMemoryCache '{_cacheName}' disposed", "EnhancedMemoryCache");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error disposing EnhancedMemoryCache '{_cacheName}': {ex.Message}", "EnhancedMemoryCache");
                }
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Represents a cache entry with metadata.
    /// </summary>
    internal class CacheEntry
    {
        public string Key { get; set; } = string.Empty;
        public long EstimatedSize { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    #endregion
}
