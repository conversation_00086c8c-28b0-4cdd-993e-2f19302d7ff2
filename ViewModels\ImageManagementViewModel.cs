using System;
using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Common.Utilities;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the ImageManagementDialog providing MVVM data binding and state management.
    /// Manages image loading states, visibility of UI elements, and crop guide functionality.
    /// Follows UFU2 MVVM patterns with proper error handling and logging integration.
    /// </summary>
    public class ImageManagementViewModel : BaseViewModel
    {
        #region Private Fields

        private bool _isImageLoaded = false;
        private bool _isLoading = false;
        private BitmapImage? _currentImage;
        private BitmapImage? _originalImage; // Backup of original image for reset functionality
        private double _zoomPercentage = 100.0;
        private double _rotationAngle = 0.0;
        private Rect _cropRectangle = new Rect(133, 15, 254, 290);
        private bool _hasBeenCropped = false; // Track if image has been cropped
        private double _imageOffsetX = 0.0; // X offset for image dragging
        private double _imageOffsetY = 0.0; // Y offset for image dragging
        private double _postCropVisualScale = 1.0; // Visual scale factor for post-crop fitting (maintains 100% zoom)

        // Image Drag State Management Fields
        private bool _isDragging = false;
        private Point _lastMousePosition = new Point(0, 0);
        private Point _dragStartPosition = new Point(0, 0);
        private double _dragStartOffsetX = 0.0;
        private double _dragStartOffsetY = 0.0;
        private DateTime _lastDragUpdate = DateTime.MinValue;
        private DateTime _lastBoundaryLogUpdate = DateTime.MinValue;
        private const int DragThrottleMs = 16; // ~60 FPS for smooth dragging
        private const int BoundaryLogThrottleMs = 500; // Log boundary calculations every 500ms max
        private const double BaseMinVisibleMargin = 20.0; // Base minimum pixels visible on each side
        private const double MaxMinVisibleMargin = 50.0; // Maximum minimum pixels visible on each side

        // WYSIWYG Backend Simulation Fields
        private PreviewBackendSynchronizer? _backendSynchronizer;
        private CoordinateMapper? _coordinateMapper;
        private bool _isWysiwygEnabled = true;

        // Performance monitoring fields
        private Stopwatch? _sliderOperationStopwatch;
        private DateTime _lastSliderUpdate = DateTime.MinValue;
        private const int SliderDebounceMs = 50; // Minimum time between performance logs

        // Error handling constants
        private const double MinZoomPercentage = 25.0;
        private const double MaxZoomPercentage = 400.0;
        private const double DefaultZoomPercentage = 100.0;
        private const double DefaultRotationAngle = 0.0;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets whether an image is currently loaded.
        /// Controls the visibility of image-dependent UI elements.
        /// </summary>
        public bool IsImageLoaded
        {
            get => _isImageLoaded;
            set
            {
                if (SetProperty(ref _isImageLoaded, value))
                {
                    LoggingService.LogDebugLazy(() => $"Image loaded state changed to: {value}", "ImageManagementViewModel");

                    // Notify related properties that depend on image loaded state
                    OnPropertyChanged(nameof(NoImagePlaceholderVisibility));
                    OnPropertyChanged(nameof(CropGuideVisibility));
                    OnPropertyChanged(nameof(CanSave));
                    OnPropertyChanged(nameof(CanReset));
                    OnPropertyChanged(nameof(CanCrop));
                    OnPropertyChanged(nameof(HasChanges));
                }
            }
        }

        /// <summary>
        /// Gets or sets whether an image loading operation is in progress.
        /// Controls the visibility of the loading indicator.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    LoggingService.LogDebug($"Loading state changed to: {value}", "ImageManagementViewModel");
                    
                    // Notify loading indicator visibility property
                    OnPropertyChanged(nameof(LoadingIndicatorVisibility));
                }
            }
        }

        /// <summary>
        /// Gets or sets the current loaded image.
        /// Automatically updates IsImageLoaded when set.
        /// </summary>
        public BitmapImage? CurrentImage
        {
            get => _currentImage;
            set
            {
                if (SetProperty(ref _currentImage, value))
                {
                    // Update image loaded state based on whether we have an image
                    IsImageLoaded = value != null;
                    LoggingService.LogDebug($"Current image updated - HasImage: {IsImageLoaded}", "ImageManagementViewModel");

                    // Notify visual feedback properties
                    OnPropertyChanged(nameof(CanReset));
                    OnPropertyChanged(nameof(CanSave));
                    OnPropertyChanged(nameof(CanCrop));
                    OnPropertyChanged(nameof(HasChanges));
                }
            }
        }

        /// <summary>
        /// Gets the visibility of the "No Image Placeholder" element.
        /// Visible when no image is loaded, collapsed when an image is present.
        /// </summary>
        public Visibility NoImagePlaceholderVisibility
        {
            get => IsImageLoaded ? Visibility.Collapsed : Visibility.Visible;
        }

        /// <summary>
        /// Gets the visibility of the crop guide rectangle.
        /// Hidden when no image is loaded, visible when an image is present.
        /// </summary>
        public Visibility CropGuideVisibility
        {
            get => IsImageLoaded ? Visibility.Visible : Visibility.Hidden;
        }

        /// <summary>
        /// Gets the visibility of the loading indicator.
        /// Visible during loading operations, collapsed otherwise.
        /// </summary>
        public Visibility LoadingIndicatorVisibility
        {
            get => IsLoading ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// Gets or sets the zoom percentage for image scaling.
        /// Valid range is 25-400%. Values outside this range are automatically clamped.
        /// Includes comprehensive error handling, validation, and performance monitoring.
        /// </summary>
        public double ZoomPercentage
        {
            get => _zoomPercentage;
            set
            {
                try
                {
                    // Start performance monitoring for slider operations
                    StartSliderPerformanceMonitoring("ZoomPercentage");

                    // Validate input value
                    if (double.IsNaN(value) || double.IsInfinity(value))
                    {
                        LoggingService.LogWarning($"Invalid zoom percentage value received: {value}. Using default value.", "ImageManagementViewModel");
                        ErrorManager.LogException(new ArgumentException($"Invalid zoom percentage: {value}"), LogLevel.Warning, "ImageManagementViewModel");
                        value = DefaultZoomPercentage;
                    }

                    // Clamp value to valid range with detailed logging for out-of-range values
                    var originalValue = value;
                    var clampedValue = Math.Max(MinZoomPercentage, Math.Min(MaxZoomPercentage, value));

                    // Apply integer constraint - restrict zoom to integer percentages only
                    var integerConstrainedValue = Math.Round(clampedValue);

                    if (Math.Abs(originalValue - clampedValue) > 0.001) // Check if clamping occurred
                    {
                        LoggingService.LogInfo($"Zoom percentage clamped from {originalValue:F1}% to {clampedValue:F1}% (valid range: {MinZoomPercentage}-{MaxZoomPercentage}%)", "ImageManagementViewModel");
                    }

                    if (Math.Abs(clampedValue - integerConstrainedValue) > 0.001) // Check if integer constraint was applied
                    {
                        LoggingService.LogDebug($"Zoom percentage constrained to integer value from {clampedValue:F1}% to {integerConstrainedValue:F0}%", "ImageManagementViewModel");
                    }

                    clampedValue = integerConstrainedValue;

                    if (SetProperty(ref _zoomPercentage, clampedValue))
                    {
                        // Log successful property change
                        LogSliderOperation("ZoomPercentage", clampedValue, "%");
                        
                        try
                        {
                            // Notify dependent computed properties with error handling
                            OnPropertyChanged(nameof(ZoomPercentageText));
                            OnPropertyChanged(nameof(ZoomScale));
                            OnPropertyChanged(nameof(HasChanges));
                            OnPropertyChanged(nameof(CanReset));
                        }
                        catch (Exception dependentEx)
                        {
                            LoggingService.LogError($"Error updating dependent properties for ZoomPercentage: {dependentEx.Message}", "ImageManagementViewModel");
                            ErrorManager.LogException(dependentEx, LogLevel.Error, "ImageManagementViewModel");
                        }
                    }

                    // Complete performance monitoring
                    CompleteSliderPerformanceMonitoring("ZoomPercentage");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Critical error in ZoomPercentage setter: {ex.Message}", "ImageManagementViewModel");
                    ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء تعديل نسبة التكبير. سيتم استخدام القيمة الافتراضية.", "خطأ في التكبير", LogLevel.Error, "ImageManagementViewModel");
                    
                    // Graceful degradation - reset to safe default value
                    try
                    {
                        if (SetProperty(ref _zoomPercentage, DefaultZoomPercentage))
                        {
                            OnPropertyChanged(nameof(ZoomPercentageText));
                            OnPropertyChanged(nameof(ZoomScale));
                        }
                    }
                    catch (Exception fallbackEx)
                    {
                        LoggingService.LogError($"Failed to reset zoom percentage to default: {fallbackEx.Message}", "ImageManagementViewModel");
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets the rotation angle in degrees.
        /// Values are automatically normalized to -180 to +180 degree range for bidirectional rotation support.
        /// Includes comprehensive error handling, validation, and performance monitoring.
        /// </summary>
        public double RotationAngle
        {
            get => _rotationAngle;
            set
            {
                try
                {
                    // Start performance monitoring for slider operations
                    StartSliderPerformanceMonitoring("RotationAngle");

                    // Validate input value
                    if (double.IsNaN(value) || double.IsInfinity(value))
                    {
                        LoggingService.LogWarning($"Invalid rotation angle value received: {value}. Using default value.", "ImageManagementViewModel");
                        ErrorManager.LogException(new ArgumentException($"Invalid rotation angle: {value}"), LogLevel.Warning, "ImageManagementViewModel");
                        value = DefaultRotationAngle;
                    }

                    // Handle extreme values gracefully
                    var originalValue = value;
                    if (Math.Abs(value) > 3600) // More than 10 full rotations
                    {
                        LoggingService.LogInfo($"Extreme rotation angle detected: {originalValue:F1}°. Normalizing to bidirectional range.", "ImageManagementViewModel");
                    }

                    // Validate rotation bounds for bidirectional support (-180° to +180°)
                    if (value < -180 || value > 180)
                    {
                        // Normalize angle to -180 to +180 degree range with error handling
                        double normalizedValue;
                        try
                        {
                            // Convert to -180 to +180 range
                            normalizedValue = value;
                            while (normalizedValue > 180) normalizedValue -= 360;
                            while (normalizedValue < -180) normalizedValue += 360;
                        }
                        catch (Exception normalizationEx)
                        {
                            LoggingService.LogError($"Error normalizing rotation angle {value}: {normalizationEx.Message}", "ImageManagementViewModel");
                            ErrorManager.ShowUserErrorToast("حدث خطأ أثناء تطبيع زاوية الدوران. سيتم استخدام القيمة الافتراضية.", "خطأ في الدوران");
                            normalizedValue = DefaultRotationAngle;
                        }
                        value = normalizedValue;
                    }

                    // Apply integer constraint - restrict rotation to integer degrees only
                    var integerConstrainedValue = Math.Round(value);

                    if (Math.Abs(value - integerConstrainedValue) > 0.001) // Check if integer constraint was applied
                    {
                        LoggingService.LogDebug($"Rotation angle constrained to integer value from {value:F1}° to {integerConstrainedValue:F0}°", "ImageManagementViewModel");
                    }

                    value = integerConstrainedValue;

                    if (SetProperty(ref _rotationAngle, value))
                    {
                        // Log successful property change
                        LogSliderOperation("RotationAngle", value, "°");

                        try
                        {
                            // Notify dependent computed property with error handling
                            OnPropertyChanged(nameof(RotationAngleText));
                            OnPropertyChanged(nameof(HasChanges));
                            OnPropertyChanged(nameof(CanReset));
                        }
                        catch (Exception dependentEx)
                        {
                            LoggingService.LogError($"Error updating dependent properties for RotationAngle: {dependentEx.Message}", "ImageManagementViewModel");
                            ErrorManager.LogException(dependentEx, LogLevel.Error, "ImageManagementViewModel");
                        }
                    }

                    // Complete performance monitoring
                    CompleteSliderPerformanceMonitoring("RotationAngle");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Critical error in RotationAngle setter: {ex.Message}", "ImageManagementViewModel");
                    ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء تعديل زاوية الدوران. سيتم استخدام القيمة الافتراضية.", "خطأ في الدوران", LogLevel.Error, "ImageManagementViewModel");

                    // Graceful degradation - reset to safe default value
                    try
                    {
                        if (SetProperty(ref _rotationAngle, DefaultRotationAngle))
                        {
                            OnPropertyChanged(nameof(RotationAngleText));
                        }
                    }
                    catch (Exception fallbackEx)
                    {
                        LoggingService.LogError($"Failed to reset rotation angle to default: {fallbackEx.Message}", "ImageManagementViewModel");
                    }
                }
            }
        }

        /// <summary>
        /// Gets the formatted zoom percentage text for display.
        /// Returns the zoom percentage with "%" suffix (e.g., "100%").
        /// Includes error handling for formatting operations.
        /// </summary>
        public string ZoomPercentageText
        {
            get
            {
                try
                {
                    return $"{ZoomPercentage:F0}%";
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error formatting zoom percentage text: {ex.Message}", "ImageManagementViewModel");
                    ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
                    return "100%"; // Safe fallback
                }
            }
        }

        /// <summary>
        /// Gets the formatted rotation angle text for display.
        /// Returns the rotation angle with "°" suffix and direction indicator (e.g., "+45°", "-30°").
        /// Includes error handling for formatting operations.
        /// </summary>
        public string RotationAngleText
        {
            get
            {
                try
                {
                    var angle = RotationAngle;
                    if (angle == 0)
                        return "0°";
                    else if (angle > 0)
                        return $"+{angle:F0}°";
                    else
                        return $"{angle:F0}°"; // Negative sign already included
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error formatting rotation angle text: {ex.Message}", "ImageManagementViewModel");
                    ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
                    return "0°"; // Safe fallback
                }
            }
        }

        /// <summary>
        /// Gets the zoom scale factor for transform binding.
        /// Converts percentage to decimal scale (e.g., 100% = 1.0, 200% = 2.0).
        /// Includes error handling and validation for transform operations.
        /// </summary>
        public double ZoomScale
        {
            get
            {
                try
                {
                    var scale = ZoomPercentage / 100.0;
                    
                    // Validate the computed scale value
                    if (double.IsNaN(scale) || double.IsInfinity(scale) || scale <= 0)
                    {
                        LoggingService.LogWarning($"Invalid zoom scale computed: {scale}. Using default scale.", "ImageManagementViewModel");
                        return 1.0; // Default scale
                    }
                    
                    return scale;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error computing zoom scale: {ex.Message}", "ImageManagementViewModel");
                    ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
                    return 1.0; // Safe fallback scale
                }
            }
        }

        /// <summary>
        /// Gets or sets the crop rectangle bounds for the interactive crop area.
        /// Represents the position and size of the crop selection in UI coordinates.
        /// Maintains proper aspect ratio for profile images (127:145).
        /// </summary>
        public Rect CropRectangle
        {
            get => _cropRectangle;
            set
            {
                if (SetProperty(ref _cropRectangle, value))
                {
                    LoggingService.LogDebug($"Crop rectangle updated: {value}", "ImageManagementViewModel");

                    // Validate the crop rectangle
                    try
                    {
                        ValidateCropRectangle();
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error validating crop rectangle: {ex.Message}", "ImageManagementViewModel");
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets whether the image has been cropped.
        /// Used to track crop state for automatic cropping workflow.
        /// </summary>
        public bool HasBeenCropped
        {
            get => _hasBeenCropped;
            set
            {
                if (SetProperty(ref _hasBeenCropped, value))
                {
                    LoggingService.LogDebug($"HasBeenCropped state changed to: {value}", "ImageManagementViewModel");

                    // Notify visual feedback properties
                    OnPropertyChanged(nameof(CanReset));
                    OnPropertyChanged(nameof(CanSave));
                    OnPropertyChanged(nameof(CanCrop));
                    OnPropertyChanged(nameof(HasChanges));
                }
            }
        }

        /// <summary>
        /// Gets or sets the original image backup for reset functionality.
        /// Preserves the original loaded image before any cropping operations.
        /// </summary>
        public BitmapImage? OriginalImage
        {
            get => _originalImage;
            set
            {
                if (SetProperty(ref _originalImage, value))
                {
                    LoggingService.LogDebug($"OriginalImage backup updated: {(value != null ? "Set" : "Cleared")}", "ImageManagementViewModel");

                    // Notify related properties that depend on original image state
                    OnPropertyChanged(nameof(CanReset));
                    OnPropertyChanged(nameof(CanSave));
                    OnPropertyChanged(nameof(HasChanges));
                }
            }
        }

        /// <summary>
        /// Gets whether the reset operation is available.
        /// True when an original image backup exists and changes have been made (crop, zoom, or rotation).
        /// </summary>
        public bool CanReset
        {
            get => OriginalImage != null && IsImageLoaded && HasChanges;
        }

        /// <summary>
        /// Gets whether the crop operation is available.
        /// True when an image is loaded and not currently loading.
        /// Disabled after successful cropping until reset is performed.
        /// </summary>
        public bool CanCrop
        {
            get => IsImageLoaded && !IsLoading && !HasBeenCropped;
        }

        /// <summary>
        /// Gets whether the save operation is available.
        /// True only after the user has explicitly performed a crop operation.
        /// </summary>
        public bool CanSave
        {
            get => IsImageLoaded && HasBeenCropped;
        }

        /// <summary>
        /// Gets whether any changes have been made to the image.
        /// True when crop, zoom, rotation, or drag changes have occurred.
        /// </summary>
        public bool HasChanges
        {
            get => HasBeenCropped ||
                   Math.Abs(ZoomPercentage - DefaultZoomPercentage) > 0.1 ||
                   Math.Abs(RotationAngle - DefaultRotationAngle) > 0.1 ||
                   Math.Abs(ImageOffsetX) > 0.1 ||
                   Math.Abs(ImageOffsetY) > 0.1;
        }

        #endregion

        #region Zoom Commands

        /// <summary>
        /// Command to zoom in by the specified step size.
        /// Uses RelayCommand pattern with error handling and validation.
        /// </summary>
        public RelayCommand ZoomInCommand { get; private set; }

        /// <summary>
        /// Command to zoom out by the specified step size.
        /// Uses RelayCommand pattern with error handling and validation.
        /// </summary>
        public RelayCommand ZoomOutCommand { get; private set; }

        /// <summary>
        /// Gets or sets the zoom step size in percentage for command-based zoom.
        /// Default is 10% for smooth incremental zoom operations.
        /// </summary>
        public double ZoomStepSize { get; set; } = 10.0;

        #endregion

        #region Rotation Commands

        /// <summary>
        /// Command to rotate the image clockwise by the specified step size.
        /// Uses RelayCommand pattern with error handling and validation.
        /// </summary>
        public RelayCommand RotateClockwiseCommand { get; private set; }

        /// <summary>
        /// Command to rotate the image counterclockwise by the specified step size.
        /// Uses RelayCommand pattern with error handling and validation.
        /// </summary>
        public RelayCommand RotateCounterclockwiseCommand { get; private set; }

        /// <summary>
        /// Gets or sets the rotation step size in degrees for command-based rotation.
        /// Default is 15 degrees for smooth incremental rotation.
        /// </summary>
        public double RotationStepSize { get; set; } = 15.0;

        #endregion

        #region Image Offset Properties

        /// <summary>
        /// Gets or sets the X offset for image dragging within the preview container.
        /// Used for positioning the image within the fixed crop rectangle.
        /// </summary>
        public double ImageOffsetX
        {
            get => _imageOffsetX;
            set
            {
                if (SetProperty(ref _imageOffsetX, value))
                {
                    LoggingService.LogDebug($"ImageOffsetX updated to: {value:F2}", "ImageManagementViewModel");

                    // Notify dependent properties that depend on drag offset changes
                    OnPropertyChanged(nameof(HasChanges));
                    OnPropertyChanged(nameof(CanReset));
                }
            }
        }

        /// <summary>
        /// Gets or sets the Y offset for image dragging within the preview container.
        /// Used for positioning the image within the fixed crop rectangle.
        /// </summary>
        public double ImageOffsetY
        {
            get => _imageOffsetY;
            set
            {
                if (SetProperty(ref _imageOffsetY, value))
                {
                    LoggingService.LogDebug($"ImageOffsetY updated to: {value:F2}", "ImageManagementViewModel");

                    // Notify dependent properties that depend on drag offset changes
                    OnPropertyChanged(nameof(HasChanges));
                    OnPropertyChanged(nameof(CanReset));
                }
            }
        }

        /// <summary>
        /// Gets or sets the visual scale factor applied to cropped images for fitting within InteractiveCropGuide boundaries.
        /// This scale is applied at the display level while maintaining ZoomPercentage at 100%.
        /// Value of 1.0 means no scaling, values less than 1.0 scale down for better fit.
        /// </summary>
        public double PostCropVisualScale
        {
            get => _postCropVisualScale;
            set
            {
                if (SetProperty(ref _postCropVisualScale, value))
                {
                    LoggingService.LogDebug($"PostCropVisualScale updated to: {value:F3}", "ImageManagementViewModel");
                }
            }
        }

        /// <summary>
        /// Gets whether the image is currently being dragged.
        /// Used for cursor management and visual feedback.
        /// </summary>
        public bool IsDragging
        {
            get => _isDragging;
            private set
            {
                if (SetProperty(ref _isDragging, value))
                {
                    LoggingService.LogDebug($"Drag state changed to: {value}", "ImageManagementViewModel");
                    OnPropertyChanged(nameof(DragCursor));
                }
            }
        }

        /// <summary>
        /// Gets the appropriate cursor for the current drag state.
        /// Returns SizeAll during drag, Hand when hovering over draggable image.
        /// </summary>
        public System.Windows.Input.Cursor DragCursor
        {
            get
            {
                if (!IsImageLoaded) return System.Windows.Input.Cursors.Arrow;
                return IsDragging ? System.Windows.Input.Cursors.SizeAll : System.Windows.Input.Cursors.Hand;
            }
        }

        #endregion

        #region Image Drag Methods

        /// <summary>
        /// Starts a drag operation when the mouse is pressed on the image.
        /// Captures the initial mouse position and current image offsets.
        /// </summary>
        /// <param name="mousePosition">The mouse position in preview container coordinates</param>
        public void StartDrag(Point mousePosition)
        {
            try
            {
                if (!IsImageLoaded || IsLoading)
                {
                    LoggingService.LogDebug("Drag start ignored - no image loaded or loading in progress", "ImageManagementViewModel");
                    return;
                }

                IsDragging = true;
                _lastMousePosition = mousePosition;
                _dragStartPosition = mousePosition;
                _dragStartOffsetX = ImageOffsetX;
                _dragStartOffsetY = ImageOffsetY;
                _lastDragUpdate = DateTime.Now;

                LoggingService.LogDebugLazy(() => $"Drag started at position: {mousePosition.X:F1}, {mousePosition.Y:F1}", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error starting drag operation: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
                IsDragging = false;
            }
        }

        /// <summary>
        /// Updates the image position during a drag operation.
        /// Includes throttling for performance and boundary constraints.
        /// </summary>
        /// <param name="mousePosition">The current mouse position in preview container coordinates</param>
        public void UpdateDrag(Point mousePosition)
        {
            try
            {
                if (!IsDragging || !IsImageLoaded)
                {
                    return;
                }

                // Throttle drag updates for performance
                var now = DateTime.Now;
                if ((now - _lastDragUpdate).TotalMilliseconds < DragThrottleMs)
                {
                    return;
                }
                _lastDragUpdate = now;

                // Calculate the drag delta from the start position
                var deltaX = mousePosition.X - _dragStartPosition.X;
                var deltaY = mousePosition.Y - _dragStartPosition.Y;

                // Calculate new offsets
                var newOffsetX = _dragStartOffsetX + deltaX;
                var newOffsetY = _dragStartOffsetY + deltaY;

                // Apply boundary constraints
                var constrainedOffsets = ApplyDragBoundaryConstraints(newOffsetX, newOffsetY);

                // Update image offsets
                ImageOffsetX = constrainedOffsets.X;
                ImageOffsetY = constrainedOffsets.Y;

                _lastMousePosition = mousePosition;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating drag operation: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
            }
        }

        /// <summary>
        /// Ends the current drag operation and performs final position validation.
        /// </summary>
        public void EndDrag()
        {
            try
            {
                if (!IsDragging)
                {
                    return;
                }

                // Calculate total drag distance for logging
                var totalDragDistance = Math.Sqrt(
                    Math.Pow(_lastMousePosition.X - _dragStartPosition.X, 2) +
                    Math.Pow(_lastMousePosition.Y - _dragStartPosition.Y, 2)
                );

                LoggingService.LogDebug($"Drag ended - Total distance: {totalDragDistance:F1}px, Final offset: ({ImageOffsetX:F1}, {ImageOffsetY:F1})", "ImageManagementViewModel");

                IsDragging = false;

                // Perform final boundary validation
                var constrainedOffsets = ApplyDragBoundaryConstraints(ImageOffsetX, ImageOffsetY);
                if (Math.Abs(constrainedOffsets.X - ImageOffsetX) > 0.1 || Math.Abs(constrainedOffsets.Y - ImageOffsetY) > 0.1)
                {
                    ImageOffsetX = constrainedOffsets.X;
                    ImageOffsetY = constrainedOffsets.Y;
                    LoggingService.LogDebug("Final drag position adjusted to respect boundaries", "ImageManagementViewModel");
                }

                // Update WYSIWYG backend simulation if enabled
                if (_isWysiwygEnabled && _backendSynchronizer != null)
                {
                    try
                    {
                        var dragOffset = new Point(ImageOffsetX, ImageOffsetY);
                        _backendSynchronizer.SynchronizeTransformations(
                            ZoomPercentage, 
                            RotationAngle, 
                            dragOffset, 
                            CropRectangle);
                    }
                    catch (Exception syncEx)
                    {
                        LoggingService.LogWarning($"Failed to synchronize drag position with backend: {syncEx.Message}", "ImageManagementViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error ending drag operation: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
                IsDragging = false;
            }
        }

        /// <summary>
        /// Calculates an adaptive minimum visible margin based on zoom level, image size, and container dimensions.
        /// Uses smaller margins for higher zoom levels and larger images to allow better edge positioning.
        /// </summary>
        /// <param name="zoomScale">Current zoom scale factor</param>
        /// <param name="imageWidth">Rotated image width</param>
        /// <param name="imageHeight">Rotated image height</param>
        /// <param name="containerWidth">Container width</param>
        /// <param name="containerHeight">Container height</param>
        /// <param name="shouldLog">Whether to log debug information</param>
        /// <returns>Adaptive minimum visible margin in pixels</returns>
        private double CalculateAdaptiveMinVisibleMargin(double zoomScale, double imageWidth, double imageHeight,
            double containerWidth, double containerHeight, bool shouldLog = false)
        {
            try
            {
                // Base calculation: smaller margin for higher zoom levels
                double zoomFactor = Math.Max(0.3, Math.Min(1.0, 1.0 / zoomScale));

                // Size factor: smaller margin for larger images relative to container
                double imageSizeRatio = Math.Min(imageWidth / containerWidth, imageHeight / containerHeight);
                double sizeFactor = Math.Max(0.4, Math.Min(1.0, 1.0 - (imageSizeRatio - 1.0) * 0.3));

                // Calculate adaptive margin
                double adaptiveMargin = BaseMinVisibleMargin + (MaxMinVisibleMargin - BaseMinVisibleMargin) * zoomFactor * sizeFactor;

                // Ensure margin doesn't exceed reasonable limits
                double maxReasonableMargin = Math.Min(containerWidth, containerHeight) * 0.15; // Max 15% of smaller container dimension
                adaptiveMargin = Math.Min(adaptiveMargin, maxReasonableMargin);

                // Only log adaptive margin calculation periodically to reduce overhead
                if (shouldLog)
                {
                    LoggingService.LogDebug($"Adaptive margin calculated: {adaptiveMargin:F1}px for zoom {zoomScale * 100:F0}%, " +
                        $"image size ratio {imageSizeRatio:F2}, zoom factor {zoomFactor:F2}, size factor {sizeFactor:F2}",
                        "ImageManagementViewModel");
                }

                return adaptiveMargin;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating adaptive minimum visible margin: {ex.Message}", "ImageManagementViewModel");
                return BaseMinVisibleMargin;
            }
        }

        /// <summary>
        /// Applies rotation-aware boundary constraints to prevent the image from being dragged completely outside the preview area.
        /// Maintains an adaptive minimum visible margin on all sides, accounting for the rotated bounding box of the image.
        /// Uses trigonometric calculations to determine the actual visual bounds of the rotated image.
        /// </summary>
        /// <param name="offsetX">The proposed X offset</param>
        /// <param name="offsetY">The proposed Y offset</param>
        /// <returns>The constrained offset point</returns>
        public Point ApplyDragBoundaryConstraints(double offsetX, double offsetY)
        {
            try
            {
                if (CurrentImage == null)
                {
                    return new Point(0, 0);
                }

                // Preview container dimensions (fixed at 500x320)
                const double containerWidth = 500.0;
                const double containerHeight = 320.0;

                // Calculate rotation-aware image dimensions using the rotated bounding box
                var originalSize = new Size(CurrentImage.PixelWidth, CurrentImage.PixelHeight);
                var rotatedBoundingSize = CoordinateTransformHelper.CalculateOptimalRenderSize(
                    originalSize, RotationAngle, ZoomScale);

                // Throttle boundary constraint logging to reduce overhead during drag operations
                var now = DateTime.Now;
                bool shouldLog = (now - _lastBoundaryLogUpdate).TotalMilliseconds >= BoundaryLogThrottleMs;

                if (shouldLog)
                {
                    _lastBoundaryLogUpdate = now;
                    LoggingService.LogDebugLazy(() => $"Boundary constraint calculation - Original: {originalSize.Width}x{originalSize.Height}, " +
                        $"Rotation: {RotationAngle:F1}°, Zoom: {ZoomScale:F2}, " +
                        $"Rotated Bounding: {rotatedBoundingSize.Width:F1}x{rotatedBoundingSize.Height:F1}",
                        "ImageManagementViewModel");

                    // Log detailed trigonometric calculations for debugging extreme rotations
                    if (Math.Abs(RotationAngle % 90) > 0.1) // Non-90-degree rotations
                    {
                        double radians = RotationAngle * Math.PI / 180.0;
                        double cos = Math.Abs(Math.Cos(radians));
                        double sin = Math.Abs(Math.Sin(radians));
                        LoggingService.LogDebug($"Trigonometric values - Angle: {RotationAngle:F1}°, " +
                            $"Radians: {radians:F4}, Cos: {cos:F4}, Sin: {sin:F4}, " +
                            $"Width expansion: {cos * originalSize.Width + sin * originalSize.Height:F1}, " +
                            $"Height expansion: {sin * originalSize.Width + cos * originalSize.Height:F1}",
                            "ImageManagementViewModel");
                    }
                }

                // Use the rotated bounding box dimensions for constraint calculations
                var imageWidth = rotatedBoundingSize.Width;
                var imageHeight = rotatedBoundingSize.Height;

                // Calculate adaptive minimum visible margin
                var adaptiveMargin = CalculateAdaptiveMinVisibleMargin(ZoomScale, imageWidth, imageHeight, containerWidth, containerHeight, shouldLog);

                // Calculate boundary constraints based on the relationship between image and container
                double maxOffsetX, minOffsetX, maxOffsetY, minOffsetY;

                if (imageWidth <= containerWidth)
                {
                    // Image is smaller than or equal to container width - allow positioning to container edges
                    maxOffsetX = (containerWidth - imageWidth) / 2.0;
                    minOffsetX = -(containerWidth - imageWidth) / 2.0;
                }
                else
                {
                    // Image is larger than container - ensure minimum visible margin
                    maxOffsetX = (imageWidth - adaptiveMargin) / 2.0;
                    minOffsetX = -(imageWidth - adaptiveMargin) / 2.0;
                }

                if (imageHeight <= containerHeight)
                {
                    // Image is smaller than or equal to container height - allow positioning to container edges
                    maxOffsetY = (containerHeight - imageHeight) / 2.0;
                    minOffsetY = -(containerHeight - imageHeight) / 2.0;
                }
                else
                {
                    // Image is larger than container - ensure minimum visible margin
                    maxOffsetY = (imageHeight - adaptiveMargin) / 2.0;
                    minOffsetY = -(imageHeight - adaptiveMargin) / 2.0;
                }

                // Clamp the offsets to the calculated boundaries
                var constrainedX = Math.Max(minOffsetX, Math.Min(maxOffsetX, offsetX));
                var constrainedY = Math.Max(minOffsetY, Math.Min(maxOffsetY, offsetY));

                // Log boundary constraint application for debugging
                if (Math.Abs(constrainedX - offsetX) > 0.1 || Math.Abs(constrainedY - offsetY) > 0.1)
                {
                    LoggingService.LogDebug($"Boundary constraints applied - Original offset: ({offsetX:F1}, {offsetY:F1}), " +
                        $"Constrained: ({constrainedX:F1}, {constrainedY:F1}), " +
                        $"Adaptive margin: {adaptiveMargin:F1}px, " +
                        $"Bounds: X[{minOffsetX:F1}, {maxOffsetX:F1}], Y[{minOffsetY:F1}, {maxOffsetY:F1}]",
                        "ImageManagementViewModel");
                }

                return new Point(constrainedX, constrainedY);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying rotation-aware drag boundary constraints: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
                return new Point(0, 0);
            }
        }

        /// <summary>
        /// Resets the image drag position to center (0, 0).
        /// Called during image reset operations.
        /// </summary>
        public void ResetDragPosition()
        {
            try
            {
                LoggingService.LogDebug("Resetting image drag position to center", "ImageManagementViewModel");
                
                ImageOffsetX = 0.0;
                ImageOffsetY = 0.0;
                IsDragging = false;

                // Clear drag state
                _lastMousePosition = new Point(0, 0);
                _dragStartPosition = new Point(0, 0);
                _dragStartOffsetX = 0.0;
                _dragStartOffsetY = 0.0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error resetting drag position: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
            }
        }

        /// <summary>
        /// Moves the image by the specified offset with boundary constraints applied.
        /// Used for keyboard-based image positioning.
        /// </summary>
        /// <param name="deltaX">The X offset to add to the current position</param>
        /// <param name="deltaY">The Y offset to add to the current position</param>
        public void MoveImageByOffset(double deltaX, double deltaY)
        {
            try
            {
                if (!IsImageLoaded || IsLoading)
                {
                    return;
                }

                // Calculate new position
                var newOffsetX = ImageOffsetX + deltaX;
                var newOffsetY = ImageOffsetY + deltaY;

                // Apply boundary constraints
                var constrainedOffsets = ApplyDragBoundaryConstraints(newOffsetX, newOffsetY);

                // Update image offsets
                ImageOffsetX = constrainedOffsets.X;
                ImageOffsetY = constrainedOffsets.Y;

                // Update WYSIWYG backend simulation if enabled
                if (_isWysiwygEnabled && _backendSynchronizer != null)
                {
                    try
                    {
                        var dragOffset = new Point(ImageOffsetX, ImageOffsetY);
                        _backendSynchronizer.SynchronizeTransformations(
                            ZoomPercentage, 
                            RotationAngle, 
                            dragOffset, 
                            CropRectangle);
                    }
                    catch (Exception syncEx)
                    {
                        LoggingService.LogDebug($"Failed to synchronize keyboard movement with backend: {syncEx.Message}", "ImageManagementViewModel");
                    }
                }

                LoggingService.LogDebug($"Image moved by offset ({deltaX:F1}, {deltaY:F1}) to position ({ImageOffsetX:F1}, {ImageOffsetY:F1})", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error moving image by offset: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
            }
        }

        #endregion

        #region Public Methods



        /// <summary>
        /// Validates the result of an edge positioning test to ensure it meets expected criteria.
        /// </summary>
        /// <param name="scenario">Test scenario information</param>
        /// <param name="rotation">Rotation angle being tested</param>
        /// <param name="edgeTest">Edge test information</param>
        /// <param name="constrainedOffset">The constrained offset result</param>
        /// <returns>True if the result is valid</returns>
        private bool ValidateEdgePositioningResult(dynamic scenario, double rotation, dynamic edgeTest, Point constrainedOffset)
        {
            try
            {
                // Check for NaN or Infinity
                if (double.IsNaN(constrainedOffset.X) || double.IsInfinity(constrainedOffset.X) ||
                    double.IsNaN(constrainedOffset.Y) || double.IsInfinity(constrainedOffset.Y))
                {
                    return false;
                }

                // For high zoom scenarios, ensure the constraint allows reasonable positioning
                if (scenario.ZoomScale >= 2.0)
                {
                    // High zoom should allow larger offsets (closer to edges)
                    double maxReasonableOffset = 200.0; // Should allow positioning within 200px of center
                    if (Math.Abs(constrainedOffset.X) > maxReasonableOffset || Math.Abs(constrainedOffset.Y) > maxReasonableOffset)
                    {
                        return false;
                    }
                }

                // For low zoom scenarios, ensure images can reach container edges
                if (scenario.ZoomScale <= 1.0)
                {
                    // Low zoom should allow positioning close to container edges
                    double containerHalfWidth = 250.0; // 500/2
                    double containerHalfHeight = 160.0; // 320/2

                    // Should allow positioning within reasonable distance of container edges
                    if (edgeTest.Name.Contains("Left") || edgeTest.Name.Contains("Right"))
                    {
                        if (Math.Abs(Math.Abs(constrainedOffset.X) - containerHalfWidth) > 100.0)
                        {
                            return false;
                        }
                    }

                    if (edgeTest.Name.Contains("Top") || edgeTest.Name.Contains("Bottom"))
                    {
                        if (Math.Abs(Math.Abs(constrainedOffset.Y) - containerHalfHeight) > 100.0)
                        {
                            return false;
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating edge positioning result: {ex.Message}", "ImageManagementViewModel");
                return false;
            }
        }



        /// <summary>
        /// Tests rotation-aware boundary constraints with various edge cases to ensure proper functionality.
        /// This method validates boundary calculations for extreme rotations, high zoom levels, and edge cases.
        /// Used for debugging and validation of the rotation-aware constraint system.
        /// </summary>
        /// <param name="testRotationAngles">Array of rotation angles to test (optional, uses default test cases if null)</param>
        /// <param name="testZoomLevels">Array of zoom levels to test (optional, uses default test cases if null)</param>
        /// <returns>True if all tests pass, false if any issues are detected</returns>
        public bool TestRotationAwareBoundaryConstraints(double[]? testRotationAngles = null, double[]? testZoomLevels = null)
        {
            try
            {
                if (CurrentImage == null)
                {
                    LoggingService.LogWarning("Cannot test boundary constraints - no image loaded", "ImageManagementViewModel");
                    return false;
                }

                LoggingService.LogInfo("Starting rotation-aware boundary constraint testing", "ImageManagementViewModel");

                // Default test cases for comprehensive validation
                var rotationAngles = testRotationAngles ?? new double[] { 0, 15, 30, 45, 60, 75, 90, 135, 180, 225, 270, 315, 359.5 };
                var zoomLevels = testZoomLevels ?? new double[] { 0.25, 0.5, 1.0, 1.5, 2.0, 3.0, 4.0 };

                bool allTestsPassed = true;
                int testCount = 0;
                int passedTests = 0;

                foreach (var rotation in rotationAngles)
                {
                    foreach (var zoom in zoomLevels)
                    {
                        testCount++;

                        // Temporarily set test values
                        var originalRotation = RotationAngle;
                        var originalZoom = ZoomScale;

                        try
                        {
                            // Set test rotation and zoom
                            _rotationAngle = rotation;
                            _zoomPercentage = zoom * 100;

                            // Test boundary constraint calculation
                            var testOffset = new Point(100, 100); // Arbitrary test offset
                            var constrainedOffset = ApplyDragBoundaryConstraints(testOffset.X, testOffset.Y);

                            // Validate that constraints are reasonable
                            bool testPassed = ValidateBoundaryConstraintResult(rotation, zoom, testOffset, constrainedOffset);

                            if (testPassed)
                            {
                                passedTests++;
                            }
                            else
                            {
                                allTestsPassed = false;
                                LoggingService.LogWarning($"Boundary constraint test failed - Rotation: {rotation:F1}°, " +
                                    $"Zoom: {zoom:F2}, Input: ({testOffset.X:F1}, {testOffset.Y:F1}), " +
                                    $"Output: ({constrainedOffset.X:F1}, {constrainedOffset.Y:F1})",
                                    "ImageManagementViewModel");
                            }
                        }
                        finally
                        {
                            // Restore original values
                            _rotationAngle = originalRotation;
                            _zoomPercentage = originalZoom * 100;
                        }
                    }
                }

                LoggingService.LogInfo($"Boundary constraint testing completed - {passedTests}/{testCount} tests passed",
                    "ImageManagementViewModel");

                return allTestsPassed;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during boundary constraint testing: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
                return false;
            }
        }

        /// <summary>
        /// Starts a loading operation by setting the loading state.
        /// Hides the no image placeholder and shows the loading indicator.
        /// Includes comprehensive error handling and state validation.
        /// </summary>
        public void StartLoading()
        {
            try
            {
                LoggingService.LogDebug("Starting image loading operation", "ImageManagementViewModel");
                
                // Validate current state before starting loading
                if (IsLoading)
                {
                    LoggingService.LogWarning("Loading operation already in progress. Ignoring duplicate start request.", "ImageManagementViewModel");
                    return;
                }
                
                IsLoading = true;
                LoggingService.LogInfo("Image loading started successfully", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error starting loading operation: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء بدء تحميل الصورة.", "خطأ في التحميل", LogLevel.Error, "ImageManagementViewModel");
                
                // Ensure loading state is cleared on error
                try
                {
                    IsLoading = false;
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Failed to clear loading state after error: {fallbackEx.Message}", "ImageManagementViewModel");
                }
            }
        }

        /// <summary>
        /// Completes a loading operation with a successful result.
        /// Sets the loaded image and updates all related states.
        /// Includes comprehensive validation and error handling.
        /// </summary>
        /// <param name="image">The successfully loaded image</param>
        public void CompleteLoading(BitmapImage? image)
        {
            try
            {
                LoggingService.LogDebug($"Completing image loading operation - HasImage: {image != null}", "ImageManagementViewModel");
                
                // Validate the loaded image if provided
                if (image != null)
                {
                    try
                    {
                        // Basic validation of the image
                        var width = image.PixelWidth;
                        var height = image.PixelHeight;
                        
                        if (width <= 0 || height <= 0)
                        {
                            LoggingService.LogWarning($"Invalid image dimensions: {width}x{height}. Treating as failed load.", "ImageManagementViewModel");
                            image = null;
                        }
                        else
                        {
                            // Calculate additional preview metrics
                            var aspectRatio = (double)width / height;
                            var totalPixels = (long)width * height;
                            var megapixels = totalPixels / 1_000_000.0;

                            LoggingService.LogInfo($"Preview Image Validated - Dimensions: {width}x{height}, Aspect Ratio: {aspectRatio:F2}, Megapixels: {megapixels:F1}MP, Total Pixels: {totalPixels:N0}", "ImageManagementViewModel");

                            // Log format and quality information if available
                            try
                            {
                                var format = image.Format.ToString();
                                var dpiX = image.DpiX;
                                var dpiY = image.DpiY;
                                LoggingService.LogDebug($"Preview Image Properties - Format: {format}, DPI: {dpiX:F0}x{dpiY:F0}", "ImageManagementViewModel");
                            }
                            catch (Exception formatEx)
                            {
                                LoggingService.LogDebug($"Unable to retrieve image format details: {formatEx.Message}", "ImageManagementViewModel");
                            }
                        }
                    }
                    catch (Exception validationEx)
                    {
                        LoggingService.LogWarning($"Error validating loaded image: {validationEx.Message}. Proceeding with load.", "ImageManagementViewModel");
                    }
                }
                
                CurrentImage = image;
                IsLoading = false;

                // Create backup of original image for reset functionality
                if (image != null)
                {
                    try
                    {
                        LoggingService.LogInfo($"Creating original image backup - Dimensions: {image.PixelWidth}x{image.PixelHeight}", "ImageManagementViewModel");
                        OriginalImage = image; // Store backup of original image
                        HasBeenCropped = false; // Reset crop state for new image
                        ImageOffsetX = 0.0; // Reset image position
                        ImageOffsetY = 0.0;
                        PostCropVisualScale = 1.0; // Reset visual scaling

                        ResetTransforms();
                        ResetCropRectangle();

                        // Initialize WYSIWYG backend simulation for the loaded image
                        if (_isWysiwygEnabled)
                        {
                            try
                            {
                                InitializeBackendSimulation();
                            }
                            catch (Exception simEx)
                            {
                                LoggingService.LogWarning($"Failed to initialize backend simulation: {simEx.Message}", "ImageManagementViewModel");
                                // Continue without WYSIWYG - it will fall back to standard cropping
                            }
                        }

                        LoggingService.LogDebug("Original image backup created and state reset for new image", "ImageManagementViewModel");
                    }
                    catch (Exception resetEx)
                    {
                        LoggingService.LogWarning($"Error resetting transforms and crop rectangle after image load: {resetEx.Message}", "ImageManagementViewModel");
                    }
                }
                else
                {
                    // Clear backup when no image is loaded
                    OriginalImage = null;
                    HasBeenCropped = false;
                    ImageOffsetX = 0.0;
                    ImageOffsetY = 0.0;
                    PostCropVisualScale = 1.0;
                }
                
                LoggingService.LogInfo($"Image loading completed successfully - Success: {image != null}", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error completing loading operation: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء إكمال تحميل الصورة.", "خطأ في التحميل", LogLevel.Error, "ImageManagementViewModel");
                
                // Ensure loading state is cleared even on error
                try
                {
                    IsLoading = false;
                    CurrentImage = null;
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Failed to clear state after loading error: {fallbackEx.Message}", "ImageManagementViewModel");
                }
            }
        }

        /// <summary>
        /// Completes a loading operation without overwriting the original image backup.
        /// Used after reset operations to maintain the original image reference.
        /// </summary>
        /// <param name="image">The current image to set</param>
        public void CompleteLoadingPreservingOriginal(BitmapImage? image)
        {
            try
            {
                LoggingService.LogDebug($"Completing loading operation while preserving original image backup - HasImage: {image != null}", "ImageManagementViewModel");

                // Update current image without affecting OriginalImage backup
                CurrentImage = image;
                IsLoading = false;

                LoggingService.LogInfo("Loading completed successfully with original image backup preserved", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error completing loading operation while preserving original: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء إكمال العملية.", "خطأ في التحميل", LogLevel.Error, "ImageManagementViewModel");

                // Ensure loading state is cleared even on error
                try
                {
                    IsLoading = false;
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Failed to clear loading state after error: {fallbackEx.Message}", "ImageManagementViewModel");
                }
            }
        }

        /// <summary>
        /// Completes a loading operation with an error result.
        /// Clears the loading state and maintains the no image state.
        /// Includes comprehensive error handling and state cleanup.
        /// </summary>
        public void CompleteLoadingWithError()
        {
            try
            {
                LoggingService.LogInfo("Completing image loading operation with error", "ImageManagementViewModel");
                
                CurrentImage = null;
                IsLoading = false;
                
                // Validate and correct transforms after error
                try
                {
                    ValidateAndCorrectTransforms();
                }
                catch (Exception validationEx)
                {
                    LoggingService.LogWarning($"Error validating transforms after loading error: {validationEx.Message}", "ImageManagementViewModel");
                }
                
                LoggingService.LogInfo("Image loading error handled successfully", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling loading error: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
                
                // Ensure loading state is cleared as last resort
                try
                {
                    IsLoading = false;
                    CurrentImage = null;
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Failed to clear state in error handler: {fallbackEx.Message}", "ImageManagementViewModel");
                }
            }
        }

        /// <summary>
        /// Clears the current image and resets to no image state.
        /// Used when removing or resetting the image.
        /// Includes comprehensive state cleanup and error handling.
        /// </summary>
        public void ClearImage()
        {
            try
            {
                LoggingService.LogInfo("Clearing current image", "ImageManagementViewModel");
                
                CurrentImage = null;
                IsLoading = false;
                
                // Reset transforms when clearing image
                try
                {
                    ResetTransforms();
                }
                catch (Exception resetEx)
                {
                    LoggingService.LogWarning($"Error resetting transforms during image clear: {resetEx.Message}", "ImageManagementViewModel");
                }
                
                LoggingService.LogInfo("Image cleared successfully", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing image: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء مسح الصورة.", "خطأ في المسح", LogLevel.Error, "ImageManagementViewModel");
                
                // Ensure state is cleared even on error
                try
                {
                    CurrentImage = null;
                    IsLoading = false;
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Failed to clear state after clear error: {fallbackEx.Message}", "ImageManagementViewModel");
                }
            }
        }

        /// <summary>
        /// Resets zoom, rotation, and position values to their default states.
        /// Includes comprehensive error handling and logging.
        /// </summary>
        public void ResetTransforms()
        {
            try
            {
                LoggingService.LogInfo("Resetting image transforms to default values", "ImageManagementViewModel");
                
                // Reset zoom percentage to default
                ZoomPercentage = DefaultZoomPercentage;
                
                // Reset rotation angle to default
                RotationAngle = DefaultRotationAngle;
                
                // Reset drag position to center
                ResetDragPosition();

                // Reset visual scaling
                PostCropVisualScale = 1.0;
                
                LoggingService.LogInfo("Image transforms reset successfully", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error resetting image transforms: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء إعادة تعيين إعدادات الصورة.", "خطأ في إعادة التعيين", LogLevel.Error, "ImageManagementViewModel");
            }
        }

        /// <summary>
        /// Validates the current transform values and corrects any invalid states.
        /// Used for error recovery and state validation.
        /// </summary>
        public void ValidateAndCorrectTransforms()
        {
            try
            {
                LoggingService.LogDebug("Validating and correcting transform values", "ImageManagementViewModel");
                
                var correctionsMade = false;
                
                // Validate zoom percentage
                if (double.IsNaN(_zoomPercentage) || double.IsInfinity(_zoomPercentage) || 
                    _zoomPercentage < MinZoomPercentage || _zoomPercentage > MaxZoomPercentage)
                {
                    LoggingService.LogWarning($"Invalid zoom percentage detected: {_zoomPercentage}. Correcting to default.", "ImageManagementViewModel");
                    _zoomPercentage = DefaultZoomPercentage;
                    OnPropertyChanged(nameof(ZoomPercentage));
                    OnPropertyChanged(nameof(ZoomPercentageText));
                    OnPropertyChanged(nameof(ZoomScale));
                    correctionsMade = true;
                }
                
                // Validate rotation angle
                if (double.IsNaN(_rotationAngle) || double.IsInfinity(_rotationAngle))
                {
                    LoggingService.LogWarning($"Invalid rotation angle detected: {_rotationAngle}. Correcting to default.", "ImageManagementViewModel");
                    _rotationAngle = DefaultRotationAngle;
                    OnPropertyChanged(nameof(RotationAngle));
                    OnPropertyChanged(nameof(RotationAngleText));
                    correctionsMade = true;
                }
                
                if (correctionsMade)
                {
                    LoggingService.LogInfo("Transform values corrected successfully", "ImageManagementViewModel");
                }
                else
                {
                    LoggingService.LogDebug("All transform values are valid", "ImageManagementViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating and correcting transforms: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementViewModel");
            }
        }

        #endregion

        #region Performance Monitoring Methods

        /// <summary>
        /// Starts performance monitoring for slider operations.
        /// Used to track the performance of zoom and rotation slider interactions.
        /// </summary>
        /// <param name="operationType">The type of slider operation being monitored</param>
        private void StartSliderPerformanceMonitoring(string operationType)
        {
            try
            {
                _sliderOperationStopwatch = Stopwatch.StartNew();
                LoggingService.LogDebugLazy(() => $"Started performance monitoring for {operationType}", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error starting performance monitoring for {operationType}: {ex.Message}", "ImageManagementViewModel");
            }
        }

        /// <summary>
        /// Completes performance monitoring for slider operations.
        /// Logs performance metrics if the operation took significant time.
        /// </summary>
        /// <param name="operationType">The type of slider operation being monitored</param>
        private void CompleteSliderPerformanceMonitoring(string operationType)
        {
            try
            {
                if (_sliderOperationStopwatch != null)
                {
                    _sliderOperationStopwatch.Stop();
                    var elapsedMs = _sliderOperationStopwatch.ElapsedMilliseconds;
                    
                    // Log performance if operation took longer than expected
                    if (elapsedMs > 10) // Log operations taking more than 10ms
                    {
                        LoggingService.LogInfo($"{operationType} operation completed in {elapsedMs}ms", "ImageManagementViewModel");
                    }
                    
                    // Log warning for very slow operations
                    if (elapsedMs > 100) // Warn for operations taking more than 100ms
                    {
                        LoggingService.LogWarning($"Slow {operationType} operation detected: {elapsedMs}ms", "ImageManagementViewModel");
                    }
                    
                    _sliderOperationStopwatch = null;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error completing performance monitoring for {operationType}: {ex.Message}", "ImageManagementViewModel");
            }
        }

        /// <summary>
        /// Logs slider operation details with debouncing to prevent excessive logging.
        /// Only logs if sufficient time has passed since the last log entry.
        /// Includes performance measurement for optimization analysis.
        /// </summary>
        /// <param name="propertyName">The name of the property being changed</param>
        /// <param name="value">The new value</param>
        /// <param name="unit">The unit of measurement</param>
        private void LogSliderOperation(string propertyName, double value, string unit)
        {
            try
            {
                var now = DateTime.Now;

                // Debounce logging to prevent excessive log entries during rapid slider movements
                if ((now - _lastSliderUpdate).TotalMilliseconds >= SliderDebounceMs)
                {
                    // Use performance measurement wrapper for logging operations
                    LoggingService.MeasureLoggingPerformance($"SliderLog_{propertyName}", () =>
                    {
                        LoggingService.LogDebugLazy(() => $"{propertyName} changed to: {value:F1}{unit}", "ImageManagementViewModel");
                    });
                    _lastSliderUpdate = now;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error logging slider operation for {propertyName}: {ex.Message}", "ImageManagementViewModel");
            }
        }

        #endregion

        #region Crop Rectangle Validation

        /// <summary>
        /// Validates the crop rectangle to ensure it maintains proper aspect ratio and bounds.
        /// Applies corrections if necessary to maintain profile image requirements.
        /// </summary>
        private void ValidateCropRectangle()
        {
            try
            {
                const double targetAspectRatio = 127.0 / 145.0; // Profile image aspect ratio
                const double tolerance = 0.01;

                var currentAspectRatio = _cropRectangle.Width / _cropRectangle.Height;

                // Check if aspect ratio needs correction
                if (Math.Abs(currentAspectRatio - targetAspectRatio) > tolerance)
                {
                    LoggingService.LogDebug($"Correcting crop rectangle aspect ratio from {currentAspectRatio:F3} to {targetAspectRatio:F3}",
                        "ImageManagementViewModel");

                    // Adjust height to maintain target aspect ratio
                    var correctedHeight = _cropRectangle.Width / targetAspectRatio;
                    _cropRectangle.Height = correctedHeight;

                    // Ensure the corrected rectangle fits within bounds
                    if (_cropRectangle.Bottom > 320) // Preview container height
                    {
                        _cropRectangle.Height = 320 - _cropRectangle.Y;
                        _cropRectangle.Width = _cropRectangle.Height * targetAspectRatio;
                    }

                    LoggingService.LogDebug($"Crop rectangle corrected to: {_cropRectangle}", "ImageManagementViewModel");
                }

                // Ensure minimum size
                const double minWidth = 50.0;
                const double minHeight = minWidth / targetAspectRatio;

                if (_cropRectangle.Width < minWidth || _cropRectangle.Height < minHeight)
                {
                    _cropRectangle.Width = Math.Max(minWidth, _cropRectangle.Width);
                    _cropRectangle.Height = Math.Max(minHeight, _cropRectangle.Height);
                    LoggingService.LogDebug($"Crop rectangle size corrected to minimum: {_cropRectangle}", "ImageManagementViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating crop rectangle: {ex.Message}", "ImageManagementViewModel");
            }
        }

        /// <summary>
        /// Resets the crop rectangle to the default centered position with proper aspect ratio.
        /// </summary>
        public void ResetCropRectangle()
        {
            try
            {
                const double defaultWidth = 254.0;
                const double defaultHeight = 290.0;
                const double containerWidth = 500.0;
                const double containerHeight = 320.0;

                var x = (containerWidth - defaultWidth) / 2;
                var y = (containerHeight - defaultHeight) / 2;

                CropRectangle = new Rect(x, y, defaultWidth, defaultHeight);

                LoggingService.LogInfo("Crop rectangle reset to default position", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error resetting crop rectangle: {ex.Message}", "ImageManagementViewModel");
            }
        }

        /// <summary>
        /// Resets all transformations and restores the original image.
        /// Restores the original loaded image, resets zoom/rotation, and clears crop state.
        /// </summary>
        public void ResetToOriginal()
        {
            try
            {
                LoggingService.LogInfo("Starting reset to original image operation", "ImageManagementViewModel");

                // Validate that we have an original image backup
                if (OriginalImage == null)
                {
                    LoggingService.LogWarning("No original image backup available for reset", "ImageManagementViewModel");
                    ErrorManager.ShowUserErrorToast(
                        "لا توجد نسخة احتياطية من الصورة الأصلية للاستعادة.",
                        "لا توجد صورة أصلية"
                    );
                    return;
                }

                // Log original image dimensions for verification
                LoggingService.LogInfo($"Restoring original image - Dimensions: {OriginalImage.PixelWidth}x{OriginalImage.PixelHeight}", "ImageManagementViewModel");

                // Restore the original image
                CurrentImage = OriginalImage;

                // Reset all transformation properties comprehensively
                ResetTransforms();

                // Reset crop state
                HasBeenCropped = false;
                ResetCropRectangle();

                // Reinitialize backend simulation for the original image dimensions
                if (_isWysiwygEnabled && CurrentImage != null)
                {
                    try
                    {
                        LoggingService.LogInfo("Reinitializing backend simulation for original image after reset", "ImageManagementViewModel");
                        InitializeBackendSimulation();
                        LoggingService.LogInfo("Backend simulation successfully reinitialized for original image", "ImageManagementViewModel");
                    }
                    catch (Exception simEx)
                    {
                        LoggingService.LogWarning($"Failed to reinitialize backend simulation after reset: {simEx.Message}", "ImageManagementViewModel");
                        // Continue without WYSIWYG - it will fall back to standard cropping
                    }
                }

                // Verify the restoration was successful
                if (CurrentImage != null)
                {
                    LoggingService.LogInfo($"Reset completed - Current image dimensions: {CurrentImage.PixelWidth}x{CurrentImage.PixelHeight}", "ImageManagementViewModel");
                }

                LoggingService.LogInfo("Successfully reset to original image with all transformations and backend simulation cleared", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error resetting to original image: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ أثناء إعادة تعيين الصورة إلى حالتها الأصلية.",
                    "خطأ في إعادة التعيين",
                    LogLevel.Error,
                    "ImageManagementViewModel"
                );
            }
        }

        #endregion

        #region Post-Crop Visual Fit Methods

        /// <summary>
        /// Applies visual fitting for cropped images to appear within InteractiveCropGuide boundaries
        /// while maintaining ZoomPercentage at 100%. Uses transform-based scaling for visual appearance.
        /// </summary>
        /// <param name="croppedImage">The cropped image to apply visual fit for</param>
        private void ApplyPostCropVisualFit(BitmapImage croppedImage)
        {
            try
            {
                if (croppedImage == null)
                {
                    LoggingService.LogWarning("Cannot apply visual fit for null image", "ImageManagementViewModel");
                    PostCropVisualScale = 1.0; // Reset to no scaling
                    return;
                }

                LoggingService.LogInfo($"Applying post-crop visual fit for cropped image: {croppedImage.PixelWidth}x{croppedImage.PixelHeight}", "ImageManagementViewModel");

                // InteractiveCropGuide dimensions (target area for fitting)
                const double cropGuideWidth = 254.0;
                const double cropGuideHeight = 290.0;

                // Preview container dimensions
                const double containerWidth = 500.0;
                const double containerHeight = 320.0;

                // Get cropped image dimensions
                double imageWidth = croppedImage.PixelWidth;
                double imageHeight = croppedImage.PixelHeight;

                // Validate image dimensions
                if (imageWidth <= 0 || imageHeight <= 0)
                {
                    LoggingService.LogWarning($"Invalid image dimensions: {imageWidth}x{imageHeight}, no visual scaling applied", "ImageManagementViewModel");
                    PostCropVisualScale = 1.0;
                    return;
                }

                // Calculate how the image would naturally fit in the container with Stretch="Uniform" at 100% zoom
                double containerScaleX = containerWidth / imageWidth;
                double containerScaleY = containerHeight / imageHeight;
                double naturalContainerScale = Math.Min(containerScaleX, containerScaleY);

                // Calculate the natural display size of the image in the container at 100% zoom
                double naturalDisplayWidth = imageWidth * naturalContainerScale;
                double naturalDisplayHeight = imageHeight * naturalContainerScale;

                LoggingService.LogDebug($"Natural display size at 100% zoom: {naturalDisplayWidth:F1}x{naturalDisplayHeight:F1}", "ImageManagementViewModel");

                // Calculate scale factors needed to fit within crop guide boundaries
                double cropGuideScaleX = cropGuideWidth / naturalDisplayWidth;
                double cropGuideScaleY = cropGuideHeight / naturalDisplayHeight;

                // Use the smaller scale factor to ensure the image fits completely within crop guide
                double visualScale = Math.Min(cropGuideScaleX, cropGuideScaleY);

                // Only apply scaling if the image is larger than the crop guide area
                // If the image already fits, don't make it smaller
                if (visualScale > 1.0)
                {
                    visualScale = 1.0; // Don't scale up, just maintain current size
                }

                // Apply the visual scale factor
                PostCropVisualScale = visualScale;

                // Calculate final visual display dimensions for logging
                double finalVisualWidth = naturalDisplayWidth * visualScale;
                double finalVisualHeight = naturalDisplayHeight * visualScale;

                LoggingService.LogInfo($"Post-crop visual fit applied:", "ImageManagementViewModel");
                LoggingService.LogInfo($"  • Cropped image: {imageWidth:F0}x{imageHeight:F0} pixels", "ImageManagementViewModel");
                LoggingService.LogInfo($"  • Natural display size (100% zoom): {naturalDisplayWidth:F1}x{naturalDisplayHeight:F1}", "ImageManagementViewModel");
                LoggingService.LogInfo($"  • Crop guide target: {cropGuideWidth:F0}x{cropGuideHeight:F0}", "ImageManagementViewModel");
                LoggingService.LogInfo($"  • Scale factors - X: {cropGuideScaleX:F3}, Y: {cropGuideScaleY:F3}", "ImageManagementViewModel");
                LoggingService.LogInfo($"  • Applied visual scale: {visualScale:F3}", "ImageManagementViewModel");
                LoggingService.LogInfo($"  • Final visual size: {finalVisualWidth:F1}x{finalVisualHeight:F1}", "ImageManagementViewModel");
                LoggingService.LogInfo($"  • ZoomPercentage maintained at: 100%", "ImageManagementViewModel");

                // Validate that the final visual size fits within crop guide boundaries
                if (finalVisualWidth <= cropGuideWidth + 0.1 && finalVisualHeight <= cropGuideHeight + 0.1)
                {
                    LoggingService.LogInfo("✓ Visual fit validation PASSED - Image will visually fit within crop guide boundaries", "ImageManagementViewModel");
                }
                else
                {
                    LoggingService.LogWarning($"⚠ Visual fit validation WARNING - Final visual size may exceed crop guide: {finalVisualWidth:F1}x{finalVisualHeight:F1} vs {cropGuideWidth:F0}x{cropGuideHeight:F0}", "ImageManagementViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying post-crop visual fit: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تطبيق التوضع البصري للصورة المقصوصة. سيتم استخدام العرض الافتراضي.",
                    "خطأ في التوضع البصري",
                    LogLevel.Warning,
                    "ImageManagementViewModel");

                // Fallback to no scaling on error
                PostCropVisualScale = 1.0;
            }
        }

        #endregion

        #region Real-time Crop Preview Methods

        /// <summary>
        /// Applies a real-time crop preview by replacing the current image with the cropped result.
        /// This enables sequential crop operations and immediate visual feedback.
        /// </summary>
        /// <param name="croppedImage">The cropped BitmapSource to apply as preview</param>
        /// <returns>BitmapImage for UI binding or null if conversion fails</returns>
        public BitmapImage? ApplyCropPreview(BitmapSource croppedImage)
        {
            try
            {
                LoggingService.LogInfo("Applying real-time crop preview", "ImageManagementViewModel");

                if (croppedImage == null)
                {
                    LoggingService.LogWarning("Cannot apply crop preview - cropped image is null", "ImageManagementViewModel");
                    return null;
                }

                // Convert BitmapSource to BitmapImage for ViewModel compatibility
                var croppedBitmapImage = ConvertBitmapSourceToBitmapImage(croppedImage);
                if (croppedBitmapImage == null)
                {
                    LoggingService.LogError("Failed to convert cropped image to BitmapImage for preview", "ImageManagementViewModel");
                    return null;
                }

                // Log crop operation details
                LoggingService.LogInfo($"Applying crop preview - New dimensions: {croppedBitmapImage.PixelWidth}x{croppedBitmapImage.PixelHeight}", "ImageManagementViewModel");
                if (OriginalImage != null)
                {
                    LoggingService.LogInfo($"Original image backup preserved - Dimensions: {OriginalImage.PixelWidth}x{OriginalImage.PixelHeight}", "ImageManagementViewModel");
                }

                // Replace the current image with the cropped result
                CurrentImage = croppedBitmapImage;

                // Reset transformations to default for the new cropped image (maintain 100% zoom)
                ZoomPercentage = 100.0;
                RotationAngle = 0.0;
                ImageOffsetX = 0.0;
                ImageOffsetY = 0.0;

                // Apply visual fit scaling through transform-based approach
                ApplyPostCropVisualFit(croppedBitmapImage);

                // Reset crop rectangle to default position for potential subsequent crops
                ResetCropRectangle();

                // Mark as cropped and update WYSIWYG system if enabled
                HasBeenCropped = true;

                if (_isWysiwygEnabled)
                {
                    try
                    {
                        InitializeBackendSimulation();
                    }
                    catch (Exception simEx)
                    {
                        LoggingService.LogWarning($"Failed to reinitialize backend simulation after crop: {simEx.Message}", "ImageManagementViewModel");
                    }
                }

                LoggingService.LogInfo($"Real-time crop preview applied successfully - New image size: {croppedBitmapImage.PixelWidth}x{croppedBitmapImage.PixelHeight}", "ImageManagementViewModel");
                return croppedBitmapImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying crop preview: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ أثناء تطبيق معاينة القص.",
                    "خطأ في معاينة القص",
                    LogLevel.Error,
                    "ImageManagementViewModel"
                );
                return null;
            }
        }

        /// <summary>
        /// Converts a BitmapSource to BitmapImage for ViewModel compatibility.
        /// Handles the conversion safely with proper error handling and memory management.
        /// </summary>
        /// <param name="bitmapSource">The BitmapSource to convert</param>
        /// <returns>BitmapImage or null if conversion fails</returns>
        private BitmapImage? ConvertBitmapSourceToBitmapImage(BitmapSource bitmapSource)
        {
            try
            {
                if (bitmapSource == null)
                {
                    LoggingService.LogWarning("Cannot convert null BitmapSource to BitmapImage", "ImageManagementViewModel");
                    return null;
                }

                // If it's already a BitmapImage, return it directly
                if (bitmapSource is BitmapImage bitmapImage)
                {
                    return bitmapImage;
                }

                // Create a new BitmapImage from the BitmapSource
                var convertedImage = new BitmapImage();

                using (var memoryStream = new MemoryStream())
                {
                    // Encode the BitmapSource to a stream
                    var encoder = new PngBitmapEncoder();
                    encoder.Frames.Add(BitmapFrame.Create(bitmapSource));
                    encoder.Save(memoryStream);

                    // Create BitmapImage from the stream
                    memoryStream.Position = 0;
                    convertedImage.BeginInit();
                    convertedImage.CacheOption = BitmapCacheOption.OnLoad;
                    convertedImage.StreamSource = memoryStream;
                    convertedImage.EndInit();
                    convertedImage.Freeze(); // Make it thread-safe
                }

                LoggingService.LogDebug($"Successfully converted BitmapSource to BitmapImage - Size: {convertedImage.PixelWidth}x{convertedImage.PixelHeight}", "ImageManagementViewModel");
                return convertedImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error converting BitmapSource to BitmapImage: {ex.Message}", "ImageManagementViewModel");
                return null;
            }
        }

        #endregion

        #region Image Cropping Methods

        /// <summary>
        /// Creates a cropped image based on the interactive crop rectangle and image transformations.
        /// Uses the user-defined crop area from the InteractiveCropRectangle control.
        /// Follows UFU2 patterns with comprehensive error handling and logging.
        /// </summary>
        /// <returns>Cropped BitmapSource or null if operation fails</returns>
        public BitmapSource? CreateCroppedImage()
        {
            try
            {
                LoggingService.LogInfo("Starting interactive image crop operation", "ImageManagementViewModel");

                // Validate current state
                if (CurrentImage == null)
                {
                    LoggingService.LogWarning("No image loaded for cropping", "ImageManagementViewModel");
                    ErrorManager.ShowUserErrorToast(
                        "لا توجد صورة محملة للقص. يرجى تحميل صورة أولاً.",
                        "لا توجد صورة"
                    );
                    return null;
                }

                // Validate crop rectangle
                if (CropRectangle.IsEmpty || CropRectangle.Width <= 0 || CropRectangle.Height <= 0)
                {
                    LoggingService.LogWarning("Invalid crop rectangle for cropping", "ImageManagementViewModel");
                    ErrorManager.ShowUserErrorToast(
                        "منطقة القص غير صالحة. يرجى تحديد منطقة صالحة للقص.",
                        "منطقة قص غير صالحة"
                    );
                    return null;
                }

                // Get current image dimensions
                var imageSize = new Size(CurrentImage.PixelWidth, CurrentImage.PixelHeight);
                var displaySize = new Size(500, 320); // Preview container size

                // Validate image parameters
                if (!CoordinateTransformHelper.ValidateImageParameters(
                    imageSize.Width, imageSize.Height, displaySize.Width, displaySize.Height,
                    ZoomScale, RotationAngle))
                {
                    LoggingService.LogError("Invalid image parameters for cropping", "ImageManagementViewModel");
                    ErrorManager.ShowUserErrorToast(
                        "معاملات الصورة غير صالحة للقص. يرجى التحقق من الصورة والمحاولة مرة أخرى.",
                        "معاملات غير صالحة"
                    );
                    return null;
                }

                // Initialize backend simulation if not already done
                if (_isWysiwygEnabled && (_backendSynchronizer == null || _coordinateMapper == null))
                {
                    InitializeBackendSimulation();
                }

                // Use WYSIWYG cropping if available, otherwise fall back to standard
                BitmapSource? croppedImage;
                if (_isWysiwygEnabled && _backendSynchronizer != null && _coordinateMapper != null)
                {
                    LoggingService.LogInfo("Using WYSIWYG cropping system", "ImageManagementViewModel");
                    croppedImage = CreateWYSIWYGCroppedImage();
                }
                else
                {
                    LoggingService.LogInfo("Using standard cropping system", "ImageManagementViewModel");
                    croppedImage = CreateCroppedImageStandard();
                }

                if (croppedImage != null)
                {
                    // Mark that the image has been cropped
                    HasBeenCropped = true;
                    LoggingService.LogInfo($"Image cropped successfully using interactive rectangle - New size: {croppedImage.PixelWidth}x{croppedImage.PixelHeight}", "ImageManagementViewModel");
                }
                else
                {
                    LoggingService.LogError("Failed to create cropped image", "ImageManagementViewModel");
                }

                return croppedImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating cropped image: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء قص الصورة. يرجى التحقق من منطقة القص والمحاولة مرة أخرى.",
                    "خطأ في قص الصورة",
                    LogLevel.Error,
                    "ImageManagementViewModel");
                return null;
            }
        }

        /// <summary>
        /// Creates a cropped bitmap from the source image using the specified crop area and rotation.
        /// IMPORTANT: For WYSIWYG accuracy, rotation is applied to the source image BEFORE cropping,
        /// and crop coordinates are expected to be in the original (unrotated) image coordinate system.
        /// This ensures consistent 127×145 output dimensions regardless of rotation angle.
        /// </summary>
        /// <param name="sourceImage">Source image to crop</param>
        /// <param name="cropArea">Area to crop in original image coordinates (unrotated)</param>
        /// <param name="rotationAngle">Rotation angle to apply</param>
        /// <returns>Cropped BitmapSource or null if operation fails</returns>
        private BitmapSource? CreateCroppedBitmap(BitmapSource sourceImage, Rect cropArea, double rotationAngle)
        {
            try
            {
                LoggingService.LogDebug($"Creating WYSIWYG cropped bitmap - Source: {sourceImage.PixelWidth}x{sourceImage.PixelHeight}, " +
                    $"Crop (unrotated coords): {cropArea}, Rotation: {rotationAngle:F1}°", "ImageManagementViewModel");

                // Step 1: Apply rotation to the source image FIRST (if needed)
                BitmapSource processedImage = sourceImage;
                Rect finalCropArea = cropArea;

                if (Math.Abs(rotationAngle) > 0.1)
                {
                    LoggingService.LogDebug($"Applying {rotationAngle:F1}° rotation to source image before cropping (arbitrary angle support)", "ImageManagementViewModel");

                    // Use RenderTargetBitmap for arbitrary rotation angle support (not limited to 90° multiples)
                    processedImage = CreateRotatedBitmap(sourceImage, rotationAngle);

                    if (processedImage == null)
                    {
                        LoggingService.LogError($"Failed to create rotated bitmap for angle {rotationAngle:F1}°", "ImageManagementViewModel");
                        return null;
                    }

                    // For rotated images, we need to adjust the crop coordinates to account for the rotation
                    // The crop area is still in the original coordinate system, but we need to map it to the rotated image
                    finalCropArea = CalculateRotatedCropArea(cropArea, sourceImage.PixelWidth, sourceImage.PixelHeight, rotationAngle);

                    LoggingService.LogDebug($"Rotated image size: {processedImage.PixelWidth}x{processedImage.PixelHeight}, " +
                        $"Adjusted crop area: {finalCropArea}", "ImageManagementViewModel");
                }

                // Step 2: Ensure crop area is within the processed image bounds
                var imageBounds = new Rect(0, 0, processedImage.PixelWidth, processedImage.PixelHeight);
                var validCropArea = CropGuideCalculator.ValidateCropArea(finalCropArea, imageBounds);

                LoggingService.LogDebug($"Final validated crop area (before precision): {validCropArea}", "ImageManagementViewModel");

                // Step 3: Apply coordinate precision and rounding for clean integer values
                var precisionCropArea = ApplyCoordinatePrecision(validCropArea);

                LoggingService.LogDebug($"Final crop area (after precision): {precisionCropArea}", "ImageManagementViewModel");

                // Step 4: Create cropped bitmap from the processed (potentially rotated) image
                var croppedBitmap = new CroppedBitmap(processedImage,
                    new Int32Rect(
                        (int)Math.Max(0, precisionCropArea.X),
                        (int)Math.Max(0, precisionCropArea.Y),
                        (int)Math.Min(processedImage.PixelWidth - precisionCropArea.X, precisionCropArea.Width),
                        (int)Math.Min(processedImage.PixelHeight - precisionCropArea.Y, precisionCropArea.Height)));

                // Freeze the bitmap for performance and thread safety
                croppedBitmap.Freeze();

                LoggingService.LogInfo($"WYSIWYG cropped bitmap created successfully - Final size: {croppedBitmap.PixelWidth}x{croppedBitmap.PixelHeight}",
                    "ImageManagementViewModel");

                return croppedBitmap;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating WYSIWYG cropped bitmap: {ex.Message}", "ImageManagementViewModel");
                return null;
            }
        }

        /// <summary>
        /// Applies coordinate precision and rounding to ensure clean integer values in crop operations.
        /// Uses Math.Floor() for X coordinates and intelligent rounding for Y coordinates based on decimal parts.
        /// This prevents fractional pixel coordinates that can cause cropping errors.
        /// </summary>
        /// <param name="cropArea">Original crop area with potentially fractional coordinates</param>
        /// <returns>Crop area with precise integer coordinates</returns>
        private Rect ApplyCoordinatePrecision(Rect cropArea)
        {
            try
            {
                LoggingService.LogDebug($"Applying coordinate precision to crop area: {cropArea}", "ImageManagementViewModel");

                // Apply Math.Floor() to X coordinates as specified
                double preciseX = Math.Floor(cropArea.X);
                double preciseWidth = Math.Floor(cropArea.Width);

                // Apply intelligent rounding to Y coordinates based on decimal parts
                double preciseY = ApplyIntelligentRounding(cropArea.Y);
                double preciseHeight = ApplyIntelligentRounding(cropArea.Height);

                var preciseCropArea = new Rect(preciseX, preciseY, preciseWidth, preciseHeight);

                LoggingService.LogDebug($"Applied coordinate precision - Original: {cropArea}, Precise: {preciseCropArea}",
                    "ImageManagementViewModel");

                return preciseCropArea;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying coordinate precision: {ex.Message}", "ImageManagementViewModel");
                // Fallback to original crop area
                return cropArea;
            }
        }

        /// <summary>
        /// Applies intelligent rounding to a coordinate value based on its decimal part.
        /// If decimal part >= 0.5: use Math.Ceiling()
        /// If decimal part < 0.5: use Math.Floor()
        /// </summary>
        /// <param name="value">Coordinate value to round</param>
        /// <returns>Intelligently rounded coordinate value</returns>
        private double ApplyIntelligentRounding(double value)
        {
            try
            {
                double decimalPart = value - Math.Floor(value);

                if (decimalPart >= 0.5)
                {
                    return Math.Ceiling(value);
                }
                else
                {
                    return Math.Floor(value);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying intelligent rounding to value {value}: {ex.Message}", "ImageManagementViewModel");
                // Fallback to simple rounding
                return Math.Round(value);
            }
        }

        /// <summary>
        /// Creates a rotated bitmap using RenderTargetBitmap to support arbitrary rotation angles.
        /// This method replaces TransformedBitmap which only supports 90° multiples and specific transforms.
        /// Supports any rotation angle from 0° to 360° with high image quality preservation.
        /// </summary>
        /// <param name="sourceImage">Source image to rotate</param>
        /// <param name="rotationAngle">Rotation angle in degrees (supports arbitrary angles)</param>
        /// <returns>Rotated BitmapSource or null if operation fails</returns>
        private BitmapSource? CreateRotatedBitmap(BitmapSource sourceImage, double rotationAngle)
        {
            try
            {
                LoggingService.LogDebug($"Creating rotated bitmap - Source: {sourceImage.PixelWidth}x{sourceImage.PixelHeight}, " +
                    $"Angle: {rotationAngle:F1}° (arbitrary angle support)", "ImageManagementViewModel");

                // Calculate the rotated image dimensions
                double radians = rotationAngle * Math.PI / 180.0;
                double cos = Math.Abs(Math.Cos(radians));
                double sin = Math.Abs(Math.Sin(radians));

                int rotatedWidth = (int)Math.Ceiling(sourceImage.PixelWidth * cos + sourceImage.PixelHeight * sin);
                int rotatedHeight = (int)Math.Ceiling(sourceImage.PixelWidth * sin + sourceImage.PixelHeight * cos);

                LoggingService.LogDebug($"Calculated rotated dimensions: {rotatedWidth}x{rotatedHeight}", "ImageManagementViewModel");

                // Create a DrawingVisual to render the rotated image
                var drawingVisual = new DrawingVisual();
                using (var drawingContext = drawingVisual.RenderOpen())
                {
                    // Set up the rotation transform around the center of the new canvas
                    var rotateTransform = new RotateTransform(rotationAngle, rotatedWidth / 2.0, rotatedHeight / 2.0);

                    // Calculate the position to center the original image in the rotated canvas
                    double offsetX = (rotatedWidth - sourceImage.PixelWidth) / 2.0;
                    double offsetY = (rotatedHeight - sourceImage.PixelHeight) / 2.0;

                    drawingContext.PushTransform(rotateTransform);

                    // Draw the source image at the calculated offset to center it
                    var imageRect = new Rect(offsetX, offsetY, sourceImage.PixelWidth, sourceImage.PixelHeight);
                    drawingContext.DrawImage(sourceImage, imageRect);

                    drawingContext.Pop();
                }

                // Render the DrawingVisual to a RenderTargetBitmap
                var renderTargetBitmap = new RenderTargetBitmap(
                    rotatedWidth, rotatedHeight,
                    96, 96, // DPI
                    PixelFormats.Pbgra32);

                renderTargetBitmap.Render(drawingVisual);

                // Freeze the bitmap for performance and thread safety
                renderTargetBitmap.Freeze();

                LoggingService.LogDebug($"Successfully created rotated bitmap: {renderTargetBitmap.PixelWidth}x{renderTargetBitmap.PixelHeight}",
                    "ImageManagementViewModel");

                return renderTargetBitmap;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating rotated bitmap for angle {rotationAngle:F1}°: {ex.Message}", "ImageManagementViewModel");
                return null;
            }
        }

        /// <summary>
        /// Calculates the crop area coordinates for a rotated image.
        /// Maps crop coordinates from the original image space to the rotated image space.
        /// </summary>
        /// <param name="originalCropArea">Crop area in original image coordinates</param>
        /// <param name="originalWidth">Original image width</param>
        /// <param name="originalHeight">Original image height</param>
        /// <param name="rotationAngle">Rotation angle in degrees</param>
        /// <returns>Crop area adjusted for the rotated image</returns>
        private Rect CalculateRotatedCropArea(Rect originalCropArea, int originalWidth, int originalHeight, double rotationAngle)
        {
            try
            {
                LoggingService.LogDebug($"Calculating rotated crop area - Original: {originalCropArea}, " +
                    $"Image: {originalWidth}x{originalHeight}, Rotation: {rotationAngle:F1}°", "ImageManagementViewModel");

                // For now, use a simplified approach that works for most rotation angles
                // This can be enhanced with more sophisticated coordinate transformation if needed

                // Calculate the center of the original image
                double centerX = originalWidth / 2.0;
                double centerY = originalHeight / 2.0;

                // Calculate the center of the crop area
                double cropCenterX = originalCropArea.X + originalCropArea.Width / 2.0;
                double cropCenterY = originalCropArea.Y + originalCropArea.Height / 2.0;

                // For rotated images, we need to account for the fact that the rotated image
                // has different dimensions and the crop area needs to be repositioned
                double radians = rotationAngle * Math.PI / 180.0;
                double cos = Math.Cos(radians);
                double sin = Math.Sin(radians);

                // Calculate rotated image dimensions
                double rotatedWidth = Math.Abs(originalWidth * cos) + Math.Abs(originalHeight * sin);
                double rotatedHeight = Math.Abs(originalWidth * sin) + Math.Abs(originalHeight * cos);

                // Calculate the offset from the original center to the rotated center
                double rotatedCenterX = rotatedWidth / 2.0;
                double rotatedCenterY = rotatedHeight / 2.0;

                // Transform the crop center to the rotated coordinate system
                double relativeCropX = cropCenterX - centerX;
                double relativeCropY = cropCenterY - centerY;

                double rotatedCropCenterX = relativeCropX * cos - relativeCropY * sin + rotatedCenterX;
                double rotatedCropCenterY = relativeCropX * sin + relativeCropY * cos + rotatedCenterY;

                // Calculate the final crop area in rotated coordinates
                var rotatedCropArea = new Rect(
                    rotatedCropCenterX - originalCropArea.Width / 2.0,
                    rotatedCropCenterY - originalCropArea.Height / 2.0,
                    originalCropArea.Width,
                    originalCropArea.Height);

                LoggingService.LogDebug($"Calculated rotated crop area: {rotatedCropArea}", "ImageManagementViewModel");

                return rotatedCropArea;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating rotated crop area: {ex.Message}", "ImageManagementViewModel");
                // Fallback to original crop area
                return originalCropArea;
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ImageManagementViewModel.
        /// Sets up the initial state with no image loaded and default zoom/rotation values.
        /// Includes comprehensive error handling and state validation.
        /// </summary>
        public ImageManagementViewModel()
        {
            try
            {
                LoggingService.LogInfo("Initializing ImageManagementViewModel", "ImageManagementViewModel");

                // Initialize commands
                ZoomInCommand = new RelayCommand(ExecuteZoomIn, CanExecuteZoomIn, "ZoomIn");
                ZoomOutCommand = new RelayCommand(ExecuteZoomOut, CanExecuteZoomOut, "ZoomOut");
                RotateClockwiseCommand = new RelayCommand(ExecuteRotateClockwise, CanExecuteRotateClockwise, "RotateClockwise");
                RotateCounterclockwiseCommand = new RelayCommand(ExecuteRotateCounterclockwise, CanExecuteRotateCounterclockwise, "RotateCounterclockwise");

                // Initialize image state with validation
                _isImageLoaded = false;
                _isLoading = false;
                _currentImage = null;

                // Initialize zoom and rotation to default values with validation
                _zoomPercentage = DefaultZoomPercentage;
                _rotationAngle = DefaultRotationAngle;

                // Initialize performance monitoring fields
                _sliderOperationStopwatch = null;
                _lastSliderUpdate = DateTime.MinValue;

                // Initialize WYSIWYG backend simulation
                _backendSynchronizer = null;
                _coordinateMapper = null;
                _isWysiwygEnabled = true;

                // Validate initial state
                try
                {
                    ValidateAndCorrectTransforms();
                }
                catch (Exception validationEx)
                {
                    LoggingService.LogWarning($"Error during initial state validation: {validationEx.Message}", "ImageManagementViewModel");

                    // Ensure safe fallback values
                    _zoomPercentage = DefaultZoomPercentage;
                    _rotationAngle = DefaultRotationAngle;
                }

                LoggingService.LogInfo($"ImageManagementViewModel initialized successfully - Zoom: {_zoomPercentage}%, Rotation: {_rotationAngle}°", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error initializing ImageManagementViewModel: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء تهيئة نموذج إدارة الصور. قد تواجه مشاكل في وظائف الصورة.", "خطأ في التهيئة", LogLevel.Error, "ImageManagementViewModel");

                // Ensure safe fallback state even on critical error
                try
                {
                    _isImageLoaded = false;
                    _isLoading = false;
                    _currentImage = null;
                    _zoomPercentage = DefaultZoomPercentage;
                    _rotationAngle = DefaultRotationAngle;
                    _sliderOperationStopwatch = null;
                    _lastSliderUpdate = DateTime.MinValue;
                    _backendSynchronizer = null;
                    _coordinateMapper = null;
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Failed to set fallback state during initialization: {fallbackEx.Message}", "ImageManagementViewModel");
                }
            }
        }

        #endregion

        #region Zoom Command Methods

        /// <summary>
        /// Executes the zoom in command.
        /// Increases zoom by the specified step size with bounds checking.
        /// </summary>
        private void ExecuteZoomIn()
        {
            try
            {
                var newZoom = ZoomPercentage + ZoomStepSize;

                // Check bounds before applying
                if (newZoom > MaxZoomPercentage)
                {
                    LoggingService.LogInfo($"Zoom in blocked: would exceed maximum zoom ({MaxZoomPercentage}%)", "ImageManagementViewModel");
                    ErrorManager.ShowUserErrorToast($"لا يمكن التكبير أكثر من {MaxZoomPercentage}%", "حد التكبير الأقصى");
                    return;
                }

                ZoomPercentage = newZoom;
                LoggingService.LogInfo($"Zoomed in by {ZoomStepSize}% to {ZoomPercentage}%", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing zoom in command: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.ShowUserErrorToast("حدث خطأ أثناء تكبير الصورة", "خطأ في التكبير");
            }
        }

        /// <summary>
        /// Determines whether the zoom in command can execute.
        /// </summary>
        /// <returns>True if an image is loaded, not currently loading, and zoom can be increased</returns>
        private bool CanExecuteZoomIn()
        {
            return IsImageLoaded && !IsLoading && ZoomPercentage < MaxZoomPercentage;
        }

        /// <summary>
        /// Executes the zoom out command.
        /// Decreases zoom by the specified step size with bounds checking.
        /// </summary>
        private void ExecuteZoomOut()
        {
            try
            {
                var newZoom = ZoomPercentage - ZoomStepSize;

                // Check bounds before applying
                if (newZoom < MinZoomPercentage)
                {
                    LoggingService.LogInfo($"Zoom out blocked: would go below minimum zoom ({MinZoomPercentage}%)", "ImageManagementViewModel");
                    ErrorManager.ShowUserErrorToast($"لا يمكن التصغير أقل من {MinZoomPercentage}%", "حد التصغير الأدنى");
                    return;
                }

                ZoomPercentage = newZoom;
                LoggingService.LogInfo($"Zoomed out by {ZoomStepSize}% to {ZoomPercentage}%", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing zoom out command: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.ShowUserErrorToast("حدث خطأ أثناء تصغير الصورة", "خطأ في التصغير");
            }
        }

        /// <summary>
        /// Determines whether the zoom out command can execute.
        /// </summary>
        /// <returns>True if an image is loaded, not currently loading, and zoom can be decreased</returns>
        private bool CanExecuteZoomOut()
        {
            return IsImageLoaded && !IsLoading && ZoomPercentage > MinZoomPercentage;
        }

        #endregion

        #region Rotation Command Methods

        /// <summary>
        /// Executes the rotate clockwise command.
        /// Rotates the image by the specified step size in the positive direction.
        /// </summary>
        private void ExecuteRotateClockwise()
        {
            try
            {
                var newAngle = RotationAngle + RotationStepSize;

                // Ensure we stay within the -180 to +180 range
                if (newAngle > 180)
                {
                    newAngle = newAngle - 360;
                }

                RotationAngle = newAngle;
                LoggingService.LogInfo($"Rotated clockwise by {RotationStepSize}° to {RotationAngle}°", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing rotate clockwise command: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.ShowUserErrorToast("حدث خطأ أثناء دوران الصورة في اتجاه عقارب الساعة", "خطأ في الدوران");
            }
        }

        /// <summary>
        /// Determines whether the rotate clockwise command can execute.
        /// </summary>
        /// <returns>True if an image is loaded and not currently loading</returns>
        private bool CanExecuteRotateClockwise()
        {
            return IsImageLoaded && !IsLoading;
        }

        /// <summary>
        /// Executes the rotate counterclockwise command.
        /// Rotates the image by the specified step size in the negative direction.
        /// </summary>
        private void ExecuteRotateCounterclockwise()
        {
            try
            {
                var newAngle = RotationAngle - RotationStepSize;

                // Ensure we stay within the -180 to +180 range
                if (newAngle < -180)
                {
                    newAngle = newAngle + 360;
                }

                RotationAngle = newAngle;
                LoggingService.LogInfo($"Rotated counterclockwise by {RotationStepSize}° to {RotationAngle}°", "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing rotate counterclockwise command: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.ShowUserErrorToast("حدث خطأ أثناء دوران الصورة عكس اتجاه عقارب الساعة", "خطأ في الدوران");
            }
        }

        /// <summary>
        /// Determines whether the rotate counterclockwise command can execute.
        /// </summary>
        /// <returns>True if an image is loaded and not currently loading</returns>
        private bool CanExecuteRotateCounterclockwise()
        {
            return IsImageLoaded && !IsLoading;
        }

        #endregion

        #region WYSIWYG Backend Simulation

        /// <summary>
        /// Gets or sets whether WYSIWYG backend simulation is enabled.
        /// When enabled, provides pixel-perfect accuracy between preview and final crop output.
        /// </summary>
        public bool IsWysiwygEnabled
        {
            get => _isWysiwygEnabled;
            set
            {
                if (SetProperty(ref _isWysiwygEnabled, value))
                {
                    LoggingService.LogInfo($"WYSIWYG mode {(value ? "enabled" : "disabled")}", "ImageManagementViewModel");

                    // Re-initialize backend simulation if enabling and image is loaded
                    if (value && CurrentImage != null)
                    {
                        try
                        {
                            InitializeBackendSimulation();
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogWarning($"Failed to initialize backend simulation when enabling WYSIWYG: {ex.Message}",
                                "ImageManagementViewModel");
                        }
                    }
                    else if (!value)
                    {
                        // Clear backend simulation when disabling
                        _backendSynchronizer = null;
                        _coordinateMapper = null;
                    }
                }
            }
        }

        /// <summary>
        /// Initializes the backend simulation system for WYSIWYG functionality.
        /// Must be called after loading an image to enable precise coordinate mapping.
        /// </summary>
        public void InitializeBackendSimulation()
        {
            try
            {
                if (CurrentImage == null)
                {
                    LoggingService.LogWarning("Cannot initialize backend simulation without a loaded image", "ImageManagementViewModel");
                    return;
                }

                var imageSize = new Size(CurrentImage.PixelWidth, CurrentImage.PixelHeight);

                // Initialize backend synchronizer
                _backendSynchronizer = new PreviewBackendSynchronizer(imageSize);
                _coordinateMapper = new CoordinateMapper(_backendSynchronizer);

                LoggingService.LogInfo($"Backend simulation initialized for {imageSize.Width}x{imageSize.Height} image - " +
                    $"Backend container: {_backendSynchronizer.BackendContainer.BackendSize.Width}x{_backendSynchronizer.BackendContainer.BackendSize.Height}",
                    "ImageManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing backend simulation: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تهيئة نظام المحاكاة الخلفية. قد تتأثر دقة القص.",
                    "خطأ في تهيئة المحاكاة",
                    LogLevel.Warning,
                    "ImageManagementViewModel");

                // Disable WYSIWYG on error
                _isWysiwygEnabled = false;
            }
        }

        /// <summary>
        /// Creates a WYSIWYG cropped image that exactly matches what the user sees in the preview.
        /// Uses the backend simulation system for pixel-perfect accuracy.
        /// </summary>
        /// <returns>Cropped bitmap source or null if operation fails</returns>
        public BitmapSource? CreateWYSIWYGCroppedImage()
        {
            try
            {
                if (!_isWysiwygEnabled || _backendSynchronizer == null || _coordinateMapper == null || CurrentImage == null)
                {
                    LoggingService.LogWarning("WYSIWYG system not available, falling back to standard cropping", "ImageManagementViewModel");
                    return CreateCroppedImageStandard();
                }

                LoggingService.LogInfo("Creating WYSIWYG cropped image", "ImageManagementViewModel");

                // Get current transformation state
                var backendState = _backendSynchronizer.SynchronizeTransformations(
                    ZoomPercentage,
                    RotationAngle,
                    new Point(ImageOffsetX, ImageOffsetY),
                    CropRectangle);

                // Map crop rectangle to exact image coordinates
                var imageCropArea = _coordinateMapper.MapCropRectangleToImageCoordinates(
                    CropRectangle, backendState);

                LoggingService.LogDebug($"WYSIWYG crop area calculated: {imageCropArea}", "ImageManagementViewModel");

                // Validate the transformation
                var validation = TransformationValidator.ValidateTransformation(
                    new Size(CurrentImage.PixelWidth, CurrentImage.PixelHeight),
                    backendState,
                    imageCropArea);

                if (!validation.IsValid)
                {
                    LoggingService.LogWarning($"Transformation validation failed: {string.Join(", ", validation.Errors)}",
                        "ImageManagementViewModel");

                    // Show user-friendly warning message with clear guidance
                    if (validation.Errors.Count > 0)
                    {
                        ErrorManager.ShowUserWarningToast(
                            "لا يمكن قص الصورة - المنطقة المحددة تتجاوز حدود الصورة. يرجى تعديل منطقة القص أو موضع الصورة",
                            "تحذير من حدود القص",
                            "ImageManagementViewModel"
                        );
                    }

                    // Log that crop operation was skipped due to validation failure
                    LoggingService.LogInfo("WYSIWYG crop operation skipped due to validation failure - preserving user state and UI responsiveness", "ImageManagementViewModel");

                    // ENHANCED UX: Preserve all user state and maintain UI responsiveness
                    // - Keep all current transformations (zoom, rotation, drag positions)
                    // - Maintain crop rectangle visibility for user adjustment
                    // - Ensure all UI controls remain functional
                    // - Allow immediate retry after user adjustments
                    // - Do NOT modify HasBeenCropped flag or any other state variables

                    // Return null to skip only the crop execution while preserving everything else
                    return null;
                }

                // Show warnings to user if any
                if (validation.Warnings.Count > 0)
                {
                    LoggingService.LogInfo($"Transformation warnings: {string.Join(", ", validation.Warnings)}",
                        "ImageManagementViewModel");
                }

                // Create the cropped image with exact WYSIWYG accuracy
                var croppedImage = CreateCroppedBitmap(CurrentImage, imageCropArea, RotationAngle);

                if (croppedImage != null)
                {
                    HasBeenCropped = true;
                    LoggingService.LogInfo($"WYSIWYG cropped image created successfully - Size: {croppedImage.PixelWidth}x{croppedImage.PixelHeight}",
                        "ImageManagementViewModel");
                }
                else
                {
                    LoggingService.LogError("Failed to create WYSIWYG cropped image", "ImageManagementViewModel");
                    ErrorManager.ShowUserErrorToast(
                        "فشل في إنشاء الصورة المقصوصة بدقة WYSIWYG. يرجى المحاولة مرة أخرى.",
                        "فشل في القص"
                    );
                }

                return croppedImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating WYSIWYG cropped image: {ex.Message}", "ImageManagementViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء إنشاء الصورة المقصوصة بدقة WYSIWYG. سيتم استخدام الطريقة التقليدية.",
                    "خطأ في القص المتقدم",
                    LogLevel.Warning,
                    "ImageManagementViewModel");

                // Fallback to standard cropping
                return CreateCroppedImageStandard();
            }
        }

        /// <summary>
        /// Fallback method for standard cropping when WYSIWYG is not available
        /// </summary>
        private BitmapSource? CreateCroppedImageStandard()
        {
            try
            {
                if (CurrentImage == null)
                    return null;

                LoggingService.LogInfo("Using standard cropping method", "ImageManagementViewModel");

                // Use existing cropping logic
                var imageSize = new Size(CurrentImage.PixelWidth, CurrentImage.PixelHeight);
                var displaySize = new Size(500, 320); // Corrected preview container size

                var imageCropArea = CropGuideCalculator.CalculateImageCropArea(
                    CropRectangle, imageSize, displaySize, ZoomScale, RotationAngle);

                // Validate the transformation for consistency with WYSIWYG method
                var backendState = new BackendTransformState
                {
                    ZoomScale = ZoomPercentage / 100.0, // Convert percentage to scale
                    RotationAngle = RotationAngle,
                    DragOffset = new Point(ImageOffsetX, ImageOffsetY),
                    CropRectangle = CropRectangle,
                    ImageDisplaySize = new Size(imageSize.Width * (ZoomPercentage / 100.0), imageSize.Height * (ZoomPercentage / 100.0)),
                    PreviewImageProperties = new PreviewImageProperties
                    {
                        DisplayScale = ZoomPercentage / 100.0,
                        DisplaySize = displaySize,
                        CenterOffset = new Point(0, 0),
                        ZoomScale = ZoomPercentage / 100.0
                    }
                };

                var validation = TransformationValidator.ValidateTransformation(
                    imageSize, backendState, imageCropArea);

                if (!validation.IsValid)
                {
                    LoggingService.LogWarning($"Standard cropping validation failed: {string.Join(", ", validation.Errors)}",
                        "ImageManagementViewModel");

                    // Show user-friendly warning message with clear guidance
                    if (validation.Errors.Count > 0)
                    {
                        ErrorManager.ShowUserWarningToast(
                            "لا يمكن قص الصورة - المنطقة المحددة تتجاوز حدود الصورة. يرجى تعديل منطقة القص أو موضع الصورة",
                            "تحذير من حدود القص",
                            "ImageManagementViewModel"
                        );
                    }

                    // Log that crop operation was skipped due to validation failure
                    LoggingService.LogInfo("Standard crop operation skipped due to validation failure - preserving user state and UI responsiveness", "ImageManagementViewModel");

                    // ENHANCED UX: Preserve all user state and maintain UI responsiveness
                    // - Keep all current transformations (zoom, rotation, drag positions)
                    // - Maintain crop rectangle visibility for user adjustment
                    // - Ensure all UI controls remain functional
                    // - Allow immediate retry after user adjustments
                    // - Do NOT modify HasBeenCropped flag or any other state variables

                    // Return null to skip only the crop execution while preserving everything else
                    return null;
                }

                return CreateCroppedBitmap(CurrentImage, imageCropArea, RotationAngle);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in standard cropping: {ex.Message}", "ImageManagementViewModel");
                return null;
            }
        }

        /// <summary>
        /// Tests arbitrary rotation support to ensure the CreateRotatedBitmap method works with non-90° angles.
        /// This validates the fix for the TransformedBitmap limitation that caused errors with arbitrary angles.
        /// </summary>
        /// <returns>True if arbitrary rotation support works correctly</returns>
        private bool TestArbitraryRotationSupport()
        {
            try
            {
                if (CurrentImage == null)
                {
                    LoggingService.LogWarning("Cannot test arbitrary rotation support - no image loaded", "ImageManagementViewModel");
                    return false;
                }

                LoggingService.LogInfo("Testing arbitrary rotation support (non-90° multiples)", "ImageManagementViewModel");

                // Test problematic angles that previously failed with TransformedBitmap
                var problematicAngles = new double[] { 34.3, 67.8, 123.4, 156.7, 234.5, 289.1, 345.6 };
                bool allTestsPassed = true;
                int testCount = 0;
                int passedTests = 0;

                foreach (var angle in problematicAngles)
                {
                    testCount++;

                    try
                    {
                        LoggingService.LogDebug($"Testing arbitrary rotation at {angle:F1}°", "ImageManagementViewModel");

                        // Test the CreateRotatedBitmap method directly
                        var rotatedBitmap = CreateRotatedBitmap(CurrentImage, angle);

                        if (rotatedBitmap != null)
                        {
                            // Validate the rotated bitmap
                            if (rotatedBitmap.PixelWidth > 0 && rotatedBitmap.PixelHeight > 0)
                            {
                                passedTests++;
                                LoggingService.LogDebug($"Arbitrary rotation test PASSED for {angle:F1}° - " +
                                    $"Output: {rotatedBitmap.PixelWidth}x{rotatedBitmap.PixelHeight}", "ImageManagementViewModel");
                            }
                            else
                            {
                                allTestsPassed = false;
                                LoggingService.LogWarning($"Arbitrary rotation test FAILED for {angle:F1}° - " +
                                    $"Invalid output dimensions: {rotatedBitmap.PixelWidth}x{rotatedBitmap.PixelHeight}", "ImageManagementViewModel");
                            }
                        }
                        else
                        {
                            allTestsPassed = false;
                            LoggingService.LogWarning($"Arbitrary rotation test FAILED for {angle:F1}° - " +
                                $"CreateRotatedBitmap returned null", "ImageManagementViewModel");
                        }
                    }
                    catch (Exception ex)
                    {
                        allTestsPassed = false;
                        LoggingService.LogError($"Arbitrary rotation test FAILED for {angle:F1}° - Exception: {ex.Message}", "ImageManagementViewModel");
                    }
                }

                LoggingService.LogInfo($"Arbitrary rotation support testing completed - {passedTests}/{testCount} tests passed",
                    "ImageManagementViewModel");

                return allTestsPassed;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during arbitrary rotation support testing: {ex.Message}", "ImageManagementViewModel");
                return false;
            }
        }

        /// <summary>
        /// Tests crop coordinate accuracy to ensure consistent output dimensions regardless of rotation.
        /// This validates the core fix for the rotation-crop coordinate system issue.
        /// </summary>
        /// <param name="testRotationAngles">Array of rotation angles to test</param>
        /// <returns>True if crop coordinates produce consistent results</returns>
        private bool TestCropCoordinateAccuracy(double[]? testRotationAngles = null)
        {
            try
            {
                LoggingService.LogInfo("Testing crop coordinate accuracy across rotation angles", "ImageManagementViewModel");

                var rotationAngles = testRotationAngles ?? new double[] { 0, 45, 90, 135, 180, 225, 270, 315 };
                bool allTestsPassed = true;
                int testCount = 0;
                int passedTests = 0;

                // Store original values
                var originalRotation = RotationAngle;
                var originalCropRect = CropRectangle;

                // Use a fixed crop rectangle for testing
                var testCropRect = new Rect(150, 50, 127, 145); // Standard profile image dimensions

                foreach (var rotation in rotationAngles)
                {
                    testCount++;

                    try
                    {
                        // Set test values
                        _rotationAngle = rotation;
                        _cropRectangle = testCropRect;

                        // Test coordinate mapping
                        if (_isWysiwygEnabled && _backendSynchronizer != null && _coordinateMapper != null)
                        {
                            var backendState = _backendSynchronizer.SynchronizeTransformations(
                                ZoomPercentage, rotation, new Point(ImageOffsetX, ImageOffsetY), testCropRect);

                            var imageCropArea = _coordinateMapper.MapCropRectangleToImageCoordinates(
                                testCropRect, backendState);

                            // Validate that the mapped coordinates are reasonable
                            bool testPassed = ValidateCropCoordinateMapping(rotation, testCropRect, imageCropArea);

                            if (testPassed)
                            {
                                passedTests++;
                                LoggingService.LogDebug($"Crop coordinate test PASSED for {rotation:F1}° - " +
                                    $"Input: {testCropRect}, Mapped: {imageCropArea}", "ImageManagementViewModel");
                            }
                            else
                            {
                                allTestsPassed = false;
                                LoggingService.LogWarning($"Crop coordinate test FAILED for {rotation:F1}° - " +
                                    $"Input: {testCropRect}, Mapped: {imageCropArea}", "ImageManagementViewModel");
                            }
                        }
                        else
                        {
                            LoggingService.LogWarning("WYSIWYG system not available for crop coordinate testing", "ImageManagementViewModel");
                            testCount--; // Don't count this test
                        }
                    }
                    finally
                    {
                        // Restore original values
                        _rotationAngle = originalRotation;
                        _cropRectangle = originalCropRect;
                    }
                }

                LoggingService.LogInfo($"Crop coordinate accuracy testing completed - {passedTests}/{testCount} tests passed",
                    "ImageManagementViewModel");

                return allTestsPassed;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during crop coordinate accuracy testing: {ex.Message}", "ImageManagementViewModel");
                return false;
            }
        }

        /// <summary>
        /// Validates that crop coordinate mapping produces reasonable results.
        /// </summary>
        /// <param name="rotationAngle">Rotation angle used in test</param>
        /// <param name="inputCropRect">Original crop rectangle</param>
        /// <param name="mappedCropRect">Mapped crop rectangle</param>
        /// <returns>True if the mapping is valid</returns>
        private bool ValidateCropCoordinateMapping(double rotationAngle, Rect inputCropRect, Rect mappedCropRect)
        {
            try
            {
                // Check for NaN or infinite values
                if (double.IsNaN(mappedCropRect.X) || double.IsInfinity(mappedCropRect.X) ||
                    double.IsNaN(mappedCropRect.Y) || double.IsInfinity(mappedCropRect.Y) ||
                    double.IsNaN(mappedCropRect.Width) || double.IsInfinity(mappedCropRect.Width) ||
                    double.IsNaN(mappedCropRect.Height) || double.IsInfinity(mappedCropRect.Height))
                {
                    LoggingService.LogError("Invalid crop coordinate mapping - NaN or Infinity detected", "ImageManagementViewModel");
                    return false;
                }

                // Check that dimensions are positive
                if (mappedCropRect.Width <= 0 || mappedCropRect.Height <= 0)
                {
                    LoggingService.LogError("Invalid crop coordinate mapping - Non-positive dimensions", "ImageManagementViewModel");
                    return false;
                }

                // For the coordinate system fix, the key test is that the aspect ratio should be preserved
                // regardless of rotation (since we're not rotating the crop coordinates anymore)
                double inputAspectRatio = inputCropRect.Width / inputCropRect.Height;
                double mappedAspectRatio = mappedCropRect.Width / mappedCropRect.Height;
                double aspectRatioTolerance = 0.1; // 10% tolerance

                if (Math.Abs(inputAspectRatio - mappedAspectRatio) > aspectRatioTolerance)
                {
                    LoggingService.LogWarning($"Crop coordinate mapping may have aspect ratio issues - " +
                        $"Input: {inputAspectRatio:F3}, Mapped: {mappedAspectRatio:F3}", "ImageManagementViewModel");
                    // Don't fail the test for this, just log a warning
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating crop coordinate mapping: {ex.Message}", "ImageManagementViewModel");
                return false;
            }
        }

        /// <summary>
        /// Validates the result of a boundary constraint calculation for reasonableness.
        /// </summary>
        /// <param name="rotation">Rotation angle used in test</param>
        /// <param name="zoom">Zoom level used in test</param>
        /// <param name="inputOffset">Original offset before constraints</param>
        /// <param name="constrainedOffset">Offset after applying constraints</param>
        /// <returns>True if the result is valid, false otherwise</returns>
        private bool ValidateBoundaryConstraintResult(double rotation, double zoom, Point inputOffset, Point constrainedOffset)
        {
            try
            {
                // Check for NaN or infinite values
                if (double.IsNaN(constrainedOffset.X) || double.IsInfinity(constrainedOffset.X) ||
                    double.IsNaN(constrainedOffset.Y) || double.IsInfinity(constrainedOffset.Y))
                {
                    LoggingService.LogError($"Invalid constraint result - NaN or Infinity detected", "ImageManagementViewModel");
                    return false;
                }

                // Check that constrained values are within reasonable bounds
                const double maxReasonableOffset = 1000.0; // Reasonable maximum offset
                if (Math.Abs(constrainedOffset.X) > maxReasonableOffset || Math.Abs(constrainedOffset.Y) > maxReasonableOffset)
                {
                    LoggingService.LogWarning($"Constraint result may be unreasonable - Large offset detected: " +
                        $"({constrainedOffset.X:F1}, {constrainedOffset.Y:F1})", "ImageManagementViewModel");
                    return false;
                }

                // For extreme zoom levels, ensure constraints are still applied
                if (zoom > 3.0)
                {
                    // At high zoom, constraints should be more restrictive
                    if (Math.Abs(constrainedOffset.X) > Math.Abs(inputOffset.X) ||
                        Math.Abs(constrainedOffset.Y) > Math.Abs(inputOffset.Y))
                    {
                        LoggingService.LogWarning($"High zoom constraint validation failed - " +
                            $"Constrained offset larger than input", "ImageManagementViewModel");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating boundary constraint result: {ex.Message}", "ImageManagementViewModel");
                return false;
            }
        }

        #endregion
    }
}
