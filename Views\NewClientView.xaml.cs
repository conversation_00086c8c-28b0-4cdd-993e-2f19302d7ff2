using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using MaterialDesignThemes.Wpf;
using UFU2.ViewModels;
using UFU2.Common;
using UFU2.Services;
using UFU2.Models;

namespace UFU2.Views
{
    /// <summary>
    /// Interaction logic for NewClientView.xaml
    /// A UserControl that provides a modal dialog for adding new clients.
    /// Integrates with MaterialDesign DialogHost for consistent modal behavior
    /// and follows UFU2 design patterns with Arabic RTL layout support.
    /// OPTIMIZED: Enhanced with debounced validation and WeakEventManager for Phase 2B UI optimizations.
    /// </summary>
    public partial class NewClientView : UserControl
    {
        #region Private Fields for Optimization

        // Debouncing for NameFr synchronization to reduce validation frequency
        private readonly DispatcherTimer _nameFrSyncDebounceTimer;
        private const int NameFrSyncDebounceMs = 300; // 300ms debounce delay for typing

        // WeakEventManager for PropertyChanged subscriptions to prevent memory leaks
        private WeakReference<NPersonalViewModel>? _personalViewModelRef;
        private WeakReference<NewClientViewModel>? _mainViewModelRef;

        // Performance monitoring
        private static int _nameFrSyncCount = 0;
        private static int _debouncedSyncCount = 0;

        #endregion
        #region Private Fields

        /// <summary>
        /// Flag to prevent circular synchronization between NPersonalView and ViewModel.
        /// When true, synchronization events are ignored to prevent infinite loops.
        /// </summary>
        private bool _isSynchronizing = false;

        #endregion

        /// <summary>
        /// Initializes a new instance of the <see cref="NewClientView"/> class.
        /// OPTIMIZED: Enhanced with debounce timer initialization and background processing.
        /// </summary>
        public NewClientView()
        {
            // Start performance monitoring
            var monitoringService = ServiceLocator.GetService<ViewLoadingMonitoringService>();
            var loadingStopwatch = monitoringService?.StartViewLoading("NewClientView", "NewClientView", ViewLoadingType.Immediate) ?? Stopwatch.StartNew();

            InitializeComponent();
            DataContext = new NewClientViewModel();

            // Initialize debounce timer for NameFr synchronization optimization
            _nameFrSyncDebounceTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(NameFrSyncDebounceMs)
            };
            _nameFrSyncDebounceTimer.Tick += NameFrSyncDebounceTimer_Tick;

            // Set the DataContext for child components to share the same ViewModel instance immediately
            // This ensures immediate availability for user interaction
            ActivityTabView.DataContext = this.DataContext;
            ActivityDetailView.DataContext = this.DataContext;
            FileCheckContentControl.DataContext = this.DataContext;

            // Set up critical synchronization immediately for user interaction
            SetupPhoneNumbersSync();
            SetupNameFrSync();
            SetupSaveDataTransfer();

            // Register with memory optimization service
            var memoryService = ServiceLocator.GetService<ViewMemoryOptimizationService>();
            memoryService?.RegisterView("NewClientView", this, 15.0); // Estimated 15MB

            // Queue background initialization for non-critical components
            QueueBackgroundInitialization();

            // Complete performance monitoring
            monitoringService?.CompleteViewLoading("NewClientView", loadingStopwatch, true);

            Loaded += NewClientView_Loaded;
            Unloaded += NewClientView_Unloaded;
        }



        /// <summary>
        /// Queues background initialization for non-critical components.
        /// OPTIMIZATION: Improves perceived performance by deferring non-essential initialization.
        /// </summary>
        private void QueueBackgroundInitialization()
        {
            var backgroundService = ServiceLocator.GetService<BackgroundViewInitializationService>();
            if (backgroundService == null) return;

            // Queue background data loading for activity types
            backgroundService.QueueBackgroundInitialization(
                "NewClientView_ActivityTypes",
                async (cancellationToken) =>
                {
                    try
                    {
                        // Preload activity type data in background
                        var activityTypeService = ServiceLocator.GetService<ActivityTypeBaseService>();
                        if (activityTypeService != null)
                        {
                            await activityTypeService.GetAllAsync();
                        }

                        LoggingService.LogDebug("Background activity types preloading completed", "NewClientView");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error in background activity types loading: {ex.Message}", "NewClientView");
                    }
                },
                BackgroundTaskPriority.Low);

            // Queue background validation rules loading
            backgroundService.QueueBackgroundInitialization(
                "NewClientView_ValidationRules",
                async (cancellationToken) =>
                {
                    try
                    {
                        // Preload validation rules in background
                        var clientValidationService = ServiceLocator.GetService<ClientValidationService>();
                        if (clientValidationService != null)
                        {
                            await Task.Run(() => clientValidationService.ValidateClientCreation(new ClientCreationData()), cancellationToken);
                        }

                        LoggingService.LogDebug("Background validation rules preloading completed", "NewClientView");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error in background validation rules loading: {ex.Message}", "NewClientView");
                    }
                },
                BackgroundTaskPriority.Low);

            LoggingService.LogDebug("Queued background initialization tasks for NewClientView", "NewClientView");
        }

        private void NewClientView_Loaded(object sender, RoutedEventArgs e)
        {
            // Diagnostic log to check the DataContext
            Debug.WriteLine($"LoadingSpinner DataContext: {LoadingSpinner.DataContext}");

            // Update memory service access time
            var memoryService = ServiceLocator.GetService<ViewMemoryOptimizationService>();
            memoryService?.UpdateViewAccess("NewClientView");
        }

        /// <summary>
        /// Sets up phone numbers synchronization between NPersonalView and ViewModel.
        /// DISABLED: Removed bidirectional synchronization to prevent interference with NPersonalView's own data management.
        /// NPersonalView now manages its phone number data independently to ensure proper tab isolation.
        /// Data will be synchronized only when explicitly saving the client.
        /// </summary>
        private void SetupPhoneNumbersSync()
        {
            // DISABLED: Bidirectional synchronization removed to fix tab independence issues
            // NPersonalView now manages its own phone number data independently
            // This prevents circular synchronization and ensures proper data isolation between tabs

            LoggingService.LogInfo("Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)", "NewClientView");
        }

        /// <summary>
        /// Sets up real-time synchronization of NameFr between NPersonalView and NewClientViewModel.
        /// This ensures CanSave validation works correctly as the user types.
        /// OPTIMIZED: Enhanced with debouncing and WeakEventManager to prevent memory leaks.
        /// </summary>
        private void SetupNameFrSync()
        {
            try
            {
                if (DataContext is NewClientViewModel mainViewModel &&
                    PersonalInfoContentControl.DataContext is NPersonalViewModel personalViewModel)
                {
                    // Store weak references to prevent memory leaks
                    _mainViewModelRef = new WeakReference<NewClientViewModel>(mainViewModel);
                    _personalViewModelRef = new WeakReference<NPersonalViewModel>(personalViewModel);

                    // Use WeakEventManager to prevent memory leaks
                    WeakEventManager<INotifyPropertyChanged, PropertyChangedEventArgs>.AddHandler(
                        personalViewModel,
                        nameof(INotifyPropertyChanged.PropertyChanged),
                        OnPersonalViewModelPropertyChanged);

                    // Initial sync
                    mainViewModel.NameFr = personalViewModel.NameFr;
                    LoggingService.LogInfo("NameFr real-time synchronization setup completed with optimization", "NewClientView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting up NameFr synchronization: {ex.Message}", "NewClientView");
            }
        }

        /// <summary>
        /// Sets up data transfer mechanism for Save operations.
        /// Ensures NPersonalView data is transferred to ViewModel before saving.
        /// </summary>
        private void SetupSaveDataTransfer()
        {
            if (DataContext is NewClientViewModel viewModel)
            {
                // Set up the data collection delegate
                viewModel.RequestDataCollection = () => PrepareDataForSaving();
                LoggingService.LogInfo("Save data transfer mechanism setup completed", "NewClientView");
            }
        }

        /// <summary>
        /// Prepares data for saving by transferring all view data to the ViewModel.
        /// This method should be called before any save operation.
        /// </summary>
        public void PrepareDataForSaving()
        {
            if (DataContext is NewClientViewModel viewModel)
            {
                TransferPersonalViewDataToViewModel(viewModel);
            }
        }

        /// <summary>
        /// Transfers data from NPersonalView to ViewModel for saving.
        /// This ensures all user input is captured before the save operation.
        /// Collects all PersonalInfo data including NameFr, NameAr, BirthDate, BirthPlace, Gender, Address, NationalId.
        /// </summary>
        /// <param name="viewModel">The NewClientViewModel instance</param>
        private void TransferPersonalViewDataToViewModel(NewClientViewModel viewModel)
        {
            try
            {
                // Transfer all PersonalInfo data from NPersonalView to ViewModel
                if (PersonalInfoContentControl.DataContext is NPersonalViewModel personalViewModel)
                {
                    viewModel.NameFr = personalViewModel.NameFr;
                    viewModel.NameAr = personalViewModel.NameAr;
                    viewModel.BirthDate = personalViewModel.BirthDate;
                    viewModel.BirthPlace = personalViewModel.BirthPlace;
                    viewModel.Gender = personalViewModel.Gender;
                    viewModel.Address = personalViewModel.Address;
                    viewModel.NationalId = personalViewModel.NationalId;

                    LoggingService.LogInfo($"Transferred PersonalInfo data - NameFr: '{viewModel.NameFr}', NameAr: '{viewModel.NameAr}', BirthDate: '{viewModel.BirthDate}', Gender: {viewModel.Gender}", "NewClientView");
                }

                // Sync current phone number from TextBox to NPersonalView collection first
                PersonalInfoContentControl.SyncPhoneNumberToCollection();

                // Clear ViewModel phone numbers and copy from NPersonalView
                viewModel.PhoneNumbers.Clear();
                foreach (var phone in PersonalInfoContentControl.PhoneNumbers.PhoneNumbers)
                {
                    viewModel.PhoneNumbers.AddPhoneNumber(phone.Clone());
                }

                LoggingService.LogInfo($"Transferred {PersonalInfoContentControl.PhoneNumbers.PhoneNumbers.Count} phone numbers to ViewModel for saving", "NewClientView");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error transferring personal view data to ViewModel: {ex.Message}", "NewClientView");
            }
        }

        // DISABLED: These synchronization methods are no longer used to ensure tab independence
        // NPersonalView now manages its phone number data independently

        /*
        /// <summary>
        /// DISABLED: Synchronizes phone numbers from ViewModel to NPersonalView.
        /// This method was causing interference with NPersonalView's independent data management.
        /// </summary>
        /// <param name="viewModel">The NewClientViewModel instance</param>
        private void SyncViewModelToPersonalView(NewClientViewModel viewModel)
        {
            // Method disabled to prevent tab interference
        }

        /// <summary>
        /// DISABLED: Synchronizes phone numbers from NPersonalView to ViewModel.
        /// This method was causing interference with NPersonalView's independent data management.
        /// </summary>
        /// <param name="viewModel">The NewClientViewModel instance</param>
        private void SyncPersonalViewToViewModel(NewClientViewModel viewModel)
        {
            // Method disabled to prevent tab interference
        }
        */

        

        #region Optimization Event Handlers

        /// <summary>
        /// Handles PropertyChanged events from NPersonalViewModel with debouncing.
        /// OPTIMIZED: Uses debouncing to reduce validation frequency and improve performance.
        /// </summary>
        private void OnPersonalViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == "NameFr")
            {
                System.Threading.Interlocked.Increment(ref _nameFrSyncCount);

                // Start debounce timer to batch synchronization
                _nameFrSyncDebounceTimer.Stop();
                _nameFrSyncDebounceTimer.Start();
            }
        }

        /// <summary>
        /// Handles the debounce timer tick to process batched NameFr synchronization.
        /// OPTIMIZED: Reduces synchronization frequency and improves typing responsiveness.
        /// </summary>
        private void NameFrSyncDebounceTimer_Tick(object? sender, EventArgs e)
        {
            _nameFrSyncDebounceTimer.Stop();

            try
            {
                if (_personalViewModelRef?.TryGetTarget(out var personalViewModel) == true &&
                    _mainViewModelRef?.TryGetTarget(out var mainViewModel) == true)
                {
                    // Sync NameFr to main ViewModel for validation
                    mainViewModel.NameFr = personalViewModel.NameFr;
                    System.Threading.Interlocked.Increment(ref _debouncedSyncCount);

                    LoggingService.LogDebug($"NameFr synced to main ViewModel: '{personalViewModel.NameFr}'", "NewClientView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in NameFr sync debounce timer: {ex.Message}", "NewClientView");
            }
        }

        /// <summary>
        /// Handles the Unloaded event for cleanup.
        /// OPTIMIZED: Proper cleanup to prevent memory leaks, including memory optimization service cleanup.
        /// </summary>
        private void NewClientView_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clean up WeakEventManager subscriptions
                if (_personalViewModelRef?.TryGetTarget(out var personalViewModel) == true)
                {
                    WeakEventManager<INotifyPropertyChanged, PropertyChangedEventArgs>.RemoveHandler(
                        personalViewModel,
                        nameof(INotifyPropertyChanged.PropertyChanged),
                        OnPersonalViewModelPropertyChanged);
                }

                // Stop and dispose debounce timer
                _nameFrSyncDebounceTimer?.Stop();

                // Clean up memory optimization tracking
                var memoryService = ServiceLocator.GetService<ViewMemoryOptimizationService>();
                memoryService?.CleanupView("NewClientView");

                // Clear weak references
                _personalViewModelRef = null;
                _mainViewModelRef = null;

                LoggingService.LogDebug("NewClientView cleanup completed with memory optimization cleanup", "NewClientView");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during NewClientView cleanup: {ex.Message}", "NewClientView");
            }
        }

        /// <summary>
        /// Gets performance statistics for the NewClientView component.
        /// Used for Phase 2B UI optimization monitoring.
        /// </summary>
        /// <returns>Performance statistics including sync counts and debounce effectiveness</returns>
        public static (int NameFrSyncCount, int DebouncedSyncCount, double DebounceEffectiveness) GetPerformanceStats()
        {
            var syncCount = _nameFrSyncCount;
            var debouncedCount = _debouncedSyncCount;
            var debounceEffectiveness = syncCount > 0 ? (double)debouncedCount / syncCount : 0.0;

            return (syncCount, debouncedCount, debounceEffectiveness);
        }

        /// <summary>
        /// Resets performance counters for testing and monitoring.
        /// </summary>
        public static void ResetPerformanceCounters()
        {
            System.Threading.Interlocked.Exchange(ref _nameFrSyncCount, 0);
            System.Threading.Interlocked.Exchange(ref _debouncedSyncCount, 0);
        }

        #endregion

    }
}
