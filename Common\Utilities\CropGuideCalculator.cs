using System;
using System.Windows;
using UFU2.Common;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Helper class for calculating crop guide positions and validating crop areas.
    /// Provides functionality for positioning the crop guide overlay and converting
    /// between UI coordinates and image pixel coordinates for cropping operations.
    /// Follows UFU2 patterns with comprehensive error handling and logging.
    /// </summary>
    public static class CropGuideCalculator
    {
        #region Constants

        /// <summary>
        /// Default crop guide width for profile images (interactive guide dimensions)
        /// </summary>
        public const double DefaultCropGuideWidth = 254.0;

        /// <summary>
        /// Default crop guide height for profile images (interactive guide dimensions)
        /// </summary>
        public const double DefaultCropGuideHeight = 290.0;

        /// <summary>
        /// Target aspect ratio for profile images (127/145 = 0.****************)
        /// </summary>
        public const double ProfileImageAspectRatio = 127.0 / 145.0;

        #endregion

        #region Crop Guide Position Calculation

        /// <summary>
        /// Calculates the position of the crop guide overlay based on image transformations.
        /// Takes into account zoom, rotation, and translation to position the guide correctly.
        /// </summary>
        /// <param name="imageSize">Size of the source image</param>
        /// <param name="canvasSize">Size of the canvas container</param>
        /// <param name="zoomScale">Current zoom scale</param>
        /// <param name="rotationAngle">Current rotation angle in degrees</param>
        /// <param name="translation">Current translation offset</param>
        /// <returns>Rectangle representing the crop guide position</returns>
        public static Rect CalculateCropGuidePosition(Size imageSize, Size canvasSize, 
            double zoomScale, double rotationAngle, Point translation)
        {
            try
            {
                LoggingService.LogDebug($"Calculating crop guide position - ImageSize: {imageSize.Width},{imageSize.Height}, " +
                    $"CanvasSize: {canvasSize.Width},{canvasSize.Height}, Zoom: {zoomScale}, " +
                    $"Rotation: {rotationAngle}, Translation: {translation.X},{translation.Y}", 
                    "CropGuideCalculator");

                // Validate input parameters
                if (!CoordinateTransformHelper.ValidateImageParameters(imageSize.Width, imageSize.Height, 
                    canvasSize.Width, canvasSize.Height, zoomScale, rotationAngle))
                {
                    LoggingService.LogWarning("Invalid parameters for crop guide calculation", "CropGuideCalculator");
                    return GetDefaultCropGuidePosition(canvasSize);
                }

                // Create bidirectional transformation
                var (forwardMatrix, inverseMatrix) = CoordinateTransformHelper.CreateBidirectionalTransform(
                    imageSize, canvasSize, zoomScale, rotationAngle);

                // Calculate the bounds of the transformed image
                var imageBounds = CalculateTransformedImageBounds(imageSize, forwardMatrix, canvasSize, translation);

                // Create crop guide rectangle with target aspect ratio
                var cropGuideRect = CoordinateTransformHelper.GetUniformRect(ProfileImageAspectRatio, imageBounds);

                LoggingService.LogDebug($"Calculated crop guide position: {cropGuideRect}", "CropGuideCalculator");

                return cropGuideRect;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating crop guide position: {ex.Message}", "CropGuideCalculator");
                return GetDefaultCropGuidePosition(canvasSize);
            }
        }

        /// <summary>
        /// Calculates the bounds of the transformed image within the canvas.
        /// </summary>
        /// <param name="imageSize">Original image size</param>
        /// <param name="transformMatrix">Transformation matrix</param>
        /// <param name="canvasSize">Canvas size</param>
        /// <param name="translation">Translation offset</param>
        /// <returns>Rectangle representing the transformed image bounds</returns>
        private static Rect CalculateTransformedImageBounds(Size imageSize, System.Windows.Media.Matrix transformMatrix, 
            Size canvasSize, Point translation)
        {
            try
            {
                // Calculate the optimal render size
                var renderSize = CoordinateTransformHelper.CalculateOptimalRenderSize(imageSize, 0, 1.0);

                // Apply the transformation matrix to get the actual size
                var transformedSize = new Size(
                    renderSize.Width * Math.Abs(transformMatrix.M11) + renderSize.Height * Math.Abs(transformMatrix.M12),
                    renderSize.Width * Math.Abs(transformMatrix.M21) + renderSize.Height * Math.Abs(transformMatrix.M22));

                // Center the image in the canvas and apply translation
                double x = (canvasSize.Width - transformedSize.Width) / 2 + translation.X;
                double y = (canvasSize.Height - transformedSize.Height) / 2 + translation.Y;

                return new Rect(x, y, transformedSize.Width, transformedSize.Height);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating transformed image bounds: {ex.Message}", "CropGuideCalculator");
                return new Rect(0, 0, canvasSize.Width, canvasSize.Height);
            }
        }

        /// <summary>
        /// Returns the default crop guide position centered in the canvas.
        /// </summary>
        /// <param name="canvasSize">Size of the canvas</param>
        /// <returns>Default crop guide rectangle</returns>
        private static Rect GetDefaultCropGuidePosition(Size canvasSize)
        {
            try
            {
                double width = Math.Min(DefaultCropGuideWidth, canvasSize.Width * 0.8);
                double height = width / ProfileImageAspectRatio;

                if (height > canvasSize.Height * 0.8)
                {
                    height = canvasSize.Height * 0.8;
                    width = height * ProfileImageAspectRatio;
                }

                double x = (canvasSize.Width - width) / 2;
                double y = (canvasSize.Height - height) / 2;

                return new Rect(x, y, width, height);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting default crop guide position: {ex.Message}", "CropGuideCalculator");
                return new Rect(0, 0, DefaultCropGuideWidth, DefaultCropGuideHeight);
            }
        }

        #endregion

        #region Crop Area Validation

        /// <summary>
        /// Validates that the crop area is within the specified bounds and adjusts if necessary.
        /// </summary>
        /// <param name="cropArea">Crop area rectangle</param>
        /// <param name="bounds">Boundary rectangle</param>
        /// <returns>Validated and potentially adjusted crop area</returns>
        public static Rect ValidateCropArea(Rect cropArea, Rect bounds)
        {
            try
            {
                LoggingService.LogDebug($"Validating crop area: {cropArea} against bounds: {bounds}", "CropGuideCalculator");

                if (!CoordinateTransformHelper.IsValidRect(cropArea) || !CoordinateTransformHelper.IsValidRect(bounds))
                {
                    LoggingService.LogWarning("Invalid crop area or bounds provided", "CropGuideCalculator");
                    return bounds;
                }

                // Ensure crop area is within bounds
                double left = Math.Max(cropArea.Left, bounds.Left);
                double top = Math.Max(cropArea.Top, bounds.Top);
                double right = Math.Min(cropArea.Right, bounds.Right);
                double bottom = Math.Min(cropArea.Bottom, bounds.Bottom);

                // Ensure minimum dimensions
                if (right - left < 1.0 || bottom - top < 1.0)
                {
                    LoggingService.LogWarning("Crop area too small after validation, using bounds", "CropGuideCalculator");
                    return bounds;
                }

                var validatedArea = new Rect(left, top, right - left, bottom - top);
                
                if (validatedArea != cropArea)
                {
                    LoggingService.LogDebug($"Crop area adjusted to: {validatedArea}", "CropGuideCalculator");
                }

                return validatedArea;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating crop area: {ex.Message}", "CropGuideCalculator");
                return bounds;
            }
        }

        #endregion

        #region Image Coordinate Conversion

        /// <summary>
        /// Converts crop guide rectangle coordinates to image pixel coordinates.
        /// </summary>
        /// <param name="guideRect">Crop guide rectangle in UI coordinates</param>
        /// <param name="imageSize">Original image size</param>
        /// <param name="displaySize">Display container size</param>
        /// <param name="zoomScale">Current zoom scale</param>
        /// <param name="rotationAngle">Current rotation angle</param>
        /// <returns>Rectangle in image pixel coordinates</returns>
        public static Rect CalculateImageCropArea(Rect guideRect, Size imageSize, Size displaySize, 
            double zoomScale, double rotationAngle)
        {
            try
            {
                LoggingService.LogDebug($"Calculating image crop area from guide rect: {guideRect}", "CropGuideCalculator");

                // Create bidirectional transformation
                var (forwardMatrix, inverseMatrix) = CoordinateTransformHelper.CreateBidirectionalTransform(
                    imageSize, displaySize, zoomScale, rotationAngle);

                // Transform UI coordinates to image coordinates
                var imageCropArea = CoordinateTransformHelper.TransformUIToImageCoordinates(guideRect, inverseMatrix);

                // Validate the crop area is within image bounds
                var imageBounds = new Rect(0, 0, imageSize.Width, imageSize.Height);
                var validatedCropArea = ValidateCropArea(imageCropArea, imageBounds);

                LoggingService.LogDebug($"Image crop area calculated: {validatedCropArea}", "CropGuideCalculator");

                return validatedCropArea;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating image crop area: {ex.Message}", "CropGuideCalculator");
                return new Rect(0, 0, imageSize.Width, imageSize.Height);
            }
        }

        #endregion
    }
}
