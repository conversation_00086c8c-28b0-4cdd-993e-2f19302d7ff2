using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using UFU2.Common;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Centralized cache coordination service for UFU2 application.
    /// Manages cache invalidation strategies, cache warming, and cross-service cache consistency.
    /// Implements Day 3 cache coordination requirements for performance optimization.
    /// </summary>
    public class CacheCoordinatorService : IDisposable
    {
        #region Private Fields

        private readonly ConcurrentDictionary<string, ICacheableService> _cacheableServices;
        private readonly ConcurrentDictionary<string, DateTime> _lastWarmupTimes;
        private readonly Timer _healthMonitoringTimer;
        private readonly Timer _coordinatedCleanupTimer;
        private readonly SemaphoreSlim _coordinationLock;
        private bool _disposed = false;

        // Cache invalidation dependency mapping
        private readonly Dictionary<string, List<string>> _invalidationDependencies;

        // Cache warming priorities
        private readonly Dictionary<string, int> _warmupPriorities;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the CacheCoordinatorService.
        /// </summary>
        public CacheCoordinatorService()
        {
            _cacheableServices = new ConcurrentDictionary<string, ICacheableService>();
            _lastWarmupTimes = new ConcurrentDictionary<string, DateTime>();
            _coordinationLock = new SemaphoreSlim(1, 1);

            // Initialize invalidation dependencies
            _invalidationDependencies = InitializeInvalidationDependencies();

            // Initialize warmup priorities (higher number = higher priority)
            _warmupPriorities = new Dictionary<string, int>
            {
                ["ActivityTypeBaseService"] = 100, // Highest priority - core lookup data
                ["ValidationService"] = 90,        // High priority - validation rules
                ["FileCheckBusinessRuleService"] = 80 // Medium-high priority - business rules
            };

            // Start health monitoring timer (every 5 minutes)
            _healthMonitoringTimer = new Timer(MonitorCacheHealth, null, 
                TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

            // Start coordinated cleanup timer (every 30 minutes)
            _coordinatedCleanupTimer = new Timer(PerformCoordinatedCleanup, null,
                TimeSpan.FromMinutes(30), TimeSpan.FromMinutes(30));

            LoggingService.LogInfo("CacheCoordinatorService initialized with health monitoring and coordinated cleanup", "CacheCoordinatorService");
        }

        #endregion

        #region Service Registration

        /// <summary>
        /// Registers a cacheable service for coordination.
        /// </summary>
        /// <param name="service">The cacheable service to register</param>
        public void RegisterCacheableService(ICacheableService service)
        {
            if (service == null)
                throw new ArgumentNullException(nameof(service));

            _cacheableServices.TryAdd(service.ServiceName, service);
            LoggingService.LogDebug($"Registered cacheable service for coordination: {service.ServiceName}", "CacheCoordinatorService");
        }

        /// <summary>
        /// Unregisters a cacheable service from coordination.
        /// </summary>
        /// <param name="serviceName">The name of the service to unregister</param>
        public void UnregisterCacheableService(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
                return;

            _cacheableServices.TryRemove(serviceName, out _);
            _lastWarmupTimes.TryRemove(serviceName, out _);
            LoggingService.LogDebug($"Unregistered cacheable service from coordination: {serviceName}", "CacheCoordinatorService");
        }

        #endregion

        #region Cache Coordination

        /// <summary>
        /// Coordinates cache invalidation across all affected services.
        /// </summary>
        /// <param name="invalidationContext">Context information about the data change</param>
        public async Task CoordinateInvalidationAsync(CacheInvalidationContext invalidationContext)
        {
            if (invalidationContext == null)
                throw new ArgumentNullException(nameof(invalidationContext));

            await _coordinationLock.WaitAsync();
            try
            {
                LoggingService.LogInfo($"Coordinating cache invalidation for {invalidationContext.DataType} ({invalidationContext.InvalidationType})", "CacheCoordinatorService");

                // Get affected services based on data type
                var affectedServices = GetAffectedServices(invalidationContext.DataType);

                // Invalidate caches in affected services
                foreach (var serviceName in affectedServices)
                {
                    if (_cacheableServices.TryGetValue(serviceName, out var service))
                    {
                        try
                        {
                            service.InvalidateCache(invalidationContext);
                            LoggingService.LogDebug($"Invalidated cache for service: {serviceName}", "CacheCoordinatorService");
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error invalidating cache for service {serviceName}: {ex.Message}", "CacheCoordinatorService");
                        }
                    }
                }

                // If this is a critical data change, trigger coordinated warmup
                if (IsCriticalDataChange(invalidationContext))
                {
                    await CoordinateWarmupAsync(affectedServices);
                }
            }
            finally
            {
                _coordinationLock.Release();
            }
        }

        /// <summary>
        /// Coordinates cache warmup across all registered services.
        /// </summary>
        public async Task CoordinateWarmupAsync()
        {
            await CoordinateWarmupAsync(_cacheableServices.Keys.ToList());
        }

        /// <summary>
        /// Coordinates cache warmup for specific services.
        /// </summary>
        /// <param name="serviceNames">List of service names to warm up</param>
        public async Task CoordinateWarmupAsync(List<string> serviceNames)
        {
            await _coordinationLock.WaitAsync();
            try
            {
                LoggingService.LogInfo($"Coordinating cache warmup for {serviceNames.Count} services", "CacheCoordinatorService");

                // Sort services by warmup priority
                var sortedServices = serviceNames
                    .Where(name => _cacheableServices.ContainsKey(name))
                    .OrderByDescending(name => _warmupPriorities.GetValueOrDefault(name, 0))
                    .ToList();

                // Warm up caches in priority order
                foreach (var serviceName in sortedServices)
                {
                    if (_cacheableServices.TryGetValue(serviceName, out var service))
                    {
                        try
                        {
                            var startTime = DateTime.UtcNow;
                            await service.WarmupCacheAsync();
                            var duration = DateTime.UtcNow - startTime;

                            _lastWarmupTimes[serviceName] = DateTime.UtcNow;
                            LoggingService.LogInfo($"Cache warmup completed for {serviceName} in {duration.TotalMilliseconds:F0}ms", "CacheCoordinatorService");
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error warming up cache for service {serviceName}: {ex.Message}", "CacheCoordinatorService");
                        }
                    }
                }
            }
            finally
            {
                _coordinationLock.Release();
            }
        }

        /// <summary>
        /// Clears all caches across all registered services.
        /// </summary>
        public async Task ClearAllCachesAsync()
        {
            await _coordinationLock.WaitAsync();
            try
            {
                LoggingService.LogInfo("Clearing all caches across all services", "CacheCoordinatorService");

                foreach (var service in _cacheableServices.Values)
                {
                    try
                    {
                        service.ClearCache();
                        LoggingService.LogDebug($"Cleared cache for service: {service.ServiceName}", "CacheCoordinatorService");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error clearing cache for service {service.ServiceName}: {ex.Message}", "CacheCoordinatorService");
                    }
                }
            }
            finally
            {
                _coordinationLock.Release();
            }
        }

        #endregion

        #region Cache Health Monitoring

        /// <summary>
        /// Gets comprehensive cache health information across all services.
        /// </summary>
        /// <returns>Dictionary of service names to their cache health information</returns>
        public Dictionary<string, CacheHealthInfo> GetOverallCacheHealth()
        {
            var healthInfo = new Dictionary<string, CacheHealthInfo>();

            foreach (var service in _cacheableServices.Values)
            {
                try
                {
                    var health = service.GetCacheHealth();
                    healthInfo[service.ServiceName] = health;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error getting cache health for service {service.ServiceName}: {ex.Message}", "CacheCoordinatorService");
                    
                    // Add error health info
                    healthInfo[service.ServiceName] = new CacheHealthInfo
                    {
                        IsHealthy = false,
                        HealthStatus = $"Error: {ex.Message}",
                        HitRatio = 0.0,
                        ItemCount = 0,
                        MemoryUsageBytes = 0
                    };
                }
            }

            return healthInfo;
        }

        /// <summary>
        /// Gets cache statistics across all services.
        /// </summary>
        /// <returns>Dictionary of service names to their cache statistics</returns>
        public Dictionary<string, Dictionary<string, object>> GetAllCacheStatistics()
        {
            var allStats = new Dictionary<string, Dictionary<string, object>>();

            foreach (var service in _cacheableServices.Values)
            {
                try
                {
                    var stats = service.GetCacheStatistics();
                    allStats[service.ServiceName] = stats;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error getting cache statistics for service {service.ServiceName}: {ex.Message}", "CacheCoordinatorService");
                }
            }

            return allStats;
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Initializes the cache invalidation dependency mapping.
        /// Defines which services need cache invalidation when specific data types change.
        /// </summary>
        /// <returns>Dictionary mapping data types to affected service names</returns>
        private Dictionary<string, List<string>> InitializeInvalidationDependencies()
        {
            return new Dictionary<string, List<string>>
            {
                ["ActivityType"] = new List<string>
                {
                    "ActivityTypeBaseService",
                    "ValidationService",
                    "FileCheckBusinessRuleService"
                },
                ["ValidationRule"] = new List<string>
                {
                    "ValidationService",
                    "FileCheckBusinessRuleService"
                },
                ["FileCheckRule"] = new List<string>
                {
                    "FileCheckBusinessRuleService",
                    "ValidationService"
                },
                ["BusinessRule"] = new List<string>
                {
                    "FileCheckBusinessRuleService",
                    "ValidationService",
                    "ActivityTypeBaseService"
                }
            };
        }

        /// <summary>
        /// Gets the list of services affected by a data type change.
        /// </summary>
        /// <param name="dataType">The type of data that changed</param>
        /// <returns>List of service names that should have their caches invalidated</returns>
        private List<string> GetAffectedServices(string dataType)
        {
            if (_invalidationDependencies.TryGetValue(dataType, out var affectedServices))
            {
                return affectedServices;
            }

            // If no specific mapping exists, return all services for safety
            return _cacheableServices.Keys.ToList();
        }

        /// <summary>
        /// Determines if a data change is critical and requires immediate cache warmup.
        /// </summary>
        /// <param name="invalidationContext">The invalidation context</param>
        /// <returns>True if the change is critical and requires warmup</returns>
        private bool IsCriticalDataChange(CacheInvalidationContext invalidationContext)
        {
            // Critical data types that require immediate warmup
            var criticalDataTypes = new[] { "ActivityType", "ValidationRule", "BusinessRule" };

            return criticalDataTypes.Contains(invalidationContext.DataType) ||
                   invalidationContext.InvalidationType == CacheInvalidationType.Full;
        }

        /// <summary>
        /// Monitors cache health across all services (timer callback).
        /// </summary>
        /// <param name="state">Timer state (not used)</param>
        private void MonitorCacheHealth(object? state)
        {
            try
            {
                LoggingService.LogDebug("Performing cache health monitoring", "CacheCoordinatorService");

                var healthInfo = GetOverallCacheHealth();
                var unhealthyServices = healthInfo.Where(kvp => !kvp.Value.IsHealthy).ToList();

                if (unhealthyServices.Any())
                {
                    LoggingService.LogWarning($"Found {unhealthyServices.Count} unhealthy cache services: {string.Join(", ", unhealthyServices.Select(s => s.Key))}", "CacheCoordinatorService");

                    // Optionally trigger cache warmup for unhealthy services
                    Task.Run(async () =>
                    {
                        try
                        {
                            await CoordinateWarmupAsync(unhealthyServices.Select(s => s.Key).ToList());
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error during health-triggered cache warmup: {ex.Message}", "CacheCoordinatorService");
                        }
                    });
                }

                // Log overall cache statistics
                var totalHitRatio = CalculateSafeAverageHitRatio(healthInfo.Values);
                var totalItems = healthInfo.Values.Sum(h => h?.ItemCount ?? 0);
                var totalMemory = healthInfo.Values.Sum(h => h?.MemoryUsageBytes ?? 0);

                LoggingService.LogInfo($"Cache health summary - Services: {healthInfo.Count}, Avg hit ratio: {totalHitRatio:P1}, Total items: {totalItems}, Memory: {totalMemory / 1024 / 1024:F1}MB", "CacheCoordinatorService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache health monitoring: {ex.Message}", "CacheCoordinatorService");
            }
        }

        /// <summary>
        /// Performs coordinated cleanup across all caches (timer callback).
        /// </summary>
        /// <param name="state">Timer state (not used)</param>
        private void PerformCoordinatedCleanup(object? state)
        {
            try
            {
                LoggingService.LogDebug("Performing coordinated cache cleanup", "CacheCoordinatorService");

                // Get cache statistics before cleanup
                var statsBefore = GetAllCacheStatistics();
                var totalItemsBefore = statsBefore.Values.Sum(stats =>
                    stats.TryGetValue("ItemCount", out var count) ? Convert.ToInt32(count) : 0);

                // Trigger cleanup on all services (this will compact memory caches)
                foreach (var service in _cacheableServices.Values)
                {
                    try
                    {
                        // Get current health to determine if cleanup is needed
                        var health = service.GetCacheHealth();

                        // If memory usage is high or hit ratio is low, clear and rewarm
                        if (health.MemoryUsageBytes > 50 * 1024 * 1024 || health.HitRatio < 0.5) // 50MB threshold or <50% hit ratio
                        {
                            LoggingService.LogInfo($"Performing cleanup for {service.ServiceName} - Memory: {health.MemoryUsageBytes / 1024 / 1024:F1}MB, Hit ratio: {health.HitRatio:P1}", "CacheCoordinatorService");

                            service.ClearCache();

                            // Schedule warmup for critical services
                            if (_warmupPriorities.ContainsKey(service.ServiceName))
                            {
                                Task.Run(async () =>
                                {
                                    try
                                    {
                                        await service.WarmupCacheAsync();
                                    }
                                    catch (Exception ex)
                                    {
                                        LoggingService.LogError($"Error during cleanup-triggered warmup for {service.ServiceName}: {ex.Message}", "CacheCoordinatorService");
                                    }
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error during cleanup for service {service.ServiceName}: {ex.Message}", "CacheCoordinatorService");
                    }
                }

                // Log cleanup results
                var statsAfter = GetAllCacheStatistics();
                var totalItemsAfter = statsAfter.Values.Sum(stats =>
                    stats.TryGetValue("ItemCount", out var count) ? Convert.ToInt32(count) : 0);

                LoggingService.LogInfo($"Coordinated cleanup completed - Items before: {totalItemsBefore}, after: {totalItemsAfter}", "CacheCoordinatorService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during coordinated cache cleanup: {ex.Message}", "CacheCoordinatorService");
            }
        }

        /// <summary>
        /// Safely calculates the average hit ratio from a collection of CacheHealthInfo objects.
        /// Handles null values and empty collections to prevent NullReferenceException.
        /// </summary>
        /// <param name="healthInfoCollection">Collection of CacheHealthInfo objects</param>
        /// <returns>Average hit ratio, or 0.0 if no valid data is available</returns>
        private static double CalculateSafeAverageHitRatio(IEnumerable<CacheHealthInfo> healthInfoCollection)
        {
            try
            {
                if (healthInfoCollection == null)
                {
                    LoggingService.LogWarning("HealthInfo collection is null when calculating average hit ratio", "CacheCoordinatorService");
                    return 0.0;
                }

                var validHealthInfos = healthInfoCollection
                    .Where(h => h != null && h.HitRatio > 0)
                    .ToList();

                if (!validHealthInfos.Any())
                {
                    LoggingService.LogDebug("No valid health info with positive hit ratio found", "CacheCoordinatorService");
                    return 0.0;
                }

                return validHealthInfos.Average(h => h.HitRatio);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating safe average hit ratio: {ex.Message}", "CacheCoordinatorService");
                return 0.0;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the cache coordinator service and its resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;

                try
                {
                    _healthMonitoringTimer?.Dispose();
                    _coordinatedCleanupTimer?.Dispose();
                    _coordinationLock?.Dispose();

                    LoggingService.LogInfo("CacheCoordinatorService disposed", "CacheCoordinatorService");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error disposing CacheCoordinatorService: {ex.Message}", "CacheCoordinatorService");
                }
            }
        }

        #endregion
    }
}
