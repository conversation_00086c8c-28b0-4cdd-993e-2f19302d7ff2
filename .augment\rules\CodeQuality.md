---
type: "always_apply"
---

# UFU2 Code Quality Standards

This document outlines the mandatory code quality standards for the UFU2 application. All code contributions must adhere to these principles to ensure maintainability, performance, security, and alignment with the SPARC methodology.

## Core Principles

*   **Modularity:** Code must be broken into small, focused modules/files.
*   **Separation of Concerns:** Strict adherence to architectural patterns (MVVM).
*   **Testability:** Code must be written to facilitate easy unit and integration testing.
*   **Maintainability:** Clear, readable, well-documented code with minimal technical debt.
*   **Performance:** Efficient resource usage and responsive UI (e.g., 60-120 FPS).
*   **Security:** Protection against vulnerabilities (e.g., SQL injection via parameterized queries).
*   **SPARC Alignment:** Practices must align with the SPARC development methodology.

## File Size and Structure

*   **Small/Very Focused Classes:** 50-150 lines (e.g., simple Models, small Helper classes, EventArgs).
*   **Standard Classes:** 150-500 lines (e.g., moderately complex ViewModels, Services, Converters). This is the **target** range for most classes.
*   **Larger Complex Classes:** 500-1000 lines (e.g., complex ViewModels handling significant logic for a specific view, or large Service classes). Classes in this range should be scrutinized and may benefit from refactoring.
*   **Classes > 1000 lines:** **Must be refactored.** This is a hard limit indicating the class is too large and complex.

## Mandatory Architecture Patterns

### MVVM Implementation

*   All ViewModels **must** inherit from `BaseViewModel`.
*   UI interactions **must** use `RelayCommand` or similar standardized command implementations.
*   Views **must** be lightweight; all business logic belongs in ViewModels.
*   Dependency Injection **must** be handled via `ServiceLocator.GetService<T>()`.

### Database Access

*   All SQLite operations **must** use `DatabaseService` with the Dapper ORM.
*   Database calls **must** be `async/await`.
*   **Always** use parameterized queries to prevent SQL injection.
*   Multi-step operations **must** be wrapped in database transactions.

### Service Layer

*   Business logic **must** reside in dedicated service classes.
*   Services **must** be accessed via the `ServiceLocator`.
*   Services **must** handle their own dependencies through the `ServiceLocator`.

## Coding Practices

### Reusability & DRY

*   Before creating new code, **always** check existing implementations:
    *   ViewModels: Property patterns, validation logic, command implementations.
    *   Services: Data access patterns, business logic.
    *   XAML: Reusable styles in `/Resources/Styles/`.
    *   Converters: Existing value converters in `/Converters/`.
    *   Utilities: Helper methods in `/Common/`.
*   **Never duplicate code.** Extend or reference existing implementations.

### Error Handling

*   Use `ErrorManager` for all application-level exception handling.
*   Use `LoggingService` for all structured logging operations.
*   Provide user-facing error messages localized in Arabic.
*   Handle database connection failures gracefully with retry logic where appropriate.

### Performance Optimization

*   Use `Dispatcher.Invoke` or `DispatcherOptimizationService` for UI thread safety.
*   Implement `IDisposable` for classes managing unmanaged resources or subscriptions.
*   Use `async` operations for I/O or heavy computational work.
*   Leverage the `BaseViewModel`'s smart batching system for `PropertyChanged` notifications.
*   Debounce user input validation where necessary.

## UI and Localization Standards

### Material Design

*   Use `DynamicResource` for all colors and styles to support theming.
*   Support Light/Dark themes via `ThemeManager`.
*   Follow existing component patterns and styles defined in `/Resources/Styles/`.

### RTL Support (Critical)

*   Primary language: Arabic (RTL).
*   All UI **must** support RTL text flow and layout mirroring.
*   Test layouts thoroughly with Arabic content.
*   Use appropriate text alignment and flow direction properties for RTL languages.

## Security Standards

*   **Always** use parameterized queries for database interactions.
*   Validate and sanitize all user inputs.
*   Avoid exposing sensitive information in logs or error messages shown to users.
*   Follow secure coding practices for data handling.

## Testing Standards (Implied)

*   Code structure **must** facilitate unit testing (e.g., dependency injection).
*   Business logic in services **should** be independently testable.
*   ViewModels **should** expose state and commands that can be tested without UI interaction.

## Production Code Only

*   Create only production-ready code. No temporary, test, mock, or placeholder files.
*   Focus on core business functions: client management, activity tracking, document management, payment processing.

## SPARC Alignment

*   **Specification & Pseudocode:** Ensure logic is well-defined before coding.
*   **Architecture:** Designs must respect service boundaries and MVVM.
*   **Refinement:** Includes TDD, debugging, and optimization.
*   **Modularity:** Adhere to the file size guidelines (150-500 lines target).
*   **Configuration:** Avoid hardcoding; use configuration abstractions.