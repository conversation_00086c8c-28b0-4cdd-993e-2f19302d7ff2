using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UFU2.Common;
using UFU2.Common.Extensions;
using UFU2.ViewModels;

namespace UFU2.Services
{
    /// <summary>
    /// Service for validating personal information data according to UFU2 business rules.
    /// Provides centralized validation logic with Arabic error messages for user feedback.
    /// Implements comprehensive validation for NameFr field including required field and Latin character validation.
    /// Integrates with existing IDataErrorInfo pattern and UFU2 architectural standards.
    /// </summary>
    public class PersonalInfoValidationService
    {
        #region Private Fields

        // Compiled regex for phone number validation (consistent with existing patterns)
        private static readonly Regex PhoneDigitsRegex = new Regex(@"\D", RegexOptions.Compiled);

        #endregion

        #region Public Methods

        /// <summary>
        /// Validates personal information data from NPersonalViewModel.
        /// Performs comprehensive validation including required fields and business rules.
        /// </summary>
        /// <param name="viewModel">The NPersonalViewModel to validate</param>
        /// <returns>ValidationResult containing all validation errors with Arabic messages</returns>
        public ValidationResult ValidatePersonalInfo(NPersonalViewModel viewModel)
        {
            var result = new ValidationResult();

            try
            {
                if (viewModel == null)
                {
                    result.AddError("ViewModel", "بيانات النموذج غير صحيحة");
                    return result;
                }

                // Validate NameFr field only - all other validation removed as per database binding requirements
                ValidateNameFr(viewModel.NameFr, result);

                LoggingService.LogDebug($"Personal info validation completed - Errors: {result.ErrorCount}", "PersonalInfoValidationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during personal info validation: {ex.Message}", "PersonalInfoValidationService");
                result.AddError("Validation", "حدث خطأ أثناء التحقق من البيانات");
            }

            return result;
        }

        /// <summary>
        /// Validates a specific property of personal information.
        /// Used for real-time validation feedback in UI controls.
        /// </summary>
        /// <param name="propertyName">The name of the property to validate</param>
        /// <param name="value">The value to validate</param>
        /// <returns>Error message in Arabic if validation fails, null if valid</returns>
        public string? ValidateProperty(string propertyName, object? value)
        {
            try
            {
                switch (propertyName)
                {
                    case nameof(NPersonalViewModel.NameFr):
                        return ValidateNameFrProperty(value?.ToString());
                    default:
                        return null; // Only NameFr validation as per database binding requirements
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating property {propertyName}: {ex.Message}", "PersonalInfoValidationService");
                return null;
            }
        }

        #endregion

        #region Private Validation Methods

        /// <summary>
        /// Validates the NameFr field according to business rules.
        /// Checks for required field and Latin character validation.
        /// </summary>
        /// <param name="nameFr">The NameFr value to validate</param>
        /// <param name="result">ValidationResult to add errors to</param>
        private void ValidateNameFr(string? nameFr, ValidationResult result)
        {
            try
            {
                // Check if NameFr is required and provided
                if (string.IsNullOrWhiteSpace(nameFr))
                {
                    result.AddError(nameof(NPersonalViewModel.NameFr), "الاسم باللاتينية مطلوب");
                    return;
                }

                // Validate Latin characters only
                if (!TextBoxExtensions.IsValidLatinText(nameFr))
                {
                    result.AddError(nameof(NPersonalViewModel.NameFr), "الاسم يجب أن يحتوي على أحرف لاتينية فقط");
                    return;
                }

                // Check minimum length
                if (nameFr.Trim().Length < 2)
                {
                    result.AddError(nameof(NPersonalViewModel.NameFr), "الاسم يجب أن يحتوي على حرفين على الأقل");
                    return;
                }

                // Check maximum length
                if (nameFr.Trim().Length > 100)
                {
                    result.AddError(nameof(NPersonalViewModel.NameFr), "الاسم طويل جداً (الحد الأقصى 100 حرف)");
                    return;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating NameFr: {ex.Message}", "PersonalInfoValidationService");
                result.AddError(nameof(NPersonalViewModel.NameFr), "حدث خطأ أثناء التحقق من الاسم");
            }
        }

        /// <summary>
        /// Validates the phone number according to business rules.
        /// Checks for minimum digit requirements.
        /// </summary>
        /// <param name="phoneNumber">The phone number to validate</param>
        /// <param name="result">ValidationResult to add errors to</param>
        private void ValidatePhoneNumber(string? phoneNumber, ValidationResult result)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber))
                    return; // Phone number is optional

                // Extract only digits from the phone number
                var digitsOnly = PhoneDigitsRegex.Replace(phoneNumber, "");

                if (digitsOnly.Length < 9)
                {
                    result.AddError(nameof(NPersonalViewModel.PhoneNumber), "رقم هاتف غير صحيح");
                    return;
                }

                if (digitsOnly.Length > 15)
                {
                    result.AddError(nameof(NPersonalViewModel.PhoneNumber), "رقم الهاتف طويل جداً");
                    return;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating phone number: {ex.Message}", "PersonalInfoValidationService");
                result.AddError(nameof(NPersonalViewModel.PhoneNumber), "حدث خطأ أثناء التحقق من رقم الهاتف");
            }
        }

        /// <summary>
        /// Validates NameFr property for real-time feedback.
        /// </summary>
        /// <param name="nameFr">The NameFr value to validate</param>
        /// <returns>Error message in Arabic if validation fails, null if valid</returns>
        private string? ValidateNameFrProperty(string? nameFr)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(nameFr))
                {
                    return "الاسم باللاتينية مطلوب";
                }

                if (!TextBoxExtensions.IsValidLatinText(nameFr))
                {
                    return "الاسم يجب أن يحتوي على أحرف لاتينية فقط";
                }

                if (nameFr.Trim().Length < 2)
                {
                    return "الاسم يجب أن يحتوي على حرفين على الأقل";
                }

                if (nameFr.Trim().Length > 100)
                {
                    return "الاسم طويل جداً (الحد الأقصى 100 حرف)";
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating NameFr property: {ex.Message}", "PersonalInfoValidationService");
                return null;
            }
        }

        /// <summary>
        /// Validates phone number property for real-time feedback.
        /// </summary>
        /// <param name="phoneNumber">The phone number to validate</param>
        /// <returns>Error message in Arabic if validation fails, null if valid</returns>
        private string? ValidatePhoneNumberProperty(string? phoneNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber))
                    return null; // Empty is allowed

                var digitsOnly = PhoneDigitsRegex.Replace(phoneNumber, "");

                if (digitsOnly.Length < 9)
                {
                    return "رقم هاتف غير صحيح";
                }

                if (digitsOnly.Length > 15)
                {
                    return "رقم الهاتف طويل جداً";
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating phone number property: {ex.Message}", "PersonalInfoValidationService");
                return null;
            }
        }

        #endregion
    }

    /// <summary>
    /// Represents the result of a validation operation.
    /// Contains validation errors with property names and Arabic error messages.
    /// </summary>
    public class ValidationResult
    {
        #region Private Fields

        private readonly Dictionary<string, string> _errors = new Dictionary<string, string>();

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets whether the validation result is valid (no errors).
        /// </summary>
        public bool IsValid => _errors.Count == 0;

        /// <summary>
        /// Gets the number of validation errors.
        /// </summary>
        public int ErrorCount => _errors.Count;

        /// <summary>
        /// Gets all validation errors as a dictionary.
        /// </summary>
        public IReadOnlyDictionary<string, string> Errors => _errors;

        #endregion

        #region Public Methods

        /// <summary>
        /// Adds a validation error for a specific property.
        /// </summary>
        /// <param name="propertyName">The name of the property with the error</param>
        /// <param name="errorMessage">The error message in Arabic</param>
        public void AddError(string propertyName, string errorMessage)
        {
            if (!string.IsNullOrWhiteSpace(propertyName) && !string.IsNullOrWhiteSpace(errorMessage))
            {
                _errors[propertyName] = errorMessage;
            }
        }

        /// <summary>
        /// Gets the error message for a specific property.
        /// </summary>
        /// <param name="propertyName">The name of the property</param>
        /// <returns>Error message if exists, null otherwise</returns>
        public string? GetError(string propertyName)
        {
            return _errors.TryGetValue(propertyName, out var error) ? error : null;
        }

        /// <summary>
        /// Gets all error messages as a single string.
        /// </summary>
        /// <returns>Combined error messages separated by newlines</returns>
        public string GetErrorsAsString()
        {
            return string.Join(Environment.NewLine, _errors.Values);
        }

        /// <summary>
        /// Clears all validation errors.
        /// </summary>
        public void Clear()
        {
            _errors.Clear();
        }

        #endregion
    }
}
