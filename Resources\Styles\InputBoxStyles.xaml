﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">


    <!--
        ========================================
        UNDELINE TEXT BOX STYLES
        ========================================
    -->

    <Style
        x:Key="BaseInputTextBoxStyle"
        BasedOn="{StaticResource MaterialDesignFloatingHintTextBox}"
        TargetType="TextBox">
        <Setter Property="CaretBrush" Value="{DynamicResource UnderLineForeground}" />
        <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{DynamicResource UnderLineBorderHover}" />
        <Setter Property="materialDesign:TextFieldAssist.UnderlineCornerRadius" Value="8" />
        <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="4" />
        <Setter Property="FontSize" Value="{DynamicResource LabelMediumFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="BorderBrush" Value="{DynamicResource UnderLineBorder}" />
    </Style>

    <Style
        x:Key="UnderlineTextBoxStyle"
        BasedOn="{StaticResource BaseInputTextBoxStyle}"
        TargetType="TextBox">
        <Setter Property="BorderThickness" Value="0,0,0,2" />
        <Setter Property="Margin" Value="8,0,8,0" />
        <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.85" />
        <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="5,12" />
        <Setter Property="materialDesign:HintAssist.Foreground" Value="{DynamicResource UnderLineFocuseColor}" />
        <Setter Property="Height" Value="32" />
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True" />
    </Style>

    <Style
        x:Key="MultiLineTextBoxStyle"
        BasedOn="{StaticResource BaseInputTextBoxStyle}"
        TargetType="TextBox">
        <Setter Property="BorderThickness" Value="0,0,0,2" />
        <Setter Property="Margin" Value="8,0,8,0" />
        <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.85" />
        <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="5" />
        <Setter Property="materialDesign:HintAssist.Foreground" Value="{DynamicResource UnderLineFocuseColor}" />
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True" />
    </Style>


    <!--
        ========================================
        UNDELINE COMBO BOX STYLES
        ========================================
    -->
    <Style
        x:Key="BaseComboBoxStyle"
        BasedOn="{StaticResource MaterialDesignFloatingHintComboBox}"
        TargetType="ComboBox">
        <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{DynamicResource UnderLineBorderHover}" />
        <Setter Property="materialDesign:TextFieldAssist.UnderlineCornerRadius" Value="8" />
        <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="4" />
        <Setter Property="FontSize" Value="{DynamicResource LabelMediumFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0,0,0,2" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="BorderBrush" Value="{DynamicResource UnderLineBorder}" />
    </Style>

    <Style
        x:Key="UnderlineComboBoxStyle"
        BasedOn="{StaticResource BaseComboBoxStyle}"
        TargetType="ComboBox">
        <Setter Property="Margin" Value="8,0,8,0" />
        <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.85" />
        <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="8,0" />
        <Setter Property="materialDesign:HintAssist.Foreground" Value="{DynamicResource UnderLineFocuseColor}" />
        <Setter Property="Height" Value="32" />
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True" />
        <Setter Property="materialDesign:ComboBoxAssist.ShowSelectedItem" Value="True" />
    </Style>

    <!--
        ========================================
        UNDELINE AUTOSUGGESTBOX STYLES
        ========================================
    -->
    <Style
        x:Key="BaseAutoSuggestBoxStyle"
        BasedOn="{StaticResource MaterialDesignFloatingHintAutoSuggestBox}"
        TargetType="materialDesign:AutoSuggestBox">
        <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{DynamicResource UnderLineBorderHover}" />
        <Setter Property="materialDesign:TextFieldAssist.UnderlineCornerRadius" Value="8" />
        <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="4" />
        <Setter Property="FontSize" Value="{DynamicResource LabelMediumFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0,0,0,2" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="BorderBrush" Value="{DynamicResource UnderLineBorder}" />
    </Style>

    <Style
        x:Key="UnderlineAutoSuggestBoxStyle"
        BasedOn="{StaticResource BaseAutoSuggestBoxStyle}"
        TargetType="materialDesign:AutoSuggestBox">
        <Setter Property="Margin" Value="8,0,8,0" />
        <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.85" />
        <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="8,0" />
        <Setter Property="materialDesign:HintAssist.Foreground" Value="{DynamicResource UnderLineFocuseColor}" />
        <Setter Property="Height" Value="32" />
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True" />
    </Style>

    <!--
        ========================================
        OUTLINED TEXT BOX STYLES
        ========================================
    -->
    <!--  must update it  -->
    <Style
        x:Key="BaseOutlinedTextBoxStyle"
        BasedOn="{StaticResource MaterialDesignOutlinedTextBox}"
        TargetType="TextBox">
        <Setter Property="CaretBrush" Value="{DynamicResource UnderLineForeground}" />
        <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{DynamicResource UnderLineBorderHover}" />
        <Setter Property="materialDesign:HintAssist.Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="materialDesign:HintAssist.ApplyHintPaddingBrush" Value="True" />
        <Setter Property="materialDesign:HintAssist.HintPaddingBrush" Value="{DynamicResource SurfaceBrush}" />
        <Setter Property="FontSize" Value="{DynamicResource LabelMediumFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="CaretBrush" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource UnderLineBorder}" />
    </Style>

</ResourceDictionary>