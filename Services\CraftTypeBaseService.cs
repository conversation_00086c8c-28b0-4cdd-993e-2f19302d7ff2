using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Caching.Memory;
using UFU2.Common;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.Services.Interfaces;
using UFU2.Services.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Service class for managing CraftTypeBase table operations.
    /// Handles table creation, data import from JSON, and CRUD operations.
    /// Includes comprehensive caching for improved performance with cache hit/miss tracking.
    /// </summary>
    public class CraftTypeBaseService : ICacheableService, IDisposable
    {
        private readonly DatabaseService _databaseService;
        private IMemoryCache _searchCache;
        private IMemoryCache _dataCache;

        // Cache statistics for monitoring
        private int _searchCacheHits = 0;
        private int _searchCacheMisses = 0;
        private int _dataCacheHits = 0;
        private int _dataCacheMisses = 0;

        /// <summary>
        /// Gets the service name for cache management identification.
        /// </summary>
        public string ServiceName => "CraftTypeBaseService";

        /// <summary>
        /// SQL statement for inserting craft type data.
        /// Note: Table creation is now handled by DatabaseMigrationService using APP_Schema.sql
        /// </summary>
        private const string InsertSql = @"
            INSERT OR REPLACE INTO CraftTypeBase (Code, Description, Content, Secondary)
            VALUES (@Code, @Description, @Content, @Secondary)";

        /// <summary>
        /// SQL statement for selecting all craft types.
        /// </summary>
        private const string SelectAllSql = @"
            SELECT Code, Description, Content, Secondary 
            FROM CraftTypeBase 
            ORDER BY Code";

        /// <summary>
        /// SQL statement for selecting craft type by code.
        /// </summary>
        private const string SelectByCodeSql = @"
            SELECT Code, Description, Content, Secondary 
            FROM CraftTypeBase 
            WHERE Code = @Code";

        /// <summary>
        /// SQL statement for searching craft types by description.
        /// </summary>
        private const string SearchByDescriptionSql = @"
            SELECT Code, Description, Content, Secondary 
            FROM CraftTypeBase 
            WHERE Description LIKE @SearchPattern 
            ORDER BY Code 
            LIMIT @Limit";

        /// <summary>
        /// SQL statement for counting craft types.
        /// </summary>
        private const string CountSql = @"
            SELECT COUNT(*) FROM CraftTypeBase";

        /// <summary>
        /// Initializes a new instance of the CraftTypeBaseService class.
        /// </summary>
        /// <param name="databaseService">Database service instance (optional - will use reference database from ServiceLocator if null)</param>
        public CraftTypeBaseService(DatabaseService databaseService = null)
        {
            // Use reference database from ServiceLocator if no specific database service provided
            _databaseService = databaseService ?? ServiceLocator.GetService<DatabaseService>("ReferenceDatabase");

            if (_databaseService == null)
            {
                throw new InvalidOperationException("Reference database service not found. Ensure ServiceLocator is properly initialized with reference database.");
            }

            // Initialize search cache with 5-minute expiration and 100 item limit
            _searchCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 100,
                CompactionPercentage = 0.25
            });

            // Initialize data cache for GetAllAsync results with 30-minute expiration and 50 item limit
            _dataCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 50,
                CompactionPercentage = 0.25
            });

            LoggingService.LogDebug("CraftTypeBaseService initialized with reference database", "CraftTypeBaseService");
        }

        /// <summary>
        /// Creates the CraftTypeBase table if it doesn't exist.
        /// Note: Table creation is now handled by DatabaseMigrationService using APP_Schema.sql.
        /// This method is kept for backward compatibility and now only validates table existence.
        /// </summary>
        public async Task CreateTableAsync()
        {
            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Validate that the table exists (created by migration service)
                const string checkTableSql = @"
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type='table' AND name='CraftTypeBase'";

                int tableExists = await connection.ExecuteScalarAsync<int>(checkTableSql);

                if (tableExists == 0)
                {
                    LoggingService.LogWarning("CraftTypeBase table does not exist. It should have been created by DatabaseMigrationService.", "CraftTypeBaseService");
                    throw new InvalidOperationException("CraftTypeBase table not found. Database schema may not be properly initialized.");
                }

                LoggingService.LogDebug("CraftTypeBase table validation completed successfully", "CraftTypeBaseService");
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في التحقق من جدول أنواع الحرف", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "CraftTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Seeds craft type data from embedded JSON resource.
        /// </summary>
        /// <param name="progress">Progress reporter for import operation</param>
        /// <returns>Number of records imported</returns>
        public async Task<int> SeedCraftTypeData(IProgress<double> progress = null)
        {
            try
            {
                LoggingService.LogDebug("Starting import from embedded JSON resource", "CraftTypeBaseService");

                // Read JSON content from embedded resource
                string jsonContent = ReadEmbeddedJsonResource("UFU2.Database.craft_Type.json");

                if (string.IsNullOrEmpty(jsonContent))
                {
                    LoggingService.LogWarning("Embedded JSON resource is empty or not found", "CraftTypeBaseService");
                    return 0;
                }

                var importedCount = await ImportFromJsonContent(jsonContent, progress);
                if (importedCount > 0)
                {
                    ErrorManager.ShowUserSuccessToast($"تم استيراد {importedCount} نوع حرفة بنجاح", "تم الاستيراد", "CraftTypeBaseService");
                }
                return importedCount;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error seeding craft type data: {ex.Message}", "CraftTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Reads embedded JSON resource content.
        /// </summary>
        /// <param name="resourceName">Name of the embedded resource</param>
        /// <returns>JSON content as string</returns>
        private string ReadEmbeddedJsonResource(string resourceName)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                using var stream = assembly.GetManifestResourceStream(resourceName);
                
                if (stream == null)
                {
                    LoggingService.LogWarning($"Embedded resource not found: {resourceName}", "CraftTypeBaseService");
                    return string.Empty;
                }

                using var reader = new StreamReader(stream);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error reading embedded resource {resourceName}: {ex.Message}", "CraftTypeBaseService");
                return string.Empty;
            }
        }

        /// <summary>
        /// Imports craft type data from JSON content.
        /// </summary>
        /// <param name="jsonContent">JSON content to import</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>Number of records imported</returns>
        private async Task<int> ImportFromJsonContent(string jsonContent, IProgress<double> progress = null)
        {
            try
            {
                var craftTypes = JsonSerializer.Deserialize<List<CraftTypeBaseModel>>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (craftTypes == null || !craftTypes.Any())
                {
                    LoggingService.LogWarning("No craft types found in JSON content", "CraftTypeBaseService");
                    return 0;
                }

                LoggingService.LogInfo($"Found {craftTypes.Count} craft types to import", "CraftTypeBaseService");

                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                var transaction = await connection.BeginTransactionAsync().ConfigureAwait(false);
                try
                {
                    int importedCount = 0;
                    for (int i = 0; i < craftTypes.Count; i++)
                    {
                        var craftType = craftTypes[i];
                        if (craftType.IsValid())
                        {
                            await connection.ExecuteAsync(InsertSql, craftType, transaction);
                            importedCount++;
                        }
                        else
                        {
                            LoggingService.LogWarning($"Invalid craft type data: {craftType.Code}", "CraftTypeBaseService");
                        }

                        // Report progress
                        progress?.Report((double)(i + 1) / craftTypes.Count);
                    }

                    await transaction.CommitAsync().ConfigureAwait(false);
                    LoggingService.LogInfo($"Successfully imported {importedCount} craft types", "CraftTypeBaseService");
                    return importedCount;
                }
                catch
                {
                    await transaction.RollbackAsync().ConfigureAwait(false);
                    throw;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error importing craft type data: {ex.Message}", "CraftTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Gets craft type by code with caching for improved performance.
        /// </summary>
        /// <param name="code">Craft code to search for</param>
        /// <returns>Craft type if found, null otherwise</returns>
        public async Task<CraftTypeBaseModel> GetByCodeAsync(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
            {
                return null;
            }

            string cacheKey = $"craft_code_{code.ToLowerInvariant()}";

            // Check cache first
            if (_dataCache.TryGetValue(cacheKey, out CraftTypeBaseModel cachedResult))
            {
                _dataCacheHits++;
                LoggingService.LogDebug($"Returning cached craft type for code: {code} (Cache hits: {_dataCacheHits}, misses: {_dataCacheMisses})", "CraftTypeBaseService");
                return cachedResult;
            }

            _dataCacheMisses++;

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                var result = await connection.QueryFirstOrDefaultAsync<CraftTypeBaseModel>(SelectByCodeSql, new { Code = code }).ConfigureAwait(false);

                // Cache the result for 30 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                    Size = 1,
                    Priority = CacheItemPriority.High
                };
                _dataCache.Set(cacheKey, result, cacheOptions);

                LoggingService.LogDebug($"Retrieved craft type for code: {code} (Cache hits: {_dataCacheHits}, misses: {_dataCacheMisses})", "CraftTypeBaseService");
                return result;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, $"فشل في البحث عن الحرفة: {code}", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "CraftTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Gets all craft types from the database with caching for improved performance.
        /// Uses connection pooling and caches results for 30 minutes to reduce database load.
        /// </summary>
        /// <returns>List of all craft types</returns>
        public async Task<List<CraftTypeBaseModel>> GetAllAsync()
        {
            const string cacheKey = "crafts_all";

            // Check cache first
            if (_dataCache.TryGetValue(cacheKey, out List<CraftTypeBaseModel> cachedResult))
            {
                _dataCacheHits++;
                LoggingService.LogDebug($"Returning cached craft types (Cache hits: {_dataCacheHits}, misses: {_dataCacheMisses})", "CraftTypeBaseService");
                return cachedResult;
            }

            _dataCacheMisses++;

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                var result = await connection.QueryAsync<CraftTypeBaseModel>(SelectAllSql).ConfigureAwait(false);
                var resultList = result.ToList();

                // Cache the results for 30 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                    Size = 1,
                    Priority = CacheItemPriority.High // High priority for frequently accessed data
                };
                _dataCache.Set(cacheKey, resultList, cacheOptions);

                LoggingService.LogDebug($"Cached {resultList.Count} craft types (Cache hits: {_dataCacheHits}, misses: {_dataCacheMisses})", "CraftTypeBaseService");
                return resultList;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في جلب أنواع الحرف", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "CraftTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Searches craft types by description with database-level filtering and caching.
        /// Provides significant performance improvement over loading all crafts into memory.
        /// </summary>
        /// <param name="searchTerm">The search term to look for in descriptions</param>
        /// <param name="limit">Maximum number of results to return (default: 10)</param>
        /// <returns>List of matching craft types</returns>
        public async Task<List<CraftTypeBaseModel>> SearchByDescriptionAsync(string searchTerm, int limit = 10)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
            {
                return new List<CraftTypeBaseModel>();
            }

            // Normalize search term for better matching
            string normalizedSearchTerm = TextNormalizationHelper.NormalizeForSearch(searchTerm);
            string cacheKey = $"craft_search_{normalizedSearchTerm.ToLowerInvariant()}_{limit}";

            // Check cache first
            if (_searchCache.TryGetValue(cacheKey, out List<CraftTypeBaseModel> cachedResult))
            {
                _searchCacheHits++;
                LoggingService.LogDebug($"Returning cached search results for '{searchTerm}' (Cache hits: {_searchCacheHits}, misses: {_searchCacheMisses})", "CraftTypeBaseService");
                return cachedResult;
            }

            _searchCacheMisses++;

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Use normalized search term for better matching
                string searchPattern = $"%{normalizedSearchTerm}%";
                var result = await connection.QueryAsync<CraftTypeBaseModel>(
                    SearchByDescriptionSql,
                    new { SearchPattern = searchPattern, Limit = limit });

                var resultList = result.ToList();

                // Cache the results for 5 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                    Size = 1
                };
                _searchCache.Set(cacheKey, resultList, cacheOptions);

                LoggingService.LogDebug($"Found {resultList.Count} craft types matching '{searchTerm}' (Cache hits: {_searchCacheHits}, misses: {_searchCacheMisses})", "CraftTypeBaseService");
                return resultList;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, $"فشل في البحث عن الحرف: {searchTerm}", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "CraftTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Enhanced search with fuzzy matching and similarity scoring for improved accuracy.
        /// Handles whitespace variations, Arabic character normalization, and typos.
        /// </summary>
        /// <param name="searchTerm">The search term to look for</param>
        /// <param name="limit">Maximum number of results to return (default: 10)</param>
        /// <param name="minSimilarity">Minimum similarity score (0.0 to 1.0, default: 0.3)</param>
        /// <returns>List of matching craft types with similarity scores</returns>
        public async Task<List<CraftTypeBaseModel>> SearchByDescriptionEnhancedAsync(string searchTerm, int limit = 10, double minSimilarity = 0.3)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
            {
                return new List<CraftTypeBaseModel>();
            }

            try
            {
                // Get all craft types for word frequency-based search
                var allCrafts = await GetAllAsync().ConfigureAwait(false);

                // Use WordFrequencySearchService for enhanced Arabic text analysis and word frequency ranking
                var wordFrequencySearchService = ServiceLocator.GetService<WordFrequencySearchService>();
                if (wordFrequencySearchService != null)
                {
                    var searchRequest = new Models.SearchRequest
                    {
                        SearchTerm = searchTerm,
                        MaxResults = limit,
                        MinSimilarity = 0.7, // Higher threshold for exact prefix matching
                        UseArabicAnalysis = true,
                        IncludePartialMatches = false, // Only exact prefix matches
                        FilterStopWords = false // Keep all words for exact prefix matching
                    };

                    var searchResults = await wordFrequencySearchService.SearchAsync(
                        allCrafts,
                        searchRequest,
                        craft => craft.Description
                    ).ConfigureAwait(false);

                    // Extract the craft models from enhanced search results
                    var results = searchResults.Results.Select(sr => sr.Item).ToList();

                    LoggingService.LogDebug($"Exact prefix craft search completed for '{searchTerm}': {results.Count} results with Arabic analysis", "CraftTypeBaseService");

                    return results;
                }
                else
                {
                    // Fallback to EnhancedSearchService if WordFrequencySearchService is not available
                    var enhancedSearchService = new EnhancedSearchService();
                    var searchResults = await enhancedSearchService.SearchAsync(
                        allCrafts,
                        searchTerm,
                        craft => craft.Description,
                        limit,
                        minSimilarity
                    ).ConfigureAwait(false);

                    var results = searchResults.Select(sr => sr.Item).ToList();
                    LoggingService.LogDebug($"Fallback craft search completed for '{searchTerm}': {results.Count} results", "CraftTypeBaseService");

                    return results;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in enhanced craft search for '{searchTerm}': {ex.Message}", "CraftTypeBaseService");
                // Fallback to regular search
                return await SearchByDescriptionAsync(searchTerm, limit).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Gets the total count of craft types in the database.
        /// </summary>
        /// <returns>Total count of records</returns>
        public async Task<int> GetCountAsync()
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var count = await connection.ExecuteScalarAsync<int>(CountSql);
                return count;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في عد أنواع الحرف", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "CraftTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Checks if the CraftTypeBase table has data.
        /// </summary>
        /// <returns>True if table has data, false otherwise</returns>
        public async Task<bool> HasDataAsync()
        {
            var count = await GetCountAsync();
            return count > 0;
        }

        /// <summary>
        /// Inserts a single craft type into the database.
        /// </summary>
        /// <param name="craftType">The craft type to insert</param>
        /// <returns>True if insertion was successful, false otherwise</returns>
        public async Task<bool> InsertAsync(CraftTypeBaseModel craftType)
        {
            if (craftType == null)
            {
                throw new ArgumentNullException(nameof(craftType));
            }

            if (!craftType.IsValid())
            {
                LoggingService.LogWarning($"Invalid craft type data: {craftType.Code}", "CraftTypeBaseService");
                return false;
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var rowsAffected = await connection.ExecuteAsync(InsertSql, craftType);

                if (rowsAffected > 0)
                {
                    LoggingService.LogDebug($"Successfully inserted craft type: {craftType.Code} - {craftType.Description}", "CraftTypeBaseService");
                    ErrorManager.ShowUserSuccessToast($"تم إضافة نوع الحرفة بنجاح\nالرمز: {craftType.Code}", "تم الإضافة", "CraftTypeBaseService");
                    return true;
                }
                else
                {
                    LoggingService.LogWarning($"No rows affected when inserting craft type: {craftType.Code}", "CraftTypeBaseService");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, $"فشل في إضافة نوع الحرفة: {craftType.Code}", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "CraftTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Clears all caches (search and data).
        /// Useful when craft data is modified and caches need to be invalidated.
        /// </summary>
        public void ClearCache()
        {
            try
            {
                // Clear search cache
                _searchCache?.Dispose();
                _searchCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 100,
                    CompactionPercentage = 0.25
                });

                // Clear data cache
                _dataCache?.Dispose();
                _dataCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 50,
                    CompactionPercentage = 0.25
                });

                // Reset cache statistics
                _searchCacheHits = 0;
                _searchCacheMisses = 0;
                _dataCacheHits = 0;
                _dataCacheMisses = 0;

                LoggingService.LogDebug("All caches cleared and recreated", "CraftTypeBaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing caches: {ex.Message}", "CraftTypeBaseService");
            }
        }

        /// <summary>
        /// Gets cache statistics for monitoring and debugging.
        /// </summary>
        /// <returns>Dictionary containing cache performance metrics</returns>
        public Dictionary<string, object> GetCacheStatistics()
        {
            try
            {
                var totalSearchRequests = _searchCacheHits + _searchCacheMisses;
                var totalDataRequests = _dataCacheHits + _dataCacheMisses;

                return new Dictionary<string, object>
                {
                    ["ServiceName"] = ServiceName,
                    ["SearchCacheHits"] = _searchCacheHits,
                    ["SearchCacheMisses"] = _searchCacheMisses,
                    ["SearchCacheHitRatio"] = totalSearchRequests > 0 ? (double)_searchCacheHits / totalSearchRequests : 0.0,
                    ["DataCacheHits"] = _dataCacheHits,
                    ["DataCacheMisses"] = _dataCacheMisses,
                    ["DataCacheHitRatio"] = totalDataRequests > 0 ? (double)_dataCacheHits / totalDataRequests : 0.0,
                    ["TotalCacheHits"] = _searchCacheHits + _dataCacheHits,
                    ["TotalCacheMisses"] = _searchCacheMisses + _dataCacheMisses,
                    ["OverallHitRatio"] = (totalSearchRequests + totalDataRequests) > 0 ?
                        (double)(_searchCacheHits + _dataCacheHits) / (totalSearchRequests + totalDataRequests) : 0.0
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting cache statistics: {ex.Message}", "CraftTypeBaseService");
                return new Dictionary<string, object> { ["Error"] = ex.Message };
            }
        }

        /// <summary>
        /// Warms up the cache by preloading frequently accessed craft types.
        /// This method should be called during application startup for optimal performance.
        /// </summary>
        public async Task WarmupCacheAsync()
        {
            try
            {
                LoggingService.LogDebug("Starting cache warmup for CraftTypeBaseService", "CraftTypeBaseService");

                // Preload all craft types
                await GetAllAsync().ConfigureAwait(false);

                // Preload common search terms (these are typical search patterns in UFU2)
                var commonSearchTerms = new[] { "تجاري", "حرفي", "مهني", "صناعة", "تقليدي", "Commercial", "Craft", "Professional", "Traditional" };

                foreach (var term in commonSearchTerms)
                {
                    try
                    {
                        await SearchByDescriptionAsync(term, 5).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogWarning($"Error warming up cache for search term '{term}': {ex.Message}", "CraftTypeBaseService");
                    }
                }

                LoggingService.LogInfo("Cache warmup completed for CraftTypeBaseService", "CraftTypeBaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache warmup: {ex.Message}", "CraftTypeBaseService");
            }
        }

        /// <summary>
        /// Invalidates specific cache entries based on data change events.
        /// </summary>
        /// <param name="invalidationContext">Context information about what data changed</param>
        public void InvalidateCache(CacheInvalidationContext invalidationContext)
        {
            try
            {
                if (invalidationContext == null)
                    return;

                LoggingService.LogDebug($"Invalidating CraftTypeBaseService cache for {invalidationContext.DataType} ({invalidationContext.InvalidationType})", "CraftTypeBaseService");

                switch (invalidationContext.InvalidationType)
                {
                    case CacheInvalidationType.Full:
                        // Clear all caches
                        ClearCache();
                        break;

                    case CacheInvalidationType.Create:
                    case CacheInvalidationType.Delete:
                        // Clear data cache (affects GetAllAsync results)
                        _dataCache?.Remove("crafts_all");
                        break;

                    case CacheInvalidationType.Update:
                        // Clear specific item cache if DataId is provided
                        if (!string.IsNullOrEmpty(invalidationContext.DataId))
                        {
                            string cacheKey = $"craft_code_{invalidationContext.DataId.ToLowerInvariant()}";
                            _dataCache?.Remove(cacheKey);
                            _dataCache?.Remove("crafts_all");

                            // If the change affects description (searchable content), clear search cache too
                            if (invalidationContext.AdditionalContext.ContainsKey("DescriptionChanged") &&
                                (bool)invalidationContext.AdditionalContext["DescriptionChanged"])
                            {
                                // Clear all search cache entries
                                _searchCache?.Dispose();
                                _searchCache = new MemoryCache(new MemoryCacheOptions
                                {
                                    SizeLimit = 100,
                                    CompactionPercentage = 0.25
                                });
                            }
                        }
                        break;
                }

                LoggingService.LogDebug($"Cache invalidation completed for CraftTypeBaseService", "CraftTypeBaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache invalidation: {ex.Message}", "CraftTypeBaseService");
            }
        }

        /// <summary>
        /// Gets cache health information for monitoring.
        /// </summary>
        /// <returns>Cache health metrics</returns>
        public CacheHealthInfo GetCacheHealth()
        {
            try
            {
                var stats = GetCacheStatistics();
                var overallHitRatio = (double)stats["OverallHitRatio"];
                var totalRequests = (int)stats["TotalCacheHits"] + (int)stats["TotalCacheMisses"];

                return new CacheHealthInfo
                {
                    HitRatio = overallHitRatio,
                    ItemCount = totalRequests, // Approximation
                    MemoryUsageBytes = 0, // Would need more complex calculation
                    IsHealthy = overallHitRatio >= 0.7 && totalRequests > 0, // 70% hit ratio threshold
                    HealthStatus = overallHitRatio >= 0.7 ? "Healthy" :
                                  totalRequests == 0 ? "No Activity" : "Poor Performance"
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting cache health: {ex.Message}", "CraftTypeBaseService");
                return new CacheHealthInfo
                {
                    IsHealthy = false,
                    HealthStatus = $"Error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Disposes of the service and its resources.
        /// </summary>
        public void Dispose()
        {
            try
            {
                _searchCache?.Dispose();
                _dataCache?.Dispose();

                var stats = GetCacheStatistics();
                LoggingService.LogInfo($"CraftTypeBaseService disposed. Final cache stats - Search hit ratio: {stats["SearchCacheHitRatio"]:P1}, Data hit ratio: {stats["DataCacheHitRatio"]:P1}", "CraftTypeBaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disposing CraftTypeBaseService: {ex.Message}", "CraftTypeBaseService");
            }
        }
    }
}
