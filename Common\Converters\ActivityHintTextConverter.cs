using System;
using System.Globalization;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that provides dynamic hint text for activity form fields based on the selected activity type.
    /// Returns appropriate Arabic hint text for different activity types and field contexts.
    /// Used to customize form field hints in NActivityDetailView based on the current activity selection.
    /// </summary>
    public class ActivityHintTextConverter : IValueConverter
    {
        /// <summary>
        /// Converts the selected activity type to appropriate hint text for the specified field.
        /// </summary>
        /// <param name="value">The selected activity type (MainCommercial, SecondaryCommercial, Craft, Professional)</param>
        /// <param name="targetType">The target type (should be string)</param>
        /// <param name="parameter">The field name to get hint text for (ActivityDescription, ActivityStartDate, CommercialRegister, ActivityLocation)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Localized Arabic hint text for the specified field and activity type</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var activityType = value?.ToString() ?? "MainCommercial";
                var fieldName = parameter?.ToString() ?? string.Empty;

                return GetHintText(activityType, fieldName);
            }
            catch (Exception)
            {
                // Return default hint text if any error occurs
                return GetDefaultHintText(parameter?.ToString() ?? string.Empty);
            }
        }

        /// <summary>
        /// Not implemented for this converter as it's one-way only.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ActivityHintTextConverter is a one-way converter.");
        }

        /// <summary>
        /// Gets the appropriate hint text based on activity type and field name.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <param name="fieldName">The field name to get hint text for</param>
        /// <returns>Localized Arabic hint text</returns>
        private static string GetHintText(string activityType, string fieldName)
        {
            return fieldName switch
            {
                "ActivityDescription" => GetActivityDescriptionHint(activityType),
                "ActivityStartDate" => GetActivityStartDateHint(activityType),
                "CommercialRegister" => GetCommercialRegisterHint(activityType),
                "ActivityLocation" => GetActivityLocationHint(activityType),
                _ => GetDefaultHintText(fieldName)
            };
        }

        /// <summary>
        /// Gets activity description hint text based on activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Appropriate hint text for activity description field</returns>
        private static string GetActivityDescriptionHint(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "وصف النشاط التجاري الرئيسي",
                "SecondaryCommercial" => "وصف النشاط التجاري الثانوي",
                "Craft" => "وصف النشاط الحرفي",
                "Professional" => "وصف النشاط المهني",
                _ => "وصف النشاط"
            };
        }

        /// <summary>
        /// Gets activity start date hint text based on activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Appropriate hint text for activity start date field</returns>
        private static string GetActivityStartDateHint(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "تاريخ بداية النشاط التجاري",
                "SecondaryCommercial" => "تاريخ بداية النشاط الثانوي",
                "Craft" => "تاريخ بداية النشاط الحرفي",
                "Professional" => "تاريخ بداية النشاط المهني",
                _ => "تاريخ بداية النشاط"
            };
        }

        /// <summary>
        /// Gets commercial register hint text based on activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Appropriate hint text for commercial register field</returns>
        private static string GetCommercialRegisterHint(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "رقم السجل التجاري الرئيسي",
                "SecondaryCommercial" => "رقم السجل التجاري الثانوي",
                "Craft" => "رقم التسجيل",
                "Professional" => "رقم القرار",
                _ => "رقم السجل التجاري"
            };
        }

        /// <summary>
        /// Gets activity location hint text based on activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Appropriate hint text for activity location field</returns>
        private static string GetActivityLocationHint(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "عنوان المحل التجاري الرئيسي",
                "SecondaryCommercial" => "عنوان المحل التجاري الثانوي",
                "Craft" => "عنوان ورشة الحرفة",
                "Professional" => "عنوان مكتب العمل المهني",
                _ => "عنوان مكان النشاط"
            };
        }

        /// <summary>
        /// Gets default hint text for unknown field names.
        /// </summary>
        /// <param name="fieldName">The field name</param>
        /// <returns>Default hint text</returns>
        private static string GetDefaultHintText(string fieldName)
        {
            return fieldName switch
            {
                "ActivityDescription" => "وصف النشاط",
                "ActivityStartDate" => "تاريخ بداية النشاط",
                "CommercialRegister" => "رقم السجل التجاري",
                "ActivityLocation" => "عنوان مكان النشاط",
                _ => "معلومات النشاط"
            };
        }
    }
}
