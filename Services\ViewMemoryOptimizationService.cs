using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// View memory optimization service for UFU2 application.
    /// Monitors and optimizes memory usage of view components and background-loaded content.
    /// Implements Phase 2C Task 3.2 requirements for memory usage optimization.
    /// </summary>
    public class ViewMemoryOptimizationService : IDisposable
    {
        #region Private Fields

        private readonly ConcurrentDictionary<string, ViewMemoryInfo> _viewMemoryTracking;
        private readonly ConcurrentDictionary<WeakReference, ViewCleanupInfo> _viewCleanupQueue;
        private readonly Timer _memoryMonitoringTimer;
        private readonly Timer _cleanupTimer;
        private readonly MemoryPressureHandler? _memoryPressureHandler;
        private readonly Process _currentProcess;
        private bool _disposed = false;

        // Configuration
        private readonly TimeSpan _monitoringInterval = TimeSpan.FromMinutes(2);
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(5);
        private readonly long _memoryThresholdMB = 300; // Start optimization at 300MB
        private readonly long _criticalMemoryThresholdMB = 500; // Aggressive cleanup at 500MB
        private readonly TimeSpan _viewRetentionTime = TimeSpan.FromMinutes(10);

        // Performance tracking
        private static long _totalViewsTracked = 0;
        private static long _totalViewsCleanedUp = 0;
        private static long _memoryFreedMB = 0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ViewMemoryOptimizationService.
        /// </summary>
        public ViewMemoryOptimizationService()
        {
            _viewMemoryTracking = new ConcurrentDictionary<string, ViewMemoryInfo>();
            _viewCleanupQueue = new ConcurrentDictionary<WeakReference, ViewCleanupInfo>();
            _currentProcess = Process.GetCurrentProcess();

            // Get memory pressure handler from ServiceLocator
            _memoryPressureHandler = ServiceLocator.GetMemoryPressureHandler();

            // Start monitoring timers
            _memoryMonitoringTimer = new Timer(
                MonitorMemoryUsage,
                null,
                _monitoringInterval,
                _monitoringInterval);

            _cleanupTimer = new Timer(
                PerformViewCleanup,
                null,
                _cleanupInterval,
                _cleanupInterval);

            LoggingService.LogInfo("ViewMemoryOptimizationService initialized", "ViewMemoryOptimizationService");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Registers a view for memory tracking and optimization.
        /// </summary>
        /// <param name="viewId">Unique identifier for the view</param>
        /// <param name="view">The view to track</param>
        /// <param name="estimatedMemoryUsageMB">Estimated memory usage in MB</param>
        public void RegisterView(string viewId, FrameworkElement view, double estimatedMemoryUsageMB = 0)
        {
            if (string.IsNullOrEmpty(viewId) || view == null)
                return;

            var memoryInfo = new ViewMemoryInfo
            {
                ViewId = viewId,
                ViewType = view.GetType().Name,
                RegisteredAt = DateTime.UtcNow,
                EstimatedMemoryUsageMB = estimatedMemoryUsageMB,
                LastAccessedAt = DateTime.UtcNow
            };

            _viewMemoryTracking[viewId] = memoryInfo;

            // Add to cleanup queue with weak reference
            var weakRef = new WeakReference(view);
            _viewCleanupQueue[weakRef] = new ViewCleanupInfo
            {
                ViewId = viewId,
                RegisteredAt = DateTime.UtcNow,
                ViewType = view.GetType().Name
            };

            System.Threading.Interlocked.Increment(ref _totalViewsTracked);

            LoggingService.LogDebug($"Registered view for memory tracking: {viewId} ({view.GetType().Name})", "ViewMemoryOptimizationService");
        }

        /// <summary>
        /// Updates the last accessed time for a view.
        /// </summary>
        /// <param name="viewId">View identifier</param>
        public void UpdateViewAccess(string viewId)
        {
            if (_viewMemoryTracking.TryGetValue(viewId, out var memoryInfo))
            {
                memoryInfo.LastAccessedAt = DateTime.UtcNow;
                memoryInfo.AccessCount++;
            }
        }

        /// <summary>
        /// Manually triggers cleanup for a specific view.
        /// </summary>
        /// <param name="viewId">View identifier to cleanup</param>
        public void CleanupView(string viewId)
        {
            if (_viewMemoryTracking.TryRemove(viewId, out var memoryInfo))
            {
                // Find and remove from cleanup queue
                var toRemove = _viewCleanupQueue
                    .Where(kvp => kvp.Value.ViewId == viewId)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var weakRef in toRemove)
                {
                    _viewCleanupQueue.TryRemove(weakRef, out _);
                }

                System.Threading.Interlocked.Increment(ref _totalViewsCleanedUp);
                System.Threading.Interlocked.Add(ref _memoryFreedMB, (long)memoryInfo.EstimatedMemoryUsageMB);

                LoggingService.LogDebug($"Cleaned up view: {viewId}", "ViewMemoryOptimizationService");
            }
        }

        /// <summary>
        /// Forces memory optimization based on current memory pressure.
        /// </summary>
        public void ForceMemoryOptimization()
        {
            var currentMemoryMB = GetCurrentMemoryUsageMB();
            
            if (currentMemoryMB > _criticalMemoryThresholdMB)
            {
                PerformAggressiveCleanup();
            }
            else if (currentMemoryMB > _memoryThresholdMB)
            {
                PerformSelectiveCleanup();
            }
        }

        /// <summary>
        /// Gets memory optimization statistics.
        /// </summary>
        /// <returns>Memory optimization statistics</returns>
        public ViewMemoryOptimizationStatistics GetOptimizationStatistics()
        {
            var currentMemoryMB = GetCurrentMemoryUsageMB();
            var trackedViews = _viewMemoryTracking.Count;
            var estimatedTrackedMemoryMB = _viewMemoryTracking.Values.Sum(v => v.EstimatedMemoryUsageMB);

            return new ViewMemoryOptimizationStatistics
            {
                CurrentMemoryUsageMB = currentMemoryMB,
                TotalViewsTracked = _totalViewsTracked,
                TotalViewsCleanedUp = _totalViewsCleanedUp,
                MemoryFreedMB = _memoryFreedMB,
                ActiveTrackedViews = trackedViews,
                EstimatedTrackedMemoryMB = estimatedTrackedMemoryMB,
                MemoryOptimizationRatio = _totalViewsTracked > 0 ? (double)_totalViewsCleanedUp / _totalViewsTracked : 0.0,
                IsMemoryPressureHigh = currentMemoryMB > _memoryThresholdMB
            };
        }

        /// <summary>
        /// Gets detailed information about tracked views.
        /// </summary>
        /// <returns>Dictionary of view IDs and their memory information</returns>
        public Dictionary<string, ViewMemoryInfo> GetTrackedViews()
        {
            return new Dictionary<string, ViewMemoryInfo>(_viewMemoryTracking);
        }

        /// <summary>
        /// Optimizes memory usage for background-loaded components.
        /// </summary>
        /// <param name="maxRetentionTime">Maximum time to retain unused components</param>
        public void OptimizeBackgroundComponents(TimeSpan? maxRetentionTime = null)
        {
            var retentionTime = maxRetentionTime ?? _viewRetentionTime;
            var cutoffTime = DateTime.UtcNow - retentionTime;

            var viewsToCleanup = _viewMemoryTracking
                .Where(kvp => kvp.Value.LastAccessedAt < cutoffTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var viewId in viewsToCleanup)
            {
                CleanupView(viewId);
            }

            if (viewsToCleanup.Count > 0)
            {
                LoggingService.LogInfo($"Optimized {viewsToCleanup.Count} background components", "ViewMemoryOptimizationService");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Gets current memory usage in MB.
        /// </summary>
        private long GetCurrentMemoryUsageMB()
        {
            try
            {
                _currentProcess.Refresh();
                return _currentProcess.WorkingSet64 / (1024 * 1024);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Monitors memory usage periodically.
        /// </summary>
        private void MonitorMemoryUsage(object? state)
        {
            try
            {
                var currentMemoryMB = GetCurrentMemoryUsageMB();
                
                if (currentMemoryMB > _criticalMemoryThresholdMB)
                {
                    LoggingService.LogWarning($"Critical memory usage detected: {currentMemoryMB}MB", "ViewMemoryOptimizationService");
                    PerformAggressiveCleanup();
                }
                else if (currentMemoryMB > _memoryThresholdMB)
                {
                    LoggingService.LogInfo($"High memory usage detected: {currentMemoryMB}MB", "ViewMemoryOptimizationService");
                    PerformSelectiveCleanup();
                }

                // Clean up dead weak references
                CleanupDeadReferences();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error monitoring memory usage: {ex.Message}", "ViewMemoryOptimizationService");
            }
        }

        /// <summary>
        /// Performs periodic view cleanup.
        /// </summary>
        private void PerformViewCleanup(object? state)
        {
            try
            {
                OptimizeBackgroundComponents();
                CleanupDeadReferences();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error performing view cleanup: {ex.Message}", "ViewMemoryOptimizationService");
            }
        }

        /// <summary>
        /// Performs selective cleanup of least recently used views.
        /// </summary>
        private void PerformSelectiveCleanup()
        {
            var lruViews = _viewMemoryTracking
                .OrderBy(kvp => kvp.Value.LastAccessedAt)
                .Take(_viewMemoryTracking.Count / 3) // Clean up 1/3 of views
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var viewId in lruViews)
            {
                CleanupView(viewId);
            }

            LoggingService.LogInfo($"Performed selective cleanup of {lruViews.Count} views", "ViewMemoryOptimizationService");
        }

        /// <summary>
        /// Performs aggressive cleanup of all non-critical views.
        /// </summary>
        private void PerformAggressiveCleanup()
        {
            var cutoffTime = DateTime.UtcNow - TimeSpan.FromMinutes(2); // Very recent access threshold
            var viewsToCleanup = _viewMemoryTracking
                .Where(kvp => kvp.Value.LastAccessedAt < cutoffTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var viewId in viewsToCleanup)
            {
                CleanupView(viewId);
            }

            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            LoggingService.LogWarning($"Performed aggressive cleanup of {viewsToCleanup.Count} views", "ViewMemoryOptimizationService");
        }

        /// <summary>
        /// Cleans up dead weak references.
        /// </summary>
        private void CleanupDeadReferences()
        {
            var deadReferences = _viewCleanupQueue
                .Where(kvp => !kvp.Key.IsAlive)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var deadRef in deadReferences)
            {
                if (_viewCleanupQueue.TryRemove(deadRef, out var cleanupInfo))
                {
                    _viewMemoryTracking.TryRemove(cleanupInfo.ViewId, out _);
                }
            }

            if (deadReferences.Count > 0)
            {
                LoggingService.LogDebug($"Cleaned up {deadReferences.Count} dead view references", "ViewMemoryOptimizationService");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the ViewMemoryOptimizationService.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _memoryMonitoringTimer?.Dispose();
                _cleanupTimer?.Dispose();
                _currentProcess?.Dispose();
                _disposed = true;

                LoggingService.LogInfo("ViewMemoryOptimizationService disposed", "ViewMemoryOptimizationService");
            }
        }

        #endregion
    }

    /// <summary>
    /// Memory information for a tracked view.
    /// </summary>
    public class ViewMemoryInfo
    {
        public string ViewId { get; set; } = string.Empty;
        public string ViewType { get; set; } = string.Empty;
        public DateTime RegisteredAt { get; set; }
        public DateTime LastAccessedAt { get; set; }
        public double EstimatedMemoryUsageMB { get; set; }
        public int AccessCount { get; set; }
    }

    /// <summary>
    /// Cleanup information for a view.
    /// </summary>
    public class ViewCleanupInfo
    {
        public string ViewId { get; set; } = string.Empty;
        public string ViewType { get; set; } = string.Empty;
        public DateTime RegisteredAt { get; set; }
    }

    /// <summary>
    /// Statistics for view memory optimization.
    /// </summary>
    public class ViewMemoryOptimizationStatistics
    {
        public long CurrentMemoryUsageMB { get; set; }
        public long TotalViewsTracked { get; set; }
        public long TotalViewsCleanedUp { get; set; }
        public long MemoryFreedMB { get; set; }
        public int ActiveTrackedViews { get; set; }
        public double EstimatedTrackedMemoryMB { get; set; }
        public double MemoryOptimizationRatio { get; set; }
        public bool IsMemoryPressureHigh { get; set; }
    }
}
