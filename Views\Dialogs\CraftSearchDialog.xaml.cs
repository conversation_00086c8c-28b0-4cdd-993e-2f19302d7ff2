using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Dialog for searching and selecting craft types from the CraftTypeBase database.
    /// Provides real-time search functionality with code and description filtering.
    /// </summary>
    public partial class CraftSearchDialog : UserControl
    {
        #region Private Fields

        private readonly CraftTypeBaseService? _craftTypeService;
        private readonly ObservableCollection<CraftTypeBaseModel> _searchResults;
        private CraftTypeBaseModel? _selectedCraft;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the CraftSearchDialog class.
        /// </summary>
        public CraftSearchDialog()
        {
            InitializeComponent();

            // Initialize search results collection
            _searchResults = new ObservableCollection<CraftTypeBaseModel>();
            ResultsListView.ItemsSource = _searchResults;

            // Get CraftTypeBaseService from ServiceLocator
            if (ServiceLocator.TryGetService<CraftTypeBaseService>(out var craftTypeService))
            {
                _craftTypeService = craftTypeService;
            }
            else
            {
                LoggingService.LogWarning("CraftTypeBaseService not available in CraftSearchDialog", "CraftSearchDialog");
            }

            // Load initial data
            _ = LoadInitialDataAsync();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the selected craft type.
        /// </summary>
        /// <returns>The selected CraftTypeBaseModel or null if none selected</returns>
        public CraftTypeBaseModel? GetSelectedCraft()
        {
            return _selectedCraft;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Loads initial craft data to display in the results list.
        /// Shows the first 20 craft types by default.
        /// </summary>
        private async Task LoadInitialDataAsync()
        {
            try
            {
                if (_craftTypeService == null)
                    return;

                var allCrafts = await _craftTypeService.GetAllAsync();
                var initialCrafts = allCrafts.Take(20).ToList();

                _searchResults.Clear();
                foreach (var craft in initialCrafts)
                {
                    _searchResults.Add(craft);
                }

                LoggingService.LogDebug($"Loaded {initialCrafts.Count} initial craft types", "CraftSearchDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading initial craft data: {ex.Message}", "CraftSearchDialog");
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ أثناء تحميل بيانات الحرف",
                    "خطأ في التحميل",
                    "CraftSearchDialog");
            }
        }

        /// <summary>
        /// Performs search based on the provided search term.
        /// Searches both code and description fields.
        /// </summary>
        /// <param name="searchTerm">The search term</param>
        private async Task PerformSearchAsync(string searchTerm)
        {
            try
            {
                if (_craftTypeService == null)
                    return;

                _searchResults.Clear();

                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    // Load initial data if search is empty
                    await LoadInitialDataAsync();
                    return;
                }

                // Search by description using enhanced search with fuzzy matching
                var descriptionResults = await _craftTypeService.SearchByDescriptionEnhancedAsync(searchTerm, 50, 0.3);
                
                // Search by code if the search term looks like a code
                var codeResults = new List<CraftTypeBaseModel>();
                if (searchTerm.All(c => char.IsDigit(c) || c == '-'))
                {
                    var allCrafts = await _craftTypeService.GetAllAsync();
                    codeResults = allCrafts.Where(c => c.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // Combine and deduplicate results
                var combinedResults = descriptionResults.Union(codeResults, new CraftCodeComparer()).Take(50).ToList();

                foreach (var craft in combinedResults)
                {
                    _searchResults.Add(craft);
                }

                LoggingService.LogDebug($"Search for '{searchTerm}' returned {combinedResults.Count} results", "CraftSearchDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error performing search: {ex.Message}", "CraftSearchDialog");
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ أثناء البحث",
                    "خطأ في البحث",
                    "CraftSearchDialog");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the TextChanged event for the search TextBox.
        /// Performs real-time search with a small delay to avoid excessive database queries.
        /// </summary>
        private async void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is not TextBox textBox)
                return;

            var searchTerm = textBox.Text?.Trim() ?? string.Empty;
            
            // Add a small delay to avoid excessive searches while typing
            await Task.Delay(300);
            
            // Check if the search term is still the same (user might have continued typing)
            if (textBox.Text?.Trim() == searchTerm)
            {
                await PerformSearchAsync(searchTerm);
            }
        }

        /// <summary>
        /// Handles the SelectionChanged event for the results ListView.
        /// Updates the selected craft and enables/disables the Select button.
        /// </summary>
        private void ResultsListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is not ListView listView)
                return;

            _selectedCraft = listView.SelectedItem as CraftTypeBaseModel;
            SelectButton.IsEnabled = _selectedCraft != null;

            if (_selectedCraft != null)
            {
                LoggingService.LogDebug($"Craft selected: {_selectedCraft.Code} - {_selectedCraft.Description}", "CraftSearchDialog");
            }
        }

        /// <summary>
        /// Handles the Click event for the Select button.
        /// Closes the dialog with the selected craft as the result.
        /// </summary>
        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCraft == null)
                return;

            try
            {
                LoggingService.LogInfo($"Craft selected for use: {_selectedCraft.Code} - {_selectedCraft.Description}", "CraftSearchDialog");
                
                // Close dialog with selected craft as result
                DialogHost.CloseDialogCommand.Execute(_selectedCraft, this);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error selecting craft: {ex.Message}", "CraftSearchDialog");
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ أثناء اختيار الحرفة",
                    "خطأ في الاختيار",
                    "CraftSearchDialog");
            }
        }

        /// <summary>
        /// Handles the Click event for the Add New button.
        /// Opens the AddCraftTypeDialog to create a new craft type.
        /// </summary>
        private async void AddNewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Opening AddCraftTypeDialog from CraftSearchDialog", "CraftSearchDialog");

                // Get the current search term to use as a suggestion for the new craft code
                string searchTerm = SearchTextBox.Text?.Trim() ?? string.Empty;

                // If search term looks like a craft code, use it; otherwise use empty string
                string suggestedCode = string.Empty;
                if (searchTerm.Length <= 9 && searchTerm.All(c => char.IsDigit(c) || c == '-'))
                {
                    suggestedCode = searchTerm;
                }

                // Show the AddCraftTypeDialog using the CraftSearchDialogHost
                var newCraftType = await Views.Dialogs.AddCraftTypeDialog.ShowDialogAsync(suggestedCode, "CraftSearchDialogHost");

                if (newCraftType != null)
                {
                    LoggingService.LogInfo($"New craft type created: {newCraftType.Code} - {newCraftType.Description}", "CraftSearchDialog");

                    // Close the search dialog with the new craft type as result
                    DialogHost.CloseDialogCommand.Execute(newCraftType, this);
                }
                else
                {
                    LoggingService.LogDebug("AddCraftTypeDialog was cancelled", "CraftSearchDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening AddCraftTypeDialog: {ex.Message}", "CraftSearchDialog");
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ أثناء فتح نافذة إضافة الحرفة",
                    "خطأ في النظام",
                    "CraftSearchDialog");
            }
        }

        /// <summary>
        /// Handles the Click event for the Cancel button.
        /// Closes the dialog without selecting any craft.
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug("Craft search dialog cancelled", "CraftSearchDialog");

                // Close dialog with false result
                DialogHost.CloseDialogCommand.Execute(false, this);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error cancelling craft search: {ex.Message}", "CraftSearchDialog");
            }
        }

        #endregion

        #region Helper Classes

        /// <summary>
        /// Comparer for CraftTypeBaseModel to avoid duplicates in search results.
        /// </summary>
        private class CraftCodeComparer : IEqualityComparer<CraftTypeBaseModel>
        {
            public bool Equals(CraftTypeBaseModel? x, CraftTypeBaseModel? y)
            {
                if (x == null && y == null) return true;
                if (x == null || y == null) return false;
                return string.Equals(x.Code, y.Code, StringComparison.OrdinalIgnoreCase);
            }

            public int GetHashCode(CraftTypeBaseModel obj)
            {
                return obj?.Code?.GetHashCode() ?? 0;
            }
        }

        #endregion
    }
}
