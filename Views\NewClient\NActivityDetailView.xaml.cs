using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using UFU2.Common;
using UFU2.Common.Extensions;
using UFU2.Common.Utilities;
using UFU2.ViewModels;
using UFU2.Views.Dialogs;
using MaterialDesignThemes.Wpf;

namespace UFU2.Views.NewClient
{
    /// <summary>
    /// Interaction logic for NActivityDetailView.xaml
    /// A UserControl that provides a form for collecting detailed activity information.
    /// Integrates with UFU2 design system and follows MaterialDesign patterns.
    /// Supports Arabic RTL layout and proper accessibility features.
    /// 
    /// This component displays activity details including:
    /// - Activity name, code, and type
    /// - Location and registration information  
    /// - Financial details and status information
    /// - Employee count and operational status
    /// 
    /// The component follows UFU2 architectural patterns with:
    /// - MaterialDesign + WPF-UI hybrid framework integration
    /// - Arabic RTL layout support with proper text alignment
    /// - UFU2 design token consistency for styling
    /// - Clean XAML-only implementation without business logic
    /// - Accessibility attributes for screen readers
    /// - Performance optimizations with UseLayoutRounding
    /// </summary>
    public partial class NActivityDetailView : UserControl, IDisposable
    {
        #region Private Fields

        /// <summary>
        /// Flag to prevent dialog opening during programmatic ComboBox changes (e.g., tab switching).
        /// When true, the SelectionChanged event will not trigger dialog opening.
        /// </summary>
        private bool _isUpdatingProgrammatically = false;

        /// <summary>
        /// Cache for frequently accessed values to reduce property access overhead.
        /// </summary>
        private readonly Dictionary<string, object> _cachedValues = new();

        /// <summary>
        /// Flag to track initialization state for performance optimization.
        /// </summary>
        private bool _isInitialized = false;

        /// <summary>
        /// Weak reference to cached ViewModel to avoid repeated DataContext casting.
        /// </summary>
        private WeakReference<NewClientViewModel>? _cachedViewModel;

        // OPTIMIZATION: Phase 2B UI optimization fields

        /// <summary>
        /// Grouped validation debouncing to reduce validation frequency.
        /// </summary>
        private readonly DispatcherTimer _validationDebounceTimer;

        /// <summary>
        /// Performance monitoring counters.
        /// </summary>
        private static int _validationTriggerCount = 0;
        private static int _debouncedValidationCount = 0;

        /// <summary>
        /// Conditional binding state tracking for inactive form sections.
        /// </summary>
        private bool _isFormActive = true;
        private string _lastActivityStatus = string.Empty;

        /// <summary>
        /// Debounce delay for grouped validation (500ms).
        /// </summary>
        private const int ValidationDebounceMs = 500;

        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the NActivityDetailView class.
        /// Sets up the form with proper Arabic RTL support and UFU2 styling.
        /// The DataContext should be set by the parent view (NewClientView) to ensure
        /// proper data sharing between the form fields and command buttons.
        /// Configures automatic date formatting for the ActivityStartDateTextBox control.
        ///
        /// This is a static UI component without business logic integration.
        /// Future implementations can add ViewModel binding and command handling
        /// while maintaining the current visual structure and styling.
        /// </summary>
        public NActivityDetailView()
        {
            InitializeComponent();

            // OPTIMIZATION: Initialize debounce timer for grouped validation
            _validationDebounceTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(ValidationDebounceMs)
            };
            _validationDebounceTimer.Tick += ValidationDebounceTimer_Tick;

            // Attach automatic date formatting to the ActivityStartDateTextBox
            // This provides DD/MM/YYYY formatting with real-time and focus-loss triggers
            TextBoxExtensions.AttachDateFormatting(ActivityStartDateTextBox);

            // Attach LostFocus handler for auto-populating payment years
            ActivityStartDateTextBox.LostFocus += ActivityStartDateTextBox_LostFocus;

            // Subscribe to DataContext changes to set up ViewModel event handlers
            DataContextChanged += NActivityDetailView_DataContextChanged;

            // Performance optimization: Mark as initialized
            _isInitialized = true;

            // Note: DataContext is set by the parent NewClientView to ensure
            // the same ViewModel instance is shared between components.
            // This component currently provides static UI structure only.
        }
        #endregion

        #region Performance Optimization Methods

        /// <summary>
        /// Gets the cached ViewModel instance to avoid repeated DataContext casting.
        /// Provides significant performance improvement for frequent ViewModel access.
        /// </summary>
        /// <returns>The NewClientViewModel instance or null if not available</returns>
        private NewClientViewModel? GetCachedViewModel()
        {
            // Check if we have a valid cached reference
            if (_cachedViewModel?.TryGetTarget(out var cachedVm) == true)
            {
                return cachedVm;
            }

            // Cache miss - get from DataContext and cache it
            if (DataContext is NewClientViewModel viewModel)
            {
                _cachedViewModel = new WeakReference<NewClientViewModel>(viewModel);
                return viewModel;
            }

            return null;
        }

        /// <summary>
        /// Caches a value to reduce repeated property access overhead.
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        private void CacheValue(string key, object value)
        {
            _cachedValues[key] = value;
        }

        /// <summary>
        /// Gets a cached value if available.
        /// </summary>
        /// <typeparam name="T">Type of the cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached value or default</returns>
        private T? GetCachedValue<T>(string key)
        {
            return _cachedValues.TryGetValue(key, out var value) && value is T typedValue ? typedValue : default;
        }

        /// <summary>
        /// Clears the value cache to prevent memory leaks.
        /// </summary>
        private void ClearCache()
        {
            _cachedValues.Clear();
            _cachedViewModel = null;
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the TextChanged event for ActivityCodeTextBox to provide real-time formatting for craft codes.
        /// Formats craft codes in XX-XX-XXX pattern and restricts input to numeric characters only.
        /// </summary>
        private void ActivityCodeTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is not TextBox textBox)
                return;

            var viewModel = GetCachedViewModel();
            if (viewModel?.SelectedActivityType != "Craft")
                return;

            // Get current cursor position
            int cursorPosition = textBox.SelectionStart;
            string originalText = textBox.Text;

            // Use shared utility for formatting
            string formattedText = CraftCodeFormatter.ProcessCraftCodeInput(originalText);

            // Update text if it changed
            if (formattedText != originalText)
            {
                textBox.Text = formattedText;

                // Adjust cursor position using shared utility
                int newCursorPosition = CraftCodeFormatter.CalculateNewCursorPosition(originalText, formattedText, cursorPosition);
                textBox.SelectionStart = Math.Min(newCursorPosition, formattedText.Length);
            }
        }


        /// <summary>
        /// Handles the LostFocus event for ActivityStartDateTextBox.
        /// Auto-populates G12 and BIS payment years when a valid date is entered.
        /// Optimized with cached ViewModel access for improved performance.
        /// </summary>
        private void ActivityStartDateTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (sender is not TextBox textBox)
                return;

            var viewModel = GetCachedViewModel();
            if (viewModel == null)
                return;

            var activityStartDate = textBox.Text?.Trim();

            // Check if the date is valid
            if (!string.IsNullOrEmpty(activityStartDate) && PaymentYearRangeCalculator.IsValidActivityStartDate(activityStartDate))
            {
                // Auto-populate payment years for the current activity tab
                viewModel.AutoPopulatePaymentYears(activityStartDate);
            }
            else
            {
                // Clear payment years if date becomes invalid
                viewModel.ClearPaymentYears();
            }
        }

        /// <summary>
        /// Handles the DataContextChanged event to set up ViewModel event subscriptions.
        /// Optimized with caching and performance improvements.
        /// </summary>
        private void NActivityDetailView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            try
            {
                // Clear cache when DataContext changes
                ClearCache();

                // Unsubscribe from previous ViewModel events
                if (e.OldValue is NewClientViewModel oldViewModel)
                {
                    oldViewModel.PropertyChanged -= ViewModel_PropertyChanged;
                    oldViewModel.EditStatusRequested -= ViewModel_EditStatusRequested;
                }

                // Subscribe to new ViewModel events and cache the new ViewModel
                if (e.NewValue is NewClientViewModel newViewModel)
                {
                    newViewModel.PropertyChanged += ViewModel_PropertyChanged;
                    newViewModel.EditStatusRequested += ViewModel_EditStatusRequested;
                    _cachedViewModel = new WeakReference<NewClientViewModel>(newViewModel);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling DataContext change: {ex.Message}", "NActivityDetailView");
            }
        }

        /// <summary>
        /// Handles PropertyChanged events from the ViewModel to detect programmatic changes.
        /// </summary>
        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            try
            {
                // Detect when SelectedActivityType changes (tab switching)
                if (e.PropertyName == nameof(NewClientViewModel.SelectedActivityType))
                {
                    // Set flag to prevent dialog opening during tab switch
                    _isUpdatingProgrammatically = true;

                    // Reset flag after a longer delay to ensure all UI updates complete
                    // This prevents the dialog from opening when switching tabs
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        _isUpdatingProgrammatically = false;
                    }), System.Windows.Threading.DispatcherPriority.ApplicationIdle);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling ViewModel property change: {ex.Message}", "NActivityDetailView");
            }
        }

        /// <summary>
        /// Handles the EditStatusRequested event from the ViewModel.
        /// Opens the ActivityStatusUpdateDialog for editing existing status information.
        /// Optimized with cached ViewModel access for improved performance.
        /// </summary>
        private async void ViewModel_EditStatusRequested()
        {
            try
            {
                var viewModel = GetCachedViewModel();
                if (viewModel == null)
                    return;

                await OpenActivityStatusUpdateDialog(viewModel, "Edit");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling edit status request: {ex.Message}", "NActivityDetailView");
                ErrorManager.LogException(ex, LogLevel.Error, "NActivityDetailView");
                ErrorManager.ShowUserErrorToast("حدث خطأ أثناء فتح نافذة تعديل معلومات الحالة", "خطأ");
            }
        }

        /// <summary>
        /// Handles the SelectionChanged event for ActivityStatusComboBox.
        /// Tracks status changes but doesn't open dialog immediately to prevent auto-popup issues.
        /// </summary>
        private void ActivityStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (sender is not ComboBox comboBox)
                    return;

                var viewModel = GetCachedViewModel();
                if (viewModel == null)
                    return;

                // Skip if this is a programmatic change (e.g., tab switching)
                if (_isUpdatingProgrammatically)
                {
                    LoggingService.LogDebug("Skipping status change tracking - programmatic ComboBox change detected", "NActivityDetailView");
                    return;
                }

                var selectedStatus = comboBox.SelectedItem?.ToString();
                if (string.IsNullOrWhiteSpace(selectedStatus))
                    return;

                // Track the status change but don't open dialog yet
                LoggingService.LogDebug($"Activity status changed to: {selectedStatus}", "NActivityDetailView");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling activity status change: {ex.Message}", "NActivityDetailView");
                ErrorManager.LogException(ex, LogLevel.Error, "NActivityDetailView");
            }
        }

        /// <summary>
        /// Handles the LostFocus event for ActivityStatusComboBox.
        /// Opens the ActivityStatusUpdateDialog when a non-active status is selected and the control loses focus.
        /// This prevents auto-popup issues when switching tabs.
        /// </summary>
        private async void ActivityStatusComboBox_LostFocus(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is not ComboBox comboBox)
                    return;

                var viewModel = GetCachedViewModel();
                if (viewModel == null)
                    return;

                // Skip if this is a programmatic change (e.g., tab switching)
                if (_isUpdatingProgrammatically)
                {
                    LoggingService.LogDebug("Skipping dialog opening - programmatic ComboBox change detected", "NActivityDetailView");
                    return;
                }

                var selectedStatus = comboBox.SelectedItem?.ToString();
                if (string.IsNullOrWhiteSpace(selectedStatus))
                    return;

                // Check if the selected status requires additional information
                if (ShouldOpenUpdateDialog(selectedStatus))
                {
                    await OpenActivityStatusUpdateDialog(viewModel, "StatusChange");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling activity status focus loss: {ex.Message}", "NActivityDetailView");
                ErrorManager.LogException(ex, LogLevel.Error, "NActivityDetailView");
                ErrorManager.ShowUserErrorToast("حدث خطأ أثناء تحديث معلومات النشاط", "خطأ");
            }
        }

        /// <summary>
        /// Opens the ActivityStatusUpdateDialog with current activity data.
        /// Handles both status change and edit scenarios.
        /// </summary>
        /// <param name="viewModel">The NewClientViewModel instance</param>
        /// <param name="context">The context for opening ("StatusChange" or "Edit")</param>
        private async Task OpenActivityStatusUpdateDialog(NewClientViewModel viewModel, string context)
        {
            try
            {
                LoggingService.LogInfo($"Opening ActivityStatusUpdateDialog - Context: {context}, Status: {viewModel.CurrentActivity.ActivityStatus}", "NActivityDetailView");

                // Get current activity data
                var currentActivity = viewModel.CurrentActivity;
                var activityType = viewModel.SelectedActivityType;
                var selectedStatus = currentActivity.ActivityStatus;

                // Create and show the dialog
                var updateDialog = new ActivityStatusUpdateDialog(
                    currentActivity.ActivityUpdateDate,
                    currentActivity.ActivityUpdateNote,
                    selectedStatus,
                    activityType);

                var result = await DialogHost.Show(updateDialog, "NewClientDialogHost");

                // Handle dialog result
                if (result is bool dialogResult && dialogResult && updateDialog.DialogResult)
                {
                    // User saved the update information
                    var (updateDate, updateNote) = updateDialog.GetUpdateData();
                    currentActivity.ActivityUpdateDate = updateDate;
                    currentActivity.ActivityUpdateNote = updateNote;

                    LoggingService.LogInfo($"Activity status update saved - Context: {context}, Date: {updateDate}, Note length: {updateNote?.Length ?? 0}", "NActivityDetailView");
                }
                else
                {
                    // User cancelled - apply default values if fields were empty (only for status changes, not edits)
                    if (context == "StatusChange" &&
                        string.IsNullOrWhiteSpace(currentActivity.ActivityUpdateDate) &&
                        string.IsNullOrWhiteSpace(currentActivity.ActivityUpdateNote))
                    {
                        currentActivity.ActivityUpdateDate = "xx/xx/xxxx";
                        currentActivity.ActivityUpdateNote = "لا توجد معلومات إضافية";
                        LoggingService.LogInfo("Activity status update cancelled, default values applied", "NActivityDetailView");
                    }
                    else
                    {
                        LoggingService.LogInfo($"Activity status update cancelled - Context: {context}, keeping existing values", "NActivityDetailView");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening activity status update dialog: {ex.Message}", "NActivityDetailView");
                ErrorManager.LogException(ex, LogLevel.Error, "NActivityDetailView");
                ErrorManager.ShowUserErrorToast("حدث خطأ أثناء فتح نافذة تحديث معلومات النشاط", "خطأ");
            }
        }

        /// <summary>
        /// Determines whether the ActivityStatusUpdateDialog should be opened for the given status.
        /// </summary>
        /// <param name="status">The activity status to check</param>
        /// <returns>True if dialog should be opened</returns>
        private static bool ShouldOpenUpdateDialog(string status)
        {
            return status switch
            {
                "معدل" => true,        // Modified/Edited
                "غير نشط" => true,     // Inactive
                "شطب" => true,         // Suspended/Cancelled
                _ => false             // Active statuses or unknown
            };
        }
        #endregion

        #region Cleanup and Disposal

        /// <summary>
        /// Handles cleanup when the control is unloaded.
        /// Ensures proper disposal of resources to prevent memory leaks.
        /// </summary>
        private void NActivityDetailView_Unloaded(object sender, RoutedEventArgs e)
        {
            Dispose();
        }

        /// <summary>
        /// Disposes of resources and cleans up event handlers to prevent memory leaks.
        /// </summary>
        public void Dispose()
        {
            try
            {
                // Clear caches
                ClearCache();

                // Unsubscribe from events
                if (GetCachedViewModel() is NewClientViewModel viewModel)
                {
                    viewModel.PropertyChanged -= ViewModel_PropertyChanged;
                    viewModel.EditStatusRequested -= ViewModel_EditStatusRequested;
                }

                // Clean up event handlers
                DataContextChanged -= NActivityDetailView_DataContextChanged;
                if (ActivityStartDateTextBox != null)
                {
                    ActivityStartDateTextBox.LostFocus -= ActivityStartDateTextBox_LostFocus;
                }

                // OPTIMIZATION: Clean up debounce timer
                _validationDebounceTimer?.Stop();

                LoggingService.LogDebug("NActivityDetailView disposed successfully", "NActivityDetailView");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during NActivityDetailView disposal: {ex.Message}", "NActivityDetailView");
            }
        }

        #endregion

        #region Optimization Methods

        /// <summary>
        /// Handles the validation debounce timer tick to process grouped validation.
        /// OPTIMIZED: Reduces validation frequency and improves form responsiveness.
        /// </summary>
        private void ValidationDebounceTimer_Tick(object? sender, EventArgs e)
        {
            _validationDebounceTimer.Stop();

            try
            {
                // Trigger grouped validation for better performance
                if (GetCachedViewModel() is NewClientViewModel viewModel)
                {
                    // Use NewClientViewModel's TriggerValidation method for grouped validation
                    viewModel.TriggerValidation();
                    System.Threading.Interlocked.Increment(ref _debouncedValidationCount);

                    LoggingService.LogDebug("Grouped validation triggered", "NActivityDetailView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in validation debounce timer: {ex.Message}", "NActivityDetailView");
            }
        }

        /// <summary>
        /// Triggers debounced validation for performance optimization.
        /// </summary>
        private void TriggerDebouncedValidation()
        {
            System.Threading.Interlocked.Increment(ref _validationTriggerCount);

            // Start debounce timer to batch validation requests
            _validationDebounceTimer.Stop();
            _validationDebounceTimer.Start();
        }

        /// <summary>
        /// Updates conditional binding state based on activity status.
        /// OPTIMIZED: Reduces binding overhead for inactive form sections.
        /// </summary>
        private void UpdateConditionalBindingState(string activityStatus)
        {
            if (_lastActivityStatus != activityStatus)
            {
                _isFormActive = !string.IsNullOrEmpty(activityStatus) && activityStatus != "Inactive";
                _lastActivityStatus = activityStatus;

                // Update form responsiveness based on activity status
                if (!_isFormActive)
                {
                    // Reduce validation frequency for inactive forms
                    _validationDebounceTimer.Interval = TimeSpan.FromMilliseconds(ValidationDebounceMs * 2);
                }
                else
                {
                    // Restore normal validation frequency for active forms
                    _validationDebounceTimer.Interval = TimeSpan.FromMilliseconds(ValidationDebounceMs);
                }
            }
        }

        /// <summary>
        /// Gets performance statistics for the NActivityDetailView component.
        /// Used for Phase 2B UI optimization monitoring.
        /// </summary>
        /// <returns>Performance statistics including validation counts and debounce effectiveness</returns>
        public static (int ValidationTriggerCount, int DebouncedValidationCount, double DebounceEffectiveness) GetPerformanceStats()
        {
            var triggerCount = _validationTriggerCount;
            var debouncedCount = _debouncedValidationCount;
            var debounceEffectiveness = triggerCount > 0 ? (double)debouncedCount / triggerCount : 0.0;

            return (triggerCount, debouncedCount, debounceEffectiveness);
        }

        /// <summary>
        /// Resets performance counters for testing and monitoring.
        /// </summary>
        public static void ResetPerformanceCounters()
        {
            System.Threading.Interlocked.Exchange(ref _validationTriggerCount, 0);
            System.Threading.Interlocked.Exchange(ref _debouncedValidationCount, 0);
        }

        #endregion
    }
}
