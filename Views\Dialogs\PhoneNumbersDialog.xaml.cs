using System.Windows;
using System.Windows.Controls;
using System.Collections.Specialized;
using UFU2.ViewModels;
using UFU2.Common.Extensions;
using UFU2.Common.Converters;
using UFU2.Common;
using MaterialDesignThemes.Wpf;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for PhoneNumbersDialog.xaml
    /// A UserControl that provides a dialog for managing multiple phone numbers.
    /// Integrates with MaterialDesign DialogHost for consistent modal behavior
    /// and follows UFU2 design patterns with Arabic RTL layout support.
    /// </summary>
    public partial class PhoneNumbersDialog : UserControl
    {
        #region Constructor

        /// <summary>
        /// Initializes a new instance of the PhoneNumbersDialog class.
        /// Sets up phone number formatting and initializes the ViewModel.
        /// </summary>
        public PhoneNumbersDialog()
        {
            InitializeComponent();

            // Attach UFU2 phone number formatting to the phone number input TextBox
            TextBoxExtensions.AttachPhoneNumberFormatting(PhoneNumberTextBox);

            // Set up the ViewModel
            DataContext = new PhoneNumbersDialogViewModel();

            // Set up auto-scroll functionality
            SetupAutoScroll();
        }

        /// <summary>
        /// Initializes a new instance of the PhoneNumbersDialog class with existing phone numbers.
        /// </summary>
        /// <param name="existingPhoneNumbers">Existing phone numbers collection to edit</param>
        public PhoneNumbersDialog(Models.PhoneNumbersCollectionModel existingPhoneNumbers)
        {
            InitializeComponent();

            // Attach UFU2 phone number formatting to the phone number input TextBox
            TextBoxExtensions.AttachPhoneNumberFormatting(PhoneNumberTextBox);

            // Set up the ViewModel with existing data
            DataContext = new PhoneNumbersDialogViewModel(existingPhoneNumbers);

            // Set up auto-scroll functionality
            SetupAutoScroll();
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the Loaded event to set focus on the phone number input.
        /// </summary>
        /// <param name="sender">The source of the event</param>
        /// <param name="e">Event arguments</param>
        private void PhoneNumbersDialog_Loaded(object sender, RoutedEventArgs e)
        {
            // Set focus to the phone number input for better user experience
            PhoneNumberTextBox.Focus();
        }

        /// <summary>
        /// Handles the LostFocus event for the PhoneNumberTextBox to format the phone number.
        /// Applies UFU2 phone number formatting (XXXX-XX-XX-XX for 10 digits, XXX-XX-XX-XX for 9 digits).
        /// </summary>
        private void PhoneNumberTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                try
                {
                    // Use the PhoneNumberConverter to format the phone number
                    var converter = new PhoneNumberConverter();
                    var formattedNumber = converter.Convert(textBox.Text, typeof(string), null, System.Globalization.CultureInfo.CurrentCulture);

                    if (formattedNumber != null)
                    {
                        textBox.Text = formattedNumber.ToString();
                    }
                }
                catch
                {
                    // If formatting fails, keep the original text
                    // This ensures the dialog doesn't crash on invalid input
                }
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the Save button click event.
        /// Closes the dialog with a positive result (true) to indicate save operation.
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            DialogHost.CloseDialogCommand.Execute(true, this);
        }

        /// <summary>
        /// Handles the Cancel button click event.
        /// Closes the dialog with a negative result (false) to indicate cancel operation.
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogHost.CloseDialogCommand.Execute(false, this);
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the phone numbers collection from the ViewModel.
        /// Used by the parent view to retrieve the updated phone numbers.
        /// </summary>
        /// <returns>The phone numbers collection model</returns>
        public Models.PhoneNumbersCollectionModel GetPhoneNumbers()
        {
            if (DataContext is PhoneNumbersDialogViewModel viewModel)
            {
                return viewModel.PhoneNumbers;
            }
            return new Models.PhoneNumbersCollectionModel();
        }

        /// <summary>
        /// Sets the primary phone number in the dialog.
        /// Used when opening the dialog with an existing phone number from the main view.
        /// </summary>
        /// <param name="phoneNumber">The phone number to set as primary</param>
        public void SetPrimaryPhoneNumber(string phoneNumber)
        {
            if (DataContext is PhoneNumbersDialogViewModel viewModel)
            {
                viewModel.SetPrimaryPhoneNumber(phoneNumber);
            }
        }

        /// <summary>
        /// Gets the primary phone number from the dialog.
        /// Used to update the main view's PhoneNumberTextBox.
        /// </summary>
        /// <returns>The primary phone number</returns>
        public string GetPrimaryPhoneNumber()
        {
            if (DataContext is PhoneNumbersDialogViewModel viewModel)
            {
                return viewModel.PhoneNumbers.PrimaryPhoneNumber;
            }
            return string.Empty;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Sets up auto-scroll functionality for the ListView.
        /// Subscribes to the PhoneNumbers collection changes to automatically scroll to bottom when new items are added.
        /// </summary>
        private void SetupAutoScroll()
        {
            if (DataContext is PhoneNumbersDialogViewModel viewModel)
            {
                // Subscribe to collection changes to implement auto-scroll
                viewModel.PhoneNumbers.PhoneNumbers.CollectionChanged += PhoneNumbers_CollectionChanged;
            }
        }

        /// <summary>
        /// Handles collection changes to implement auto-scroll functionality.
        /// Automatically scrolls to the bottom when new phone numbers are added.
        /// </summary>
        /// <param name="sender">The collection that changed</param>
        /// <param name="e">Collection change event arguments</param>
        private void PhoneNumbers_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // Only scroll to bottom when items are added
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                // Use Dispatcher to ensure the UI has updated before scrolling
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // Scroll to the last item (bottom of the list)
                        if (PhoneNumbersListView.Items.Count > 0)
                        {
                            var lastItem = PhoneNumbersListView.Items[PhoneNumbersListView.Items.Count - 1];
                            PhoneNumbersListView.ScrollIntoView(lastItem);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log any errors but don't crash the dialog
                        LoggingService.LogWarning($"Auto-scroll failed: {ex.Message}", "PhoneNumbersDialog");
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        /// <summary>
        /// Masks a phone number for privacy in logs.
        /// Shows first 3 and last 3 digits, masks the middle with asterisks.
        /// </summary>
        /// <param name="phoneNumber">The phone number to mask</param>
        /// <returns>Masked phone number string</returns>
        private string MaskPhoneNumber(string? phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
            {
                return "[EMPTY]";
            }

            if (phoneNumber.Length <= 6)
            {
                // For short numbers, mask all but first and last character
                return phoneNumber.Length <= 2 ? new string('*', phoneNumber.Length) :
                       $"{phoneNumber[0]}{new string('*', phoneNumber.Length - 2)}{phoneNumber[^1]}";
            }

            // For longer numbers, show first 3 and last 3 digits
            return $"{phoneNumber[..3]}{new string('*', phoneNumber.Length - 6)}{phoneNumber[^3..]}";
        }

        #endregion
    }
}
