using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UFU2.Common;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Memory pressure handler for UFU2 application.
    /// Monitors system memory usage and coordinates cache eviction during high memory pressure.
    /// Implements Day 3 Task 3.3 requirements for memory pressure handling.
    /// </summary>
    public class MemoryPressureHandler : IDisposable
    {
        #region Private Fields

        private readonly CacheCoordinatorService _cacheCoordinator;
        private readonly Timer _memoryMonitoringTimer;
        private readonly SemaphoreSlim _pressureHandlingLock;
        private readonly Process _currentProcess;
        private bool _disposed = false;

        // Configuration
        private readonly TimeSpan _monitoringInterval = TimeSpan.FromMinutes(1);
        private readonly long _highMemoryThresholdMB = 500; // 500MB threshold for UFU2 process
        private readonly long _criticalMemoryThresholdMB = 750; // 750MB critical threshold
        private readonly double _systemMemoryThresholdPercent = 0.85; // 85% of system memory
        private readonly int _consecutiveHighReadingsThreshold = 3; // Trigger action after 3 consecutive high readings

        // State tracking
        private int _consecutiveHighReadings = 0;
        private DateTime _lastPressureEventTime = DateTime.MinValue;
        private readonly TimeSpan _minimumTimeBetweenEvents = TimeSpan.FromMinutes(5);

        // Statistics
        private long _totalMemoryPressureEvents = 0;
        private long _totalCacheEvictionsTriggered = 0;
        private DateTime _lastMemoryCheck = DateTime.UtcNow;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the MemoryPressureHandler.
        /// </summary>
        /// <param name="cacheCoordinator">The cache coordinator service</param>
        public MemoryPressureHandler(CacheCoordinatorService cacheCoordinator)
        {
            _cacheCoordinator = cacheCoordinator ?? throw new ArgumentNullException(nameof(cacheCoordinator));
            _pressureHandlingLock = new SemaphoreSlim(1, 1);
            _currentProcess = Process.GetCurrentProcess();

            // Start memory monitoring timer
            _memoryMonitoringTimer = new Timer(MonitorMemoryUsage, null, _monitoringInterval, _monitoringInterval);

            LoggingService.LogInfo("MemoryPressureHandler initialized with memory monitoring", "MemoryPressureHandler");
        }

        #endregion

        #region Memory Monitoring

        /// <summary>
        /// Monitors memory usage and triggers pressure handling if needed (timer callback).
        /// </summary>
        /// <param name="state">Timer state (not used)</param>
        private void MonitorMemoryUsage(object? state)
        {
            try
            {
                _lastMemoryCheck = DateTime.UtcNow;

                var memoryInfo = GetMemoryInfo();
                var pressureLevel = DetermineMemoryPressureLevel(memoryInfo);

                LoggingService.LogDebug($"Memory check - Process: {memoryInfo.ProcessMemoryMB:F1}MB, System: {memoryInfo.SystemMemoryUsagePercent:P1}, Pressure: {pressureLevel}", "MemoryPressureHandler");

                if (pressureLevel != MemoryPressureLevel.Normal)
                {
                    _consecutiveHighReadings++;
                    
                    if (_consecutiveHighReadings >= _consecutiveHighReadingsThreshold)
                    {
                        var timeSinceLastEvent = DateTime.UtcNow - _lastPressureEventTime;
                        if (timeSinceLastEvent >= _minimumTimeBetweenEvents)
                        {
                            Task.Run(async () => await HandleMemoryPressureAsync(pressureLevel, memoryInfo));
                        }
                    }
                }
                else
                {
                    _consecutiveHighReadings = 0;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during memory monitoring: {ex.Message}", "MemoryPressureHandler");
            }
        }

        /// <summary>
        /// Gets current memory information.
        /// </summary>
        /// <returns>Memory information</returns>
        private MemoryInfo GetMemoryInfo()
        {
            try
            {
                // Refresh process information
                _currentProcess.Refresh();

                var processMemoryBytes = _currentProcess.WorkingSet64;
                var processMemoryMB = processMemoryBytes / 1024.0 / 1024.0;

                // Get system memory information
                var totalSystemMemoryMB = GetTotalSystemMemoryMB();
                var availableSystemMemoryMB = GetAvailableSystemMemoryMB();
                var usedSystemMemoryMB = totalSystemMemoryMB - availableSystemMemoryMB;
                var systemMemoryUsagePercent = totalSystemMemoryMB > 0 ? usedSystemMemoryMB / totalSystemMemoryMB : 0.0;

                return new MemoryInfo
                {
                    ProcessMemoryBytes = processMemoryBytes,
                    ProcessMemoryMB = processMemoryMB,
                    TotalSystemMemoryMB = totalSystemMemoryMB,
                    AvailableSystemMemoryMB = availableSystemMemoryMB,
                    UsedSystemMemoryMB = usedSystemMemoryMB,
                    SystemMemoryUsagePercent = systemMemoryUsagePercent
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting memory information: {ex.Message}", "MemoryPressureHandler");
                return new MemoryInfo(); // Return default values
            }
        }

        /// <summary>
        /// Determines the memory pressure level based on current memory usage.
        /// </summary>
        /// <param name="memoryInfo">Current memory information</param>
        /// <returns>Memory pressure level</returns>
        private MemoryPressureLevel DetermineMemoryPressureLevel(MemoryInfo memoryInfo)
        {
            // Check for critical pressure
            if (memoryInfo.ProcessMemoryMB >= _criticalMemoryThresholdMB ||
                memoryInfo.SystemMemoryUsagePercent >= 0.95)
            {
                return MemoryPressureLevel.Critical;
            }

            // Check for high pressure
            if (memoryInfo.ProcessMemoryMB >= _highMemoryThresholdMB ||
                memoryInfo.SystemMemoryUsagePercent >= _systemMemoryThresholdPercent)
            {
                return MemoryPressureLevel.High;
            }

            // Check for moderate pressure
            if (memoryInfo.ProcessMemoryMB >= _highMemoryThresholdMB * 0.75 ||
                memoryInfo.SystemMemoryUsagePercent >= _systemMemoryThresholdPercent * 0.9)
            {
                return MemoryPressureLevel.Moderate;
            }

            return MemoryPressureLevel.Normal;
        }

        #endregion

        #region Memory Pressure Handling

        /// <summary>
        /// Handles memory pressure by coordinating cache eviction.
        /// </summary>
        /// <param name="pressureLevel">Level of memory pressure</param>
        /// <param name="memoryInfo">Current memory information</param>
        private async Task HandleMemoryPressureAsync(MemoryPressureLevel pressureLevel, MemoryInfo memoryInfo)
        {
            await _pressureHandlingLock.WaitAsync();
            try
            {
                _lastPressureEventTime = DateTime.UtcNow;
                Interlocked.Increment(ref _totalMemoryPressureEvents);

                LoggingService.LogWarning($"Memory pressure detected - Level: {pressureLevel}, Process: {memoryInfo.ProcessMemoryMB:F1}MB, System: {memoryInfo.SystemMemoryUsagePercent:P1}", "MemoryPressureHandler");

                switch (pressureLevel)
                {
                    case MemoryPressureLevel.Moderate:
                        await HandleModeratePressureAsync();
                        break;

                    case MemoryPressureLevel.High:
                        await HandleHighPressureAsync();
                        break;

                    case MemoryPressureLevel.Critical:
                        await HandleCriticalPressureAsync();
                        break;
                }

                // Force garbage collection after cache eviction
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                LoggingService.LogInfo($"Memory pressure handling completed for level: {pressureLevel}", "MemoryPressureHandler");
            }
            finally
            {
                _pressureHandlingLock.Release();
            }
        }

        /// <summary>
        /// Handles moderate memory pressure.
        /// </summary>
        private async Task HandleModeratePressureAsync()
        {
            // Trigger selective cache cleanup for low-priority services
            var invalidationContext = new CacheInvalidationContext
            {
                DataType = "MemoryPressure",
                InvalidationType = CacheInvalidationType.Full,
                AdditionalContext = new Dictionary<string, object>
                {
                    ["PressureLevel"] = "Moderate",
                    ["Reason"] = "Moderate memory pressure detected"
                }
            };

            // Clear caches for less critical services only
            await TriggerSelectiveCacheEvictionAsync(0.25); // Evict 25% of cache items
            Interlocked.Increment(ref _totalCacheEvictionsTriggered);
        }

        /// <summary>
        /// Handles high memory pressure.
        /// </summary>
        private async Task HandleHighPressureAsync()
        {
            // Trigger more aggressive cache cleanup
            await TriggerSelectiveCacheEvictionAsync(0.5); // Evict 50% of cache items
            Interlocked.Increment(ref _totalCacheEvictionsTriggered);

            LoggingService.LogWarning("High memory pressure: Performed aggressive cache eviction", "MemoryPressureHandler");
        }

        /// <summary>
        /// Handles critical memory pressure.
        /// </summary>
        private async Task HandleCriticalPressureAsync()
        {
            // Clear all caches immediately
            await _cacheCoordinator.ClearAllCachesAsync();
            Interlocked.Increment(ref _totalCacheEvictionsTriggered);

            LoggingService.LogError("Critical memory pressure: Cleared all caches", "MemoryPressureHandler");
        }

        /// <summary>
        /// Triggers selective cache eviction based on eviction percentage.
        /// </summary>
        /// <param name="evictionPercentage">Percentage of cache items to evict (0.0 to 1.0)</param>
        private async Task TriggerSelectiveCacheEvictionAsync(double evictionPercentage)
        {
            try
            {
                var healthInfo = _cacheCoordinator.GetOverallCacheHealth();
                
                // Sort services by memory usage and hit ratio (evict from least efficient first)
                var servicesToEvict = healthInfo
                    .Where(kvp => kvp.Value.ItemCount > 0)
                    .OrderBy(kvp => kvp.Value.HitRatio) // Low hit ratio first
                    .ThenByDescending(kvp => kvp.Value.MemoryUsageBytes) // High memory usage first
                    .Take((int)(healthInfo.Count * evictionPercentage))
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var serviceName in servicesToEvict)
                {
                    var invalidationContext = new CacheInvalidationContext
                    {
                        DataType = "MemoryPressure",
                        InvalidationType = CacheInvalidationType.Full,
                        AdditionalContext = new Dictionary<string, object>
                        {
                            ["ServiceName"] = serviceName,
                            ["Reason"] = "Memory pressure eviction"
                        }
                    };

                    await _cacheCoordinator.CoordinateInvalidationAsync(invalidationContext);
                }

                LoggingService.LogInfo($"Selective cache eviction completed for {servicesToEvict.Count} services", "MemoryPressureHandler");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during selective cache eviction: {ex.Message}", "MemoryPressureHandler");
            }
        }

        #endregion

        #region System Memory Information

        /// <summary>
        /// Gets the total system memory in MB.
        /// </summary>
        /// <returns>Total system memory in MB</returns>
        private double GetTotalSystemMemoryMB()
        {
            try
            {
                // Use GC.GetTotalMemory as a fallback approach
                // This is not perfect but provides a reasonable estimate
                var gcMemory = GC.GetTotalMemory(false) / 1024.0 / 1024.0;

                // Estimate total system memory based on process working set
                // This is a rough approximation
                var processMemory = _currentProcess.WorkingSet64 / 1024.0 / 1024.0;

                // Assume the process is using a small fraction of total memory
                // and estimate total memory (this is very rough)
                var estimatedTotal = Math.Max(processMemory * 10, 4096); // At least 4GB

                return Math.Min(estimatedTotal, 32768); // Cap at 32GB for safety
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Could not get total system memory: {ex.Message}", "MemoryPressureHandler");
            }

            return 8192; // Default to 8GB if unable to determine
        }

        /// <summary>
        /// Gets the available system memory in MB.
        /// </summary>
        /// <returns>Available system memory in MB</returns>
        private double GetAvailableSystemMemoryMB()
        {
            try
            {
                // Since we can't easily get available memory without System.Management,
                // we'll estimate based on process memory usage
                var processMemory = _currentProcess.WorkingSet64 / 1024.0 / 1024.0;
                var totalMemory = GetTotalSystemMemoryMB();

                // Rough estimate: assume available memory is total minus a reasonable usage
                // This is not accurate but provides a working approximation
                var estimatedUsed = Math.Max(processMemory * 2, totalMemory * 0.3); // At least 30% used
                var estimatedAvailable = totalMemory - estimatedUsed;

                return Math.Max(estimatedAvailable, 512); // At least 512MB available
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Could not get available system memory: {ex.Message}", "MemoryPressureHandler");
            }

            return 2048; // Default to 2GB if unable to determine
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets memory pressure statistics for monitoring.
        /// </summary>
        /// <returns>Dictionary containing memory pressure statistics</returns>
        public Dictionary<string, object> GetStatistics()
        {
            var memoryInfo = GetMemoryInfo();
            var pressureLevel = DetermineMemoryPressureLevel(memoryInfo);

            return new Dictionary<string, object>
            {
                ["ProcessMemoryMB"] = memoryInfo.ProcessMemoryMB,
                ["SystemMemoryUsagePercent"] = memoryInfo.SystemMemoryUsagePercent,
                ["CurrentPressureLevel"] = pressureLevel.ToString(),
                ["TotalPressureEvents"] = _totalMemoryPressureEvents,
                ["TotalCacheEvictionsTriggered"] = _totalCacheEvictionsTriggered,
                ["ConsecutiveHighReadings"] = _consecutiveHighReadings,
                ["LastMemoryCheck"] = _lastMemoryCheck,
                ["LastPressureEventTime"] = _lastPressureEventTime,
                ["HighMemoryThresholdMB"] = _highMemoryThresholdMB,
                ["CriticalMemoryThresholdMB"] = _criticalMemoryThresholdMB,
                ["SystemMemoryThresholdPercent"] = _systemMemoryThresholdPercent
            };
        }

        /// <summary>
        /// Forces immediate memory pressure check and handling.
        /// </summary>
        public async Task ForceMemoryPressureCheckAsync()
        {
            try
            {
                LoggingService.LogInfo("Forcing immediate memory pressure check", "MemoryPressureHandler");

                var memoryInfo = GetMemoryInfo();
                var pressureLevel = DetermineMemoryPressureLevel(memoryInfo);

                if (pressureLevel != MemoryPressureLevel.Normal)
                {
                    await HandleMemoryPressureAsync(pressureLevel, memoryInfo);
                }
                else
                {
                    LoggingService.LogInfo("No memory pressure detected during forced check", "MemoryPressureHandler");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during forced memory pressure check: {ex.Message}", "MemoryPressureHandler");
            }
        }

        /// <summary>
        /// Gets current memory information for external monitoring.
        /// </summary>
        /// <returns>Current memory information</returns>
        public MemoryInfo GetCurrentMemoryInfo()
        {
            return GetMemoryInfo();
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the memory pressure handler and its resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;

                try
                {
                    _memoryMonitoringTimer?.Dispose();
                    _pressureHandlingLock?.Dispose();
                    _currentProcess?.Dispose();

                    LoggingService.LogInfo("MemoryPressureHandler disposed", "MemoryPressureHandler");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error disposing MemoryPressureHandler: {ex.Message}", "MemoryPressureHandler");
                }
            }
        }

        #endregion
    }

    #region Supporting Classes and Enums

    /// <summary>
    /// Memory information structure.
    /// </summary>
    public class MemoryInfo
    {
        public long ProcessMemoryBytes { get; set; }
        public double ProcessMemoryMB { get; set; }
        public double TotalSystemMemoryMB { get; set; }
        public double AvailableSystemMemoryMB { get; set; }
        public double UsedSystemMemoryMB { get; set; }
        public double SystemMemoryUsagePercent { get; set; }
    }

    /// <summary>
    /// Memory pressure levels.
    /// </summary>
    public enum MemoryPressureLevel
    {
        Normal,
        Moderate,
        High,
        Critical
    }

    #endregion
}
