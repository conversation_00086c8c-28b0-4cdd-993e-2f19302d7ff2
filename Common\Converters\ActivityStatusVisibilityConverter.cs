using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that controls visibility of activity status-dependent UI elements.
    /// Shows elements when activity status indicates modification, inactivity, or suspension.
    /// Hides elements when activity status indicates active state.
    /// 
    /// This converter is used to conditionally show additional information sections
    /// (like update date and notes) when an activity has a non-active status.
    /// 
    /// Visibility Logic:
    /// - VISIBLE: "معدل" (Modified/Edited), "غير نشط" (Inactive), "شطب" (Suspended/Cancelled)
    /// - COLLAPSED: "قيد" (Active), "نشط" (Active), null, empty, or unknown values
    /// </summary>
    public class ActivityStatusVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts activity status to visibility state.
        /// </summary>
        /// <param name="value">The activity status string (Arabic)</param>
        /// <param name="targetType">The target type (should be Visibility)</param>
        /// <param name="parameter">Optional parameter for inversion (not used currently)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Visibility.Visible for non-active statuses, Visibility.Collapsed for active or unknown statuses</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var activityStatus = value?.ToString()?.Trim() ?? string.Empty;
                
                // Return visibility based on activity status
                return ShouldShowForStatus(activityStatus) ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception)
            {
                // Default to collapsed if any error occurs
                return Visibility.Collapsed;
            }
        }

        /// <summary>
        /// Not implemented for this converter as it's one-way only.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ActivityStatusVisibilityConverter is a one-way converter.");
        }

        /// <summary>
        /// Determines whether UI elements should be visible for the given activity status.
        /// </summary>
        /// <param name="activityStatus">The activity status to evaluate</param>
        /// <returns>True if elements should be visible, false if they should be collapsed</returns>
        private static bool ShouldShowForStatus(string activityStatus)
        {
            // Show for non-active statuses that indicate modification, inactivity, or suspension
            return activityStatus switch
            {
                "معدل" => true,        // Modified/Edited - show additional info
                "غير نشط" => true,     // Inactive - show additional info
                "شطب" => true,         // Suspended/Cancelled - show additional info
                "قيد" => false,        // Active (Commercial) - hide additional info
                "نشط" => false,        // Active (Craft/Professional) - hide additional info
                _ => false             // Unknown, null, or empty - hide by default
            };
        }

        /// <summary>
        /// Gets a user-friendly description of the visibility logic for the given status.
        /// Used for debugging and documentation purposes.
        /// </summary>
        /// <param name="activityStatus">The activity status to describe</param>
        /// <returns>Arabic description of why the element is shown or hidden</returns>
        public static string GetVisibilityReason(string activityStatus)
        {
            return activityStatus switch
            {
                "معدل" => "مرئي - النشاط معدل ويحتاج معلومات إضافية",
                "غير نشط" => "مرئي - النشاط غير نشط ويحتاج معلومات إضافية", 
                "شطب" => "مرئي - النشاط مشطوب ويحتاج معلومات إضافية",
                "قيد" => "مخفي - النشاط قيد ولا يحتاج معلومات إضافية",
                "نشط" => "مخفي - النشاط نشط ولا يحتاج معلومات إضافية",
                _ => "مخفي - حالة النشاط غير معروفة"
            };
        }

        /// <summary>
        /// Validates that the given activity status is a known value.
        /// </summary>
        /// <param name="activityStatus">The activity status to validate</param>
        /// <returns>True if the status is a known value, false otherwise</returns>
        public static bool IsKnownActivityStatus(string activityStatus)
        {
            return activityStatus switch
            {
                "معدل" or "غير نشط" or "شطب" or "قيد" or "نشط" => true,
                _ => false
            };
        }

        /// <summary>
        /// Gets all activity statuses that will make elements visible.
        /// Used for testing and validation purposes.
        /// </summary>
        /// <returns>Array of status values that result in visible elements</returns>
        public static string[] GetVisibleStatuses()
        {
            return new[] { "معدل", "غير نشط", "شطب" };
        }

        /// <summary>
        /// Gets all activity statuses that will make elements collapsed.
        /// Used for testing and validation purposes.
        /// </summary>
        /// <returns>Array of status values that result in collapsed elements</returns>
        public static string[] GetCollapsedStatuses()
        {
            return new[] { "قيد", "نشط" };
        }
    }
}
