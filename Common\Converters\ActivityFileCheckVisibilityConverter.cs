using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that controls the visibility of file check chips based on the selected activity type.
    /// Different activity types require different file check documents for client registration.
    /// Returns Visibility.Visible for required file types, Visibility.Collapsed for others.
    /// 
    /// Supported file check types and their activity requirements:
    /// - CAS (Social Security): Required for all activity types
    /// - NIF (Tax ID): Required for all activity types
    /// - NIS (Statistical ID): Required for all activity types
    /// - RC (Commercial Register): Required only for MainCommercial and SecondaryCommercial
    /// - ART (Craft Card): Required only for Craft activities
    /// - AGR (Professional Certificate): Required only for Professional activities
    /// - DEx (Existence Declaration): Required for all activity types
    /// </summary>
    public class ActivityFileCheckVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts the selected activity type to a Visibility value for file check chips.
        /// </summary>
        /// <param name="value">The selected activity type (MainCommercial, SecondaryCommercial, Craft, Professional)</param>
        /// <param name="targetType">The target type (should be Visibility)</param>
        /// <param name="parameter">The file check type (CAS, NIF, NIS, RC, ART, AGR, DEx, or "Always" for always visible)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Visibility.Visible if file check is required for the activity type, Visibility.Collapsed otherwise</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var activityType = value?.ToString() ?? "MainCommercial";
                var fileCheckType = parameter?.ToString() ?? string.Empty;

                // Handle special case for always visible items (Notes, G12, BIS buttons)
                if (fileCheckType.Equals("Always", StringComparison.OrdinalIgnoreCase))
                {
                    return Visibility.Visible;
                }

                var shouldShow = ShouldShowFileCheck(activityType, fileCheckType);
                return shouldShow ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception)
            {
                // Default to visible if any error occurs to ensure functionality
                return Visibility.Visible;
            }
        }

        /// <summary>
        /// Not implemented as this is a one-way converter.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ActivityFileCheckVisibilityConverter is a one-way converter.");
        }

        /// <summary>
        /// Determines whether a specific file check should be visible for the given activity type.
        /// Uses FileCheckTypeRules for centralized business rule management.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <param name="fileCheckType">The file check type to evaluate</param>
        /// <returns>True if the file check should be visible, false otherwise</returns>
        private static bool ShouldShowFileCheck(string activityType, string fileCheckType)
        {
            try
            {
                // Use FileCheckTypeRules for centralized business rule validation
                return UFU2.Models.FileCheckTypeRules.IsValidFileCheckType(activityType, fileCheckType);
            }
            catch (Exception)
            {
                // Fallback to legacy logic if FileCheckTypeRules fails
                return fileCheckType.ToUpperInvariant() switch
                {
                    "CAS" => true,
                    "NIF" => true,
                    "NIS" => true,
                    "RC" => activityType is "MainCommercial" or "SecondaryCommercial",
                    "ART" => activityType == "Craft",
                    "AGR" => activityType == "Professional",
                    "DEX" => true,
                    _ => false
                };
            }
        }

        /// <summary>
        /// Gets the list of required file checks for a specific activity type.
        /// Uses FileCheckTypeRules for centralized business rule management.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Array of required file check types for the activity</returns>
        public static string[] GetRequiredFileChecks(string activityType)
        {
            try
            {
                // Use FileCheckTypeRules for centralized business rule management
                return UFU2.Models.FileCheckTypeRules.GetRequiredFileCheckTypes(activityType).ToArray();
            }
            catch (Exception)
            {
                // Fallback to legacy logic if FileCheckTypeRules fails
                return activityType switch
                {
                    "MainCommercial" => new[] { "CAS", "NIF", "NIS", "RC", "DEX" },
                    "SecondaryCommercial" => new[] { "CAS", "NIF", "NIS", "RC", "DEX" },
                    "Craft" => new[] { "CAS", "NIF", "NIS", "ART", "DEX" },
                    "Professional" => new[] { "CAS", "NIF", "NIS", "AGR", "DEX" },
                    _ => new[] { "CAS", "NIF", "NIS", "DEX" }
                };
            }
        }

        /// <summary>
        /// Gets user-friendly Arabic description for file check requirements by activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Arabic description of required file checks</returns>
        public static string GetFileCheckDescription(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "الملفات المطلوبة للنشاط التجاري الرئيسي: الضمان الاجتماعي، رقم التعريف الجبائي، رقم التعريف الاحصائي، السجل التجاري، تصريح بالوجود",
                "SecondaryCommercial" => "الملفات المطلوبة للنشاط التجاري الثانوي: الضمان الاجتماعي، رقم التعريف الجبائي، رقم التعريف الاحصائي، السجل التجاري، تصريح بالوجود",
                "Craft" => "الملفات المطلوبة للنشاط الحرفي: الضمان الاجتماعي، رقم التعريف الجبائي، رقم التعريف الاحصائي، بطاقة الحرفي، تصريح بالوجود",
                "Professional" => "الملفات المطلوبة للنشاط المهني: الضمان الاجتماعي، رقم التعريف الجبائي، رقم التعريف الاحصائي، شهادة مهني، تصريح بالوجود",
                _ => "الملفات المطلوبة للنشاط: الضمان الاجتماعي، رقم التعريف الجبائي، رقم التعريف الاحصائي، تصريح بالوجود"
            };
        }

        /// <summary>
        /// Gets the Arabic label for a specific file check type.
        /// </summary>
        /// <param name="fileCheckType">The file check type</param>
        /// <returns>Arabic label for the file check type</returns>
        public static string GetFileCheckLabel(string fileCheckType)
        {
            return fileCheckType.ToUpperInvariant() switch
            {
                "CAS" => "الضمان الاجتماعي (CAS)",
                "NIF" => "رقم التعريف الجبائي (NIF)",
                "NIS" => "رقم التعريف الاحصائي (NIS)",
                "RC" => "السجل التجاري (RC)",
                "ART" => "بطاقة الحرفي (ART)",
                "AGR" => "شهادة مهني (AGR)",
                "DEX" => "تصريح بالوجود (DEx)",
                _ => fileCheckType
            };
        }

        /// <summary>
        /// Gets the Arabic tooltip text for a specific file check type.
        /// </summary>
        /// <param name="fileCheckType">The file check type</param>
        /// <returns>Arabic tooltip text for the file check type</returns>
        public static string GetFileCheckTooltip(string fileCheckType)
        {
            return fileCheckType.ToUpperInvariant() switch
            {
                "CAS" => "فحص وثيقة الضمان الاجتماعي",
                "NIF" => "فحص رقم التعريف الجبائي",
                "NIS" => "فحص رقم التعريف الاحصائي",
                "RC" => "فحص السجل التجاري",
                "ART" => "فحص بطاقة الحرفي",
                "AGR" => "فحص شهادة مهني",
                "DEX" => "فحص تصريح بالوجود",
                _ => $"فحص {fileCheckType}"
            };
        }

        /// <summary>
        /// Validates if all required file checks are completed for a given activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <param name="completedChecks">Array of completed file check types</param>
        /// <returns>True if all required checks are completed, false otherwise</returns>
        public static bool AreAllRequiredChecksCompleted(string activityType, string[] completedChecks)
        {
            var requiredChecks = GetRequiredFileChecks(activityType);
            
            foreach (var required in requiredChecks)
            {
                if (!Array.Exists(completedChecks, check => 
                    check.Equals(required, StringComparison.OrdinalIgnoreCase)))
                {
                    return false;
                }
            }
            
            return true;
        }
    }
}
