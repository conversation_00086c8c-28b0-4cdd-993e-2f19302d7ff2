﻿using System.Configuration;
using System.Data;
using System.Windows;
using System.Windows.Threading;
using System.Threading.Tasks;
using System.IO;
using System.Diagnostics;
using UFU2.Services;
using UFU2.Common;

namespace UFU2
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        #region Performance Tracking Fields

        /// <summary>
        /// Tracks application startup time for performance analysis
        /// </summary>
        private static readonly Stopwatch _startupStopwatch = Stopwatch.StartNew();

        /// <summary>
        /// Tracks total logging overhead during application lifecycle
        /// </summary>
        private static long _totalLoggingOverheadMs = 0;

        /// <summary>
        /// Tracks number of debug log calls made during application lifecycle
        /// </summary>
        private static int _debugLogCallCount = 0;

        #endregion

        /// <summary>
        /// Application constructor - sets up global exception handlers
        /// </summary>
        public App()
        {
            // Set up global exception handlers
            SetupGlobalExceptionHandlers();
        }
        /// <summary>
        /// Application startup event handler
        /// Initializes services before showing the main window
        /// </summary>
        /// <param name="e">Startup event arguments</param>
        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // Initialize session-based file logging first
                var loggingInitialized = LoggingService.InitializeSessionLogging();
                if (!loggingInitialized)
                {
                    // Continue without file logging if initialization fails
                    LoggingService.LogWarning("Session file logging initialization failed, continuing with console logging only", "App");
                }

                // Configure log level to reduce excessive debug output in production
                #if DEBUG
                    LoggingService.SetMinimumLogLevel(LoggingService.LogLevel.Debug);
                #else
                    LoggingService.SetMinimumLogLevel(LoggingService.LogLevel.Info);
                #endif

                LoggingService.LogInfo("UFU2 Application starting up", "App");

                // Initialize ServiceLocator
                ServiceLocator.Initialize();

                // Initialize ThemeManager with Dark theme as default
                var themeInitialized = await ThemeManager.InitializeAsync(ApplicationTheme.Dark);

                if (!themeInitialized)
                {
                    LoggingService.LogWarning("ThemeManager initialization failed, continuing with default theme", "App");
                }
                else
                {
                    LoggingService.LogDebug("ThemeManager initialized successfully", "App");
                }

                // Initialize Database services through ServiceLocator
                try
                {
                    await ServiceLocator.InitializeDatabaseServicesAsync();
                }
                catch (Exception dbEx)
                {
                    LoggingService.LogError($"Database service initialization failed: {dbEx.Message}", "App");
                    
                    // Show Arabic error message to user using ErrorManager
                    ErrorManager.HandleErrorToast(dbEx,
                        "فشل في تهيئة قاعدة البيانات. سيستمر التطبيق ولكن قد لا تعمل ميزات قاعدة البيانات بشكل صحيح.",
                        "خطأ في قاعدة البيانات",
                        LogLevel.Warning,
                        "App");
                    
                    // Continue application startup even if database initialization fails
                    LoggingService.LogWarning("Application will continue with limited database functionality", "App");
                }

                // Call base startup
                base.OnStartup(e);

                _startupStopwatch.Stop();
                LoggingService.LogDebug($"UFU2 Application startup completed in {_startupStopwatch.ElapsedMilliseconds}ms", "App");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during application startup: {ex.Message}", "App");

                // Show Arabic error message to user using ErrorManager
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء بدء تشغيل التطبيق. سيستمر التطبيق ولكن قد لا تعمل بعض الميزات بشكل صحيح.",
                    "خطأ في بدء التشغيل",
                    LogLevel.Warning,
                    "App");

                // Continue with base startup even if there was an error
                base.OnStartup(e);
            }
        }

        /// <summary>
        /// Application exit event handler
        /// Performs cleanup operations including session logging finalization
        /// </summary>
        /// <param name="e">Exit event arguments</param>
        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("UFU2 Application shutting down", "App");

                // Report performance metrics
                _startupStopwatch.Stop();
                LoggingService.LogDebug($"Application runtime: {_startupStopwatch.Elapsed.TotalSeconds:F1}s", "App");
                LoggingService.LogDebug($"Performance metrics - Debug log calls: {_debugLogCallCount}, Total logging overhead: {_totalLoggingOverheadMs}ms", "App");

                // Dispose all registered services
                ServiceLocator.DisposeServices();

                // Perform any cleanup operations here
                // ThemeManager is static so no explicit cleanup needed

                LoggingService.LogInfo("UFU2 Application shutdown completed", "App");

                // Finalize session logging (writes session footer and cleans up)
                LoggingService.FinalizeSessionLogging();

                base.OnExit(e);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during application shutdown: {ex.Message}", "App");

                // Ensure session logging is finalized even if there was an error
                try
                {
                    LoggingService.FinalizeSessionLogging();
                }
                catch
                {
                    // Ignore errors during finalization to prevent cascading failures
                }

                base.OnExit(e);
            }
        }



        #region Global Exception Handling

        /// <summary>
        /// Sets up global exception handlers to catch unhandled exceptions
        /// </summary>
        private void SetupGlobalExceptionHandlers()
        {
            // Handle unhandled exceptions in the main UI thread
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;

            // Handle unhandled exceptions in background threads
            AppDomain.CurrentDomain.UnhandledException += App_UnhandledException;

            // Handle unhandled exceptions in Task operations
            TaskScheduler.UnobservedTaskException += App_UnobservedTaskException;
        }

        /// <summary>
        /// Handles unhandled exceptions in the main UI thread
        /// </summary>
        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                LoggingService.LogError($"Unhandled UI thread exception: {e.Exception.Message}", "App");
                LoggingService.LogError($"Stack trace: {e.Exception.StackTrace}", "App");

                // Show Arabic error message to user using ErrorManager
                ErrorManager.HandleErrorToast(e.Exception,
                    "حدث خطأ غير متوقع. سيحاول التطبيق المتابعة. يرجى حفظ عملك وإعادة التشغيل إذا استمرت المشاكل.",
                    "خطأ في التطبيق",
                    LogLevel.Error,
                    "App");

                // Mark as handled to prevent application crash
                e.Handled = true;
            }
            catch (Exception ex)
            {
                // Last resort - log to console if logging service fails
                System.Console.WriteLine($"Critical error in exception handler: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles unhandled exceptions in background threads
        /// </summary>
        private void App_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                LoggingService.LogError($"Unhandled background thread exception: {exception?.Message ?? "Unknown error"}", "App");
                LoggingService.LogError($"Stack trace: {exception?.StackTrace ?? "No stack trace available"}", "App");
                LoggingService.LogError($"Is terminating: {e.IsTerminating}", "App");

                if (e.IsTerminating)
                {
                    // Application is terminating - try to save critical data
                    LoggingService.LogError("Application is terminating due to unhandled exception", "App");

                    // Finalize logging before termination
                    LoggingService.FinalizeSessionLogging();
                }
            }
            catch (Exception ex)
            {
                // Last resort - log to console if logging service fails
                System.Console.WriteLine($"Critical error in background exception handler: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles unhandled exceptions in Task operations
        /// </summary>
        private void App_UnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                LoggingService.LogError($"Unhandled Task exception: {e.Exception.Message}", "App");
                LoggingService.LogError($"Stack trace: {e.Exception.StackTrace}", "App");

                // Mark as observed to prevent application crash
                e.SetObserved();
            }
            catch (Exception ex)
            {
                // Last resort - log to console if logging service fails
                System.Console.WriteLine($"Critical error in task exception handler: {ex.Message}");
            }
        }

        #endregion


    }
}
