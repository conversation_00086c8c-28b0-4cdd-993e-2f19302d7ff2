using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converts a boolean value to Visibility with inverse logic.
    /// Returns Collapsed if the boolean is true, otherwise returns Visible.
    /// This converter is used to hide UI elements when a condition is true (opposite of BooleanToVisibilityConverter).
    /// </summary>
    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts a boolean value to Visibility with inverse logic
        /// </summary>
        /// <param name="value">The boolean value to convert</param>
        /// <param name="targetType">The target type (should be Visibility)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Collapsed if boolean is true, Visible if false or null</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }

            // If value is null or not a boolean, treat as false (show element)
            return Visibility.Visible;
        }

        /// <summary>
        /// Converts back from Visibility to boolean with inverse logic (not implemented)
        /// </summary>
        /// <param name="value">The Visibility value</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">Optional parameter</param>
        /// <param name="culture">The culture info</param>
        /// <returns>Not implemented - throws NotImplementedException</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("InverseBooleanToVisibilityConverter does not support ConvertBack");
        }
    }
}
