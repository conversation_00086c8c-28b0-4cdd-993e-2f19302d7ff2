using System.ComponentModel;
using System.Windows.Input;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the AddNotesDialog providing MVVM data binding and command handling.
    /// Manages note content editing, flag selection, and save/cancel operations.
    /// Supports both add new note and edit existing note modes.
    /// Implements IDataErrorInfo for real-time validation feedback with Arabic error messages.
    ///
    /// REFACTORING STATUS: ✅ COMPLETED - ValidationService integration (Task 3.2)
    /// BACKUP CREATED: AddNotesDialogViewModel.cs.backup2 - Implementation before ValidationService integration
    /// PREVIOUS BACKUP: AddNotesDialogViewModel.cs.backup - Original implementation before IDisposable enhancement
    /// </summary>
    public class AddNotesDialogViewModel : BaseViewModel, IDataErrorInfo
    {
        #region Private Fields

        private string _noteContent = string.Empty;
        private int _selectedPriority = 0; // 0 = Normal (Green)
        private bool _isEditMode = false;
        private string _noteId = string.Empty;
        private Dictionary<int, string> _priorityLevels;
        private readonly ValidationService _validationService;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the note content being edited.
        /// </summary>
        public string NoteContent
        {
            get => _noteContent;
            set
            {
                if (SetProperty(ref _noteContent, value))
                {
                    SaveCommand.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected priority level for the note.
        /// 0 = Normal (Green), 1 = Medium (Orange), 2 = High (Red).
        /// </summary>
        public int SelectedPriority
        {
            get => _selectedPriority;
            set => SetProperty(ref _selectedPriority, value);
        }

        /// <summary>
        /// Gets whether the dialog is in edit mode (true) or add mode (false).
        /// </summary>
        public bool IsEditMode
        {
            get => _isEditMode;
            private set => SetProperty(ref _isEditMode, value);
        }

        /// <summary>
        /// Gets the dialog header text based on the current mode.
        /// </summary>
        public string DialogHeader => IsEditMode ? "تعديل الملاحظة" : "إضافة ملاحظة جديدة";

        /// <summary>
        /// Gets the available priority levels with their Arabic display names.
        /// </summary>
        public Dictionary<int, string> PriorityLevels
        {
            get => _priorityLevels;
            private set => SetProperty(ref _priorityLevels, value);
        }

        /// <summary>
        /// Gets whether the note content is valid for saving.
        /// </summary>
        public bool IsContentValid => string.IsNullOrEmpty(_validationService?.ValidateNoteContent(NoteContent));

        #endregion

        #region Commands

        /// <summary>
        /// Command to save the note.
        /// </summary>
        public RelayCommand SaveCommand { get; private set; }

        /// <summary>
        /// Command to cancel the dialog.
        /// </summary>
        public RelayCommand CancelCommand { get; private set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the AddNotesDialogViewModel class for adding a new note.
        /// </summary>
        public AddNotesDialogViewModel()
        {
            // Initialize ValidationService through ServiceLocator
            _validationService = ServiceLocator.GetService<ValidationService>();

            InitializeViewModel();
            IsEditMode = false;
        }

        /// <summary>
        /// Initializes a new instance of the AddNotesDialogViewModel class for editing an existing note.
        /// </summary>
        /// <param name="existingNote">The note to edit</param>
        public AddNotesDialogViewModel(NoteModel existingNote)
        {
            if (existingNote == null)
                throw new ArgumentNullException(nameof(existingNote));

            // Initialize ValidationService through ServiceLocator
            _validationService = ServiceLocator.GetService<ValidationService>();

            InitializeViewModel();
            LoadExistingNote(existingNote);
            IsEditMode = true;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes the ViewModel with default values and commands.
        /// </summary>
        private void InitializeViewModel()
        {
            // Initialize priority levels dictionary with Arabic names
            PriorityLevels = NoteModel.GetAllPriorityLevels();

            // Initialize commands
            InitializeCommands();

            // Initialize BaseViewModel
            OnInitialize();

            LoggingService.LogInfo("AddNotesDialogViewModel initialized with ValidationService integration", "AddNotesDialogViewModel");
        }

        /// <summary>
        /// Initializes the commands for the ViewModel.
        /// </summary>
        private void InitializeCommands()
        {
            SaveCommand = new RelayCommand(
                execute: ExecuteSave,
                canExecute: CanExecuteSave,
                commandName: "SaveNote"
            );

            CancelCommand = new RelayCommand(
                execute: ExecuteCancel,
                canExecute: _ => true,
                commandName: "CancelNote"
            );
        }

        /// <summary>
        /// Loads an existing note for editing.
        /// </summary>
        /// <param name="note">The note to load</param>
        private void LoadExistingNote(NoteModel note)
        {
            _noteId = note.Id;
            NoteContent = note.Content;
            SelectedPriority = note.Priority;
        }

        #endregion

        #region Command Implementations

        /// <summary>
        /// Determines whether the save command can be executed.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        /// <returns>True if the note content is valid, false otherwise</returns>
        private bool CanExecuteSave(object? parameter)
        {
            return IsContentValid;
        }

        /// <summary>
        /// Executes the save command.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        private void ExecuteSave(object? parameter)
        {
            try
            {
                if (!IsContentValid)
                {
                    LoggingService.LogWarning("Attempted to save note with invalid content", "AddNotesDialogViewModel");
                    return;
                }

                LoggingService.LogInfo($"Saving note - Mode: {(IsEditMode ? "Edit" : "Add")}, Content length: {NoteContent?.Length ?? 0}", "AddNotesDialogViewModel");

                // The actual save operation will be handled by the dialog code-behind
                // This ViewModel just validates and prepares the data
                OnSaveRequested();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error saving note: {ex.Message}", "AddNotesDialogViewModel");
            }
        }

        /// <summary>
        /// Executes the cancel command.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        private void ExecuteCancel(object? parameter)
        {
            try
            {
                LoggingService.LogInfo("Note editing cancelled", "AddNotesDialogViewModel");
                OnCancelRequested();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error cancelling note: {ex.Message}", "AddNotesDialogViewModel");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the note data for saving.
        /// </summary>
        /// <returns>A NoteModel with the current data</returns>
        public NoteModel GetNoteData()
        {
            if (IsEditMode)
            {
                return new NoteModel
                {
                    Id = _noteId,
                    Content = NoteContent?.Trim() ?? string.Empty,
                    Priority = SelectedPriority,
                    ModifiedDate = DateTime.Now
                };
            }
            else
            {
                return new NoteModel(NoteContent?.Trim() ?? string.Empty, SelectedPriority);
            }
        }

        /// <summary>
        /// Resets the dialog to default values for adding a new note.
        /// </summary>
        public void ResetForNewNote()
        {
            _noteId = string.Empty;
            NoteContent = string.Empty;
            SelectedPriority = 0; // Normal priority (Green)
            IsEditMode = false;
            OnPropertyChanged(nameof(DialogHeader));
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the user requests to save the note.
        /// </summary>
        public event EventHandler? SaveRequested;

        /// <summary>
        /// Event raised when the user requests to cancel the dialog.
        /// </summary>
        public event EventHandler? CancelRequested;

        /// <summary>
        /// Raises the SaveRequested event.
        /// </summary>
        protected virtual void OnSaveRequested()
        {
            SaveRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Raises the CancelRequested event.
        /// </summary>
        protected virtual void OnCancelRequested()
        {
            CancelRequested?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region IDataErrorInfo Implementation

        /// <summary>
        /// Gets an error message indicating what is wrong with this object.
        /// </summary>
        public string Error => string.Empty;

        /// <summary>
        /// Gets the error message for the property with the given name.
        /// </summary>
        /// <param name="columnName">The name of the property whose error message to get</param>
        /// <returns>The error message for the property</returns>
        public string this[string columnName]
        {
            get
            {
                try
                {
                    return columnName switch
                    {
                        nameof(NoteContent) => _validationService?.ValidateNoteContent(NoteContent) ?? string.Empty,
                        _ => string.Empty
                    };
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error validating property {columnName}: {ex.Message}", "AddNotesDialogViewModel");
                    return string.Empty;
                }
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes resources and cleans up event subscriptions, collections, and references.
        /// Implements enhanced IDisposable pattern with BaseViewModel integration.
        /// Prevents memory leaks from SaveRequested and CancelRequested event handlers and PriorityLevels dictionary.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    LoggingService.LogDebug("Starting disposal of AddNotesDialogViewModel resources", GetType().Name);

                    // Clear dialog event handlers to prevent memory leaks
                    try
                    {
                        SaveRequested = null;
                        CancelRequested = null;
                        LoggingService.LogDebug("Dialog event handlers cleared successfully", GetType().Name);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error clearing dialog event handlers: {ex.Message}", GetType().Name);
                    }

                    // Clear PriorityLevels dictionary to aid garbage collection
                    if (_priorityLevels != null)
                    {
                        try
                        {
                            _priorityLevels.Clear();
                            LoggingService.LogDebug("PriorityLevels dictionary cleared successfully", GetType().Name);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error clearing PriorityLevels dictionary: {ex.Message}", GetType().Name);
                        }
                    }

                    // Clear property references to aid garbage collection
                    _noteContent = null;
                    _noteId = null;
                    _priorityLevels = null;

                    LoggingService.LogDebug("AddNotesDialogViewModel disposal completed successfully", GetType().Name);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error during AddNotesDialogViewModel disposal: {ex.Message}", GetType().Name);
            }
            finally
            {
                // Always call base disposal to maintain inheritance chain
                base.Dispose(disposing);
            }
        }

        #endregion
    }
}
