<UserControl
    x:Class="UFU2.Views.UserControls.ToastNotification"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Width="380"
    d:DesignHeight="100"
    d:DesignWidth="380"
    mc:Ignorable="d">

    <UserControl.Resources>
        <!--  Entrance Animation  -->
        <Storyboard x:Key="EntranceStoryboard">
            <DoubleAnimation
                Storyboard.TargetProperty="Opacity"
                From="0"
                To="1"
                Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation
                Storyboard.TargetProperty="RenderTransform.(TranslateTransform.Y)"
                From="20"
                To="0"
                Duration="0:0:0.4">
                <DoubleAnimation.EasingFunction>
                    <BackEase Amplitude="0.3" EasingMode="EaseOut" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!--  Exit Animation  -->
        <Storyboard x:Key="ExitStoryboard">
            <DoubleAnimation
                Storyboard.TargetProperty="Opacity"
                From="1"
                To="0"
                Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation
                Storyboard.TargetProperty="RenderTransform.(TranslateTransform.Y)"
                From="0"
                To="20"
                Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>

    <UserControl.RenderTransform>
        <TranslateTransform />
    </UserControl.RenderTransform>

    <Grid>
        <!--  Left colored border  -->
        <Border
            x:Name="ColorBorder"
            Width="6"
            HorizontalAlignment="Left"
            Background="{DynamicResource SuccessBrush}" />

        <!--  Main content  -->
        <Border
            x:Name="RootBorder"
            Margin="6,0,0,0"
            Padding="16"
            Background="{DynamicResource SurfaceBrush}"
            BorderBrush="{DynamicResource OutlineBrush}"
            BorderThickness="0,1,1,1"
            SnapsToDevicePixels="True">
            <Border.Effect>
                <DropShadowEffect
                    BlurRadius="8"
                    Opacity="0.3"
                    ShadowDepth="2"
                    Color="{DynamicResource ShadowColor}" />
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Header area with icon, title and close button  -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!--  Icon  -->
                    <materialDesign:PackIcon
                        x:Name="ToastIcon"
                        Grid.Column="0"
                        Width="24"
                        Height="24"
                        Margin="0,0,12,0"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource SuccessBrush}"
                        Kind="CheckCircle" />

                    <!--  Title  -->
                    <TextBlock
                        x:Name="TitleText"
                        Grid.Column="1"
                        FlowDirection="RightToLeft"
                        Style="{StaticResource TostSubTitleTextStyle}" />

                    <!--  Close button  -->
                    <Button
                        x:Name="CloseButton"
                        Grid.Column="2"
                        Width="24"
                        Height="24"
                        Margin="8,0,0,0"
                        Padding="0"
                        Style="{StaticResource IconButtonStyle}"
                        ToolTip="إغلاق">
                        <materialDesign:PackIcon
                            x:Name="CloseIcon"
                            Width="16"
                            Height="16"
                            Kind="Close" />
                    </Button>
                </Grid>

                <!--  Message content  -->
                <TextBlock
                    x:Name="MessageText"
                    Grid.Row="1"
                    FlowDirection="RightToLeft"
                    Style="{StaticResource ToastTextStyle}" />

                <!--  Progress bar  -->
                <Grid Grid.Row="2" Margin="0,12,0,0">
                    <ProgressBar
                        x:Name="ToastProgressBar"
                        Height="4"
                        VerticalAlignment="Center"
                        Background="{DynamicResource SurfaceDimBrush}"
                        BorderBrush="{DynamicResource SurfaceDimBrush}"
                        Foreground="{DynamicResource SurfaceDimBrush}"
                        Maximum="100"
                        Minimum="0"
                        Value="0" />
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
