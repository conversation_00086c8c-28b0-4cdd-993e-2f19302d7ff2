using System;
using System.Windows;
using UFU2.Services;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Handles edge cases for the WYSIWYG backend simulation system.
    /// Provides specialized handling for small images, extreme transformations,
    /// and boundary conditions that require special consideration.
    /// Follows UFU2 patterns with comprehensive error handling and logging.
    /// </summary>
    public static class EdgeCaseHandler
    {
        #region Constants

        /// <summary>
        /// Minimum backend container size for small image upscaling
        /// </summary>
        private const double MinimumBackendSize = 1000.0;

        /// <summary>
        /// Maximum allowed zoom scale to prevent memory issues
        /// </summary>
        private const double MaximumZoomScale = 10.0;

        /// <summary>
        /// Minimum image dimension to be considered valid
        /// </summary>
        private const double MinimumImageDimension = 10.0;

        #endregion

        #region Public Methods

        /// <summary>
        /// Handles small images that are smaller than the preview container.
        /// Implements appropriate upscaling strategies to maintain quality and precision.
        /// </summary>
        /// <param name="originalImageSize">Size of the original image</param>
        /// <returns>Backend container result with upscaling information</returns>
        public static BackendContainerResult HandleSmallImage(Size originalImageSize)
        {
            try
            {
                LoggingService.LogDebug($"Handling small image: {originalImageSize.Width}x{originalImageSize.Height}", 
                    "EdgeCaseHandler");

                // Validate input
                if (!IsValidImageSize(originalImageSize))
                {
                    LoggingService.LogWarning($"Invalid image size for small image handling: {originalImageSize}", 
                        "EdgeCaseHandler");
                    return CreateFallbackResult();
                }

                // Check if image is actually small
                if (!IsSmallImage(originalImageSize))
                {
                    LoggingService.LogDebug("Image is not small, using standard calculation", "EdgeCaseHandler");
                    return BackendContainerCalculator.CalculateOptimalBackendContainer(originalImageSize);
                }

                // Calculate upscale factor to reach minimum backend size
                double maxDimension = Math.Max(originalImageSize.Width, originalImageSize.Height);
                double upscaleFactor = Math.Max(
                    MinimumBackendSize / maxDimension,
                    2.0 // Minimum 2x upscale for quality
                );

                // Calculate backend scale factor based on upscaled image
                double upscaledWidth = originalImageSize.Width * upscaleFactor;
                double upscaledHeight = originalImageSize.Height * upscaleFactor;

                double backendScaleFactor = Math.Max(
                    upscaledWidth / 500.0,  // Preview width
                    upscaledHeight / 320.0  // Preview height
                );

                backendScaleFactor = Math.Ceiling(backendScaleFactor);

                var backendSize = new Size(
                    500.0 * backendScaleFactor,
                    320.0 * backendScaleFactor
                );

                var result = new BackendContainerResult
                {
                    BackendSize = backendSize,
                    ScaleFactor = backendScaleFactor,
                    RequiresUpscaling = true,
                    UpscaleFactor = upscaleFactor,
                    IsValid = true
                };

                LoggingService.LogDebug($"Small image handled - Backend: {backendSize.Width}x{backendSize.Height}, " +
                    $"Scale: {backendScaleFactor:F2}, Upscale: {upscaleFactor:F2}", "EdgeCaseHandler");

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling small image: {ex.Message}", "EdgeCaseHandler");
                return CreateFallbackResult();
            }
        }

        /// <summary>
        /// Validates and constrains zoom scale values to prevent extreme transformations.
        /// </summary>
        /// <param name="zoomScale">Requested zoom scale</param>
        /// <returns>Constrained zoom scale within safe limits</returns>
        public static double ConstrainZoomScale(double zoomScale)
        {
            try
            {
                if (double.IsNaN(zoomScale) || double.IsInfinity(zoomScale) || zoomScale <= 0)
                {
                    LoggingService.LogWarning($"Invalid zoom scale: {zoomScale}, using default", "EdgeCaseHandler");
                    return 1.0;
                }

                double constrainedScale = Math.Max(0.1, Math.Min(MaximumZoomScale, zoomScale));

                if (Math.Abs(constrainedScale - zoomScale) > 1e-6)
                {
                    LoggingService.LogDebug($"Zoom scale constrained from {zoomScale:F6} to {constrainedScale:F6}", 
                        "EdgeCaseHandler");
                }

                return constrainedScale;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error constraining zoom scale: {ex.Message}", "EdgeCaseHandler");
                return 1.0;
            }
        }

        /// <summary>
        /// Validates and normalizes rotation angle values.
        /// </summary>
        /// <param name="rotationAngle">Requested rotation angle in degrees</param>
        /// <returns>Normalized rotation angle (0-360 degrees)</returns>
        public static double NormalizeRotationAngle(double rotationAngle)
        {
            try
            {
                if (double.IsNaN(rotationAngle) || double.IsInfinity(rotationAngle))
                {
                    LoggingService.LogWarning($"Invalid rotation angle: {rotationAngle}, using 0", "EdgeCaseHandler");
                    return 0.0;
                }

                // Normalize to 0-360 range
                double normalized = ((rotationAngle % 360) + 360) % 360;

                return normalized;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error normalizing rotation angle: {ex.Message}", "EdgeCaseHandler");
                return 0.0;
            }
        }

        /// <summary>
        /// Handles extreme transformation scenarios that might cause numerical instability.
        /// </summary>
        /// <param name="backendState">Backend transformation state to validate</param>
        /// <returns>Validated and corrected backend state</returns>
        public static BackendTransformState HandleExtremeTransformations(BackendTransformState backendState)
        {
            try
            {
                if (backendState == null)
                {
                    LoggingService.LogWarning("Null backend state provided, creating default", "EdgeCaseHandler");
                    return CreateDefaultBackendState();
                }

                // Constrain zoom scale
                backendState.ZoomScale = ConstrainZoomScale(backendState.ZoomScale);

                // Normalize rotation angle
                backendState.RotationAngle = NormalizeRotationAngle(backendState.RotationAngle);

                // Validate drag offset
                if (double.IsNaN(backendState.DragOffset.X) || double.IsInfinity(backendState.DragOffset.X) ||
                    double.IsNaN(backendState.DragOffset.Y) || double.IsInfinity(backendState.DragOffset.Y))
                {
                    LoggingService.LogWarning("Invalid drag offset detected, resetting to origin", "EdgeCaseHandler");
                    backendState.DragOffset = new Point(0, 0);
                }

                // Validate crop rectangle
                if (backendState.CropRectangle.IsEmpty || 
                    double.IsNaN(backendState.CropRectangle.Width) || 
                    double.IsNaN(backendState.CropRectangle.Height))
                {
                    LoggingService.LogWarning("Invalid crop rectangle detected, using default", "EdgeCaseHandler");
                    backendState.CropRectangle = new Rect(0, 0, 254, 290);
                }

                return backendState;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling extreme transformations: {ex.Message}", "EdgeCaseHandler");
                return CreateDefaultBackendState();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validates that an image size is valid for processing
        /// </summary>
        private static bool IsValidImageSize(Size imageSize)
        {
            return imageSize.Width >= MinimumImageDimension && 
                   imageSize.Height >= MinimumImageDimension &&
                   !double.IsNaN(imageSize.Width) && !double.IsInfinity(imageSize.Width) &&
                   !double.IsNaN(imageSize.Height) && !double.IsInfinity(imageSize.Height);
        }

        /// <summary>
        /// Determines if an image is considered small
        /// </summary>
        private static bool IsSmallImage(Size imageSize)
        {
            return imageSize.Width < 500 && imageSize.Height < 320;
        }

        /// <summary>
        /// Creates a fallback backend container result
        /// </summary>
        private static BackendContainerResult CreateFallbackResult()
        {
            return new BackendContainerResult
            {
                BackendSize = new Size(1000, 640),
                ScaleFactor = 2.0,
                RequiresUpscaling = false,
                UpscaleFactor = 1.0,
                IsValid = false
            };
        }

        /// <summary>
        /// Creates a default backend transformation state
        /// </summary>
        private static BackendTransformState CreateDefaultBackendState()
        {
            return new BackendTransformState
            {
                ZoomScale = 1.0,
                RotationAngle = 0.0,
                DragOffset = new Point(0, 0),
                CropRectangle = new Rect(0, 0, 254, 290),
                ImageDisplaySize = new Size(500, 320),
                PreviewImageProperties = new PreviewImageProperties
                {
                    DisplayScale = 1.0,
                    DisplaySize = new Size(500, 320),
                    CenterOffset = new Point(0, 0),
                    ZoomScale = 1.0
                }
            };
        }

        #endregion
    }
}
