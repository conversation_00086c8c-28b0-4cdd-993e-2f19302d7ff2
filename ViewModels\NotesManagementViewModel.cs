using System.Linq;
using System.Windows.Input;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for managing client notes.
    /// Handles the collection of notes associated with the client.
    /// Extracted from NewClientViewModel to improve maintainability and separation of concerns.
    /// </summary>
    public class NotesManagementViewModel : BaseViewModel
    {
        #region Private Fields

        private NotesCollectionModel _notes;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the notes collection for client notes management.
        /// This collection is used by the NFileCheckView for managing client notes.
        /// Data persists throughout the client creation process until Save or Cancel.
        /// </summary>
        public NotesCollectionModel Notes
        {
            get => _notes;
            private set => SetProperty(ref _notes, value);
        }

        /// <summary>
        /// Gets the display text for the notes card in NFileCheckView.
        /// Shows the most recent note content when notes exist, or fallback message when no notes are present.
        /// </summary>
        public string NotesDisplayText
        {
            get
            {
                if (_notes == null || _notes.Count == 0)
                {
                    return "لايوجد ملاحظات بعد";
                }

                var latestNote = _notes.GetNotesSortedByDate().FirstOrDefault();
                return latestNote?.Content ?? "لايوجد ملاحظات بعد";
            }
        }

        /// <summary>
        /// Gets the total count of notes.
        /// </summary>
        public int NotesCount => _notes?.Count ?? 0;

        /// <summary>
        /// Gets a value indicating whether there are any notes.
        /// </summary>
        public bool HasNotes => NotesCount > 0;

        #endregion

        #region Commands

        /// <summary>
        /// Command to add a new note.
        /// </summary>
        public ICommand AddNoteCommand { get; }

        /// <summary>
        /// Command to edit an existing note.
        /// </summary>
        public ICommand EditNoteCommand { get; }

        /// <summary>
        /// Command to delete a note.
        /// </summary>
        public ICommand DeleteNoteCommand { get; }

        /// <summary>
        /// Command to manage notes (open notes management dialog).
        /// </summary>
        public ICommand ManageNotesCommand { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the NotesManagementViewModel class.
        /// </summary>
        public NotesManagementViewModel()
        {
            _notes = new NotesCollectionModel();

            // Subscribe to notes collection changes to update display properties
            _notes.Notes.CollectionChanged += (s, e) => RefreshNotesDisplay();

            // Initialize commands
            AddNoteCommand = new RelayCommand(
                execute: AddNote,
                commandName: "AddNote"
            );

            EditNoteCommand = new RelayCommand(
                execute: () => EditNote(),
                canExecute: () => _notes?.SelectedNote != null,
                commandName: "EditNote"
            );

            DeleteNoteCommand = new RelayCommand(
                execute: () => DeleteNote(),
                canExecute: () => _notes?.SelectedNote != null,
                commandName: "DeleteNote"
            );

            ManageNotesCommand = new RelayCommand(
                execute: ManageNotes,
                commandName: "ManageNotes"
            );

            LoggingService.LogDebug("NotesManagementViewModel initialized", "NotesManagementViewModel");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Refreshes the notes display text by triggering property change notification.
        /// Called when notes are updated through dialogs or direct manipulation.
        /// </summary>
        public void RefreshNotesDisplay()
        {
            OnPropertyChanged(nameof(NotesDisplayText));
            OnPropertyChanged(nameof(NotesCount));
            OnPropertyChanged(nameof(HasNotes));
        }

        /// <summary>
        /// Validates the notes data.
        /// </summary>
        /// <returns>True if all notes data is valid, false otherwise</returns>
        public bool IsValid()
        {
            try
            {
                // Notes are optional, but if provided, they should have content
                if (_notes?.Notes?.Count > 0)
                {
                    foreach (var note in _notes.Notes)
                    {
                        if (string.IsNullOrWhiteSpace(note.Content))
                        {
                            LoggingService.LogDebug("Notes validation failed: Empty note content found", "NotesManagementViewModel");
                            return false;
                        }
                    }
                }

                LoggingService.LogDebug("Notes validation passed", "NotesManagementViewModel");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating notes data: {ex.Message}", "NotesManagementViewModel");
                return false;
            }
        }

        /// <summary>
        /// Clears all notes.
        /// </summary>
        public void Clear()
        {
            try
            {
                _notes?.Notes?.Clear();
                RefreshNotesDisplay();
                LoggingService.LogDebug("Notes cleared", "NotesManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing notes: {ex.Message}", "NotesManagementViewModel");
            }
        }

        /// <summary>
        /// Adds a new note with the specified content.
        /// </summary>
        /// <param name="content">The content of the note</param>
        /// <param name="isImportant">Whether the note is marked as important</param>
        public void AddNoteWithContent(string content, bool isImportant = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(content))
                {
                    LoggingService.LogWarning("Cannot add note with empty content", "NotesManagementViewModel");
                    return;
                }

                var newNote = new NoteModel
                {
                    Content = content.Trim(),
                    Priority = isImportant ? 2 : 0, // High priority if important, normal otherwise
                    CreatedDate = DateTime.Now
                };

                _notes.Notes.Add(newNote);
                RefreshNotesDisplay();

                LoggingService.LogInfo($"Note added: {content.Substring(0, Math.Min(50, content.Length))}...", "NotesManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding note: {ex.Message}", "NotesManagementViewModel");
            }
        }

        /// <summary>
        /// Updates an existing note with new content.
        /// </summary>
        /// <param name="note">The note to update</param>
        /// <param name="newContent">The new content for the note</param>
        /// <param name="isImportant">Whether the note is marked as important</param>
        public void UpdateNote(NoteModel note, string newContent, bool isImportant = false)
        {
            try
            {
                if (note == null)
                {
                    LoggingService.LogWarning("Cannot update null note", "NotesManagementViewModel");
                    return;
                }

                if (string.IsNullOrWhiteSpace(newContent))
                {
                    LoggingService.LogWarning("Cannot update note with empty content", "NotesManagementViewModel");
                    return;
                }

                note.Content = newContent.Trim();
                note.Priority = isImportant ? 2 : 0; // High priority if important, normal otherwise
                note.ModifiedDate = DateTime.Now;

                RefreshNotesDisplay();

                LoggingService.LogInfo($"Note updated: {newContent.Substring(0, Math.Min(50, newContent.Length))}...", "NotesManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating note: {ex.Message}", "NotesManagementViewModel");
            }
        }

        /// <summary>
        /// Removes a note from the collection.
        /// </summary>
        /// <param name="note">The note to remove</param>
        public void RemoveNote(NoteModel note)
        {
            try
            {
                if (note == null)
                {
                    LoggingService.LogWarning("Cannot remove null note", "NotesManagementViewModel");
                    return;
                }

                _notes.Notes.Remove(note);
                RefreshNotesDisplay();

                LoggingService.LogInfo($"Note removed: {note.Content?.Substring(0, Math.Min(50, note.Content?.Length ?? 0))}...", "NotesManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing note: {ex.Message}", "NotesManagementViewModel");
            }
        }

        /// <summary>
        /// Gets all notes sorted by date (most recent first).
        /// </summary>
        /// <returns>List of notes sorted by date</returns>
        public List<NoteModel> GetNotesSortedByDate()
        {
            try
            {
                return _notes?.GetNotesSortedByDate()?.ToList() ?? new List<NoteModel>();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting sorted notes: {ex.Message}", "NotesManagementViewModel");
                return new List<NoteModel>();
            }
        }

        /// <summary>
        /// Gets all important notes.
        /// </summary>
        /// <returns>List of important notes</returns>
        public List<NoteModel> GetImportantNotes()
        {
            try
            {
                return _notes?.Notes?.Where(n => n.Priority > 0)?.ToList() ?? new List<NoteModel>();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting important notes: {ex.Message}", "NotesManagementViewModel");
                return new List<NoteModel>();
            }
        }

        /// <summary>
        /// Searches notes by content.
        /// </summary>
        /// <param name="searchTerm">The term to search for</param>
        /// <returns>List of notes containing the search term</returns>
        public List<NoteModel> SearchNotes(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return GetNotesSortedByDate();
                }

                return _notes?.Notes?
                    .Where(n => n.Content?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)?
                    .ToList() ?? new List<NoteModel>();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error searching notes: {ex.Message}", "NotesManagementViewModel");
                return new List<NoteModel>();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Adds a new note through the dialog interface.
        /// </summary>
        private void AddNote()
        {
            try
            {
                LoggingService.LogInfo("Add note dialog requested", "NotesManagementViewModel");
                
                // TODO: Implement add note dialog opening
                // This should integrate with the existing AddNotesDialogViewModel
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening add note dialog: {ex.Message}", "NotesManagementViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء فتح نافذة إضافة الملاحظة", 
                    "خطأ في إضافة الملاحظة", 
                    LogLevel.Error, 
                    "NotesManagementViewModel");
            }
        }

        /// <summary>
        /// Edits the selected note through the dialog interface.
        /// </summary>
        private void EditNote()
        {
            try
            {
                if (_notes?.SelectedNote == null)
                {
                    LoggingService.LogWarning("No note selected for editing", "NotesManagementViewModel");
                    return;
                }

                LoggingService.LogInfo("Edit note dialog requested", "NotesManagementViewModel");
                
                // TODO: Implement edit note dialog opening
                // This should integrate with the existing note editing dialog
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening edit note dialog: {ex.Message}", "NotesManagementViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء فتح نافذة تعديل الملاحظة", 
                    "خطأ في تعديل الملاحظة", 
                    LogLevel.Error, 
                    "NotesManagementViewModel");
            }
        }

        /// <summary>
        /// Deletes the selected note.
        /// </summary>
        private void DeleteNote()
        {
            try
            {
                if (_notes?.SelectedNote == null)
                {
                    LoggingService.LogWarning("No note selected for deletion", "NotesManagementViewModel");
                    return;
                }

                // TODO: Show confirmation dialog before deletion
                RemoveNote(_notes.SelectedNote);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error deleting note: {ex.Message}", "NotesManagementViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء حذف الملاحظة", 
                    "خطأ في حذف الملاحظة", 
                    LogLevel.Error, 
                    "NotesManagementViewModel");
            }
        }

        /// <summary>
        /// Opens the notes management dialog.
        /// </summary>
        private void ManageNotes()
        {
            try
            {
                LoggingService.LogInfo("Notes management dialog requested", "NotesManagementViewModel");
                
                // TODO: Implement notes management dialog opening
                // This should integrate with the existing notes management dialog
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening notes management dialog: {ex.Message}", "NotesManagementViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء فتح نافذة إدارة الملاحظات", 
                    "خطأ في إدارة الملاحظات", 
                    LogLevel.Error, 
                    "NotesManagementViewModel");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the ViewModel resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _notes?.Notes?.Clear();
                LoggingService.LogDebug("NotesManagementViewModel disposed", "NotesManagementViewModel");
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}