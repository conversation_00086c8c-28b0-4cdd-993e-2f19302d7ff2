using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Service for detecting duplicate clients and resolving activity descriptions.
    /// Provides database queries to find clients with matching NameFr and complete client data retrieval.
    /// Integrates with UFU2 architecture patterns and follows MVVM design principles.
    /// </summary>
    public class DuplicateClientDetectionService : IDisposable
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;
        private bool _disposed = false;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the DuplicateClientDetectionService class.
        /// </summary>
        public DuplicateClientDetectionService()
        {
            _databaseService = ServiceLocator.GetService<DatabaseService>() 
                ?? throw new InvalidOperationException("DatabaseService not found in ServiceLocator");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Finds clients with matching NameFr and returns complete client information with activity descriptions.
        /// Uses optimized JOIN queries to retrieve all necessary data in a single database round trip.
        /// </summary>
        /// <param name="nameFr">The French name to search for</param>
        /// <returns>List of duplicate client data with resolved activity descriptions</returns>
        public async Task<List<DuplicateClientData>> FindDuplicateClientsAsync(string nameFr)
        {
            if (string.IsNullOrWhiteSpace(nameFr))
            {
                LoggingService.LogWarning("NameFr is required for duplicate detection", "DuplicateClientDetectionService");
                return new List<DuplicateClientData>();
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Optimized query to get clients with matching NameFr and their first activity
                const string duplicateQuery = @"
                    SELECT DISTINCT
                        c.Uid as ClientUid,
                        c.NameFr,
                        c.NameAr,
                        c.BirthDate,
                        c.BirthPlace,
                        c.Gender,
                        c.Address,
                        c.NationalId,
                        c.CreatedAt,
                        a.Uid as ActivityUid,
                        a.ActivityType,
                        a.CreatedAt as ActivityCreatedAt
                    FROM Clients c
                    LEFT JOIN Activities a ON c.Uid = a.ClientUid
                    WHERE LOWER(TRIM(c.NameFr)) = LOWER(TRIM(@NameFr))
                    ORDER BY c.CreatedAt DESC, a.CreatedAt ASC";

                var queryResults = await connection.QueryAsync<dynamic>(duplicateQuery, new { NameFr = nameFr.Trim() });

                if (!queryResults.Any())
                {
                    LoggingService.LogDebug($"No duplicate clients found for NameFr: {nameFr}", "DuplicateClientDetectionService");
                    return new List<DuplicateClientData>();
                }

                // Group by client and process each client's data
                var clientGroups = queryResults.GroupBy(r => (string)r.ClientUid);
                var duplicateClients = new List<DuplicateClientData>();

                foreach (var clientGroup in clientGroups)
                {
                    var firstRow = clientGroup.First();
                    var clientData = new DuplicateClientData
                    {
                        ClientUid = firstRow.ClientUid,
                        NameFr = firstRow.NameFr ?? string.Empty,
                        NameAr = firstRow.NameAr,
                        BirthDate = firstRow.BirthDate,
                        BirthPlace = firstRow.BirthPlace,
                        Gender = firstRow.Gender != null ? (int)(long)firstRow.Gender : 0,
                        Address = firstRow.Address,
                        NationalId = firstRow.NationalId,
                        CreatedAt = firstRow.CreatedAt ?? string.Empty
                    };

                    // Get activity description for the first activity (if any)
                    var firstActivity = clientGroup.Where(r => r.ActivityUid != null).FirstOrDefault();
                    if (firstActivity != null)
                    {
                        clientData.ActivityDescription = await ResolveActivityDescriptionAsync(connection, 
                            firstActivity.ActivityUid, firstActivity.ActivityType);
                    }
                    else
                    {
                        clientData.ActivityDescription = "نشاط غير محدد"; // Unspecified Activity
                    }

                    // Load phone numbers for this client
                    clientData.PhoneNumbers = await GetClientPhoneNumbersAsync(connection, clientData.ClientUid);

                    duplicateClients.Add(clientData);
                }

                LoggingService.LogInfo($"Found {duplicateClients.Count} duplicate clients for NameFr: {nameFr}", "DuplicateClientDetectionService");
                return duplicateClients;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error finding duplicate clients for NameFr '{nameFr}': {ex.Message}", "DuplicateClientDetectionService");
                throw;
            }
        }

        /// <summary>
        /// Retrieves complete client data including all phone numbers for data loading.
        /// </summary>
        /// <param name="clientUid">The client UID to retrieve</param>
        /// <returns>Complete client data or null if not found</returns>
        public async Task<DuplicateClientData?> GetCompleteClientDataAsync(string clientUid)
        {
            if (string.IsNullOrWhiteSpace(clientUid))
            {
                LoggingService.LogWarning("ClientUid is required for complete data retrieval", "DuplicateClientDetectionService");
                return null;
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Get client basic data
                const string clientQuery = @"
                    SELECT Uid, NameFr, NameAr, BirthDate, BirthPlace, Gender, Address, NationalId, CreatedAt
                    FROM Clients
                    WHERE Uid = @ClientUid";

                var clientData = await connection.QueryFirstOrDefaultAsync<dynamic>(clientQuery, new { ClientUid = clientUid });

                if (clientData == null)
                {
                    LoggingService.LogWarning($"Client not found: {clientUid}", "DuplicateClientDetectionService");
                    return null;
                }

                var result = new DuplicateClientData
                {
                    ClientUid = clientData.Uid,
                    NameFr = clientData.NameFr ?? string.Empty,
                    NameAr = clientData.NameAr,
                    BirthDate = clientData.BirthDate,
                    BirthPlace = clientData.BirthPlace,
                    Gender = clientData.Gender != null ? (int)(long)clientData.Gender : 0,
                    Address = clientData.Address,
                    NationalId = clientData.NationalId,
                    CreatedAt = clientData.CreatedAt ?? string.Empty
                };

                // Load all phone numbers
                result.PhoneNumbers = await GetClientPhoneNumbersAsync(connection, clientUid);

                LoggingService.LogInfo($"Retrieved complete client data for: {clientUid}", "DuplicateClientDetectionService");
                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error retrieving complete client data for '{clientUid}': {ex.Message}", "DuplicateClientDetectionService");
                throw;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Resolves activity description based on ActivityType and related data.
        /// </summary>
        private async Task<string> ResolveActivityDescriptionAsync(System.Data.IDbConnection connection, string activityUid, string activityType)
        {
            try
            {
                switch (activityType)
                {
                    case "MainCommercial":
                    case "SecondaryCommercial":
                        return await ResolveCommercialActivityDescriptionAsync(connection, activityUid);

                    case "Craft":
                        return await ResolveCraftActivityDescriptionAsync(connection, activityUid);

                    case "Professional":
                        return await ResolveProfessionalActivityDescriptionAsync(connection, activityUid);

                    default:
                        LoggingService.LogWarning($"Unknown activity type: {activityType}", "DuplicateClientDetectionService");
                        return "نشاط غير محدد"; // Unspecified Activity
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error resolving activity description for {activityUid}: {ex.Message}", "DuplicateClientDetectionService");
                return "نشاط غير محدد"; // Fallback
            }
        }

        /// <summary>
        /// Resolves commercial activity description from ActivityTypeBase table.
        /// </summary>
        private async Task<string> ResolveCommercialActivityDescriptionAsync(System.Data.IDbConnection connection, string activityUid)
        {
            const string commercialQuery = @"
                SELECT atb.Description
                FROM CommercialActivityCodes cac
                INNER JOIN ActivityTypeBase atb ON cac.ActivityCode = atb.Code
                WHERE cac.ActivityUid = @ActivityUid
                LIMIT 1";

            var description = await connection.QueryFirstOrDefaultAsync<string>(commercialQuery, new { ActivityUid = activityUid });
            return description ?? "نشاط تجاري غير محدد"; // Unspecified Commercial Activity
        }

        /// <summary>
        /// Resolves craft activity description from CraftTypeBase table.
        /// </summary>
        private async Task<string> ResolveCraftActivityDescriptionAsync(System.Data.IDbConnection connection, string activityUid)
        {
            const string craftQuery = @"
                SELECT ctb.Description
                FROM CraftActivityCodes cac
                INNER JOIN CraftTypeBase ctb ON cac.CraftCode = ctb.Code
                WHERE cac.ActivityUid = @ActivityUid";

            var description = await connection.QueryFirstOrDefaultAsync<string>(craftQuery, new { ActivityUid = activityUid });
            return description ?? "حرفة غير محددة"; // Unspecified Craft
        }

        /// <summary>
        /// Resolves professional activity description from ProfessionNames table.
        /// </summary>
        private async Task<string> ResolveProfessionalActivityDescriptionAsync(System.Data.IDbConnection connection, string activityUid)
        {
            const string professionalQuery = @"
                SELECT ActivityDescription
                FROM ProfessionNames
                WHERE ActivityUid = @ActivityUid";

            var description = await connection.QueryFirstOrDefaultAsync<string>(professionalQuery, new { ActivityUid = activityUid });
            return description ?? "مهنة غير محددة"; // Unspecified Profession
        }

        /// <summary>
        /// Retrieves all phone numbers for a client with type and primary designation.
        /// </summary>
        private async Task<List<PhoneNumberData>> GetClientPhoneNumbersAsync(System.Data.IDbConnection connection, string clientUid)
        {
            const string phoneQuery = @"
                SELECT PhoneNumber, PhoneType, IsPrimary
                FROM PhoneNumbers
                WHERE ClientUid = @ClientUid
                ORDER BY IsPrimary DESC, PhoneType ASC";

            var phoneResults = await connection.QueryAsync<dynamic>(phoneQuery, new { ClientUid = clientUid });

            return phoneResults.Select(p => new PhoneNumberData
            {
                ClientUid = clientUid,
                PhoneNumber = p.PhoneNumber ?? string.Empty,
                PhoneType = ConvertPhoneTypeToString(p.PhoneType != null ? (int)(long)p.PhoneType : 0),
                IsPrimary = p.IsPrimary == 1
            }).ToList();
        }

        /// <summary>
        /// Converts integer phone type to string representation.
        /// </summary>
        private static string ConvertPhoneTypeToString(int phoneType)
        {
            return phoneType switch
            {
                0 => "Mobile",
                1 => "Home",
                2 => "Work",
                3 => "Fax",
                _ => "Mobile"
            };
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the service and releases resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // No specific resources to dispose for this service
                _disposed = true;
            }
        }

        #endregion
    }
}
