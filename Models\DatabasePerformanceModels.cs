using System;
using System.Collections.Generic;

namespace UFU2.Models
{
    /// <summary>
    /// Represents a query performance metric for database monitoring.
    /// </summary>
    public class QueryPerformanceMetric
    {
        /// <summary>
        /// The SQL query that was executed.
        /// </summary>
        public string Query { get; set; } = string.Empty;

        /// <summary>
        /// Name of the operation for identification.
        /// </summary>
        public string OperationName { get; set; } = string.Empty;

        /// <summary>
        /// Query execution time in milliseconds.
        /// </summary>
        public long ExecutionTimeMs { get; set; }

        /// <summary>
        /// Start time of the query execution (UTC).
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End time of the query execution (UTC).
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Number of rows returned or affected.
        /// </summary>
        public int RowCount { get; set; }

        /// <summary>
        /// Query plan analysis information.
        /// </summary>
        public QueryPlanAnalysis? QueryPlan { get; set; }
    }

    /// <summary>
    /// Represents the analysis of a query execution plan.
    /// </summary>
    public class QueryPlanAnalysis
    {
        /// <summary>
        /// Raw query plan rows from EXPLAIN QUERY PLAN.
        /// </summary>
        public List<QueryPlanRow> PlanRows { get; set; } = new List<QueryPlanRow>();

        /// <summary>
        /// Indicates if the query uses table scans.
        /// </summary>
        public bool HasTableScan { get; set; }

        /// <summary>
        /// Indicates if the query uses indexes.
        /// </summary>
        public bool HasIndexUsage { get; set; }

        /// <summary>
        /// List of tables accessed by the query.
        /// </summary>
        public List<string> TablesAccessed { get; set; } = new List<string>();

        /// <summary>
        /// List of indexes used by the query.
        /// </summary>
        public List<string> IndexesUsed { get; set; } = new List<string>();
    }

    /// <summary>
    /// Represents a single row from EXPLAIN QUERY PLAN output.
    /// </summary>
    public class QueryPlanRow
    {
        /// <summary>
        /// Plan row ID.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Parent row ID.
        /// </summary>
        public int Parent { get; set; }

        /// <summary>
        /// Auxiliary information.
        /// </summary>
        public int NotUsed { get; set; }

        /// <summary>
        /// Detailed description of the plan step.
        /// </summary>
        public string Detail { get; set; } = string.Empty;
    }

    /// <summary>
    /// Represents a database index for analysis.
    /// </summary>
    public class DatabaseIndex
    {
        /// <summary>
        /// Name of the index.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Name of the table the index belongs to.
        /// </summary>
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// SQL definition of the index.
        /// </summary>
        public string? Definition { get; set; }
    }

    /// <summary>
    /// Represents statistics for a database index.
    /// </summary>
    public class IndexStatistics
    {
        /// <summary>
        /// Name of the index.
        /// </summary>
        public string IndexName { get; set; } = string.Empty;

        /// <summary>
        /// Name of the table the index belongs to.
        /// </summary>
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// SQL definition of the index.
        /// </summary>
        public string Definition { get; set; } = string.Empty;

        /// <summary>
        /// Number of times the index has been used (estimated).
        /// </summary>
        public int UsageCount { get; set; }

        /// <summary>
        /// Last time the index was used.
        /// </summary>
        public DateTime? LastUsed { get; set; }

        /// <summary>
        /// Indicates if the index appears to be unused.
        /// </summary>
        public bool IsUnused => UsageCount == 0;
    }

    /// <summary>
    /// Represents the analysis of index effectiveness.
    /// </summary>
    public class IndexEffectivenessAnalysis
    {
        /// <summary>
        /// Total number of indexes analyzed.
        /// </summary>
        public int TotalIndexes { get; set; }

        /// <summary>
        /// Statistics for each index.
        /// </summary>
        public List<IndexStatistics> IndexStatistics { get; set; } = new List<IndexStatistics>();

        /// <summary>
        /// List of unused index names.
        /// </summary>
        public List<string> UnusedIndexes { get; set; } = new List<string>();

        /// <summary>
        /// Optimization recommendations.
        /// </summary>
        public List<string> Recommendations { get; set; } = new List<string>();
    }

    /// <summary>
    /// Represents a comprehensive performance report.
    /// </summary>
    public class PerformanceReport
    {
        /// <summary>
        /// Time period covered by the report.
        /// </summary>
        public string ReportPeriod { get; set; } = string.Empty;

        /// <summary>
        /// Total number of queries executed.
        /// </summary>
        public int TotalQueries { get; set; }

        /// <summary>
        /// Average execution time in milliseconds.
        /// </summary>
        public double AverageExecutionTimeMs { get; set; }

        /// <summary>
        /// Maximum execution time in milliseconds.
        /// </summary>
        public long MaxExecutionTimeMs { get; set; }

        /// <summary>
        /// Minimum execution time in milliseconds.
        /// </summary>
        public long MinExecutionTimeMs { get; set; }

        /// <summary>
        /// Number of slow queries (above threshold).
        /// </summary>
        public int SlowQueryCount { get; set; }

        /// <summary>
        /// Top slow queries for analysis.
        /// </summary>
        public List<QueryPerformanceMetric> TopSlowQueries { get; set; } = new List<QueryPerformanceMetric>();

        /// <summary>
        /// Number of queries that used table scans.
        /// </summary>
        public int TableScanCount { get; set; }

        /// <summary>
        /// Number of queries that used indexes.
        /// </summary>
        public int IndexUsageCount { get; set; }

        /// <summary>
        /// Percentage of queries using indexes.
        /// </summary>
        public double IndexUsagePercentage => TotalQueries > 0 ? (IndexUsageCount * 100.0) / TotalQueries : 0;

        /// <summary>
        /// Percentage of slow queries.
        /// </summary>
        public double SlowQueryPercentage => TotalQueries > 0 ? (SlowQueryCount * 100.0) / TotalQueries : 0;
    }

    /// <summary>
    /// Represents the result of a database maintenance operation.
    /// </summary>
    public class MaintenanceResult
    {
        /// <summary>
        /// Start time of the maintenance operation (UTC).
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End time of the maintenance operation (UTC).
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Total duration of the maintenance operation in milliseconds.
        /// </summary>
        public long TotalDurationMs { get; set; }

        /// <summary>
        /// Duration of the VACUUM operation in milliseconds.
        /// </summary>
        public long VacuumDurationMs { get; set; }

        /// <summary>
        /// Duration of the ANALYZE operation in milliseconds.
        /// </summary>
        public long AnalyzeDurationMs { get; set; }

        /// <summary>
        /// Database size before maintenance in MB.
        /// </summary>
        public double DatabaseSizeBeforeMB { get; set; }

        /// <summary>
        /// Database size after maintenance in MB.
        /// </summary>
        public double DatabaseSizeAfterMB { get; set; }

        /// <summary>
        /// Amount of space reclaimed in MB.
        /// </summary>
        public double SpaceReclaimedMB { get; set; }

        /// <summary>
        /// Indicates if the maintenance operation was successful.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if the operation failed.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets a summary of the maintenance operation.
        /// </summary>
        public string GetSummary()
        {
            if (!Success)
            {
                return $"Maintenance failed: {ErrorMessage}";
            }

            return $"Maintenance completed in {TotalDurationMs}ms. " +
                   $"Database size: {DatabaseSizeBeforeMB:F2}MB → {DatabaseSizeAfterMB:F2}MB " +
                   $"(reclaimed {SpaceReclaimedMB:F2}MB). " +
                   $"VACUUM: {VacuumDurationMs}ms, ANALYZE: {AnalyzeDurationMs}ms.";
        }
    }
}