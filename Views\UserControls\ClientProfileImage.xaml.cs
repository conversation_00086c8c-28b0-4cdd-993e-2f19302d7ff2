using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using UFU2.Common;
using MaterialDesignThemes.Wpf;

namespace UFU2.Views.UserControls
{
    /// <summary>
    /// UserControl for displaying and managing client profile images.
    /// Provides click-to-edit functionality with gender-based default images.
    /// Follows UFU2 standards with MaterialDesign styling and Arabic RTL support.
    /// OPTIMIZED: Enhanced with debouncing and reduced logging for Phase 2B UI optimizations.
    /// </summary>
    public partial class ClientProfileImage : UserControl
    {
        #region Private Fields for Optimization

        // Debouncing for property change handlers to reduce excessive logging
        private readonly DispatcherTimer _debounceTimer;
        private int _pendingGenderChange = -1;
        private bool _hasPendingImageSourceChange = false;
        private const int DebounceDelayMs = 100; // 100ms debounce delay

        // Performance monitoring
        private static int _genderChangeCount = 0;
        private static int _imageSourceChangeCount = 0;

        #endregion

        #region Dependency Properties

        /// <summary>
        /// Dependency property for the Gender value used to determine default image.
        /// Binds to the Gender property from the parent ViewModel.
        /// OPTIMIZED: Enhanced with debounced change handling.
        /// </summary>
        public static readonly DependencyProperty GenderProperty =
            DependencyProperty.Register(
                nameof(Gender),
                typeof(int),
                typeof(ClientProfileImage),
                new PropertyMetadata(0, OnGenderChanged)); // Default to Male (0)

        /// <summary>
        /// Gets or sets the gender value for determining the appropriate default image.
        /// 0 = Male (ذكر), 1 = Female (أنثى)
        /// Maps to ComboBox SelectedIndex: 0 = ذكر, 1 = أنثى
        /// </summary>
        public int Gender
        {
            get => (int)GetValue(GenderProperty);
            set => SetValue(GenderProperty, value);
        }

        /// <summary>
        /// Dependency property for custom profile image source.
        /// When set, this overrides the gender-based default image.
        /// </summary>
        public static readonly DependencyProperty ProfileImageSourceProperty =
            DependencyProperty.Register(
                nameof(ProfileImageSource),
                typeof(System.Windows.Media.ImageSource),
                typeof(ClientProfileImage),
                new PropertyMetadata(null, OnProfileImageSourceChanged));

        /// <summary>
        /// Gets or sets the custom profile image source.
        /// When null, the control displays gender-based default images.
        /// </summary>
        public System.Windows.Media.ImageSource? ProfileImageSource
        {
            get => (System.Windows.Media.ImageSource?)GetValue(ProfileImageSourceProperty);
            set => SetValue(ProfileImageSourceProperty, value);
        }

        /// <summary>
        /// Dependency property for controlling the loading state.
        /// Shows/hides the loading indicator during image operations.
        /// </summary>
        public static readonly DependencyProperty IsLoadingProperty =
            DependencyProperty.Register(
                nameof(IsLoading),
                typeof(bool),
                typeof(ClientProfileImage),
                new PropertyMetadata(false, OnIsLoadingChanged));

        /// <summary>
        /// Gets or sets whether the control is in loading state.
        /// When true, displays a loading indicator and disables interaction.
        /// </summary>
        public bool IsLoading
        {
            get => (bool)GetValue(IsLoadingProperty);
            set => SetValue(IsLoadingProperty, value);
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the profile image is clicked for editing.
        /// Allows parent controls to handle the image management dialog opening.
        /// </summary>
        public event EventHandler? ImageEditRequested;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ClientProfileImage UserControl.
        /// Sets up the control with proper data context and event handlers.
        /// OPTIMIZED: Enhanced with debounce timer initialization.
        /// </summary>
        public ClientProfileImage()
        {
            InitializeComponent();

            // Initialize debounce timer for property change optimization
            _debounceTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(DebounceDelayMs)
            };
            _debounceTimer.Tick += DebounceTimer_Tick;

            LoggingService.LogDebug("ClientProfileImage control initialized with optimization", "ClientProfileImage");

            // Add debugging for binding (reduced frequency)
            this.Loaded += (s, e) =>
            {
                LoggingService.LogDebug($"ClientProfileImage loaded - Gender: {Gender}", "ClientProfileImage");
            };
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the profile image button click event.
        /// Opens the ImageManagementDialog using MaterialDesign DialogHost.
        /// Follows UFU2 patterns for dialog opening with proper button state management.
        /// </summary>
        private async void ProfileImageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug("Profile image button clicked", "ClientProfileImage");

                // Disable button during operation to prevent multiple clicks
                ProfileImageButton.IsEnabled = false;
                IsLoading = true;

                // Raise event for parent controls to handle
                // The parent control (NPersonalView) will handle the ImageManagementDialog opening
                ImageEditRequested?.Invoke(this, EventArgs.Empty);

                LoggingService.LogInfo("Image edit request delegated to parent control", "ClientProfileImage");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling profile image button click: {ex.Message}", "ClientProfileImage");

                // Show error toast notification in Arabic
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ أثناء فتح إدارة الصورة الشخصية",
                    "خطأ"
                );
            }
            finally
            {
                // Re-enable button and hide loading indicator
                IsLoading = false;
                ProfileImageButton.IsEnabled = true;
            }
        }

        #endregion

        #region Image Management Integration

        /// <summary>
        /// Handles successful image management by updating the profile image with the edited/cropped image.
        /// Integrates the cropped image into the profile image workflow and updates the UI.
        /// </summary>
        /// <param name="imageManagementDialog">The completed image management dialog</param>
        private async Task HandleSuccessfulImageManagement(UFU2.Views.Dialogs.ImageManagementDialog imageManagementDialog)
        {
            try
            {
                LoggingService.LogInfo("Processing successful image management result", "ClientProfileImage");

                // Get the edited image from the dialog
                var editedImage = imageManagementDialog.EditedImage;
                var savedImagePath = imageManagementDialog.SavedImagePath;

                if (editedImage != null)
                {
                    LoggingService.LogInfo($"Updating profile image with edited image - Size: {editedImage.PixelWidth}x{editedImage.PixelHeight}", "ClientProfileImage");

                    // Update the profile image source
                    await UpdateProfileImageSource(editedImage, savedImagePath);

                    // Raise event to notify parent controls of the image change
                    ImageUpdated?.Invoke(this, new ProfileImageUpdatedEventArgs(editedImage, savedImagePath));

                    // Show success toast notification
                    ErrorManager.ShowUserInfoToast(
                        "تم تحديث الصورة الشخصية بنجاح. الصورة الجديدة جاهزة للاستخدام.",
                        "تم تحديث الصورة"
                    );
                }
                else
                {
                    LoggingService.LogWarning("No edited image available from image management dialog", "ClientProfileImage");
                    ErrorManager.ShowUserWarningToast(
                        "لم يتم العثور على صورة محررة. يرجى المحاولة مرة أخرى.",
                        "لا توجد صورة"
                    );
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling successful image management: {ex.Message}", "ClientProfileImage");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تحديث الصورة الشخصية. يرجى المحاولة مرة أخرى.",
                    "خطأ في تحديث الصورة",
                    LogLevel.Error,
                    "ClientProfileImage");
            }
        }

        /// <summary>
        /// Updates the profile image source with the new edited image.
        /// Handles proper disposal of the previous image and updates the UI.
        /// </summary>
        /// <param name="newImage">The new edited image</param>
        /// <param name="imagePath">The path where the image was saved (optional)</param>
        private async Task UpdateProfileImageSource(BitmapImage newImage, string? imagePath)
        {
            try
            {
                LoggingService.LogDebug("Updating profile image source", "ClientProfileImage");

                // Update the ProfileImageSource dependency property
                // This will trigger the ProfileImageConverter to use the custom image
                ProfileImageSource = newImage;

                // Log the update
                LoggingService.LogInfo($"Profile image source updated successfully - Path: {imagePath ?? "In-memory"}", "ClientProfileImage");

                // Add a small delay to ensure UI updates
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating profile image source: {ex.Message}", "ClientProfileImage");
                throw;
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the profile image is updated with a new image
        /// </summary>
        public event EventHandler<ProfileImageUpdatedEventArgs>? ImageUpdated;

        #endregion

        #region Event Args

        /// <summary>
        /// Event arguments for profile image updated events
        /// </summary>
        public class ProfileImageUpdatedEventArgs : EventArgs
        {
            public BitmapImage UpdatedImage { get; }
            public string? SavedImagePath { get; }

            public ProfileImageUpdatedEventArgs(BitmapImage updatedImage, string? savedImagePath)
            {
                UpdatedImage = updatedImage;
                SavedImagePath = savedImagePath;
            }
        }

        #endregion

        #region Dependency Property Change Handlers

        /// <summary>
        /// Handles changes to the Gender dependency property.
        /// OPTIMIZED: Uses debouncing to reduce excessive logging and processing.
        /// </summary>
        private static void OnGenderChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ClientProfileImage control)
            {
                try
                {
                    System.Threading.Interlocked.Increment(ref _genderChangeCount);

                    var newGender = (int)e.NewValue;
                    control._pendingGenderChange = newGender;

                    // Start debounce timer to batch logging and processing
                    control._debounceTimer.Stop();
                    control._debounceTimer.Start();

                    // The image update is handled automatically by XAML MultiBinding with ProfileImageConverter
                    // No manual intervention needed - the converter will be called automatically
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error handling gender change: {ex.Message}", "ClientProfileImage");
                }
            }
        }

        /// <summary>
        /// Handles changes to the ProfileImageSource dependency property.
        /// OPTIMIZED: Uses debouncing to reduce excessive logging.
        /// </summary>
        private static void OnProfileImageSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ClientProfileImage control)
            {
                try
                {
                    System.Threading.Interlocked.Increment(ref _imageSourceChangeCount);

                    control._hasPendingImageSourceChange = true;

                    // Start debounce timer to batch logging
                    control._debounceTimer.Stop();
                    control._debounceTimer.Start();

                    // The image update is handled automatically by XAML MultiBinding with ProfileImageConverter
                    // No manual intervention needed - the converter will be called automatically
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error handling profile image source change: {ex.Message}", "ClientProfileImage");
                }
            }
        }

        /// <summary>
        /// Handles changes to the IsLoading dependency property.
        /// Shows/hides the loading indicator and manages button state.
        /// </summary>
        private static void OnIsLoadingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ClientProfileImage control)
            {
                try
                {
                    var isLoading = (bool)e.NewValue;
                    
                    // Update loading indicator visibility
                    control.LoadingIndicator.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
                    
                    // Update button enabled state
                    control.ProfileImageButton.IsEnabled = !isLoading;
                    
                    LoggingService.LogDebug($"Loading state changed to: {isLoading}", "ClientProfileImage");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error handling loading state change: {ex.Message}", "ClientProfileImage");
                }
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Updates the profile image with a new custom image source.
        /// This method can be called from parent controls after image editing.
        /// </summary>
        /// <param name="newImageSource">The new image source to display</param>
        public void UpdateProfileImage(System.Windows.Media.ImageSource? newImageSource)
        {
            try
            {
                ProfileImageSource = newImageSource;
                LoggingService.LogInfo("Profile image updated successfully", "ClientProfileImage");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating profile image: {ex.Message}", "ClientProfileImage");
            }
        }

        /// <summary>
        /// Resets the profile image to the gender-based default.
        /// Clears any custom image and reverts to automatic gender-based selection.
        /// </summary>
        public void ResetToDefault()
        {
            try
            {
                ProfileImageSource = null;
                LoggingService.LogInfo("Profile image reset to gender-based default", "ClientProfileImage");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error resetting profile image to default: {ex.Message}", "ClientProfileImage");
            }
        }

        /// <summary>
        /// Gets performance statistics for the ClientProfileImage component.
        /// Used for Phase 2B UI optimization monitoring.
        /// </summary>
        /// <returns>Performance statistics including change counts</returns>
        public static (int GenderChangeCount, int ImageSourceChangeCount) GetPerformanceStats()
        {
            return (_genderChangeCount, _imageSourceChangeCount);
        }

        /// <summary>
        /// Resets performance counters for testing and monitoring.
        /// </summary>
        public static void ResetPerformanceCounters()
        {
            System.Threading.Interlocked.Exchange(ref _genderChangeCount, 0);
            System.Threading.Interlocked.Exchange(ref _imageSourceChangeCount, 0);
        }

        #endregion

        #region Private Optimization Methods

        /// <summary>
        /// Handles the debounce timer tick to process batched property changes.
        /// OPTIMIZED: Reduces logging frequency and improves performance.
        /// </summary>
        private void DebounceTimer_Tick(object? sender, EventArgs e)
        {
            _debounceTimer.Stop();

            try
            {
                // Process pending gender change
                if (_pendingGenderChange >= 0)
                {
                    string genderText = _pendingGenderChange switch
                    {
                        0 => "Male",
                        1 => "Female",
                        _ => "Unknown"
                    };
                    LoggingService.LogDebug($"Gender changed to: {_pendingGenderChange} ({genderText})", "ClientProfileImage");
                    _pendingGenderChange = -1;
                }

                // Process pending image source change
                if (_hasPendingImageSourceChange)
                {
                    var currentImageSource = ProfileImageSource;
                    if (currentImageSource != null)
                    {
                        LoggingService.LogDebug("Custom profile image source set", "ClientProfileImage");
                    }
                    else
                    {
                        LoggingService.LogDebug($"Profile image source cleared, will revert to gender-based default for gender: {Gender}", "ClientProfileImage");
                    }
                    _hasPendingImageSourceChange = false;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in debounce timer tick: {ex.Message}", "ClientProfileImage");
            }
        }

        #endregion
    }
}
