using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Input;
using UFU2.Models;
using UFU2.Common;
using UFU2.Commands;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for PaymentYearsSelectionDialog.
    /// Manages year range calculation, tab-specific selections, and pagination.
    /// Follows UFU2 MVVM patterns with memory-only persistence.
    ///
    /// REFACTORING STATUS: ✅ COMPLETED - BaseViewModel inheritance implemented (Task 1.3)
    /// </summary>
    public class PaymentYearsSelectionDialogViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly List<PaymentYearModel> _allYears;
        private ObservableCollection<PaymentYearModel> _currentPageYears;
        private string _selectedTab;
        private int _currentPage;
        private int _totalPages;
        private const int YearsPerPage = 30;
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the PaymentYearsSelectionDialogViewModel class.
        /// </summary>
        /// <param name="startYear">The starting year for the range</param>
        /// <param name="endYear">The ending year for the range</param>
        public PaymentYearsSelectionDialogViewModel(int startYear, int endYear)
        {
            // Initialize year range
            _allYears = new List<PaymentYearModel>();
            for (int year = startYear; year <= endYear; year++)
            {
                _allYears.Add(new PaymentYearModel(year));
            }

            // Initialize collections
            _currentPageYears = new ObservableCollection<PaymentYearModel>();

            // Initialize properties
            _selectedTab = "G12";
            _currentPage = 0;
            _totalPages = (int)Math.Ceiling((double)_allYears.Count / YearsPerPage);

            // Initialize commands
            PreviousPageCommand = new RelayCommand(ExecutePreviousPage, CanExecutePreviousPage);
            NextPageCommand = new RelayCommand(ExecuteNextPage, CanExecuteNextPage);
            SwitchTabCommand = new RelayCommand<string>(ExecuteSwitchTab);

            // Initialize BaseViewModel
            OnInitialize();

            // Load first page
            LoadCurrentPage();

            LoggingService.LogInfo($"PaymentYearsSelectionDialogViewModel initialized for years {startYear}-{endYear}", GetType().Name);
        }
        #endregion

        #region Properties
        /// <summary>
        /// Gets the collection of years for the current page.
        /// </summary>
        public ObservableCollection<PaymentYearModel> CurrentPageYears
        {
            get => _currentPageYears;
            private set => SetProperty(ref _currentPageYears, value);
        }

        /// <summary>
        /// Gets or sets the currently selected tab (G12 or BIS).
        /// </summary>
        public string SelectedTab
        {
            get => _selectedTab;
            set
            {
                if (SetProperty(ref _selectedTab, value))
                {
                    UpdateCurrentTabForAllYears();
                    LoadCurrentPage();
                }
            }
        }

        /// <summary>
        /// Gets the current page number (0-based).
        /// </summary>
        public int CurrentPage
        {
            get => _currentPage;
            private set => SetProperty(ref _currentPage, value);
        }

        /// <summary>
        /// Gets the total number of pages.
        /// </summary>
        public int TotalPages
        {
            get => _totalPages;
            private set => SetProperty(ref _totalPages, value);
        }

        /// <summary>
        /// Gets whether there are multiple pages.
        /// </summary>
        public bool HasMultiplePages => TotalPages > 1;

        /// <summary>
        /// Gets the page information text.
        /// </summary>
        public string PageInfo => $"{CurrentPage + 1} / {TotalPages}";
        #endregion

        #region Commands
        /// <summary>
        /// Command to navigate to the previous page.
        /// </summary>
        public ICommand PreviousPageCommand { get; }

        /// <summary>
        /// Command to navigate to the next page.
        /// </summary>
        public ICommand NextPageCommand { get; }

        /// <summary>
        /// Command to switch between G12 and BIS tabs.
        /// </summary>
        public ICommand SwitchTabCommand { get; }
        #endregion

        #region Command Implementations
        private void ExecutePreviousPage()
        {
            if (CanExecutePreviousPage())
            {
                CurrentPage--;
                LoadCurrentPage();
            }
        }

        private bool CanExecutePreviousPage()
        {
            return CurrentPage > 0;
        }

        private void ExecuteNextPage()
        {
            if (CanExecuteNextPage())
            {
                CurrentPage++;
                LoadCurrentPage();
            }
        }

        private bool CanExecuteNextPage()
        {
            return CurrentPage < TotalPages - 1;
        }

        private void ExecuteSwitchTab(string? tabName)
        {
            if (!string.IsNullOrEmpty(tabName))
            {
                SelectedTab = tabName;
            }
        }
        #endregion

        #region Methods
        /// <summary>
        /// Updates the CurrentTab property for all years to match the selected tab.
        /// </summary>
        private void UpdateCurrentTabForAllYears()
        {
            foreach (var year in _allYears)
            {
                year.CurrentTab = SelectedTab;
                year.OnPropertyChanged(nameof(year.IsCurrentTabSelected));
            }
        }

        /// <summary>
        /// Loads the years for the current page based on selected tab.
        /// </summary>
        private void LoadCurrentPage()
        {
            var startIndex = CurrentPage * YearsPerPage;
            var pageYears = _allYears.Skip(startIndex).Take(YearsPerPage).ToList();

            CurrentPageYears.Clear();
            foreach (var year in pageYears)
            {
                year.CurrentTab = SelectedTab; // Ensure current tab is set
                year.OnPropertyChanged(nameof(year.IsCurrentTabSelected)); // Notify binding update
                CurrentPageYears.Add(year);
            }

            // Notify command state changes
            OnPropertyChanged(nameof(PageInfo));
            ((RelayCommand)PreviousPageCommand).RaiseCanExecuteChanged();
            ((RelayCommand)NextPageCommand).RaiseCanExecuteChanged();
        }

        /// <summary>
        /// Gets the selected years for the specified tab.
        /// </summary>
        /// <param name="tabName">The tab name (G12 or BIS)</param>
        /// <returns>List of selected years</returns>
        public List<int> GetSelectedYears(string tabName)
        {
            if (tabName == "G12")
            {
                return _allYears.Where(y => y.IsSelectedG12).Select(y => y.Year).OrderByDescending(y => y).ToList();
            }
            else if (tabName == "BIS")
            {
                return _allYears.Where(y => y.IsSelectedBIS).Select(y => y.Year).OrderByDescending(y => y).ToList();
            }
            return new List<int>();
        }

        /// <summary>
        /// Sets the selected years for the specified tab.
        /// </summary>
        /// <param name="tabName">The tab name (G12 or BIS)</param>
        /// <param name="selectedYears">List of years to select</param>
        public void SetSelectedYears(string tabName, List<int> selectedYears)
        {
            if (selectedYears == null) return;

            foreach (var year in _allYears)
            {
                if (tabName == "G12")
                {
                    year.IsSelectedG12 = selectedYears.Contains(year.Year);
                }
                else if (tabName == "BIS")
                {
                    year.IsSelectedBIS = selectedYears.Contains(year.Year);
                }
            }
        }

        /// <summary>
        /// Clears all selections for both tabs.
        /// </summary>
        public void ClearAllSelections()
        {
            foreach (var year in _allYears)
            {
                year.ClearSelections();
            }
        }
        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes resources and cleans up collections.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    // Clear collections to prevent memory leaks
                    _currentPageYears?.Clear();
                    _allYears?.Clear();

                    LoggingService.LogInfo("PaymentYearsSelectionDialogViewModel disposed", GetType().Name);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disposing PaymentYearsSelectionDialogViewModel: {ex.Message}", GetType().Name);
            }
            finally
            {
                // Call base disposal
                base.Dispose(disposing);
            }
        }

        #endregion
    }
}
