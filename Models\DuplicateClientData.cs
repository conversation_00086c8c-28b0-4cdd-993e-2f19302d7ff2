using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;

namespace UFU2.Models
{
    /// <summary>
    /// Data model for duplicate client detection dialog.
    /// Contains client information with resolved activity description and phone numbers.
    /// Supports data binding and display formatting for the duplicate detection UI.
    /// </summary>
    public class DuplicateClientData : INotifyPropertyChanged
    {
        #region Private Fields

        private string _clientUid = string.Empty;
        private string _nameFr = string.Empty;
        private string? _nameAr;
        private string? _birthDate;
        private string? _birthPlace;
        private int _gender;
        private string? _address;
        private string? _nationalId;
        private string _createdAt = string.Empty;
        private string _activityDescription = string.Empty;
        private List<PhoneNumberData> _phoneNumbers = new List<PhoneNumberData>();

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the unique client identifier.
        /// </summary>
        public string ClientUid
        {
            get => _clientUid;
            set => SetProperty(ref _clientUid, value);
        }

        /// <summary>
        /// Gets or sets the French name of the client.
        /// </summary>
        public string NameFr
        {
            get => _nameFr;
            set => SetProperty(ref _nameFr, value);
        }

        /// <summary>
        /// Gets or sets the Arabic name of the client.
        /// </summary>
        public string? NameAr
        {
            get => _nameAr;
            set => SetProperty(ref _nameAr, value);
        }

        /// <summary>
        /// Gets or sets the birth date.
        /// </summary>
        public string? BirthDate
        {
            get => _birthDate;
            set => SetProperty(ref _birthDate, value);
        }

        /// <summary>
        /// Gets or sets the birth place.
        /// </summary>
        public string? BirthPlace
        {
            get => _birthPlace;
            set => SetProperty(ref _birthPlace, value);
        }

        /// <summary>
        /// Gets or sets the gender (0 = Male, 1 = Female).
        /// </summary>
        public int Gender
        {
            get => _gender;
            set => SetProperty(ref _gender, value);
        }

        /// <summary>
        /// Gets or sets the address.
        /// </summary>
        public string? Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        /// <summary>
        /// Gets or sets the national ID.
        /// </summary>
        public string? NationalId
        {
            get => _nationalId;
            set => SetProperty(ref _nationalId, value);
        }

        /// <summary>
        /// Gets or sets the creation timestamp.
        /// </summary>
        public string CreatedAt
        {
            get => _createdAt;
            set => SetProperty(ref _createdAt, value);
        }

        /// <summary>
        /// Gets or sets the resolved activity description.
        /// </summary>
        public string ActivityDescription
        {
            get => _activityDescription;
            set => SetProperty(ref _activityDescription, value);
        }

        /// <summary>
        /// Gets or sets the list of phone numbers associated with this client.
        /// </summary>
        public List<PhoneNumberData> PhoneNumbers
        {
            get => _phoneNumbers;
            set => SetProperty(ref _phoneNumbers, value);
        }

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the display text for the duplicate client dialog.
        /// Format: {ClientUid} - {NameFr} - {ActivityDescription} \n {CreatedAt}
        /// </summary>
        public string DisplayText
        {
            get
            {
                var activityText = string.IsNullOrWhiteSpace(ActivityDescription) ? "نشاط غير محدد" : ActivityDescription;
                return $"{ClientUid} - {NameFr} - {activityText}\n{CreatedAt}";
            }
        }

        /// <summary>
        /// Gets the primary phone number for this client.
        /// </summary>
        public string? PrimaryPhoneNumber
        {
            get
            {
                var primaryPhone = PhoneNumbers?.FirstOrDefault(p => p.IsPrimary);
                return primaryPhone?.PhoneNumber ?? PhoneNumbers?.FirstOrDefault()?.PhoneNumber;
            }
        }

        /// <summary>
        /// Gets the gender display text in Arabic.
        /// </summary>
        public string GenderDisplayText
        {
            get => Gender == 1 ? "أنثى" : "ذكر";
        }

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the DuplicateClientData class.
        /// </summary>
        public DuplicateClientData()
        {
            PhoneNumbers = new List<PhoneNumberData>();
        }

        /// <summary>
        /// Initializes a new instance of the DuplicateClientData class with specified values.
        /// </summary>
        /// <param name="clientUid">The client UID</param>
        /// <param name="nameFr">The French name</param>
        /// <param name="activityDescription">The activity description</param>
        /// <param name="createdAt">The creation timestamp</param>
        public DuplicateClientData(string clientUid, string nameFr, string activityDescription, string createdAt)
        {
            ClientUid = clientUid ?? string.Empty;
            NameFr = nameFr ?? string.Empty;
            ActivityDescription = activityDescription ?? string.Empty;
            CreatedAt = createdAt ?? string.Empty;
            PhoneNumbers = new List<PhoneNumberData>();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Creates a copy of the duplicate client data.
        /// </summary>
        /// <returns>A new DuplicateClientData instance with the same values</returns>
        public DuplicateClientData Clone()
        {
            return new DuplicateClientData
            {
                ClientUid = this.ClientUid,
                NameFr = this.NameFr,
                NameAr = this.NameAr,
                BirthDate = this.BirthDate,
                BirthPlace = this.BirthPlace,
                Gender = this.Gender,
                Address = this.Address,
                NationalId = this.NationalId,
                CreatedAt = this.CreatedAt,
                ActivityDescription = this.ActivityDescription,
                PhoneNumbers = this.PhoneNumbers?.Select(p => p.Clone()).ToList() ?? new List<PhoneNumberData>()
            };
        }

        /// <summary>
        /// Returns a string representation of the duplicate client data.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return DisplayText;
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Occurs when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Sets the property value and raises PropertyChanged if the value has changed.
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">The new value</param>
        /// <param name="propertyName">Name of the property (automatically provided)</param>
        /// <returns>True if the property was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);

            // Notify dependent properties when relevant properties change
            if (propertyName == nameof(ClientUid) || propertyName == nameof(NameFr) || 
                propertyName == nameof(ActivityDescription) || propertyName == nameof(CreatedAt))
            {
                OnPropertyChanged(nameof(DisplayText));
            }

            if (propertyName == nameof(Gender))
            {
                OnPropertyChanged(nameof(GenderDisplayText));
            }

            if (propertyName == nameof(PhoneNumbers))
            {
                OnPropertyChanged(nameof(PrimaryPhoneNumber));
            }

            return true;
        }

        /// <summary>
        /// Raises the PropertyChanged event.
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
