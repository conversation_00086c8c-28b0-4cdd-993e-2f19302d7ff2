<UserControl
    x:Class="UFU2.Views.Dialogs.CraftSearchDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    Width="620"
    Height="500"
    FlowDirection="RightToLeft">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  String to Visibility Converter  -->
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <materialDesign:DialogHost
        x:Name="CraftSearchDialogHost"
        DialogTheme="Inherit"
        Identifier="CraftSearchDialogHost">

        <materialDesign:Card
            Width="620"
            Height="500"
            Padding="0"
            materialDesign:ElevationAssist.Elevation="Dp8"
            Style="{StaticResource DialogBaseCardStyle}">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Header  -->
                <materialDesign:Card
                    Grid.Row="0"
                    Margin="0"
                    Style="{DynamicResource HeaderCardStyle}">
                    <TextBlock
                        HorizontalAlignment="Center"
                        Style="{StaticResource HeadlineStyle}"
                        Text="البحث عن أنواع الحرف" />
                </materialDesign:Card>

                <!--  Search Section  -->
                <StackPanel Grid.Row="1" Margin="12,16">
                    <TextBox
                        x:Name="SearchTextBox"
                        materialDesign:HintAssist.Hint="ابحث عن نوع الحرفة..."
                        materialDesign:TextFieldAssist.HasClearButton="True"
                        Style="{StaticResource UnderlineTextBoxStyle}"
                        TextChanged="SearchTextBox_TextChanged"
                        ToolTip="بحث بالرمز تحت تنسيق (xx-xx-xxx)" />
                    <TextBlock
                        Margin="12,8,0,0"
                        Style="{StaticResource BodyTextStyle}"
                        Text="يمكنك البحث بالرمز أو الوصف" />
                </StackPanel>

                <!--  Results List  -->
                <materialDesign:Card
                    Grid.Row="2"
                    Margin="24,0,24,16"
                    Style="{StaticResource ContentCardStyle}">
                    <ListView
                        x:Name="ResultsListView"
                        materialDesign:ListViewAssist.ListViewItemPadding="12,8"
                        ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectionChanged="ResultsListView_SelectionChanged">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <TextBlock
                                        Margin="0,0,12,0"
                                        Padding="8,4"
                                        VerticalAlignment="Top"
                                        FontFamily="Consolas"
                                        Style="{StaticResource LabelTextStyle}"
                                        Text="{Binding Code}" />

                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                        <TextBlock
                                            Style="{DynamicResource BodyTextStyle}"
                                            Text="{Binding Description}"
                                            TextTrimming="CharacterEllipsis" />
                                        <TextBlock
                                            Foreground="{DynamicResource MaterialDesignBodyLight}"
                                            Style="{DynamicResource LabelTextStyle}"
                                            Text="{Binding Content}"
                                            TextTrimming="CharacterEllipsis"
                                            Visibility="{Binding Content, Converter={StaticResource StringToVisibilityConverter}}" />
                                    </StackPanel>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </materialDesign:Card>

                <!--  Action Buttons  -->
                <StackPanel
                    Grid.Row="3"
                    Margin="24,0,24,16"
                    HorizontalAlignment="Right"
                    Orientation="Horizontal">

                    <Button
                        x:Name="CancelButton"
                        Margin="0,0,8,0"
                        Click="CancelButton_Click"
                        Content="إلغاء"
                        Style="{StaticResource SecondaryButtonStyle}" />
                    <Button
                        x:Name="SelectButton"
                        Click="SelectButton_Click"
                        Content="اختيار"
                        IsEnabled="False"
                        Style="{StaticResource PrimaryButtonStyle}" />
                </StackPanel>
                <Button
                    x:Name="AddNewButton"
                    Grid.Column="1"
                    Width="36"
                    Height="36"
                    Margin="12,0"
                    Padding="0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Panel.ZIndex="1"
                    Click="AddNewButton_Click"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="إضافة نوع حرفة جديد للقاعدة البايانات">
                    <materialDesign:PackIcon
                        Width="20"
                        Height="20"
                        Kind="Plus" />
                </Button>
            </Grid>
        </materialDesign:Card>
    </materialDesign:DialogHost>
</UserControl>
