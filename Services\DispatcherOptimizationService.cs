using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Priority levels for Dispatcher operations in UFU2.
    /// Determines batching behavior and execution timing for optimal UI responsiveness.
    /// </summary>
    public enum DispatcherOperationPriority
    {
        /// <summary>Background priority - can be delayed for better performance</summary>
        Background = 0,
        /// <summary>Normal priority - standard UI updates</summary>
        Normal = 1,
        /// <summary>High priority - important UI updates that need quick execution</summary>
        High = 2,
        /// <summary>Critical priority - immediate execution required</summary>
        Critical = 3
    }

    /// <summary>
    /// UFU2 Dispatcher optimization service for batching UI thread operations and monitoring performance.
    /// Provides intelligent Dispatcher call batching, priority-based UI updates, and UI thread monitoring
    /// to improve UFU2 client data management responsiveness and prevent UI thread blocking.
    /// 
    /// Features:
    /// - Smart Dispatcher call batching with priority levels
    /// - UI thread blocking detection and monitoring
    /// - Cross-thread property update optimization
    /// - Performance metrics collection and analysis
    /// - Arabic RTL UI compatibility with MaterialDesign themes
    /// - Integration with UFU2 ServiceLocator and logging infrastructure
    /// </summary>
    public class DispatcherOptimizationService : IDisposable
    {
        #region Constants

        // Batching intervals for different priority levels
        private const int BackgroundBatchIntervalMs = 50; // 20 FPS for background operations
        private const int NormalBatchIntervalMs = 16; // 60 FPS for normal operations
        private const int HighPriorityBatchIntervalMs = 8; // 120 FPS for high priority operations
        private const int MaxBatchSize = 100; // Maximum operations per batch

        // UI thread monitoring constants
        private const int UIThreadMonitoringIntervalMs = 1000; // Check UI thread every second
        private const int UIThreadBlockingThresholdMs = 100; // Consider blocked if operation takes > 100ms
        private const int UIThreadCriticalBlockingThresholdMs = 500; // Critical blocking threshold

        #endregion

        #region Private Fields

        // Dispatcher batching infrastructure
        private readonly Dispatcher _dispatcher;
        private readonly Dictionary<DispatcherOperationPriority, ConcurrentQueue<DispatcherOperation>> _operationQueues;
        private readonly Dictionary<DispatcherOperationPriority, DispatcherTimer> _batchTimers;
        private readonly object _batchLock = new object();
        private bool _disposed = false;

        // UI thread monitoring
        private readonly DispatcherTimer _uiThreadMonitorTimer;
        private readonly Stopwatch _uiThreadStopwatch = new();
        private readonly Queue<double> _recentExecutionTimes = new();
        private bool _isUIThreadBlocked = false;
        private DateTime _lastUIThreadCheck = DateTime.UtcNow;

        // Performance tracking
        private readonly Dictionary<DispatcherOperationPriority, int> _operationCounts = new();
        private readonly Dictionary<DispatcherOperationPriority, int> _batchedOperationCounts = new();
        private readonly Dictionary<DispatcherOperationPriority, double> _averageExecutionTimes = new();
        private int _totalUIThreadBlockingEvents = 0;
        private double _totalUIThreadBlockingTime = 0;

        #endregion

        #region Constructor and Initialization

        /// <summary>
        /// Initializes a new instance of the DispatcherOptimizationService.
        /// Sets up batching timers and UI thread monitoring for optimal UFU2 performance.
        /// </summary>
        public DispatcherOptimizationService()
        {
            _dispatcher = Dispatcher.CurrentDispatcher ?? System.Windows.Application.Current?.Dispatcher 
                ?? throw new InvalidOperationException("No Dispatcher available for DispatcherOptimizationService");

            // Initialize operation queues and counters
            _operationQueues = new Dictionary<DispatcherOperationPriority, ConcurrentQueue<DispatcherOperation>>();
            _batchTimers = new Dictionary<DispatcherOperationPriority, DispatcherTimer>();

            foreach (DispatcherOperationPriority priority in Enum.GetValues<DispatcherOperationPriority>())
            {
                _operationQueues[priority] = new ConcurrentQueue<DispatcherOperation>();
                _operationCounts[priority] = 0;
                _batchedOperationCounts[priority] = 0;
                _averageExecutionTimes[priority] = 0;

                // Don't create timers for Critical priority (immediate execution)
                if (priority != DispatcherOperationPriority.Critical)
                {
                    _batchTimers[priority] = CreateBatchTimer(priority);
                }
            }

            // Initialize UI thread monitoring
            _uiThreadMonitorTimer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromMilliseconds(UIThreadMonitoringIntervalMs)
            };
            _uiThreadMonitorTimer.Tick += OnUIThreadMonitorTick;
            _uiThreadMonitorTimer.Start();

            LoggingService.LogDebug("DispatcherOptimizationService initialized with smart batching and UI thread monitoring", "DispatcherOptimizationService");
        }

        /// <summary>
        /// Creates a batch timer for the specified priority level.
        /// </summary>
        /// <param name="priority">Priority level for the timer</param>
        /// <returns>Configured DispatcherTimer</returns>
        private DispatcherTimer CreateBatchTimer(DispatcherOperationPriority priority)
        {
            var interval = priority switch
            {
                DispatcherOperationPriority.Background => BackgroundBatchIntervalMs,
                DispatcherOperationPriority.Normal => NormalBatchIntervalMs,
                DispatcherOperationPriority.High => HighPriorityBatchIntervalMs,
                _ => NormalBatchIntervalMs
            };

            var timer = new DispatcherTimer(GetDispatcherPriority(priority))
            {
                Interval = TimeSpan.FromMilliseconds(interval)
            };

            timer.Tick += (sender, e) => ProcessBatchedOperations(priority);
            return timer;
        }

        /// <summary>
        /// Maps UFU2 operation priority to WPF DispatcherPriority.
        /// </summary>
        /// <param name="priority">UFU2 operation priority</param>
        /// <returns>Corresponding WPF DispatcherPriority</returns>
        private static DispatcherPriority GetDispatcherPriority(DispatcherOperationPriority priority)
        {
            return priority switch
            {
                DispatcherOperationPriority.Background => DispatcherPriority.Background,
                DispatcherOperationPriority.Normal => DispatcherPriority.Normal,
                DispatcherOperationPriority.High => DispatcherPriority.Loaded,
                DispatcherOperationPriority.Critical => DispatcherPriority.Send,
                _ => DispatcherPriority.Normal
            };
        }

        #endregion

        #region Public API

        /// <summary>
        /// Executes an action on the UI thread with smart batching optimization.
        /// Provides priority-based execution and automatic batching for improved performance.
        /// </summary>
        /// <param name="action">Action to execute on the UI thread</param>
        /// <param name="priority">Priority level for the operation</param>
        /// <returns>Task representing the operation completion</returns>
        public Task InvokeAsync(Action action, DispatcherOperationPriority priority = DispatcherOperationPriority.Normal)
        {
            if (action == null) throw new ArgumentNullException(nameof(action));
            if (_disposed) throw new ObjectDisposedException(nameof(DispatcherOptimizationService));

            // Critical priority operations execute immediately
            if (priority == DispatcherOperationPriority.Critical)
            {
                return ExecuteImmediateAsync(action, priority);
            }

            // Check if we're already on the UI thread
            if (_dispatcher.CheckAccess())
            {
                return ExecuteImmediateAsync(action, priority);
            }

            // Add to batching queue for cross-thread operations
            return EnqueueBatchedOperation(action, priority);
        }

        /// <summary>
        /// Executes an action on the UI thread immediately without batching.
        /// Use for critical operations that require immediate execution.
        /// </summary>
        /// <param name="action">Action to execute immediately</param>
        /// <returns>Task representing the operation completion</returns>
        public Task InvokeImmediateAsync(Action action)
        {
            return InvokeAsync(action, DispatcherOperationPriority.Critical);
        }

        #endregion

        #region Private Implementation

        /// <summary>
        /// Executes an action immediately on the UI thread with performance monitoring.
        /// </summary>
        /// <param name="action">Action to execute</param>
        /// <param name="priority">Priority level for metrics</param>
        /// <returns>Task representing the operation completion</returns>
        private Task ExecuteImmediateAsync(Action action, DispatcherOperationPriority priority)
        {
            var tcs = new TaskCompletionSource<bool>();

            var operation = _dispatcher.BeginInvoke(new Action(() =>
            {
                var stopwatch = Stopwatch.StartNew();
                try
                {
                    action();
                    tcs.SetResult(true);
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error executing immediate Dispatcher operation: {ex.Message}", "DispatcherOptimizationService");
                    tcs.SetException(ex);
                }
                finally
                {
                    stopwatch.Stop();
                    UpdateExecutionMetrics(priority, stopwatch.ElapsedMilliseconds);
                }
            }), GetDispatcherPriority(priority));

            return tcs.Task;
        }

        /// <summary>
        /// Enqueues an operation for batched execution.
        /// </summary>
        /// <param name="action">Action to enqueue</param>
        /// <param name="priority">Priority level</param>
        /// <returns>Task representing the operation completion</returns>
        private Task EnqueueBatchedOperation(Action action, DispatcherOperationPriority priority)
        {
            var operation = new DispatcherOperation
            {
                Action = action,
                Priority = priority,
                QueuedTime = DateTime.UtcNow,
                CompletionSource = new TaskCompletionSource<bool>()
            };

            _operationQueues[priority].Enqueue(operation);
            _operationCounts[priority]++;

            // Start the appropriate batch timer
            lock (_batchLock)
            {
                if (_batchTimers.ContainsKey(priority) && !_batchTimers[priority].IsEnabled)
                {
                    _batchTimers[priority].Start();
                }

                // Check if we should flush immediately due to batch size
                if (_operationQueues[priority].Count >= MaxBatchSize)
                {
                    LoggingService.LogDebug($"Large batch detected for {priority} priority ({_operationQueues[priority].Count} operations), flushing immediately", "DispatcherOptimizationService");
                    ProcessBatchedOperations(priority);
                }
            }

            return operation.CompletionSource.Task;
        }

        /// <summary>
        /// Processes batched operations for the specified priority level.
        /// </summary>
        /// <param name="priority">Priority level to process</param>
        private void ProcessBatchedOperations(DispatcherOperationPriority priority)
        {
            if (!_batchTimers.ContainsKey(priority)) return;

            _batchTimers[priority].Stop();

            var operations = new List<DispatcherOperation>();

            // Collect all pending operations
            while (_operationQueues[priority].TryDequeue(out var operation))
            {
                operations.Add(operation);
            }

            if (operations.Count == 0) return;

            _batchedOperationCounts[priority] += operations.Count;

            // Execute all operations in a single Dispatcher call
            var batchStopwatch = Stopwatch.StartNew();
            _dispatcher.BeginInvoke(new Action(() =>
            {
                foreach (var operation in operations)
                {
                    var operationStopwatch = Stopwatch.StartNew();
                    try
                    {
                        operation.Action();
                        operation.CompletionSource.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error executing batched Dispatcher operation: {ex.Message}", "DispatcherOptimizationService");
                        operation.CompletionSource.SetException(ex);
                    }
                    finally
                    {
                        operationStopwatch.Stop();
                        UpdateExecutionMetrics(priority, operationStopwatch.ElapsedMilliseconds);
                    }
                }

                batchStopwatch.Stop();
                LoggingService.LogDebug($"Processed {operations.Count} {priority} priority operations in {batchStopwatch.ElapsedMilliseconds}ms", "DispatcherOptimizationService");
            }), GetDispatcherPriority(priority));
        }

        /// <summary>
        /// Updates execution metrics for performance monitoring.
        /// </summary>
        /// <param name="priority">Priority level</param>
        /// <param name="executionTimeMs">Execution time in milliseconds</param>
        private void UpdateExecutionMetrics(DispatcherOperationPriority priority, double executionTimeMs)
        {
            // Update average execution time using exponential moving average
            _averageExecutionTimes[priority] = _averageExecutionTimes[priority] * 0.9 + executionTimeMs * 0.1;

            // Track UI thread blocking
            if (executionTimeMs > UIThreadBlockingThresholdMs)
            {
                _totalUIThreadBlockingEvents++;
                _totalUIThreadBlockingTime += executionTimeMs;

                if (executionTimeMs > UIThreadCriticalBlockingThresholdMs)
                {
                    LoggingService.LogWarning($"Critical UI thread blocking detected: {executionTimeMs:F1}ms for {priority} priority operation", "DispatcherOptimizationService");
                }
            }
        }

        /// <summary>
        /// Handles UI thread monitoring timer tick.
        /// </summary>
        private void OnUIThreadMonitorTick(object? sender, EventArgs e)
        {
            try
            {
                MonitorUIThreadPerformance();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during UI thread monitoring: {ex.Message}", "DispatcherOptimizationService");
            }
        }

        /// <summary>
        /// Monitors UI thread performance and detects blocking conditions.
        /// </summary>
        private void MonitorUIThreadPerformance()
        {
            var now = DateTime.UtcNow;
            var timeSinceLastCheck = (now - _lastUIThreadCheck).TotalMilliseconds;
            _lastUIThreadCheck = now;

            // Check if UI thread is responsive
            var responsiveCheckStopwatch = Stopwatch.StartNew();
            _dispatcher.Invoke(() => { /* Empty operation to test responsiveness */ }, DispatcherPriority.Normal);
            responsiveCheckStopwatch.Stop();

            var responseTime = responsiveCheckStopwatch.ElapsedMilliseconds;
            _recentExecutionTimes.Enqueue(responseTime);

            // Keep only recent measurements (last 10 checks)
            while (_recentExecutionTimes.Count > 10)
            {
                _recentExecutionTimes.Dequeue();
            }

            // Determine if UI thread is blocked
            var averageResponseTime = _recentExecutionTimes.Average();
            var wasBlocked = _isUIThreadBlocked;
            _isUIThreadBlocked = averageResponseTime > UIThreadBlockingThresholdMs;

            // Log state changes
            if (_isUIThreadBlocked && !wasBlocked)
            {
                LoggingService.LogWarning($"UI thread blocking detected - Average response time: {averageResponseTime:F1}ms", "DispatcherOptimizationService");
            }
            else if (!_isUIThreadBlocked && wasBlocked)
            {
                LoggingService.LogDebug($"UI thread responsiveness restored - Average response time: {averageResponseTime:F1}ms", "DispatcherOptimizationService");
            }
        }

        #endregion

        #region Performance Monitoring and Metrics

        /// <summary>
        /// Gets current performance statistics for the Dispatcher optimization service.
        /// </summary>
        /// <returns>Performance information</returns>
        public DispatcherOptimizationPerformanceInfo GetPerformanceInfo()
        {
            lock (_batchLock)
            {
                return new DispatcherOptimizationPerformanceInfo
                {
                    IsUIThreadBlocked = _isUIThreadBlocked,
                    AverageUIThreadResponseTime = _recentExecutionTimes.Count > 0 ? _recentExecutionTimes.Average() : 0,
                    TotalUIThreadBlockingEvents = _totalUIThreadBlockingEvents,
                    TotalUIThreadBlockingTime = _totalUIThreadBlockingTime,
                    OperationCounts = new Dictionary<DispatcherOperationPriority, int>(_operationCounts),
                    BatchedOperationCounts = new Dictionary<DispatcherOperationPriority, int>(_batchedOperationCounts),
                    AverageExecutionTimes = new Dictionary<DispatcherOperationPriority, double>(_averageExecutionTimes),
                    PendingOperationCounts = _operationQueues.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Count),
                    ActiveTimerCounts = _batchTimers.Count(kvp => kvp.Value.IsEnabled)
                };
            }
        }

        /// <summary>
        /// Flushes all pending batched operations immediately.
        /// Useful for ensuring all operations complete before application shutdown.
        /// </summary>
        public void FlushAllPendingOperations()
        {
            foreach (var priority in _operationQueues.Keys)
            {
                if (priority != DispatcherOperationPriority.Critical)
                {
                    ProcessBatchedOperations(priority);
                }
            }
        }

        /// <summary>
        /// Logs current performance statistics.
        /// </summary>
        public void LogPerformanceStatistics()
        {
            var perfInfo = GetPerformanceInfo();

            var totalOperations = perfInfo.OperationCounts.Values.Sum();
            var totalBatchedOperations = perfInfo.BatchedOperationCounts.Values.Sum();
            var batchingEfficiency = totalOperations > 0 ? (totalBatchedOperations * 100.0) / totalOperations : 0;

            LoggingService.LogDebug(
                $"Dispatcher Optimization Performance - " +
                $"Total Operations: {totalOperations}, Batched: {totalBatchedOperations}, " +
                $"Batching Efficiency: {batchingEfficiency:F1}%, " +
                $"UI Thread Blocked: {perfInfo.IsUIThreadBlocked}, " +
                $"Avg Response Time: {perfInfo.AverageUIThreadResponseTime:F1}ms, " +
                $"Blocking Events: {perfInfo.TotalUIThreadBlockingEvents}, " +
                $"Active Timers: {perfInfo.ActiveTimerCounts}",
                "DispatcherOptimizationService");
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the DispatcherOptimizationService and cleans up resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method for proper disposal pattern.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // Flush all pending operations
                    FlushAllPendingOperations();

                    // Stop and dispose timers
                    _uiThreadMonitorTimer?.Stop();
                    foreach (var timer in _batchTimers.Values)
                    {
                        timer?.Stop();
                    }

                    LoggingService.LogDebug("DispatcherOptimizationService disposed successfully", "DispatcherOptimizationService");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error during DispatcherOptimizationService disposal: {ex.Message}", "DispatcherOptimizationService");
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// Performance information for the Dispatcher optimization service.
    /// </summary>
    public class DispatcherOptimizationPerformanceInfo
    {
        public bool IsUIThreadBlocked { get; set; }
        public double AverageUIThreadResponseTime { get; set; }
        public int TotalUIThreadBlockingEvents { get; set; }
        public double TotalUIThreadBlockingTime { get; set; }
        public Dictionary<DispatcherOperationPriority, int> OperationCounts { get; set; } = new();
        public Dictionary<DispatcherOperationPriority, int> BatchedOperationCounts { get; set; } = new();
        public Dictionary<DispatcherOperationPriority, double> AverageExecutionTimes { get; set; } = new();
        public Dictionary<DispatcherOperationPriority, int> PendingOperationCounts { get; set; } = new();
        public int ActiveTimerCounts { get; set; }

        public override string ToString()
        {
            var totalOps = OperationCounts.Values.Sum();
            var totalBatched = BatchedOperationCounts.Values.Sum();
            var efficiency = totalOps > 0 ? (totalBatched * 100.0) / totalOps : 0;

            return $"Dispatcher Optimization - Operations: {totalOps}, Batched: {totalBatched}, " +
                   $"Efficiency: {efficiency:F1}%, UI Blocked: {IsUIThreadBlocked}, " +
                   $"Response Time: {AverageUIThreadResponseTime:F1}ms, " +
                   $"Blocking Events: {TotalUIThreadBlockingEvents}";
        }
    }

    /// <summary>
    /// Represents a batched Dispatcher operation with priority and timing information.
    /// </summary>
    internal class DispatcherOperation
    {
        public Action Action { get; set; } = null!;
        public DispatcherOperationPriority Priority { get; set; }
        public DateTime QueuedTime { get; set; }
        public TaskCompletionSource<bool> CompletionSource { get; set; } = null!;
    }
}
