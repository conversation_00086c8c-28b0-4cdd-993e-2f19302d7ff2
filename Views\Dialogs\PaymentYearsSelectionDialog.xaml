<UserControl
    x:Class="UFU2.Views.Dialogs.PaymentYearsSelectionDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    Width="450"
    Height="360"
    Loaded="UserControl_Loaded"
    UseLayoutRounding="True">

    <UserControl.Resources>
        <!--  Built-in Converters  -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    </UserControl.Resources>

    <materialDesign:Card Padding="0" Style="{StaticResource DialogBaseCardStyle}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <!--  Header section  -->
            <materialDesign:Card
                Grid.Row="0"
                Margin="0"
                Style="{DynamicResource HeaderCardStyle}">
                <TextBlock
                    HorizontalAlignment="Center"
                    Style="{StaticResource HeadlineStyle}"
                    Text="تحديد سنوات الدفع" />
            </materialDesign:Card>

            <!--  Tab Headers  -->
            <Grid Grid.Row="1" Margin="7,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <RadioButton
                    x:Name="G12Tab"
                    Grid.Column="0"
                    Margin="0,0,4,0"
                    Command="{Binding SwitchTabCommand}"
                    CommandParameter="G12"
                    Content="G12"
                    GroupName="PaymentTabs"
                    IsChecked="True"
                    Style="{StaticResource TabRadioButtonStyle}" />

                <RadioButton
                    x:Name="BISTab"
                    Grid.Column="1"
                    Margin="4,0,4,0"
                    Command="{Binding SwitchTabCommand}"
                    CommandParameter="BIS"
                    Content="BIS"
                    GroupName="PaymentTabs"
                    Style="{StaticResource TabRadioButtonStyle}" />

                <!--  Pagination Controls  -->
                <Grid Grid.Column="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <Button
                        Grid.Column="0"
                        Width="27"
                        Height="27"
                        Margin="8,0"
                        Padding="0"
                        Command="{Binding NextPageCommand}"
                        Style="{StaticResource SecondaryButtonStyle}"
                        ToolTip="الصفحة التالية">
                        <materialDesign:PackIcon
                            Width="18"
                            Height="18"
                            Kind="ChevronRight" />
                    </Button>

                    <TextBlock
                        Grid.Column="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FlowDirection="LeftToRight"
                        Style="{DynamicResource LabelTextStyle}"
                        Text="{Binding PageInfo}" />

                    <Button
                        Grid.Column="2"
                        Width="27"
                        Height="27"
                        Margin="8,0"
                        Padding="0"
                        Command="{Binding PreviousPageCommand}"
                        Style="{StaticResource SecondaryButtonStyle}"
                        ToolTip="الصفحة السابقة">
                        <materialDesign:PackIcon
                            Width="18"
                            Height="18"
                            Kind="ChevronLeft" />
                    </Button>
                </Grid>
            </Grid>

            <!--  Year Chips Container  -->
            <materialDesign:Card
                Grid.Row="2"
                Margin="7,3"
                Padding="0"
                Style="{StaticResource ContentCardStyle}">
                <ScrollViewer
                    Margin="12,8"
                    HorizontalScrollBarVisibility="Disabled"
                    VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding CurrentPageYears}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel
                                    HorizontalAlignment="Center"
                                    FlowDirection="LeftToRight"
                                    Orientation="Horizontal" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <CheckBox
                                    x:Name="YearChip"
                                    Content="{Binding YearText}"
                                    IsChecked="{Binding IsCurrentTabSelected, Mode=TwoWay}"
                                    Style="{StaticResource PaymentYearChipStyle}" />
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </materialDesign:Card>

            <!--  Footer with Action Buttons  -->
            <userControls:SaveCancelButtonsControl
                Grid.Row="3"
                CancelClick="CloseButton_Click"
                CancelText="إغلاق"
                CancelTooltip="إغلاق النافذة"
                SaveClick="SaveButton_Click"
                SaveTooltip="حفظ السنوات المحددة" />
        </Grid>
    </materialDesign:Card>
</UserControl>
