using System;
using System.Collections.Generic;

namespace UFU2.Models
{
    /// <summary>
    /// Data transfer object for client creation operations.
    /// Contains all necessary information to create a new client with related entities.
    /// Supports flexible data entry with optional fields for incomplete information.
    /// </summary>
    public class ClientCreationData
    {
        /// <summary>
        /// Gets or sets the French name of the client (required).
        /// Used for UID generation and official documentation.
        /// </summary>
        public string NameFr { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Arabic name of the client (optional).
        /// Supports RTL text display and Arabic documentation.
        /// </summary>
        public string? NameAr { get; set; }

        /// <summary>
        /// Gets or sets the birth date in DD/MM/YYYY format (optional).
        /// Supports placeholder format "xx/xx/xxxx" for unknown dates.
        /// </summary>
        public string? BirthDate { get; set; }

        /// <summary>
        /// Gets or sets the birth place (optional).
        /// Can contain Arabic or French text.
        /// </summary>
        public string? BirthPlace { get; set; }

        /// <summary>
        /// Gets or sets the gender (optional).
        /// 0 = Male, 1 = Female, null = Not specified.
        /// </summary>
        public int? Gender { get; set; }

        /// <summary>
        /// Gets or sets the address (optional).
        /// Full address including street, city, and region information.
        /// Supports Arabic RTL text.
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// Gets or sets the national ID number (optional).
        /// Algerian national identification number.
        /// </summary>
        public string? NationalId { get; set; }

        /// <summary>
        /// Gets or sets the list of phone numbers for the client (optional).
        /// Can include multiple phone numbers with different types.
        /// Only one phone number can be marked as primary.
        /// </summary>
        public List<PhoneNumberData>? PhoneNumbers { get; set; }

        /// <summary>
        /// Gets or sets the list of activities for the client (optional).
        /// Can include multiple business activities with complete related data.
        /// </summary>
        public List<ActivityCreationData>? Activities { get; set; }
        /// <summary>
        /// Gets or sets the list of notes for the client (optional).
        /// </summary>
        public List<NoteData>? Notes { get; set; }


        /// <summary>
        /// Initializes a new instance of the ClientCreationData class.
        /// </summary>
        public ClientCreationData()
        {
            PhoneNumbers = new List<PhoneNumberData>();
            Activities = new List<ActivityCreationData>();
        }

        /// <summary>
        /// Validates the client creation data.
        /// </summary>
        /// <returns>True if the data is valid for client creation</returns>
        public bool IsValid()
        {
            // French name is required
            if (string.IsNullOrWhiteSpace(NameFr))
                return false;

            // Validate phone numbers if provided
            if (PhoneNumbers != null)
            {
                var primaryPhones = PhoneNumbers.Where(p => p.IsPrimary).Count();
                if (primaryPhones > 1)
                    return false; // Only one primary phone allowed

                foreach (var phone in PhoneNumbers)
                {
                    if (!phone.IsValid())
                        return false;
                }
            }

            // Validate activities if provided
            if (Activities != null)
            {
                foreach (var activity in Activities)
                {
                    if (!activity.IsValid())
                        return false;
                }
            }

            return true;
        }
    }

    /// <summary>
    /// Data transfer object for client update operations.
    /// Contains fields that can be updated for an existing client.
    /// French name and UID cannot be changed after creation.
    /// </summary>
    public class ClientUpdateData
    {
        /// <summary>
        /// Gets or sets the Arabic name of the client (optional).
        /// </summary>
        public string? NameAr { get; set; }

        /// <summary>
        /// Gets or sets the birth date in DD/MM/YYYY format (optional).
        /// </summary>
        public string? BirthDate { get; set; }

        /// <summary>
        /// Gets or sets the birth place (optional).
        /// </summary>
        public string? BirthPlace { get; set; }

        /// <summary>
        /// Gets or sets the gender (optional).
        /// 0 = Male, 1 = Female, null = Not specified.
        /// </summary>
        public int? Gender { get; set; }

        /// <summary>
        /// Gets or sets the address (optional).
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// Gets or sets the national ID number (optional).
        /// </summary>
        public string? NationalId { get; set; }
    }

    /// <summary>
    /// Data transfer object for complete client data retrieval.
    /// Contains all client information including related entities.
    /// </summary>
    public class ClientData
    {
        /// <summary>
        /// Gets or sets the unique client identifier.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the French name of the client.
        /// </summary>
        public string NameFr { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Arabic name of the client.
        /// </summary>
        public string? NameAr { get; set; }

        /// <summary>
        /// Gets or sets the birth date.
        /// </summary>
        public string? BirthDate { get; set; }

        /// <summary>
        /// Gets or sets the birth place.
        /// </summary>
        public string? BirthPlace { get; set; }

        /// <summary>
        /// Gets or sets the gender.
        /// </summary>
        public int? Gender { get; set; }

        /// <summary>
        /// Gets or sets the address.
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// Gets or sets the national ID number.
        /// </summary>
        public string? NationalId { get; set; }

        /// <summary>
        /// Gets or sets the creation timestamp.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the last update timestamp.
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Gets or sets the list of phone numbers.
        /// </summary>
        public List<PhoneNumberData> PhoneNumbers { get; set; } = new();

        /// <summary>
        /// Gets or sets the list of activities.
        /// </summary>
        public List<ActivityData> Activities { get; set; } = new();
    }
}