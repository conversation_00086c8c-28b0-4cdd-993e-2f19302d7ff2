using System;
using System.Windows;
using System.Windows.Controls;
using UFU2.ViewModels;
using UFU2.Services;
using UFU2.Common;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for AddActivityDialog.xaml
    /// Dialog for adding new activity codes to the ActivityTypeBase database.
    /// Follows UFU2 dialog patterns with MaterialDesign integration.
    /// </summary>
    public partial class AddActivityDialog : UserControl
    {
        #region Private Fields
        private readonly AddActivityDialogViewModel _viewModel;
        private bool _dialogResult = false;
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the AddActivityDialog class.
        /// </summary>
        /// <param name="activityCode">The activity code that was not found</param>
        public AddActivityDialog(string activityCode)
        {
            InitializeComponent();

            // Initialize ViewModel
            _viewModel = new AddActivityDialogViewModel(activityCode);
            DataContext = _viewModel;

            LoggingService.LogDebug($"AddActivityDialog initialized for code: {activityCode}", "AddActivityDialog");
        }
        #endregion

        #region Public Properties
        /// <summary>
        /// Gets the dialog result indicating whether the user saved or cancelled.
        /// </summary>
        public bool DialogResult => _dialogResult;

        /// <summary>
        /// Gets the activity code that was processed.
        /// </summary>
        public string ActivityCode => _viewModel.ActivityCode;

        /// <summary>
        /// Gets the activity description that was entered.
        /// </summary>
        public string ActivityDescription => _viewModel.ActivityDescription;
        #endregion

        #region Event Handlers
        /// <summary>
        /// Handles the Save button click event.
        /// Validates and saves the activity to the database.
        /// </summary>
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug($"Attempting to save new activity: {_viewModel.ActivityCode}", "AddActivityDialog");

                var success = await _viewModel.SaveActivityAsync();

                if (success)
                {
                    _dialogResult = true;
                    LoggingService.LogDebug($"AddActivityDialog saved successfully for code: {_viewModel.ActivityCode}", "AddActivityDialog");

                    // Close dialog with positive result
                    MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(true, null);
                }
                else
                {
                    LoggingService.LogError($"Failed to save activity in AddActivityDialog: {_viewModel.ActivityCode}", "AddActivityDialog");

                    // Use ErrorManager for consistent Arabic RTL error handling
                    ErrorManager.ShowUserErrorToast(
                        "فشل في حفظ النشاط. يرجى المحاولة مرة أخرى.",
                        "خطأ في الحفظ",
                        "AddActivityDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Exception in SaveButton_Click: {ex.Message}", "AddActivityDialog");

                // Use ErrorManager for consistent Arabic RTL error handling with exception logging
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",
                    "خطأ",
                    LogLevel.Error,
                    "AddActivityDialog");
            }
        }

        /// <summary>
        /// Handles the Cancel button click event.
        /// Closes the dialog without saving.
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _dialogResult = false;
                LoggingService.LogDebug($"AddActivityDialog cancelled for code: {_viewModel.ActivityCode}", "AddActivityDialog");

                // Close dialog with negative result
                MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(false, null);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Exception in CancelButton_Click: {ex.Message}", "AddActivityDialog");
            }
        }
        #endregion
    }
}
