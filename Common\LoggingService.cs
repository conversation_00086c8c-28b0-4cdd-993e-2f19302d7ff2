using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;

namespace UFU2.Common
{
    /// <summary>
    /// Enhanced logging service for UFU2 application.
    /// Provides session-based file logging with different log levels while maintaining console output.
    /// Thread-safe operations ensure reliable logging across the application lifecycle.
    /// Includes log level filtering to reduce excessive debug output.
    /// </summary>
    public static class LoggingService
    {
        #region Private Fields

        /// <summary>
        /// Lock object for thread-safe file operations
        /// </summary>
        private static readonly object _fileLock = new object();

        /// <summary>
        /// Current session log file path
        /// </summary>
        private static string? _currentLogFilePath;

        /// <summary>
        /// Session start timestamp for consistent session tracking
        /// </summary>
        private static DateTime _sessionStartTime;

        /// <summary>
        /// Flag indicating if file logging is initialized
        /// </summary>
        private static bool _isFileLoggingInitialized = false;

        /// <summary>
        /// Logs directory path
        /// </summary>
        private static readonly string _logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");

        /// <summary>
        /// Maximum number of log files to maintain in the Logs directory
        /// </summary>
        private const int MaxLogFiles = 15;

        /// <summary>
        /// Minimum log level for filtering. Only logs at or above this level will be written.
        /// </summary>
        private static LogLevel _minimumLogLevel = LogLevel.Debug;

        /// <summary>
        /// Services that should have reduced debug logging to prevent log spam
        /// </summary>
        private static readonly HashSet<string> _reducedLoggingServices = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "TextNormalizationHelper",
            "EnhancedSearchService",
            "FtsSearchService"
        };

        #endregion

        #region Enums

        /// <summary>
        /// Log levels for filtering log output
        /// </summary>
        public enum LogLevel
        {
            Debug = 0,
            Info = 1,
            Warning = 2,
            Error = 3
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Sets the minimum log level for filtering. Only logs at or above this level will be written.
        /// </summary>
        /// <param name="level">Minimum log level</param>
        public static void SetMinimumLogLevel(LogLevel level)
        {
            _minimumLogLevel = level;
            LogInfo($"Log level set to {level}", "LoggingService");
        }

        /// <summary>
        /// Gets the current minimum log level
        /// </summary>
        /// <returns>Current minimum log level</returns>
        public static LogLevel GetMinimumLogLevel()
        {
            return _minimumLogLevel;
        }

        /// <summary>
        /// Initializes session-based file logging
        /// Creates the logs directory and sets up the current session log file
        /// </summary>
        /// <returns>True if initialization was successful, false otherwise</returns>
        public static bool InitializeSessionLogging()
        {
            lock (_fileLock)
            {
                try
                {
                    if (_isFileLoggingInitialized)
                    {
                        return true; // Already initialized
                    }

                    // Record session start time
                    _sessionStartTime = DateTime.Now;

                    // Create logs directory if it doesn't exist
                    if (!Directory.Exists(_logsDirectory))
                    {
                        Directory.CreateDirectory(_logsDirectory);
                    }

                    // Perform log file rotation to maintain maximum file limit
                    PerformLogFileRotation();

                    // Generate log file name with session timestamp
                    var fileName = $"UFU2_App_Log_{_sessionStartTime:yyyy-MM-dd_HH-mm-ss}.log";
                    _currentLogFilePath = Path.Combine(_logsDirectory, fileName);

                    // Write session header
                    var sessionHeader = $"=== UFU2 Application Session Started at {_sessionStartTime:yyyy-MM-dd HH:mm:ss} ===";
                    File.WriteAllText(_currentLogFilePath, sessionHeader + Environment.NewLine);

                    _isFileLoggingInitialized = true;
                    return true;
                }
                catch (Exception ex)
                {
                    // Log to console/debug only if file logging fails
                    var errorMessage = $"Failed to initialize session logging: {ex.Message}";
                    Debug.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[ERROR]\t\t[LoggingService]\t{errorMessage}");
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[ERROR]\t\t[LoggingService]\t{errorMessage}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Finalizes session logging by writing session footer and cleaning up
        /// </summary>
        public static void FinalizeSessionLogging()
        {
            lock (_fileLock)
            {
                try
                {
                    if (_isFileLoggingInitialized && !string.IsNullOrEmpty(_currentLogFilePath))
                    {
                        var sessionEndTime = DateTime.Now;
                        var sessionFooter = $"=== UFU2 Application Session Ended at {sessionEndTime:yyyy-MM-dd HH:mm:ss} ===";

                        File.AppendAllText(_currentLogFilePath, sessionFooter + Environment.NewLine);

                        _isFileLoggingInitialized = false;
                        _currentLogFilePath = null;
                    }
                }
                catch (Exception ex)
                {
                    // Log to console/debug only if file logging fails
                    var errorMessage = $"Failed to finalize session logging: {ex.Message}";
                    Debug.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[ERROR]\t\t[LoggingService]\t{errorMessage}");
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[ERROR]\t\t[LoggingService]\t{errorMessage}");
                }
            }
        }

        /// <summary>
        /// Logs an informational message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message</param>
        public static void LogInfo(string message, string source = "UFU2")
        {
            WriteLogWithLevel("INFO", message, source, LogLevel.Info);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message</param>
        public static void LogWarning(string message, string source = "UFU2")
        {
            WriteLogWithLevel("WARN", message, source, LogLevel.Warning);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message</param>
        public static void LogError(string message, string source = "UFU2")
        {
            WriteLogWithLevel("ERROR", message, source, LogLevel.Error);
        }

        /// <summary>
        /// Logs a debug message (removed in RELEASE builds via conditional compilation)
        /// Includes filtering for services that generate excessive debug output
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message</param>
        [Conditional("DEBUG")]
        public static void LogDebug(string message, string source = "UFU2")
        {
            // Apply filtering for services that generate excessive debug output
            if (_reducedLoggingServices.Contains(source))
            {
                // Only log debug messages from these services if they contain important keywords
                if (!IsImportantDebugMessage(message))
                {
                    return; // Skip routine debug messages
                }
            }

            WriteLogWithLevel("DEBUG", message, source, LogLevel.Debug);
        }

        /// <summary>
        /// Logs a debug message with lazy evaluation for expensive string operations
        /// Only evaluates the message function in DEBUG builds
        /// Includes filtering for services that generate excessive debug output
        /// </summary>
        /// <param name="messageFunc">Function that returns the message to log</param>
        /// <param name="source">The source of the log message</param>
        [Conditional("DEBUG")]
        public static void LogDebugLazy(Func<string> messageFunc, string source = "UFU2")
        {
            // Apply filtering for services that generate excessive debug output
            if (_reducedLoggingServices.Contains(source))
            {
                var message = messageFunc();
                if (!IsImportantDebugMessage(message))
                {
                    return; // Skip routine debug messages
                }
                WriteLogWithLevel("DEBUG", message, source, LogLevel.Debug);
            }
            else
            {
                WriteLogWithLevel("DEBUG", messageFunc(), source, LogLevel.Debug);
            }
        }

        /// <summary>
        /// Measures logging performance for optimization analysis
        /// Only active in DEBUG builds
        /// </summary>
        /// <param name="operationName">Name of the operation being measured</param>
        /// <param name="action">Action to measure</param>
        [Conditional("DEBUG")]
        public static void MeasureLoggingPerformance(string operationName, Action action)
        {
            var stopwatch = Stopwatch.StartNew();
            action();
            stopwatch.Stop();

            if (stopwatch.ElapsedMilliseconds > 5) // Log slow operations
            {
                LogDebug($"Performance: {operationName} took {stopwatch.ElapsedMilliseconds}ms", "LoggingService");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Writes a log message with level filtering to debug output, console, and session log file
        /// Thread-safe operation that handles file I/O errors gracefully
        /// Uses tab spacing for improved readability: [timestamp] \t [level] \t\t [source] message
        /// </summary>
        /// <param name="level">The log level string</param>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message</param>
        /// <param name="logLevel">The log level enum for filtering</param>
        private static void WriteLogWithLevel(string level, string message, string source, LogLevel logLevel)
        {
            // Apply log level filtering
            if (logLevel < _minimumLogLevel)
            {
                return; // Skip logs below minimum level
            }

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logMessage = $"[{timestamp}]  \t[{level}]\t\t[{source}]\t{message}";

            // Always write to debug output (visible in Visual Studio Output window)
            Debug.WriteLine(logMessage);

            // Always write to console if available
            Console.WriteLine(logMessage);

            // Write to session log file if initialized
            WriteToSessionLogFile(logMessage);
        }

        /// <summary>
        /// Legacy WriteLog method for backward compatibility
        /// </summary>
        /// <param name="level">The log level</param>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the log message</param>
        private static void WriteLog(string level, string message, string source)
        {
            LogLevel logLevel = level switch
            {
                "DEBUG" => LogLevel.Debug,
                "INFO" => LogLevel.Info,
                "WARN" => LogLevel.Warning,
                "ERROR" => LogLevel.Error,
                _ => LogLevel.Debug
            };

            WriteLogWithLevel(level, message, source, logLevel);
        }

        /// <summary>
        /// Determines if a debug message from reduced logging services contains important information
        /// </summary>
        /// <param name="message">The debug message to evaluate</param>
        /// <returns>True if the message should be logged, false if it should be filtered out</returns>
        private static bool IsImportantDebugMessage(string message)
        {
            if (string.IsNullOrWhiteSpace(message))
                return false;

            // Keywords that indicate important debug information
            var importantKeywords = new[]
            {
                "error", "exception", "failed", "failure", "warning", "critical",
                "initialized", "disposed", "cache cleared", "performance",
                "completed", "started", "finished", "timeout", "retry"
            };

            var lowerMessage = message.ToLowerInvariant();
            return importantKeywords.Any(keyword => lowerMessage.Contains(keyword));
        }

        /// <summary>
        /// Writes a log message to the current session log file
        /// Thread-safe operation with graceful error handling
        /// </summary>
        /// <param name="logMessage">The formatted log message to write</param>
        private static void WriteToSessionLogFile(string logMessage)
        {
            if (!_isFileLoggingInitialized || string.IsNullOrEmpty(_currentLogFilePath))
            {
                return; // File logging not initialized, skip silently
            }

            lock (_fileLock)
            {
                try
                {
                    // Append log message to current session file
                    File.AppendAllText(_currentLogFilePath, logMessage + Environment.NewLine);
                }
                catch (Exception ex)
                {
                    // Don't use WriteLog here to avoid infinite recursion
                    // Log file I/O errors only to debug/console
                    var errorMessage = $"Failed to write to session log file: {ex.Message}";
                    Debug.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[ERROR]\t\t[LoggingService]\t{errorMessage}");
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[ERROR]\t\t[LoggingService]\t{errorMessage}");
                }
            }
        }

        /// <summary>
        /// Performs log file rotation to maintain the maximum number of log files
        /// Deletes the oldest log files when the limit would be exceeded
        /// </summary>
        private static void PerformLogFileRotation()
        {
            try
            {
                // Get all existing log files in the directory
                var logFiles = Directory.GetFiles(_logsDirectory, "UFU2_App_Log_*.log")
                    .Select(filePath => new FileInfo(filePath))
                    .ToList();

                // If we're not at the limit yet, no rotation needed
                if (logFiles.Count < MaxLogFiles)
                {
                    return;
                }

                // Sort files by creation time (oldest first) based on filename timestamp
                var sortedFiles = logFiles
                    .Select(file => new
                    {
                        FileInfo = file,
                        Timestamp = ExtractTimestampFromFilename(file.Name)
                    })
                    .Where(item => item.Timestamp.HasValue)
                    .OrderBy(item => item.Timestamp!.Value)
                    .ToList();

                // Calculate how many files to delete (we want to keep MaxLogFiles - 1 to make room for the new one)
                int filesToDelete = sortedFiles.Count - (MaxLogFiles - 1);

                if (filesToDelete > 0)
                {
                    var filesToRemove = sortedFiles.Take(filesToDelete).ToList();

                    foreach (var fileToRemove in filesToRemove)
                    {
                        try
                        {
                            File.Delete(fileToRemove.FileInfo.FullName);

                            // Log the deletion using direct console/debug output to avoid recursion
                            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[INFO]\t\t[LoggingService]\tDeleted old log file: {fileToRemove.FileInfo.Name}";
                            Debug.WriteLine(logMessage);
                            Console.WriteLine(logMessage);
                        }
                        catch (Exception deleteEx)
                        {
                            // Log deletion error but continue with rotation
                            var errorMessage = $"Failed to delete old log file {fileToRemove.FileInfo.Name}: {deleteEx.Message}";
                            Debug.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[WARN]\t\t[LoggingService]\t{errorMessage}");
                            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[WARN]\t\t[LoggingService]\t{errorMessage}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log rotation error but don't prevent new log file creation
                var errorMessage = $"Error during log file rotation: {ex.Message}";
                Debug.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[ERROR]\t\t[LoggingService]\t{errorMessage}");
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]  \t[ERROR]\t\t[LoggingService]\t{errorMessage}");
            }
        }

        /// <summary>
        /// Extracts the timestamp from a log filename in the format UFU2_App_Log_YYYY-MM-DD_HH-mm-ss.log
        /// </summary>
        /// <param name="filename">The log filename</param>
        /// <returns>The extracted DateTime if successful, null otherwise</returns>
        private static DateTime? ExtractTimestampFromFilename(string filename)
        {
            try
            {
                // Expected format: UFU2_App_Log_YYYY-MM-DD_HH-mm-ss.log
                var prefix = "UFU2_App_Log_";
                var suffix = ".log";

                if (!filename.StartsWith(prefix) || !filename.EndsWith(suffix))
                {
                    return null;
                }

                // Extract the timestamp part: YYYY-MM-DD_HH-mm-ss
                var timestampPart = filename.Substring(prefix.Length, filename.Length - prefix.Length - suffix.Length);

                // Parse the timestamp: YYYY-MM-DD_HH-mm-ss
                if (DateTime.TryParseExact(timestampPart, "yyyy-MM-dd_HH-mm-ss", null, System.Globalization.DateTimeStyles.None, out DateTime timestamp))
                {
                    return timestamp;
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        #endregion
    }

    /// <summary>
    /// Log level enumeration
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error
    }
}
