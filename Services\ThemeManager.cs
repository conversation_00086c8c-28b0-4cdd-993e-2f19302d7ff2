using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;
using MaterialDesignColors;
using UFU2.Common;
using UFU2.Services;

namespace UFU2.Services
{
    /// <summary>
    /// Comprehensive theme management service for UFU2 application.
    /// Handles dynamic switching between Light and Dark themes with MaterialDesign.
    /// Manages custom color mapping and resource dictionary updates while maintaining Arabic RTL layout support.
    /// </summary>
    public static class ThemeManager
    {
        #region Private Fields

        private static bool _isInitialized = false;
        private static ApplicationTheme _currentTheme = ApplicationTheme.Dark;
        private static readonly object _themeLock = new object();

        // Theme resource dictionary references
        private static ResourceDictionary _darkThemeDict;
        private static ResourceDictionary _lightThemeDict;
        private static BundledTheme _materialDesignTheme;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the current application theme
        /// </summary>
        public static ApplicationTheme CurrentTheme
        {
            get { return _currentTheme; }
        }

        /// <summary>
        /// Gets whether the theme manager has been initialized
        /// </summary>
        public static bool IsInitialized
        {
            get { return _isInitialized; }
        }

        /// <summary>
        /// Event raised when the theme changes
        /// </summary>
        public static event EventHandler<ThemeChangedEventArgs> ThemeChanged;

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes the ThemeManager with the current application theme
        /// Must be called during application startup before any theme operations
        /// </summary>
        /// <param name="initialTheme">The initial theme to set (defaults to Dark)</param>
        /// <returns>True if initialization was successful</returns>
        public static async Task<bool> InitializeAsync(ApplicationTheme initialTheme = ApplicationTheme.Dark)
        {
            return await ErrorManager.ExecuteWithErrorHandlingAsync(async () =>
            {
                lock (_themeLock)
                {
                    if (_isInitialized)
                    {
                        LoggingService.LogWarning("ThemeManager already initialized", "ThemeManager");
                        return;
                    }

                    LoggingService.LogDebug("Initializing ThemeManager", "ThemeManager");

                    // Load theme resource dictionaries
                    LoadThemeResourceDictionaries();

                    // Get reference to MaterialDesign BundledTheme
                    _materialDesignTheme = GetMaterialDesignBundledTheme();

                    // Set initial theme
                    _currentTheme = initialTheme;

                    _isInitialized = true;
                    LoggingService.LogDebug($"ThemeManager initialized with {initialTheme} theme", "ThemeManager");
                }

                // Apply initial theme
                await ApplyThemeAsync(_currentTheme);

            }, "ThemeManager", "Initialization", "Error during ThemeManager initialization", LogLevel.Error);
        }

        /// <summary>
        /// Loads the theme resource dictionaries from the Resources/Themes directory
        /// </summary>
        private static void LoadThemeResourceDictionaries()
        {
            try
            {
                // Load Dark Theme
                _darkThemeDict = new ResourceDictionary
                {
                    Source = new Uri("/Resources/Themes/DarkTheme.xaml", UriKind.Relative)
                };

                // Load Light Theme
                _lightThemeDict = new ResourceDictionary
                {
                    Source = new Uri("/Resources/Themes/LightTheme.xaml", UriKind.Relative)
                };

                LoggingService.LogDebug("Theme resource dictionaries loaded successfully", "ThemeManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading theme resource dictionaries: {ex.Message}", "ThemeManager");
                throw;
            }
        }

        /// <summary>
        /// Gets reference to the MaterialDesign BundledTheme from application resources
        /// </summary>
        /// <returns>BundledTheme instance or null if not found</returns>
        private static BundledTheme GetMaterialDesignBundledTheme()
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources?.MergedDictionaries != null)
                {
                    foreach (var dict in app.Resources.MergedDictionaries)
                    {
                        if (dict is BundledTheme bundledTheme)
                        {
                            LoggingService.LogDebug("Found MaterialDesign BundledTheme in application resources", "ThemeManager");
                            return bundledTheme;
                        }
                    }
                }

                LoggingService.LogWarning("MaterialDesign BundledTheme not found in application resources", "ThemeManager");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting MaterialDesign BundledTheme: {ex.Message}", "ThemeManager");
                return null;
            }
        }

        #endregion

        #region Theme Switching

        /// <summary>
        /// Switches to the specified theme with full UI update
        /// </summary>
        /// <param name="theme">The theme to switch to</param>
        /// <returns>True if theme switch was successful</returns>
        public static async Task<bool> SwitchThemeAsync(ApplicationTheme theme)
        {
            if (!_isInitialized)
            {
                LoggingService.LogError("ThemeManager not initialized. Call InitializeAsync first.", "ThemeManager");
                return false;
            }

            if (_currentTheme == theme)
            {
                LoggingService.LogDebug($"Theme is already set to {theme}", "ThemeManager");
                return true;
            }

            return await ErrorManager.ExecuteWithErrorHandlingAsync(async () =>
            {
                LoggingService.LogInfo($"Switching theme from {_currentTheme} to {theme}", "ThemeManager");

                var previousTheme = _currentTheme;
                _currentTheme = theme;

                // Apply the new theme
                await ApplyThemeAsync(theme);

                // Raise theme changed event
                ThemeChanged?.Invoke(null, new ThemeChangedEventArgs(previousTheme, theme));

                LoggingService.LogInfo($"Theme switched successfully to {theme}", "ThemeManager");

            }, "ThemeManager", "ThemeSwitch", $"Error switching theme to {theme}", LogLevel.Error);
        }

        /// <summary>
        /// Toggles between Light and Dark themes
        /// </summary>
        /// <returns>True if toggle was successful</returns>
        public static async Task<bool> ToggleThemeAsync()
        {
            var newTheme = _currentTheme == ApplicationTheme.Dark ? ApplicationTheme.Light : ApplicationTheme.Dark;
            return await SwitchThemeAsync(newTheme);
        }

        #endregion

        #region Theme Application

        /// <summary>
        /// Applies the specified theme to all UI frameworks and resource dictionaries
        /// </summary>
        /// <param name="theme">The theme to apply</param>
        private static async Task ApplyThemeAsync(ApplicationTheme theme)
        {
            await Task.Run(() =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    lock (_themeLock)
                    {
                        // 1. Update MaterialDesign Theme
                        UpdateMaterialDesignTheme(theme);

                        // 2. Update Custom Theme Resource Dictionary
                        UpdateCustomThemeResources(theme);

                        LoggingService.LogDebug($"Theme application completed for {theme}", "ThemeManager");
                    }
                });
            });
        }

        
        /// <summary>
        /// Updates the MaterialDesign BundledTheme with custom colors
        /// </summary>
        /// <param name="theme">The theme to apply</param>
        private static void UpdateMaterialDesignTheme(ApplicationTheme theme)
        {
            try
            {
                if (_materialDesignTheme == null)
                {
                    LoggingService.LogWarning("MaterialDesign BundledTheme not available for update", "ThemeManager");
                    return;
                }

                // Set base theme
                var baseTheme = theme == ApplicationTheme.Dark ? BaseTheme.Dark : BaseTheme.Light;
                _materialDesignTheme.BaseTheme = baseTheme;

                // Apply custom colors from UFU2 theme files
                ApplyCustomMaterialDesignColors(theme);

                LoggingService.LogDebug($"MaterialDesign theme updated to {baseTheme} with UFU2 custom colors", "ThemeManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating MaterialDesign theme: {ex.Message}", "ThemeManager");
            }
        }

        /// <summary>
        /// Gets the custom primary and secondary colors for the specified theme
        /// </summary>
        /// <param name="theme">The theme to get colors for</param>
        /// <returns>Tuple of primary and secondary colors</returns>
        private static (string primaryColor, string secondaryColor) GetCustomColorsForTheme(ApplicationTheme theme)
        {
            if (theme == ApplicationTheme.Dark)
            {
                // Dark Theme: Use warm orange colors from DarkTheme.xaml
                return ("White", "White");
            }
            else
            {
                // Light Theme: Use navy blue colors from LightTheme.xaml
                return ("Blue", "Blue");
            }
        }

        /// <summary>
        /// Updates MaterialDesign theme with custom colors from UFU2 theme files
        /// </summary>
        /// <param name="theme">The theme to apply custom colors for</param>
        private static void ApplyCustomMaterialDesignColors(ApplicationTheme theme)
        {
            try
            {
                if (_materialDesignTheme == null)
                {
                    LoggingService.LogWarning("MaterialDesign BundledTheme not available for custom color application", "ThemeManager");
                    return;
                }

                // Get the appropriate theme dictionary
                var themeDict = theme == ApplicationTheme.Dark ? _darkThemeDict : _lightThemeDict;
                if (themeDict == null)
                {
                    LoggingService.LogError($"Theme dictionary for {theme} is null", "ThemeManager");
                    return;
                }

                // Apply custom primary and secondary colors based on theme
                if (theme == ApplicationTheme.Dark)
                {
                    // Dark Theme: Use warm orange colors
                    var primaryColor = GetColorFromTheme(themeDict, "PrimaryColor");
                    var secondaryColor = GetColorFromTheme(themeDict, "SecondaryColor");

                    if (primaryColor.HasValue)
                    {
                        _materialDesignTheme.PrimaryColor = GetClosestMaterialDesignColor(primaryColor.Value, true);
                    }

                    if (secondaryColor.HasValue)
                    {
                        _materialDesignTheme.SecondaryColor = GetClosestMaterialDesignSecondaryColor(secondaryColor.Value, true);
                    }
                }
                else
                {
                    // Light Theme: Use navy blue colors
                    var primaryColor = GetColorFromTheme(themeDict, "LightTheme_NavyBlueDarkColor");
                    var secondaryColor = GetColorFromTheme(themeDict, "LightTheme_NavyBlueMediumColor");

                    if (primaryColor.HasValue)
                    {
                        _materialDesignTheme.PrimaryColor = GetClosestMaterialDesignColor(primaryColor.Value, false);
                    }

                    if (secondaryColor.HasValue)
                    {
                        _materialDesignTheme.SecondaryColor = GetClosestMaterialDesignSecondaryColor(secondaryColor.Value, false);
                    }
                }

                LoggingService.LogDebug($"Custom MaterialDesign colors applied for {theme} theme", "ThemeManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying custom MaterialDesign colors: {ex.Message}", "ThemeManager");
            }
        }

        /// <summary>
        /// Gets a color resource from the specified theme dictionary
        /// </summary>
        /// <param name="themeDict">The theme dictionary to search</param>
        /// <param name="colorKey">The color key to retrieve</param>
        /// <returns>The color value or null if not found</returns>
        private static Color? GetColorFromTheme(ResourceDictionary themeDict, string colorKey)
        {
            try
            {
                if (themeDict.Contains(colorKey))
                {
                    var resource = themeDict[colorKey];
                    if (resource is Color color)
                    {
                        return color;
                    }
                }

                LoggingService.LogWarning($"Color '{colorKey}' not found in theme dictionary", "ThemeManager");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting color '{colorKey}' from theme: {ex.Message}", "ThemeManager");
                return null;
            }
        }

        /// <summary>
        /// Gets the closest MaterialDesign primary color for the given custom color
        /// </summary>
        /// <param name="customColor">The custom color to match</param>
        /// <param name="isDarkTheme">Whether this is for dark theme</param>
        /// <returns>The closest MaterialDesign primary color</returns>
        private static PrimaryColor? GetClosestMaterialDesignColor(Color customColor, bool isDarkTheme)
        {
            // For dark theme warm orange colors, use Orange
            if (isDarkTheme)
            {
                return PrimaryColor.DeepPurple;
            }
            // For light theme navy blue colors, use Blue
            else
            {
                return PrimaryColor.DeepPurple;
            }
        }

        /// <summary>
        /// Gets the closest MaterialDesign secondary color for the given custom color
        /// </summary>
        /// <param name="customColor">The custom color to match</param>
        /// <param name="isDarkTheme">Whether this is for dark theme</param>
        /// <returns>The closest MaterialDesign secondary color</returns>
        private static SecondaryColor? GetClosestMaterialDesignSecondaryColor(Color customColor, bool isDarkTheme)
        {
            // For dark theme warm orange colors, use Orange
            if (isDarkTheme)
            {
                return SecondaryColor.Orange;
            }
            // For light theme navy blue colors, use Blue
            else
            {
                return SecondaryColor.Blue;
            }
        }

        /// <summary>
        /// Updates the custom theme resource dictionary in application resources
        /// </summary>
        /// <param name="theme">The theme to apply</param>
        private static void UpdateCustomThemeResources(ApplicationTheme theme)
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources?.MergedDictionaries == null)
                {
                    LoggingService.LogWarning("Application resources not available for theme update", "ThemeManager");
                    return;
                }

                // Remove existing theme dictionaries
                RemoveExistingThemeResources(app.Resources.MergedDictionaries);

                // Add the appropriate theme dictionary
                var themeDict = theme == ApplicationTheme.Dark ? _darkThemeDict : _lightThemeDict;
                if (themeDict != null)
                {
                    app.Resources.MergedDictionaries.Add(themeDict);
                    LoggingService.LogDebug($"Custom theme resources updated to {theme}", "ThemeManager");
                }
                else
                {
                    LoggingService.LogError($"Theme dictionary for {theme} is null", "ThemeManager");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating custom theme resources: {ex.Message}", "ThemeManager");
            }
        }

        /// <summary>
        /// Removes existing theme resource dictionaries from the merged dictionaries collection
        /// </summary>
        /// <param name="mergedDictionaries">The merged dictionaries collection</param>
        private static void RemoveExistingThemeResources(System.Collections.ObjectModel.Collection<ResourceDictionary> mergedDictionaries)
        {
            try
            {
                // Remove any existing theme dictionaries
                var toRemove = new System.Collections.Generic.List<ResourceDictionary>();

                foreach (var dict in mergedDictionaries)
                {
                    if (dict.Source != null &&
                        (dict.Source.ToString().Contains("/Resources/Themes/DarkTheme.xaml") ||
                         dict.Source.ToString().Contains("/Resources/Themes/LightTheme.xaml")))
                    {
                        toRemove.Add(dict);
                    }
                }

                foreach (var dict in toRemove)
                {
                    mergedDictionaries.Remove(dict);
                }

                LoggingService.LogDebug($"Removed {toRemove.Count} existing theme dictionaries", "ThemeManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing existing theme resources: {ex.Message}", "ThemeManager");
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets the theme-appropriate color resource from the current theme dictionary
        /// </summary>
        /// <param name="resourceKey">The resource key to retrieve</param>
        /// <returns>The color resource or null if not found</returns>
        public static Color? GetThemeColor(string resourceKey)
        {
            try
            {
                var currentDict = _currentTheme == ApplicationTheme.Dark ? _darkThemeDict : _lightThemeDict;
                if (currentDict?.Contains(resourceKey) == true)
                {
                    var resource = currentDict[resourceKey];
                    if (resource is Color color)
                    {
                        return color;
                    }
                    else if (resource is SolidColorBrush brush)
                    {
                        return brush.Color;
                    }
                }

                LoggingService.LogWarning($"Theme color '{resourceKey}' not found in current theme", "ThemeManager");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting theme color '{resourceKey}': {ex.Message}", "ThemeManager");
                return null;
            }
        }

        /// <summary>
        /// Forces a refresh of all UI elements to apply theme changes
        /// </summary>
        public static void RefreshUI()
        {
            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    // Force refresh of all windows
                    foreach (Window window in Application.Current.Windows)
                    {
                        window.InvalidateVisual();
                        window.UpdateLayout();
                    }
                });

                LoggingService.LogDebug("UI refresh completed", "ThemeManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error refreshing UI: {ex.Message}", "ThemeManager");
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Event arguments for theme change events
    /// </summary>
    public class ThemeChangedEventArgs : EventArgs
    {
        public ApplicationTheme PreviousTheme { get; }
        public ApplicationTheme NewTheme { get; }

        public ThemeChangedEventArgs(ApplicationTheme previousTheme, ApplicationTheme newTheme)
        {
            PreviousTheme = previousTheme;
            NewTheme = newTheme;
        }
    }

    /// <summary>
    /// Application theme enumeration
    /// </summary>
    public enum ApplicationTheme
    {
        Light,
        Dark
    }

    #endregion
}
