using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace UFU2.Models
{
    /// <summary>
    /// Model class representing a craft type from the CraftTypeBase database table.
    /// Contains craft code, description, content, and secondary data with validation support.
    /// </summary>
    public class CraftTypeBaseModel : INotifyPropertyChanged, IDataErrorInfo
    {
        private string _code;
        private string _description;
        private string _content;
        private string _secondary;

        /// <summary>
        /// Gets or sets the craft code (primary identifier).
        /// </summary>
        [Required(ErrorMessage = "كود الحرفة مطلوب")]
        [StringLength(20, ErrorMessage = "كود الحرفة يجب أن يكون أقل من 20 حرف")]
        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged(nameof(Code));
                }
            }
        }

        /// <summary>
        /// Gets or sets the craft description.
        /// </summary>
        [Required(ErrorMessage = "وصف الحرفة مطلوب")]
        [StringLength(1000, ErrorMessage = "وصف الحرفة يجب أن يكون أقل من 1000 حرف")]
        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged(nameof(Description));
                }
            }
        }

        /// <summary>
        /// Gets or sets the craft content details.
        /// </summary>
        [StringLength(2000, ErrorMessage = "محتوى الحرفة يجب أن يكون أقل من 2000 حرف")]
        public string Content
        {
            get => _content;
            set
            {
                if (_content != value)
                {
                    _content = value;
                    OnPropertyChanged(nameof(Content));
                }
            }
        }

        /// <summary>
        /// Gets or sets the secondary craft information.
        /// </summary>
        [StringLength(1000, ErrorMessage = "المعلومات الثانوية يجب أن تكون أقل من 1000 حرف")]
        public string Secondary
        {
            get => _secondary;
            set
            {
                if (_secondary != value)
                {
                    _secondary = value;
                    OnPropertyChanged(nameof(Secondary));
                }
            }
        }

        /// <summary>
        /// Default constructor for CraftTypeBaseModel.
        /// </summary>
        public CraftTypeBaseModel()
        {
        }

        /// <summary>
        /// Constructor with parameters for CraftTypeBaseModel.
        /// </summary>
        /// <param name="code">Craft code</param>
        /// <param name="description">Craft description</param>
        /// <param name="content">Craft content</param>
        /// <param name="secondary">Secondary information</param>
        public CraftTypeBaseModel(string code, string description, string content = null, string secondary = null)
        {
            Code = code;
            Description = description;
            Content = content;
            Secondary = secondary;
        }

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event raised when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event for the specified property.
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region IDataErrorInfo Implementation

        /// <summary>
        /// Gets an error message indicating what is wrong with this object.
        /// </summary>
        public string Error
        {
            get
            {
                var validationResults = new List<ValidationResult>();
                var validationContext = new ValidationContext(this);
                
                if (!Validator.TryValidateObject(this, validationContext, validationResults, true))
                {
                    return string.Join(Environment.NewLine, validationResults.Select(r => r.ErrorMessage));
                }
                
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the error message for the property with the given name.
        /// </summary>
        /// <param name="columnName">The name of the property whose error message to get</param>
        /// <returns>The error message for the property</returns>
        public string this[string columnName]
        {
            get
            {
                var validationResults = new List<ValidationResult>();
                var validationContext = new ValidationContext(this) { MemberName = columnName };
                
                var property = GetType().GetProperty(columnName);
                if (property != null)
                {
                    var value = property.GetValue(this);
                    if (!Validator.TryValidateProperty(value, validationContext, validationResults))
                    {
                        return validationResults.FirstOrDefault()?.ErrorMessage ?? string.Empty;
                    }
                }
                
                return string.Empty;
            }
        }

        #endregion

        #region Object Overrides

        /// <summary>
        /// Determines whether the specified object is equal to the current object.
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>True if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object obj)
        {
            if (obj is CraftTypeBaseModel other)
            {
                return string.Equals(Code, other.Code, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the current object.
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Code?.GetHashCode() ?? 0;
        }

        /// <summary>
        /// Returns a string representation of the craft type.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"{Code} - {Description}";
        }

        #endregion

        /// <summary>
        /// Validates the model and returns true if all properties are valid.
        /// </summary>
        /// <returns>True if model is valid, false otherwise</returns>
        public bool IsValid()
        {
            return string.IsNullOrEmpty(Error);
        }

        /// <summary>
        /// Creates a copy of the current CraftTypeBaseModel.
        /// </summary>
        /// <returns>New instance with copied values</returns>
        public CraftTypeBaseModel Clone()
        {
            return new CraftTypeBaseModel(Code, Description, Content, Secondary);
        }
    }
}
