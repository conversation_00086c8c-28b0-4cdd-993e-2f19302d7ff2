using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Background view initialization service for UFU2 application.
    /// Handles background processing of view components without affecting immediate user interaction.
    /// Implements Phase 2C Task 3.1 requirements for background view initialization.
    /// </summary>
    public class BackgroundViewInitializationService : IDisposable
    {
        #region Private Fields

        private readonly ConcurrentQueue<BackgroundInitializationTask> _initializationQueue;
        private readonly ConcurrentDictionary<string, BackgroundInitializationProgress> _progressTracking;
        private readonly SemaphoreSlim _processingLock;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Timer _progressReportingTimer;
        private Task? _backgroundProcessingTask;
        private bool _disposed = false;

        // Configuration
        private readonly int _maxConcurrentTasks = 3;
        private readonly TimeSpan _progressReportingInterval = TimeSpan.FromSeconds(2);

        // Performance tracking
        private static long _totalTasksProcessed = 0;
        private static long _totalTasksQueued = 0;
        private static long _averageProcessingTimeMs = 0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the BackgroundViewInitializationService.
        /// </summary>
        public BackgroundViewInitializationService()
        {
            _initializationQueue = new ConcurrentQueue<BackgroundInitializationTask>();
            _progressTracking = new ConcurrentDictionary<string, BackgroundInitializationProgress>();
            _processingLock = new SemaphoreSlim(_maxConcurrentTasks, _maxConcurrentTasks);
            _cancellationTokenSource = new CancellationTokenSource();

            // Start background processing task
            _backgroundProcessingTask = Task.Run(ProcessInitializationQueueAsync);

            // Start progress reporting timer
            _progressReportingTimer = new Timer(
                ReportProgress,
                null,
                _progressReportingInterval,
                _progressReportingInterval);

            LoggingService.LogInfo("BackgroundViewInitializationService initialized", "BackgroundViewInitializationService");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Queues a view component for background initialization.
        /// </summary>
        /// <param name="taskId">Unique identifier for the task</param>
        /// <param name="initializationAction">Action to perform in background</param>
        /// <param name="priority">Priority level for the task</param>
        /// <param name="progressCallback">Optional progress callback</param>
        public void QueueBackgroundInitialization(
            string taskId,
            Func<CancellationToken, Task> initializationAction,
            BackgroundTaskPriority priority = BackgroundTaskPriority.Normal,
            Action<BackgroundInitializationProgress>? progressCallback = null)
        {
            if (string.IsNullOrEmpty(taskId) || initializationAction == null)
                return;

            var task = new BackgroundInitializationTask
            {
                TaskId = taskId,
                InitializationAction = initializationAction,
                Priority = priority,
                QueuedAt = DateTime.UtcNow,
                ProgressCallback = progressCallback
            };

            _initializationQueue.Enqueue(task);
            System.Threading.Interlocked.Increment(ref _totalTasksQueued);

            // Initialize progress tracking
            _progressTracking[taskId] = new BackgroundInitializationProgress
            {
                TaskId = taskId,
                Status = BackgroundTaskStatus.Queued,
                QueuedAt = DateTime.UtcNow
            };

            LoggingService.LogDebug($"Queued background initialization task: {taskId} (Priority: {priority})", "BackgroundViewInitializationService");
        }

        /// <summary>
        /// Queues a UI component for background data loading.
        /// </summary>
        /// <param name="taskId">Unique identifier for the task</param>
        /// <param name="component">UI component to initialize</param>
        /// <param name="dataLoadingAction">Data loading action</param>
        /// <param name="priority">Priority level for the task</param>
        public void QueueComponentDataLoading(
            string taskId,
            FrameworkElement component,
            Func<CancellationToken, Task<object?>> dataLoadingAction,
            BackgroundTaskPriority priority = BackgroundTaskPriority.Normal)
        {
            QueueBackgroundInitialization(
                taskId,
                async (cancellationToken) =>
                {
                    try
                    {
                        // Load data in background
                        var data = await dataLoadingAction(cancellationToken);

                        // Update UI on UI thread
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            if (component.DataContext != data && data != null)
                            {
                                component.DataContext = data;
                            }
                        }, DispatcherPriority.Background, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        LoggingService.LogDebug($"Background data loading cancelled for task: {taskId}", "BackgroundViewInitializationService");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error in background data loading for task {taskId}: {ex.Message}", "BackgroundViewInitializationService");
                    }
                },
                priority);
        }

        /// <summary>
        /// Gets the current progress of a background task.
        /// </summary>
        /// <param name="taskId">Task identifier</param>
        /// <returns>Progress information or null if task not found</returns>
        public BackgroundInitializationProgress? GetTaskProgress(string taskId)
        {
            return _progressTracking.TryGetValue(taskId, out var progress) ? progress : null;
        }

        /// <summary>
        /// Gets all current background tasks and their progress.
        /// </summary>
        /// <returns>Dictionary of task IDs and their progress</returns>
        public Dictionary<string, BackgroundInitializationProgress> GetAllTaskProgress()
        {
            return new Dictionary<string, BackgroundInitializationProgress>(_progressTracking);
        }

        /// <summary>
        /// Cancels a specific background task.
        /// </summary>
        /// <param name="taskId">Task identifier to cancel</param>
        public void CancelTask(string taskId)
        {
            if (_progressTracking.TryGetValue(taskId, out var progress))
            {
                progress.Status = BackgroundTaskStatus.Cancelled;
                progress.CompletedAt = DateTime.UtcNow;
                LoggingService.LogInfo($"Cancelled background task: {taskId}", "BackgroundViewInitializationService");
            }
        }

        /// <summary>
        /// Gets performance statistics for background processing.
        /// </summary>
        /// <returns>Performance statistics</returns>
        public BackgroundProcessingStatistics GetPerformanceStatistics()
        {
            var activeTasksCount = 0;
            var completedTasksCount = 0;
            var failedTasksCount = 0;

            foreach (var progress in _progressTracking.Values)
            {
                switch (progress.Status)
                {
                    case BackgroundTaskStatus.Running:
                        activeTasksCount++;
                        break;
                    case BackgroundTaskStatus.Completed:
                        completedTasksCount++;
                        break;
                    case BackgroundTaskStatus.Failed:
                        failedTasksCount++;
                        break;
                }
            }

            return new BackgroundProcessingStatistics
            {
                TotalTasksQueued = _totalTasksQueued,
                TotalTasksProcessed = _totalTasksProcessed,
                ActiveTasksCount = activeTasksCount,
                CompletedTasksCount = completedTasksCount,
                FailedTasksCount = failedTasksCount,
                AverageProcessingTimeMs = _averageProcessingTimeMs,
                QueueLength = _initializationQueue.Count
            };
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Processes the background initialization queue.
        /// </summary>
        private async Task ProcessInitializationQueueAsync()
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    if (_initializationQueue.TryDequeue(out var task))
                    {
                        await _processingLock.WaitAsync(_cancellationTokenSource.Token);
                        try
                        {
                            _ = Task.Run(async () =>
                            {
                                try
                                {
                                    await ProcessSingleTaskAsync(task);
                                }
                                finally
                                {
                                    _processingLock.Release();
                                }
                            }, _cancellationTokenSource.Token);
                        }
                        catch
                        {
                            _processingLock.Release();
                            throw;
                        }
                    }
                    else
                    {
                        // No tasks in queue, wait a bit
                        await Task.Delay(100, _cancellationTokenSource.Token);
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error in background processing queue: {ex.Message}", "BackgroundViewInitializationService");
                    await Task.Delay(1000, _cancellationTokenSource.Token); // Wait before retrying
                }
            }
        }

        /// <summary>
        /// Processes a single background task.
        /// </summary>
        private async Task ProcessSingleTaskAsync(BackgroundInitializationTask task)
        {
            var startTime = DateTime.UtcNow;
            var progress = _progressTracking.GetOrAdd(task.TaskId, new BackgroundInitializationProgress
            {
                TaskId = task.TaskId,
                Status = BackgroundTaskStatus.Running,
                StartedAt = startTime
            });

            progress.Status = BackgroundTaskStatus.Running;
            progress.StartedAt = startTime;

            try
            {
                // Execute the initialization action
                await task.InitializationAction(_cancellationTokenSource.Token);

                // Mark as completed
                progress.Status = BackgroundTaskStatus.Completed;
                progress.CompletedAt = DateTime.UtcNow;
                progress.ProcessingTimeMs = (long)(progress.CompletedAt.Value - startTime).TotalMilliseconds;

                // Update performance tracking
                System.Threading.Interlocked.Increment(ref _totalTasksProcessed);
                var currentAvg = Interlocked.Read(ref _averageProcessingTimeMs);
                var newAvg = (currentAvg + progress.ProcessingTimeMs) / 2;
                Interlocked.Exchange(ref _averageProcessingTimeMs, newAvg);

                // Notify progress callback
                task.ProgressCallback?.Invoke(progress);

                LoggingService.LogDebug($"Completed background task: {task.TaskId} in {progress.ProcessingTimeMs}ms", "BackgroundViewInitializationService");
            }
            catch (OperationCanceledException)
            {
                progress.Status = BackgroundTaskStatus.Cancelled;
                progress.CompletedAt = DateTime.UtcNow;
                LoggingService.LogDebug($"Background task cancelled: {task.TaskId}", "BackgroundViewInitializationService");
            }
            catch (Exception ex)
            {
                progress.Status = BackgroundTaskStatus.Failed;
                progress.CompletedAt = DateTime.UtcNow;
                progress.ErrorMessage = ex.Message;
                LoggingService.LogError($"Background task failed: {task.TaskId} - {ex.Message}", "BackgroundViewInitializationService");
            }
        }

        /// <summary>
        /// Reports progress periodically.
        /// </summary>
        private void ReportProgress(object? state)
        {
            try
            {
                var stats = GetPerformanceStatistics();
                if (stats.ActiveTasksCount > 0 || stats.QueueLength > 0)
                {
                    LoggingService.LogDebug($"Background processing status - Active: {stats.ActiveTasksCount}, Queue: {stats.QueueLength}, Avg: {stats.AverageProcessingTimeMs}ms", "BackgroundViewInitializationService");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error reporting background processing progress: {ex.Message}", "BackgroundViewInitializationService");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the BackgroundViewInitializationService.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _cancellationTokenSource.Cancel();
                _progressReportingTimer?.Dispose();
                _backgroundProcessingTask?.Wait(TimeSpan.FromSeconds(5));
                _processingLock?.Dispose();
                _cancellationTokenSource?.Dispose();
                _disposed = true;

                LoggingService.LogInfo("BackgroundViewInitializationService disposed", "BackgroundViewInitializationService");
            }
        }

        #endregion
    }

    /// <summary>
    /// Represents a background initialization task.
    /// </summary>
    public class BackgroundInitializationTask
    {
        public string TaskId { get; set; } = string.Empty;
        public Func<CancellationToken, Task> InitializationAction { get; set; } = null!;
        public BackgroundTaskPriority Priority { get; set; }
        public DateTime QueuedAt { get; set; }
        public Action<BackgroundInitializationProgress>? ProgressCallback { get; set; }
    }

    /// <summary>
    /// Represents the progress of a background initialization task.
    /// </summary>
    public class BackgroundInitializationProgress
    {
        public string TaskId { get; set; } = string.Empty;
        public BackgroundTaskStatus Status { get; set; }
        public DateTime QueuedAt { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public long ProcessingTimeMs { get; set; }
        public string? ErrorMessage { get; set; }
        public double ProgressPercentage { get; set; }
    }

    /// <summary>
    /// Performance statistics for background processing.
    /// </summary>
    public class BackgroundProcessingStatistics
    {
        public long TotalTasksQueued { get; set; }
        public long TotalTasksProcessed { get; set; }
        public int ActiveTasksCount { get; set; }
        public int CompletedTasksCount { get; set; }
        public int FailedTasksCount { get; set; }
        public long AverageProcessingTimeMs { get; set; }
        public int QueueLength { get; set; }
    }

    /// <summary>
    /// Priority levels for background tasks.
    /// </summary>
    public enum BackgroundTaskPriority
    {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    }

    /// <summary>
    /// Status of background tasks.
    /// </summary>
    public enum BackgroundTaskStatus
    {
        Queued,
        Running,
        Completed,
        Failed,
        Cancelled
    }
}
