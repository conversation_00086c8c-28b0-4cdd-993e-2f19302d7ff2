using System;
using System.Collections.Generic;
using System.Linq;
using MaterialDesignThemes.Wpf;
using UFU2.Models;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Static mapping class that provides comprehensive conversion between phone types,
    /// Arabic display names, and Material Design icons for the UFU2 project.
    /// 
    /// This class centralizes all phone type conversion logic and supports:
    /// - Phone type enum to Arabic display text
    /// - Phone type enum to Material Design icons
    /// - Bidirectional lookups for all data representations
    /// - Error handling with graceful fallbacks
    /// </summary>
    public static class PhoneTypeIconMapping
    {
        #region Private Static Fields

        /// <summary>
        /// Core mapping dictionary that associates each phone type with its icon and Arabic name.
        /// This serves as the single source of truth for all phone type conversions.
        /// </summary>
        private static readonly Dictionary<PhoneType, PhoneTypeInfo> _phoneTypeMappings = new()
        {
            {
                PhoneType.Mobile,
                new PhoneTypeInfo
                {
                    PhoneType = PhoneType.Mobile,
                    ArabicName = "هاتف محمول",
                    EnglishName = "Mobile",
                    IconKind = PackIconKind.Cellphone
                }
            },
            {
                PhoneType.Home,
                new PhoneTypeInfo
                {
                    PhoneType = PhoneType.Home,
                    ArabicName = "هاتف المنزل",
                    EnglishName = "Home",
                    IconKind = PackIconKind.Home
                }
            },
            {
                PhoneType.Work,
                new PhoneTypeInfo
                {
                    PhoneType = PhoneType.Work,
                    ArabicName = "هاتف العمل",
                    EnglishName = "Work",
                    IconKind = PackIconKind.OfficeBuilding
                }
            },
            {
                PhoneType.Fax,
                new PhoneTypeInfo
                {
                    PhoneType = PhoneType.Fax,
                    ArabicName = "فاكس",
                    EnglishName = "Fax",
                    IconKind = PackIconKind.Fax
                }
            }
        };

        #endregion

        #region Public Conversion Methods

        /// <summary>
        /// Converts a phone type enum to its Arabic display name.
        /// </summary>
        /// <param name="phoneType">The phone type to convert</param>
        /// <returns>Arabic display name, or default mobile name if not found</returns>
        public static string GetArabicName(PhoneType phoneType)
        {
            return _phoneTypeMappings.TryGetValue(phoneType, out var info)
                ? info.ArabicName
                : _phoneTypeMappings[PhoneType.Mobile].ArabicName; // Fallback to mobile
        }

        /// <summary>
        /// Converts a phone type enum to its English name.
        /// </summary>
        /// <param name="phoneType">The phone type to convert</param>
        /// <returns>English name, or default mobile name if not found</returns>
        public static string GetEnglishName(PhoneType phoneType)
        {
            return _phoneTypeMappings.TryGetValue(phoneType, out var info)
                ? info.EnglishName
                : _phoneTypeMappings[PhoneType.Mobile].EnglishName; // Fallback to mobile
        }

        /// <summary>
        /// Converts a phone type enum to its Material Design icon kind.
        /// </summary>
        /// <param name="phoneType">The phone type to convert</param>
        /// <returns>PackIconKind for the phone type, or mobile icon if not found</returns>
        public static PackIconKind GetIconKind(PhoneType phoneType)
        {
            return _phoneTypeMappings.TryGetValue(phoneType, out var info)
                ? info.IconKind
                : _phoneTypeMappings[PhoneType.Mobile].IconKind; // Fallback to mobile
        }

        /// <summary>
        /// Converts an Arabic display name to its corresponding phone type enum.
        /// </summary>
        /// <param name="arabicName">The Arabic name to convert</param>
        /// <returns>Phone type enum, or Mobile if not found</returns>
        public static PhoneType GetPhoneTypeFromArabic(string arabicName)
        {
            if (string.IsNullOrWhiteSpace(arabicName))
                return PhoneType.Mobile;

            var mapping = _phoneTypeMappings.Values
                .FirstOrDefault(info => string.Equals(info.ArabicName, arabicName.Trim(), StringComparison.OrdinalIgnoreCase));

            return mapping?.PhoneType ?? PhoneType.Mobile;
        }

        /// <summary>
        /// Converts an English name to its corresponding phone type enum.
        /// </summary>
        /// <param name="englishName">The English name to convert</param>
        /// <returns>Phone type enum, or Mobile if not found</returns>
        public static PhoneType GetPhoneTypeFromEnglish(string englishName)
        {
            if (string.IsNullOrWhiteSpace(englishName))
                return PhoneType.Mobile;

            var mapping = _phoneTypeMappings.Values
                .FirstOrDefault(info => string.Equals(info.EnglishName, englishName.Trim(), StringComparison.OrdinalIgnoreCase));

            return mapping?.PhoneType ?? PhoneType.Mobile;
        }

        /// <summary>
        /// Converts a Material Design icon kind to its corresponding phone type enum.
        /// </summary>
        /// <param name="iconKind">The icon kind to convert</param>
        /// <returns>Phone type enum, or Mobile if not found</returns>
        public static PhoneType GetPhoneTypeFromIcon(PackIconKind iconKind)
        {
            var mapping = _phoneTypeMappings.Values
                .FirstOrDefault(info => info.IconKind == iconKind);

            return mapping?.PhoneType ?? PhoneType.Mobile;
        }

        /// <summary>
        /// Converts an Arabic display name to its corresponding Material Design icon kind.
        /// </summary>
        /// <param name="arabicName">The Arabic name to convert</param>
        /// <returns>PackIconKind for the phone type, or mobile icon if not found</returns>
        public static PackIconKind GetIconFromArabic(string arabicName)
        {
            var phoneType = GetPhoneTypeFromArabic(arabicName);
            return GetIconKind(phoneType);
        }

        /// <summary>
        /// Converts an English name to its corresponding Material Design icon kind.
        /// </summary>
        /// <param name="englishName">The English name to convert</param>
        /// <returns>PackIconKind for the phone type, or mobile icon if not found</returns>
        public static PackIconKind GetIconFromEnglish(string englishName)
        {
            var phoneType = GetPhoneTypeFromEnglish(englishName);
            return GetIconKind(phoneType);
        }

        /// <summary>
        /// Converts a Material Design icon kind to its corresponding Arabic display name.
        /// </summary>
        /// <param name="iconKind">The icon kind to convert</param>
        /// <returns>Arabic display name, or default mobile name if not found</returns>
        public static string GetArabicFromIcon(PackIconKind iconKind)
        {
            var phoneType = GetPhoneTypeFromIcon(iconKind);
            return GetArabicName(phoneType);
        }

        /// <summary>
        /// Converts a Material Design icon kind to its corresponding English name.
        /// </summary>
        /// <param name="iconKind">The icon kind to convert</param>
        /// <returns>English name, or default mobile name if not found</returns>
        public static string GetEnglishFromIcon(PackIconKind iconKind)
        {
            var phoneType = GetPhoneTypeFromIcon(iconKind);
            return GetEnglishName(phoneType);
        }

        /// <summary>
        /// Gets all available phone type mappings as a dictionary for ComboBox binding.
        /// </summary>
        /// <returns>Dictionary with phone type as key and Arabic name as value</returns>
        public static Dictionary<PhoneType, string> GetAllPhoneTypesWithArabicNames()
        {
            return _phoneTypeMappings.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.ArabicName
            );
        }

        /// <summary>
        /// Gets all available phone type mappings as a dictionary with English names.
        /// </summary>
        /// <returns>Dictionary with phone type as key and English name as value</returns>
        public static Dictionary<PhoneType, string> GetAllPhoneTypesWithEnglishNames()
        {
            return _phoneTypeMappings.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.EnglishName
            );
        }

        /// <summary>
        /// Gets all available phone type mappings as a dictionary with icon kinds.
        /// </summary>
        /// <returns>Dictionary with phone type as key and PackIconKind as value</returns>
        public static Dictionary<PhoneType, PackIconKind> GetAllPhoneTypesWithIcons()
        {
            return _phoneTypeMappings.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.IconKind
            );
        }

        #endregion

        #region Helper Classes

        /// <summary>
        /// Internal class that holds all information for a phone type.
        /// Used to maintain consistency across all conversion methods.
        /// </summary>
        private class PhoneTypeInfo
        {
            public PhoneType PhoneType { get; set; }
            public string ArabicName { get; set; } = string.Empty;
            public string EnglishName { get; set; } = string.Empty;
            public PackIconKind IconKind { get; set; }
        }

        #endregion
    }
}
