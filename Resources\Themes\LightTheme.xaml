﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--
        ========================================
        UFU2 THEME COLOR SYSTEM - LIGHT THEME
        ========================================
        
        This ResourceDictionary defines the color system for the UFU2 application
        using a light theme. Colors are categorized by **purpose** and **UI role**,
        not appearance (e.g., not “BlueColor” or “GrayColor”), enabling consistent
        reuse and easy theming across light and light modes.
        
        ========================================
        THEME CHARACTERISTICS:
        - light backgrounds (deep blues)
        - Light text for contrast and readability
        - Warm orange as an accent/brand color
        - Business-professional visual identity
        ========================================
        
        NAMING CONVENTION:
        - Semantic: {Purpose}{Level}{Type} (e.g., PrimaryTextColor, BackgroundBase)
        - Avoid hardcoded appearance in names (e.g., "Blue", "Light")
        - Emphasize *functional role* (Background, Text, Accent, etc.)
        - Enables easy swapping for Light Theme with same keys
    -->

    <!--
        ========================================
        BASE COLOR PALETTE (Raw Theme Values)
        These are low-level color values specific to the light theme.
        DO NOT reference directly in UI – using semantic brushes instead.
        ========================================
    -->
    <!--

        <Color x:Key="Surface">#E6BFC6D4</Color>
        <Color x:Key="SurfaceDim">#E6D1D6E0</Color>
        <Color x:Key="SurfaceBright">#E6E0E4EB</Color>

        <Color x:Key="InverseSurface">#E6D1D6E0</Color>
        <Color x:Key="InverseSurfaceDim">#E6BFC6D4</Color>
    -->
    <!--  #e8e4dd
    <Color x:Key="Surface">#E6F8F7F5</Color>
    <Color x:Key="SurfaceDim">#E6F0EDE9</Color>
    <Color x:Key="SurfaceBright">#E6e8e4dd</Color>

    <Color x:Key="Surface">#E6F6F6F6</Color>
    <Color x:Key="SurfaceDim">#E6EEEEEE</Color>
    <Color x:Key="SurfaceBright">#E6E3E3E3</Color>

        <Color x:Key="Surface">#E6FAFAFA</Color>
    <Color x:Key="SurfaceDim">#E6EBEBEB</Color>
    <Color x:Key="SurfaceBright">#E6E6E6E6</Color>

    <Color x:Key="InverseSurface">#E6D7D7D7</Color>
    <Color x:Key="InverseSurfaceDim">#E6D2D2D2</Color>


    -->

    <!--  ========== SURFACE COLORS (LIGHT THEME) ==========  -->
    <Color x:Key="Surface">#E6FAFAFA</Color>
    <Color x:Key="SurfaceDim">#E6EBEBEB</Color>
    <Color x:Key="SurfaceBright">#E6E6E6E6</Color>

    <Color x:Key="InverseSurface">#E6D7D7D7</Color>
    <Color x:Key="InverseSurfaceDim">#E6D2D2D2</Color>

    <!--  ========== PRIMARY COLORS (LIGHT THEME) ==========  -->
    <!--    -->
    <Color x:Key="PrimaryFixed">#FFFFB02E</Color>
    <Color x:Key="PrimaryDim">#FFFFA309</Color>

    <Color x:Key="OnPrimary">#FFFFA105</Color>

    <Color x:Key="PrimaryContainer">#99FFB02E</Color>
    <Color x:Key="PrimaryContainerDim">#4DFFB02E</Color>
    <Color x:Key="PrimaryContainerBright">#1AFFB02E</Color>

    <Color x:Key="OnPrimaryContainer">#FFFFB02E</Color>

    <!--  ========== PRIMARY TEXT COLORS ==========  -->
    <Color x:Key="TextFillColorPrimary">#E4000000</Color>
    <Color x:Key="TextFillColorSecondary">#9E000000</Color>
    <Color x:Key="TextFillColorTertiary">#72000000</Color>
    <Color x:Key="TextFillColorDisabled">#5C000000</Color>
    <Color x:Key="TextPlaceholderColor">#9E000000</Color>
    <Color x:Key="TextFillColorInverse">#FFFFFF</Color>

    <!--  ========== STATUS COLORS ==========  -->
    <Color x:Key="Success">#2EF47F</Color>
    <Color x:Key="OnSuccess">#07873B</Color>

    <Color x:Key="Warning">#FACC15</Color>
    <Color x:Key="OnWarning">#BA9504</Color>

    <Color x:Key="Error">#FF4C4C</Color>
    <Color x:Key="OnError">#F90000</Color>

    <Color x:Key="Info">#00FFFF</Color>
    <Color x:Key="OnInfo">#006061</Color>

    <!--  ========== G12/Bis STATUS COLORS ==========  -->
    <Color x:Key="ChipBackgoundPaid">#4D07873B</Color>
    <Color x:Key="ChipBorderPaid">#07873B</Color>

    <Color x:Key="ChipBackgoundNotPaid">#4DF90000</Color>
    <Color x:Key="ChipBorderNotPaid">#F90000</Color>

    <!--  ========== Popup Flag Status Colors ==========  -->
    <Color x:Key="NormaleFlag">#2EF47F</Color>
    <Color x:Key="ImportantFlag">#FACC15</Color>
    <Color x:Key="VeryImportantFlag">#FF4C4C</Color>

    <!--
        ========================================
        COLORS WITH OPACITY
        ========================================
    -->

    <!--  ========== PRIMARY COLORS ==========  -->
    <Color x:Key="PrimaryColor10">#1A673AB7</Color>
    <Color x:Key="PrimaryColor20">#33673AB7</Color>
    <Color x:Key="PrimaryColor30">#4D673AB7</Color>
    <Color x:Key="PrimaryColor40">#66673AB7</Color>
    <Color x:Key="PrimaryColor50">#80673AB7</Color>
    <Color x:Key="PrimaryColor60">#99673AB7</Color>
    <Color x:Key="PrimaryColor70">#B3673AB7</Color>
    <Color x:Key="PrimaryColor80">#CC673AB7</Color>
    <Color x:Key="PrimaryColor90">#E6673AB7</Color>
    <!--  ========== PRIMARY COLORS DIM ==========  -->
    <Color x:Key="PrimaryColorDim10">#1A7E57C2</Color>
    <Color x:Key="PrimaryColorDim20">#337E57C2</Color>
    <Color x:Key="PrimaryColorDim30">#4D7E57C2</Color>
    <Color x:Key="PrimaryColorDim40">#667E57C2</Color>
    <Color x:Key="PrimaryColorDim50">#807E57C2</Color>
    <Color x:Key="PrimaryColorDim60">#997E57C2</Color>
    <Color x:Key="PrimaryColorDim70">#B37E57C2</Color>
    <Color x:Key="PrimaryColorDim80">#CC7E57C2</Color>
    <Color x:Key="PrimaryColorDim90">#E67E57C2</Color>
    <!--  ========== PRIMARY COLORS BRIGHT ==========  -->
    <Color x:Key="PrimaryColorBright10">#1AAD8FE5</Color>
    <Color x:Key="PrimaryColorBright20">#33AD8FE5</Color>
    <Color x:Key="PrimaryColorBright30">#4DAD8FE5</Color>
    <Color x:Key="PrimaryColorBright40">#66AD8FE5</Color>
    <Color x:Key="PrimaryColorBright50">#80AD8FE5</Color>
    <Color x:Key="PrimaryColorBright60">#99AD8FE5</Color>
    <Color x:Key="PrimaryColorBright70">#B3AD8FE5</Color>
    <Color x:Key="PrimaryColorBright80">#CCAD8FE5</Color>
    <Color x:Key="PrimaryColorBright90">#E6AD8FE5</Color>
    <!--  ========== onPRIMARY COLORS ==========  -->
    <Color x:Key="OnPrimaryColor10">#1A563098</Color>
    <Color x:Key="OnPrimaryColor20">#33563098</Color>
    <Color x:Key="OnPrimaryColor30">#4D563098</Color>
    <Color x:Key="OnPrimaryColor40">#66563098</Color>
    <Color x:Key="OnPrimaryColor50">#80563098</Color>
    <Color x:Key="OnPrimaryColor60">#99563098</Color>
    <Color x:Key="OnPrimaryColor70">#B3563098</Color>
    <Color x:Key="OnPrimaryColor80">#CC563098</Color>
    <Color x:Key="OnPrimaryColor90">#E6563098</Color>
    <!--  ========== onPRIMARY COLORS DIM ==========  -->
    <Color x:Key="OnPrimaryColorDim10">#1A662CBB</Color>
    <Color x:Key="OnPrimaryColorDim20">#33662CBB</Color>
    <Color x:Key="OnPrimaryColorDim30">#4D662CBB</Color>
    <Color x:Key="OnPrimaryColorDim40">#66662CBB</Color>
    <Color x:Key="OnPrimaryColorDim50">#80662CBB</Color>
    <Color x:Key="OnPrimaryColorDim60">#99662CBB</Color>
    <Color x:Key="OnPrimaryColorDim70">#B3662CBB</Color>
    <Color x:Key="OnPrimaryColorDim80">#CC662CBB</Color>
    <Color x:Key="OnPrimaryColorDim90">#E6662CBB</Color>
    <!--  ========== onPRIMARY COLORS BRIGHT ==========  -->
    <Color x:Key="OnPrimaryColorBright10">#1A7D36E5</Color>
    <Color x:Key="OnPrimaryColorBright20">#337D36E5</Color>
    <Color x:Key="OnPrimaryColorBright30">#4D7D36E5</Color>
    <Color x:Key="OnPrimaryColorBright40">#667D36E5</Color>
    <Color x:Key="OnPrimaryColorBright50">#807D36E5</Color>
    <Color x:Key="OnPrimaryColorBright60">#997D36E5</Color>
    <Color x:Key="OnPrimaryColorBright70">#B37D36E5</Color>
    <Color x:Key="OnPrimaryColorBright80">#CC7D36E5</Color>
    <Color x:Key="OnPrimaryColorBright90">#E67D36E5</Color>

    <!--
        ========================================
        COMPONENT TOKEN COLORS
        ========================================
    -->
    <!--  ========== Standard Brushes ==========  -->
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="SurfaceDimBrush" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="SurfaceBrightBrush" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="InverseSurfaceBrush" Color="{StaticResource InverseSurface}" />
    <SolidColorBrush x:Key="InverseSurfaceDimBrush" Color="{StaticResource InverseSurfaceDim}" />
    <SolidColorBrush x:Key="InverseSurfaceBrightBrush" Color="red" />
    <SolidColorBrush x:Key="TextFillColorPrimaryBrush" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="TextFillColorSecondaryBrush" Color="{StaticResource TextFillColorSecondary}" />
    <SolidColorBrush x:Key="TextFillColorTertiaryBrush" Color="{StaticResource TextFillColorTertiary}" />
    <SolidColorBrush x:Key="TextFillColorDisabledBrush" Color="{StaticResource TextFillColorDisabled}" />

    <!--  ========== Primary Color Brushes ==========  -->
    <SolidColorBrush x:Key="PrimaryFixedBrush" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="PrimaryContainerBrush" Color="{StaticResource PrimaryContainer}" />

    <!--  ========== Main Window Color ==========  -->
    <SolidColorBrush x:Key="MainWindowBackgroundBase" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="MainWindowBackgoundAccent" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="MainWindowForegroundBase" Color="{StaticResource TextFillColorPrimary}" />

    <!--  ========== Confirmation Window Color ==========  -->
    <SolidColorBrush x:Key="ConfirmWindowBackground" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="ConfirmWindowBackgroundBase" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ConfirmWindowForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ConfirmWindowIconColor" Color="{StaticResource Warning}" />

    <!--  ========== Primary Button Color ==========  -->
    <SolidColorBrush x:Key="ButtonBackgroundPrimary" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="ButtonForegroundPrimary" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ButtonBorderPrimary" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="ButtonHoverPrimary" Color="{StaticResource PrimaryDim}" />

    <!--  ========== Secondary Button Color ==========  -->
    <SolidColorBrush x:Key="ButtonBackgroundSecondary" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="ButtonForegroundSecondary" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="ButtonBorderSecondary" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ButtonHoverSecondary" Color="{StaticResource SurfaceBright}" />

    <!--  ========== Container Button Color ==========  -->
    <SolidColorBrush x:Key="ButtonBackgroundContainer" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="ButtonBorderContainer" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="ButtonForegroundContainer" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="ButtonBorderHoverContainer" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ButtonBackgroundHoverContainer" Color="{StaticResource SurfaceBright}" />

    <!--  ========== Back Button Color ==========  -->
    <SolidColorBrush x:Key="BackBorderBackground" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="BackIconForeground" Color="{StaticResource OnPrimaryContainer}" />

    <!--  ========== Plus Button Color ==========  -->
    <SolidColorBrush x:Key="PlusBorderBackgroundBase" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="PlusForegroundBase" Color="{StaticResource OnPrimaryContainer}" />

    <!--  ========== Icon Buttons Color==========  -->
    <SolidColorBrush x:Key="IconButtonBackgroundBase" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="IconButtonBackgroundHover" Color="{StaticResource OnPrimary}" />

    <!--  ========== Icon Delete Buttons Color==========  -->
    <SolidColorBrush x:Key="DeleteButtonForeground" Color="{StaticResource Error}" />
    <SolidColorBrush x:Key="DeleteButtonForegroundHover" Color="{StaticResource OnError}" />

    <!--  ========== Icon Edit Buttons Color==========  -->
    <SolidColorBrush x:Key="EditButtonForeground" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="EditButtonForegroundHover" Color="{StaticResource OnPrimary}" />

    <!--  ========== Card Color ==========  -->
    <SolidColorBrush x:Key="CardBackgroundBase" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="CardForegroundBase" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="CardBackgroundAccent" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="CardBackgroundgOverlay" Color="{StaticResource SurfaceBright}" />

    <!--  ========== Undeline Text/Combo Box Color ==========  -->
    <SolidColorBrush x:Key="UnderLineForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="UnderLineBorder" Color="{StaticResource InverseSurfaceDim}" />
    <SolidColorBrush x:Key="UnderLineBorderHover" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="UnderLineFocuseColor" Color="{StaticResource OnPrimaryContainer}" />

    <!--  ========== TRadioButton Color ==========  -->
    <SolidColorBrush x:Key="RadioForegroundColor" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="RadioBackgroundColor" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="RadioBackgroundChecked" Color="{StaticResource PrimaryContainerDim}" />
    <SolidColorBrush x:Key="RadioBorderChecked" Color="{StaticResource OnPrimary}" />
    <SolidColorBrush x:Key="RadioHover" Color="{StaticResource PrimaryContainerBright}" />
    <SolidColorBrush x:Key="RadioBorderHover" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="RadioBorderBrush" Color="{StaticResource PrimaryContainer}" />

    <!--  ========== Toggle Switch Color ==========  -->
    <!--  Disabled State Color Not Implemented yet  -->
    <SolidColorBrush x:Key="ThumbOffBackground" Color="{DynamicResource InverseSurface}" />
    <SolidColorBrush x:Key="ThumbOffForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ThumbOnBackground" Color="{DynamicResource PrimaryFixed}" />
    <SolidColorBrush x:Key="ThumbOnForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="TrackOffBackground" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="TrackOnBackground" Color="{StaticResource PrimaryContainerDim}" />

    <!--  ==========  Chip Colors ==========  -->
    <SolidColorBrush x:Key="ChipBackgroundBase" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="ChipForegroundBase" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ChipBorderBase" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ChipBorderHover" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="ChipHoverBase" Color="{StaticResource PrimaryContainerBright}" />
    <SolidColorBrush x:Key="ChipBackgroundSelected" Color="{StaticResource PrimaryContainerDim}" />
    <SolidColorBrush x:Key="ChipBorderSelected" Color="{StaticResource Surface}" />

    <!--  ========== Years Chip Color ==========  -->
    <!--  Note if change color go Style and change rest color there  -->
    <SolidColorBrush x:Key="YearBackgroundNotPaid" Color="{StaticResource ChipBackgoundNotPaid}" />
    <SolidColorBrush x:Key="YearBorderNotPaid" Color="{StaticResource ChipBorderNotPaid}" />
    <SolidColorBrush x:Key="YearBackgroundPaid" Color="{StaticResource ChipBackgoundPaid}" />
    <SolidColorBrush x:Key="YearBorderPaid" Color="{StaticResource ChipBorderPaid}" />

    <!--  ==========  Scroll Component Colors ==========  -->
    <SolidColorBrush x:Key="ScrollThumbBackground" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ScrollThumbHoverBackground" Color="{StaticResource PrimaryContainerDim}" />
    <SolidColorBrush x:Key="ScrollThumbPressedBackground" Color="{StaticResource PrimaryContainer}" />
    <SolidColorBrush x:Key="ScrollTrackBackground" Color="{StaticResource SurfaceDim}" />

    <!--  ========== PopUp Flag Colors ==========  -->
    <SolidColorBrush x:Key="NormaleFlagPopup" Color="{StaticResource NormaleFlag}" />
    <SolidColorBrush x:Key="ImportantFlagPopup" Color="{StaticResource ImportantFlag}" />
    <SolidColorBrush x:Key="VeryImportantFlagPopup" Color="{StaticResource VeryImportantFlag}" />

    <!--  ========== Toast Notification Colors ==========  -->

    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource Success}" />
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource Info}" />
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource Warning}" />
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource Error}" />

    <!--  ==========  Enhanced Slider Component Colors ==========  -->
    <SolidColorBrush x:Key="SliderThumbBackground" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="SliderThumbBorder" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="SliderThumbHoverBackground" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="SliderThumbPressedBackground" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="SliderTrackBackground" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="SliderTrackBorder" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="SliderRippleBackground" Color="{StaticResource PrimaryContainer}" />
    <SolidColorBrush x:Key="SliderFocusGlow" Color="{StaticResource PrimaryContainer}" />
    <SolidColorBrush x:Key="SliderTickMarkBrush" Color="{StaticResource TextFillColorPrimary}" />

    <!--  ==========  Window Chrome Colors ==========  -->
    <SolidColorBrush x:Key="TitleBarBackground" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="TitleBarForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="WindowBorderBrush" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="WindowControlButtonHover" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="WindowControlButtonPressed" Color="{StaticResource InverseSurface}" />

    <SolidColorBrush x:Key="WindowButtonForegroud" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="WindowButtonHoverBackgroud" Color="{StaticResource InverseSurfaceDim}" />
    <SolidColorBrush x:Key="WindowButtonPressBackgroud" Color="{StaticResource InverseSurface}" />
    <SolidColorBrush x:Key="WindowCloseButtonHoverBackgroud" Color="{StaticResource Error}" />
    <SolidColorBrush x:Key="WindowCloseButtonPressBackgroud" Color="{StaticResource OnError}" />

    <!--  ==========  ToolTips Colors ==========  -->
    <SolidColorBrush x:Key="ToolTipsForegroud" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ToolTipsBackgroud" Color="{StaticResource InverseSurfaceDim}" />

</ResourceDictionary>
