using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Comprehensive text normalization utility for Arabic text processing, whitespace handling,
    /// diacritics removal, and search optimization. Provides consistent text processing for
    /// improved search accuracy and database element matching.
    /// </summary>
    public static class TextNormalizationHelper
    {
        #region Private Fields

        /// <summary>
        /// Regex pattern for normalizing multiple consecutive whitespace characters
        /// </summary>
        private static readonly Regex MultipleWhitespaceRegex = new(@"\s+", RegexOptions.Compiled);

        /// <summary>
        /// Regex pattern for Arabic diacritics removal
        /// </summary>
        private static readonly Regex ArabicDiacriticsRegex = new(@"[\u064B-\u065F\u0670\u06D6-\u06ED]", RegexOptions.Compiled);

        /// <summary>
        /// Dictionary for Arabic character normalization (variant forms to standard forms)
        /// </summary>
        private static readonly Dictionary<char, char> ArabicCharacterNormalization = new()
        {
            // Yeh variants
            { 'ي', 'ی' }, // Arabic Yeh to Farsi <PERSON>h
            { 'ى', 'ی' }, // Alef <PERSON> to Farsi <PERSON>h
            
            // Teh Marbuta variants
            { 'ة', 'ه' }, // Teh Marbuta to Heh
            
            // Kaf variants
            { 'ك', 'ک' }, // Arabic Kaf to Farsi Kaf
            
            // Waw variants
            { 'و', 'و' }, // Standard Waw (no change needed)
            
            // Alef variants
            { 'أ', 'ا' }, // Alef with Hamza above to Alef
            { 'إ', 'ا' }, // Alef with Hamza below to Alef
            { 'آ', 'ا' }, // Alef with Madda above to Alef
            { 'ٱ', 'ا' }, // Alef Wasla to Alef
        };

        /// <summary>
        /// Common Arabic stop words that can be ignored in search
        /// </summary>
        private static readonly HashSet<string> ArabicStopWords = new(StringComparer.OrdinalIgnoreCase)
        {
            "في", "من", "إلى", "على", "عن", "مع", "بين", "تحت", "فوق", "أمام", "خلف", "بعد", "قبل",
            "و", "أو", "لكن", "إذا", "عندما", "حيث", "كيف", "ماذا", "متى", "أين", "لماذا",
            "هذا", "هذه", "ذلك", "تلك", "التي", "الذي", "اللذان", "اللتان", "الذين", "اللواتي"
        };

        /// <summary>
        /// Arabic conjunctions that should be separated from adjacent words for better search matching
        /// </summary>
        private static readonly HashSet<string> ArabicConjunctions = new(StringComparer.OrdinalIgnoreCase)
        {
            "و", "أو", "لكن", "إذا", "عندما", "حيث", "كما", "لأن", "بل", "غير", "سوى"
        };

        /// <summary>
        /// Regex pattern for detecting Arabic conjunctions attached to words
        /// </summary>
        private static readonly Regex ArabicConjunctionRegex = new(@"(^|[\s])([وأ])([^\s\u0640-\u06FF]*)([^\s]*)", RegexOptions.Compiled);

        #endregion

        #region Public Methods

        /// <summary>
        /// Normalizes text for search operations with comprehensive whitespace and Arabic text processing.
        /// Handles multiple consecutive spaces, tabs, line breaks, and Arabic character variants.
        /// </summary>
        /// <param name="text">Text to normalize</param>
        /// <param name="preserveDiacritics">Whether to preserve Arabic diacritics (default: false)</param>
        /// <param name="preserveCase">Whether to preserve case (default: false)</param>
        /// <returns>Normalized text optimized for search operations</returns>
        public static string NormalizeForSearch(string text, bool preserveDiacritics = false, bool preserveCase = false)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return string.Empty;
            }

            try
            {
                // Step 1: Normalize whitespace
                string normalized = NormalizeWhitespace(text);

                // Step 2: Normalize Arabic conjunctions (separate attached conjunctions)
                normalized = NormalizeArabicConjunctions(normalized);

                // Step 3: Normalize Arabic characters
                normalized = NormalizeArabicCharacters(normalized);

                // Step 4: Remove diacritics if requested
                if (!preserveDiacritics)
                {
                    normalized = RemoveArabicDiacritics(normalized);
                }

                // Step 4: Normalize case if requested
                if (!preserveCase)
                {
                    normalized = normalized.ToLowerInvariant();
                }

                // Step 5: Final whitespace cleanup
                normalized = normalized.Trim();

                return normalized;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error normalizing text '{text}': {ex.Message}", "TextNormalizationHelper");
                return text?.Trim() ?? string.Empty;
            }
        }

        /// <summary>
        /// Normalizes Arabic conjunctions by ensuring proper spacing around them.
        /// Handles cases where conjunctions like 'و' are attached to words without spaces.
        /// For example: "وصانع" becomes "و صانع" for better search matching.
        /// </summary>
        /// <param name="text">Text containing Arabic conjunctions</param>
        /// <returns>Text with properly spaced Arabic conjunctions</returns>
        public static string NormalizeArabicConjunctions(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return string.Empty;
            }

            try
            {
                var normalized = new StringBuilder(text.Length + 10); // Extra space for potential additions

                for (int i = 0; i < text.Length; i++)
                {
                    char currentChar = text[i];

                    // Check if current character is a conjunction
                    if (currentChar == 'و' || currentChar == 'أ')
                    {
                        // Check if this is the start of a word (preceded by space or start of string)
                        bool isWordStart = i == 0 || char.IsWhiteSpace(text[i - 1]);

                        // Check if there's a non-space character immediately after
                        bool hasAttachedWord = i + 1 < text.Length && !char.IsWhiteSpace(text[i + 1]);

                        if (isWordStart && hasAttachedWord)
                        {
                            // Add the conjunction with a space after it
                            normalized.Append(currentChar);
                            normalized.Append(' ');
                        }
                        else
                        {
                            normalized.Append(currentChar);
                        }
                    }
                    else
                    {
                        normalized.Append(currentChar);
                    }
                }

                return normalized.ToString();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error normalizing Arabic conjunctions in text '{text}': {ex.Message}", "TextNormalizationHelper");
                return text;
            }
        }

        /// <summary>
        /// Normalizes whitespace characters including spaces, tabs, line breaks, and multiple consecutive whitespace.
        /// Converts all whitespace variations to single spaces for consistent matching.
        /// </summary>
        /// <param name="text">Text to normalize</param>
        /// <returns>Text with normalized whitespace</returns>
        public static string NormalizeWhitespace(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return string.Empty;
            }

            try
            {
                // Replace all whitespace characters (spaces, tabs, newlines, etc.) with single spaces
                // This handles \t, \n, \r, \u00A0 (non-breaking space), and other Unicode whitespace
                string normalized = MultipleWhitespaceRegex.Replace(text, " ");

                // Trim leading and trailing whitespace
                return normalized.Trim();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error normalizing whitespace in text '{text}': {ex.Message}", "TextNormalizationHelper");
                return text;
            }
        }

        /// <summary>
        /// Normalizes Arabic character variants to standard forms for consistent matching.
        /// Handles common Arabic character variations like ي/ی, ة/ه, ك/ک, etc.
        /// </summary>
        /// <param name="text">Text containing Arabic characters</param>
        /// <returns>Text with normalized Arabic characters</returns>
        public static string NormalizeArabicCharacters(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return string.Empty;
            }

            try
            {
                var normalized = new StringBuilder(text.Length);

                foreach (char c in text)
                {
                    // Check if character has a normalized form
                    if (ArabicCharacterNormalization.TryGetValue(c, out char normalizedChar))
                    {
                        normalized.Append(normalizedChar);
                    }
                    else
                    {
                        normalized.Append(c);
                    }
                }

                return normalized.ToString();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error normalizing Arabic characters in text '{text}': {ex.Message}", "TextNormalizationHelper");
                return text;
            }
        }

        /// <summary>
        /// Removes Arabic diacritics (Tashkeel) from text for improved search matching.
        /// Removes Fatha, Damma, Kasra, Sukun, Shadda, and other diacritical marks.
        /// </summary>
        /// <param name="text">Text containing Arabic diacritics</param>
        /// <returns>Text with diacritics removed</returns>
        public static string RemoveArabicDiacritics(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return string.Empty;
            }

            try
            {
                return ArabicDiacriticsRegex.Replace(text, string.Empty);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing Arabic diacritics from text '{text}': {ex.Message}", "TextNormalizationHelper");
                return text;
            }
        }

        /// <summary>
        /// Generates alternative search patterns for Arabic text with conjunction variations.
        /// Helps match text regardless of conjunction spacing variations.
        /// </summary>
        /// <param name="searchText">Original search text</param>
        /// <returns>Array of alternative search patterns</returns>
        public static string[] GenerateConjunctionVariations(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                return Array.Empty<string>();
            }

            var variations = new List<string> { searchText };

            try
            {
                // Add variation with normalized conjunctions
                string normalized = NormalizeArabicConjunctions(searchText);
                if (!variations.Contains(normalized, StringComparer.OrdinalIgnoreCase))
                {
                    variations.Add(normalized);
                }

                // Add variation with conjunctions removed
                string withoutConjunctions = RemoveArabicConjunctions(searchText);
                if (!string.IsNullOrWhiteSpace(withoutConjunctions) &&
                    !variations.Contains(withoutConjunctions, StringComparer.OrdinalIgnoreCase))
                {
                    variations.Add(withoutConjunctions);
                }

                // Add variation with conjunctions attached (reverse of normalization)
                string attached = AttachArabicConjunctions(searchText);
                if (!variations.Contains(attached, StringComparer.OrdinalIgnoreCase))
                {
                    variations.Add(attached);
                }

                return variations.ToArray();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating conjunction variations for '{searchText}': {ex.Message}", "TextNormalizationHelper");
                return new[] { searchText };
            }
        }

        /// <summary>
        /// Removes Arabic conjunctions from text for alternative matching.
        /// </summary>
        private static string RemoveArabicConjunctions(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            string[] words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var filteredWords = words.Where(word => !ArabicConjunctions.Contains(word)).ToArray();
            return string.Join(" ", filteredWords);
        }

        /// <summary>
        /// Attaches Arabic conjunctions to adjacent words (reverse of normalization).
        /// </summary>
        private static string AttachArabicConjunctions(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            return text.Replace(" و ", "و").Replace(" أو ", "أو");
        }

        /// <summary>
        /// Extracts individual words from Arabic text, handling conjunctions and compound words.
        /// Provides enhanced word extraction for better search term matching.
        /// </summary>
        /// <param name="text">Text to extract words from</param>
        /// <param name="includeConjunctions">Whether to include conjunctions as separate terms</param>
        /// <returns>Array of extracted words</returns>
        public static string[] ExtractArabicWords(string text, bool includeConjunctions = false)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return Array.Empty<string>();
            }

            try
            {
                // First normalize the text to separate conjunctions
                string normalized = NormalizeForSearch(text);

                // Split into words
                string[] words = normalized.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

                if (!includeConjunctions)
                {
                    // Filter out conjunctions
                    words = words.Where(word => !ArabicConjunctions.Contains(word)).ToArray();
                }

                return words;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error extracting Arabic words from text '{text}': {ex.Message}", "TextNormalizationHelper");
                return new[] { text.Trim() };
            }
        }

        /// <summary>
        /// Prepares search terms by splitting, normalizing, and filtering stop words.
        /// Optimizes search terms for better matching accuracy.
        /// </summary>
        /// <param name="searchText">Raw search input</param>
        /// <param name="removeStopWords">Whether to remove Arabic stop words (default: true)</param>
        /// <param name="minTermLength">Minimum term length to include (default: 2)</param>
        /// <returns>Array of normalized search terms</returns>
        public static string[] PrepareSearchTerms(string searchText, bool removeStopWords = true, int minTermLength = 2)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                return Array.Empty<string>();
            }

            try
            {
                // Normalize the entire search text first
                string normalized = NormalizeForSearch(searchText);

                // Split into individual terms
                string[] terms = normalized.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

                // Filter terms
                var filteredTerms = terms
                    .Where(term => term.Length >= minTermLength)
                    .Where(term => !removeStopWords || !ArabicStopWords.Contains(term))
                    .Distinct(StringComparer.OrdinalIgnoreCase)
                    .ToArray();

                return filteredTerms;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error preparing search terms from '{searchText}': {ex.Message}", "TextNormalizationHelper");
                return new[] { NormalizeForSearch(searchText) };
            }
        }

        /// <summary>
        /// Tests the Arabic conjunction normalization improvements with specific examples.
        /// This method validates that the search improvements work correctly.
        /// </summary>
        /// <returns>Test results with success/failure status</returns>
        public static string TestArabicConjunctionNormalization()
        {
            var testResults = new StringBuilder();
            testResults.AppendLine("=== Arabic Conjunction Normalization Test Results ===");

            try
            {
                // Test Case 1: Basic conjunction separation
                string test1Input = "وصانع";
                string test1Expected = "و صانع";
                string test1Result = NormalizeArabicConjunctions(test1Input);
                bool test1Pass = test1Result == test1Expected;
                testResults.AppendLine($"Test 1 - Basic conjunction separation:");
                testResults.AppendLine($"  Input: '{test1Input}'");
                testResults.AppendLine($"  Expected: '{test1Expected}'");
                testResults.AppendLine($"  Result: '{test1Result}'");
                testResults.AppendLine($"  Status: {(test1Pass ? "PASS" : "FAIL")}");
                testResults.AppendLine();

                // Test Case 2: Full search term normalization
                string test2Input = "حرفي نجار وصانع";
                string test2Result = NormalizeForSearch(test2Input);
                testResults.AppendLine($"Test 2 - Full search term normalization:");
                testResults.AppendLine($"  Input: '{test2Input}'");
                testResults.AppendLine($"  Result: '{test2Result}'");
                testResults.AppendLine();

                // Test Case 3: Target text normalization
                string test3Input = "حرفي نجار و صانع الأثاث";
                string test3Result = NormalizeForSearch(test3Input);
                testResults.AppendLine($"Test 3 - Target text normalization:");
                testResults.AppendLine($"  Input: '{test3Input}'");
                testResults.AppendLine($"  Result: '{test3Result}'");
                testResults.AppendLine();

                // Test Case 4: Conjunction variations
                string[] test4Variations = GenerateConjunctionVariations("حرفي نجار وصانع");
                testResults.AppendLine($"Test 4 - Conjunction variations:");
                testResults.AppendLine($"  Input: 'حرفي نجار وصانع'");
                testResults.AppendLine($"  Variations:");
                for (int i = 0; i < test4Variations.Length; i++)
                {
                    testResults.AppendLine($"    {i + 1}. '{test4Variations[i]}'");
                }
                testResults.AppendLine();

                // Test Case 5: Word extraction
                string[] test5Words = ExtractArabicWords("حرفي نجار وصانع", false);
                testResults.AppendLine($"Test 5 - Word extraction (without conjunctions):");
                testResults.AppendLine($"  Input: 'حرفي نجار وصانع'");
                testResults.AppendLine($"  Extracted words: [{string.Join(", ", test5Words.Select(w => $"'{w}'"))}]");
                testResults.AppendLine();

                testResults.AppendLine("=== Test Summary ===");
                testResults.AppendLine($"Basic conjunction test: {(test1Pass ? "PASS" : "FAIL")}");
                testResults.AppendLine("All normalization and variation tests completed.");
                testResults.AppendLine("Check the results above to verify correct behavior.");

                return testResults.ToString();
            }
            catch (Exception ex)
            {
                testResults.AppendLine($"ERROR during testing: {ex.Message}");
                return testResults.ToString();
            }
        }

        /// <summary>
        /// Checks if text contains Arabic characters.
        /// Useful for applying Arabic-specific processing only when needed.
        /// </summary>
        /// <param name="text">Text to check</param>
        /// <returns>True if text contains Arabic characters</returns>
        public static bool ContainsArabicCharacters(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return false;
            }

            try
            {
                return text.Any(c => 
                    (c >= '\u0600' && c <= '\u06FF') || // Arabic block
                    (c >= '\u0750' && c <= '\u077F') || // Arabic Supplement
                    (c >= '\uFB50' && c <= '\uFDFF') || // Arabic Presentation Forms-A
                    (c >= '\uFE70' && c <= '\uFEFF'));  // Arabic Presentation Forms-B
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking Arabic characters in text '{text}': {ex.Message}", "TextNormalizationHelper");
                return false;
            }
        }

        /// <summary>
        /// Generates search patterns for SQL LIKE queries with normalized text.
        /// Creates both exact and partial match patterns for comprehensive search.
        /// </summary>
        /// <param name="searchTerm">Search term to create patterns for</param>
        /// <returns>Dictionary of pattern types and their SQL LIKE patterns</returns>
        public static Dictionary<string, string> GenerateSearchPatterns(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new Dictionary<string, string>();
            }

            try
            {
                string normalized = NormalizeForSearch(searchTerm);
                var patterns = new Dictionary<string, string>
                {
                    ["exact"] = normalized,
                    ["starts_with"] = $"{normalized}%",
                    ["ends_with"] = $"%{normalized}",
                    ["contains"] = $"%{normalized}%",
                    ["word_boundary"] = $"% {normalized} %"
                };

                return patterns;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating search patterns for '{searchTerm}': {ex.Message}", "TextNormalizationHelper");
                return new Dictionary<string, string> { ["contains"] = $"%{searchTerm}%" };
            }
        }

        #endregion
    }
}
