using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Enhanced database service that integrates performance monitoring with database operations.
    /// Provides a wrapper around standard database operations with automatic performance tracking.
    /// </summary>
    public class EnhancedDatabaseService : IDisposable
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;
        private readonly DatabasePerformanceMonitoringService _performanceMonitoringService;
        private bool _disposed = false;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the EnhancedDatabaseService.
        /// </summary>
        /// <param name="databaseService">The core database service</param>
        /// <param name="performanceMonitoringService">The performance monitoring service</param>
        public EnhancedDatabaseService(
            DatabaseService databaseService,
            DatabasePerformanceMonitoringService performanceMonitoringService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _performanceMonitoringService = performanceMonitoringService ?? throw new ArgumentNullException(nameof(performanceMonitoringService));

            LoggingService.LogInfo("EnhancedDatabaseService initialized with performance monitoring", "EnhancedDatabaseService");
        }

        #endregion

        #region Enhanced Query Methods

        /// <summary>
        /// Executes a query with automatic performance monitoring.
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="query">The SQL query</param>
        /// <param name="parameters">Query parameters</param>
        /// <param name="operationName">Name of the operation for monitoring</param>
        /// <returns>Query results</returns>
        public async Task<IEnumerable<T>> QueryAsync<T>(
            string query, 
            object? parameters = null, 
            string operationName = "Query")
        {
            try
            {
                return await _performanceMonitoringService.ExecuteQueryWithMonitoringAsync<T>(
                    query, parameters, operationName);
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, 
                    "فشل في تنفيذ الاستعلام", 
                    "خطأ في قاعدة البيانات", 
                    LogLevel.Error, 
                    "EnhancedDatabaseService");
                throw;
            }
        }

        /// <summary>
        /// Executes a query and returns the first result with performance monitoring.
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="query">The SQL query</param>
        /// <param name="parameters">Query parameters</param>
        /// <param name="operationName">Name of the operation for monitoring</param>
        /// <returns>First result or default</returns>
        public async Task<T?> QueryFirstOrDefaultAsync<T>(
            string query, 
            object? parameters = null, 
            string operationName = "QueryFirstOrDefault")
        {
            var results = await QueryAsync<T>(query, parameters, operationName);
            return results.FirstOrDefault();
        }

        /// <summary>
        /// Executes a query and returns a single result with performance monitoring.
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="query">The SQL query</param>
        /// <param name="parameters">Query parameters</param>
        /// <param name="operationName">Name of the operation for monitoring</param>
        /// <returns>Single result</returns>
        public async Task<T> QuerySingleAsync<T>(
            string query, 
            object? parameters = null, 
            string operationName = "QuerySingle")
        {
            var results = await QueryAsync<T>(query, parameters, operationName);
            return results.Single();
        }

        /// <summary>
        /// Executes a scalar query with performance monitoring.
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="query">The SQL query</param>
        /// <param name="parameters">Query parameters</param>
        /// <param name="operationName">Name of the operation for monitoring</param>
        /// <returns>Scalar result</returns>
        public async Task<T> ExecuteScalarAsync<T>(
            string query, 
            object? parameters = null, 
            string operationName = "ExecuteScalar")
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var result = await connection.ExecuteScalarAsync<T>(query, parameters);

                // Record performance metric manually for scalar operations
                var metric = new QueryPerformanceMetric
                {
                    Query = query,
                    OperationName = operationName,
                    ExecutionTimeMs = 0, // Would need stopwatch for accurate timing
                    StartTime = DateTime.UtcNow,
                    EndTime = DateTime.UtcNow,
                    RowCount = 1
                };

                LoggingService.LogDebug($"Scalar query executed: {operationName}", "EnhancedDatabaseService");
                return result;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, 
                    "فشل في تنفيذ الاستعلام المفرد", 
                    "خطأ في قاعدة البيانات", 
                    LogLevel.Error, 
                    "EnhancedDatabaseService");
                throw;
            }
        }

        /// <summary>
        /// Executes a command with performance monitoring.
        /// </summary>
        /// <param name="command">The SQL command</param>
        /// <param name="parameters">Command parameters</param>
        /// <param name="operationName">Name of the operation for monitoring</param>
        /// <returns>Number of affected rows</returns>
        public async Task<int> ExecuteAsync(
            string command, 
            object? parameters = null, 
            string operationName = "Execute")
        {
            try
            {
                return await _performanceMonitoringService.ExecuteCommandWithMonitoringAsync(
                    command, parameters, operationName);
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, 
                    "فشل في تنفيذ الأمر", 
                    "خطأ في قاعدة البيانات", 
                    LogLevel.Error, 
                    "EnhancedDatabaseService");
                throw;
            }
        }

        #endregion

        #region Transaction Support

        /// <summary>
        /// Executes multiple operations within a transaction with performance monitoring.
        /// </summary>
        /// <param name="operations">List of database operations</param>
        /// <param name="operationName">Name of the transaction for monitoring</param>
        /// <returns>True if all operations succeeded</returns>
        public async Task<bool> ExecuteTransactionAsync(
            List<(string Command, object? Parameters)> operations,
            string operationName = "Transaction")
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();
                
                var totalAffectedRows = 0;
                var startTime = DateTime.UtcNow;

                try
                {
                    foreach (var (command, parameters) in operations)
                    {
                        var affectedRows = await connection.ExecuteAsync(command, parameters, transaction);
                        totalAffectedRows += affectedRows;
                    }

                    transaction.Commit();

                    // Record transaction performance metric
                    var metric = new QueryPerformanceMetric
                    {
                        Query = $"Transaction with {operations.Count} operations",
                        OperationName = operationName,
                        ExecutionTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds,
                        StartTime = startTime,
                        EndTime = DateTime.UtcNow,
                        RowCount = totalAffectedRows
                    };

                    LoggingService.LogInfo($"Transaction completed successfully: {operationName} ({operations.Count} operations, {totalAffectedRows} rows affected)", "EnhancedDatabaseService");
                    return true;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, 
                    "فشل في تنفيذ المعاملة", 
                    "خطأ في قاعدة البيانات", 
                    LogLevel.Error, 
                    "EnhancedDatabaseService");
                return false;
            }
        }

        #endregion

        #region Performance Monitoring Access

        /// <summary>
        /// Gets performance metrics for a specific time period.
        /// </summary>
        /// <param name="fromTime">Start time (UTC)</param>
        /// <param name="toTime">End time (UTC)</param>
        /// <returns>Performance metrics</returns>
        public List<QueryPerformanceMetric> GetPerformanceMetrics(DateTime fromTime, DateTime toTime)
        {
            return _performanceMonitoringService.GetPerformanceMetrics(fromTime, toTime);
        }

        /// <summary>
        /// Gets slow queries above the specified threshold.
        /// </summary>
        /// <param name="thresholdMs">Execution time threshold in milliseconds</param>
        /// <returns>Slow query metrics</returns>
        public List<QueryPerformanceMetric> GetSlowQueries(int thresholdMs = 100)
        {
            return _performanceMonitoringService.GetSlowQueries(thresholdMs);
        }

        /// <summary>
        /// Generates a performance report for the specified time period.
        /// </summary>
        /// <param name="fromTime">Start time (UTC)</param>
        /// <param name="toTime">End time (UTC)</param>
        /// <returns>Performance report</returns>
        public PerformanceReport GeneratePerformanceReport(DateTime fromTime, DateTime toTime)
        {
            return _performanceMonitoringService.GeneratePerformanceReport(fromTime, toTime);
        }

        /// <summary>
        /// Analyzes index effectiveness and provides recommendations.
        /// </summary>
        /// <returns>Index effectiveness analysis</returns>
        public async Task<IndexEffectivenessAnalysis> AnalyzeIndexEffectivenessAsync()
        {
            return await _performanceMonitoringService.AnalyzeIndexEffectivenessAsync();
        }

        /// <summary>
        /// Performs comprehensive database maintenance operations.
        /// </summary>
        /// <returns>Maintenance operation results</returns>
        public async Task<MaintenanceResult> PerformDatabaseMaintenanceAsync()
        {
            return await _performanceMonitoringService.PerformDatabaseMaintenanceAsync();
        }

        #endregion

        #region Core Database Service Access

        /// <summary>
        /// Creates a new database connection.
        /// </summary>
        /// <returns>Database connection</returns>
        public SqliteConnection CreateConnection()
        {
            return _databaseService.CreateConnection();
        }

        /// <summary>
        /// Gets the database file path.
        /// </summary>
        /// <returns>Database file path</returns>
        public string GetDatabasePath()
        {
            return _databaseService.GetDatabasePath();
        }

        /// <summary>
        /// Checks if the database file exists.
        /// </summary>
        /// <returns>True if database exists</returns>
        public bool DatabaseExists()
        {
            return _databaseService.DatabaseExists();
        }

        #endregion

        #region Convenience Methods

        /// <summary>
        /// Gets a summary of recent database performance.
        /// </summary>
        /// <param name="hoursBack">Number of hours to look back</param>
        /// <returns>Performance summary</returns>
        public async Task<string> GetPerformanceSummaryAsync(int hoursBack = 24)
        {
            try
            {
                var fromTime = DateTime.UtcNow.AddHours(-hoursBack);
                var toTime = DateTime.UtcNow;
                
                var report = GeneratePerformanceReport(fromTime, toTime);
                
                if (report.TotalQueries == 0)
                {
                    return $"No database activity in the last {hoursBack} hours.";
                }

                return $"Database Performance Summary (Last {hoursBack}h):\n" +
                       $"• Total Queries: {report.TotalQueries}\n" +
                       $"• Average Execution Time: {report.AverageExecutionTimeMs:F1}ms\n" +
                       $"• Slow Queries: {report.SlowQueryCount} ({report.SlowQueryPercentage:F1}%)\n" +
                       $"• Index Usage: {report.IndexUsageCount} queries ({report.IndexUsagePercentage:F1}%)\n" +
                       $"• Table Scans: {report.TableScanCount} queries";
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to generate performance summary: {ex.Message}", "EnhancedDatabaseService");
                return "Failed to generate performance summary.";
            }
        }

        /// <summary>
        /// Logs current performance statistics to the logging service.
        /// </summary>
        public async Task LogPerformanceStatisticsAsync()
        {
            try
            {
                var summary = await GetPerformanceSummaryAsync(1); // Last hour
                LoggingService.LogInfo($"Performance Statistics: {summary}", "EnhancedDatabaseService");

                var slowQueries = GetSlowQueries();
                if (slowQueries.Any())
                {
                    LoggingService.LogWarning($"Found {slowQueries.Count} slow queries in recent activity", "EnhancedDatabaseService");
                    
                    foreach (var slowQuery in slowQueries.Take(5)) // Log top 5 slow queries
                    {
                        LoggingService.LogWarning(
                            $"Slow Query ({slowQuery.ExecutionTimeMs}ms): {slowQuery.OperationName} - {slowQuery.Query.Substring(0, Math.Min(100, slowQuery.Query.Length))}...",
                            "EnhancedDatabaseService");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to log performance statistics: {ex.Message}", "EnhancedDatabaseService");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the service resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _performanceMonitoringService?.Dispose();
                    _databaseService?.Dispose();
                    LoggingService.LogInfo("EnhancedDatabaseService disposed", "EnhancedDatabaseService");
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
