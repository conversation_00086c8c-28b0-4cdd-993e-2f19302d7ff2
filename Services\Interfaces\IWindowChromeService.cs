using System;
using System.Windows;
using System.Windows.Shell;
using UFU2.Models;

namespace UFU2.Services.Interfaces
{
    /// <summary>
    /// Service interface for managing custom window chrome functionality
    /// </summary>
    public interface IWindowChromeService : IDisposable
    {
        /// <summary>
        /// Applies custom chrome to the specified window
        /// </summary>
        /// <param name="window">The window to apply chrome to</param>
        void ApplyCustomChrome(Window window);

        /// <summary>
        /// Updates the chrome theme based on the current application theme
        /// </summary>
        /// <param name="theme">The theme to apply</param>
        void UpdateTheme(ApplicationTheme theme);

        /// <summary>
        /// Configures window chrome with the specified ViewModel
        /// </summary>
        /// <param name="window">The target window</param>
        /// <param name="viewModel">The ViewModel containing chrome configuration</param>
        void ConfigureWindowChrome(Window window, object viewModel);

        /// <summary>
        /// Creates a new WindowChrome instance with default configuration
        /// </summary>
        /// <returns>Configured WindowChrome instance</returns>
        WindowChrome CreateWindowChrome();

        /// <summary>
        /// Gets the current window chrome configuration
        /// </summary>
        /// <returns>Current WindowChromeConfiguration</returns>
        WindowChromeConfiguration GetConfiguration();

        /// <summary>
        /// Gets the current window chrome theme
        /// </summary>
        /// <returns>Current WindowChromeTheme</returns>
        WindowChromeTheme GetTheme();
    }
}