using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Performance dashboard service for UFU2 application.
    /// Provides comprehensive performance insights and monitoring across all optimization services.
    /// Implements Phase 2C comprehensive performance tracking and reporting.
    /// </summary>
    public class PerformanceDashboardService : IDisposable
    {
        #region Private Fields

        private readonly Timer _dashboardUpdateTimer;
        private readonly Timer _reportingTimer;
        private bool _disposed = false;

        // Configuration
        private readonly TimeSpan _updateInterval = TimeSpan.FromMinutes(1);
        private readonly TimeSpan _reportingInterval = TimeSpan.FromMinutes(10);

        // Performance tracking
        private PerformanceDashboard _currentDashboard = new PerformanceDashboard();

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the PerformanceDashboardService.
        /// </summary>
        public PerformanceDashboardService()
        {
            // Start dashboard update timer
            _dashboardUpdateTimer = new Timer(
                UpdateDashboard,
                null,
                _updateInterval,
                _updateInterval);

            // Start reporting timer
            _reportingTimer = new Timer(
                GenerateComprehensiveReport,
                null,
                _reportingInterval,
                _reportingInterval);

            LoggingService.LogInfo("PerformanceDashboardService initialized", "PerformanceDashboardService");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the current performance dashboard.
        /// </summary>
        /// <returns>Current performance dashboard</returns>
        public PerformanceDashboard GetCurrentDashboard()
        {
            UpdateDashboard(null);
            return _currentDashboard;
        }

        /// <summary>
        /// Gets performance improvement summary compared to baseline.
        /// </summary>
        /// <returns>Performance improvement summary</returns>
        public PerformanceImprovementSummary GetImprovementSummary()
        {
            var dashboard = GetCurrentDashboard();
            
            // Baseline values from Phase 2C roadmap
            const double baselineDialogInitTimeMs = 250.0; // Middle of 200-300ms range
            const double baselineMemoryUsageMB = 400.0;
            const double targetImprovementPercentage = 65.0; // Target: 60-70%

            var currentDialogTime = dashboard.ViewLoadingMetrics?.AverageLoadingTimeMs ?? baselineDialogInitTimeMs;
            var currentMemoryUsage = dashboard.MemoryOptimizationMetrics?.CurrentMemoryUsageMB ?? baselineMemoryUsageMB;

            var dialogTimeImprovement = Math.Max(0, (baselineDialogInitTimeMs - currentDialogTime) / baselineDialogInitTimeMs * 100);
            var memoryEfficiency = Math.Max(0, (baselineMemoryUsageMB - currentMemoryUsage) / baselineMemoryUsageMB * 100);

            return new PerformanceImprovementSummary
            {
                DialogInitializationImprovement = dialogTimeImprovement,
                MemoryEfficiencyImprovement = memoryEfficiency,
                OverallPerformanceScore = CalculateOverallPerformanceScore(dashboard),
                TargetAchievement = dialogTimeImprovement >= targetImprovementPercentage,
                BaselineDialogTimeMs = baselineDialogInitTimeMs,
                CurrentDialogTimeMs = currentDialogTime,
                BaselineMemoryUsageMB = baselineMemoryUsageMB,
                CurrentMemoryUsageMB = currentMemoryUsage,
                Phase2CTargetsMet = ValidatePhase2CTargets(dashboard)
            };
        }

        /// <summary>
        /// Generates a comprehensive performance report.
        /// </summary>
        /// <returns>Comprehensive performance report</returns>
        public string GeneratePerformanceReport()
        {
            var dashboard = GetCurrentDashboard();
            var improvement = GetImprovementSummary();

            var report = $@"
=== UFU2 Performance Optimization Report ===
Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC

PHASE 2C PERFORMANCE TARGETS:
✓ Target: 60-70% improvement in dialog initialization
✓ Current Achievement: {improvement.DialogInitializationImprovement:F1}%
✓ Target Met: {(improvement.TargetAchievement ? "YES" : "NO")}

DIALOG PERFORMANCE:
• Baseline: {improvement.BaselineDialogTimeMs:F0}ms
• Current: {improvement.CurrentDialogTimeMs:F1}ms
• Improvement: {improvement.DialogInitializationImprovement:F1}%
• Fast Load Ratio: {dashboard.ViewLoadingMetrics?.FastLoadRatio:P1}
• Background Load Ratio: {dashboard.ViewLoadingMetrics?.BackgroundLoadRatio:P1}

MEMORY OPTIMIZATION:
• Current Usage: {improvement.CurrentMemoryUsageMB:F1}MB
• Memory Efficiency: {improvement.MemoryEfficiencyImprovement:F1}%
• Views Tracked: {dashboard.MemoryOptimizationMetrics?.ActiveTrackedViews}
• Memory Freed: {dashboard.MemoryOptimizationMetrics?.MemoryFreedMB}MB

BACKGROUND PROCESSING:
• Active Tasks: {dashboard.BackgroundProcessingMetrics?.ActiveTasksCount}
• Completed Tasks: {dashboard.BackgroundProcessingMetrics?.CompletedTasksCount}
• Average Processing: {dashboard.BackgroundProcessingMetrics?.AverageProcessingTimeMs}ms
• Queue Length: {dashboard.BackgroundProcessingMetrics?.QueueLength}

OVERALL PERFORMANCE SCORE: {improvement.OverallPerformanceScore:F1}/100

PHASE 2C TARGETS STATUS:
{FormatTargetsStatus(improvement.Phase2CTargetsMet)}

=== End Report ===
";

            return report;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Updates the performance dashboard with current metrics.
        /// </summary>
        private void UpdateDashboard(object? state)
        {
            try
            {
                var dashboard = new PerformanceDashboard
                {
                    LastUpdated = DateTime.UtcNow
                };

                // Collect view loading metrics
                var viewLoadingService = ServiceLocator.GetService<ViewLoadingMonitoringService>();
                if (viewLoadingService != null)
                {
                    dashboard.ViewLoadingMetrics = viewLoadingService.GetPerformanceSummary();
                }

                // Collect memory optimization metrics
                var memoryService = ServiceLocator.GetService<ViewMemoryOptimizationService>();
                if (memoryService != null)
                {
                    dashboard.MemoryOptimizationMetrics = memoryService.GetOptimizationStatistics();
                }

                // Collect background processing metrics
                var backgroundService = ServiceLocator.GetService<BackgroundViewInitializationService>();
                if (backgroundService != null)
                {
                    dashboard.BackgroundProcessingMetrics = backgroundService.GetPerformanceStatistics();
                }

                // Collect UI responsiveness metrics
                var uiService = ServiceLocator.GetService<UIResponsivenessMonitoringService>();
                if (uiService != null)
                {
                    dashboard.UIResponsivenessMetrics = uiService.GetCurrentResponsivenessInfo();
                }

                _currentDashboard = dashboard;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating performance dashboard: {ex.Message}", "PerformanceDashboardService");
            }
        }

        /// <summary>
        /// Calculates overall performance score.
        /// </summary>
        private double CalculateOverallPerformanceScore(PerformanceDashboard dashboard)
        {
            var scores = new List<double>();

            // View loading score (30%)
            if (dashboard.ViewLoadingMetrics != null)
            {
                scores.Add(dashboard.ViewLoadingMetrics.PerformanceScore * 0.3);
            }

            // Memory optimization score (25%)
            if (dashboard.MemoryOptimizationMetrics != null)
            {
                var memoryScore = dashboard.MemoryOptimizationMetrics.IsMemoryPressureHigh ? 50.0 : 100.0;
                memoryScore *= dashboard.MemoryOptimizationMetrics.MemoryOptimizationRatio;
                scores.Add(memoryScore * 0.25);
            }

            // Background processing score (25%)
            if (dashboard.BackgroundProcessingMetrics != null)
            {
                var bgScore = dashboard.BackgroundProcessingMetrics.TotalTasksProcessed > 0 ? 
                    Math.Min(100, 100 - dashboard.BackgroundProcessingMetrics.AverageProcessingTimeMs / 10) : 100;
                scores.Add(bgScore * 0.25);
            }

            // UI responsiveness score (20%)
            if (dashboard.UIResponsivenessMetrics != null)
            {
                // Calculate UI score based on responsiveness level (lower is better)
                var uiScore = dashboard.UIResponsivenessMetrics.CurrentResponsivenessLevel switch
                {
                    UIResponsivenessLevel.Excellent => 100,
                    UIResponsivenessLevel.Good => 80,
                    UIResponsivenessLevel.Fair => 60,
                    UIResponsivenessLevel.Poor => 40,
                    UIResponsivenessLevel.Critical => 20,
                    _ => 50
                };
                scores.Add(uiScore * 0.2);
            }

            return scores.Count > 0 ? scores.Sum() : 0;
        }

        /// <summary>
        /// Validates Phase 2C targets.
        /// </summary>
        private Phase2CTargetsStatus ValidatePhase2CTargets(PerformanceDashboard dashboard)
        {
            return new Phase2CTargetsStatus
            {
                DialogInitializationTarget = dashboard.ViewLoadingMetrics?.AverageLoadingTimeMs <= 150,
                BackgroundProcessingTarget = dashboard.BackgroundProcessingMetrics?.AverageProcessingTimeMs <= 500,
                MemoryOptimizationTarget = dashboard.MemoryOptimizationMetrics?.CurrentMemoryUsageMB <= 350,
                UIResponsivenessTarget = dashboard.UIResponsivenessMetrics?.CurrentResponsivenessLevel <= UIResponsivenessLevel.Good
            };
        }

        /// <summary>
        /// Formats targets status for reporting.
        /// </summary>
        private string FormatTargetsStatus(Phase2CTargetsStatus targets)
        {
            return $@"• Dialog Initialization (≤150ms): {(targets.DialogInitializationTarget ? "✓ MET" : "✗ NOT MET")}
• Background Processing (≤500ms): {(targets.BackgroundProcessingTarget ? "✓ MET" : "✗ NOT MET")}
• Memory Optimization (≤350MB): {(targets.MemoryOptimizationTarget ? "✓ MET" : "✗ NOT MET")}
• UI Responsiveness (Good+): {(targets.UIResponsivenessTarget ? "✓ MET" : "✗ NOT MET")}";
        }

        /// <summary>
        /// Generates comprehensive performance report.
        /// </summary>
        private void GenerateComprehensiveReport(object? state)
        {
            try
            {
                var report = GeneratePerformanceReport();
                LoggingService.LogInfo($"Performance Dashboard Report:\n{report}", "PerformanceDashboardService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating comprehensive report: {ex.Message}", "PerformanceDashboardService");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the PerformanceDashboardService.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _dashboardUpdateTimer?.Dispose();
                _reportingTimer?.Dispose();
                _disposed = true;

                LoggingService.LogInfo("PerformanceDashboardService disposed", "PerformanceDashboardService");
            }
        }

        #endregion
    }

    /// <summary>
    /// Comprehensive performance dashboard data.
    /// </summary>
    public class PerformanceDashboard
    {
        public DateTime LastUpdated { get; set; }
        public ViewLoadingPerformanceSummary? ViewLoadingMetrics { get; set; }
        public ViewMemoryOptimizationStatistics? MemoryOptimizationMetrics { get; set; }
        public BackgroundProcessingStatistics? BackgroundProcessingMetrics { get; set; }
        public UIResponsivenessInfo? UIResponsivenessMetrics { get; set; }
    }

    /// <summary>
    /// Performance improvement summary compared to baseline.
    /// </summary>
    public class PerformanceImprovementSummary
    {
        public double DialogInitializationImprovement { get; set; }
        public double MemoryEfficiencyImprovement { get; set; }
        public double OverallPerformanceScore { get; set; }
        public bool TargetAchievement { get; set; }
        public double BaselineDialogTimeMs { get; set; }
        public double CurrentDialogTimeMs { get; set; }
        public double BaselineMemoryUsageMB { get; set; }
        public double CurrentMemoryUsageMB { get; set; }
        public Phase2CTargetsStatus Phase2CTargetsMet { get; set; } = new Phase2CTargetsStatus();
    }

    /// <summary>
    /// Phase 2C targets validation status.
    /// </summary>
    public class Phase2CTargetsStatus
    {
        public bool DialogInitializationTarget { get; set; }
        public bool BackgroundProcessingTarget { get; set; }
        public bool MemoryOptimizationTarget { get; set; }
        public bool UIResponsivenessTarget { get; set; }
    }
}
