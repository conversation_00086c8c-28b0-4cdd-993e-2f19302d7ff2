using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Threading;
using UFU2.Common;
using UFU2.Services;

namespace UFU2.Views.NewClient
{
    /// <summary>
    /// Interaction logic for NActivityTabView.xaml
    /// A UserControl that provides activity information management with tabbed interface.
    /// Integrates with UFU2 design system and follows MaterialDesign patterns.
    /// Supports Arabic RTL layout and proper accessibility features.
    /// Uses MVVM data binding with NewClientViewModel for activity management.
    /// OPTIMIZED: Enhanced with tab switching debouncing and cached configurations for Phase 2B UI optimizations.
    /// </summary>
    public partial class NActivityTabView : UserControl
    {
        #region Private Fields for Optimization

        // Debouncing for tab switching to prevent rapid state changes
        private readonly DispatcherTimer _tabSwitchDebounceTimer;
        private string _pendingActivityType = string.Empty;
        private const int TabSwitchDebounceMs = 150; // 150ms debounce delay

        // Cached activity type configurations for performance
        private static readonly Dictionary<string, ActivityTabConfig> _activityTabConfigs = new()
        {
            { "MainCommercial", new ActivityTabConfig("MainCommercial", "النشاط التجاري الرئيسي", 0) },
            { "SecondaryCommercial", new ActivityTabConfig("SecondaryCommercial", "النشاط التجاري الثانوي", 1) },
            { "Craft", new ActivityTabConfig("Craft", "النشاط الحرفي", 2) },
            { "Professional", new ActivityTabConfig("Professional", "النشاط المهني", 3) }
        };

        // Performance monitoring
        private static int _tabSwitchCount = 0;
        private static int _debouncedSwitchCount = 0;
        private string _currentActivityType = "MainCommercial";

        #endregion

        #region Helper Classes

        /// <summary>
        /// Configuration data for activity tabs to improve performance.
        /// </summary>
        private class ActivityTabConfig
        {
            public string ActivityType { get; }
            public string DisplayName { get; }
            public int TabIndex { get; }

            public ActivityTabConfig(string activityType, string displayName, int tabIndex)
            {
                ActivityType = activityType;
                DisplayName = displayName;
                TabIndex = tabIndex;
            }
        }

        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the NActivityTabView class.
        /// Sets up the activity form with proper Arabic RTL support and UFU2 styling.
        /// The DataContext should be set by the parent view (NewClientView) to ensure
        /// proper data sharing between the activity tabs and command buttons.
        /// OPTIMIZED: Enhanced with debounce timer initialization.
        /// </summary>
        public NActivityTabView()
        {
            try
            {
                InitializeComponent();

                // Initialize debounce timer for tab switching optimization
                _tabSwitchDebounceTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(TabSwitchDebounceMs)
                };
                _tabSwitchDebounceTimer.Tick += TabSwitchDebounceTimer_Tick;

                // Set up accessibility properties
                AutomationProperties.SetName(this, "Activity Information Form");
                AutomationProperties.SetHelpText(this, "Form for managing client activity information with tabbed interface");

                // Subscribe to events for proper cleanup
                Loaded += NActivityTabView_Loaded;
                Unloaded += NActivityTabView_Unloaded;

                LoggingService.LogInfo("NActivityTabView initialized with optimization", nameof(NActivityTabView));
            }
            catch (Exception ex)
            {
                ErrorManager.LogException(ex, LogLevel.Error, nameof(NActivityTabView));
                ErrorManager.ShowUserError("حدث خطأ أثناء تحميل نموذج معلومات النشاط", "خطأ");
            }
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// Handles the Loaded event for the NActivityTabView.
        /// Sets up initial focus and accessibility features.
        /// </summary>
        private void NActivityTabView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Set initial focus to the first activity tab for accessibility
                MainCommercialActivityTab?.Focus();
                
                LoggingService.LogInfo("NActivityTabView loaded successfully", nameof(NActivityTabView));
            }
            catch (Exception ex)
            {
                ErrorManager.LogException(ex, LogLevel.Warning, nameof(NActivityTabView));
            }
        }

        /// <summary>
        /// Handles the Unloaded event for the NActivityTabView.
        /// Performs cleanup operations to prevent memory leaks.
        /// </summary>
        private void NActivityTabView_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Unsubscribe from events to prevent memory leaks
                Loaded -= NActivityTabView_Loaded;
                Unloaded -= NActivityTabView_Unloaded;
                
                LoggingService.LogInfo("NActivityTabView unloaded and cleaned up", nameof(NActivityTabView));
            }
            catch (Exception ex)
            {
                ErrorManager.LogException(ex, LogLevel.Warning, nameof(NActivityTabView));
            }
        }
        #endregion

        #region Public Properties
        /// <summary>
        /// Gets or sets the currently selected activity type.
        /// This property can be bound to from the parent ViewModel.
        /// </summary>
        public string SelectedActivityType
        {
            get { return (string)GetValue(SelectedActivityTypeProperty); }
            set { SetValue(SelectedActivityTypeProperty, value); }
        }

        /// <summary>
        /// Dependency property for SelectedActivityType.
        /// </summary>
        public static readonly DependencyProperty SelectedActivityTypeProperty =
            DependencyProperty.Register(nameof(SelectedActivityType), typeof(string), typeof(NActivityTabView),
                new PropertyMetadata("MainCommercial", OnSelectedActivityTypeChanged));

        /// <summary>
        /// Gets or sets whether the Activity ID Format is enabled.
        /// This property can be bound to from the parent ViewModel.
        /// </summary>
        public bool IsActivityIdFormatEnabled
        {
            get { return (bool)GetValue(IsActivityIdFormatEnabledProperty); }
            set { SetValue(IsActivityIdFormatEnabledProperty, value); }
        }

        /// <summary>
        /// Dependency property for IsActivityIdFormatEnabled.
        /// </summary>
        public static readonly DependencyProperty IsActivityIdFormatEnabledProperty =
            DependencyProperty.Register(nameof(IsActivityIdFormatEnabled), typeof(bool), typeof(NActivityTabView),
                new PropertyMetadata(false));
        #endregion

        #region Private Methods
        /// <summary>
        /// Handles changes to the SelectedActivityType property.
        /// OPTIMIZED: Uses debouncing to prevent rapid tab switching.
        /// </summary>
        private static void OnSelectedActivityTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is NActivityTabView view && e.NewValue is string activityType)
            {
                System.Threading.Interlocked.Increment(ref _tabSwitchCount);

                // Use debouncing for better performance
                view._pendingActivityType = activityType;
                view._tabSwitchDebounceTimer.Stop();
                view._tabSwitchDebounceTimer.Start();
            }
        }

        /// <summary>
        /// Updates the selected tab based on the activity type.
        /// OPTIMIZED: Reduced UI updates and improved performance.
        /// </summary>
        private void UpdateSelectedTab(string activityType)
        {
            try
            {
                // Skip update if already selected (avoid unnecessary UI updates)
                if (_currentActivityType == activityType)
                {
                    return;
                }

                // Use cached configuration for better performance
                if (!_activityTabConfigs.TryGetValue(activityType, out var config))
                {
                    // Fallback to MainCommercial if invalid activity type
                    config = _activityTabConfigs["MainCommercial"];
                    activityType = "MainCommercial";
                }

                // Get all radio buttons for efficient updating
                var radioButtons = new[]
                {
                    MainCommercialActivityTab,
                    SecondaryCommercialActivityTab,
                    CraftActivityTab,
                    ProfessionalActivityTab
                };

                // Update only the necessary radio buttons to minimize UI updates
                for (int i = 0; i < radioButtons.Length; i++)
                {
                    bool shouldBeChecked = i == config.TabIndex;
                    if (radioButtons[i].IsChecked != shouldBeChecked)
                    {
                        radioButtons[i].IsChecked = shouldBeChecked;
                    }
                }

                _currentActivityType = activityType;
                System.Threading.Interlocked.Increment(ref _debouncedSwitchCount);
            }
            catch (Exception ex)
            {
                ErrorManager.LogException(ex, LogLevel.Warning, nameof(NActivityTabView));
            }
        }

        /// <summary>
        /// Handles the debounce timer tick to process batched tab switching.
        /// OPTIMIZED: Reduces tab switching frequency and improves performance.
        /// </summary>
        private void TabSwitchDebounceTimer_Tick(object? sender, EventArgs e)
        {
            _tabSwitchDebounceTimer.Stop();

            try
            {
                if (!string.IsNullOrEmpty(_pendingActivityType))
                {
                    UpdateSelectedTab(_pendingActivityType);
                    LoggingService.LogDebug($"Activity tab switched to: {_pendingActivityType}", nameof(NActivityTabView));
                    _pendingActivityType = string.Empty;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in tab switch debounce timer: {ex.Message}", nameof(NActivityTabView));
            }
        }

        /// <summary>
        /// Gets performance statistics for the NActivityTabView component.
        /// Used for Phase 2B UI optimization monitoring.
        /// </summary>
        /// <returns>Performance statistics including tab switch counts and debounce effectiveness</returns>
        public static (int TabSwitchCount, int DebouncedSwitchCount, double DebounceEffectiveness) GetPerformanceStats()
        {
            var tabSwitchCount = _tabSwitchCount;
            var debouncedSwitchCount = _debouncedSwitchCount;
            var debounceEffectiveness = tabSwitchCount > 0 ? (double)debouncedSwitchCount / tabSwitchCount : 0.0;

            return (tabSwitchCount, debouncedSwitchCount, debounceEffectiveness);
        }

        /// <summary>
        /// Resets performance counters for testing and monitoring.
        /// </summary>
        public static void ResetPerformanceCounters()
        {
            System.Threading.Interlocked.Exchange(ref _tabSwitchCount, 0);
            System.Threading.Interlocked.Exchange(ref _debouncedSwitchCount, 0);
        }

        /// <summary>
        /// Gets the cached activity type configurations for external use.
        /// </summary>
        /// <returns>Dictionary of activity type configurations</returns>
        public static IReadOnlyDictionary<string, string> GetActivityTypeDisplayNames()
        {
            var displayNames = new Dictionary<string, string>();
            foreach (var config in _activityTabConfigs)
            {
                displayNames[config.Key] = config.Value.DisplayName;
            }
            return displayNames;
        }

        #endregion
    }
}
