using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the DuplicateClientDetectionDialog providing MVVM data binding and command handling.
    /// Manages duplicate client selection, data loading, and dialog interaction.
    /// Inherits from BaseViewModel and follows UFU2 architectural patterns.
    /// </summary>
    public class DuplicateClientDetectionDialogViewModel : BaseViewModel
    {
        #region Private Fields

        private string _headerText = string.Empty;
        private string _contentMessage = string.Empty;
        private ObservableCollection<DuplicateClientData> _duplicateClients = new ObservableCollection<DuplicateClientData>();
        private DuplicateClientData? _selectedClient;
        private bool _isLoading;
        private readonly DuplicateClientDetectionService _duplicateDetectionService;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the header text for the dialog.
        /// Format: "عملاء بنفس الاسم {NameFr}"
        /// </summary>
        public string HeaderText
        {
            get => _headerText;
            set => SetProperty(ref _headerText, value);
        }

        /// <summary>
        /// Gets or sets the content message for the dialog.
        /// </summary>
        public string ContentMessage
        {
            get => _contentMessage;
            set => SetProperty(ref _contentMessage, value);
        }

        /// <summary>
        /// Gets the collection of duplicate clients found.
        /// </summary>
        public ObservableCollection<DuplicateClientData> DuplicateClients
        {
            get => _duplicateClients;
            private set => SetProperty(ref _duplicateClients, value);
        }

        /// <summary>
        /// Gets or sets the currently selected duplicate client.
        /// </summary>
        public DuplicateClientData? SelectedClient
        {
            get => _selectedClient;
            set
            {
                if (SetProperty(ref _selectedClient, value))
                {
                    // Update command states when selection changes
                    UseSelectedClientCommand?.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets whether the dialog is in loading state.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to use the selected client's data.
        /// </summary>
        public RelayCommand UseSelectedClientCommand { get; private set; }

        /// <summary>
        /// Command to create a new client (ignore duplicates).
        /// </summary>
        public RelayCommand CreateNewClientCommand { get; private set; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the DuplicateClientDetectionDialogViewModel class.
        /// </summary>
        public DuplicateClientDetectionDialogViewModel()
        {
            // Get service from ServiceLocator
            _duplicateDetectionService = ServiceLocator.GetService<DuplicateClientDetectionService>()
                ?? throw new InvalidOperationException("DuplicateClientDetectionService not found in ServiceLocator");

            // Initialize default values
            ContentMessage = "تم العثور على عميل موجود بنفس الاسم. يمكنك استخدام بياناته أو إنشاء عميل جديد.";

            // Initialize commands
            InitializeCommands();
        }

        /// <summary>
        /// Initializes a new instance with duplicate clients data.
        /// </summary>
        /// <param name="nameFr">The French name that was searched</param>
        /// <param name="duplicateClients">The list of duplicate clients found</param>
        public DuplicateClientDetectionDialogViewModel(string nameFr, System.Collections.Generic.List<DuplicateClientData> duplicateClients)
            : this()
        {
            if (!string.IsNullOrWhiteSpace(nameFr))
            {
                HeaderText = $"عملاء بنفس الاسم {nameFr}";
            }

            if (duplicateClients?.Any() == true)
            {
                DuplicateClients = new ObservableCollection<DuplicateClientData>(duplicateClients);
                
                // Pre-select the first client
                SelectedClient = DuplicateClients.FirstOrDefault();
            }
        }

        #endregion

        #region Command Initialization

        /// <summary>
        /// Initializes the commands for the ViewModel.
        /// </summary>
        private void InitializeCommands()
        {
            UseSelectedClientCommand = new RelayCommand(
                execute: ExecuteUseSelectedClient,
                canExecute: CanExecuteUseSelectedClient,
                commandName: "UseSelectedClient"
            );

            CreateNewClientCommand = new RelayCommand(
                execute: ExecuteCreateNewClient,
                canExecute: CanExecuteCreateNewClient,
                commandName: "CreateNewClient"
            );
        }

        #endregion

        #region Command Implementations

        /// <summary>
        /// Executes the use selected client command.
        /// </summary>
        private async void ExecuteUseSelectedClient(object? parameter)
        {
            if (SelectedClient == null)
            {
                LoggingService.LogWarning("No client selected for use", "DuplicateClientDetectionDialogViewModel");
                return;
            }

            try
            {
                IsLoading = true;
                LoggingService.LogInfo($"Using selected client: {SelectedClient.ClientUid}", "DuplicateClientDetectionDialogViewModel");

                // Get complete client data including all phone numbers
                var completeClientData = await _duplicateDetectionService.GetCompleteClientDataAsync(SelectedClient.ClientUid);

                if (completeClientData != null)
                {
                    // Update the selected client with complete data
                    SelectedClient = completeClientData;

                    // Close dialog with success result
                    DialogResult = true;

                    // Raise event to request dialog closure
                    CloseRequested?.Invoke(this, true);
                }
                else
                {
                    LoggingService.LogError($"Failed to retrieve complete data for client: {SelectedClient.ClientUid}", "DuplicateClientDetectionDialogViewModel");
                    ErrorManager.ShowUserErrorToast("حدث خطأ أثناء تحميل بيانات العميل", "خطأ في التحميل", "DuplicateClientDetectionDialogViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error using selected client: {ex.Message}", "DuplicateClientDetectionDialogViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء استخدام بيانات العميل المحدد",
                    "خطأ في الاستخدام",
                    LogLevel.Error,
                    "DuplicateClientDetectionDialogViewModel");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Determines whether the use selected client command can execute.
        /// </summary>
        private bool CanExecuteUseSelectedClient(object? parameter)
        {
            return SelectedClient != null && !IsLoading;
        }

        /// <summary>
        /// Executes the create new client command.
        /// </summary>
        private void ExecuteCreateNewClient(object? parameter)
        {
            try
            {
                LoggingService.LogInfo("User chose to create new client", "DuplicateClientDetectionDialogViewModel");

                // Clear selection and close dialog with cancel result
                SelectedClient = null;
                DialogResult = false;

                // Raise event to request dialog closure
                CloseRequested?.Invoke(this, false);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating new client: {ex.Message}", "DuplicateClientDetectionDialogViewModel");
            }
        }

        /// <summary>
        /// Determines whether the create new client command can execute.
        /// </summary>
        private bool CanExecuteCreateNewClient(object? parameter)
        {
            return !IsLoading;
        }

        #endregion

        #region Public Properties for Dialog Result

        /// <summary>
        /// Gets or sets the dialog result.
        /// True = Use selected client, False = Create new client
        /// </summary>
        public bool? DialogResult { get; set; }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the dialog should be closed.
        /// </summary>
        public event EventHandler<bool>? CloseRequested;

        #endregion

        #region Public Methods

        /// <summary>
        /// Loads duplicate clients for the specified name.
        /// </summary>
        /// <param name="nameFr">The French name to search for</param>
        public async Task LoadDuplicateClientsAsync(string nameFr)
        {
            if (string.IsNullOrWhiteSpace(nameFr))
            {
                LoggingService.LogWarning("NameFr is required for loading duplicate clients", "DuplicateClientDetectionDialogViewModel");
                return;
            }

            try
            {
                IsLoading = true;
                HeaderText = $"عملاء بنفس الاسم {nameFr}";

                var duplicates = await _duplicateDetectionService.FindDuplicateClientsAsync(nameFr);

                DuplicateClients.Clear();
                foreach (var duplicate in duplicates)
                {
                    DuplicateClients.Add(duplicate);
                }

                // Pre-select the first client if any found
                SelectedClient = DuplicateClients.FirstOrDefault();

                LoggingService.LogInfo($"Loaded {DuplicateClients.Count} duplicate clients for '{nameFr}'", "DuplicateClientDetectionDialogViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading duplicate clients for '{nameFr}': {ex.Message}", "DuplicateClientDetectionDialogViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء البحث عن العملاء المكررين",
                    "خطأ في البحث",
                    LogLevel.Error,
                    "DuplicateClientDetectionDialogViewModel");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Dispose

        /// <summary>
        /// Disposes the ViewModel and releases resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _duplicateDetectionService?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
