using System;
using System.Globalization;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that inverts boolean values.
    /// Converts true to false and false to true.
    /// Used for enabling/disabling controls based on inverse boolean conditions.
    /// </summary>
    public class InverseBooleanConverter : IValueConverter
    {
        /// <summary>
        /// Converts a boolean value to its inverse.
        /// </summary>
        /// <param name="value">The boolean value to invert</param>
        /// <param name="targetType">The target type (not used)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>The inverted boolean value</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }

            // If value is not a boolean, return false as default
            return false;
        }

        /// <summary>
        /// Converts back by inverting the boolean value again.
        /// </summary>
        /// <param name="value">The boolean value to invert back</param>
        /// <param name="targetType">The target type (not used)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>The inverted boolean value</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }

            // If value is not a boolean, return false as default
            return false;
        }
    }
}
