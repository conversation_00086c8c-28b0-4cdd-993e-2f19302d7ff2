using System;
using System.Threading;
using System.Threading.Tasks;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Service for monitoring database connection pool performance and effectiveness.
    /// Provides periodic reporting and performance analysis capabilities.
    /// </summary>
    public class DatabasePoolMonitoringService : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private Timer? _reportingTimer;
        private bool _disposed = false;

        // Monitoring configuration
        private const int DefaultReportingIntervalMinutes = 30;
        private const int DetailedReportingIntervalMinutes = 120;

        /// <summary>
        /// Initializes a new instance of the DatabasePoolMonitoringService.
        /// </summary>
        /// <param name="databaseService">The database service to monitor</param>
        public DatabasePoolMonitoringService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
        }

        /// <summary>
        /// Starts monitoring the database connection pool.
        /// </summary>
        /// <param name="enablePeriodicReporting">Whether to enable automatic periodic reporting</param>
        public void StartMonitoring(bool enablePeriodicReporting = true)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(DatabasePoolMonitoringService));
            }

            LoggingService.LogInfo("Starting database pool monitoring", "DatabasePoolMonitoringService");

            if (enablePeriodicReporting)
            {
                // Setup periodic reporting timer
                _reportingTimer = new Timer(PeriodicReport, null,
                    TimeSpan.FromMinutes(DefaultReportingIntervalMinutes),
                    TimeSpan.FromMinutes(DefaultReportingIntervalMinutes));

                LoggingService.LogInfo($"Periodic pool reporting enabled (interval: {DefaultReportingIntervalMinutes} minutes)", "DatabasePoolMonitoringService");
            }

            // Log initial pool status
            LogCurrentPoolStatus();
        }

        /// <summary>
        /// Stops monitoring the database connection pool.
        /// </summary>
        public void StopMonitoring()
        {
            _reportingTimer?.Dispose();
            _reportingTimer = null;
            LoggingService.LogInfo("Database pool monitoring stopped", "DatabasePoolMonitoringService");
        }

        /// <summary>
        /// Logs the current pool status and basic statistics.
        /// </summary>
        public void LogCurrentPoolStatus()
        {
            try
            {
                var stats = _databaseService.GetPoolStatistics();
                
                LoggingService.LogInfo($"Database Pool Status:", "DatabasePoolMonitoringService");
                LoggingService.LogInfo($"  Current Size: {stats.CurrentPoolSize}/{stats.MaxPoolSize}", "DatabasePoolMonitoringService");
                LoggingService.LogInfo($"  Health: {(stats.IsHealthy ? "Healthy" : "Unhealthy")}", "DatabasePoolMonitoringService");
                LoggingService.LogInfo($"  Utilization: {stats.UtilizationPercentage:F1}%", "DatabasePoolMonitoringService");
                
                if (stats.TotalConnectionRequests > 0)
                {
                    LoggingService.LogInfo($"  Performance: {stats.PoolHitRatio:P1} hit ratio, {stats.TotalConnectionRequests:N0} total requests", "DatabasePoolMonitoringService");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error logging pool status: {ex.Message}", "DatabasePoolMonitoringService");
            }
        }

        /// <summary>
        /// Generates a detailed performance report.
        /// </summary>
        public void GenerateDetailedReport()
        {
            try
            {
                LoggingService.LogInfo("=== Database Pool Detailed Performance Report ===", "DatabasePoolMonitoringService");
                _databaseService.LogPoolEffectiveness();
                LoggingService.LogInfo("=== End of Detailed Report ===", "DatabasePoolMonitoringService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating detailed report: {ex.Message}", "DatabasePoolMonitoringService");
            }
        }

        /// <summary>
        /// Analyzes pool performance and provides recommendations.
        /// </summary>
        /// <returns>Performance analysis and recommendations</returns>
        public PoolPerformanceAnalysis AnalyzePerformance()
        {
            try
            {
                var stats = _databaseService.GetPoolStatistics();
                var analysis = new PoolPerformanceAnalysis();

                // Analyze pool hit ratio
                if (stats.PoolHitRatio >= 0.9)
                {
                    analysis.PerformanceRating = PoolPerformanceRating.Excellent;
                    analysis.Recommendations.Add("Pool performance is excellent. No changes needed.");
                }
                else if (stats.PoolHitRatio >= 0.7)
                {
                    analysis.PerformanceRating = PoolPerformanceRating.Good;
                    analysis.Recommendations.Add("Pool performance is good. Monitor for any degradation.");
                }
                else if (stats.PoolHitRatio >= 0.5)
                {
                    analysis.PerformanceRating = PoolPerformanceRating.Fair;
                    analysis.Recommendations.Add("Consider increasing pool size to improve hit ratio.");
                }
                else
                {
                    analysis.PerformanceRating = PoolPerformanceRating.Poor;
                    analysis.Recommendations.Add("Pool performance is poor. Review pool configuration and application usage patterns.");
                }

                // Analyze utilization
                if (stats.UtilizationPercentage > 90)
                {
                    analysis.Recommendations.Add("Pool utilization is very high. Consider increasing maximum pool size.");
                }
                else if (stats.UtilizationPercentage < 20 && stats.TotalConnectionRequests > 100)
                {
                    analysis.Recommendations.Add("Pool utilization is low. Consider reducing maximum pool size to save resources.");
                }

                // Analyze health
                if (!stats.IsHealthy)
                {
                    analysis.Recommendations.Add("Pool is unhealthy. Check for connection issues or increase minimum pool size.");
                }

                analysis.Statistics = stats;
                return analysis;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error analyzing pool performance: {ex.Message}", "DatabasePoolMonitoringService");
                return new PoolPerformanceAnalysis
                {
                    PerformanceRating = PoolPerformanceRating.Unknown,
                    Recommendations = { "Error occurred during analysis. Check logs for details." }
                };
            }
        }

        /// <summary>
        /// Periodic reporting callback.
        /// </summary>
        /// <param name="state">Timer state (unused)</param>
        private void PeriodicReport(object? state)
        {
            if (_disposed) return;

            try
            {
                LogCurrentPoolStatus();
                
                // Generate detailed report every few cycles
                var stats = _databaseService.GetPoolStatistics();
                if (stats.TotalConnectionRequests > 0 && stats.TotalConnectionRequests % 1000 == 0)
                {
                    GenerateDetailedReport();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in periodic pool reporting: {ex.Message}", "DatabasePoolMonitoringService");
            }
        }

        /// <summary>
        /// Disposes the monitoring service and stops all timers.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                StopMonitoring();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Performance analysis results for the connection pool.
    /// </summary>
    public class PoolPerformanceAnalysis
    {
        /// <summary>
        /// Overall performance rating.
        /// </summary>
        public PoolPerformanceRating PerformanceRating { get; set; } = PoolPerformanceRating.Unknown;

        /// <summary>
        /// List of recommendations for improving performance.
        /// </summary>
        public List<string> Recommendations { get; set; } = new List<string>();

        /// <summary>
        /// Detailed pool statistics.
        /// </summary>
        public ConnectionPoolStatistics? Statistics { get; set; }
    }

    /// <summary>
    /// Pool performance rating levels.
    /// </summary>
    public enum PoolPerformanceRating
    {
        Unknown,
        Poor,
        Fair,
        Good,
        Excellent
    }
}
