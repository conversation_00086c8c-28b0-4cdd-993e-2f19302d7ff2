﻿#pragma checksum "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6EB547ACA8DEB460363D2AFEF13582B953E435CC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.MahApps;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using UFU2.Common.Converters;


namespace UFU2.Views.Dialogs {
    
    
    /// <summary>
    /// AddCraftTypeDialog
    /// </summary>
    public partial class AddCraftTypeDialog : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 24 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card MainCard;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContentTextBox;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SecondaryTextBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UFU2;component/views/dialogs/addcrafttypedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 10 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            ((UFU2.Views.Dialogs.AddCraftTypeDialog)(target)).Loaded += new System.Windows.RoutedEventHandler(this.AddCraftTypeDialog_Loaded);
            
            #line default
            #line hidden
            
            #line 11 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            ((UFU2.Views.Dialogs.AddCraftTypeDialog)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.AddCraftTypeDialog_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MainCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 3:
            this.CodeTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 66 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            this.CodeTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CodeTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.ContentTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 108 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            this.ContentTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.ContentTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 111 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            this.ContentTextBox.PreviewKeyDown += new System.Windows.Input.KeyEventHandler(this.ContentTextBox_PreviewKeyDown);
            
            #line default
            #line hidden
            
            #line 114 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            this.ContentTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ContentTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SecondaryTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 134 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            this.SecondaryTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SecondaryTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 137 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            this.SecondaryTextBox.PreviewKeyDown += new System.Windows.Input.KeyEventHandler(this.SecondaryTextBox_PreviewKeyDown);
            
            #line default
            #line hidden
            
            #line 140 "..\..\..\..\..\Views\Dialogs\AddCraftTypeDialog.xaml"
            this.SecondaryTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SecondaryTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

