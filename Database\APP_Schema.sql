-- ============================================================================
-- UFU2 Reference Database Schema (APP_Database.db)
-- ============================================================================
-- This schema defines the reference data tables for UFU2 application.
-- Contains activity types, craft types, and geographical location data.
-- All reference data is static and used for lookups and validation.
-- ============================================================================

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- REFERENCE DATA TABLES
-- ============================================================================

-- Activity Types - Official activity classifications
CREATE TABLE IF NOT EXISTS ActivityTypeBase (
    Code TEXT PRIMARY KEY NOT NULL,
    Description TEXT NOT NULL,

    -- Data validation constraints
    CONSTRAINT chk_activity_code_not_empty CHECK (length(trim(Code)) > 0),
    CONSTRAINT chk_activity_description_not_empty CHECK (length(trim(Description)) > 0)
);

-- Craft Types - Traditional craft classifications
CREATE TABLE IF NOT EXISTS CraftTypeBase (
    Code TEXT PRIMARY KEY NOT NULL,
    Description TEXT NOT NULL,
    Content TEXT,
    Secondary TEXT,

    -- Data validation constraints
    CONSTRAINT chk_craft_code_not_empty CHECK (length(trim(Code)) > 0),
    CONSTRAINT chk_craft_description_not_empty CHECK (length(trim(Description)) > 0)
);

-- CPI Wilayas - Administrative provinces (from official Algerian Ministry of Finance data)
CREATE TABLE IF NOT EXISTS CpiWilayas (
    Code TEXT PRIMARY KEY NOT NULL,
    NameAr TEXT NOT NULL,
    NameFr TEXT NOT NULL,
    DisplayValue TEXT NOT NULL,

    -- Data validation constraints
    CONSTRAINT chk_wilaya_code_not_empty CHECK (length(trim(Code)) > 0),
    CONSTRAINT chk_wilaya_name_ar_not_empty CHECK (length(trim(NameAr)) > 0),
    CONSTRAINT chk_wilaya_name_fr_not_empty CHECK (length(trim(NameFr)) > 0),
    CONSTRAINT chk_wilaya_display_not_empty CHECK (length(trim(DisplayValue)) > 0)
);

-- CPI Dairas - Administrative districts within wilayas
CREATE TABLE IF NOT EXISTS CpiDairas (
    Code TEXT PRIMARY KEY NOT NULL,
    WilayaCode TEXT NOT NULL,
    NameAr TEXT NOT NULL,
    NameFr TEXT NOT NULL,
    DisplayValue TEXT NOT NULL,

    -- Foreign key relationship
    FOREIGN KEY (WilayaCode) REFERENCES CpiWilayas(Code) ON DELETE RESTRICT,

    -- Data validation constraints
    CONSTRAINT chk_daira_code_not_empty CHECK (length(trim(Code)) > 0),
    CONSTRAINT chk_daira_wilaya_code_not_empty CHECK (length(trim(WilayaCode)) > 0),
    CONSTRAINT chk_daira_name_ar_not_empty CHECK (length(trim(NameAr)) > 0),
    CONSTRAINT chk_daira_name_fr_not_empty CHECK (length(trim(NameFr)) > 0),
    CONSTRAINT chk_daira_display_not_empty CHECK (length(trim(DisplayValue)) > 0)
);

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Activity Types indexes
CREATE INDEX IF NOT EXISTS idx_activity_type_description 
ON ActivityTypeBase(Description);

-- Craft Types indexes
CREATE INDEX IF NOT EXISTS idx_craft_type_description 
ON CraftTypeBase(Description);

CREATE INDEX IF NOT EXISTS idx_craft_type_content 
ON CraftTypeBase(Content) 
WHERE Content IS NOT NULL;

-- CPI Wilayas indexes
CREATE INDEX IF NOT EXISTS idx_cpi_wilayas_name_ar 
ON CpiWilayas(NameAr);

CREATE INDEX IF NOT EXISTS idx_cpi_wilayas_name_fr 
ON CpiWilayas(NameFr);

CREATE INDEX IF NOT EXISTS idx_cpi_wilayas_display 
ON CpiWilayas(DisplayValue);

-- CPI Dairas indexes
CREATE INDEX IF NOT EXISTS idx_cpi_dairas_wilaya_code 
ON CpiDairas(WilayaCode);

CREATE INDEX IF NOT EXISTS idx_cpi_dairas_name_ar 
ON CpiDairas(NameAr);

CREATE INDEX IF NOT EXISTS idx_cpi_dairas_name_fr 
ON CpiDairas(NameFr);

CREATE INDEX IF NOT EXISTS idx_cpi_dairas_display 
ON CpiDairas(DisplayValue);

-- Composite index for efficient wilaya-daira lookups
CREATE INDEX IF NOT EXISTS idx_cpi_dairas_wilaya_display 
ON CpiDairas(WilayaCode, DisplayValue);

-- ============================================================================
-- FULL-TEXT SEARCH SUPPORT
-- ============================================================================

-- FTS5 virtual table for Activity Types (Arabic text search)
CREATE VIRTUAL TABLE IF NOT EXISTS ActivityTypeBaseFts 
USING fts5(Code, Description, content='ActivityTypeBase', content_rowid='rowid');

-- FTS5 virtual table for Craft Types (Arabic text search)
CREATE VIRTUAL TABLE IF NOT EXISTS CraftTypeBaseFts 
USING fts5(Code, Description, Content, Secondary, content='CraftTypeBase', content_rowid='rowid');

-- FTS5 virtual table for CPI Locations (Arabic/French text search)
CREATE VIRTUAL TABLE IF NOT EXISTS CpiLocationsFts 
USING fts5(
    WilayaCode, WilayaNameAr, WilayaNameFr, WilayaDisplay,
    DairaCode, DairaNameAr, DairaNameFr, DairaDisplay
);

-- ============================================================================
-- SCHEMA VERSION TRACKING
-- ============================================================================

-- Schema version table for migration tracking
CREATE TABLE IF NOT EXISTS ReferenceSchemaVersion (
    Version INTEGER PRIMARY KEY,
    AppliedAt TEXT DEFAULT (datetime('now')),
    Description TEXT
);

-- Insert initial schema version
INSERT OR REPLACE INTO ReferenceSchemaVersion (Version, Description) 
VALUES (1, 'Initial reference database schema with activity types, craft types, and CPI locations');

-- ============================================================================
-- VIEWS FOR CONVENIENT QUERYING
-- ============================================================================

-- Complete location hierarchy view
CREATE VIEW IF NOT EXISTS CpiLocationHierarchy AS
SELECT 
    w.Code as WilayaCode,
    w.NameAr as WilayaNameAr,
    w.NameFr as WilayaNameFr,
    w.DisplayValue as WilayaDisplay,
    d.Code as DairaCode,
    d.NameAr as DairaNameAr,
    d.NameFr as DairaNameFr,
    d.DisplayValue as DairaDisplay
FROM CpiWilayas w
LEFT JOIN CpiDairas d ON w.Code = d.WilayaCode
ORDER BY w.DisplayValue, d.DisplayValue;

-- Activity types with search ranking
CREATE VIEW IF NOT EXISTS ActivityTypeSearch AS
SELECT 
    Code,
    Description,
    length(Description) as DescriptionLength,
    CASE 
        WHEN Description LIKE '%مؤسسة%' THEN 1
        WHEN Description LIKE '%إنتاج%' THEN 2
        WHEN Description LIKE '%تجارة%' THEN 3
        ELSE 4
    END as CategoryRank
FROM ActivityTypeBase
ORDER BY CategoryRank, DescriptionLength;

-- Craft types with content classification
CREATE VIEW IF NOT EXISTS CraftTypeClassified AS
SELECT 
    Code,
    Description,
    Content,
    Secondary,
    CASE 
        WHEN Content IS NOT NULL AND Secondary IS NOT NULL THEN 'Complete'
        WHEN Content IS NOT NULL THEN 'HasContent'
        WHEN Secondary IS NOT NULL THEN 'HasSecondary'
        ELSE 'Basic'
    END as DataCompleteness
FROM CraftTypeBase
ORDER BY DataCompleteness DESC, Description;

-- ============================================================================
-- TRIGGERS FOR FTS SYNCHRONIZATION
-- ============================================================================

-- Activity Types FTS synchronization
CREATE TRIGGER IF NOT EXISTS trg_activity_type_fts_insert
    AFTER INSERT ON ActivityTypeBase
BEGIN
    INSERT INTO ActivityTypeBaseFts(rowid, Code, Description) 
    VALUES (NEW.rowid, NEW.Code, NEW.Description);
END;

CREATE TRIGGER IF NOT EXISTS trg_activity_type_fts_update
    AFTER UPDATE ON ActivityTypeBase
BEGIN
    UPDATE ActivityTypeBaseFts 
    SET Code = NEW.Code, Description = NEW.Description 
    WHERE rowid = NEW.rowid;
END;

CREATE TRIGGER IF NOT EXISTS trg_activity_type_fts_delete
    AFTER DELETE ON ActivityTypeBase
BEGIN
    DELETE FROM ActivityTypeBaseFts WHERE rowid = OLD.rowid;
END;

-- Craft Types FTS synchronization
CREATE TRIGGER IF NOT EXISTS trg_craft_type_fts_insert
    AFTER INSERT ON CraftTypeBase
BEGIN
    INSERT INTO CraftTypeBaseFts(rowid, Code, Description, Content, Secondary) 
    VALUES (NEW.rowid, NEW.Code, NEW.Description, NEW.Content, NEW.Secondary);
END;

CREATE TRIGGER IF NOT EXISTS trg_craft_type_fts_update
    AFTER UPDATE ON CraftTypeBase
BEGIN
    UPDATE CraftTypeBaseFts 
    SET Code = NEW.Code, Description = NEW.Description, Content = NEW.Content, Secondary = NEW.Secondary 
    WHERE rowid = NEW.rowid;
END;

CREATE TRIGGER IF NOT EXISTS trg_craft_type_fts_delete
    AFTER DELETE ON CraftTypeBase
BEGIN
    DELETE FROM CraftTypeBaseFts WHERE rowid = OLD.rowid;
END;

-- ============================================================================
-- MAINTENANCE PROCEDURES
-- ============================================================================

-- Note: SQLite doesn't support stored procedures, but these are the SQL commands
-- that should be used for maintenance operations:

-- Rebuild FTS indexes for optimal performance
-- INSERT INTO ActivityTypeBaseFts(ActivityTypeBaseFts) VALUES('rebuild');
-- INSERT INTO CraftTypeBaseFts(CraftTypeBaseFts) VALUES('rebuild');

-- Analyze tables for query optimization
-- ANALYZE ActivityTypeBase;
-- ANALYZE CraftTypeBase;
-- ANALYZE CpiWilayas;
-- ANALYZE CpiDairas;

-- Vacuum database to reclaim space
-- VACUUM;
