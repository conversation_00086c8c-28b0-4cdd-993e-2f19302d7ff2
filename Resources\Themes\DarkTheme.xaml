﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--
        ========================================
        UFU2 THEME COLOR SYSTEM - DARK THEME
        ========================================
        
        This ResourceDictionary defines the color system for the UFU2 application
        using a dark theme. Colors are categorized by **purpose** and **UI role**,
        not appearance (e.g., not “BlueColor” or “GrayColor”), enabling consistent
        reuse and easy theming across dark and light modes.
        
        ========================================
        THEME CHARACTERISTICS:
        - Dark backgrounds (deep blues)
        - Light text for contrast and readability
        - Warm orange as an accent/brand color
        - Business-professional visual identity
        ========================================
        
        NAMING CONVENTION:
        - Semantic: {Purpose}{Level}{Type} (e.g., PrimaryTextColor, BackgroundBase)
        - Avoid hardcoded appearance in names (e.g., "Blue", "Light")
        - Emphasize *functional role* (Background, Text, Accent, etc.)
        - Enables easy swapping for Light Theme with same keys
    -->

    <!--
        ========================================
        BASE COLOR PALETTE (Raw Theme Values)
        These are low-level color values specific to the dark theme.
        DO NOT reference directly in UI – using semantic brushes instead.
        ========================================
    -->
    <!--
        <Color x:Key="Surface">#E6222526</Color>
        <Color x:Key="SurfaceDim">#E6353A3E</Color>
        <Color x:Key="SurfaceBright">#E6696A74</Color>

        <Color x:Key="InverseSurface">#E61D1F20</Color>
        <Color x:Key="InverseSurfaceDim">#E64B5153</Color>
    -->

    <!--  ========== SURFACE COLORS (DARK THEME) ==========  -->
    <Color x:Key="Surface">#E61D1F20</Color>
    <Color x:Key="SurfaceDim">#E6222526</Color>
    <Color x:Key="SurfaceBright">#E6353A3E</Color>

    <Color x:Key="InverseSurface">#E64E5766</Color>
    <Color x:Key="InverseSurfaceDim">#E6667085</Color>

    <!--  ========== PRIMARY COLORS (DARK THEME) ==========  -->
    <Color x:Key="PrimaryFixed">#FFFFB02E</Color>
    <Color x:Key="PrimaryDim">#FFFFA309</Color>

    <Color x:Key="OnPrimary">#FFFFA105</Color>

    <Color x:Key="PrimaryContainer">#99FFB02E</Color>
    <Color x:Key="PrimaryContainerDim">#4DFFB02E</Color>
    <Color x:Key="PrimaryContainerBright">#1AFFB02E</Color>

    <Color x:Key="OnPrimaryContainer">#FFFFB02E</Color>


    <!--  ========== PRIMARY TEXT COLORS ==========  -->
    <Color x:Key="TextFillColorPrimary">#FFFFFF</Color>
    <Color x:Key="TextFillColorSecondary">#C5FFFFFF</Color>
    <Color x:Key="TextFillColorTertiary">#87FFFFFF</Color>
    <Color x:Key="TextFillColorDisabled">#a8a9ad</Color>
    <Color x:Key="TextPlaceholderColor">#87FFFFFF</Color>
    <Color x:Key="TextFillColorInverse">#E4000000</Color>

    <!--  ========== STATUS COLORS ==========  -->
    <Color x:Key="Success">#2EF47F</Color>
    <Color x:Key="OnSuccess">#07873B</Color>

    <Color x:Key="Warning">#FACC15</Color>
    <Color x:Key="OnWarning">#BA9504</Color>

    <Color x:Key="Error">#FF4C4C</Color>
    <Color x:Key="OnError">#F90000</Color>

    <Color x:Key="Info">#00FFFF</Color>
    <Color x:Key="OnInfo">#006061</Color>

    <!--  ========== G12/Bis STATUS COLORS ==========  -->
    <Color x:Key="ChipBackgoundPaid">#4D2EF47F</Color>
    <Color x:Key="ChipBorderPaid">#07873B</Color>

    <Color x:Key="ChipBackgoundNotPaid">#4DF90000</Color>
    <Color x:Key="ChipBorderNotPaid">#F90000</Color>

    <!--  ========== Popup Flag Status Colors ==========  -->
    <Color x:Key="NormaleFlag">#2EF47F</Color>
    <Color x:Key="ImportantFlag">#FACC15</Color>
    <Color x:Key="VeryImportantFlag">#FF4C4C</Color>

    <!--
        ========================================
        COMPONENT TOKEN COLORS
        ========================================
    -->
    <!--  ========== Standard Brushes ==========  -->
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="SurfaceDimBrush" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="SurfaceBrightBrush" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="InverseSurfaceBrush" Color="{StaticResource InverseSurface}" />
    <SolidColorBrush x:Key="InverseSurfaceDimBrush" Color="{StaticResource InverseSurfaceDim}" />
    <SolidColorBrush x:Key="InverseSurfaceBrightBrush" Color="red" />
    <SolidColorBrush x:Key="TextFillColorPrimaryBrush" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="TextFillColorSecondaryBrush" Color="{StaticResource TextFillColorSecondary}" />
    <SolidColorBrush x:Key="TextFillColorTertiaryBrush" Color="{StaticResource TextFillColorTertiary}" />
    <SolidColorBrush x:Key="TextFillColorDisabledBrush" Color="{StaticResource TextFillColorDisabled}" />

    <!--  ========== Primary Color Brushes ==========  -->
    <SolidColorBrush x:Key="PrimaryFixedBrush" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="PrimaryContainerBrush" Color="{StaticResource PrimaryContainer}" />

    <!--  ========== Main Window Color ==========  -->
    <SolidColorBrush x:Key="MainWindowBackgroundBase" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="MainWindowBackgoundAccent" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="MainWindowForegroundBase" Color="{StaticResource TextFillColorPrimary}" />

    <!--  ========== Confirmation Window Color ==========  -->
    <SolidColorBrush x:Key="ConfirmWindowBackground" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="ConfirmWindowBackgroundBase" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ConfirmWindowForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ConfirmWindowIconColor" Color="{StaticResource Warning}" />

    <!--  ========== Primary Button Color ==========  -->
    <SolidColorBrush x:Key="ButtonBackgroundPrimary" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="ButtonForegroundPrimary" Color="#2F2F2F" />
    <SolidColorBrush x:Key="ButtonBorderPrimary" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="ButtonHoverPrimary" Color="{StaticResource PrimaryDim}" />

    <!--  ========== Secondary Button Color ==========  -->
    <SolidColorBrush x:Key="ButtonBackgroundSecondary" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="ButtonForegroundSecondary" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="ButtonBorderSecondary" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ButtonHoverSecondary" Color="{StaticResource SurfaceBright}" />

    <!--  ========== Container Button Color ==========  -->
    <SolidColorBrush x:Key="ButtonBackgroundContainer" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="ButtonBorderContainer" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="ButtonForegroundContainer" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="ButtonBorderHoverContainer" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ButtonBackgroundHoverContainer" Color="{StaticResource SurfaceBright}" />

    <!--  ========== Back Button Color ==========  -->
    <SolidColorBrush x:Key="BackBorderBackground" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="BackIconForeground" Color="{StaticResource OnPrimaryContainer}" />

    <!--  ========== Plus Button Color ==========  -->
    <SolidColorBrush x:Key="PlusBorderBackgroundBase" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="PlusForegroundBase" Color="{StaticResource OnPrimaryContainer}" />

    <!--  ========== Icon Buttons Color ==========  -->
    <SolidColorBrush x:Key="IconButtonBackgroundBase" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="IconButtonBackgroundHover" Color="{StaticResource OnPrimary}" />

    <!--  ========== Icon Delete Buttons Color==========  -->
    <SolidColorBrush x:Key="DeleteButtonForeground" Color="{StaticResource Error}" />
    <SolidColorBrush x:Key="DeleteButtonForegroundHover" Color="{StaticResource OnError}" />

    <!--  ========== Icon Edit Buttons Color==========  -->
    <SolidColorBrush x:Key="EditButtonForeground" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="EditButtonForegroundHover" Color="{StaticResource OnPrimary}" />

    <!--  ========== Card Color ==========  -->
    <SolidColorBrush x:Key="CardBackgroundBase" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="CardForegroundBase" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="CardBackgroundAccent" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="CardBackgroundgOverlay" Color="{StaticResource SurfaceBright}" />

    <!--  ========== Undeline Text/Combo Box Color ==========  -->
    <SolidColorBrush x:Key="UnderLineForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="UnderLineBorder" Color="{StaticResource InverseSurfaceDim}" />
    <SolidColorBrush x:Key="UnderLineBorderHover" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="UnderLineFocuseColor" Color="{StaticResource OnPrimaryContainer}" />

    <!--  ========== TRadioButton Color ==========  -->
    <SolidColorBrush x:Key="RadioForegroundColor" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="RadioBackgroundColor" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="RadioBackgroundChecked" Color="{StaticResource PrimaryContainerDim}" />
    <SolidColorBrush x:Key="RadioBorderChecked" Color="{StaticResource OnPrimary}" />
    <SolidColorBrush x:Key="RadioHover" Color="{StaticResource PrimaryContainerBright}" />
    <SolidColorBrush x:Key="RadioBorderHover" Color="{StaticResource OnPrimaryContainer}" />
    <SolidColorBrush x:Key="RadioBorderBrush" Color="{StaticResource PrimaryContainer}" />

    <!--  ========== Toggle Switch Color ==========  -->
    <!--  Disabled State Color Not Implemented yet  -->
    <SolidColorBrush x:Key="ThumbOffBackground" Color="{DynamicResource InverseSurface}" />
    <SolidColorBrush x:Key="ThumbOffForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ThumbOnBackground" Color="{DynamicResource PrimaryFixed}" />
    <SolidColorBrush x:Key="ThumbOnForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="TrackOffBackground" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="TrackOnBackground" Color="{StaticResource PrimaryContainerDim}" />

    <!--  ==========  Chip Colors ==========  -->
    <SolidColorBrush x:Key="ChipBackgroundBase" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="ChipForegroundBase" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ChipBorderBase" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ChipBorderHover" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="ChipHoverBase" Color="{StaticResource PrimaryContainerBright}" />
    <SolidColorBrush x:Key="ChipBackgroundSelected" Color="{StaticResource PrimaryContainerDim}" />
    <SolidColorBrush x:Key="ChipBorderSelected" Color="{StaticResource Surface}" />

    <!--  ========== Years Chip Color ==========  -->
    <!--  Note if change color go Style and change rest color there  -->
    <SolidColorBrush x:Key="YearBackgroundNotPaid" Color="{StaticResource ChipBackgoundNotPaid}" />
    <SolidColorBrush x:Key="YearBorderNotPaid" Color="{StaticResource ChipBorderNotPaid}" />
    <SolidColorBrush x:Key="YearBackgroundPaid" Color="{StaticResource ChipBackgoundPaid}" />
    <SolidColorBrush x:Key="YearBorderPaid" Color="{StaticResource ChipBorderPaid}" />

    <!--  ==========  Scroll Component Colors ==========  -->
    <SolidColorBrush x:Key="ScrollThumbBackground" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="ScrollThumbHoverBackground" Color="{StaticResource PrimaryContainerDim}" />
    <SolidColorBrush x:Key="ScrollThumbPressedBackground" Color="{StaticResource PrimaryContainer}" />
    <SolidColorBrush x:Key="ScrollTrackBackground" Color="{StaticResource SurfaceDim}" />

    <!--  ========== PopUp Flag Colors ==========  -->
    <SolidColorBrush x:Key="NormaleFlagPopup" Color="{StaticResource NormaleFlag}" />
    <SolidColorBrush x:Key="ImportantFlagPopup" Color="{StaticResource ImportantFlag}" />
    <SolidColorBrush x:Key="VeryImportantFlagPopup" Color="{StaticResource VeryImportantFlag}" />

    <!--  ========== Toast Notification Colors ==========  -->

    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource Success}" />
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource Info}" />
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource Warning}" />
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource Error}" />

    <!--  ==========  Enhanced Slider Component Colors ==========  -->
    <SolidColorBrush x:Key="SliderThumbBackground" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="SliderThumbBorder" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="SliderThumbHoverBackground" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="SliderThumbPressedBackground" Color="{StaticResource PrimaryFixed}" />
    <SolidColorBrush x:Key="SliderTrackBackground" Color="{StaticResource SurfaceDim}" />
    <SolidColorBrush x:Key="SliderTrackBorder" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="SliderRippleBackground" Color="{StaticResource PrimaryContainer}" />
    <SolidColorBrush x:Key="SliderFocusGlow" Color="{StaticResource PrimaryContainer}" />
    <SolidColorBrush x:Key="SliderTickMarkBrush" Color="{StaticResource TextFillColorPrimary}" />

    <!--  ==========  Window Chrome Colors ==========  -->
    <SolidColorBrush x:Key="TitleBarBackground" Color="{StaticResource Surface}" />
    <SolidColorBrush x:Key="TitleBarForeground" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="WindowBorderBrush" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="WindowControlButtonHover" Color="{StaticResource SurfaceBright}" />
    <SolidColorBrush x:Key="WindowControlButtonPressed" Color="{StaticResource InverseSurface}" />

    <SolidColorBrush x:Key="WindowButtonForegroud" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="WindowButtonHoverBackgroud" Color="{StaticResource InverseSurfaceDim}" />
    <SolidColorBrush x:Key="WindowButtonPressBackgroud" Color="{StaticResource InverseSurface}" />
    <SolidColorBrush x:Key="WindowCloseButtonHoverBackgroud" Color="{StaticResource Error}" />
    <SolidColorBrush x:Key="WindowCloseButtonPressBackgroud" Color="{StaticResource OnError}" />

    <!--  ==========  ToolTips Colors ==========  -->
    <SolidColorBrush x:Key="ToolTipsForegroud" Color="{StaticResource TextFillColorPrimary}" />
    <SolidColorBrush x:Key="ToolTipsBackgroud" Color="{StaticResource InverseSurfaceDim}" />


</ResourceDictionary>
