using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converts a string value to Visibility.
    /// Returns Visible if the string is not null or empty, otherwise returns Collapsed.
    /// This converter is used to show/hide UI elements based on whether string content exists.
    /// </summary>
    public class StringToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts a string value to Visibility
        /// </summary>
        /// <param name="value">The string value to convert</param>
        /// <param name="targetType">The target type (should be Visibility)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Visible if string is not null/empty, Collapsed otherwise</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return string.IsNullOrEmpty(stringValue) ? Visibility.Collapsed : Visibility.Visible;
            }

            // If value is null or not a string, treat as empty
            return Visibility.Collapsed;
        }

        /// <summary>
        /// Converts back from Visibility to string (not implemented)
        /// </summary>
        /// <param name="value">The Visibility value</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">Optional parameter</param>
        /// <param name="culture">The culture info</param>
        /// <returns>Not implemented - throws NotImplementedException</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("StringToVisibilityConverter does not support ConvertBack");
        }
    }
}
