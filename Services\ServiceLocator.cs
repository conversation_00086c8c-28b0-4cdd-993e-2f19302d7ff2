using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UFU2.Common;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Simple service locator for UFU2 application.
    /// Provides centralized access to application services while maintaining MVVM architecture.
    /// Enhanced with cache management capabilities for improved performance coordination.
    /// </summary>
    public static class ServiceLocator
    {
        private static readonly Dictionary<Type, object> _services = new Dictionary<Type, object>();
        private static readonly Dictionary<string, object> _namedServices = new Dictionary<string, object>();
        private static bool _isInitialized = false;

        // Cache management tracking
        private static readonly List<ICacheableService> _cacheableServices = new List<ICacheableService>();
        private static CacheCoordinatorService? _cacheCoordinator;
        private static CacheMonitoringService? _cacheMonitoring;
        private static MemoryPressureHandler? _memoryPressureHandler;

        /// <summary>
        /// Gets whether the service locator has been initialized
        /// </summary>
        public static bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the service locator with default services
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
            {
                LoggingService.LogWarning("ServiceLocator already initialized", "ServiceLocator");
                return;
            }

            try
            {
                LoggingService.LogDebug("Initializing ServiceLocator", "ServiceLocator");

                // Register services here
                // Note: ThemeManager is static, so we don't need to register it
                // But we can provide a wrapper if needed for consistency

                // Register ToastService wrapper for dependency injection
                RegisterService<IToastService>(new ToastServiceWrapper());

                // Register ValidationService for centralized validation logic
                RegisterService<ValidationService>(new ValidationService());

                // NOTE: ClientValidationService registration moved to InitializeDatabaseServicesAsync()
                // because it depends on DatabaseService which is not available during basic initialization

                // Register WindowChromeService for custom window chrome functionality
                RegisterService<IWindowChromeService>(new WindowChromeService());

                // Register DispatcherOptimizationService for UI thread performance optimization
                RegisterService<DispatcherOptimizationService>(new DispatcherOptimizationService());

                // Register UIResponsivenessMonitoringService for comprehensive UI performance monitoring
                RegisterService<UIResponsivenessMonitoringService>(new UIResponsivenessMonitoringService());

                // Register Phase 2C Day 3 services for background processing and memory optimization
                RegisterService<BackgroundViewInitializationService>(new BackgroundViewInitializationService());
                RegisterService<ViewMemoryOptimizationService>(new ViewMemoryOptimizationService());
                RegisterService<ViewLoadingMonitoringService>(new ViewLoadingMonitoringService());

                // Register PerformanceDashboardService for comprehensive performance tracking
                RegisterService<PerformanceDashboardService>(new PerformanceDashboardService());

                // Register Phase 2D memory management services
                InitializeMemoryManagementServices();

                _isInitialized = true;
                LoggingService.LogDebug("ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services", "ServiceLocator");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing ServiceLocator: {ex.Message}", "ServiceLocator");
                throw;
            }
        }

        /// <summary>
        /// Initializes database services and registers them with the service locator
        /// This method should be called after the basic ServiceLocator initialization
        /// </summary>
        public static async Task InitializeDatabaseServicesAsync()
        {
            try
            {
                LoggingService.LogDebug("Initializing three-database architecture services", "ServiceLocator");

                // Initialize client database service (default registration for backward compatibility)
                var clientDatabaseService = new DatabaseService(DatabaseType.ClientData);
                RegisterService(clientDatabaseService);

                // Initialize reference database service (explicit key registration)
                var referenceDatabaseService = new DatabaseService(DatabaseType.ReferenceData);
                RegisterService("ReferenceDatabase", referenceDatabaseService);

                // Initialize archive database service (explicit key registration)
                var archiveDatabaseService = new DatabaseService(DatabaseType.ArchiveData);
                RegisterService("ArchiveDatabase", archiveDatabaseService);

                // Initialize database migration services for all three databases
                var clientMigrationService = new DatabaseMigrationService(clientDatabaseService);
                RegisterService(clientMigrationService);

                var referenceMigrationService = new DatabaseMigrationService(referenceDatabaseService);
                RegisterService("ReferenceMigrationService", referenceMigrationService);

                var archiveMigrationService = new DatabaseMigrationService(archiveDatabaseService);
                RegisterService("ArchiveMigrationService", archiveMigrationService);

                // Apply database schema migrations for all three databases
                LoggingService.LogInfo("Initializing client database schema", "ServiceLocator");
                await clientMigrationService.InitializeSchemaAsync();

                LoggingService.LogInfo("Initializing reference database schema", "ServiceLocator");
                await referenceMigrationService.InitializeSchemaAsync();

                LoggingService.LogInfo("Initializing archive database schema", "ServiceLocator");
                await archiveMigrationService.InitializeSchemaAsync();

                // Validate schema after initialization
                bool schemaValid = await clientMigrationService.ValidateSchemaAsync();
                if (!schemaValid)
                {
                    throw new InvalidOperationException("Client database schema validation failed after initialization");
                }

                // Initialize UID generation service (uses client database)
                var uidGenerationService = new UIDGenerationService(clientDatabaseService);
                RegisterService(uidGenerationService);

                // Initialize ArchiveDatabaseService (uses archive database)
                var archiveDbService = new ArchiveDatabaseService(archiveDatabaseService);
                RegisterService(archiveDbService);

                // Initialize client database service (uses client database with audit logging)
                var clientDbService = new ClientDatabaseService(clientDatabaseService, uidGenerationService, archiveDbService);
                RegisterService(clientDbService);

                // Initialize database performance monitoring service (uses client database)
                var performanceMonitoringService = new DatabasePerformanceMonitoringService(clientDatabaseService);
                RegisterService(performanceMonitoringService);

                // Initialize enhanced database service with performance monitoring
                var enhancedDatabaseService = new EnhancedDatabaseService(clientDatabaseService, performanceMonitoringService);
                RegisterService(enhancedDatabaseService);

                // Initialize database schema validator (uses client database)
                var schemaValidator = new DatabaseSchemaValidator(clientDatabaseService);
                RegisterService(schemaValidator);

                // Initialize ActivityTypeBase service (uses reference database)
                var activityTypeService = new ActivityTypeBaseService(referenceDatabaseService);
                RegisterService(activityTypeService);

                // Initialize CraftTypeBase service (uses reference database)
                var craftTypeService = new CraftTypeBaseService(referenceDatabaseService);
                RegisterService(craftTypeService);

                // Initialize CpiLocationService (uses reference database)
                var cpiLocationService = new CpiLocationService(referenceDatabaseService);
                RegisterService(cpiLocationService);

                // Initialize archive database tables
                await archiveDbService.CreateTablesAsync();

                // Initialize Arabic search services for enhanced text processing
                InitializeArabicSearchServices();

                // Initialize FileCheckBusinessRuleService (uses client database)
                var fileCheckBusinessRuleService = new FileCheckBusinessRuleService(clientDatabaseService);
                RegisterService(fileCheckBusinessRuleService);

                // Register ClientValidationService now that DatabaseService is available
                // This service depends on DatabaseService through FileCheckBusinessRuleService
                RegisterService<ClientValidationService>(new ClientValidationService());

                // Initialize client folder management service
                var clientFolderManagementService = new ClientFolderManagementService();
                RegisterService(clientFolderManagementService);

                // Register DuplicateClientDetectionService for duplicate client detection functionality
                RegisterService<DuplicateClientDetectionService>(new DuplicateClientDetectionService());

                // Create ActivityTypeBase table and seed data if needed
                await activityTypeService.CreateTableAsync();
                bool hasData = await activityTypeService.HasDataAsync();
                if (!hasData)
                {
                    LoggingService.LogInfo("Seeding activity type data from embedded resource", "ServiceLocator");
                    var progress = new Progress<double>(value =>
                    {
                        LoggingService.LogDebug($"Activity type import progress: {value:P0}", "ServiceLocator");
                    });
                    int importedCount = await activityTypeService.SeedActivityTypeData(progress);
                    LoggingService.LogInfo($"Successfully imported {importedCount} activity types", "ServiceLocator");
                }

                // Create CraftTypeBase table and seed data if needed
                await craftTypeService.CreateTableAsync();
                bool hasCraftData = await craftTypeService.HasDataAsync();
                if (!hasCraftData)
                {
                    LoggingService.LogInfo("Seeding craft type data from embedded resource", "ServiceLocator");
                    var craftProgress = new Progress<double>(value =>
                    {
                        LoggingService.LogDebug($"Craft type import progress: {value:P0}", "ServiceLocator");
                    });
                    int craftImportedCount = await craftTypeService.SeedCraftTypeData(craftProgress);
                    LoggingService.LogInfo($"Successfully imported {craftImportedCount} craft types", "ServiceLocator");
                }

                // Seed CPI location data if needed
                LoggingService.LogInfo("Seeding CPI location data from embedded resource", "ServiceLocator");
                var cpiLocationResult = await cpiLocationService.SeedCpiLocationDataAsync();
                if (cpiLocationResult.IsSuccess)
                {
                    LoggingService.LogInfo($"Successfully seeded CPI location data - Wilayas: {cpiLocationResult.WilayasCount}, Dairas: {cpiLocationResult.DairasCount}", "ServiceLocator");
                }
                else
                {
                    LoggingService.LogWarning($"CPI location data seeding failed: {cpiLocationResult.Message}", "ServiceLocator");
                }

                // Get database statistics for monitoring
                var clientStats = await clientMigrationService.GetDatabaseStatsAsync();
                LoggingService.LogInfo($"Three-database architecture initialized - Client DB Version: {clientStats.SchemaVersion}, Tables: {clientStats.TableCount}, Indexes: {clientStats.IndexCount}", "ServiceLocator");

                // Validate that all required database services are registered
                bool servicesValid = ValidateDatabaseServices();
                if (!servicesValid)
                {
                    LoggingService.LogWarning("Some required database services are missing", "ServiceLocator");
                }

                // Initialize cache coordinator after all cacheable services are registered
                InitializeCacheCoordinator();

                // Initialize cache monitoring service
                InitializeCacheMonitoring();

                // Initialize memory pressure handler
                InitializeMemoryPressureHandler();

                // Perform initial cache warmup for critical services
                await CoordinateWarmupAsync();



                // Log service registration status for debugging
                LogServiceRegistrationStatus();

                LoggingService.LogDebug("Database services initialized successfully", "ServiceLocator");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing database services: {ex.Message}", "ServiceLocator");
                
                // Register basic database service to prevent application crashes
                try
                {
                    if (!IsServiceRegistered<DatabaseService>())
                    {
                        RegisterService(new DatabaseService());
                        LoggingService.LogWarning("Registered basic database service as fallback", "ServiceLocator");
                    }
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Failed to register fallback database service: {fallbackEx.Message}", "ServiceLocator");
                }

                throw;
            }
        }

        /// <summary>
        /// Registers a service instance
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="instance">The service instance</param>
        public static void RegisterService<T>(T instance) where T : class
        {
            if (instance == null)
                throw new ArgumentNullException(nameof(instance));

            var serviceType = typeof(T);

            if (_services.ContainsKey(serviceType))
            {
                LoggingService.LogWarning($"Service {serviceType.Name} is already registered, replacing", "ServiceLocator");
                _services[serviceType] = instance;
            }
            else
            {
                _services.Add(serviceType, instance);
                LoggingService.LogDebug($"Service {serviceType.Name} registered", "ServiceLocator");
            }

            // Track cacheable services for cache management
            if (instance is ICacheableService cacheableService)
            {
                _cacheableServices.Add(cacheableService);

                // Register with cache coordinator if available
                if (_cacheCoordinator != null)
                {
                    _cacheCoordinator.RegisterCacheableService(cacheableService);
                }

                LoggingService.LogDebug($"Registered cacheable service: {cacheableService.ServiceName}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Registers a service instance with a specific name/key
        /// </summary>
        /// <param name="serviceName">The name/key for the service</param>
        /// <param name="instance">The service instance</param>
        public static void RegisterService(string serviceName, object instance)
        {
            if (string.IsNullOrWhiteSpace(serviceName))
                throw new ArgumentException("Service name cannot be null or empty", nameof(serviceName));
            if (instance == null)
                throw new ArgumentNullException(nameof(instance));

            if (_namedServices.ContainsKey(serviceName))
            {
                LoggingService.LogWarning($"Named service '{serviceName}' is already registered, replacing", "ServiceLocator");
                _namedServices[serviceName] = instance;
            }
            else
            {
                _namedServices.Add(serviceName, instance);
                LoggingService.LogDebug($"Named service '{serviceName}' registered with type {instance.GetType().Name}", "ServiceLocator");
            }

            // Track cacheable services for cache management
            if (instance is ICacheableService cacheableService)
            {
                _cacheableServices.Add(cacheableService);

                // Register with cache coordinator if available
                if (_cacheCoordinator != null)
                {
                    _cacheCoordinator.RegisterCacheableService(cacheableService);
                }

                LoggingService.LogDebug($"Registered cacheable named service: {cacheableService.ServiceName}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Gets a service instance
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>The service instance</returns>
        /// <exception cref="InvalidOperationException">Thrown when the service is not registered</exception>
        public static T GetService<T>() where T : class
        {
            var serviceType = typeof(T);
            
            if (_services.TryGetValue(serviceType, out var service))
            {
                return (T)service;
            }

            throw new InvalidOperationException($"Service {serviceType.Name} is not registered");
        }

        /// <summary>
        /// Gets a named service instance
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="serviceName">The name/key of the service</param>
        /// <returns>The service instance</returns>
        /// <exception cref="InvalidOperationException">Thrown when the named service is not registered</exception>
        public static T GetService<T>(string serviceName) where T : class
        {
            if (string.IsNullOrWhiteSpace(serviceName))
                throw new ArgumentException("Service name cannot be null or empty", nameof(serviceName));

            if (_namedServices.TryGetValue(serviceName, out var service))
            {
                if (service is T typedService)
                {
                    return typedService;
                }
                throw new InvalidOperationException($"Named service '{serviceName}' is not of type {typeof(T).Name}");
            }

            throw new InvalidOperationException($"Named service '{serviceName}' is not registered");
        }

        /// <summary>
        /// Tries to get a service instance
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="service">The service instance if found</param>
        /// <returns>True if the service was found, false otherwise</returns>
        public static bool TryGetService<T>(out T? service) where T : class
        {
            var serviceType = typeof(T);
            
            if (_services.TryGetValue(serviceType, out var serviceObj))
            {
                service = (T)serviceObj;
                return true;
            }

            service = null;
            return false;
        }

        /// <summary>
        /// Checks if a service is registered
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>True if the service is registered, false otherwise</returns>
        public static bool IsServiceRegistered<T>() where T : class
        {
            return _services.ContainsKey(typeof(T));
        }

        /// <summary>
        /// Unregisters a service
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>True if the service was unregistered, false if it wasn't registered</returns>
        public static bool UnregisterService<T>() where T : class
        {
            var serviceType = typeof(T);
            
            if (_services.Remove(serviceType))
            {
                LoggingService.LogDebug($"Service {serviceType.Name} unregistered", "ServiceLocator");
                return true;
            }

            return false;
        }

        /// <summary>
        /// Clears all registered services and disposes IDisposable services
        /// </summary>
        public static void Clear()
        {
            DisposeServices();
            _services.Clear();
            _namedServices.Clear();
            _isInitialized = false;
            LoggingService.LogDebug("ServiceLocator cleared", "ServiceLocator");
        }

        /// <summary>
        /// Gets all registered service types
        /// </summary>
        /// <returns>Collection of registered service types</returns>
        public static IEnumerable<Type> GetRegisteredServiceTypes()
        {
            return _services.Keys.ToList();
        }

        /// <summary>
        /// Gets the count of registered services
        /// </summary>
        /// <returns>Number of registered services</returns>
        public static int GetServiceCount()
        {
            return _services.Count;
        }

        /// <summary>
        /// Gets a summary of all registered services for debugging and monitoring
        /// </summary>
        /// <returns>Dictionary with service names and their registration status</returns>
        public static Dictionary<string, bool> GetServiceRegistrationSummary()
        {
            var summary = new Dictionary<string, bool>();

            // Check core services
            summary["DatabaseService"] = IsServiceRegistered<DatabaseService>();
            summary["DatabaseMigrationService"] = IsServiceRegistered<DatabaseMigrationService>();
            summary["UIDGenerationService"] = IsServiceRegistered<UIDGenerationService>();
            summary["ClientDatabaseService"] = IsServiceRegistered<ClientDatabaseService>();
            summary["DatabasePerformanceMonitoringService"] = IsServiceRegistered<DatabasePerformanceMonitoringService>();
            summary["EnhancedDatabaseService"] = IsServiceRegistered<EnhancedDatabaseService>();
            summary["DatabaseSchemaValidator"] = IsServiceRegistered<DatabaseSchemaValidator>();
            summary["ActivityTypeBaseService"] = IsServiceRegistered<ActivityTypeBaseService>();
            summary["ClientValidationService"] = IsServiceRegistered<ClientValidationService>();

            // Check existing services
            summary["ValidationService"] = IsServiceRegistered<ValidationService>();
            summary["IToastService"] = IsServiceRegistered<IToastService>();
            summary["IWindowChromeService"] = IsServiceRegistered<IWindowChromeService>();

            return summary;
        }

        /// <summary>
        /// Logs the current service registration status for debugging
        /// </summary>
        public static void LogServiceRegistrationStatus()
        {
            try
            {
                LoggingService.LogInfo("=== Service Registration Status ===", "ServiceLocator");
                
                var summary = GetServiceRegistrationSummary();
                foreach (var (serviceName, isRegistered) in summary)
                {
                    var status = isRegistered ? "✓ Registered" : "✗ Not Registered";
                    LoggingService.LogInfo($"{serviceName}: {status}", "ServiceLocator");
                }

                LoggingService.LogInfo($"Total registered services: {GetServiceCount()}", "ServiceLocator");
                LoggingService.LogInfo("=== End Service Registration Status ===", "ServiceLocator");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error logging service registration status: {ex.Message}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Validates that all required database services are registered
        /// </summary>
        /// <returns>True if all required services are registered</returns>
        public static bool ValidateDatabaseServices()
        {
            var requiredServices = new[]
            {
                typeof(DatabaseService),
                typeof(DatabaseMigrationService),
                typeof(UIDGenerationService),
                typeof(ClientDatabaseService),
                typeof(DatabasePerformanceMonitoringService),
                typeof(EnhancedDatabaseService),
                typeof(DatabaseSchemaValidator),
                typeof(ActivityTypeBaseService),
                typeof(CraftTypeBaseService),
                typeof(CpiLocationService),
                typeof(FileCheckBusinessRuleService),
                typeof(ClientValidationService)
            };

            foreach (var serviceType in requiredServices)
            {
                if (!_services.ContainsKey(serviceType))
                {
                    LoggingService.LogWarning($"Required database service {serviceType.Name} is not registered", "ServiceLocator");
                    return false;
                }
            }

            LoggingService.LogDebug("All required database services are registered", "ServiceLocator");
            return true;
        }

        /// <summary>
        /// Example of how ViewModels should access database services through ServiceLocator
        /// This demonstrates the proper pattern for dependency injection in UFU2
        /// </summary>
        /// <example>
        /// // In ViewModel constructor:
        /// if (ServiceLocator.TryGetService&lt;ClientDatabaseService&gt;(out var clientDbService))
        /// {
        ///     _clientDatabaseService = clientDbService;
        /// }
        /// 
        /// if (ServiceLocator.TryGetService&lt;UIDGenerationService&gt;(out var uidService))
        /// {
        ///     _uidGenerationService = uidService;
        /// }
        /// 
        /// // In ViewModel methods:
        /// var clientUID = await _uidGenerationService.GenerateClientUIDAsync(nameFr);
        /// await _clientDatabaseService.CreateClientAsync(clientData);
        /// </example>
        


        /// <summary>
        /// Resolves service dependencies for a given service type
        /// This method helps identify what services depend on others
        /// </summary>
        /// <typeparam name="T">The service type to analyze</typeparam>
        /// <returns>List of dependency types</returns>
        public static List<Type> GetServiceDependencies<T>() where T : class
        {
            var serviceType = typeof(T);
            var dependencies = new List<Type>();

            // Get constructor parameters to identify dependencies
            var constructors = serviceType.GetConstructors();
            foreach (var constructor in constructors)
            {
                var parameters = constructor.GetParameters();
                foreach (var parameter in parameters)
                {
                    if (_services.ContainsKey(parameter.ParameterType))
                    {
                        dependencies.Add(parameter.ParameterType);
                    }
                }
            }

            return dependencies;
        }

        /// <summary>
        /// Manages the lifecycle of a service, ensuring proper initialization and disposal
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <param name="serviceFactory">Factory function to create the service</param>
        /// <param name="initializeAction">Optional initialization action</param>
        /// <returns>The created and registered service</returns>
        public static async Task<T> ManageServiceLifecycleAsync<T>(Func<T> serviceFactory, Func<T, Task>? initializeAction = null) where T : class
        {
            try
            {
                LoggingService.LogDebug($"Managing lifecycle for service {typeof(T).Name}", "ServiceLocator");

                // Create the service
                var service = serviceFactory();
                
                // Initialize if needed
                if (initializeAction != null)
                {
                    await initializeAction(service);
                }

                // Register the service
                RegisterService(service);

                LoggingService.LogDebug($"Service {typeof(T).Name} lifecycle managed successfully", "ServiceLocator");
                return service;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error managing lifecycle for service {typeof(T).Name}: {ex.Message}", "ServiceLocator");
                throw;
            }
        }

        #region Memory Management Services (Phase 2D)

        /// <summary>
        /// Initializes Arabic search services for enhanced text processing and exact prefix matching.
        /// </summary>
        private static void InitializeArabicSearchServices()
        {
            try
            {
                LoggingService.LogInfo("Initializing Arabic search services with exact prefix matching", "ServiceLocator");

                // Register ArabicTextAnalyzer for advanced Arabic text processing
                var arabicTextAnalyzer = new ArabicTextAnalyzer();
                RegisterService<IArabicTextAnalyzer>(arabicTextAnalyzer);

                // Register WordFrequencySearchService for enhanced search with exact prefix matching
                var wordFrequencySearchService = new WordFrequencySearchService(arabicTextAnalyzer);
                RegisterService(wordFrequencySearchService);

                LoggingService.LogInfo("Arabic search services with exact prefix matching initialized successfully", "ServiceLocator");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing Arabic search services: {ex.Message}", "ServiceLocator");
                throw;
            }
        }

        /// <summary>
        /// Initializes memory management services for Phase 2D optimization.
        /// Creates ResourceManager, WeakEventManager, and MemoryLeakDetectionService.
        /// </summary>
        private static void InitializeMemoryManagementServices()
        {
            try
            {
                LoggingService.LogInfo("Initializing Phase 2D memory management services", "ServiceLocator");

                // Initialize ResourceManager first (no dependencies)
                var resourceManager = new ResourceManager();
                RegisterService<ResourceManager>(resourceManager);
                LoggingService.LogDebug("ResourceManager registered successfully", "ServiceLocator");

                // Initialize WeakEventManager with ResourceManager integration
                var weakEventManager = new WeakEventManager(resourceManager);
                RegisterService<WeakEventManager>(weakEventManager);
                LoggingService.LogDebug("WeakEventManager registered successfully", "ServiceLocator");

                // Initialize MemoryLeakDetectionService with both dependencies
                var memoryLeakDetectionService = new MemoryLeakDetectionService(resourceManager, weakEventManager);
                RegisterService<MemoryLeakDetectionService>(memoryLeakDetectionService);
                LoggingService.LogDebug("MemoryLeakDetectionService registered successfully", "ServiceLocator");

                LoggingService.LogInfo("Phase 2D memory management services initialized successfully", "ServiceLocator");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing memory management services: {ex.Message}", "ServiceLocator");
                throw;
            }
        }

        #endregion

        #region Cache Management

        /// <summary>
        /// Initializes the cache coordinator service for advanced cache management.
        /// Should be called after all cacheable services are registered.
        /// </summary>
        public static void InitializeCacheCoordinator()
        {
            try
            {
                if (_cacheCoordinator == null)
                {
                    _cacheCoordinator = new CacheCoordinatorService();
                    RegisterService(_cacheCoordinator);

                    // Register all existing cacheable services with the coordinator
                    foreach (var cacheableService in _cacheableServices)
                    {
                        _cacheCoordinator.RegisterCacheableService(cacheableService);
                    }

                    LoggingService.LogInfo($"Cache coordinator initialized with {_cacheableServices.Count} cacheable services", "ServiceLocator");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing cache coordinator: {ex.Message}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Gets the cache coordinator service for advanced cache operations.
        /// </summary>
        /// <returns>Cache coordinator service instance, or null if not initialized</returns>
        public static CacheCoordinatorService? GetCacheCoordinator()
        {
            return _cacheCoordinator;
        }

        /// <summary>
        /// Initializes the cache monitoring service for comprehensive cache monitoring.
        /// Should be called after the cache coordinator is initialized.
        /// </summary>
        public static void InitializeCacheMonitoring()
        {
            try
            {
                if (_cacheCoordinator != null && _cacheMonitoring == null)
                {
                    _cacheMonitoring = new CacheMonitoringService(_cacheCoordinator);
                    RegisterService(_cacheMonitoring);

                    LoggingService.LogInfo("Cache monitoring service initialized", "ServiceLocator");
                }
                else if (_cacheCoordinator == null)
                {
                    LoggingService.LogWarning("Cannot initialize cache monitoring without cache coordinator", "ServiceLocator");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing cache monitoring: {ex.Message}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Gets the cache monitoring service for cache monitoring operations.
        /// </summary>
        /// <returns>Cache monitoring service instance, or null if not initialized</returns>
        public static CacheMonitoringService? GetCacheMonitoring()
        {
            return _cacheMonitoring;
        }

        /// <summary>
        /// Initializes the memory pressure handler for memory management.
        /// Should be called after the cache coordinator is initialized.
        /// </summary>
        public static void InitializeMemoryPressureHandler()
        {
            try
            {
                if (_cacheCoordinator != null && _memoryPressureHandler == null)
                {
                    _memoryPressureHandler = new MemoryPressureHandler(_cacheCoordinator);
                    RegisterService(_memoryPressureHandler);

                    LoggingService.LogInfo("Memory pressure handler initialized", "ServiceLocator");
                }
                else if (_cacheCoordinator == null)
                {
                    LoggingService.LogWarning("Cannot initialize memory pressure handler without cache coordinator", "ServiceLocator");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing memory pressure handler: {ex.Message}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Gets the memory pressure handler for memory management operations.
        /// </summary>
        /// <returns>Memory pressure handler instance, or null if not initialized</returns>
        public static MemoryPressureHandler? GetMemoryPressureHandler()
        {
            return _memoryPressureHandler;
        }

        /// <summary>
        /// Coordinates cache invalidation across all affected services.
        /// </summary>
        /// <param name="invalidationContext">Context information about the data change</param>
        public static async Task CoordinateInvalidationAsync(CacheInvalidationContext invalidationContext)
        {
            try
            {
                if (_cacheCoordinator != null)
                {
                    await _cacheCoordinator.CoordinateInvalidationAsync(invalidationContext);
                }
                else
                {
                    // Fallback to basic cache clearing
                    ClearAllCaches();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during coordinated cache invalidation: {ex.Message}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Coordinates cache warmup across all registered services.
        /// </summary>
        public static async Task CoordinateWarmupAsync()
        {
            try
            {
                if (_cacheCoordinator != null)
                {
                    await _cacheCoordinator.CoordinateWarmupAsync();
                }
                else
                {
                    // Fallback to individual service warmup
                    foreach (var cacheableService in _cacheableServices)
                    {
                        try
                        {
                            await cacheableService.WarmupCacheAsync();
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error warming up cache for service {cacheableService.ServiceName}: {ex.Message}", "ServiceLocator");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during coordinated cache warmup: {ex.Message}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Clears all caches across all registered cacheable services.
        /// Useful for global cache invalidation when data changes affect multiple services.
        /// </summary>
        public static void ClearAllCaches()
        {
            try
            {
                LoggingService.LogDebug($"Clearing caches for {_cacheableServices.Count} cacheable services", "ServiceLocator");

                foreach (var cacheableService in _cacheableServices)
                {
                    try
                    {
                        cacheableService.ClearCache();
                        LoggingService.LogDebug($"Cleared cache for service: {cacheableService.ServiceName}", "ServiceLocator");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error clearing cache for service {cacheableService.ServiceName}: {ex.Message}", "ServiceLocator");
                    }
                }

                LoggingService.LogInfo("All service caches cleared successfully", "ServiceLocator");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during global cache clearing: {ex.Message}", "ServiceLocator");
            }
        }

        /// <summary>
        /// Gets cache statistics from all registered cacheable services.
        /// Useful for monitoring cache performance across the application.
        /// </summary>
        /// <returns>Dictionary containing cache statistics grouped by service name</returns>
        public static Dictionary<string, Dictionary<string, object>> GetAllCacheStatistics()
        {
            var allStats = new Dictionary<string, Dictionary<string, object>>();

            try
            {
                foreach (var cacheableService in _cacheableServices)
                {
                    try
                    {
                        var serviceStats = cacheableService.GetCacheStatistics();
                        allStats[cacheableService.ServiceName] = serviceStats;
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error getting cache statistics for service {cacheableService.ServiceName}: {ex.Message}", "ServiceLocator");
                        allStats[cacheableService.ServiceName] = new Dictionary<string, object> { ["Error"] = ex.Message };
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error collecting cache statistics: {ex.Message}", "ServiceLocator");
            }

            return allStats;
        }

        /// <summary>
        /// Gets the list of registered cacheable services.
        /// </summary>
        /// <returns>List of service names that support caching</returns>
        public static List<string> GetCacheableServiceNames()
        {
            return _cacheableServices.Select(s => s.ServiceName).ToList();
        }

        /// <summary>
        /// Clears cache for a specific service by name.
        /// </summary>
        /// <param name="serviceName">The name of the service to clear cache for</param>
        /// <returns>True if the service was found and cache cleared, false otherwise</returns>
        public static bool ClearCacheForService(string serviceName)
        {
            try
            {
                var service = _cacheableServices.FirstOrDefault(s => s.ServiceName == serviceName);
                if (service != null)
                {
                    service.ClearCache();
                    LoggingService.LogDebug($"Cleared cache for service: {serviceName}", "ServiceLocator");
                    return true;
                }

                LoggingService.LogWarning($"Cacheable service not found: {serviceName}", "ServiceLocator");
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing cache for service {serviceName}: {ex.Message}", "ServiceLocator");
                return false;
            }
        }

        #endregion

        /// <summary>
        /// Disposes all registered services that implement IDisposable
        /// Services are disposed in reverse order of registration to handle dependencies
        /// </summary>
        public static void DisposeServices()
        {
            try
            {
                LoggingService.LogDebug("Disposing registered services", "ServiceLocator");

                // Get all services (both typed and named) in reverse order to handle dependencies properly
                var allServices = _services.Values.Concat(_namedServices.Values).Reverse().ToList();

                foreach (var service in allServices)
                {
                    if (service is IDisposable disposableService)
                    {
                        try
                        {
                            disposableService.Dispose();
                            LoggingService.LogDebug($"Disposed service: {service.GetType().Name}", "ServiceLocator");
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error disposing service {service.GetType().Name}: {ex.Message}", "ServiceLocator");
                        }
                    }
                }

                // Clear the cacheable services list
                _cacheableServices.Clear();

                // Clear the services dictionary
                _services.Clear();

                // Reset initialization flag
                _isInitialized = false;

                LoggingService.LogDebug("Service disposal completed", "ServiceLocator");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during service disposal: {ex.Message}", "ServiceLocator");
            }
        }
    }

    /// <summary>
    /// Static helper class for accessing ThemeManager functionality
    /// Provides a consistent interface for theme operations throughout the application
    /// </summary>
    public static class ThemeService
    {
        /// <summary>
        /// Gets the current application theme
        /// </summary>
        public static ApplicationTheme CurrentTheme => ThemeManager.CurrentTheme;

        /// <summary>
        /// Gets whether the theme manager is initialized
        /// </summary>
        public static bool IsInitialized => ThemeManager.IsInitialized;

        /// <summary>
        /// Event raised when the theme changes
        /// </summary>
        public static event EventHandler<ThemeChangedEventArgs>? ThemeChanged
        {
            add => ThemeManager.ThemeChanged += value;
            remove => ThemeManager.ThemeChanged -= value;
        }

        /// <summary>
        /// Switches to the specified theme
        /// </summary>
        /// <param name="theme">The theme to switch to</param>
        /// <returns>True if the theme switch was successful</returns>
        public static async Task<bool> SwitchThemeAsync(ApplicationTheme theme)
        {
            return await ThemeManager.SwitchThemeAsync(theme);
        }

        /// <summary>
        /// Toggles between light and dark themes
        /// </summary>
        /// <returns>True if the theme toggle was successful</returns>
        public static async Task<bool> ToggleThemeAsync()
        {
            var newTheme = CurrentTheme == ApplicationTheme.Dark 
                ? ApplicationTheme.Light 
                : ApplicationTheme.Dark;
            
            return await SwitchThemeAsync(newTheme);
        }

        /// <summary>
        /// Gets a theme color resource
        /// </summary>
        /// <param name="resourceKey">The resource key</param>
        /// <returns>The color or null if not found</returns>
        public static System.Windows.Media.Color? GetThemeColor(string resourceKey)
        {
            return ThemeManager.GetThemeColor(resourceKey);
        }

        /// <summary>
        /// Forces a UI refresh to apply theme changes
        /// </summary>
        public static void RefreshUI()
        {
            ThemeManager.RefreshUI();
        }
    }
}
