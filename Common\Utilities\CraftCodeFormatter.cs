using System;
using System.Linq;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Utility class for formatting craft codes in XX-XX-XXX pattern.
    /// Provides consistent craft code formatting across the UFU2 application.
    /// </summary>
    public static class CraftCodeFormatter
    {
        /// <summary>
        /// Formats a string of digits into craft code format XX-XX-XXX.
        /// </summary>
        /// <param name="digits">String containing only digits</param>
        /// <returns>Formatted craft code string</returns>
        public static string FormatCraftCode(string digits)
        {
            if (string.IsNullOrEmpty(digits))
                return string.Empty;

            return digits.Length switch
            {
                1 => digits,
                2 => digits,
                3 => $"{digits.Substring(0, 2)}-{digits.Substring(2)}",
                4 => $"{digits.Substring(0, 2)}-{digits.Substring(2)}",
                5 => $"{digits.Substring(0, 2)}-{digits.Substring(2, 2)}-{digits.Substring(4)}",
                6 => $"{digits.Substring(0, 2)}-{digits.Substring(2, 2)}-{digits.Substring(4)}",
                7 => $"{digits.Substring(0, 2)}-{digits.Substring(2, 2)}-{digits.Substring(4)}",
                _ => digits
            };
        }

        /// <summary>
        /// Calculates the new cursor position after formatting craft code text.
        /// Maintains cursor position relative to the number of digits typed.
        /// </summary>
        /// <param name="originalText">Original text before formatting</param>
        /// <param name="formattedText">Text after formatting</param>
        /// <param name="originalCursor">Original cursor position</param>
        /// <returns>New cursor position</returns>
        public static int CalculateNewCursorPosition(string originalText, string formattedText, int originalCursor)
        {
            // Count digits before cursor in original text
            int digitsBefore = originalText.Take(originalCursor).Count(char.IsDigit);

            // Find position in formatted text that corresponds to the same number of digits
            int newPosition = 0;
            int digitCount = 0;

            for (int i = 0; i < formattedText.Length && digitCount < digitsBefore; i++)
            {
                if (char.IsDigit(formattedText[i]))
                {
                    digitCount++;
                }
                newPosition = i + 1;
            }

            return newPosition;
        }

        /// <summary>
        /// Processes craft code input by removing non-digits, limiting length, and formatting.
        /// This is the main method to use for TextChanged event handlers.
        /// </summary>
        /// <param name="input">Raw input text from TextBox</param>
        /// <returns>Formatted craft code string</returns>
        public static string ProcessCraftCodeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Remove all non-digit characters
            string digitsOnly = new string(input.Where(char.IsDigit).ToArray());

            // Limit to 7 digits maximum
            if (digitsOnly.Length > 7)
            {
                digitsOnly = digitsOnly.Substring(0, 7);
            }

            // Format with dashes: XX-XX-XXX
            return FormatCraftCode(digitsOnly);
        }

        /// <summary>
        /// Validates if a craft code follows the correct XX-XX-XXX format.
        /// </summary>
        /// <param name="code">The code to validate</param>
        /// <returns>True if valid craft code format</returns>
        public static bool IsValidCraftCodeFormat(string code)
        {
            if (string.IsNullOrEmpty(code) || code.Length != 9)
                return false;

            // Check format: XX-XX-XXX
            return code[2] == '-' &&
                   code[5] == '-' &&
                   char.IsDigit(code[0]) && char.IsDigit(code[1]) &&
                   char.IsDigit(code[3]) && char.IsDigit(code[4]) &&
                   char.IsDigit(code[6]) && char.IsDigit(code[7]) && char.IsDigit(code[8]);
        }
    }
}
