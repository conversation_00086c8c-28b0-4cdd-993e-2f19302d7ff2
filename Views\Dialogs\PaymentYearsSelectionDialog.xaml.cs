using System;
using System.Collections.Generic;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using UFU2.ViewModels;
using UFU2.Common.Utilities;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for PaymentYearsSelectionDialog.xaml
    /// A UserControl that provides a dialog for selecting payment years with tab-based organization.
    /// Integrates with MaterialDesign DialogHost for consistent modal behavior
    /// and follows UFU2 design patterns with Arabic RTL layout support.
    /// </summary>
    public partial class PaymentYearsSelectionDialog : UserControl
    {
        #region Private Fields
        private PaymentYearsSelectionDialogViewModel _viewModel;
        private bool _dialogResult = false;
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the PaymentYearsSelectionDialog class.
        /// </summary>
        /// <param name="activityStartDate">The activity start date in DD/MM/YYYY format</param>
        public PaymentYearsSelectionDialog(string activityStartDate)
        {
            InitializeComponent();

            // Calculate year range from activity start date using shared utility
            var yearRange = PaymentYearRangeCalculator.CalculateYearRange(activityStartDate);
            
            // Initialize ViewModel
            _viewModel = new PaymentYearsSelectionDialogViewModel(yearRange.StartYear, yearRange.EndYear);
            DataContext = _viewModel;
        }

        /// <summary>
        /// Initializes a new instance of the PaymentYearsSelectionDialog class with existing selections.
        /// Always applies the provided selections to restore the exact user state.
        /// Empty lists represent explicit user choice to uncheck all years.
        /// </summary>
        /// <param name="activityStartDate">The activity start date in DD/MM/YYYY format</param>
        /// <param name="g12SelectedYears">Previously selected G12 years (empty list = user unchecked all)</param>
        /// <param name="bisSelectedYears">Previously selected BIS years (empty list = user unchecked all)</param>
        public PaymentYearsSelectionDialog(string activityStartDate, List<int> g12SelectedYears, List<int> bisSelectedYears)
            : this(activityStartDate)
        {
            // Always apply provided selections to restore exact user state
            // Empty lists mean user explicitly unchecked all years, not first-time use
            if (g12SelectedYears != null)
            {
                _viewModel.SetSelectedYears("G12", g12SelectedYears);
            }
            if (bisSelectedYears != null)
            {
                _viewModel.SetSelectedYears("BIS", bisSelectedYears);
            }
        }
        #endregion

        #region Properties
        /// <summary>
        /// Gets the dialog result indicating whether the user saved or cancelled.
        /// </summary>
        public bool DialogResult => _dialogResult;

        /// <summary>
        /// Gets the selected G12 years.
        /// </summary>
        public List<int> SelectedG12Years => _viewModel?.GetSelectedYears("G12") ?? new List<int>();

        /// <summary>
        /// Gets the selected BIS years.
        /// </summary>
        public List<int> SelectedBISYears => _viewModel?.GetSelectedYears("BIS") ?? new List<int>();
        #endregion

        #region Loaded Visual State Fix
        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            // Force G12Tab to update its checked state visually
            if (G12Tab != null)
            {
                G12Tab.IsChecked = false;
                G12Tab.UpdateLayout();
                G12Tab.IsChecked = true;
                G12Tab.UpdateLayout();
            }
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// Handles the Save button click event.
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            _dialogResult = true;

            // Close the dialog with positive result using the identifier
            MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(true, null);
        }

        /// <summary>
        /// Handles the Close button click event.
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            _dialogResult = false;

            // Close the dialog with negative result using the identifier
            MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(false, null);
        }
        #endregion
    }
}
