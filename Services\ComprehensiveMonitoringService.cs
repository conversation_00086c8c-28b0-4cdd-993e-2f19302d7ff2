using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.Services
{
    /// <summary>
    /// Comprehensive monitoring service that orchestrates all validation and monitoring tools.
    /// Provides centralized access to schema validation, UID validation, performance monitoring,
    /// and business rule validation with integrated reporting and alerting capabilities.
    /// </summary>
    public class ComprehensiveMonitoringService : IDisposable
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;
        private readonly DatabaseSchemaValidator _schemaValidator;
        private readonly DatabaseValidationService _validationService;
        private readonly DatabasePerformanceMonitoringService _performanceMonitoringService;
        private readonly UIDGenerationService _uidGenerationService;
        private readonly Timer _scheduledValidationTimer;
        private readonly object _monitoringLock = new object();
        private bool _disposed = false;

        // Monitoring configuration
        private const int ScheduledValidationIntervalHours = 6;
        private const int MaxValidationHistoryCount = 100;

        // Validation history
        private readonly List<ComprehensiveValidationResult> _validationHistory = new List<ComprehensiveValidationResult>();

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ComprehensiveMonitoringService.
        /// </summary>
        /// <param name="databaseService">The database service instance</param>
        /// <param name="schemaValidator">The schema validator instance</param>
        /// <param name="validationService">The validation service instance</param>
        /// <param name="performanceMonitoringService">The performance monitoring service instance</param>
        /// <param name="uidGenerationService">The UID generation service instance</param>
        public ComprehensiveMonitoringService(
            DatabaseService databaseService,
            DatabaseSchemaValidator schemaValidator,
            DatabaseValidationService validationService,
            DatabasePerformanceMonitoringService performanceMonitoringService,
            UIDGenerationService uidGenerationService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _schemaValidator = schemaValidator ?? throw new ArgumentNullException(nameof(schemaValidator));
            _validationService = validationService ?? throw new ArgumentNullException(nameof(validationService));
            _performanceMonitoringService = performanceMonitoringService ?? throw new ArgumentNullException(nameof(performanceMonitoringService));
            _uidGenerationService = uidGenerationService ?? throw new ArgumentNullException(nameof(uidGenerationService));

            // Initialize scheduled validation timer (runs every 6 hours)
            _scheduledValidationTimer = new Timer(
                callback: async _ => await PerformScheduledValidationAsync(),
                state: null,
                dueTime: TimeSpan.FromHours(ScheduledValidationIntervalHours),
                period: TimeSpan.FromHours(ScheduledValidationIntervalHours)
            );

            LoggingService.LogInfo("ComprehensiveMonitoringService initialized with scheduled validation every 6 hours", "ComprehensiveMonitoringService");
        }

        #endregion

        #region Comprehensive Validation

        /// <summary>
        /// Performs comprehensive validation of all database aspects with detailed reporting.
        /// </summary>
        /// <param name="includePerformanceAnalysis">Whether to include performance analysis</param>
        /// <returns>Comprehensive validation result</returns>
        public async Task<ComprehensiveValidationResult> PerformComprehensiveValidationAsync(bool includePerformanceAnalysis = true)
        {
            try
            {
                LoggingService.LogInfo("Starting comprehensive database validation and monitoring", "ComprehensiveMonitoringService");

                // Perform the comprehensive validation
                var result = await _validationService.PerformComprehensiveValidationAsync();

                // Add performance analysis if requested
                if (includePerformanceAnalysis)
                {
                    await EnhanceWithPerformanceAnalysisAsync(result);
                }

                // Store in validation history
                StoreValidationResult(result);

                // Log summary
                LoggingService.LogInfo(result.GetComprehensiveSummary(), "ComprehensiveMonitoringService");

                // Generate alerts if needed
                await GenerateAlertsIfNeededAsync(result);

                return result;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في التحقق الشامل من قاعدة البيانات", "خطأ في المراقبة", LogLevel.Error, "ComprehensiveMonitoringService");
                throw;
            }
        }

        /// <summary>
        /// Enhances validation result with performance analysis.
        /// </summary>
        /// <param name="result">Validation result to enhance</param>
        private async Task EnhanceWithPerformanceAnalysisAsync(ComprehensiveValidationResult result)
        {
            try
            {
                // Generate performance report for the last hour
                var endTime = DateTime.UtcNow;
                var startTime = endTime.AddHours(-1);
                var performanceReport = _performanceMonitoringService.GeneratePerformanceReport(startTime, endTime);

                // Analyze index effectiveness
                var indexAnalysis = await _performanceMonitoringService.AnalyzeIndexEffectivenessAsync();

                // Add performance insights to validation details
                if (result.SchemaValidation != null)
                {
                    result.SchemaValidation.ValidationDetails.Add($"Performance Report: {performanceReport.TotalQueries} queries, {performanceReport.SlowQueryCount} slow queries");
                    result.SchemaValidation.ValidationDetails.Add($"Index Analysis: {indexAnalysis.TotalIndexes} indexes, {indexAnalysis.UnusedIndexes.Count} unused");

                    // Add performance warnings
                    if (performanceReport.SlowQueryPercentage > 10)
                    {
                        result.SchemaValidation.Warnings.Add($"High percentage of slow queries: {performanceReport.SlowQueryPercentage:F1}%");
                    }

                    if (indexAnalysis.UnusedIndexes.Count > 5)
                    {
                        result.SchemaValidation.Warnings.Add($"Many unused indexes detected: {indexAnalysis.UnusedIndexes.Count}");
                    }
                }

                LoggingService.LogDebug("Enhanced validation result with performance analysis", "ComprehensiveMonitoringService");
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to enhance validation with performance analysis: {ex.Message}", "ComprehensiveMonitoringService");
            }
        }

        #endregion

        #region Scheduled Validation

        /// <summary>
        /// Performs scheduled validation automatically.
        /// </summary>
        private async Task PerformScheduledValidationAsync()
        {
            try
            {
                LoggingService.LogInfo("Starting scheduled comprehensive validation", "ComprehensiveMonitoringService");

                var result = await PerformComprehensiveValidationAsync(includePerformanceAnalysis: true);

                // Log results
                if (result.IsValid)
                {
                    LoggingService.LogInfo("Scheduled validation completed successfully - all checks passed", "ComprehensiveMonitoringService");
                }
                else
                {
                    LoggingService.LogWarning($"Scheduled validation found issues: {result.TotalErrors} errors, {result.TotalWarnings} warnings", "ComprehensiveMonitoringService");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Scheduled validation failed: {ex.Message}", "ComprehensiveMonitoringService");
            }
        }

        #endregion

        #region Validation History Management

        /// <summary>
        /// Stores validation result in history with size management.
        /// </summary>
        /// <param name="result">Validation result to store</param>
        private void StoreValidationResult(ComprehensiveValidationResult result)
        {
            lock (_monitoringLock)
            {
                _validationHistory.Add(result);

                // Maintain maximum history count
                if (_validationHistory.Count > MaxValidationHistoryCount)
                {
                    var toRemove = _validationHistory.Count - MaxValidationHistoryCount;
                    _validationHistory.RemoveRange(0, toRemove);
                }
            }

            LoggingService.LogDebug($"Stored validation result in history (total: {_validationHistory.Count})", "ComprehensiveMonitoringService");
        }

        /// <summary>
        /// Gets validation history for the specified time period.
        /// </summary>
        /// <param name="fromTime">Start time (UTC)</param>
        /// <param name="toTime">End time (UTC)</param>
        /// <returns>Validation history within the time period</returns>
        public List<ComprehensiveValidationResult> GetValidationHistory(DateTime fromTime, DateTime toTime)
        {
            lock (_monitoringLock)
            {
                return _validationHistory
                    .Where(r => r.StartTime >= fromTime && r.StartTime <= toTime)
                    .OrderByDescending(r => r.StartTime)
                    .ToList();
            }
        }

        /// <summary>
        /// Gets the most recent validation results.
        /// </summary>
        /// <param name="count">Number of recent results to return</param>
        /// <returns>Most recent validation results</returns>
        public List<ComprehensiveValidationResult> GetRecentValidationResults(int count = 10)
        {
            lock (_monitoringLock)
            {
                return _validationHistory
                    .OrderByDescending(r => r.StartTime)
                    .Take(count)
                    .ToList();
            }
        }

        #endregion

        #region Alert Generation

        /// <summary>
        /// Generates alerts based on validation results if critical issues are found.
        /// </summary>
        /// <param name="result">Validation result to analyze</param>
        private async Task GenerateAlertsIfNeededAsync(ComprehensiveValidationResult result)
        {
            try
            {
                var alerts = new List<string>();

                // Critical schema errors
                if (result.SchemaValidation != null && result.SchemaValidation.Errors.Any())
                {
                    var criticalSchemaErrors = result.SchemaValidation.Errors
                        .Where(e => e.Contains("Missing required table") || e.Contains("Foreign keys are not enabled"))
                        .ToList();

                    if (criticalSchemaErrors.Any())
                    {
                        alerts.Add($"CRITICAL SCHEMA ISSUES: {criticalSchemaErrors.Count} critical schema errors detected");
                    }
                }

                // UID generation failures
                if (result.UIDValidation != null && result.UIDValidation.Errors.Any())
                {
                    var uidErrors = result.UIDValidation.Errors
                        .Where(e => e.Contains("Duplicate") || e.Contains("Invalid"))
                        .ToList();

                    if (uidErrors.Any())
                    {
                        alerts.Add($"UID GENERATION ISSUES: {uidErrors.Count} UID-related errors detected");
                    }
                }

                // Database operation failures
                if (result.DatabaseOperationValidation != null && result.DatabaseOperationValidation.Errors.Any())
                {
                    alerts.Add($"DATABASE OPERATION ISSUES: {result.DatabaseOperationValidation.Errors.Count} database operation errors detected");
                }

                // Business rule violations
                if (result.BusinessRuleValidation != null && result.BusinessRuleValidation.Errors.Any())
                {
                    var businessRuleErrors = result.BusinessRuleValidation.Errors
                        .Where(e => e.Contains("multiple primary phone") || e.Contains("invalid file check"))
                        .ToList();

                    if (businessRuleErrors.Any())
                    {
                        alerts.Add($"BUSINESS RULE VIOLATIONS: {businessRuleErrors.Count} business rule violations detected");
                    }
                }

                // Log alerts
                foreach (var alert in alerts)
                {
                    LoggingService.LogWarning($"ALERT: {alert}", "ComprehensiveMonitoringService");
                }

                // Store alerts for potential notification system integration
                if (alerts.Any())
                {
                    await StoreAlertsAsync(alerts, result.StartTime);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to generate alerts: {ex.Message}", "ComprehensiveMonitoringService");
            }
        }

        /// <summary>
        /// Stores alerts for potential notification system integration.
        /// </summary>
        /// <param name="alerts">List of alert messages</param>
        /// <param name="timestamp">Alert timestamp</param>
        private async Task StoreAlertsAsync(List<string> alerts, DateTime timestamp)
        {
            try
            {
                // This could be extended to store alerts in database or send notifications
                // For now, we'll just log them with high priority
                foreach (var alert in alerts)
                {
                    LoggingService.LogError($"DATABASE MONITORING ALERT [{timestamp:yyyy-MM-dd HH:mm:ss}]: {alert}", "ComprehensiveMonitoringService");
                }

                await Task.CompletedTask; // Placeholder for future alert storage implementation
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to store alerts: {ex.Message}", "ComprehensiveMonitoringService");
            }
        }

        #endregion

        #region Monitoring Reports

        /// <summary>
        /// Generates a comprehensive monitoring report for the specified time period.
        /// </summary>
        /// <param name="fromTime">Start time (UTC)</param>
        /// <param name="toTime">End time (UTC)</param>
        /// <returns>Comprehensive monitoring report</returns>
        public async Task<MonitoringReport> GenerateMonitoringReportAsync(DateTime fromTime, DateTime toTime)
        {
            try
            {
                var report = new MonitoringReport
                {
                    ReportPeriod = $"{fromTime:yyyy-MM-dd HH:mm} - {toTime:yyyy-MM-dd HH:mm}",
                    GeneratedAt = DateTime.UtcNow
                };

                // Get validation history for the period
                var validationHistory = GetValidationHistory(fromTime, toTime);
                report.ValidationCount = validationHistory.Count;

                if (validationHistory.Any())
                {
                    report.SuccessfulValidations = validationHistory.Count(v => v.IsValid);
                    report.FailedValidations = validationHistory.Count(v => !v.IsValid);
                    report.AverageValidationDurationMs = validationHistory.Average(v => v.TotalDurationMs);

                    // Aggregate issues
                    report.TotalErrorsFound = validationHistory.Sum(v => v.TotalErrors);
                    report.TotalWarningsFound = validationHistory.Sum(v => v.TotalWarnings);

                    // Most recent validation
                    report.MostRecentValidation = validationHistory.OrderByDescending(v => v.StartTime).First();
                }

                // Get performance metrics
                var performanceReport = _performanceMonitoringService.GeneratePerformanceReport(fromTime, toTime);
                report.PerformanceMetrics = performanceReport;

                // Get index analysis
                report.IndexAnalysis = await _performanceMonitoringService.AnalyzeIndexEffectivenessAsync();

                // Get UID generation statistics
                report.UIDGenerationStats = await _uidGenerationService.GetUIDGenerationStatsAsync();

                LoggingService.LogInfo($"Generated monitoring report for period {report.ReportPeriod}", "ComprehensiveMonitoringService");
                return report;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في إنشاء تقرير المراقبة", "خطأ في التقرير", LogLevel.Error, "ComprehensiveMonitoringService");
                throw;
            }
        }

        /// <summary>
        /// Gets the current system health status based on recent validations and performance.
        /// </summary>
        /// <returns>System health status</returns>
        public async Task<SystemHealthStatus> GetSystemHealthStatusAsync()
        {
            try
            {
                var healthStatus = new SystemHealthStatus
                {
                    CheckTime = DateTime.UtcNow
                };

                // Check recent validation results (last 24 hours)
                var recentValidations = GetValidationHistory(DateTime.UtcNow.AddHours(-24), DateTime.UtcNow);
                
                if (recentValidations.Any())
                {
                    var latestValidation = recentValidations.OrderByDescending(v => v.StartTime).First();
                    healthStatus.LastValidationTime = latestValidation.StartTime;
                    healthStatus.LastValidationPassed = latestValidation.IsValid;
                    
                    if (!latestValidation.IsValid)
                    {
                        healthStatus.HealthIssues.Add($"Latest validation failed with {latestValidation.TotalErrors} errors");
                    }
                }
                else
                {
                    healthStatus.HealthIssues.Add("No recent validations found");
                }

                // Check performance metrics (last hour)
                var performanceReport = _performanceMonitoringService.GeneratePerformanceReport(
                    DateTime.UtcNow.AddHours(-1), DateTime.UtcNow);

                if (performanceReport.SlowQueryPercentage > 20)
                {
                    healthStatus.HealthIssues.Add($"High percentage of slow queries: {performanceReport.SlowQueryPercentage:F1}%");
                }

                // Check database connectivity
                try
                {
                    using var connection = _databaseService.CreateConnection();
                    await connection.OpenAsync();
                    // Test database connectivity
                    healthStatus.DatabaseConnectivity = true;
                }
                catch
                {
                    healthStatus.DatabaseConnectivity = false;
                    healthStatus.HealthIssues.Add("Database connectivity issues detected");
                }

                // Determine overall health
                healthStatus.OverallHealth = healthStatus.HealthIssues.Any() ? 
                    (healthStatus.HealthIssues.Count > 3 ? HealthLevel.Critical : HealthLevel.Warning) : 
                    HealthLevel.Healthy;

                return healthStatus;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to get system health status: {ex.Message}", "ComprehensiveMonitoringService");
                return new SystemHealthStatus
                {
                    CheckTime = DateTime.UtcNow,
                    OverallHealth = HealthLevel.Critical,
                    HealthIssues = new List<string> { $"Health check failed: {ex.Message}" }
                };
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the ComprehensiveMonitoringService resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _scheduledValidationTimer?.Dispose();
                    LoggingService.LogDebug("ComprehensiveMonitoringService disposed", "ComprehensiveMonitoringService");
                }
                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// Represents a comprehensive monitoring report.
    /// </summary>
    public class MonitoringReport
    {
        /// <summary>
        /// Time period covered by the report.
        /// </summary>
        public string ReportPeriod { get; set; } = string.Empty;

        /// <summary>
        /// Time when the report was generated (UTC).
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// Number of validations performed during the period.
        /// </summary>
        public int ValidationCount { get; set; }

        /// <summary>
        /// Number of successful validations.
        /// </summary>
        public int SuccessfulValidations { get; set; }

        /// <summary>
        /// Number of failed validations.
        /// </summary>
        public int FailedValidations { get; set; }

        /// <summary>
        /// Average validation duration in milliseconds.
        /// </summary>
        public double AverageValidationDurationMs { get; set; }

        /// <summary>
        /// Total errors found across all validations.
        /// </summary>
        public int TotalErrorsFound { get; set; }

        /// <summary>
        /// Total warnings found across all validations.
        /// </summary>
        public int TotalWarningsFound { get; set; }

        /// <summary>
        /// Most recent validation result.
        /// </summary>
        public ComprehensiveValidationResult? MostRecentValidation { get; set; }

        /// <summary>
        /// Performance metrics for the period.
        /// </summary>
        public PerformanceReport? PerformanceMetrics { get; set; }

        /// <summary>
        /// Index effectiveness analysis.
        /// </summary>
        public IndexEffectivenessAnalysis? IndexAnalysis { get; set; }

        /// <summary>
        /// UID generation statistics.
        /// </summary>
        public UIDGenerationStats? UIDGenerationStats { get; set; }

        /// <summary>
        /// Gets a summary of the monitoring report.
        /// </summary>
        /// <returns>Report summary string</returns>
        public string GetSummary()
        {
            var successRate = ValidationCount > 0 ? (SuccessfulValidations * 100.0) / ValidationCount : 0;
            return $"Monitoring Report ({ReportPeriod}): {ValidationCount} validations, " +
                   $"{successRate:F1}% success rate, {TotalErrorsFound} errors, {TotalWarningsFound} warnings. " +
                   $"Performance: {PerformanceMetrics?.TotalQueries ?? 0} queries analyzed.";
        }
    }

    /// <summary>
    /// Represents the current system health status.
    /// </summary>
    public class SystemHealthStatus
    {
        /// <summary>
        /// Time when the health check was performed (UTC).
        /// </summary>
        public DateTime CheckTime { get; set; }

        /// <summary>
        /// Overall health level.
        /// </summary>
        public HealthLevel OverallHealth { get; set; }

        /// <summary>
        /// List of health issues found.
        /// </summary>
        public List<string> HealthIssues { get; set; } = new List<string>();

        /// <summary>
        /// Time of the last validation (UTC).
        /// </summary>
        public DateTime? LastValidationTime { get; set; }

        /// <summary>
        /// Whether the last validation passed.
        /// </summary>
        public bool LastValidationPassed { get; set; }

        /// <summary>
        /// Database connectivity status.
        /// </summary>
        public bool DatabaseConnectivity { get; set; }

        /// <summary>
        /// Gets a summary of the system health status.
        /// </summary>
        /// <returns>Health status summary</returns>
        public string GetSummary()
        {
            var status = OverallHealth.ToString().ToUpperInvariant();
            var issueCount = HealthIssues.Count;
            var lastValidation = LastValidationTime?.ToString("yyyy-MM-dd HH:mm") ?? "Never";
            
            return $"System Health: {status} ({issueCount} issues). " +
                   $"Last validation: {lastValidation} ({(LastValidationPassed ? "PASSED" : "FAILED")}). " +
                   $"Database connectivity: {(DatabaseConnectivity ? "OK" : "FAILED")}.";
        }
    }

    /// <summary>
    /// Represents system health levels.
    /// </summary>
    public enum HealthLevel
    {
        /// <summary>
        /// System is healthy with no issues.
        /// </summary>
        Healthy,

        /// <summary>
        /// System has minor issues that should be monitored.
        /// </summary>
        Warning,

        /// <summary>
        /// System has critical issues that require immediate attention.
        /// </summary>
        Critical
    }
}
