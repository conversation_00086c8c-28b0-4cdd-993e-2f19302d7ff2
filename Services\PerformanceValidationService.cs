using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Performance validation service that provides query optimization analysis,
    /// index effectiveness validation, and performance monitoring tools.
    /// Integrates with DatabasePerformanceMonitoringService for comprehensive performance validation.
    /// </summary>
    public class PerformanceValidationService : IDisposable
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;
        private readonly DatabasePerformanceMonitoringService _performanceMonitoringService;
        private bool _disposed = false;

        // Performance validation thresholds
        private const int SlowQueryThresholdMs = 100;
        private const double MaxSlowQueryPercentage = 15.0;
        private const int MaxUnusedIndexes = 10;
        private const double MinIndexUsagePercentage = 70.0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the PerformanceValidationService.
        /// </summary>
        /// <param name="databaseService">The database service instance</param>
        /// <param name="performanceMonitoringService">The performance monitoring service instance</param>
        public PerformanceValidationService(
            DatabaseService databaseService,
            DatabasePerformanceMonitoringService performanceMonitoringService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _performanceMonitoringService = performanceMonitoringService ?? throw new ArgumentNullException(nameof(performanceMonitoringService));

            LoggingService.LogInfo("PerformanceValidationService initialized", "PerformanceValidationService");
        }

        #endregion

        #region Performance Monitoring Validation

        /// <summary>
        /// Validates performance monitoring tools and provides optimization recommendations.
        /// </summary>
        /// <returns>Performance monitoring validation result</returns>
        public async Task<PerformanceMonitoringValidationResult> ValidatePerformanceMonitoringAsync()
        {
            var result = new PerformanceMonitoringValidationResult();

            try
            {
                LoggingService.LogInfo("Starting performance monitoring validation", "PerformanceValidationService");

                // Validate query performance
                await ValidateQueryPerformanceAsync(result);

                // Validate index effectiveness
                await ValidateIndexEffectivenessAsync(result);

                // Generate optimization recommendations
                await GenerateOptimizationRecommendationsAsync(result);

                // Validate database maintenance
                await ValidateDatabaseMaintenanceAsync(result);

                result.IsValid = !result.Errors.Any();
                result.ValidationTime = DateTime.UtcNow;

                var status = result.IsValid ? "PASSED" : "FAILED";
                LoggingService.LogInfo($"Performance monitoring validation {status}: {result.Errors.Count} errors, {result.Warnings.Count} warnings", "PerformanceValidationService");

                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Performance monitoring validation failed with exception: {ex.Message}");
                result.IsValid = false;
                result.ValidationTime = DateTime.UtcNow;

                ErrorManager.HandleErrorToast(ex, "فشل في التحقق من مراقبة الأداء", "خطأ في التحقق", LogLevel.Error, "PerformanceValidationService");
                return result;
            }
        }

        /// <summary>
        /// Validates query performance and identifies optimization opportunities.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateQueryPerformanceAsync(PerformanceMonitoringValidationResult result)
        {
            try
            {
                // Generate performance report for the last 24 hours
                var endTime = DateTime.UtcNow;
                var startTime = endTime.AddHours(-24);
                var performanceReport = _performanceMonitoringService.GeneratePerformanceReport(startTime, endTime);

                result.QueriesAnalyzed = performanceReport.TotalQueries;
                result.SlowQueriesDetected = performanceReport.SlowQueryCount;

                // Validate slow query percentage
                if (performanceReport.SlowQueryPercentage > MaxSlowQueryPercentage)
                {
                    result.Errors.Add($"High percentage of slow queries: {performanceReport.SlowQueryPercentage:F1}% (threshold: {MaxSlowQueryPercentage}%)");
                }
                else if (performanceReport.SlowQueryPercentage > MaxSlowQueryPercentage / 2)
                {
                    result.Warnings.Add($"Moderate percentage of slow queries: {performanceReport.SlowQueryPercentage:F1}%");
                }

                // Analyze slow queries for patterns
                var slowQueries = _performanceMonitoringService.GetSlowQueries(SlowQueryThresholdMs);
                if (slowQueries.Any())
                {
                    var queryPatterns = AnalyzeSlowQueryPatterns(slowQueries);
                    result.QueryOptimizationSuggestions.AddRange(queryPatterns);
                }

                // Validate index usage percentage
                if (performanceReport.IndexUsagePercentage < MinIndexUsagePercentage)
                {
                    result.Warnings.Add($"Low index usage percentage: {performanceReport.IndexUsagePercentage:F1}% (target: {MinIndexUsagePercentage}%)");
                }

                result.ValidationDetails.Add($"Query performance analysis: {result.QueriesAnalyzed} queries analyzed, {result.SlowQueriesDetected} slow queries found");
                LoggingService.LogDebug($"Query performance validation completed: {result.QueriesAnalyzed} queries analyzed", "PerformanceValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Query performance validation failed: {ex.Message}");
                LoggingService.LogError($"Query performance validation error: {ex.Message}", "PerformanceValidationService");
            }
        }

        /// <summary>
        /// Validates index effectiveness and identifies unused or ineffective indexes.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateIndexEffectivenessAsync(PerformanceMonitoringValidationResult result)
        {
            try
            {
                var indexAnalysis = await _performanceMonitoringService.AnalyzeIndexEffectivenessAsync();

                result.IndexesAnalyzed = indexAnalysis.TotalIndexes;
                result.UnusedIndexesFound = indexAnalysis.UnusedIndexes.Count;

                // Validate number of unused indexes
                if (indexAnalysis.UnusedIndexes.Count > MaxUnusedIndexes)
                {
                    result.Warnings.Add($"Many unused indexes detected: {indexAnalysis.UnusedIndexes.Count} (threshold: {MaxUnusedIndexes})");
                    result.IndexOptimizationSuggestions.Add($"Consider dropping unused indexes: {string.Join(", ", indexAnalysis.UnusedIndexes.Take(5))}");
                }

                // Analyze index usage patterns
                var lowUsageIndexes = indexAnalysis.IndexStatistics
                    .Where(idx => idx.UsageCount > 0 && idx.UsageCount < 10)
                    .ToList();

                if (lowUsageIndexes.Any())
                {
                    result.Warnings.Add($"Found {lowUsageIndexes.Count} indexes with low usage (< 10 uses)");
                    result.IndexOptimizationSuggestions.Add($"Review low-usage indexes: {string.Join(", ", lowUsageIndexes.Take(3).Select(idx => idx.IndexName))}");
                }

                // Check for missing recommended indexes
                await ValidateRecommendedIndexesAsync(result);

                result.ValidationDetails.Add($"Index effectiveness analysis: {result.IndexesAnalyzed} indexes analyzed, {result.UnusedIndexesFound} unused");
                LoggingService.LogDebug($"Index effectiveness validation completed: {result.IndexesAnalyzed} indexes analyzed", "PerformanceValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Index effectiveness validation failed: {ex.Message}");
                LoggingService.LogError($"Index effectiveness validation error: {ex.Message}", "PerformanceValidationService");
            }
        }

        /// <summary>
        /// Validates that recommended indexes exist for optimal performance.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateRecommendedIndexesAsync(PerformanceMonitoringValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Get existing indexes
                var existingIndexes = await connection.QueryAsync<string>(@"
                    SELECT name FROM sqlite_master 
                    WHERE type = 'index' 
                    AND name NOT LIKE 'sqlite_%'");

                var existingIndexList = existingIndexes.ToList();

                // Define critical indexes that should exist
                var criticalIndexes = new Dictionary<string, string>
                {
                    ["idx_clients_name_fr"] = "Client name search performance",
                    ["idx_clients_name_ar"] = "Arabic client name search performance",
                    ["idx_clients_national_id"] = "National ID lookup performance",
                    ["idx_phone_numbers_client_uid"] = "Phone number lookup by client",
                    ["idx_activities_client_uid"] = "Activity lookup by client",
                    ["idx_file_check_states_activity_uid"] = "File check state lookup by activity"
                };

                // Check for missing critical indexes
                foreach (var criticalIndex in criticalIndexes)
                {
                    if (!existingIndexList.Contains(criticalIndex.Key))
                    {
                        result.Warnings.Add($"Missing recommended index: {criticalIndex.Key} ({criticalIndex.Value})");
                        result.IndexOptimizationSuggestions.Add($"Create index {criticalIndex.Key} for {criticalIndex.Value}");
                    }
                }

                LoggingService.LogDebug($"Recommended indexes validation completed: {criticalIndexes.Count} critical indexes checked", "PerformanceValidationService");
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"Failed to validate recommended indexes: {ex.Message}");
                LoggingService.LogWarning($"Recommended indexes validation error: {ex.Message}", "PerformanceValidationService");
            }
        }

        /// <summary>
        /// Generates comprehensive optimization recommendations based on analysis.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task GenerateOptimizationRecommendationsAsync(PerformanceMonitoringValidationResult result)
        {
            try
            {
                // Analyze table scan frequency
                var tableScanRecommendations = await AnalyzeTableScansAsync();
                result.PerformanceRecommendations.AddRange(tableScanRecommendations);

                // Analyze query complexity
                var complexityRecommendations = await AnalyzeQueryComplexityAsync();
                result.PerformanceRecommendations.AddRange(complexityRecommendations);

                // Database configuration recommendations
                var configRecommendations = await AnalyzeDatabaseConfigurationAsync();
                result.PerformanceRecommendations.AddRange(configRecommendations);

                // Maintenance recommendations
                var maintenanceRecommendations = await AnalyzeMaintenanceNeedsAsync();
                result.PerformanceRecommendations.AddRange(maintenanceRecommendations);

                LoggingService.LogDebug($"Generated {result.PerformanceRecommendations.Count} optimization recommendations", "PerformanceValidationService");
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"Failed to generate optimization recommendations: {ex.Message}");
                LoggingService.LogWarning($"Optimization recommendations generation error: {ex.Message}", "PerformanceValidationService");
            }
        }

        /// <summary>
        /// Validates database maintenance operations and schedules.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateDatabaseMaintenanceAsync(PerformanceMonitoringValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Check database size and fragmentation
                var pageCount = await connection.ExecuteScalarAsync<long>("PRAGMA page_count");
                var freelistCount = await connection.ExecuteScalarAsync<long>("PRAGMA freelist_count");
                var fragmentationPercentage = pageCount > 0 ? (freelistCount * 100.0) / pageCount : 0;

                if (fragmentationPercentage > 10)
                {
                    result.Warnings.Add($"Database fragmentation detected: {fragmentationPercentage:F1}% free pages");
                    result.PerformanceRecommendations.Add("Consider running VACUUM to reduce database fragmentation");
                }

                // Check statistics freshness
                var statsInfo = await connection.QueryAsync<dynamic>("PRAGMA stats");
                if (!statsInfo.Any())
                {
                    result.Warnings.Add("Database statistics appear to be missing or outdated");
                    result.PerformanceRecommendations.Add("Run ANALYZE to update database statistics for better query planning");
                }

                // Check WAL mode
                var journalMode = await connection.ExecuteScalarAsync<string>("PRAGMA journal_mode");
                if (!journalMode.Equals("wal", StringComparison.OrdinalIgnoreCase))
                {
                    result.Warnings.Add($"Database not using WAL mode (current: {journalMode})");
                    result.PerformanceRecommendations.Add("Enable WAL mode for better concurrency and performance");
                }

                result.ValidationDetails.Add($"Database maintenance validation: {fragmentationPercentage:F1}% fragmentation, journal mode: {journalMode}");
                LoggingService.LogDebug("Database maintenance validation completed", "PerformanceValidationService");
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"Database maintenance validation failed: {ex.Message}");
                LoggingService.LogWarning($"Database maintenance validation error: {ex.Message}", "PerformanceValidationService");
            }
        }

        #endregion

        #region Analysis Methods

        /// <summary>
        /// Analyzes slow query patterns to identify optimization opportunities.
        /// </summary>
        /// <param name="slowQueries">List of slow queries</param>
        /// <returns>List of optimization suggestions</returns>
        private List<string> AnalyzeSlowQueryPatterns(List<QueryPerformanceMetric> slowQueries)
        {
            var suggestions = new List<string>();

            try
            {
                // Group queries by operation type
                var selectQueries = slowQueries.Where(q => q.Query.TrimStart().StartsWith("SELECT", StringComparison.OrdinalIgnoreCase)).ToList();
                var insertQueries = slowQueries.Where(q => q.Query.TrimStart().StartsWith("INSERT", StringComparison.OrdinalIgnoreCase)).ToList();
                var updateQueries = slowQueries.Where(q => q.Query.TrimStart().StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase)).ToList();

                if (selectQueries.Count > slowQueries.Count * 0.7)
                {
                    suggestions.Add("Most slow queries are SELECT operations - consider adding indexes for frequently queried columns");
                }

                if (insertQueries.Count > slowQueries.Count * 0.3)
                {
                    suggestions.Add("High number of slow INSERT operations - consider reviewing index overhead or batch operations");
                }

                // Analyze table scan patterns
                var tableScanQueries = slowQueries.Where(q => q.QueryPlan?.HasTableScan == true).ToList();
                if (tableScanQueries.Count > slowQueries.Count * 0.5)
                {
                    suggestions.Add("Many slow queries use table scans - review indexing strategy for frequently accessed tables");
                }

                // Analyze query complexity
                var complexQueries = slowQueries.Where(q => q.Query.Split(' ').Length > 20).ToList();
                if (complexQueries.Any())
                {
                    suggestions.Add($"Found {complexQueries.Count} complex queries that may benefit from optimization or breaking into smaller operations");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to analyze slow query patterns: {ex.Message}", "PerformanceValidationService");
            }

            return suggestions;
        }

        /// <summary>
        /// Analyzes table scan frequency and provides recommendations.
        /// </summary>
        /// <returns>List of table scan recommendations</returns>
        private async Task<List<string>> AnalyzeTableScansAsync()
        {
            var recommendations = new List<string>();

            try
            {
                // Get recent performance metrics
                var recentMetrics = _performanceMonitoringService.GetPerformanceMetrics(
                    DateTime.UtcNow.AddHours(-24), DateTime.UtcNow);

                var tableScanMetrics = recentMetrics.Where(m => m.QueryPlan?.HasTableScan == true).ToList();
                
                if (tableScanMetrics.Any())
                {
                    // Group by tables accessed
                    var tableScans = tableScanMetrics
                        .SelectMany(m => m.QueryPlan?.TablesAccessed ?? new List<string>())
                        .GroupBy(table => table)
                        .OrderByDescending(g => g.Count())
                        .Take(5)
                        .ToList();

                    foreach (var tableGroup in tableScans)
                    {
                        recommendations.Add($"Table '{tableGroup.Key}' frequently uses table scans ({tableGroup.Count()} times) - consider adding appropriate indexes");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to analyze table scans: {ex.Message}", "PerformanceValidationService");
            }

            return recommendations;
        }

        /// <summary>
        /// Analyzes query complexity and provides optimization suggestions.
        /// </summary>
        /// <returns>List of query complexity recommendations</returns>
        private async Task<List<string>> AnalyzeQueryComplexityAsync()
        {
            var recommendations = new List<string>();

            try
            {
                var recentMetrics = _performanceMonitoringService.GetPerformanceMetrics(
                    DateTime.UtcNow.AddHours(-24), DateTime.UtcNow);

                // Analyze JOIN patterns
                var joinQueries = recentMetrics.Where(m => m.Query.Contains("JOIN", StringComparison.OrdinalIgnoreCase)).ToList();
                var slowJoinQueries = joinQueries.Where(m => m.ExecutionTimeMs > SlowQueryThresholdMs).ToList();

                if (slowJoinQueries.Count > joinQueries.Count * 0.3)
                {
                    recommendations.Add("High percentage of slow JOIN queries - review foreign key indexes and JOIN conditions");
                }

                // Analyze subquery usage
                var subqueryQueries = recentMetrics.Where(m => 
                    m.Query.Contains("(SELECT", StringComparison.OrdinalIgnoreCase) ||
                    m.Query.Contains("EXISTS", StringComparison.OrdinalIgnoreCase)).ToList();

                if (subqueryQueries.Any())
                {
                    var slowSubqueries = subqueryQueries.Where(m => m.ExecutionTimeMs > SlowQueryThresholdMs).ToList();
                    if (slowSubqueries.Count > subqueryQueries.Count * 0.4)
                    {
                        recommendations.Add("Consider optimizing subqueries or converting to JOINs where appropriate");
                    }
                }

                await Task.CompletedTask; // Placeholder for async operations
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to analyze query complexity: {ex.Message}", "PerformanceValidationService");
            }

            return recommendations;
        }

        /// <summary>
        /// Analyzes database configuration for performance optimization.
        /// </summary>
        /// <returns>List of configuration recommendations</returns>
        private async Task<List<string>> AnalyzeDatabaseConfigurationAsync()
        {
            var recommendations = new List<string>();

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Check cache size
                var cacheSize = await connection.ExecuteScalarAsync<long>("PRAGMA cache_size");
                if (Math.Abs(cacheSize) < 10000) // Negative values indicate pages, positive indicate KB
                {
                    recommendations.Add($"Consider increasing cache size (current: {cacheSize}) for better performance");
                }

                // Check synchronous mode
                var synchronous = await connection.ExecuteScalarAsync<string>("PRAGMA synchronous");
                if (synchronous.Equals("FULL", StringComparison.OrdinalIgnoreCase))
                {
                    recommendations.Add("Consider using NORMAL synchronous mode instead of FULL for better performance");
                }

                // Check temp store
                var tempStore = await connection.ExecuteScalarAsync<string>("PRAGMA temp_store");
                if (!tempStore.Equals("MEMORY", StringComparison.OrdinalIgnoreCase))
                {
                    recommendations.Add("Consider setting temp_store to MEMORY for better temporary table performance");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to analyze database configuration: {ex.Message}", "PerformanceValidationService");
            }

            return recommendations;
        }

        /// <summary>
        /// Analyzes maintenance needs and provides recommendations.
        /// </summary>
        /// <returns>List of maintenance recommendations</returns>
        private async Task<List<string>> AnalyzeMaintenanceNeedsAsync()
        {
            var recommendations = new List<string>();

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Check database size growth
                var pageCount = await connection.ExecuteScalarAsync<long>("PRAGMA page_count");
                var pageSize = await connection.ExecuteScalarAsync<long>("PRAGMA page_size");
                var databaseSizeMB = (pageCount * pageSize) / (1024.0 * 1024.0);

                if (databaseSizeMB > 100)
                {
                    recommendations.Add($"Large database size ({databaseSizeMB:F1}MB) - consider regular VACUUM operations");
                }

                // Check auto-vacuum setting
                var autoVacuum = await connection.ExecuteScalarAsync<string>("PRAGMA auto_vacuum");
                if (autoVacuum.Equals("NONE", StringComparison.OrdinalIgnoreCase))
                {
                    recommendations.Add("Consider enabling incremental auto-vacuum for automatic space reclamation");
                }

                // Recommend regular ANALYZE
                recommendations.Add("Schedule regular ANALYZE operations to keep query planner statistics up to date");
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to analyze maintenance needs: {ex.Message}", "PerformanceValidationService");
            }

            return recommendations;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the PerformanceValidationService resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources if any
                    LoggingService.LogDebug("PerformanceValidationService disposed", "PerformanceValidationService");
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
