using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using UFU2.Common;

namespace UFU2.Models
{
    /// <summary>
    /// Model class for managing a collection of notes for client registration.
    /// Implements INotifyPropertyChanged for proper WPF data binding support.
    /// Provides methods for adding, removing, and managing multiple notes.
    /// 
    /// This model follows UFU2 architectural patterns with:
    /// - Property change notification for real-time UI updates
    /// - ObservableCollection for automatic UI synchronization
    /// - Memory-only persistence with tab independence
    /// - Arabic RTL layout compatibility
    /// - MaterialDesign ListView integration
    /// </summary>
    public class NotesCollectionModel : INotifyPropertyChanged
    {
        #region Private Fields

        private UFU2BulkObservableCollection<NoteModel> _notes;
        private NoteModel? _selectedNote;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the collection of notes.
        /// This collection is bound to the ListView in the dialog.
        /// </summary>
        public UFU2BulkObservableCollection<NoteModel> Notes
        {
            get => _notes;
            private set => SetProperty(ref _notes, value);
        }

        /// <summary>
        /// Gets or sets the currently selected note.
        /// </summary>
        public NoteModel? SelectedNote
        {
            get => _selectedNote;
            set => SetProperty(ref _selectedNote, value);
        }

        /// <summary>
        /// Gets the total number of notes in the collection.
        /// </summary>
        public int Count => Notes.Count;

        /// <summary>
        /// Gets whether the collection has any notes.
        /// </summary>
        public bool HasNotes => Notes.Count > 0;

        /// <summary>
        /// Gets whether the collection is empty.
        /// </summary>
        public bool IsEmpty => Notes.Count == 0;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the NotesCollectionModel class.
        /// </summary>
        public NotesCollectionModel()
        {
            Notes = new UFU2BulkObservableCollection<NoteModel>();
            Notes.CollectionChanged += Notes_CollectionChanged;
        }

        /// <summary>
        /// Initializes a new instance of the NotesCollectionModel class with existing notes.
        /// </summary>
        /// <param name="existingNotes">Existing notes to initialize the collection with</param>
        public NotesCollectionModel(IEnumerable<NoteModel> existingNotes) : this()
        {
            if (existingNotes != null)
            {
                foreach (var note in existingNotes)
                {
                    Notes.Add(note);
                }
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Adds a new note to the collection.
        /// </summary>
        /// <param name="content">The note content</param>
        /// <param name="priority">The priority level (optional, defaults to 0=Normal/Green)</param>
        /// <returns>The created note</returns>
        public NoteModel AddNote(string content, int priority = 0)
        {
            if (string.IsNullOrWhiteSpace(content))
                throw new ArgumentException("Note content cannot be empty", nameof(content));

            var note = new NoteModel(content.Trim(), priority);
            Notes.Add(note);
            return note;
        }

        /// <summary>
        /// Adds an existing note to the collection.
        /// </summary>
        /// <param name="note">The note to add</param>
        public void AddNote(NoteModel note)
        {
            if (note == null)
                throw new ArgumentNullException(nameof(note));

            if (!note.IsValid())
                throw new ArgumentException("Note content is not valid", nameof(note));

            Notes.Add(note);
        }

        /// <summary>
        /// Removes a note from the collection.
        /// </summary>
        /// <param name="note">The note to remove</param>
        /// <returns>True if the note was removed, false if it wasn't found</returns>
        public bool RemoveNote(NoteModel note)
        {
            if (note == null)
                return false;

            var removed = Notes.Remove(note);
            
            // Clear selection if the removed note was selected
            if (removed && SelectedNote == note)
            {
                SelectedNote = null;
            }

            return removed;
        }

        /// <summary>
        /// Removes a note by its ID.
        /// </summary>
        /// <param name="noteId">The ID of the note to remove</param>
        /// <returns>True if the note was removed, false if it wasn't found</returns>
        public bool RemoveNoteById(string noteId)
        {
            if (string.IsNullOrWhiteSpace(noteId))
                return false;

            var note = Notes.FirstOrDefault(n => n.Id == noteId);
            return note != null && RemoveNote(note);
        }

        /// <summary>
        /// Updates an existing note in the collection.
        /// </summary>
        /// <param name="noteId">The ID of the note to update</param>
        /// <param name="newContent">The new content</param>
        /// <param name="newPriority">The new priority level (optional)</param>
        /// <returns>True if the note was updated, false if it wasn't found</returns>
        public bool UpdateNote(string noteId, string newContent, int? newPriority = null)
        {
            if (string.IsNullOrWhiteSpace(noteId) || string.IsNullOrWhiteSpace(newContent))
                return false;

            var note = Notes.FirstOrDefault(n => n.Id == noteId);
            if (note != null)
            {
                note.Update(newContent.Trim(), newPriority);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Finds a note by its ID.
        /// </summary>
        /// <param name="noteId">The ID of the note to find</param>
        /// <returns>The note if found, null otherwise</returns>
        public NoteModel? FindNoteById(string noteId)
        {
            if (string.IsNullOrWhiteSpace(noteId))
                return null;

            return Notes.FirstOrDefault(n => n.Id == noteId);
        }

        /// <summary>
        /// Gets all notes sorted by creation date (newest first).
        /// </summary>
        /// <returns>List of notes sorted by creation date</returns>
        public List<NoteModel> GetNotesSortedByDate()
        {
            return Notes.OrderByDescending(n => n.CreatedDate).ToList();
        }

        /// <summary>
        /// Gets all notes with a specific priority level.
        /// </summary>
        /// <param name="priority">The priority level to filter by (0=Normal, 1=Medium, 2=High)</param>
        /// <returns>List of notes with the specified priority level</returns>
        public List<NoteModel> GetNotesByPriority(int priority)
        {
            return Notes.Where(n => n.Priority == priority).ToList();
        }

        /// <summary>
        /// Clears all notes from the collection.
        /// </summary>
        public void Clear()
        {
            SelectedNote = null;
            Notes.Clear();
        }

        /// <summary>
        /// Validates all notes in the collection.
        /// </summary>
        /// <returns>True if all notes are valid, false otherwise</returns>
        public bool IsValid()
        {
            return Notes.All(note => note.IsValid());
        }

        /// <summary>
        /// Gets a list of all note contents as strings.
        /// </summary>
        /// <returns>List of note contents</returns>
        public List<string> GetAllNoteContents()
        {
            return Notes.Select(note => note.Content).ToList();
        }

        /// <summary>
        /// Creates a deep copy of the entire collection.
        /// </summary>
        /// <returns>A new NotesCollectionModel with cloned notes</returns>
        public NotesCollectionModel Clone()
        {
            var clonedNotes = Notes.Select(note => note.Clone()).ToList();
            return new NotesCollectionModel(clonedNotes);
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Handles changes to the notes collection.
        /// </summary>
        private void Notes_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // Notify UI of property changes when collection changes
            OnPropertyChanged(nameof(Count));
            OnPropertyChanged(nameof(HasNotes));
            OnPropertyChanged(nameof(IsEmpty));
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Sets the property value and raises PropertyChanged if the value has changed
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">New value to set</param>
        /// <param name="propertyName">Name of the property</param>
        /// <returns>True if the property value was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
