using System;
using System.Collections.Generic;
using System.Linq;
using UFU2.Common;
using UFU2.Common.Utilities;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Advanced Arabic text analyzer providing comprehensive linguistic analysis and pattern recognition.
    /// Focuses on word frequency-based matching and exact/partial matching algorithms for enhanced search accuracy.
    /// Integrates with UFU2's TextNormalizationHelper for consistent Arabic text processing.
    /// </summary>
    public class ArabicTextAnalyzer : IArabicTextAnalyzer
    {
        #region Private Fields

        /// <summary>
        /// Common Arabic stop words that should be filtered in frequency analysis.
        /// </summary>
        private static readonly HashSet<string> ArabicStopWords = new(StringComparer.OrdinalIgnoreCase)
        {
            "في", "من", "إلى", "على", "عن", "مع", "بين", "تحت", "فوق", "أمام", "خلف", "بعد", "قبل",
            "و", "أو", "لكن", "إذا", "عندما", "حيث", "كيف", "ماذا", "متى", "أين", "لماذا",
            "هذا", "هذه", "ذلك", "تلك", "التي", "الذي", "اللذان", "اللتان", "الذين", "اللواتي"
        };

        /// <summary>
        /// Arabic conjunctions for conjunction detection.
        /// </summary>
        private static readonly HashSet<string> ArabicConjunctions = new(StringComparer.OrdinalIgnoreCase)
        {
            "و", "أو", "لكن", "إذا", "عندما", "حيث", "كما", "لأن", "بل", "غير", "سوى"
        };

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ArabicTextAnalyzer class.
        /// </summary>
        public ArabicTextAnalyzer()
        {
            LoggingService.LogInfo("ArabicTextAnalyzer initialized with word frequency-based analysis", "ArabicTextAnalyzer");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Analyzes Arabic text and provides comprehensive linguistic information.
        /// </summary>
        /// <param name="text">Text to analyze</param>
        /// <returns>Detailed analysis of the Arabic text</returns>
        public ArabicTextAnalysis AnalyzeText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return new ArabicTextAnalysis();
            }

            try
            {
                // Normalize the text
                string normalizedText = TextNormalizationHelper.NormalizeForSearch(text);
                
                // Extract words
                var words = ExtractWords(text);
                
                // Generate search variations
                var searchVariations = GenerateSearchVariations(text);
                
                // Calculate word frequency
                var wordFrequency = CalculateWordFrequency(words);
                
                // Determine complexity
                var complexity = DetermineTextComplexity(words);
                
                // Check for conjunctions and Arabic text
                bool containsConjunctions = words.Any(w => w.IsConjunction);
                bool containsArabicText = words.Any(w => w.IsArabicWord);

                return new ArabicTextAnalysis
                {
                    NormalizedText = normalizedText,
                    SearchVariations = searchVariations,
                    Words = words,
                    Complexity = complexity,
                    ContainsConjunctions = containsConjunctions,
                    ContainsArabicText = containsArabicText,
                    WordFrequency = wordFrequency
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error analyzing Arabic text '{text}': {ex.Message}", "ArabicTextAnalyzer");
                return new ArabicTextAnalysis
                {
                    NormalizedText = text?.Trim() ?? string.Empty
                };
            }
        }

        /// <summary>
        /// Generates search variations for a given search term to improve matching.
        /// </summary>
        /// <param name="searchTerm">Original search term</param>
        /// <returns>Array of search variations</returns>
        public string[] GenerateSearchVariations(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return Array.Empty<string>();
            }

            try
            {
                var variations = new List<string> { searchTerm.Trim() };

                // Add normalized version
                string normalized = TextNormalizationHelper.NormalizeForSearch(searchTerm);
                if (!variations.Contains(normalized, StringComparer.OrdinalIgnoreCase))
                {
                    variations.Add(normalized);
                }

                // Add conjunction variations
                var conjunctionVariations = TextNormalizationHelper.GenerateConjunctionVariations(searchTerm);
                foreach (var variation in conjunctionVariations)
                {
                    if (!variations.Contains(variation, StringComparer.OrdinalIgnoreCase))
                    {
                        variations.Add(variation);
                    }
                }

                // Add individual words for multi-word searches
                var words = TextNormalizationHelper.ExtractArabicWords(searchTerm, includeConjunctions: false);
                foreach (var word in words)
                {
                    if (word.Length >= 2 && !variations.Contains(word, StringComparer.OrdinalIgnoreCase))
                    {
                        variations.Add(word);
                    }
                }

                return variations.ToArray();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating search variations for '{searchTerm}': {ex.Message}", "ArabicTextAnalyzer");
                return new[] { searchTerm };
            }
        }

        /// <summary>
        /// Calculates exact prefix matching similarity between search term and target text.
        /// Prioritizes exact prefix matches over any other type of matching.
        /// </summary>
        /// <param name="searchTerm">Search term to match as prefix</param>
        /// <param name="targetText">Target text to check for prefix match</param>
        /// <returns>Similarity score between 0.0 and 1.0</returns>
        public double CalculateExactPrefixSimilarity(string searchTerm, string targetText)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || string.IsNullOrWhiteSpace(targetText))
            {
                return 0.0;
            }

            try
            {
                // Normalize both texts for consistent comparison
                string normalizedSearchTerm = TextNormalizationHelper.NormalizeForSearch(searchTerm).Trim();
                string normalizedTargetText = TextNormalizationHelper.NormalizeForSearch(targetText).Trim();

                if (string.IsNullOrWhiteSpace(normalizedSearchTerm) || string.IsNullOrWhiteSpace(normalizedTargetText))
                {
                    return 0.0;
                }

                // Check for exact prefix match
                if (normalizedTargetText.StartsWith(normalizedSearchTerm, StringComparison.OrdinalIgnoreCase))
                {
                    // Calculate similarity based on how much of the target text is covered by the search term
                    double coverageRatio = (double)normalizedSearchTerm.Length / normalizedTargetText.Length;

                    // Perfect match gets score of 1.0, partial prefix matches get proportional scores
                    if (string.Equals(normalizedSearchTerm, normalizedTargetText, StringComparison.OrdinalIgnoreCase))
                    {
                        return 1.0; // Exact match
                    }
                    else
                    {
                        // Prefix match: score based on coverage ratio with minimum of 0.7 for any valid prefix
                        return Math.Max(0.7, 0.7 + (coverageRatio * 0.3));
                    }
                }

                // No prefix match found
                return 0.0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating exact prefix similarity: {ex.Message}", "ArabicTextAnalyzer");
                return 0.0;
            }
        }

        /// <summary>
        /// Extracts and analyzes individual words from Arabic text.
        /// </summary>
        /// <param name="text">Text to extract words from</param>
        /// <returns>Array of word information</returns>
        public ArabicWordInfo[] ExtractWords(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return Array.Empty<ArabicWordInfo>();
            }

            try
            {
                var words = new List<ArabicWordInfo>();
                string normalizedText = TextNormalizationHelper.NormalizeForSearch(text);
                string[] wordArray = normalizedText.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

                int position = 0;
                foreach (var word in wordArray)
                {
                    if (string.IsNullOrWhiteSpace(word))
                        continue;

                    var wordInfo = new ArabicWordInfo
                    {
                        OriginalWord = word,
                        NormalizedWord = TextNormalizationHelper.NormalizeArabicCharacters(word),
                        Position = position,
                        Length = word.Length,
                        IsConjunction = ArabicConjunctions.Contains(word),
                        IsStopWord = ArabicStopWords.Contains(word),
                        IsArabicWord = TextNormalizationHelper.ContainsArabicCharacters(word)
                    };

                    words.Add(wordInfo);
                    position += word.Length + 1; // +1 for space
                }

                return words.ToArray();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error extracting words from text '{text}': {ex.Message}", "ArabicTextAnalyzer");
                return Array.Empty<ArabicWordInfo>();
            }
        }

        /// <summary>
        /// Calculates relevance score based on exact prefix matching.
        /// Prioritizes results that start with the exact search term sequence.
        /// </summary>
        /// <param name="searchTerms">Array of search terms (typically contains the original search term)</param>
        /// <param name="targetText">Text to score against search terms</param>
        /// <returns>Relevance score between 0.0 and 1.0</returns>
        public double CalculateRelevanceScore(string[] searchTerms, string targetText)
        {
            if (searchTerms == null || !searchTerms.Any() || string.IsNullOrWhiteSpace(targetText))
            {
                return 0.0;
            }

            try
            {
                // Normalize target text
                string normalizedTargetText = TextNormalizationHelper.NormalizeForSearch(targetText).Trim();

                if (string.IsNullOrWhiteSpace(normalizedTargetText))
                {
                    return 0.0;
                }

                // Find the longest search term (usually the original search term)
                string primarySearchTerm = searchTerms
                    .Where(term => !string.IsNullOrWhiteSpace(term))
                    .OrderByDescending(term => term.Length)
                    .FirstOrDefault();

                if (string.IsNullOrWhiteSpace(primarySearchTerm))
                {
                    return 0.0;
                }

                // Use exact prefix similarity as the primary scoring method
                double prefixSimilarity = CalculateExactPrefixSimilarity(primarySearchTerm, targetText);

                if (prefixSimilarity > 0.0)
                {
                    return prefixSimilarity;
                }

                // If no prefix match, check if any search term variations match as prefix
                foreach (var searchTerm in searchTerms)
                {
                    if (string.IsNullOrWhiteSpace(searchTerm))
                        continue;

                    double variationSimilarity = CalculateExactPrefixSimilarity(searchTerm, targetText);
                    if (variationSimilarity > 0.0)
                    {
                        // Slightly reduce score for variation matches
                        return variationSimilarity * 0.9;
                    }
                }

                // No prefix matches found
                return 0.0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating relevance score: {ex.Message}", "ArabicTextAnalyzer");
                return 0.0;
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Extracts meaningful words from text, filtering out stop words and conjunctions.
        /// </summary>
        /// <param name="text">Text to extract words from</param>
        /// <returns>Array of meaningful words</returns>
        private string[] ExtractMeaningfulWords(string text)
        {
            var words = ExtractWords(text);
            return words
                .Where(w => !w.IsStopWord && !w.IsConjunction && w.Length >= 2)
                .Select(w => w.NormalizedWord)
                .ToArray();
        }

        /// <summary>
        /// Calculates word frequency from an array of word information.
        /// </summary>
        /// <param name="words">Array of word information</param>
        /// <returns>Dictionary mapping words to their frequencies</returns>
        private Dictionary<string, int> CalculateWordFrequency(ArabicWordInfo[] words)
        {
            var frequency = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);

            foreach (var word in words)
            {
                if (word.IsStopWord || word.IsConjunction || word.Length < 2)
                    continue;

                string normalizedWord = word.NormalizedWord;
                if (frequency.ContainsKey(normalizedWord))
                {
                    frequency[normalizedWord]++;
                }
                else
                {
                    frequency[normalizedWord] = 1;
                }
            }

            return frequency;
        }

        /// <summary>
        /// Calculates word frequency from an array of strings.
        /// </summary>
        /// <param name="words">Array of words</param>
        /// <returns>Dictionary mapping words to their frequencies</returns>
        private Dictionary<string, int> CalculateWordFrequency(string[] words)
        {
            var frequency = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);

            foreach (var word in words)
            {
                if (string.IsNullOrWhiteSpace(word) || word.Length < 2)
                    continue;

                if (frequency.ContainsKey(word))
                {
                    frequency[word]++;
                }
                else
                {
                    frequency[word] = 1;
                }
            }

            return frequency;
        }

        /// <summary>
        /// Determines the complexity level of the text based on word analysis.
        /// </summary>
        /// <param name="words">Array of word information</param>
        /// <returns>Text complexity level</returns>
        private TextComplexity DetermineTextComplexity(ArabicWordInfo[] words)
        {
            if (!words.Any())
                return TextComplexity.Simple;

            int totalWords = words.Length;
            int arabicWords = words.Count(w => w.IsArabicWord);
            int longWords = words.Count(w => w.Length > 6);
            int conjunctions = words.Count(w => w.IsConjunction);

            // Calculate complexity factors
            double arabicRatio = (double)arabicWords / totalWords;
            double longWordRatio = (double)longWords / totalWords;
            double conjunctionRatio = (double)conjunctions / totalWords;

            // Determine complexity based on ratios
            if (arabicRatio > 0.8 && (longWordRatio > 0.3 || conjunctionRatio > 0.2))
                return TextComplexity.Complex;

            if (arabicRatio > 0.5 && (longWordRatio > 0.2 || conjunctionRatio > 0.1))
                return TextComplexity.Moderate;

            return TextComplexity.Simple;
        }

        #endregion
    }
}
