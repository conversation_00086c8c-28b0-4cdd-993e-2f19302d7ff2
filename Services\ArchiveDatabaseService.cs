using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;
using System.Text.Json;

namespace UFU2.Services
{
    /// <summary>
    /// Archive database service for UFU2 audit trail and change tracking system.
    /// Provides specialized audit logging methods for different types of data changes.
    /// Follows UFU2 architectural patterns with comprehensive error handling and logging.
    /// </summary>
    public class ArchiveDatabaseService : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private bool _disposed = false;

        /// <summary>
        /// Initializes a new instance of the ArchiveDatabaseService class.
        /// </summary>
        /// <param name="databaseService">Archive database service instance</param>
        public ArchiveDatabaseService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            
            if (_databaseService.DatabaseType != DatabaseType.ArchiveData)
            {
                throw new ArgumentException("DatabaseService must be configured for ArchiveData type", nameof(databaseService));
            }

            LoggingService.LogDebug("ArchiveDatabaseService initialized", "ArchiveDatabaseService");
        }

        #region Data Addition Logging

        /// <summary>
        /// Logs the addition of new data to an existing entity.
        /// </summary>
        /// <param name="entityType">Type of entity (e.g., 'Client', 'Activity', 'PhoneNumber')</param>
        /// <param name="entityId">UID of the entity receiving new data</param>
        /// <param name="dataName">Name of the added field/data</param>
        /// <param name="addedValue">The new value that was added</param>
        public async Task LogDataAdditionAsync(string entityType, string entityId, string dataName, object addedValue)
        {
            if (string.IsNullOrWhiteSpace(entityType))
                throw new ArgumentException("Entity type cannot be null or empty", nameof(entityType));
            if (string.IsNullOrWhiteSpace(entityId))
                throw new ArgumentException("Entity ID cannot be null or empty", nameof(entityId));
            if (string.IsNullOrWhiteSpace(dataName))
                throw new ArgumentException("Data name cannot be null or empty", nameof(dataName));
            if (addedValue == null)
                throw new ArgumentNullException(nameof(addedValue));

            try
            {
                string serializedValue = SerializeValue(addedValue);

                const string sql = @"
                    INSERT INTO AddedEntities (EntityType, EntityId, DataName, AddedValue)
                    VALUES (@EntityType, @EntityId, @DataName, @AddedValue)";

                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                await connection.ExecuteAsync(sql, new
                {
                    EntityType = entityType,
                    EntityId = entityId,
                    DataName = dataName,
                    AddedValue = serializedValue
                });

                LoggingService.LogDebug($"Logged data addition: {entityType}[{entityId}].{dataName} = {serializedValue}", "ArchiveDatabaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to log data addition for {entityType}[{entityId}].{dataName}: {ex.Message}", "ArchiveDatabaseService");
                // Don't throw - audit logging failures should not block client operations
            }
        }

        #endregion

        #region Data Update Logging

        /// <summary>
        /// Logs the modification of existing data with old and new values.
        /// </summary>
        /// <param name="entityType">Type of entity (e.g., 'Client', 'Activity', 'PhoneNumber')</param>
        /// <param name="entityId">UID of the modified entity</param>
        /// <param name="dataName">Name of the modified field/data</param>
        /// <param name="oldValue">Previous value before change</param>
        /// <param name="newValue">New value after change</param>
        public async Task LogDataUpdateAsync(string entityType, string entityId, string dataName, object oldValue, object newValue)
        {
            if (string.IsNullOrWhiteSpace(entityType))
                throw new ArgumentException("Entity type cannot be null or empty", nameof(entityType));
            if (string.IsNullOrWhiteSpace(entityId))
                throw new ArgumentException("Entity ID cannot be null or empty", nameof(entityId));
            if (string.IsNullOrWhiteSpace(dataName))
                throw new ArgumentException("Data name cannot be null or empty", nameof(dataName));
            if (oldValue == null)
                throw new ArgumentNullException(nameof(oldValue));
            if (newValue == null)
                throw new ArgumentNullException(nameof(newValue));

            try
            {
                string serializedOldValue = SerializeValue(oldValue);
                string serializedNewValue = SerializeValue(newValue);

                const string sql = @"
                    INSERT INTO UpdatedEntities (EntityType, EntityId, DataName, OldValue, NewValue)
                    VALUES (@EntityType, @EntityId, @DataName, @OldValue, @NewValue)";

                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                await connection.ExecuteAsync(sql, new
                {
                    EntityType = entityType,
                    EntityId = entityId,
                    DataName = dataName,
                    OldValue = serializedOldValue,
                    NewValue = serializedNewValue
                });

                LoggingService.LogDebug($"Logged data update: {entityType}[{entityId}].{dataName} changed from {serializedOldValue} to {serializedNewValue}", "ArchiveDatabaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to log data update for {entityType}[{entityId}].{dataName}: {ex.Message}", "ArchiveDatabaseService");
                // Don't throw - audit logging failures should not block client operations
            }
        }

        #endregion

        #region Data Deletion Logging

        /// <summary>
        /// Logs the removal of data without replacement.
        /// </summary>
        /// <param name="entityType">Type of entity (e.g., 'Client', 'Activity', 'PhoneNumber')</param>
        /// <param name="entityId">UID of the entity losing data</param>
        /// <param name="dataName">Name of the removed field/data</param>
        /// <param name="deletedValue">The value that was removed</param>
        public async Task LogDataDeletionAsync(string entityType, string entityId, string dataName, object deletedValue)
        {
            if (string.IsNullOrWhiteSpace(entityType))
                throw new ArgumentException("Entity type cannot be null or empty", nameof(entityType));
            if (string.IsNullOrWhiteSpace(entityId))
                throw new ArgumentException("Entity ID cannot be null or empty", nameof(entityId));
            if (string.IsNullOrWhiteSpace(dataName))
                throw new ArgumentException("Data name cannot be null or empty", nameof(dataName));
            if (deletedValue == null)
                throw new ArgumentNullException(nameof(deletedValue));

            try
            {
                string serializedValue = SerializeValue(deletedValue);

                const string sql = @"
                    INSERT INTO DeletedEntities (EntityType, EntityId, DataName, DeletedValue)
                    VALUES (@EntityType, @EntityId, @DataName, @DeletedValue)";

                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                await connection.ExecuteAsync(sql, new
                {
                    EntityType = entityType,
                    EntityId = entityId,
                    DataName = dataName,
                    DeletedValue = serializedValue
                });

                LoggingService.LogDebug($"Logged data deletion: {entityType}[{entityId}].{dataName} = {serializedValue}", "ArchiveDatabaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to log data deletion for {entityType}[{entityId}].{dataName}: {ex.Message}", "ArchiveDatabaseService");
                // Don't throw - audit logging failures should not block client operations
            }
        }

        #endregion

        #region Schema Initialization

        /// <summary>
        /// Creates the archive database tables if they don't exist.
        /// </summary>
        public async Task CreateTablesAsync()
        {
            try
            {
                LoggingService.LogDebug("Creating archive database tables", "ArchiveDatabaseService");

                // Read the schema SQL from embedded resource
                string schemaSql = ReadEmbeddedSqlResource("UFU2.Database.Archive_Schema.sql");

                if (string.IsNullOrEmpty(schemaSql))
                {
                    throw new InvalidOperationException("Archive schema SQL content is empty or could not be read from embedded resource");
                }

                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Execute the schema SQL
                await connection.ExecuteAsync(schemaSql);

                LoggingService.LogInfo("Archive database tables created successfully", "ArchiveDatabaseService");
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في إنشاء جداول قاعدة بيانات الأرشيف", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "ArchiveDatabaseService");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Serializes a value to JSON string for storage.
        /// </summary>
        /// <param name="value">Value to serialize</param>
        /// <returns>JSON string representation</returns>
        private string SerializeValue(object value)
        {
            if (value == null)
                return "null";

            if (value is string stringValue)
                return stringValue;

            try
            {
                return JsonSerializer.Serialize(value, new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to serialize value, using ToString(): {ex.Message}", "ArchiveDatabaseService");
                return value.ToString();
            }
        }

        /// <summary>
        /// Reads an embedded SQL resource file.
        /// </summary>
        /// <param name="resourceName">Name of the embedded resource</param>
        /// <returns>SQL content as string</returns>
        private string ReadEmbeddedSqlResource(string resourceName)
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                using var stream = assembly.GetManifestResourceStream(resourceName);
                
                if (stream == null)
                {
                    LoggingService.LogError($"Embedded resource not found: {resourceName}", "ArchiveDatabaseService");
                    return null;
                }

                using var reader = new System.IO.StreamReader(stream);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error reading embedded resource {resourceName}: {ex.Message}", "ArchiveDatabaseService");
                return null;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the service and its resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                LoggingService.LogDebug("ArchiveDatabaseService disposed", "ArchiveDatabaseService");
                _disposed = true;
            }
        }

        #endregion
    }
}
