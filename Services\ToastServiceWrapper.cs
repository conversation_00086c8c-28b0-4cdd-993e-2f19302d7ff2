using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Wrapper for the static ToastService to implement IToastService interface.
    /// Provides dependency injection compatibility and Arabic titles for UFU2 application.
    /// </summary>
    public class ToastServiceWrapper : IToastService
    {
        #region Private Constants

        // Arabic titles for toast notifications
        private const string SuccessTitle = "نجح";           // "Success"
        private const string ErrorTitle = "خطأ";             // "Error"
        private const string WarningTitle = "تحذير";         // "Warning"
        private const string InfoTitle = "معلومات";          // "Information"

        #endregion

        #region IToastService Implementation

        /// <summary>
        /// Shows a success toast notification with Arabic title
        /// </summary>
        /// <param name="message">The message content to display</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        public void ShowSuccess(string message, int duration = 3000, string? detailMessage = null)
        {
            try
            {
                ToastService.Success(SuccessTitle, message, duration, null, detailMessage);
                LoggingService.LogInfo($"Success toast displayed: {message}", "ToastServiceWrapper");
            }
            catch (System.Exception ex)
            {
                LoggingService.LogError($"Failed to show success toast: {ex.Message}", "ToastServiceWrapper");
                // Fallback to ErrorManager if toast fails
                ErrorManager.ShowUserInfo(message, SuccessTitle, "ToastServiceWrapper");
            }
        }

        /// <summary>
        /// Shows an error toast notification with Arabic title
        /// </summary>
        /// <param name="message">The message content to display</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        public void ShowError(string message, int duration = 5000, string? detailMessage = null)
        {
            try
            {
                ToastService.Error(ErrorTitle, message, duration, null, detailMessage);
                LoggingService.LogInfo($"Error toast displayed: {message}", "ToastServiceWrapper");
            }
            catch (System.Exception ex)
            {
                LoggingService.LogError($"Failed to show error toast: {ex.Message}", "ToastServiceWrapper");
                // Fallback to ErrorManager if toast fails
                ErrorManager.ShowUserError(message, ErrorTitle, "ToastServiceWrapper");
            }
        }

        /// <summary>
        /// Shows a warning toast notification with Arabic title
        /// </summary>
        /// <param name="message">The message content to display</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        public void ShowWarning(string message, int duration = 5000, string? detailMessage = null)
        {
            try
            {
                ToastService.Warning(WarningTitle, message, duration, null, detailMessage);
                LoggingService.LogInfo($"Warning toast displayed: {message}", "ToastServiceWrapper");
            }
            catch (System.Exception ex)
            {
                LoggingService.LogError($"Failed to show warning toast: {ex.Message}", "ToastServiceWrapper");
                // Fallback to ErrorManager if toast fails
                ErrorManager.ShowUserWarning(message, WarningTitle, "ToastServiceWrapper");
            }
        }

        /// <summary>
        /// Shows an info toast notification with Arabic title
        /// </summary>
        /// <param name="message">The message content to display</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        public void ShowInfo(string message, int duration = 3000, string? detailMessage = null)
        {
            try
            {
                ToastService.Info(InfoTitle, message, duration, null, detailMessage);
                LoggingService.LogInfo($"Info toast displayed: {message}", "ToastServiceWrapper");
            }
            catch (System.Exception ex)
            {
                LoggingService.LogError($"Failed to show info toast: {ex.Message}", "ToastServiceWrapper");
                // Fallback to ErrorManager if toast fails
                ErrorManager.ShowUserInfo(message, InfoTitle, "ToastServiceWrapper");
            }
        }

        #endregion

        #region Additional Helper Methods

        /// <summary>
        /// Shows a toast with custom title and type
        /// </summary>
        /// <param name="title">Custom title for the toast</param>
        /// <param name="message">The message content</param>
        /// <param name="type">The toast type</param>
        /// <param name="duration">Duration in milliseconds</param>
        /// <param name="detailMessage">Optional detailed message</param>
        public void ShowCustomToast(string title, string message, ToastType type, int duration = 3000, string? detailMessage = null)
        {
            try
            {
                ToastService.ShowToast(title, message, type, duration, null, detailMessage);
                LoggingService.LogInfo($"Custom toast displayed: {type} - {title}: {message}", "ToastServiceWrapper");
            }
            catch (System.Exception ex)
            {
                LoggingService.LogError($"Failed to show custom toast: {ex.Message}", "ToastServiceWrapper");
                
                // Fallback to appropriate ErrorManager method based on type
                switch (type)
                {
                    case ToastType.Success:
                    case ToastType.Info:
                        ErrorManager.ShowUserInfo(message, title, "ToastServiceWrapper");
                        break;
                    case ToastType.Warning:
                        ErrorManager.ShowUserWarning(message, title, "ToastServiceWrapper");
                        break;
                    case ToastType.Error:
                        ErrorManager.ShowUserError(message, title, "ToastServiceWrapper");
                        break;
                }
            }
        }

        /// <summary>
        /// Gets whether the toast service is properly initialized
        /// </summary>
        /// <returns>True if the service is initialized and ready to use</returns>
        public bool IsServiceReady()
        {
            return ToastService.IsInitialized;
        }

        /// <summary>
        /// Gets the current number of active toasts
        /// </summary>
        /// <returns>Number of currently displayed toasts</returns>
        public int GetActiveToastCount()
        {
            return ToastService.ActiveToastCount;
        }

        #endregion
    }
}
