using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Service error handler utility for UFU2 application.
    /// Provides centralized error handling and performance logging for service operations.
    /// </summary>
    public static class ServiceErrorHandler
    {
        /// <summary>
        /// Executes an action with error handling and performance logging
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="serviceName">The name of the service</param>
        /// <param name="operationName">The name of the operation</param>
        /// <param name="errorMessage">The error message to log if an exception occurs</param>
        /// <param name="logLevel">The log level for errors</param>
        /// <returns>True if the operation succeeded, false otherwise</returns>
        public static async Task<bool> ExecuteWithPerformanceLoggingAsync(
            Action action,
            string serviceName,
            string operationName,
            string errorMessage,
            LogLevel logLevel = LogLevel.Error)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                await Task.Run(action);
                stopwatch.Stop();
                
                LoggingService.LogDebug(
                    $"{serviceName}.{operationName} completed in {stopwatch.ElapsedMilliseconds}ms",
                    serviceName);
                
                return true;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                var fullErrorMessage = $"{errorMessage}: {ex.Message}";
                
                switch (logLevel)
                {
                    case LogLevel.Error:
                        LoggingService.LogError(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Warning:
                        LoggingService.LogWarning(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Info:
                        LoggingService.LogInfo(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Debug:
                        LoggingService.LogDebug(fullErrorMessage, serviceName);
                        break;
                }
                
                return false;
            }
        }

        /// <summary>
        /// Executes an async function with error handling and performance logging
        /// </summary>
        /// <param name="func">The async function to execute</param>
        /// <param name="serviceName">The name of the service</param>
        /// <param name="operationName">The name of the operation</param>
        /// <param name="errorMessage">The error message to log if an exception occurs</param>
        /// <param name="logLevel">The log level for errors</param>
        /// <returns>True if the operation succeeded, false otherwise</returns>
        public static async Task<bool> ExecuteWithPerformanceLoggingAsync(
            Func<Task> func,
            string serviceName,
            string operationName,
            string errorMessage,
            LogLevel logLevel = LogLevel.Error)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                await func();
                stopwatch.Stop();
                
                LoggingService.LogDebug(
                    $"{serviceName}.{operationName} completed in {stopwatch.ElapsedMilliseconds}ms",
                    serviceName);
                
                return true;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                var fullErrorMessage = $"{errorMessage}: {ex.Message}";
                
                switch (logLevel)
                {
                    case LogLevel.Error:
                        LoggingService.LogError(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Warning:
                        LoggingService.LogWarning(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Info:
                        LoggingService.LogInfo(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Debug:
                        LoggingService.LogDebug(fullErrorMessage, serviceName);
                        break;
                }
                
                return false;
            }
        }

        /// <summary>
        /// Executes a function with error handling and performance logging
        /// </summary>
        /// <type param name="T">The return type of the function</type param>
        /// <param name="func">The function to execute</param>
        /// <param name="serviceName">The name of the service</param>
        /// <param name="operationName">The name of the operation</param>
        /// <param name="errorMessage">The error message to log if an exception occurs</param>
        /// <param name="defaultValue">The default value to return if an exception occurs</param>
        /// <param name="logLevel">The log level for errors</param>
        /// <returns>The result of the function or the default value if an exception occurs</returns>
        public static async Task<T> ExecuteWithPerformanceLoggingAsync<T>(
            Func<T> func,
            string serviceName,
            string operationName,
            string errorMessage,
            T defaultValue = default(T),
            LogLevel logLevel = LogLevel.Error)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var result = await Task.Run(func);
                stopwatch.Stop();
                
                LoggingService.LogDebug(
                    $"{serviceName}.{operationName} completed in {stopwatch.ElapsedMilliseconds}ms",
                    serviceName);
                
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                var fullErrorMessage = $"{errorMessage}: {ex.Message}";
                
                switch (logLevel)
                {
                    case LogLevel.Error:
                        LoggingService.LogError(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Warning:
                        LoggingService.LogWarning(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Info:
                        LoggingService.LogInfo(fullErrorMessage, serviceName);
                        break;
                    case LogLevel.Debug:
                        LoggingService.LogDebug(fullErrorMessage, serviceName);
                        break;
                }
                
                return defaultValue;
            }
        }
    }
}
