using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using UFU2.Common;
using UFU2.Common.Utilities;
using UFU2.Services.Interfaces;
using UFU2.Services.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Enhanced search service that uses exact prefix matching and Arabic text analysis.
    /// Focuses on exact prefix matching behavior where results must start with the search term.
    /// Prioritizes results based on exact prefix matches with proper Arabic text normalization.
    /// </summary>
    public class WordFrequencySearchService : IDisposable
    {
        #region Private Fields

        private readonly IArabicTextAnalyzer _arabicTextAnalyzer;
        private IMemoryCache _searchCache;
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);
        private int _cacheHits = 0;
        private int _cacheMisses = 0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the WordFrequencySearchService class.
        /// </summary>
        /// <param name="arabicTextAnalyzer">Arabic text analyzer service</param>
        public WordFrequencySearchService(IArabicTextAnalyzer arabicTextAnalyzer)
        {
            _arabicTextAnalyzer = arabicTextAnalyzer ?? throw new ArgumentNullException(nameof(arabicTextAnalyzer));

            _searchCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 300,
                CompactionPercentage = 0.25
            });

            LoggingService.LogInfo("WordFrequencySearchService initialized with Arabic text analysis", "WordFrequencySearchService");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Performs enhanced search using exact prefix matching.
        /// Results are sorted by exact prefix matches first, then by prefix length.
        /// </summary>
        /// <typeparam name="T">Type of items to search</typeparam>
        /// <param name="items">Collection of items to search</param>
        /// <param name="searchRequest">Search request parameters</param>
        /// <param name="textSelector">Function to extract searchable text from items</param>
        /// <returns>Search result collection with ranked results</returns>
        public async Task<SearchResultCollection<T>> SearchAsync<T>(
            IEnumerable<T> items,
            SearchRequest searchRequest,
            Func<T, string> textSelector)
        {
            if (string.IsNullOrWhiteSpace(searchRequest?.SearchTerm) || items == null)
            {
                return new SearchResultCollection<T>();
            }

            var stopwatch = Stopwatch.StartNew();

            try
            {
                string cacheKey = GenerateCacheKey(searchRequest);

                // Check cache first
                if (_searchCache.TryGetValue(cacheKey, out SearchResultCollection<T> cachedResults))
                {
                    _cacheHits++;
                    cachedResults.FromCache = true;
                    LogCachePerformance();
                    return cachedResults;
                }

                _cacheMisses++;

                // Analyze search term using Arabic text analyzer
                var searchAnalysis = _arabicTextAnalyzer.AnalyzeText(searchRequest.SearchTerm);
                var searchTerms = ExtractSearchTerms(searchAnalysis, searchRequest);

                var results = new List<EnhancedSearchResult<T>>();
                int totalSearched = 0;

                await Task.Run(() =>
                {
                    foreach (var item in items)
                    {
                        totalSearched++;
                        string itemText = textSelector(item);
                        
                        if (string.IsNullOrWhiteSpace(itemText))
                            continue;

                        // Calculate relevance score using word frequency analysis
                        var searchResult = AnalyzeItemMatch(item, itemText, searchTerms, searchAnalysis, searchRequest);
                        
                        if (searchResult != null && searchResult.RelevanceScore >= searchRequest.MinSimilarity)
                        {
                            results.Add(searchResult);
                        }
                    }

                    // Sort by exact prefix matches first, then by prefix length, then alphabetically
                    results = results
                        .OrderByDescending(r => r.MatchType == Models.MatchType.Exact ? 1 : 0) // Exact matches first
                        .ThenByDescending(r => r.MatchType == Models.MatchType.StartsWith ? 1 : 0) // Then prefix matches
                        .ThenByDescending(r => r.RelevanceScore) // Then by relevance score (prefix coverage)
                        .ThenByDescending(r => CalculatePrefixLength(searchRequest.SearchTerm, r.NormalizedText)) // Longer prefixes first
                        .ThenBy(r => r.NormalizedText, StringComparer.OrdinalIgnoreCase) // Alphabetical order
                        .Take(searchRequest.MaxResults)
                        .ToList();
                });

                stopwatch.Stop();

                // Create result collection
                var resultCollection = new SearchResultCollection<T>
                {
                    Results = results.ToArray(),
                    TotalSearched = totalSearched,
                    TotalMatches = results.Count,
                    ExecutionTime = stopwatch.Elapsed,
                    FromCache = false,
                    SearchTerm = searchRequest.SearchTerm,
                    NormalizedSearchTerms = searchTerms,
                    UsedArabicAnalysis = searchAnalysis.ContainsArabicText,
                    Metadata = new Dictionary<string, object>
                    {
                        ["SearchAnalysis"] = searchAnalysis,
                        ["CacheHits"] = _cacheHits,
                        ["CacheMisses"] = _cacheMisses
                    }
                };

                // Cache the results
                CacheResults(cacheKey, resultCollection);

                LogSearchCompletion(searchRequest.SearchTerm, results.Count, stopwatch.Elapsed);

                return resultCollection;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in word frequency search for '{searchRequest.SearchTerm}': {ex.Message}", "WordFrequencySearchService");
                return new SearchResultCollection<T>
                {
                    SearchTerm = searchRequest.SearchTerm,
                    ExecutionTime = stopwatch.Elapsed
                };
            }
        }

        /// <summary>
        /// Clears the search cache.
        /// </summary>
        public void ClearCache()
        {
            // IMemoryCache doesn't have a Clear() method, so we dispose and recreate it
            _searchCache?.Dispose();
            _searchCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 300,
                CompactionPercentage = 0.25
            });
            LoggingService.LogInfo("Search cache cleared", "WordFrequencySearchService");
        }

        /// <summary>
        /// Gets cache performance statistics.
        /// </summary>
        /// <returns>Dictionary containing cache performance metrics</returns>
        public Dictionary<string, object> GetCacheStatistics()
        {
            return new Dictionary<string, object>
            {
                ["CacheHits"] = _cacheHits,
                ["CacheMisses"] = _cacheMisses,
                ["HitRatio"] = _cacheHits + _cacheMisses > 0 ? (double)_cacheHits / (_cacheHits + _cacheMisses) : 0.0
            };
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Generates a cache key for the search request.
        /// </summary>
        private string GenerateCacheKey(SearchRequest request)
        {
            return $"wf_search_{request.SearchTerm.ToLowerInvariant()}_{request.MaxResults}_{request.MinSimilarity}_{request.UseArabicAnalysis}";
        }

        /// <summary>
        /// Extracts search terms from the Arabic text analysis.
        /// </summary>
        private string[] ExtractSearchTerms(ArabicTextAnalysis analysis, SearchRequest request)
        {
            var terms = new List<string>();

            // Add the normalized text
            if (!string.IsNullOrWhiteSpace(analysis.NormalizedText))
            {
                terms.Add(analysis.NormalizedText);
            }

            // Add individual meaningful words
            var meaningfulWords = analysis.Words
                .Where(w => !w.IsStopWord && !w.IsConjunction && w.Length >= 2)
                .Select(w => w.NormalizedWord)
                .Distinct(StringComparer.OrdinalIgnoreCase)
                .ToArray();

            terms.AddRange(meaningfulWords);

            // Add search variations if using Arabic analysis
            if (request.UseArabicAnalysis && analysis.ContainsArabicText)
            {
                terms.AddRange(analysis.SearchVariations);
            }

            return terms.Distinct(StringComparer.OrdinalIgnoreCase).ToArray();
        }

        /// <summary>
        /// Analyzes how well an item matches the search criteria using exact prefix matching.
        /// </summary>
        private EnhancedSearchResult<T> AnalyzeItemMatch<T>(
            T item,
            string itemText,
            string[] searchTerms,
            ArabicTextAnalysis searchAnalysis,
            SearchRequest request)
        {
            try
            {
                // Normalize item text
                string normalizedItemText = TextNormalizationHelper.NormalizeForSearch(itemText);

                // Calculate relevance score using exact prefix matching
                double relevanceScore = _arabicTextAnalyzer.CalculateRelevanceScore(searchTerms, normalizedItemText);

                if (relevanceScore < request.MinSimilarity)
                {
                    return null;
                }

                // Determine match type based on exact prefix matching
                var matchType = DeterminePrefixMatchType(request.SearchTerm, normalizedItemText);

                // For exact prefix matching, we only care about prefix matches
                int exactMatches = matchType == Models.MatchType.Exact ? 1 : 0;
                int prefixMatches = matchType == Models.MatchType.StartsWith ? 1 : 0;

                return new EnhancedSearchResult<T>
                {
                    Item = item,
                    SimilarityScore = relevanceScore, // For backward compatibility
                    RelevanceScore = relevanceScore,
                    MatchType = matchType,
                    OriginalText = itemText,
                    NormalizedText = normalizedItemText,
                    ExactWordMatches = exactMatches,
                    PartialWordMatches = prefixMatches,
                    TotalMatchedTerms = exactMatches + prefixMatches,
                    MatchedWords = exactMatches > 0 || prefixMatches > 0 ? new[] { request.SearchTerm } : Array.Empty<string>(),
                    UsedArabicAnalysis = searchAnalysis.ContainsArabicText
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error analyzing item match: {ex.Message}", "WordFrequencySearchService");
                return null;
            }
        }

        /// <summary>
        /// Calculates the length of the matching prefix between search term and target text.
        /// </summary>
        private int CalculatePrefixLength(string searchTerm, string targetText)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || string.IsNullOrWhiteSpace(targetText))
                return 0;

            try
            {
                string normalizedSearchTerm = TextNormalizationHelper.NormalizeForSearch(searchTerm).Trim();
                string normalizedTargetText = TextNormalizationHelper.NormalizeForSearch(targetText).Trim();

                if (normalizedTargetText.StartsWith(normalizedSearchTerm, StringComparison.OrdinalIgnoreCase))
                {
                    return normalizedSearchTerm.Length;
                }

                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Determines the match type based on exact prefix matching behavior.
        /// Only returns Exact or StartsWith match types for valid prefix matches.
        /// </summary>
        private Models.MatchType DeterminePrefixMatchType(string searchTerm, string targetText)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || string.IsNullOrWhiteSpace(targetText))
            {
                return Models.MatchType.Fuzzy; // No match
            }

            try
            {
                // Normalize both texts for consistent comparison
                string normalizedSearchTerm = TextNormalizationHelper.NormalizeForSearch(searchTerm).Trim();
                string normalizedTargetText = TextNormalizationHelper.NormalizeForSearch(targetText).Trim();

                // Check for exact match of entire text
                if (string.Equals(normalizedSearchTerm, normalizedTargetText, StringComparison.OrdinalIgnoreCase))
                {
                    return Models.MatchType.Exact;
                }

                // Check if target starts with search term (exact prefix match)
                if (normalizedTargetText.StartsWith(normalizedSearchTerm, StringComparison.OrdinalIgnoreCase))
                {
                    return Models.MatchType.StartsWith;
                }

                // No valid prefix match found
                return Models.MatchType.Fuzzy;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error determining prefix match type: {ex.Message}", "WordFrequencySearchService");
                return Models.MatchType.Fuzzy;
            }
        }

        /// <summary>
        /// Caches search results with appropriate expiration.
        /// </summary>
        private void CacheResults<T>(string cacheKey, SearchResultCollection<T> results)
        {
            try
            {
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = _cacheExpiration,
                    Size = 1,
                    Priority = CacheItemPriority.Normal
                };

                _searchCache.Set(cacheKey, results, cacheOptions);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error caching search results: {ex.Message}", "WordFrequencySearchService");
            }
        }

        /// <summary>
        /// Logs cache performance periodically.
        /// </summary>
        private void LogCachePerformance()
        {
            if (_cacheHits % 20 == 0 || (_cacheHits + _cacheMisses) % 50 == 0)
            {
                double hitRatio = _cacheHits + _cacheMisses > 0 ? (double)_cacheHits / (_cacheHits + _cacheMisses) : 0.0;
                LoggingService.LogDebug($"Search cache performance: {_cacheHits} hits, {_cacheMisses} misses (hit ratio: {hitRatio:P1})", "WordFrequencySearchService");
            }
        }

        /// <summary>
        /// Logs search completion with performance metrics.
        /// </summary>
        private void LogSearchCompletion(string searchTerm, int resultCount, TimeSpan executionTime)
        {
            if (resultCount > 10 || executionTime.TotalMilliseconds > 100 || _cacheMisses % 10 == 0)
            {
                LoggingService.LogDebug($"Word frequency search completed for '{searchTerm}': {resultCount} results in {executionTime.TotalMilliseconds:F1}ms", "WordFrequencySearchService");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the search cache and releases resources.
        /// </summary>
        public void Dispose()
        {
            _searchCache?.Dispose();
        }

        #endregion
    }
}
