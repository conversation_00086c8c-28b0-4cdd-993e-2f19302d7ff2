using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Dialog for displaying detailed information about a craft type.
    /// Shows content and secondary information from the CraftTypeBase database.
    /// </summary>
    public partial class CraftInformationDialog : UserControl
    {
        #region Private Fields

        private readonly CraftTypeBaseModel _craftType;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the CraftInformationDialog class.
        /// </summary>
        /// <param name="craftType">The craft type to display information for</param>
        public CraftInformationDialog(CraftTypeBaseModel craftType)
        {
            InitializeComponent();

            _craftType = craftType ?? throw new ArgumentNullException(nameof(craftType));

            LoadCraftInformation();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Loads and displays the craft information in the dialog.
        /// </summary>
        private void LoadCraftInformation()
        {
            try
            {
                // Set craft code and description
                CraftCodeTextBlock.Text = _craftType.Code;
                CraftDescriptionTextBlock.Text = _craftType.Description;

                // Set content with fallback text
                if (!string.IsNullOrWhiteSpace(_craftType.Content))
                {
                    ContentTextBlock.Text = _craftType.Content;
                }
                else
                {
                    ContentTextBlock.Text = "لاتوجد معلومات اضافية";
                    ContentTextBlock.FontStyle = FontStyles.Italic;
                    ContentTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                }

                // Set secondary information with fallback text
                if (!string.IsNullOrWhiteSpace(_craftType.Secondary))
                {
                    SecondaryTextBlock.Text = _craftType.Secondary;
                }
                else
                {
                    SecondaryTextBlock.Text = "لاتوجد معلومات اضافية";
                    SecondaryTextBlock.FontStyle = FontStyles.Italic;
                    SecondaryTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                }

                LoggingService.LogDebug($"Craft information loaded for: {_craftType.Code} - {_craftType.Description}", "CraftInformationDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading craft information: {ex.Message}", "CraftInformationDialog");
                
                // Set error message
                ContentTextBlock.Text = "حدث خطأ أثناء تحميل المعلومات";
                SecondaryTextBlock.Text = "حدث خطأ أثناء تحميل المعلومات";
                
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ أثناء تحميل معلومات الحرفة",
                    "خطأ في التحميل",
                    "CraftInformationDialog");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the Click event for the Close button.
        /// Closes the dialog.
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug("Craft information dialog closed", "CraftInformationDialog");
                
                // Close dialog
                DialogHost.CloseDialogCommand.Execute(true, this);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing craft information dialog: {ex.Message}", "CraftInformationDialog");
            }
        }

        #endregion
    }
}
