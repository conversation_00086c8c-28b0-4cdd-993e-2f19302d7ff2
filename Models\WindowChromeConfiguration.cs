using System.Windows;

namespace UFU2.Models
{
    /// <summary>
    /// Configuration model for custom window chrome settings
    /// </summary>
    public class WindowChromeConfiguration
    {
        /// <summary>
        /// Height of the window caption/title bar in pixels
        /// </summary>
        public double CaptionHeight { get; set; } = 32;

        /// <summary>
        /// Thickness of the resize border around the window in pixels
        /// </summary>
        public double ResizeBorderThickness { get; set; } = 8;

        /// <summary>
        /// Corner radius for the window chrome
        /// </summary>
        public CornerRadius CornerRadius { get; set; } = new CornerRadius(0);

        /// <summary>
        /// Glass frame thickness for Aero effects (0 to disable)
        /// </summary>
        public double GlassFrameThickness { get; set; } = 0;

        /// <summary>
        /// Whether to use native Aero caption buttons
        /// </summary>
        public bool UseAeroCaptionButtons { get; set; } = false;
    }
}