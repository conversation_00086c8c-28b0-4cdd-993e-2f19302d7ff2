using System;
using System.Windows;
using UFU2.Services;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Calculator for optimal backend container dimensions that maintain WYSIWYG accuracy.
    /// Provides exact minimum scale calculations to accommodate original images while 
    /// preserving the 500:320 aspect ratio of the PreviewContainer.
    /// Follows UFU2 patterns with comprehensive error handling and logging.
    /// </summary>
    public static class BackendContainerCalculator
    {
        #region Constants

        /// <summary>
        /// PreviewContainer dimensions as defined in ImageManagementDialog.xaml
        /// </summary>
        private const double PreviewWidth = 500.0;
        private const double PreviewHeight = 320.0;

        /// <summary>
        /// PreviewContainer aspect ratio (500:320 = 1.5625)
        /// </summary>
        private const double PreviewAspectRatio = PreviewWidth / PreviewHeight;

        /// <summary>
        /// Minimum backend container scale factor to ensure quality
        /// </summary>
        private const double MinimumScaleFactor = 1.0;

        /// <summary>
        /// Maximum backend container scale factor to prevent memory issues
        /// </summary>
        private const double MaximumScaleFactor = 10.0;

        /// <summary>
        /// Minimum backend container size for small image upscaling
        /// </summary>
        private const double MinimumBackendSize = 1000.0;

        #endregion

        #region Public Methods

        /// <summary>
        /// Calculates the optimal backend container size and scale factor for the given image.
        /// Uses exact minimum scale calculation to ensure the backend container can accommodate
        /// the original image at 100% zoom while maintaining the preview aspect ratio.
        /// </summary>
        /// <param name="originalImageSize">Size of the original image in pixels</param>
        /// <returns>Backend container result with size, scale factor, and metadata</returns>
        public static BackendContainerResult CalculateOptimalBackendContainer(Size originalImageSize)
        {
            try
            {
                LoggingService.LogDebug($"Calculating backend container for image: {originalImageSize.Width}x{originalImageSize.Height}", 
                    "BackendContainerCalculator");

                // Validate input parameters
                if (!IsValidImageSize(originalImageSize))
                {
                    LoggingService.LogWarning($"Invalid image size provided: {originalImageSize}", "BackendContainerCalculator");
                    return CreateFallbackResult();
                }

                // Handle small images that require upscaling
                if (IsSmallImage(originalImageSize))
                {
                    return CalculateUpscaledBackendContainer(originalImageSize);
                }

                // Calculate minimum scale to contain the original image
                double scaleX = originalImageSize.Width / PreviewWidth;
                double scaleY = originalImageSize.Height / PreviewHeight;
                double minScale = Math.Max(scaleX, scaleY);

                // Apply constraints and rounding
                double scaleFactor = Math.Max(MinimumScaleFactor, Math.Min(MaximumScaleFactor, Math.Ceiling(minScale)));

                var backendSize = new Size(
                    PreviewWidth * scaleFactor,
                    PreviewHeight * scaleFactor
                );

                var result = new BackendContainerResult
                {
                    BackendSize = backendSize,
                    ScaleFactor = scaleFactor,
                    RequiresUpscaling = false,
                    UpscaleFactor = 1.0,
                    IsValid = true
                };

                LoggingService.LogDebug($"Backend container calculated: {backendSize.Width}x{backendSize.Height} (scale: {scaleFactor:F2})", 
                    "BackendContainerCalculator");

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating backend container: {ex.Message}", "BackendContainerCalculator");
                return CreateFallbackResult();
            }
        }

        /// <summary>
        /// Calculates the backend crop guide size based on the scale factor.
        /// Maintains the 127:145 aspect ratio at the backend scale.
        /// </summary>
        /// <param name="scaleFactor">Backend container scale factor</param>
        /// <returns>Backend crop guide size</returns>
        public static Size CalculateBackendCropGuideSize(double scaleFactor)
        {
            try
            {
                const double defaultCropWidth = 254.0;
                const double defaultCropHeight = 290.0;

                var backendCropSize = new Size(
                    defaultCropWidth * scaleFactor,
                    defaultCropHeight * scaleFactor
                );

                LoggingService.LogDebug($"Backend crop guide size: {backendCropSize.Width}x{backendCropSize.Height}", 
                    "BackendContainerCalculator");

                return backendCropSize;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating backend crop guide size: {ex.Message}", "BackendContainerCalculator");
                return new Size(254.0, 290.0); // Fallback to default
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validates that the image size is valid for processing
        /// </summary>
        private static bool IsValidImageSize(Size imageSize)
        {
            return imageSize.Width > 0 && imageSize.Height > 0 &&
                   !double.IsNaN(imageSize.Width) && !double.IsInfinity(imageSize.Width) &&
                   !double.IsNaN(imageSize.Height) && !double.IsInfinity(imageSize.Height) &&
                   imageSize.Width <= 50000 && imageSize.Height <= 50000; // Reasonable maximum
        }

        /// <summary>
        /// Determines if an image is considered small and requires upscaling
        /// </summary>
        private static bool IsSmallImage(Size imageSize)
        {
            return imageSize.Width < PreviewWidth && imageSize.Height < PreviewHeight;
        }

        /// <summary>
        /// Calculates backend container for small images that require upscaling
        /// </summary>
        private static BackendContainerResult CalculateUpscaledBackendContainer(Size originalImageSize)
        {
            try
            {
                LoggingService.LogDebug($"Calculating upscaled backend container for small image: {originalImageSize.Width}x{originalImageSize.Height}", 
                    "BackendContainerCalculator");

                // Calculate upscale factor to reach minimum backend size
                double maxDimension = Math.Max(originalImageSize.Width, originalImageSize.Height);
                double upscaleFactor = Math.Max(
                    MinimumBackendSize / maxDimension,
                    2.0 // Minimum 2x upscale for quality
                );

                // Calculate backend scale factor
                double backendScaleFactor = Math.Max(
                    (originalImageSize.Width * upscaleFactor) / PreviewWidth,
                    (originalImageSize.Height * upscaleFactor) / PreviewHeight
                );

                backendScaleFactor = Math.Ceiling(backendScaleFactor);

                var backendSize = new Size(
                    PreviewWidth * backendScaleFactor,
                    PreviewHeight * backendScaleFactor
                );

                var result = new BackendContainerResult
                {
                    BackendSize = backendSize,
                    ScaleFactor = backendScaleFactor,
                    RequiresUpscaling = true,
                    UpscaleFactor = upscaleFactor,
                    IsValid = true
                };

                LoggingService.LogDebug($"Upscaled backend container: {backendSize.Width}x{backendSize.Height} (scale: {backendScaleFactor:F2}, upscale: {upscaleFactor:F2})", 
                    "BackendContainerCalculator");

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating upscaled backend container: {ex.Message}", "BackendContainerCalculator");
                return CreateFallbackResult();
            }
        }

        /// <summary>
        /// Creates a fallback result for error scenarios
        /// </summary>
        private static BackendContainerResult CreateFallbackResult()
        {
            return new BackendContainerResult
            {
                BackendSize = new Size(PreviewWidth * 2, PreviewHeight * 2),
                ScaleFactor = 2.0,
                RequiresUpscaling = false,
                UpscaleFactor = 1.0,
                IsValid = false
            };
        }

        #endregion
    }

    /// <summary>
    /// Result of backend container calculation with metadata
    /// </summary>
    public class BackendContainerResult
    {
        /// <summary>
        /// Calculated backend container size
        /// </summary>
        public Size BackendSize { get; set; }

        /// <summary>
        /// Scale factor from preview to backend container
        /// </summary>
        public double ScaleFactor { get; set; }

        /// <summary>
        /// Whether the original image requires upscaling
        /// </summary>
        public bool RequiresUpscaling { get; set; }

        /// <summary>
        /// Upscale factor for small images
        /// </summary>
        public double UpscaleFactor { get; set; }

        /// <summary>
        /// Whether the calculation was successful
        /// </summary>
        public bool IsValid { get; set; }
    }
}
