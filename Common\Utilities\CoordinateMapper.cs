using System;
using System.Windows;
using System.Windows.Media;
using UFU2.Services;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Provides precise coordinate transformation methods for mapping between preview coordinates,
    /// backend coordinates, and original image pixel coordinates.
    /// Handles transformation matrices correctly for zoom, rotation, and translation operations.
    /// Follows UFU2 patterns with comprehensive error handling and logging.
    /// </summary>
    public class CoordinateMapper
    {
        #region Private Fields

        private readonly PreviewBackendSynchronizer _synchronizer;
        private const double FloatingPointTolerance = 1e-10;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the CoordinateMapper.
        /// </summary>
        /// <param name="synchronizer">Preview backend synchronizer instance</param>
        public CoordinateMapper(PreviewBackendSynchronizer synchronizer)
        {
            _synchronizer = synchronizer ?? throw new ArgumentNullException(nameof(synchronizer));
            
            LoggingService.LogDebug("CoordinateMapper initialized", "CoordinateMapper");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Maps a point from preview coordinates to original image pixel coordinates.
        /// Applies inverse transformations to account for zoom, rotation, and translation.
        /// </summary>
        /// <param name="previewPoint">Point in preview coordinates</param>
        /// <param name="backendState">Backend transformation state</param>
        /// <returns>Point in original image pixel coordinates</returns>
        public Point MapPreviewToImageCoordinates(Point previewPoint, BackendTransformState backendState)
        {
            try
            {
                LoggingService.LogDebug($"Mapping preview point {previewPoint} to image coordinates", "CoordinateMapper");

                // Step 1: Convert preview point to backend coordinates
                var backendPoint = new Point(
                    previewPoint.X * _synchronizer.BackendContainer.ScaleFactor,
                    previewPoint.Y * _synchronizer.BackendContainer.ScaleFactor
                );

                // Step 2: Apply inverse transformations to get image coordinates
                var imagePoint = ApplyInverseTransformations(backendPoint, backendState);

                LoggingService.LogDebug($"Mapped to image coordinates: {imagePoint}", "CoordinateMapper");

                return imagePoint;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error mapping preview to image coordinates: {ex.Message}", "CoordinateMapper");
                return new Point(0, 0);
            }
        }

        /// <summary>
        /// Maps a crop rectangle from preview coordinates to exact image pixel coordinates.
        /// IMPORTANT: This method does NOT apply rotation to the crop rectangle coordinates.
        /// Rotation is handled separately in the final cropping stage to maintain WYSIWYG accuracy.
        /// The crop rectangle coordinates remain in the original image coordinate system.
        /// </summary>
        /// <param name="previewCropRect">Crop rectangle in preview coordinates</param>
        /// <param name="backendState">Backend transformation state</param>
        /// <returns>Crop rectangle in original image pixel coordinates (unrotated)</returns>
        public Rect MapCropRectangleToImageCoordinates(Rect previewCropRect, BackendTransformState backendState)
        {
            try
            {
                LoggingService.LogDebug($"Mapping crop rectangle {previewCropRect} to image coordinates (rotation-independent)", "CoordinateMapper");

                // Use rotation-independent mapping to maintain coordinate system separation
                var topLeft = MapPreviewToImageCoordinatesWithoutRotation(
                    new Point(previewCropRect.Left, previewCropRect.Top), backendState);
                var topRight = MapPreviewToImageCoordinatesWithoutRotation(
                    new Point(previewCropRect.Right, previewCropRect.Top), backendState);
                var bottomLeft = MapPreviewToImageCoordinatesWithoutRotation(
                    new Point(previewCropRect.Left, previewCropRect.Bottom), backendState);
                var bottomRight = MapPreviewToImageCoordinatesWithoutRotation(
                    new Point(previewCropRect.Right, previewCropRect.Bottom), backendState);

                // Calculate bounding rectangle of transformed corners
                double minX = Math.Min(Math.Min(topLeft.X, topRight.X), Math.Min(bottomLeft.X, bottomRight.X));
                double minY = Math.Min(Math.Min(topLeft.Y, topRight.Y), Math.Min(bottomLeft.Y, bottomRight.Y));
                double maxX = Math.Max(Math.Max(topLeft.X, topRight.X), Math.Max(bottomLeft.X, bottomRight.X));
                double maxY = Math.Max(Math.Max(topLeft.Y, topRight.Y), Math.Max(bottomLeft.Y, bottomRight.Y));

                var imageCropRect = new Rect(minX, minY, maxX - minX, maxY - minY);

                LoggingService.LogDebug($"Mapped crop rectangle to image coordinates (unrotated): {imageCropRect}", "CoordinateMapper");

                return imageCropRect;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error mapping crop rectangle to image coordinates: {ex.Message}", "CoordinateMapper");
                return Rect.Empty;
            }
        }

        /// <summary>
        /// Maps a point from preview coordinates to image coordinates WITHOUT applying rotation.
        /// This method is used specifically for crop rectangle mapping to maintain coordinate system separation.
        /// Rotation is handled separately in the final cropping stage.
        /// </summary>
        /// <param name="previewPoint">Point in preview coordinates</param>
        /// <param name="backendState">Backend transformation state</param>
        /// <returns>Point in original image pixel coordinates (unrotated)</returns>
        private Point MapPreviewToImageCoordinatesWithoutRotation(Point previewPoint, BackendTransformState backendState)
        {
            try
            {
                LoggingService.LogDebug($"Mapping preview point {previewPoint} to image coordinates (without rotation)", "CoordinateMapper");

                // Step 1: Convert preview point to backend coordinates
                var backendPoint = new Point(
                    previewPoint.X * _synchronizer.BackendContainer.ScaleFactor,
                    previewPoint.Y * _synchronizer.BackendContainer.ScaleFactor
                );

                // Step 2: Apply inverse transformations WITHOUT rotation to get image coordinates
                var imagePoint = ApplyInverseTransformationsWithoutRotation(backendPoint, backendState);

                LoggingService.LogDebug($"Mapped to image coordinates (unrotated): {imagePoint}", "CoordinateMapper");

                return imagePoint;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error mapping preview to image coordinates without rotation: {ex.Message}", "CoordinateMapper");
                return new Point(0, 0);
            }
        }

        /// <summary>
        /// Maps a point from original image coordinates to preview coordinates.
        /// Applies forward transformations for zoom, rotation, and translation.
        /// </summary>
        /// <param name="imagePoint">Point in original image coordinates</param>
        /// <param name="backendState">Backend transformation state</param>
        /// <returns>Point in preview coordinates</returns>
        public Point MapImageToPreviewCoordinates(Point imagePoint, BackendTransformState backendState)
        {
            try
            {
                LoggingService.LogDebug($"Mapping image point {imagePoint} to preview coordinates", "CoordinateMapper");

                // Step 1: Apply forward transformations to get backend coordinates
                var backendPoint = ApplyForwardTransformations(imagePoint, backendState);

                // Step 2: Convert backend point to preview coordinates
                var previewPoint = new Point(
                    backendPoint.X / _synchronizer.BackendContainer.ScaleFactor,
                    backendPoint.Y / _synchronizer.BackendContainer.ScaleFactor
                );

                LoggingService.LogDebug($"Mapped to preview coordinates: {previewPoint}", "CoordinateMapper");

                return previewPoint;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error mapping image to preview coordinates: {ex.Message}", "CoordinateMapper");
                return new Point(0, 0);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Applies inverse transformations to convert backend coordinates to image coordinates
        /// </summary>
        private Point ApplyInverseTransformations(Point backendPoint, BackendTransformState state)
        {
            try
            {
                // Create inverse transformation matrix
                var matrix = CreateInverseTransformationMatrix(state);

                // Apply transformation
                return matrix.Transform(backendPoint);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying inverse transformations: {ex.Message}", "CoordinateMapper");
                return backendPoint;
            }
        }

        /// <summary>
        /// Applies inverse transformations WITHOUT rotation to convert backend coordinates to image coordinates.
        /// Used specifically for crop rectangle mapping to maintain coordinate system separation.
        /// </summary>
        private Point ApplyInverseTransformationsWithoutRotation(Point backendPoint, BackendTransformState state)
        {
            try
            {
                // Create inverse transformation matrix WITHOUT rotation
                var matrix = CreateInverseTransformationMatrixWithoutRotation(state);

                // Apply transformation
                return matrix.Transform(backendPoint);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying inverse transformations without rotation: {ex.Message}", "CoordinateMapper");
                return backendPoint;
            }
        }

        /// <summary>
        /// Applies forward transformations to convert image coordinates to backend coordinates
        /// </summary>
        private Point ApplyForwardTransformations(Point imagePoint, BackendTransformState state)
        {
            try
            {
                // Create forward transformation matrix
                var matrix = CreateForwardTransformationMatrix(state);

                // Apply transformation
                return matrix.Transform(imagePoint);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying forward transformations: {ex.Message}", "CoordinateMapper");
                return imagePoint;
            }
        }

        /// <summary>
        /// Creates the inverse transformation matrix for backend to image coordinate mapping
        /// </summary>
        private Matrix CreateInverseTransformationMatrix(BackendTransformState state)
        {
            try
            {
                var matrix = Matrix.Identity;

                // Calculate backend container center
                var backendCenter = new Point(
                    _synchronizer.BackendContainer.BackendSize.Width / 2,
                    _synchronizer.BackendContainer.BackendSize.Height / 2
                );

                // Apply transformations in reverse order (inverse)
                
                // 1. Translate from backend center to origin
                matrix.Translate(-backendCenter.X, -backendCenter.Y);

                // 2. Apply drag offset (inverse)
                matrix.Translate(-state.DragOffset.X, -state.DragOffset.Y);

                // 3. Apply rotation (inverse)
                if (Math.Abs(state.RotationAngle) > FloatingPointTolerance)
                {
                    matrix.Rotate(-state.RotationAngle);
                }

                // 4. Apply zoom scale (inverse)
                if (Math.Abs(state.ZoomScale) > FloatingPointTolerance)
                {
                    matrix.Scale(1.0 / state.ZoomScale, 1.0 / state.ZoomScale);
                }

                // 5. Translate to image center
                var imageCenter = new Point(
                    _synchronizer.OriginalImageSize.Width / 2,
                    _synchronizer.OriginalImageSize.Height / 2
                );
                matrix.Translate(imageCenter.X, imageCenter.Y);

                return matrix;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating inverse transformation matrix: {ex.Message}", "CoordinateMapper");
                return Matrix.Identity;
            }
        }

        /// <summary>
        /// Creates the inverse transformation matrix WITHOUT rotation for backend to image coordinate mapping.
        /// Used specifically for crop rectangle mapping to maintain coordinate system separation.
        /// </summary>
        private Matrix CreateInverseTransformationMatrixWithoutRotation(BackendTransformState state)
        {
            try
            {
                var matrix = Matrix.Identity;

                // Calculate backend container center
                var backendCenter = new Point(
                    _synchronizer.BackendContainer.BackendSize.Width / 2,
                    _synchronizer.BackendContainer.BackendSize.Height / 2
                );

                // Apply transformations in reverse order (inverse) WITHOUT rotation

                // 1. Translate from backend center to origin
                matrix.Translate(-backendCenter.X, -backendCenter.Y);

                // 2. Apply drag offset (inverse)
                matrix.Translate(-state.DragOffset.X, -state.DragOffset.Y);

                // 3. SKIP ROTATION - This is the key difference for crop rectangle mapping

                // 4. Apply zoom scale (inverse)
                if (Math.Abs(state.ZoomScale) > FloatingPointTolerance)
                {
                    matrix.Scale(1.0 / state.ZoomScale, 1.0 / state.ZoomScale);
                }

                // 5. Translate to image center
                var imageCenter = new Point(
                    _synchronizer.OriginalImageSize.Width / 2,
                    _synchronizer.OriginalImageSize.Height / 2
                );
                matrix.Translate(imageCenter.X, imageCenter.Y);

                return matrix;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating inverse transformation matrix without rotation: {ex.Message}", "CoordinateMapper");
                return Matrix.Identity;
            }
        }

        /// <summary>
        /// Creates the forward transformation matrix for image to backend coordinate mapping
        /// </summary>
        private Matrix CreateForwardTransformationMatrix(BackendTransformState state)
        {
            try
            {
                var matrix = Matrix.Identity;

                // Calculate centers
                var imageCenter = new Point(
                    _synchronizer.OriginalImageSize.Width / 2,
                    _synchronizer.OriginalImageSize.Height / 2
                );
                var backendCenter = new Point(
                    _synchronizer.BackendContainer.BackendSize.Width / 2,
                    _synchronizer.BackendContainer.BackendSize.Height / 2
                );

                // Apply transformations in forward order
                
                // 1. Translate from image center to origin
                matrix.Translate(-imageCenter.X, -imageCenter.Y);

                // 2. Apply zoom scale
                matrix.Scale(state.ZoomScale, state.ZoomScale);

                // 3. Apply rotation
                if (Math.Abs(state.RotationAngle) > FloatingPointTolerance)
                {
                    matrix.Rotate(state.RotationAngle);
                }

                // 4. Apply drag offset
                matrix.Translate(state.DragOffset.X, state.DragOffset.Y);

                // 5. Translate to backend center
                matrix.Translate(backendCenter.X, backendCenter.Y);

                return matrix;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating forward transformation matrix: {ex.Message}", "CoordinateMapper");
                return Matrix.Identity;
            }
        }

        #endregion
    }
}
