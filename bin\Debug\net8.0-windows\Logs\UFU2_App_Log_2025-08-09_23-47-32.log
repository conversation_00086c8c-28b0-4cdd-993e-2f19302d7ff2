=== UFU2 Application Session Started at 2025-08-09 23:47:32 ===
[2025-08-09 23:47:32.947]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 23:47:32.962]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 23:47:32.966]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 23:47:32.972]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 23:47:32.990]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 23:47:32.994]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 23:47:32.998]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 23:47:33.008]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 23:47:33.014]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 23:47:33.025]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 23:47:33.035]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 23:47:33.048]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 23:47:33.058]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 23:47:33.101]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 23:47:33.109]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 23:47:33.114]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 23:47:33.118]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 23:47:33.125]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 23:47:33.139]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 23:47:33.143]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 23:47:33.156]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 23:47:33.164]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 23:47:33.168]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 23:47:33.179]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 23:47:33.186]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 23:47:33.198]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 23:47:33.205]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 23:47:33.230]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 23:47:33.243]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 23:47:33.261]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 23:47:33.277]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 23:47:33.301]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 23:47:33.341]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 23:47:33.359]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 23:47:33.397]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 23:47:33.413]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 23:47:33.429]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 23:47:33.439]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 23:47:33.473]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 104.77MB working set
[2025-08-09 23:47:33.478]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 23:47:33.485]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 23:47:33.495]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 23:47:33.551]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 23:47:33.592]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 23:47:33.609]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 23:47:33.701]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 23:47:33.728]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 23:47:33.745]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 23:47:34.055]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 23:47:34.059]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 23:47:34.063]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 23:47:34.067]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 23:47:34.078]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638903764540758006 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 23:47:34.083]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 23:47:34.092]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:47:34.097]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 23:47:34.112]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 23:47:34.240]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 23:47:34.452]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 23:47:34.485]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 23:47:34.495]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 23:47:34.511]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 23:47:34.519]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 23:47:34.530]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 23:47:34.536]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 23:47:34.546]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 23:47:34.552]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 23:47:34.558]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 23:47:34.562]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 23:47:34.566]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 23:47:34.572]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 23:47:34.577]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 23:47:34.581]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 23:47:34.588]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 23:47:34.593]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 23:47:34.600]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 23:47:34.659]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 23:47:34.690]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 23:47:34.698]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 23:47:34.703]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 23:47:34.709]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 23:47:34.713]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 23:47:34.717]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 23:47:34.723]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 23:47:34.728]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 23:47:34.732]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 23:47:34.739]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 23:47:34.743]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 23:47:34.747]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 23:47:34.752]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 23:47:34.758]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 23:47:34.763]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 23:47:34.767]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 23:47:34.773]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 23:47:34.777]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 23:47:34.822]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 23:47:34.850]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 23:47:34.871]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 23:47:34.886]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 23:47:34.896]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 23:47:34.900]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 23:47:34.907]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 23:47:34.911]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 23:47:34.915]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 23:47:34.923]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 23:47:34.933]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 23:47:34.940]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 23:47:35.008]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 23:47:35.017]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 23:47:35.023]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 23:47:35.029]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 23:47:35.034]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 23:47:35.039]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 23:47:35.044]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 23:47:35.056]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 23:47:35.062]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 23:47:35.117]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 23:47:35.135]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 1527ms
[2025-08-09 23:47:35.141]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 23:47:35.148]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 23:47:35.167]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 23:47:35.179]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 23:47:35.184]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 23:47:35.193]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 23:47:35.214]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 23:47:35.225]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 23:47:35.234]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 23:47:35.241]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 23:47:35.247]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 23:47:35.253]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:47:35.253]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:47:35.254]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:47:35.257]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 23:47:35.262]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:47:35.270]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:47:35.295]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 23:47:35.281]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 23:47:35.290]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 23:47:35.277]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 23:47:35.303]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 23:47:35.312]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 23:47:35.316]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 23:47:35.326]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 23:47:35.331]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:35.338]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 23:47:35.342]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 23:47:35.345]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 23:47:35.349]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 23:47:35.355]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 23:47:35.359]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 23:47:35.363]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 23:47:35.367]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 23:47:35.379]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 23:47:35.383]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 23:47:35.390]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 23:47:35.394]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:35.399]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 23:47:35.427]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 23:47:35.432]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 23:47:35.438]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 23:47:35.442]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 23:47:35.448]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 23:47:35.452]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 23:47:35.458]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 23:47:35.462]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 23:47:35.467]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 23:47:35.473]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 23:47:35.478]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 23:47:35.482]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 23:47:35.489]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 23:47:35.493]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 23:47:35.498]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 23:47:35.503]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 23:47:35.512]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 23:47:35.519]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 23:47:35.528]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 23:47:35.595]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 23:47:35.633]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 23:47:35.641]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 23:47:35.648]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 23:47:35.659]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 23:47:35.667]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 23:47:35.677]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 23:47:35.695]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 23:47:35.714]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 23:47:35.812]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:35.842]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 23:47:35.850]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 23:47:35.862]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:35.867]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 23:47:35.875]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 23:47:35.912]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 23:47:35.926]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 23:47:35.932]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 23:47:35.939]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 23:47:35.984]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 23:47:35.999]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 23:47:36.007]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 23:47:36.011]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 23:47:36.015]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 23:47:36.019]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:36.025]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 23:47:36.030]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 23:47:36.035]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 23:47:36.041]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 23:47:36.046]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 23:47:36.050]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 23:47:36.146]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 23:47:36.174]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 23:47:36.179]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 23:47:36.183]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 23:47:36.190]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 23:47:36.196]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 23:47:36.203]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 23:47:36.209]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 23:47:36.215]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 23:47:36.223]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 23:47:36.227]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 23:47:36.235]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 23:47:36.244]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 23:47:36.249]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:36.255]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 23:47:36.260]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 23:47:36.264]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:36.268]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 23:47:36.274]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 23:47:36.280]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 23:47:36.286]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 23:47:36.293]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 23:47:36.297]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 23:47:36.302]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 23:47:36.308]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 23:47:36.314]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 23:47:36.319]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 23:47:36.328]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 23:47:36.333]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:36.339]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 23:47:36.344]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 23:47:36.348]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 23:47:36.352]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 23:47:36.358]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 23:47:36.363]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 23:47:36.368]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 23:47:36.373]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 23:47:36.378]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 23:47:36.383]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 23:47:36.389]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 23:47:36.393]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 23:47:36.398]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 23:47:36.403]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 23:47:36.409]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 23:47:36.413]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 23:47:36.417]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 23:47:36.422]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:36.426]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 23:47:36.430]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:36.434]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 23:47:36.440]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 23:47:36.444]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 23:47:36.449]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 23:47:36.457]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 23:47:36.462]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 23:47:36.467]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 23:47:36.475]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 23:47:36.480]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 23:47:36.485]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 23:47:36.493]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 23:47:36.499]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 23:47:36.509]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 23:47:36.558]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 23:47:36.576]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 23:47:36.596]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 23:47:36.609]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 23:47:36.617]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 23:47:36.625]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 23:47:36.632]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 23:47:36.640]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 23:47:36.648]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 23:47:36.656]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 23:47:36.663]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 23:47:36.668]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 23:47:36.680]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 23:47:36.689]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 23:47:36.699]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 23:47:36.709]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 23:47:36.718]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 23:47:36.738]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 23:47:36.757]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 23:47:36.765]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 23:47:36.774]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 23:47:36.780]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 23:47:36.785]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 23:47:36.792]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 23:47:36.798]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 23:47:36.805]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 23:47:36.810]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 23:47:36.816]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 23:47:36.822]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 23:47:36.826]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 23:47:36.831]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 23:47:36.836]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 23:47:36.843]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 23:47:36.856]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 23:47:36.864]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 23:47:36.906]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 23:47:36.939]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 23:47:36.946]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:36.951]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 23:47:36.959]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 23:47:36.966]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 23:47:36.974]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 23:47:36.980]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 23:47:36.985]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 23:47:36.991]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 23:47:36.996]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 23:47:37.001]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 23:47:37.007]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 23:47:37.012]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 23:47:37.016]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 23:47:37.022]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 23:47:37.027]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:37.032]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 23:47:37.038]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:37.043]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:37.049]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:37.055]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 23:47:37.060]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:37.068]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:37.076]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 23:47:37.084]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 23:47:37.177]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 23:47:37.184]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:37.205]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 23:47:37.210]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 23:47:37.217]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:37.235]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 23:47:37.241]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 23:47:37.245]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 23:47:37.250]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 23:47:37.258]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 23:47:37.267]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 23:47:37.473]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 23:47:37.566]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 23:47:37.594]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 23:47:37.618]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 23:47:37.723]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 23:47:37.734]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 23:47:37.742]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 23:47:37.750]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 23:47:37.761]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 23:47:37.767]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:37.777]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-09 23:47:37.793]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-09 23:47:37.798]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 23:47:37.811]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 23:47:37.824]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 23:47:37.829]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 23:47:37.834]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 23:47:37.841]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 23:47:37.846]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 23:47:37.852]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 23:47:37.893]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 23:47:37.928]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 23:47:37.969]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 23:47:38.069]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 23:47:38.073]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 23:47:38.083]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 23:47:38.097]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 23:47:38.092]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 23:47:38.172]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 23:47:38.178]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 23:47:38.182]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 23:47:38.179]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.192]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 23:47:38.204]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 23:47:38.214]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.226]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 2)
[2025-08-09 23:47:38.226]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 2)
[2025-08-09 23:47:38.236]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.231]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.260]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 23:47:38.260]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.324]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 148ms
[2025-08-09 23:47:38.338]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 23:47:38.358]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.366]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.375]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 23:47:38.384]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.392]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.397]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 23:47:38.402]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.408]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.413]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 23:47:38.417]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.423]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.428]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 23:47:38.432]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.438]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.443]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 23:47:38.448]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.455]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-09 23:47:38.465]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 261ms
[2025-08-09 23:47:38.476]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 23:47:38.508]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 23:47:38.523]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 49ms
[2025-08-09 23:47:38.536]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 23:47:38.559]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 23:47:38.591]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 55ms
[2025-08-09 23:47:38.600]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 23:47:38.610]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:38.623]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 23:47:38.634]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:38.651]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 23:47:38.674]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:38.685]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 23:47:38.694]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 23:47:38.699]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 23:47:38.706]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 23:47:38.712]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 113ms
[2025-08-09 23:47:38.718]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 23:47:38.727]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.739]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 23:47:38.744]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.750]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.757]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 23:47:38.761]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.766]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.772]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 23:47:38.776]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.781]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.786]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 23:47:38.792]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.797]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.802]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 23:47:38.807]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.811]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.816]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 23:47:38.823]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:38.835]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:38.844]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 23:47:39.092]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:39.264]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:39.324]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 23:47:39.340]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:39.345]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:39.350]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 23:47:39.356]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:39.360]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 23:47:39.365]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 23:47:39.371]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 23:47:39.375]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 23:47:39.380]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 663ms
[2025-08-09 23:47:39.386]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 23:47:39.392]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 23:47:39.396]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 23:47:39.400]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 23:47:39.406]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 23:47:39.410]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 23:47:39.414]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 23:47:39.418]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 23:47:39.423]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 23:47:39.427]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 23:47:39.432]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 23:47:39.436]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 23:47:39.441]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 23:47:39.446]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 23:47:39.450]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 23:47:39.456]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 23:47:39.461]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 23:47:39.468]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 23:47:39.564]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 23:47:39.568]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 23:47:39.574]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 23:47:39.577]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 23:47:39.585]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 23:47:39.591]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:39.596]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 23:47:39.600]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:39.607]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 23:47:39.611]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:47:39.756]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 23:47:39.761]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 23:47:40.004]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 23:47:40.013]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:40.017]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:40.025]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:40.029]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:40.034]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:40.041]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:47:40.710]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 23:47:42.033]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.040]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:42.044]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.048]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:42.052]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.058]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:47:42.097]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.100]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:47:42.106]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.110]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:47:42.114]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-09 23:47:42.118]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.124]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-09 23:47:42.129]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-09 23:47:42.134]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-09 23:47:42.139]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 23:47:42.144]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_25265097_638903764621446882 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-09 23:47:42.148]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-09 23:47:42.152]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 23:47:42.158]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 23:47:42.162]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 23:47:42.167]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-09 23:47:42.183]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 23:47:42.189]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 23:47:42.194]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 23:47:42.275]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 23:47:42.299]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 23:47:42.389]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.393]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:42.397]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.401]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:42.407]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.411]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:47:42.940]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.945]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:42.949]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.953]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 23:47:42.958]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 23:47:42.962]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 23:47:43.012]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-09 23:47:43.017]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 23:47:43.025]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 23:47:43.034]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 23:47:43.048]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:47:43.053]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 23:47:43.060]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 23:47:43.064]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:47:43.068]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 23:47:43.073]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 23:47:43.078]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_25265097_638903764621446882
[2025-08-09 23:47:43.082]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:47:43.086]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 23:47:43.091]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 23:47:43.095]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:47:43.099]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 23:47:43.103]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 23:47:43.108]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 23:47:43.122]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-09 23:47:43.126]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-09 23:47:43.130]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 23:47:43.134]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-09 23:47:43.140]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 23:47:43.143]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 23:47:43.147]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:47:43.151]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 23:47:43.156]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 23:47:43.159]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:47:43.163]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 23:47:43.167]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 23:47:43.172]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_28090709_638903764540758006
[2025-08-09 23:47:43.176]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 23:47:43.180]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 23:47:43.184]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 23:47:43.188]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 23:47:43.192]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 23:47:43.196]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 23:47:43.200]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-09 23:47:43.205]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-09 23:47:43.209]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-09 23:47:43.213]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-09 23:47:43.218]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 23:47:43.223]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 23:47:43.232]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-09 23:47:43.237]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 23:47:43.242]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 23:47:43.246]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 23:47:43.266]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 23:47:43.278]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-09 23:47:43.290]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 23:47:43.296]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-09 23:47:43.300]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-09 23:47:43.308]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-09 23:47:43.313]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 23:47:43.317]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 23:47:43.323]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 23:47:43.328]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 23:47:43.332]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 23:47:43.336]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-09 23:47:43.342]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 23:47:43.346]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-09 23:47:43.350]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 23:47:43.357]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-09 23:47:43.361]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-09 23:47:43.365]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-09 23:47:43.369]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-09 23:47:43.376]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-09 23:47:43.380]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-09 23:47:43.384]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-09 23:47:43.391]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 0.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-09 23:47:43.395]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-09 23:47:43.399]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-09 23:47:43.406]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-09 23:47:43.411]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-09 23:47:43.415]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-09 23:47:43.419]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 23:47:43.424]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 23:47:43.428]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 23:47:43.432]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-09 23:47:43.438]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-09 23:47:43.442]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-09 23:47:43.446]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-09 23:47:43.451]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-09 23:47:43.456]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-09 23:47:43.460]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-09 23:47:43.464]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-09 23:47:43.468]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 23:47:43.476]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 23:47:43.496]  	[INFO]		[ResourceManager]	Generated memory leak report: 0 alive resources, 0 dead resources
[2025-08-09 23:47:43.510]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 1
[2025-08-09 23:47:43.514]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 1 potential leaks detected
[2025-08-09 23:47:43.518]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-09 23:47:43.524]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-09 23:47:43.529]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 23:47:43.534]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 23:47:43.539]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-09 23:47:43.543]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-09 23:47:43.548]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 23:47:43.555]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 23:47:43.559]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 2 tracked, 2 disposed, 1 cleanups
[2025-08-09 23:47:43.563]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-09 23:47:43.567]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 23:47:43.572]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-09 23:47:43.576]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 23:47:43.580]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-09 23:47:43.584]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 23:47:43.588]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-09 23:47:43.593]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 23:47:43.597]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-09 23:47:43.601]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-09 23:47:43.606]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-09 23:47:43.612]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-09 23:47:43.619]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-09 23:47:43.631]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-09 23:47:43.635]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 23:47:43.644]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-09 23:47:43.650]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-09 23:47:43.658]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-09 23:47:43.663]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-09 23:47:43.668]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 23:47:43 ===
