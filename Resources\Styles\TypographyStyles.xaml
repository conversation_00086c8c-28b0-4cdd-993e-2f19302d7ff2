﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!--
        ========================================
        UFU2_0 Centralized Typography Token System
        ========================================
    -->

    <!--
        This ResourceDictionary contains all typography tokens for the UFU2_0 application.
        Typography tokens follow Material Design 3 typography scale and support
        both Arabic and Latin text with appropriate font families.
    -->

    <!--
        ========================================
        FONT FAMILIES
        ========================================
    -->

    <!--  Primary Font Families  -->
    <FontFamily x:Key="PrimaryFontFamily">Calibri</FontFamily>
    <FontFamily x:Key="SecondaryFontFamily">Arial Black</FontFamily>
    <FontFamily x:Key="TertiaryFontFamily">{DynamicResource MaterialDesignFont}</FontFamily>


    <!--  Arabic Font Support  -->
    <FontFamily x:Key="ArabicFontFamily">pack://application:,,,/Resources/Font/#Noto Sans Arabic</FontFamily>
    <FontFamily x:Key="ArabicFontFamilyMedium">pack://application:,,,/Resources/Font/#Noto Sans Arabic Medium</FontFamily>
    <FontFamily x:Key="ArabicFontFamilySemiBold">pack://application:,,,/Resources/Font/#Noto Sans Arabic SemiBold</FontFamily>
    <FontFamily x:Key="ArabicFontFamilyBold">pack://application:,,,/Resources/Font/#Noto Sans Arabic Bold</FontFamily>

    <!--  Display Font Families  -->
    <FontFamily x:Key="DisplayFontFamily">Raleway</FontFamily>
    <FontFamily x:Key="HeadlineFontFamily">Open Sans</FontFamily>
    <FontFamily x:Key="NumericFontFamily">Saira</FontFamily>

    <!--
        ========================================
        FONT SIZES - MATERIAL DESIGN 3 SCALE
        ========================================
    -->

    <!--  Display Typography Scale  -->
    <system:Double x:Key="DisplayLargeFontSize">57</system:Double>
    <system:Double x:Key="DisplayMediumFontSize">45</system:Double>
    <system:Double x:Key="DisplaySmallFontSize">36</system:Double>

    <!--  Headline Typography Scale  -->
    <system:Double x:Key="HeadlineLargeFontSize">28</system:Double>
    <system:Double x:Key="HeadlineMediumFontSize">24</system:Double>
    <system:Double x:Key="HeadlineSmallFontSize">20</system:Double>

    <!--  SubTitle Typography Scale  -->
    <system:Double x:Key="SubTitleLargeFontSize">18</system:Double>
    <system:Double x:Key="SubTitleMediumFontSize">16</system:Double>
    <system:Double x:Key="SubTitleSmallFontSize">15</system:Double>

    <!--  Body Typography Scale  -->
    <system:Double x:Key="BodyLargeFontSize">16</system:Double>
    <system:Double x:Key="BodyMediumFontSize">14</system:Double>
    <system:Double x:Key="BodySmallFontSize">13</system:Double>

    <!--  Label Typography Scale  -->
    <system:Double x:Key="LabelLargeFontSize">14</system:Double>
    <system:Double x:Key="LabelMediumFontSize">12</system:Double>
    <system:Double x:Key="LabelSmallFontSize">11</system:Double>

    <!--  Caption Typography Scale  -->
    <system:Double x:Key="CaptionLargeFontSize">12</system:Double>
    <system:Double x:Key="CaptionMediumFontSize">10</system:Double>
    <system:Double x:Key="CaptionSmallFontSize">9</system:Double>

    <!--
        ========================================
        FONT WEIGHTS
        ========================================
    -->

    <!--  Standard Font Weights  -->
    <FontWeight x:Key="FontWeightLight">Light</FontWeight>
    <FontWeight x:Key="FontWeightRegular">Normal</FontWeight>
    <FontWeight x:Key="FontWeightMedium">Medium</FontWeight>
    <FontWeight x:Key="FontWeightSemiBold">SemiBold</FontWeight>
    <FontWeight x:Key="FontWeightBold">Bold</FontWeight>
    <FontWeight x:Key="FontWeightExtraBold">ExtraBold</FontWeight>
    <FontWeight x:Key="FontWeightBlack">Black</FontWeight>

    <!--
        ========================================
        LINE HEIGHTS
        ========================================
    -->

    <!--  Line Height Standards for Readability  -->
    <system:Double x:Key="LineHeightTight">1.2</system:Double>
    <system:Double x:Key="LineHeightNormal">1.4</system:Double>
    <system:Double x:Key="LineHeightRelaxed">1.6</system:Double>
    <system:Double x:Key="LineHeightLoose">1.8</system:Double>

    <!--
        ========================================
        LEGACY FONT SIZES (for backward compatibility)
        ========================================
    -->

    <!--  Common Legacy Font Sizes  -->
    <system:Double x:Key="FontSize10">10</system:Double>
    <system:Double x:Key="FontSize11">11</system:Double>
    <system:Double x:Key="FontSize12">12</system:Double>
    <system:Double x:Key="FontSize13">13</system:Double>
    <system:Double x:Key="FontSize14">14</system:Double>
    <system:Double x:Key="FontSize16">16</system:Double>
    <system:Double x:Key="FontSize18">18</system:Double>
    <system:Double x:Key="FontSize20">20</system:Double>
    <system:Double x:Key="FontSize22">22</system:Double>
    <system:Double x:Key="FontSize24">24</system:Double>
    <system:Double x:Key="FontSize26">26</system:Double>
    <system:Double x:Key="FontSize28">28</system:Double>
    <system:Double x:Key="FontSize32">32</system:Double>
    <system:Double x:Key="FontSize36">36</system:Double>
    <system:Double x:Key="FontSize44">44</system:Double>

    <!--
        ========================================
        TEXT BLOCK STYLES
        ========================================
    -->

    <!--  Headline Text Block FontSize 20  -->
    <Style x:Key="HeadlineStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{DynamicResource HeadlineSmallFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBold}" />
        <Setter Property="LineHeight" Value="{DynamicResource LineHeightNormal}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
    </Style>

    <!--  Subtitle Text Block FontSize 18  -->
    <Style x:Key="SubtitleStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource SecondaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightMedium}" />
        <Setter Property="LineHeight" Value="{DynamicResource LineHeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource SubTitleLargeFontSize}" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
    </Style>

    <!--  Body Text Block FontSize 14  -->
    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBlack}" />
        <Setter Property="LineHeight" Value="{DynamicResource LineHeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource BodyMediumFontSize}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
    </Style>

    <!--  Label Text Block FontSize 12  -->
    <Style x:Key="LabelTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBlack}" />
        <Setter Property="LineHeight" Value="{DynamicResource LineHeightRelaxed}" />
        <Setter Property="FontSize" Value="{StaticResource LabelMediumFontSize}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Margin" Value="0,3" />
        <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
    </Style>

    <!--  Caption Text Block FontSize 10  -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightLight}" />
        <Setter Property="LineHeight" Value="{DynamicResource LineHeightLoose}" />
        <Setter Property="FontSize" Value="{StaticResource CaptionMediumFontSize}" />
        <Setter Property="TextAlignment" Value="Justify" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
    </Style>

    <!--  Toast Text Block  -->
    <Style x:Key="ToastTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBlack}" />
        <Setter Property="LineHeight" Value="{DynamicResource LineHeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource LabelMediumFontSize}" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="Margin" Value="0,8,0,0" />
        <Setter Property="MaxHeight" Value="120" />
        <Setter Property="TextAlignment" Value="Left" />
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
        <Style.Triggers>
            <DataTrigger Binding="{Binding ElementName=MessageText, Path=Text.Length}" Value="0">
                <Setter Property="Visibility" Value="Collapsed" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!--  Toast SubTitle Text Block  -->
    <Style x:Key="TostSubTitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBlack}" />
        <Setter Property="LineHeight" Value="{DynamicResource LineHeightNormal}" />
        <Setter Property="FontSize" Value="{StaticResource SubTitleSmallFontSize}" />
        <Setter Property="TextAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
    </Style>

    <Style BasedOn="{StaticResource {x:Type ToolTip}}" TargetType="ToolTip">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="Background" Value="{DynamicResource ToolTipsBackgroud}" />
        <Setter Property="Foreground" Value="{DynamicResource ToolTipsForegroud}" />
        <Setter Property="ToolTipService.Placement" Value="Mouse" />
    </Style>

</ResourceDictionary>