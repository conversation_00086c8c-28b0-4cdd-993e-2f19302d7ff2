<Window
    x:Class="UFU2.Windows.ConfirmationWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="{Binding WindowTitle}"
    MinWidth="50"
    MinHeight="50"
    MaxWidth="300"
    MaxHeight="200"
    HorizontalAlignment="Stretch"
    VerticalAlignment="Stretch"
    AllowsTransparency="True"
    AutomationProperties.HelpText="Confirmation dialog window"
    AutomationProperties.Name="{Binding WindowTitle}"
    Background="Transparent"
    FlowDirection="{Binding FlowDirection}"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    WindowStartupLocation="CenterOwner"
    WindowStyle="None"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <!--  Built-in WPF BooleanToVisibilityConverter  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!--  Custom StringToVisibilityConverter  -->
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />

        </ResourceDictionary>
    </Window.Resources>

    <Window.InputBindings>
        <!--  Keyboard shortcuts  -->
        <KeyBinding Key="Enter" Command="{Binding PrimaryCommand}" />
        <KeyBinding Key="Escape" Command="{Binding SecondaryCommand}" />
        <KeyBinding
            Key="Y"
            Command="{Binding PrimaryCommand}"
            Modifiers="Alt" />
        <KeyBinding
            Key="N"
            Command="{Binding SecondaryCommand}"
            Modifiers="Alt" />
    </Window.InputBindings>

    <Border
        Width="300"
        Height="200"
        Background="{DynamicResource ConfirmWindowBackgroundBase}"
        CornerRadius="8">

        <materialDesign:Card AutomationProperties.Name="Confirmation dialog content" Style="{StaticResource ConfirmationWindowCard}">

            <Grid>
                <Grid.RowDefinitions>
                    <!--  Header section  -->
                    <RowDefinition Height="Auto" />
                    <!--  Content section  -->
                    <RowDefinition Height="*" />
                    <!--  Button section  -->
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Header Section  -->
                <materialDesign:Card
                    Grid.Row="0"
                    Style="{DynamicResource HeaderCardStyle}"
                    UniformCornerRadius="0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <!--  Title  -->
                        <TextBlock
                            Grid.Column="0"
                            Margin="0"
                            VerticalAlignment="Center"
                            AutomationProperties.AutomationId="ConfirmationTitle"
                            AutomationProperties.Name="Dialog title"
                            Style="{DynamicResource HeadlineStyle}"
                            Text="{Binding Title}"
                            TextWrapping="Wrap" />

                        <!--  Icon  -->
                        <materialDesign:PackIcon
                            Grid.Column="1"
                            Width="24"
                            Height="24"
                            VerticalAlignment="Center"
                            AutomationProperties.Name="Confirmation icon"
                            Foreground="{DynamicResource ConfirmWindowIconColor}"
                            Kind="{Binding IconKind}"
                            Visibility="{Binding ShowIcon, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    </Grid>
                </materialDesign:Card>

                <!--  Content Section  -->
                <ScrollViewer
                    Grid.Row="1"
                    Padding="6,5"
                    HorizontalScrollBarVisibility="Disabled"
                    VerticalScrollBarVisibility="Auto">
                    <TextBlock
                        HorizontalAlignment="Center"
                        AutomationProperties.AutomationId="ConfirmationContent"
                        AutomationProperties.Name="Dialog content"
                        Style="{DynamicResource BodyTextStyle}"
                        Text="{Binding Content}"
                        TextWrapping="Wrap" />
                </ScrollViewer>

                <!--  Button Section  -->
                <Border
                    Grid.Row="2"
                    Padding="7,12"
                    Background="{DynamicResource ConfirmWindowBackground}"
                    BorderBrush="{DynamicResource ConfirmWindowBackgroundBase}"
                    BorderThickness="0,1,0,0"
                    CornerRadius="0,0,7,7">
                    <StackPanel
                        HorizontalAlignment="Right"
                        FlowDirection="{Binding ButtonFlowDirection}"
                        Orientation="Horizontal">

                        <!--  Primary Button (OK/Yes)  -->
                        <Button
                            Margin="0,0,12,0"
                            AutomationProperties.AcceleratorKey="Enter"
                            AutomationProperties.AutomationId="PrimaryButton"
                            AutomationProperties.Name="{Binding PrimaryButtonText}"
                            Command="{Binding PrimaryCommand}"
                            Content="{Binding PrimaryButtonText}"
                            IsDefault="True"
                            Style="{StaticResource PrimaryButtonStyle}" />

                        <!--  Secondary Button (Cancel/No)  -->
                        <Button
                            AutomationProperties.AcceleratorKey="Escape"
                            AutomationProperties.AutomationId="SecondaryButton"
                            AutomationProperties.Name="{Binding SecondaryButtonText}"
                            Command="{Binding SecondaryCommand}"
                            Content="{Binding SecondaryButtonText}"
                            IsCancel="True"
                            Style="{StaticResource SecondaryButtonStyle}" />
                    </StackPanel>
                </Border>
            </Grid>
        </materialDesign:Card>
    </Border>
</Window>
