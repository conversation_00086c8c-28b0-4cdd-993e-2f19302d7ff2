using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Caching.Memory;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Service for managing CPI location data (Wilayas and Dairas) from official Algerian Ministry of Finance sources.
    /// Provides caching, search functionality, and automatic data seeding from embedded JSON resources.
    /// </summary>
    public class CpiLocationService : ICacheableService, IDisposable
    {
        private readonly DatabaseService _databaseService;
        private IMemoryCache _wilayaCache;
        private IMemoryCache _dairaCache;
        private IMemoryCache _searchCache;

        // Cache statistics for monitoring
        private int _wilayaCacheHits = 0;
        private int _wilayaCacheMisses = 0;
        private int _dairaCacheHits = 0;
        private int _dairaCacheMisses = 0;
        private int _searchCacheHits = 0;
        private int _searchCacheMisses = 0;

        /// <summary>
        /// Gets the service name for cache management identification.
        /// </summary>
        public string ServiceName => "CpiLocationService";

        /// <summary>
        /// Initializes a new instance of the CpiLocationService class.
        /// </summary>
        /// <param name="databaseService">Database service instance (optional - will use reference database from ServiceLocator if null)</param>
        public CpiLocationService(DatabaseService databaseService = null)
        {
            // Use reference database from ServiceLocator if no specific database service provided
            _databaseService = databaseService ?? ServiceLocator.GetService<DatabaseService>("ReferenceDatabase");
            
            if (_databaseService == null)
            {
                throw new InvalidOperationException("Reference database service not found. Ensure ServiceLocator is properly initialized with reference database.");
            }

            // Initialize caches
            _wilayaCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 100,
                CompactionPercentage = 0.25
            });

            _dairaCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 500,
                CompactionPercentage = 0.25
            });

            _searchCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 200,
                CompactionPercentage = 0.25
            });

            LoggingService.LogDebug("CpiLocationService initialized with reference database", "CpiLocationService");
        }

        /// <summary>
        /// Seeds CPI location data from embedded JSON resource.
        /// </summary>
        /// <returns>Seeding result with counts and status</returns>
        public async Task<CpiLocationSeedingResult> SeedCpiLocationDataAsync()
        {
            var result = new CpiLocationSeedingResult();

            try
            {
                LoggingService.LogInfo("Starting CPI location data seeding from embedded JSON resource", "CpiLocationService");

                // Read JSON content from embedded resource
                string jsonContent = ReadEmbeddedJsonResource("UFU2.Database.cpi_Location.json");

                if (string.IsNullOrEmpty(jsonContent))
                {
                    result.Message = "Embedded JSON resource is empty or not found";
                    LoggingService.LogWarning(result.Message, "CpiLocationService");
                    return result;
                }

                // Deserialize JSON data
                var locationData = JsonSerializer.Deserialize<CpiLocationJsonData>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (locationData?.Wilayas == null || locationData?.Dairas == null)
                {
                    result.Message = "Invalid JSON structure - missing wilayas or dairas data";
                    LoggingService.LogError(result.Message, "CpiLocationService");
                    return result;
                }

                // Validate data before seeding
                if (locationData.Wilayas == null || locationData.Wilayas.Length == 0)
                {
                    result.Message = "No wilaya data found in JSON resource";
                    LoggingService.LogWarning(result.Message, "CpiLocationService");
                    return result;
                }

                if (locationData.Dairas == null || locationData.Dairas.Length == 0)
                {
                    result.Message = "No daira data found in JSON resource";
                    LoggingService.LogWarning(result.Message, "CpiLocationService");
                    return result;
                }

                LoggingService.LogInfo($"Starting to seed {locationData.Wilayas.Length} wilayas and {locationData.Dairas.Length} dairas", "CpiLocationService");

                // Seed wilayas first (required for foreign key relationships)
                result.WilayasCount = await SeedWilayasAsync(locationData.Wilayas);
                LoggingService.LogInfo($"Successfully seeded {result.WilayasCount} wilayas", "CpiLocationService");

                // Seed dairas (depends on wilayas)
                result.DairasCount = await SeedDairasAsync(locationData.Dairas);
                LoggingService.LogInfo($"Successfully seeded {result.DairasCount} dairas", "CpiLocationService");

                result.IsSuccess = true;
                result.Message = $"تم تحميل البيانات الجغرافية بنجاح. الولايات: {result.WilayasCount}، الدوائر: {result.DairasCount}";

                // Clear caches after seeding
                ClearAllCaches();

                LoggingService.LogInfo("CPI location data seeding completed successfully", "CpiLocationService");
                ErrorManager.ShowUserSuccessToast(result.Message, "تم التحميل بنجاح", "CpiLocationService");

                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;

                // Provide more specific error messages based on exception type
                if (ex.Message.Contains("FOREIGN KEY constraint failed"))
                {
                    result.Message = "فشل في تحميل البيانات الجغرافية: خطأ في العلاقات بين البيانات";
                    LoggingService.LogError($"Foreign key constraint violation during CPI location seeding: {ex.Message}", "CpiLocationService");
                }
                else if (ex.Message.Contains("no such table"))
                {
                    result.Message = "فشل في تحميل البيانات الجغرافية: جداول قاعدة البيانات غير موجودة";
                    LoggingService.LogError($"Database table missing during CPI location seeding: {ex.Message}", "CpiLocationService");
                }
                else
                {
                    result.Message = $"فشل في تحميل البيانات الجغرافية: {ex.Message}";
                    LoggingService.LogError($"CPI location data seeding failed: {ex.Message}", "CpiLocationService");
                }

                ErrorManager.HandleErrorToast(ex, "فشل في تحميل البيانات الجغرافية", "خطأ في التحميل",
                                       LogLevel.Error, "CpiLocationService");
                
                return result;
            }
        }

        /// <summary>
        /// Gets all wilayas from the database with caching.
        /// </summary>
        /// <returns>List of all wilayas</returns>
        public async Task<List<CpiWilaya>> GetWilayasAsync()
        {
            const string cacheKey = "all_wilayas";

            if (_wilayaCache.TryGetValue(cacheKey, out List<CpiWilaya> cachedWilayas))
            {
                _wilayaCacheHits++;
                LoggingService.LogDebug("Retrieved wilayas from cache", "CpiLocationService");
                return cachedWilayas;
            }

            _wilayaCacheMisses++;

            try
            {
                const string selectSql = @"
                    SELECT Code, NameAr, NameFr, DisplayValue 
                    FROM CpiWilayas 
                    ORDER BY Code";

                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var wilayas = await connection.QueryAsync<CpiWilaya>(selectSql);
                var wilayaList = wilayas.ToList();

                // Cache for 30 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                    Size = 1
                };

                _wilayaCache.Set(cacheKey, wilayaList, cacheOptions);
                LoggingService.LogDebug($"Retrieved and cached {wilayaList.Count} wilayas", "CpiLocationService");

                return wilayaList;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error retrieving wilayas: {ex.Message}", "CpiLocationService");
                throw;
            }
        }

        /// <summary>
        /// Gets dairas for a specific wilaya with caching.
        /// </summary>
        /// <param name="wilayaCode">Code of the wilaya</param>
        /// <returns>List of dairas in the specified wilaya</returns>
        public async Task<List<CpiDaira>> GetDairasByWilayaAsync(string wilayaCode)
        {
            if (string.IsNullOrWhiteSpace(wilayaCode))
            {
                throw new ArgumentException("Wilaya code cannot be null or empty", nameof(wilayaCode));
            }

            var cacheKey = $"dairas_wilaya_{wilayaCode}";

            if (_dairaCache.TryGetValue(cacheKey, out List<CpiDaira> cachedDairas))
            {
                _dairaCacheHits++;
                LoggingService.LogDebug($"Retrieved dairas for wilaya {wilayaCode} from cache", "CpiLocationService");
                return cachedDairas;
            }

            _dairaCacheMisses++;

            try
            {
                const string selectSql = @"
                    SELECT Code, WilayaCode, NameAr, NameFr, DisplayValue 
                    FROM CpiDairas 
                    WHERE WilayaCode = @WilayaCode
                    ORDER BY DisplayValue";

                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var dairas = await connection.QueryAsync<CpiDaira>(selectSql, new { WilayaCode = wilayaCode });
                var dairaList = dairas.ToList();

                // Cache for 30 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                    Size = 1
                };

                _dairaCache.Set(cacheKey, dairaList, cacheOptions);
                LoggingService.LogDebug($"Retrieved and cached {dairaList.Count} dairas for wilaya {wilayaCode}", "CpiLocationService");

                return dairaList;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error retrieving dairas for wilaya {wilayaCode}: {ex.Message}", "CpiLocationService");
                throw;
            }
        }

        /// <summary>
        /// Searches locations (wilayas and dairas) by text with caching.
        /// </summary>
        /// <param name="searchText">Text to search for</param>
        /// <param name="maxResults">Maximum number of results to return</param>
        /// <returns>List of search results ordered by relevance</returns>
        public async Task<List<CpiLocationSearchResult>> SearchLocationsAsync(string searchText, int maxResults = 20)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                return new List<CpiLocationSearchResult>();
            }

            var cacheKey = $"search_{searchText.Trim().ToLowerInvariant()}_{maxResults}";

            if (_searchCache.TryGetValue(cacheKey, out List<CpiLocationSearchResult> cachedResults))
            {
                _searchCacheHits++;
                LoggingService.LogDebug($"Retrieved search results for '{searchText}' from cache", "CpiLocationService");
                return cachedResults;
            }

            _searchCacheMisses++;

            try
            {
                var searchResults = new List<CpiLocationSearchResult>();
                var searchTerm = $"%{searchText.Trim()}%";

                // Search wilayas
                const string wilayaSearchSql = @"
                    SELECT Code, NameAr, NameFr, DisplayValue 
                    FROM CpiWilayas 
                    WHERE NameAr LIKE @SearchTerm 
                       OR NameFr LIKE @SearchTerm 
                       OR DisplayValue LIKE @SearchTerm 
                       OR Code LIKE @SearchTerm
                    ORDER BY 
                        CASE 
                            WHEN Code = @ExactTerm THEN 1
                            WHEN NameAr = @ExactTerm THEN 2
                            WHEN NameFr = @ExactTerm THEN 3
                            WHEN DisplayValue = @ExactTerm THEN 4
                            ELSE 5
                        END,
                        DisplayValue";

                // Search dairas
                const string dairaSearchSql = @"
                    SELECT d.Code, d.WilayaCode, d.NameAr, d.NameFr, d.DisplayValue,
                           w.Code as WCode, w.NameAr as WNameAr, w.NameFr as WNameFr, w.DisplayValue as WDisplayValue
                    FROM CpiDairas d
                    INNER JOIN CpiWilayas w ON d.WilayaCode = w.Code
                    WHERE d.NameAr LIKE @SearchTerm 
                       OR d.NameFr LIKE @SearchTerm 
                       OR d.DisplayValue LIKE @SearchTerm 
                       OR d.Code LIKE @SearchTerm
                    ORDER BY 
                        CASE 
                            WHEN d.Code = @ExactTerm THEN 1
                            WHEN d.NameAr = @ExactTerm THEN 2
                            WHEN d.NameFr = @ExactTerm THEN 3
                            WHEN d.DisplayValue = @ExactTerm THEN 4
                            ELSE 5
                        END,
                        d.DisplayValue";

                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var searchParams = new { SearchTerm = searchTerm, ExactTerm = searchText.Trim() };

                // Execute wilaya search
                var wilayaResults = await connection.QueryAsync<CpiWilaya>(wilayaSearchSql, searchParams);
                foreach (var wilaya in wilayaResults.Take(maxResults / 2))
                {
                    searchResults.Add(new CpiLocationSearchResult
                    {
                        Wilaya = wilaya,
                        Daira = null,
                        RelevanceScore = CalculateRelevanceScore(searchText, wilaya.NameAr, wilaya.NameFr, wilaya.Code),
                        MatchType = "Wilaya"
                    });
                }

                // Execute daira search
                var dairaResults = await connection.QueryAsync(dairaSearchSql, searchParams);
                foreach (var row in dairaResults.Take(maxResults / 2))
                {
                    var daira = new CpiDaira
                    {
                        Code = row.Code,
                        WilayaCode = row.WilayaCode,
                        NameAr = row.NameAr,
                        NameFr = row.NameFr,
                        DisplayValue = row.DisplayValue
                    };

                    var wilaya = new CpiWilaya
                    {
                        Code = row.WCode,
                        NameAr = row.WNameAr,
                        NameFr = row.WNameFr,
                        DisplayValue = row.WDisplayValue
                    };

                    searchResults.Add(new CpiLocationSearchResult
                    {
                        Wilaya = wilaya,
                        Daira = daira,
                        RelevanceScore = CalculateRelevanceScore(searchText, daira.NameAr, daira.NameFr, daira.Code),
                        MatchType = "Daira"
                    });
                }

                // Sort by relevance and take top results
                var finalResults = searchResults
                    .OrderByDescending(r => r.RelevanceScore)
                    .Take(maxResults)
                    .ToList();

                // Cache for 10 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10),
                    Size = 1
                };

                _searchCache.Set(cacheKey, finalResults, cacheOptions);
                LoggingService.LogDebug($"Retrieved and cached {finalResults.Count} search results for '{searchText}'", "CpiLocationService");

                return finalResults;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error searching locations for '{searchText}': {ex.Message}", "CpiLocationService");
                throw;
            }
        }

        /// <summary>
        /// Reads embedded JSON resource content.
        /// </summary>
        /// <param name="resourceName">Name of the embedded resource</param>
        /// <returns>JSON content as string</returns>
        private string ReadEmbeddedJsonResource(string resourceName)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                using var stream = assembly.GetManifestResourceStream(resourceName);

                if (stream == null)
                {
                    LoggingService.LogWarning($"Embedded resource not found: {resourceName}", "CpiLocationService");
                    return null;
                }

                using var reader = new StreamReader(stream);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error reading embedded resource {resourceName}: {ex.Message}", "CpiLocationService");
                return null;
            }
        }

        /// <summary>
        /// Seeds wilaya data into the database.
        /// </summary>
        /// <param name="wilayas">Array of wilaya data from JSON</param>
        /// <returns>Number of wilayas seeded</returns>
        private async Task<int> SeedWilayasAsync(CpiWilayaJson[] wilayas)
        {
            const string insertSql = @"
                INSERT OR REPLACE INTO CpiWilayas (Code, NameAr, NameFr, DisplayValue)
                VALUES (@Code, @NameAr, @NameFr, @DisplayValue)";

            var wilayaModels = wilayas.Select(w => new CpiWilaya
            {
                Code = w.Key,
                NameAr = w.NameAr,
                NameFr = w.NameFr,
                DisplayValue = w.Value
            }).ToList();

            using var connection = _databaseService.CreateConnection();
            await connection.OpenAsync();

            try
            {
                // Temporarily disable foreign key constraints during seeding to avoid constraint violations
                await connection.ExecuteAsync("PRAGMA foreign_keys = OFF");

                using var transaction = connection.BeginTransaction();
                try
                {
                    var insertedCount = await connection.ExecuteAsync(insertSql, wilayaModels, transaction);
                    transaction.Commit();

                    LoggingService.LogDebug($"Successfully seeded {insertedCount} wilayas", "CpiLocationService");
                    return insertedCount;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error seeding wilayas: {ex.Message}", "CpiLocationService");
                    transaction.Rollback();
                    throw;
                }
            }
            finally
            {
                // Always re-enable foreign key constraints
                try
                {
                    await connection.ExecuteAsync("PRAGMA foreign_keys = ON");
                }
                catch (Exception ex)
                {
                    LoggingService.LogWarning($"Failed to re-enable foreign keys after wilaya seeding: {ex.Message}", "CpiLocationService");
                }
            }
        }

        /// <summary>
        /// Seeds daira data into the database.
        /// </summary>
        /// <param name="dairas">Array of daira data from JSON</param>
        /// <returns>Number of dairas seeded</returns>
        private async Task<int> SeedDairasAsync(CpiDairaJson[] dairas)
        {
            const string insertSql = @"
                INSERT OR REPLACE INTO CpiDairas (Code, WilayaCode, NameAr, NameFr, DisplayValue)
                VALUES (@Code, @WilayaCode, @NameAr, @NameFr, @DisplayValue)";

            var dairaModels = dairas.Select(d => new CpiDaira
            {
                Code = d.Key,
                WilayaCode = d.WilayaCode,
                NameAr = d.NameAr,
                NameFr = d.NameFr,
                DisplayValue = d.Value
            }).ToList();

            using var connection = _databaseService.CreateConnection();
            await connection.OpenAsync();

            try
            {
                // Temporarily disable foreign key constraints during seeding to avoid constraint violations
                await connection.ExecuteAsync("PRAGMA foreign_keys = OFF");

                using var transaction = connection.BeginTransaction();
                try
                {
                    var insertedCount = await connection.ExecuteAsync(insertSql, dairaModels, transaction);
                    transaction.Commit();

                    LoggingService.LogDebug($"Successfully seeded {insertedCount} dairas", "CpiLocationService");
                    return insertedCount;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error seeding dairas: {ex.Message}", "CpiLocationService");
                    transaction.Rollback();
                    throw;
                }
            }
            finally
            {
                // Always re-enable foreign key constraints
                try
                {
                    await connection.ExecuteAsync("PRAGMA foreign_keys = ON");
                }
                catch (Exception ex)
                {
                    LoggingService.LogWarning($"Failed to re-enable foreign keys after daira seeding: {ex.Message}", "CpiLocationService");
                }
            }
        }

        /// <summary>
        /// Calculates relevance score for search results.
        /// </summary>
        /// <param name="searchText">Original search text</param>
        /// <param name="nameAr">Arabic name</param>
        /// <param name="nameFr">French name</param>
        /// <param name="code">Location code</param>
        /// <returns>Relevance score (higher is more relevant)</returns>
        private double CalculateRelevanceScore(string searchText, string nameAr, string nameFr, string code)
        {
            var search = searchText.ToLowerInvariant();
            var score = 0.0;

            // Exact matches get highest score
            if (string.Equals(nameAr, searchText, StringComparison.OrdinalIgnoreCase) ||
                string.Equals(nameFr, searchText, StringComparison.OrdinalIgnoreCase) ||
                string.Equals(code, searchText, StringComparison.OrdinalIgnoreCase))
            {
                score += 100;
            }

            // Starts with matches get high score
            if (nameAr?.ToLowerInvariant().StartsWith(search) == true ||
                nameFr?.ToLowerInvariant().StartsWith(search) == true ||
                code?.ToLowerInvariant().StartsWith(search) == true)
            {
                score += 50;
            }

            // Contains matches get medium score
            if (nameAr?.ToLowerInvariant().Contains(search) == true ||
                nameFr?.ToLowerInvariant().Contains(search) == true ||
                code?.ToLowerInvariant().Contains(search) == true)
            {
                score += 25;
            }

            return score;
        }

        /// <summary>
        /// Clears all caches.
        /// </summary>
        public void ClearCache()
        {
            _wilayaCache?.Dispose();
            _dairaCache?.Dispose();
            _searchCache?.Dispose();

            _wilayaCache = new MemoryCache(new MemoryCacheOptions { SizeLimit = 100, CompactionPercentage = 0.25 });
            _dairaCache = new MemoryCache(new MemoryCacheOptions { SizeLimit = 500, CompactionPercentage = 0.25 });
            _searchCache = new MemoryCache(new MemoryCacheOptions { SizeLimit = 200, CompactionPercentage = 0.25 });

            LoggingService.LogDebug("All caches cleared", "CpiLocationService");
        }

        /// <summary>
        /// Clears all caches (alias for ClearCache to maintain backward compatibility).
        /// </summary>
        public void ClearAllCaches()
        {
            ClearCache();
        }

        /// <summary>
        /// Gets cache statistics for monitoring.
        /// </summary>
        /// <returns>Dictionary containing cache statistics</returns>
        public Dictionary<string, object> GetCacheStatistics()
        {
            var totalWilayaRequests = _wilayaCacheHits + _wilayaCacheMisses;
            var totalDairaRequests = _dairaCacheHits + _dairaCacheMisses;
            var totalSearchRequests = _searchCacheHits + _searchCacheMisses;

            return new Dictionary<string, object>
            {
                ["WilayaCacheHits"] = _wilayaCacheHits,
                ["WilayaCacheMisses"] = _wilayaCacheMisses,
                ["WilayaCacheHitRatio"] = totalWilayaRequests > 0 ? (double)_wilayaCacheHits / totalWilayaRequests : 0.0,
                ["DairaCacheHits"] = _dairaCacheHits,
                ["DairaCacheMisses"] = _dairaCacheMisses,
                ["DairaCacheHitRatio"] = totalDairaRequests > 0 ? (double)_dairaCacheHits / totalDairaRequests : 0.0,
                ["SearchCacheHits"] = _searchCacheHits,
                ["SearchCacheMisses"] = _searchCacheMisses,
                ["SearchCacheHitRatio"] = totalSearchRequests > 0 ? (double)_searchCacheHits / totalSearchRequests : 0.0
            };
        }

        /// <summary>
        /// Gets cache health information.
        /// </summary>
        /// <returns>Cache health information</returns>
        public CacheHealthInfo GetCacheHealth()
        {
            try
            {
                var stats = GetCacheStatistics();
                var wilayaHitRatio = (double)stats["WilayaCacheHitRatio"];
                var dairaHitRatio = (double)stats["DairaCacheHitRatio"];
                var searchHitRatio = (double)stats["SearchCacheHitRatio"];

                var isHealthy = wilayaHitRatio >= 0.7 || dairaHitRatio >= 0.7 || searchHitRatio >= 0.7;

                return new CacheHealthInfo
                {
                    HitRatio = (wilayaHitRatio + dairaHitRatio + searchHitRatio) / 3.0,
                    ItemCount = 0, // Would need more complex calculation
                    MemoryUsageBytes = 0, // Would need more complex calculation
                    IsHealthy = isHealthy,
                    HealthStatus = isHealthy ? "Healthy" : "Needs Attention"
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting cache health: {ex.Message}", "CpiLocationService");
                return new CacheHealthInfo
                {
                    HitRatio = 0.0,
                    ItemCount = 0,
                    MemoryUsageBytes = 0,
                    IsHealthy = false,
                    HealthStatus = $"Error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Warms up the cache by preloading critical data.
        /// </summary>
        /// <returns>Task representing the cache warming operation</returns>
        public async Task WarmupCacheAsync()
        {
            try
            {
                LoggingService.LogDebug("Starting cache warmup", "CpiLocationService");

                // Preload all wilayas
                await GetWilayasAsync();

                // Preload dairas for major wilayas (Algiers, Oran, Constantine)
                var majorWilayas = new[] { "16", "31", "25" }; // Algiers, Oran, Constantine
                foreach (var wilayaCode in majorWilayas)
                {
                    await GetDairasByWilayaAsync(wilayaCode);
                }

                LoggingService.LogDebug("Cache warmup completed", "CpiLocationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache warmup: {ex.Message}", "CpiLocationService");
            }
        }

        /// <summary>
        /// Invalidates specific cache entries based on data change events.
        /// </summary>
        /// <param name="invalidationContext">Context information about what data changed</param>
        public void InvalidateCache(CacheInvalidationContext invalidationContext)
        {
            try
            {
                if (invalidationContext == null)
                    return;

                LoggingService.LogDebug($"Invalidating CpiLocationService cache for {invalidationContext.DataType} ({invalidationContext.InvalidationType})", "CpiLocationService");

                switch (invalidationContext.InvalidationType)
                {
                    case CacheInvalidationType.Full:
                        // Clear all caches
                        ClearCache();
                        break;

                    case CacheInvalidationType.Create:
                    case CacheInvalidationType.Update:
                    case CacheInvalidationType.Delete:
                        // For CPI location changes, clear relevant cache entries
                        if (invalidationContext.DataType == "CpiLocation" || invalidationContext.DataType == "CpiWilaya" || invalidationContext.DataType == "CpiDaira")
                        {
                            // Clear all location-related caches since geographical data is interconnected
                            ClearCache();
                        }
                        break;
                }

                LoggingService.LogDebug("Cache invalidation completed for CpiLocationService", "CpiLocationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error invalidating cache: {ex.Message}", "CpiLocationService");
            }
        }

        /// <summary>
        /// Disposes of the service and its resources.
        /// </summary>
        public void Dispose()
        {
            try
            {
                _wilayaCache?.Dispose();
                _dairaCache?.Dispose();
                _searchCache?.Dispose();

                var stats = GetCacheStatistics();
                LoggingService.LogInfo($"CpiLocationService disposed. Final cache stats - Wilaya hit ratio: {stats["WilayaCacheHitRatio"]:P1}, Daira hit ratio: {stats["DairaCacheHitRatio"]:P1}, Search hit ratio: {stats["SearchCacheHitRatio"]:P1}", "CpiLocationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disposing CpiLocationService: {ex.Message}", "CpiLocationService");
            }
        }
    }

    /// <summary>
    /// Result of CPI location data seeding operation.
    /// </summary>
    public class CpiLocationSeedingResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public int WilayasCount { get; set; }
        public int DairasCount { get; set; }
    }
}
