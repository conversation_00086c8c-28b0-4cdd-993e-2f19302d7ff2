<UserControl
    x:Class="UFU2.Views.Dialogs.CraftInformationDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    Width="360"
    MaxHeight="500"
    FlowDirection="RightToLeft">

    <materialDesign:Card
        Width="360"
        MaxHeight="400"
        Padding="0"
        materialDesign:ElevationAssist.Elevation="Dp8"
        Style="{StaticResource DialogBaseCardStyle}">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  Header  -->
            <materialDesign:Card
                Grid.Row="0"
                Margin="0"
                Style="{DynamicResource HeaderCardStyle}">

                <TextBlock
                    HorizontalAlignment="Center"
                    Style="{StaticResource HeadlineStyle}"
                    Text="معلومات إضافية" />

            </materialDesign:Card>

            <!--  Content  -->
            <ScrollViewer
                Grid.Row="1"
                Margin="12,8"
                VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!--  Craft Code and Description  -->
                    <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                        <materialDesign:PackIcon
                            Width="12"
                            Height="12"
                            Margin="0,0,8,0"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource IconButtonBackgroundBase}"
                            Kind="Pound" />
                        <TextBlock
                            x:Name="CraftCodeTextBlock"
                            FontFamily="Consolas"
                            Style="{StaticResource LabelTextStyle}" />
                    </StackPanel>
                    <materialDesign:Card Margin="0,0,0,8" Style="{StaticResource ContentCardStyle}">
                        <TextBlock
                            x:Name="CraftDescriptionTextBlock"
                            Style="{StaticResource LabelTextStyle}"
                            TextWrapping="Wrap" />
                    </materialDesign:Card>

                    <!--  Content Section  -->
                    <StackPanel>
                        <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Width="12"
                                Height="12"
                                Margin="0,0,8,0"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource IconButtonBackgroundBase}"
                                Kind="FileDocumentOutline" />
                            <TextBlock Style="{StaticResource BodyTextStyle}" Text="المحتوى" />
                        </StackPanel>
                        <materialDesign:Card Margin="0,0,0,8" Style="{StaticResource ContentCardStyle}">
                            <TextBlock
                                x:Name="ContentTextBlock"
                                Style="{StaticResource LabelTextStyle}"
                                TextWrapping="Wrap" />
                        </materialDesign:Card>
                    </StackPanel>

                    <!--  Secondary Section  -->
                    <StackPanel>
                        <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Width="15"
                                Height="15"
                                Margin="0,0,5,0"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource IconButtonBackgroundBase}"
                                Kind="InformationVariant" />
                            <TextBlock Style="{StaticResource BodyTextStyle}" Text="الأنشطة الثانوية" />
                        </StackPanel>
                        <materialDesign:Card Margin="0,0,0,8" Style="{StaticResource ContentCardStyle}">
                            <TextBlock
                                x:Name="SecondaryTextBlock"
                                Style="{StaticResource LabelTextStyle}"
                                TextWrapping="Wrap" />
                        </materialDesign:Card>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>

            <!--  Close Button  -->
            <StackPanel
                Grid.Row="2"
                Margin="0,0,0,8"
                HorizontalAlignment="Center">
                <Button
                    x:Name="CloseButton"
                    Width="127"
                    Click="CloseButton_Click"
                    Content="إغلاق"
                    Style="{StaticResource ContainerButtonStyle}" />
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl>
