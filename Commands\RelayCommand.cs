using System.Windows.Input;
using UFU2.Common;

namespace UFU2.Commands
{
    /// <summary>
    /// Generic command implementation for MVVM pattern.
    /// Supports both parameterized and non-parameterized command execution with optional CanExecute logic.
    /// Integrates with UFU2's logging system for error handling and debugging.
    /// </summary>
    public class RelayCommand : ICommand
    {
        #region Private Fields

        private readonly Action<object?>? _executeWithParameter;
        private readonly Action? _executeWithoutParameter;
        private readonly Func<object?, bool>? _canExecute;
        private readonly string _commandName;

        #endregion

        #region Constructors

        /// <summary>
        /// Creates a new RelayCommand with parameter support
        /// </summary>
        /// <param name="execute">Action to execute when command is invoked</param>
        /// <param name="canExecute">Function to determine if command can execute (optional)</param>
        /// <param name="commandName">Name of the command for logging purposes (optional)</param>
        public RelayCommand(Action<object?> execute, Func<object?, bool>? canExecute = null, string? commandName = null)
        {
            _executeWithParameter = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
            _commandName = commandName ?? "RelayCommand";
        }

        /// <summary>
        /// Creates a new RelayCommand without parameter support
        /// </summary>
        /// <param name="execute">Action to execute when command is invoked</param>
        /// <param name="canExecute">Function to determine if command can execute (optional)</param>
        /// <param name="commandName">Name of the command for logging purposes (optional)</param>
        public RelayCommand(Action execute, Func<bool>? canExecute = null, string? commandName = null)
        {
            if (execute == null)
                throw new ArgumentNullException(nameof(execute));

            _executeWithoutParameter = execute;
            _canExecute = canExecute != null ? _ => canExecute() : null;
            _commandName = commandName ?? "RelayCommand";
        }

        #endregion

        #region ICommand Implementation

        /// <summary>
        /// Event raised when CanExecute status changes
        /// </summary>
        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        /// <summary>
        /// Determines whether the command can execute with the given parameter
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if the command can execute, false otherwise</returns>
        public bool CanExecute(object? parameter)
        {
            try
            {
                return _canExecute?.Invoke(parameter) ?? true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in CanExecute for {_commandName}: {ex.Message}", "RelayCommand");
                return false;
            }
        }

        /// <summary>
        /// Executes the command with the given parameter
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        public void Execute(object? parameter)
        {
            try
            {
                if (!CanExecute(parameter))
                {
                    return;
                }

                if (_executeWithParameter != null)
                {
                    _executeWithParameter(parameter);
                }
                else
                {
                    _executeWithoutParameter?.Invoke();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing command {_commandName}: {ex.Message}", "RelayCommand");
                throw; // Re-throw to allow calling code to handle if needed
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Manually raises the CanExecuteChanged event to refresh command state
        /// </summary>
        public void RaiseCanExecuteChanged()
        {
            try
            {
                CommandManager.InvalidateRequerySuggested();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error raising CanExecuteChanged for {_commandName}: {ex.Message}", "RelayCommand");
            }
        }

        #endregion
    }

    /// <summary>
    /// Generic RelayCommand with strongly-typed parameter support
    /// </summary>
    /// <type param name="T">Type of the command parameter</type param>
    public class RelayCommand<T> : ICommand
    {
        #region Private Fields

        private readonly Action<T?> _execute;
        private readonly Func<T?, bool>? _canExecute;
        private readonly string _commandName;

        #endregion

        #region Constructor

        /// <summary>
        /// Creates a new strongly-typed RelayCommand
        /// </summary>
        /// <param name="execute">Action to execute when command is invoked</param>
        /// <param name="canExecute">Function to determine if command can execute (optional)</param>
        /// <param name="commandName">Name of the command for logging purposes (optional)</param>
        public RelayCommand(Action<T?> execute, Func<T?, bool>? canExecute = null, string? commandName = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
            _commandName = commandName ?? $"RelayCommand<{typeof(T).Name}>";
        }

        #endregion

        #region ICommand Implementation

        /// <summary>
        /// Event raised when CanExecute status changes
        /// </summary>
        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        /// <summary>
        /// Determines whether the command can execute with the given parameter
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if the command can execute, false otherwise</returns>
        public bool CanExecute(object? parameter)
        {
            try
            {
                if (parameter is T typedParameter)
                {
                    return _canExecute?.Invoke(typedParameter) ?? true;
                }
                else if (parameter == null && !typeof(T).IsValueType)
                {
                    return _canExecute?.Invoke(default(T)) ?? true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in CanExecute for {_commandName}: {ex.Message}", "RelayCommand");
                return false;
            }
        }

        /// <summary>
        /// Executes the command with the given parameter
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        public void Execute(object? parameter)
        {
            try
            {
                if (!CanExecute(parameter))
                {
                    return;
                }

                if (parameter is T typedParameter)
                {
                    _execute(typedParameter);
                }
                else if (parameter == null && !typeof(T).IsValueType)
                {
                    _execute(default(T));
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing command {_commandName}: {ex.Message}", "RelayCommand");
                throw; // Re-throw to allow calling code to handle if needed
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Manually raises the CanExecuteChanged event to refresh command state
        /// </summary>
        public void RaiseCanExecuteChanged()
        {
            try
            {
                CommandManager.InvalidateRequerySuggested();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error raising CanExecuteChanged for {_commandName}: {ex.Message}", "RelayCommand");
            }
        }

        #endregion
    }
}
