﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <!-- Enhanced DEBUG/RELEASE configuration for logging optimization -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>false</Optimize>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignColors" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes.MahApps" Version="5.2.1" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.7" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Database\activity_Type.json" />
    <EmbeddedResource Include="Database\craft_Type.json" />
    <EmbeddedResource Include="Database\cpi_Location.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Database\UFU2_Schema.sql" />
    <EmbeddedResource Include="Database\APP_Schema.sql" />
    <EmbeddedResource Include="Database\Archive_Schema.sql" />
  </ItemGroup>

  <ItemGroup>
    <!-- Exclude Analyze directory from build - contains reference ImageCropper library for algorithm extraction -->
    <Compile Remove="Analyze\**" />
    <EmbeddedResource Remove="Analyze\**" />
    <None Remove="Analyze\**" />
    <Page Remove="Analyze\**" />
    <ApplicationDefinition Remove="Analyze\**" />
  </ItemGroup>

  <ItemGroup>
    <Page Remove="Resources\Styles\InputBoxStyles.xaml" />
    <Page Remove="Resources\Styles\ScrollViewerStyles.xaml" />
    <Page Remove="Resources\Styles\SwitchButtonStyles.xaml" />
    <Page Remove="Resources\Styles\TypographyStyles.xaml" />
  </ItemGroup>

  <ItemGroup>
    <Resource Update="Resources\Styles\InputBoxStyles.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Resource>
    <Resource Update="Resources\Styles\ScrollViewerStyles.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Resource>
    <Resource Update="Resources\Styles\SliderStyles.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Resource>
    <Resource Update="Resources\Styles\SwitchButtonStyles.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Resource>
    <Resource Update="Resources\Styles\TypographyStyles.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Resource>
  </ItemGroup>


</Project>
