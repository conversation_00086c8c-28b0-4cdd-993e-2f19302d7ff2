using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Database schema validation service for UFU2 application.
    /// Validates table structure, relationships, indexes, and constraints.
    /// </summary>
    public class DatabaseSchemaValidator
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the DatabaseSchemaValidator.
        /// </summary>
        /// <param name="databaseService">The database service instance</param>
        public DatabaseSchemaValidator(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            LoggingService.LogInfo("DatabaseSchemaValidator initialized", "DatabaseSchemaValidator");
        }

        #endregion

        #region Schema Validation

        /// <summary>
        /// Validates the complete database schema structure.
        /// </summary>
        /// <returns>Schema validation result</returns>
        public async Task<SchemaValidationResult> ValidateSchemaAsync()
        {
            var result = new SchemaValidationResult();

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                LoggingService.LogInfo("Starting comprehensive schema validation", "DatabaseSchemaValidator");

                // Validate tables
                await ValidateTablesAsync(connection, result);

                // Validate indexes
                await ValidateIndexesAsync(connection, result);

                // Validate foreign key relationships
                await ValidateForeignKeysAsync(connection, result);

                // Validate constraints
                await ValidateConstraintsAsync(connection, result);

                // Validate data integrity
                await ValidateDataIntegrityAsync(connection, result);

                result.IsValid = !result.Errors.Any();
                result.ValidationTime = DateTime.UtcNow;

                var status = result.IsValid ? "PASSED" : "FAILED";
                LoggingService.LogInfo($"Schema validation {status}: {result.Errors.Count} errors, {result.Warnings.Count} warnings", "DatabaseSchemaValidator");

                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Schema validation failed with exception: {ex.Message}");
                result.IsValid = false;
                result.ValidationTime = DateTime.UtcNow;

                ErrorManager.HandleErrorToast(ex, "فشل في التحقق من مخطط قاعدة البيانات", "خطأ في التحقق", LogLevel.Error, "DatabaseSchemaValidator");
                return result;
            }
        }

        /// <summary>
        /// Validates that all required tables exist with correct structure.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateTablesAsync(SqliteConnection connection, SchemaValidationResult result)
        {
            try
            {
                // Define expected tables with their required columns (PascalCase naming)
                var expectedTables = new Dictionary<string, List<string>>
                {
                    ["Clients"] = new List<string> { "Uid", "NameFr", "NameAr", "BirthDate", "BirthPlace", "Gender", "Address", "NationalId", "CreatedAt", "UpdatedAt" },
                    ["PhoneNumbers"] = new List<string> { "Uid", "ClientUid", "PhoneNumber", "PhoneType", "IsPrimary" },
                    ["Activities"] = new List<string> { "Uid", "ClientUid", "ActivityType", "ActivityStatus", "ActivityStartDate", "CommercialRegister", "ActivityLocation", "NifNumber", "NisNumber", "ArtNumber", "CpiDaira", "CpiWilaya", "ActivityUpdateDate", "ActivityUpdateNote", "CreatedAt", "UpdatedAt" },
                    ["CommercialActivityCodes"] = new List<string> { "Uid", "ActivityUid", "ActivityCode" },
                    ["ProfessionNames"] = new List<string> { "ActivityUid", "ActivityDescription" },
                    ["CraftActivityCodes"] = new List<string> { "Uid", "ActivityUid", "CraftCode" },
                    ["FileCheckStates"] = new List<string> { "Uid", "ActivityUid", "FileCheckType", "IsChecked", "CheckedDate" },
                    ["G12Check"] = new List<string> { "Uid", "ActivityUid", "Year" },
                    ["BisCheck"] = new List<string> { "Uid", "ActivityUid", "Year" },
                    ["Notes"] = new List<string> { "Uid", "ActivityUid", "Content", "Priority" },
                    ["UidSequences"] = new List<string> { "EntityType", "Prefix", "LastSequence", "UpdatedAt" }
                };

                // Get existing tables
                const string tablesQuery = @"
                    SELECT name FROM sqlite_master 
                    WHERE type = 'table' 
                    AND name NOT LIKE 'sqlite_%'
                    ORDER BY name";

                var existingTables = (await connection.QueryAsync<string>(tablesQuery)).ToList();

                // Check for missing tables
                foreach (var expectedTable in expectedTables.Keys)
                {
                    if (!existingTables.Contains(expectedTable))
                    {
                        result.Errors.Add($"Missing required table: {expectedTable}");
                        continue;
                    }

                    // Validate table structure
                    await ValidateTableStructureAsync(connection, expectedTable, expectedTables[expectedTable], result);
                }

                // Check for unexpected tables
                var unexpectedTables = existingTables.Except(expectedTables.Keys).ToList();
                foreach (var unexpectedTable in unexpectedTables)
                {
                    result.Warnings.Add($"Unexpected table found: {unexpectedTable}");
                }

                LoggingService.LogDebug($"Table validation completed: {existingTables.Count} tables found", "DatabaseSchemaValidator");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Table validation failed: {ex.Message}");
                LoggingService.LogError($"Table validation error: {ex.Message}", "DatabaseSchemaValidator");
            }
        }

        /// <summary>
        /// Validates the structure of a specific table.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="tableName">Name of the table to validate</param>
        /// <param name="expectedColumns">List of expected column names</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateTableStructureAsync(SqliteConnection connection, string tableName, List<string> expectedColumns, SchemaValidationResult result)
        {
            try
            {
                // Get table columns
                var columnsQuery = $"PRAGMA table_info({tableName})";
                var columns = await connection.QueryAsync<TableColumnInfo>(columnsQuery);
                var existingColumns = columns.Select(c => c.Name.ToLowerInvariant()).ToList();

                // Check for missing columns
                foreach (var expectedColumn in expectedColumns)
                {
                    if (!existingColumns.Contains(expectedColumn.ToLowerInvariant()))
                    {
                        result.Errors.Add($"Table '{tableName}' missing required column: {expectedColumn}");
                    }
                }

                // Validate primary key constraints
                var primaryKeyColumns = columns.Where(c => c.Pk > 0).OrderBy(c => c.Pk).Select(c => c.Name).ToList();
                await ValidatePrimaryKeyAsync(connection, tableName, primaryKeyColumns, result);

                LoggingService.LogDebug($"Table structure validated: {tableName} ({existingColumns.Count} columns)", "DatabaseSchemaValidator");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Failed to validate table structure for '{tableName}': {ex.Message}");
                LoggingService.LogError($"Table structure validation error for {tableName}: {ex.Message}", "DatabaseSchemaValidator");
            }
        }

        /// <summary>
        /// Validates primary key constraints for a table.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="tableName">Name of the table</param>
        /// <param name="primaryKeyColumns">List of primary key columns</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidatePrimaryKeyAsync(SqliteConnection connection, string tableName, List<string> primaryKeyColumns, SchemaValidationResult result)
        {
            try
            {
                // Define expected primary keys
                var expectedPrimaryKeys = new Dictionary<string, List<string>>
                {
                    ["Clients"] = new List<string> { "Uid" },
                    ["PhoneNumbers"] = new List<string> { "Uid" },
                    ["Activities"] = new List<string> { "Uid" },
                    ["CommercialActivityCodes"] = new List<string> { "Uid" },
                    ["ProfessionNames"] = new List<string> { "ActivityUid" },
                    ["FileCheckStates"] = new List<string> { "Uid" },
                    ["G12Check"] = new List<string> { "Uid" },
                    ["BisCheck"] = new List<string> { "Uid" },
                    ["Notes"] = new List<string> { "Uid" },
                    ["UidSequences"] = new List<string> { "EntityType", "Prefix" },
                    ["schema_version"] = new List<string> { "version" }
                };

                if (expectedPrimaryKeys.ContainsKey(tableName))
                {
                    var expected = expectedPrimaryKeys[tableName].Select(c => c.ToLowerInvariant()).OrderBy(c => c).ToList();
                    var actual = primaryKeyColumns.Select(c => c.ToLowerInvariant()).OrderBy(c => c).ToList();

                    if (!expected.SequenceEqual(actual))
                    {
                        result.Errors.Add($"Table '{tableName}' has incorrect primary key. Expected: [{string.Join(", ", expected)}], Actual: [{string.Join(", ", actual)}]");
                    }
                }
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"Failed to validate primary key for table '{tableName}': {ex.Message}");
            }
        }

        /// <summary>
        /// Validates database indexes.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateIndexesAsync(SqliteConnection connection, SchemaValidationResult result)
        {
            try
            {
                // Get all indexes
                const string indexesQuery = @"
                    SELECT name, tbl_name, sql 
                    FROM sqlite_master 
                    WHERE type = 'index' 
                    AND name NOT LIKE 'sqlite_%'
                    ORDER BY name";

                var indexes = await connection.QueryAsync<IndexInfo>(indexesQuery);
                var indexList = indexes.ToList();

                // Define expected indexes
                var expectedIndexes = new List<string>
                {
                    "idx_clients_name_fr",
                    "idx_clients_name_ar",
                    "idx_clients_national_id",
                    "idx_phone_numbers_client_uid",
                    "idx_phone_numbers_primary",
                    "idx_activities_client_uid",
                    "idx_activities_type_status",
                    "idx_commercial_activity_codes_activity_uid",
                    "idx_profession_names_activity_uid",
                    "idx_file_check_states_activity_uid",
                    "idx_g12_check_activity_uid",
                    "idx_bis_check_activity_uid",
                    "idx_notes_activity_uid"
                };

                var existingIndexNames = indexList.Select(i => i.Name).ToList();

                // Check for missing critical indexes
                foreach (var expectedIndex in expectedIndexes)
                {
                    if (!existingIndexNames.Contains(expectedIndex))
                    {
                        result.Warnings.Add($"Missing recommended index: {expectedIndex}");
                    }
                }

                // Validate index definitions
                foreach (var index in indexList)
                {
                    if (string.IsNullOrEmpty(index.Sql))
                    {
                        result.Warnings.Add($"Index '{index.Name}' has no SQL definition (might be auto-created)");
                    }
                }

                LoggingService.LogDebug($"Index validation completed: {indexList.Count} indexes found", "DatabaseSchemaValidator");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Index validation failed: {ex.Message}");
                LoggingService.LogError($"Index validation error: {ex.Message}", "DatabaseSchemaValidator");
            }
        }

        /// <summary>
        /// Validates foreign key relationships.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateForeignKeysAsync(SqliteConnection connection, SchemaValidationResult result)
        {
            try
            {
                // Check if foreign keys are enabled
                var foreignKeysEnabled = await connection.ExecuteScalarAsync<int>("PRAGMA foreign_keys");
                if (foreignKeysEnabled == 0)
                {
                    result.Errors.Add("Foreign keys are not enabled in the database");
                }

                // Define expected foreign key relationships
                var expectedForeignKeys = new Dictionary<string, List<(string Column, string ReferencedTable, string ReferencedColumn)>>
                {
                    ["PhoneNumbers"] = new List<(string, string, string)> { ("ClientUid", "Clients", "Uid") },
                    ["Activities"] = new List<(string, string, string)> { ("ClientUid", "Clients", "Uid") },
                    ["CommercialActivityCodes"] = new List<(string, string, string)> { ("ActivityUid", "Activities", "Uid") },
                    ["ProfessionNames"] = new List<(string, string, string)> { ("ActivityUid", "Activities", "Uid") },
                    ["FileCheckStates"] = new List<(string, string, string)> { ("ActivityUid", "Activities", "Uid") },
                    ["G12Check"] = new List<(string, string, string)> { ("ActivityUid", "Activities", "Uid") },
                    ["BisCheck"] = new List<(string, string, string)> { ("ActivityUid", "Activities", "Uid") },
                    ["Notes"] = new List<(string, string, string)> { ("ActivityUid", "Activities", "Uid") }
                };

                // Validate each table's foreign keys
                foreach (var tableName in expectedForeignKeys.Keys)
                {
                    await ValidateTableForeignKeysAsync(connection, tableName, expectedForeignKeys[tableName], result);
                }

                LoggingService.LogDebug("Foreign key validation completed", "DatabaseSchemaValidator");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Foreign key validation failed: {ex.Message}");
                LoggingService.LogError($"Foreign key validation error: {ex.Message}", "DatabaseSchemaValidator");
            }
        }

        /// <summary>
        /// Validates foreign keys for a specific table.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="tableName">Name of the table</param>
        /// <param name="expectedForeignKeys">Expected foreign key relationships</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateTableForeignKeysAsync(SqliteConnection connection, string tableName, List<(string Column, string ReferencedTable, string ReferencedColumn)> expectedForeignKeys, SchemaValidationResult result)
        {
            try
            {
                var foreignKeysQuery = $"PRAGMA foreign_key_list({tableName})";
                var foreignKeys = await connection.QueryAsync<ForeignKeyInfo>(foreignKeysQuery);
                var foreignKeyList = foreignKeys.ToList();

                foreach (var (column, referencedTable, referencedColumn) in expectedForeignKeys)
                {
                    var matchingFk = foreignKeyList.FirstOrDefault(fk => 
                        fk.From.Equals(column, StringComparison.OrdinalIgnoreCase) &&
                        fk.Table.Equals(referencedTable, StringComparison.OrdinalIgnoreCase) &&
                        fk.To.Equals(referencedColumn, StringComparison.OrdinalIgnoreCase));

                    if (matchingFk == null)
                    {
                        result.Errors.Add($"Missing foreign key in table '{tableName}': {column} -> {referencedTable}.{referencedColumn}");
                    }
                }
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"Failed to validate foreign keys for table '{tableName}': {ex.Message}");
            }
        }

        /// <summary>
        /// Validates database constraints.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateConstraintsAsync(SqliteConnection connection, SchemaValidationResult result)
        {
            try
            {
                // Check for CHECK constraints by examining table creation SQL
                const string tablesQuery = @"
                    SELECT name, sql FROM sqlite_master 
                    WHERE type = 'table' 
                    AND name NOT LIKE 'sqlite_%'
                    AND sql IS NOT NULL";

                var tables = await connection.QueryAsync<TableDefinition>(tablesQuery);

                foreach (var table in tables)
                {
                    ValidateTableConstraints(table.Name, table.Sql, result);
                }

                LoggingService.LogDebug("Constraint validation completed", "DatabaseSchemaValidator");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Constraint validation failed: {ex.Message}");
                LoggingService.LogError($"Constraint validation error: {ex.Message}", "DatabaseSchemaValidator");
            }
        }

        /// <summary>
        /// Validates constraints for a specific table.
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <param name="createSql">Table creation SQL</param>
        /// <param name="result">Validation result to update</param>
        private void ValidateTableConstraints(string tableName, string createSql, SchemaValidationResult result)
        {
            if (string.IsNullOrEmpty(createSql)) return;

            var sql = createSql.ToUpperInvariant();

            // Check for expected CHECK constraints
            var expectedConstraints = new Dictionary<string, List<string>>
            {
                ["Clients"] = new List<string> { "CHECK", "Gender" },
                ["PhoneNumbers"] = new List<string> { "CHECK", "PhoneType" },
                ["Activities"] = new List<string> { "CHECK", "ActivityType" },
                ["FileCheckStates"] = new List<string> { "CHECK", "FileCheckType" }
            };

            if (expectedConstraints.ContainsKey(tableName))
            {
                foreach (var constraint in expectedConstraints[tableName])
                {
                    if (!sql.Contains(constraint))
                    {
                        result.Warnings.Add($"Table '{tableName}' may be missing CHECK constraint for: {constraint}");
                    }
                }
            }
        }

        /// <summary>
        /// Validates data integrity across tables.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateDataIntegrityAsync(SqliteConnection connection, SchemaValidationResult result)
        {
            try
            {
                // Check for orphaned records
                await CheckOrphanedRecordsAsync(connection, result);

                // Check for duplicate UIDs
                await CheckDuplicateUIDsAsync(connection, result);

                // Check for invalid data formats
                await CheckDataFormatsAsync(connection, result);

                LoggingService.LogDebug("Data integrity validation completed", "DatabaseSchemaValidator");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Data integrity validation failed: {ex.Message}");
                LoggingService.LogError($"Data integrity validation error: {ex.Message}", "DatabaseSchemaValidator");
            }
        }

        /// <summary>
        /// Checks for orphaned records that violate referential integrity.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="result">Validation result to update</param>
        private async Task CheckOrphanedRecordsAsync(SqliteConnection connection, SchemaValidationResult result)
        {
            var orphanChecks = new List<(string Query, string Description)>
            {
                ("SELECT COUNT(*) FROM PhoneNumbers p LEFT JOIN Clients c ON p.ClientUid = c.Uid WHERE c.Uid IS NULL", "Orphaned phone numbers"),
                ("SELECT COUNT(*) FROM Activities a LEFT JOIN Clients c ON a.ClientUid = c.Uid WHERE c.Uid IS NULL", "Orphaned activities"),
                ("SELECT COUNT(*) FROM CommercialActivityCodes cac LEFT JOIN Activities a ON cac.ActivityUid = a.Uid WHERE a.Uid IS NULL", "Orphaned commercial activity codes"),
                ("SELECT COUNT(*) FROM FileCheckStates fcs LEFT JOIN Activities a ON fcs.ActivityUid = a.Uid WHERE a.Uid IS NULL", "Orphaned file check states"),
                ("SELECT COUNT(*) FROM Notes n LEFT JOIN Activities a ON n.ActivityUid = a.Uid WHERE a.Uid IS NULL", "Orphaned notes")
            };

            foreach (var (query, description) in orphanChecks)
            {
                try
                {
                    var count = await connection.ExecuteScalarAsync<int>(query);
                    if (count > 0)
                    {
                        result.Errors.Add($"Data integrity violation: {count} {description.ToLowerInvariant()} found");
                    }
                }
                catch (Exception ex)
                {
                    result.Warnings.Add($"Failed to check {description.ToLowerInvariant()}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Checks for duplicate UIDs in tables that should have unique identifiers.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="result">Validation result to update</param>
        private async Task CheckDuplicateUIDsAsync(SqliteConnection connection, SchemaValidationResult result)
        {
            var uidChecks = new List<(string Query, string Description)>
            {
                ("SELECT Uid, COUNT(*) FROM Clients GROUP BY Uid HAVING COUNT(*) > 1", "Duplicate client UIDs"),
                ("SELECT Uid, COUNT(*) FROM Activities GROUP BY Uid HAVING COUNT(*) > 1", "Duplicate activity UIDs")
            };

            foreach (var (query, description) in uidChecks)
            {
                try
                {
                    var duplicates = await connection.QueryAsync<dynamic>(query);
                    var duplicateList = duplicates.ToList();
                    if (duplicateList.Any())
                    {
                        result.Errors.Add($"Data integrity violation: {duplicateList.Count} {description.ToLowerInvariant()} found");
                    }
                }
                catch (Exception ex)
                {
                    result.Warnings.Add($"Failed to check {description.ToLowerInvariant()}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Checks for invalid data formats in critical fields.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="result">Validation result to update</param>
        private async Task CheckDataFormatsAsync(SqliteConnection connection, SchemaValidationResult result)
        {
            try
            {
                // Check for invalid phone number formats (basic validation)
                var invalidPhones = await connection.ExecuteScalarAsync<int>(
                    "SELECT COUNT(*) FROM PhoneNumbers WHERE PhoneNumber IS NOT NULL AND LENGTH(TRIM(PhoneNumber)) < 8");
                if (invalidPhones > 0)
                {
                    result.Warnings.Add($"Found {invalidPhones} potentially invalid phone numbers (too short)");
                }

                // Check for empty required fields
                var emptyClientNames = await connection.ExecuteScalarAsync<int>(
                    "SELECT COUNT(*) FROM Clients WHERE NameFr IS NULL OR TRIM(NameFr) = ''");
                if (emptyClientNames > 0)
                {
                    result.Errors.Add($"Found {emptyClientNames} clients with empty French names (required field)");
                }
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"Failed to validate data formats: {ex.Message}");
            }
        }

        #endregion

        #region Helper Classes

        /// <summary>
        /// Represents table column information from PRAGMA table_info.
        /// </summary>
        private class TableColumnInfo
        {
            public int Cid { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Type { get; set; } = string.Empty;
            public int NotNull { get; set; }
            public string? DfltValue { get; set; }
            public int Pk { get; set; }
        }

        /// <summary>
        /// Represents index information from sqlite_master.
        /// </summary>
        private class IndexInfo
        {
            public string Name { get; set; } = string.Empty;
            public string TblName { get; set; } = string.Empty;
            public string? Sql { get; set; }
        }

        /// <summary>
        /// Represents foreign key information from PRAGMA foreign_key_list.
        /// </summary>
        private class ForeignKeyInfo
        {
            public int Id { get; set; }
            public int Seq { get; set; }
            public string Table { get; set; } = string.Empty;
            public string From { get; set; } = string.Empty;
            public string To { get; set; } = string.Empty;
            public string OnUpdate { get; set; } = string.Empty;
            public string OnDelete { get; set; } = string.Empty;
            public string Match { get; set; } = string.Empty;
        }

        /// <summary>
        /// Represents table definition from sqlite_master.
        /// </summary>
        private class TableDefinition
        {
            public string Name { get; set; } = string.Empty;
            public string Sql { get; set; } = string.Empty;
        }

        #endregion
    }
}
