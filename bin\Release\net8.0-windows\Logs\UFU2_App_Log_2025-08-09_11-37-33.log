=== UFU2 Application Session Started at 2025-08-09 11:37:33 ===
[2025-08-09 11:37:33.530]  	[INFO]		[LoggingService]	Log level set to Info
[2025-08-09 11:37:33.536]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 11:37:33.552]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 11:37:33.559]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 11:37:33.589]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 11:37:33.592]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 11:37:33.595]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 11:37:33.598]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 11:37:33.601]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 11:37:33.604]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 11:37:33.607]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 11:37:33.616]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 11:37:33.619]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 11:37:33.852]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 11:37:33.855]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 11:37:33.857]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 11:37:33.861]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 11:37:33.864]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 11:37:33.872]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 11:37:33.875]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 11:37:34.304]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 11:37:34.307]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 11:37:34.311]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 11:37:34.313]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 11:37:34.361]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 11:37:34.361]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 11:37:34.361]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 11:37:34.362]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 11:37:34.411]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 11:37:34.415]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 11:37:34.420]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 11:37:34.423]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 11:37:34.426]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 11:37:34.429]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 11:37:34.436]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 11:37:34.439]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 11:37:34.444]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 11:37:34.447]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 11:37:34.451]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 11:37:34.457]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 11:37:34.461]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 11:37:34.464]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 11:37:34.479]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 11:37:34.484]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 11:37:34.487]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 11:37:34.491]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 11:37:34.494]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 11:37:34.501]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 11:37:34.523]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 11:37:34.569]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 11:37:34.617]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 11:37:34.635]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 11:37:34.640]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 11:37:34.648]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 11:37:34.744]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 11:37:34.750]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 11:37:34.760]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 11:37:34.775]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 11:37:34.780]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 11:37:34.784]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 11:37:34.788]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 11:37:34.791]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 11:37:34.795]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 11:37:34.798]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 11:37:34.804]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 11:37:34.856]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-09 11:37:34.863]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 52ms
[2025-08-09 11:37:34.875]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 11:37:34.891]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 23ms
[2025-08-09 11:37:34.896]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 11:37:34.900]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 6ms
[2025-08-09 11:37:34.910]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 6ms
[2025-08-09 11:37:34.922]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 11:37:34.926]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 12ms
[2025-08-09 11:37:34.929]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 11:37:34.934]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 11:37:34.938]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 11:37:34.941]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 11:37:34.944]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 11:37:34.947]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 11:37:34.951]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 11:37:34.954]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 11:37:34.958]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 11:37:34.961]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 11:37:34.964]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 11:37:34.968]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 11:37:34.971]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 11:37:34.975]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 11:37:34.978]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 11:37:36.883]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 11:37:36.933]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 11:37:37.066]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 60.0% increase in response time
[2025-08-09 11:37:37.567]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 110.0% increase in response time
[2025-08-09 11:37:37.743]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 11:37:44.475]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 11:37:44.559]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 11:37:44.606]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 11:37:44.611]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 11:37:44.616]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 11:37:44.620]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 11:37:44.713]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 11:37:45.180]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 11:37:49.014]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 11:37:49.020]  	[INFO]		[ImageManagementViewModel]	Initializing ImageManagementViewModel
[2025-08-09 11:37:49.024]  	[INFO]		[ImageManagementViewModel]	ImageManagementViewModel initialized successfully - Zoom: 100%, Rotation: 0°
[2025-08-09 11:37:49.029]  	[INFO]		[ImageManagementDialog]	ImageManagementDialog initialized - Current Memory Usage: 23.25 MB (24,377,448 bytes)
[2025-08-09 11:37:49.032]  	[INFO]		[NPersonalView]	Opening ImageManagementDialog in memory-only mode
[2025-08-09 11:37:49.040]  	[INFO]		[ClientProfileImage]	Image edit request delegated to parent control
[2025-08-09 11:38:20.234]  	[INFO]		[ImageManagementDialog]	LoadImage button clicked
[2025-08-09 11:38:23.409]  	[INFO]		[ImageManagementDialog]	User selected image file: photo_3712252582143567829_c.jpg
[2025-08-09 11:38:23.413]  	[INFO]		[ImageManagementViewModel]	Image loading started successfully
[2025-08-09 11:38:23.462]  	[INFO]		[ImageManagementDialog]	Original Image Loaded - File: photo_3712252582143567829_c.jpg, Size: 34.98 KB (35,824 bytes), Path: E:\UserFiles\Pictures\photo_3712252582143567829_c.jpg
[2025-08-09 11:38:23.467]  	[INFO]		[ImageManagementDialog]	Preview Image Generated - Dimensions: 800x800, Memory: 2.44 MB (2,560,000 bytes), Decode Width: 800px
[2025-08-09 11:38:23.470]  	[INFO]		[ImageManagementDialog]	Image Processing Metrics - Scaling: 100.0%, Compression Ratio: 0.01:1, Memory vs File: 71.46x
[2025-08-09 11:38:23.474]  	[INFO]		[ImageManagementViewModel]	Preview Image Validated - Dimensions: 800x800, Aspect Ratio: 1.00, Megapixels: 0.6MP, Total Pixels: 640,000
[2025-08-09 11:38:23.479]  	[INFO]		[ImageManagementViewModel]	Creating original image backup - Dimensions: 800x800
[2025-08-09 11:38:23.484]  	[INFO]		[ImageManagementViewModel]	Resetting image transforms to default values
[2025-08-09 11:38:23.489]  	[INFO]		[ImageManagementViewModel]	Image transforms reset successfully
[2025-08-09 11:38:23.494]  	[INFO]		[ImageManagementViewModel]	Crop rectangle reset to default position
[2025-08-09 11:38:23.500]  	[INFO]		[ImageManagementViewModel]	Backend simulation initialized for 800x800 image - Backend container: 1500x960
[2025-08-09 11:38:23.504]  	[INFO]		[ImageManagementViewModel]	Image loading completed successfully - Success: True
[2025-08-09 11:38:23.508]  	[INFO]		[ImageManagementDialog]	Image loading process completed successfully for: photo_3712252582143567829_c.jpg
[2025-08-09 11:38:24.480]  	[INFO]		[ImageManagementDialog]	Crop Image button clicked - Starting crop operation workflow
[2025-08-09 11:38:24.485]  	[INFO]		[ImageManagementDialog]	Crop validation passed - Image: 800x800, Crop: 123,15,254,290
[2025-08-09 11:38:24.488]  	[INFO]		[ImageManagementDialog]	Proceeding with direct crop operation
[2025-08-09 11:38:24.492]  	[INFO]		[ImageManagementViewModel]	Image loading started successfully
[2025-08-09 11:38:24.998]  	[INFO]		[ImageManagementViewModel]	Starting interactive image crop operation
[2025-08-09 11:38:25.002]  	[INFO]		[ImageManagementViewModel]	Using WYSIWYG cropping system
[2025-08-09 11:38:25.006]  	[INFO]		[ImageManagementViewModel]	Creating WYSIWYG cropped image
[2025-08-09 11:38:25.014]  	[INFO]		[ImageManagementViewModel]	WYSIWYG cropped bitmap created successfully - Final size: 634x725
[2025-08-09 11:38:25.018]  	[INFO]		[ImageManagementViewModel]	WYSIWYG cropped image created successfully - Size: 634x725
[2025-08-09 11:38:25.021]  	[INFO]		[ImageManagementViewModel]	Image cropped successfully using interactive rectangle - New size: 634x725
[2025-08-09 11:38:25.024]  	[INFO]		[ImageManagementDialog]	Crop operation successful - Result: 634x725
[2025-08-09 11:38:25.028]  	[INFO]		[ImageManagementViewModel]	Applying real-time crop preview
[2025-08-09 11:38:25.064]  	[INFO]		[ImageManagementViewModel]	Applying crop preview - New dimensions: 634x725
[2025-08-09 11:38:25.068]  	[INFO]		[ImageManagementViewModel]	Original image backup preserved - Dimensions: 800x800
[2025-08-09 11:38:25.073]  	[INFO]		[ImageManagementViewModel]	Applying post-crop visual fit for cropped image: 634x725
[2025-08-09 11:38:25.076]  	[INFO]		[ImageManagementViewModel]	Post-crop visual fit applied:
[2025-08-09 11:38:25.080]  	[INFO]		[ImageManagementViewModel]	  • Cropped image: 634x725 pixels
[2025-08-09 11:38:25.084]  	[INFO]		[ImageManagementViewModel]	  • Natural display size (100% zoom): 279.8x320.0
[2025-08-09 11:38:25.087]  	[INFO]		[ImageManagementViewModel]	  • Crop guide target: 254x290
[2025-08-09 11:38:25.091]  	[INFO]		[ImageManagementViewModel]	  • Scale factors - X: 0.908, Y: 0.906
[2025-08-09 11:38:25.094]  	[INFO]		[ImageManagementViewModel]	  • Applied visual scale: 0.906
[2025-08-09 11:38:25.099]  	[INFO]		[ImageManagementViewModel]	  • Final visual size: 253.6x290.0
[2025-08-09 11:38:25.102]  	[INFO]		[ImageManagementViewModel]	  • ZoomPercentage maintained at: 100%
[2025-08-09 11:38:25.106]  	[INFO]		[ImageManagementViewModel]	✓ Visual fit validation PASSED - Image will visually fit within crop guide boundaries
[2025-08-09 11:38:25.109]  	[INFO]		[ImageManagementViewModel]	Crop rectangle reset to default position
[2025-08-09 11:38:25.112]  	[INFO]		[ImageManagementViewModel]	Backend simulation initialized for 634x725 image - Backend container: 1500x960
[2025-08-09 11:38:25.117]  	[INFO]		[ImageManagementViewModel]	Real-time crop preview applied successfully - New image size: 634x725
[2025-08-09 11:38:25.120]  	[INFO]		[ImageManagementViewModel]	Loading completed successfully with original image backup preserved
[2025-08-09 11:38:25.124]  	[INFO]		[ImageManagementDialog]	Real-time crop preview applied successfully - Image replaced with cropped result
[2025-08-09 11:38:26.678]  	[INFO]		[SaveCancelButtonsControl]	Save button clicked in SaveCancelButtonsControl
[2025-08-09 11:38:26.684]  	[INFO]		[ImageManagementDialog]	Save button clicked - Starting save operation workflow
[2025-08-09 11:38:26.688]  	[INFO]		[ImageManagementDialog]	Preparing to save image with standardized dimensions - Original size: 634x725, Target: 127x145 pixels, Format: Bgr32
[2025-08-09 11:38:26.692]  	[INFO]		[ImageManagementViewModel]	Image loading started successfully
[2025-08-09 11:38:26.695]  	[INFO]		[ImageManagementDialog]	Starting in-memory image processing - Original size: 634x725
[2025-08-09 11:38:26.701]  	[INFO]		[ImageManagementDialog]	High-quality resize completed successfully - Final size: 127x145
[2025-08-09 11:38:26.705]  	[INFO]		[ImageManagementDialog]	Image processed and stored in memory successfully - Final size: 127x145 pixels
[2025-08-09 11:38:26.708]  	[INFO]		[ImageManagementViewModel]	Preview Image Validated - Dimensions: 634x725, Aspect Ratio: 0.87, Megapixels: 0.5MP, Total Pixels: 459,650
[2025-08-09 11:38:26.711]  	[INFO]		[ImageManagementViewModel]	Creating original image backup - Dimensions: 634x725
[2025-08-09 11:38:26.716]  	[INFO]		[ImageManagementViewModel]	Resetting image transforms to default values
[2025-08-09 11:38:26.720]  	[INFO]		[ImageManagementViewModel]	Image transforms reset successfully
[2025-08-09 11:38:26.723]  	[INFO]		[ImageManagementViewModel]	Crop rectangle reset to default position
[2025-08-09 11:38:26.727]  	[INFO]		[ImageManagementViewModel]	Backend simulation initialized for 634x725 image - Backend container: 1500x960
[2025-08-09 11:38:26.731]  	[INFO]		[ImageManagementViewModel]	Image loading completed successfully - Success: True
[2025-08-09 11:38:26.734]  	[INFO]		[ImageManagementDialog]	Image processed and stored in memory successfully - Standardized to 127x145 pixels
[2025-08-09 11:38:26.738]  	[INFO]		[ImageManagementDialog]	Dialog closing - Final Memory Usage: 23.34 MB (24,475,328 bytes), Result: True
[2025-08-09 11:38:26.778]  	[INFO]		[ImageManagementDialog]	Memory cleanup completed - Freed: 2.76 MB (2,891,264 bytes), After GC: 20.58 MB
[2025-08-09 11:38:26.785]  	[INFO]		[ImageManagementDialog]	ImageManagementDialog session completed with result: True
[2025-08-09 11:38:26.791]  	[INFO]		[NPersonalView]	ImageManagementDialog completed successfully
[2025-08-09 11:38:26.799]  	[INFO]		[ClientProfileImage]	Profile image updated successfully
[2025-08-09 11:38:26.805]  	[INFO]		[NPersonalView]	Profile image updated successfully in NewClientViewModel
[2025-08-09 11:38:32.212]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 11:38:32.712]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 11:38:34.764]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 11:38:34.772]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 11:38:34.785]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 11:38:34.792]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 11:38:34.802]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 11:38:34.855]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 11:38:35.626]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 11:38:35.641]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:35.645]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 11:38:35.652]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 11:38:35.656]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:35.660]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 11:38:35.666]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 11:38:35.673]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 11:38:35.684]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:35.689]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 11:38:35.693]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 11:38:35.696]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:35.701]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 11:38:35.705]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 11:38:35.708]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 11:38:35.712]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 11:38:35.720]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 11:38:35.723]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 11:38:35.727]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 11:38:35.778]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 11:38:35.804]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 11:38:35.810]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 11:38:35.815]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 11:38:35.820]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 11:38:35.825]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-09 11:38:35.830]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 50.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-09 11:38:35.835]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-09 11:38:35.840]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 66.7%
[2025-08-09 11:38:35.844]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 11:38:35.849]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 11:38:35.855]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 11:38:35.878]  	[INFO]		[ResourceManager]	Generated memory leak report: 7 alive resources, 0 dead resources
[2025-08-09 11:38:35.893]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 2
[2025-08-09 11:38:35.898]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 2 potential leaks detected
[2025-08-09 11:38:35.903]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-09 11:38:35.908]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 11:38:35.913]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 11:38:35.923]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-09 11:38:35.928]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 11:38:35.942]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 11:38:35.954]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:35.960]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 11:38:35.969]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 11:38:35.985]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:35.989]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 11:38:35.993]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 11:38:36.000]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.004]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 11:38:36.008]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 11:38:36.012]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.017]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 11:38:36.021]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 11:38:36.026]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.032]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 11:38:36.036]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 11:38:36.041]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.045]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 11:38:36.050]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 11:38:36.054]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.070]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 11:38:36.084]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 11:38:36.090]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.099]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 11:38:36.106]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 11:38:36.116]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.125]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 11:38:36.135]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 11:38:36.142]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.153]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 11:38:36.158]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 11:38:36.162]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ImageManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.167]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ImageManagementViewModel (0 handlers)
[2025-08-09 11:38:36.171]  	[INFO]		[ImageManagementViewModel]	ImageManagementViewModel disposed
[2025-08-09 11:38:36.175]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ImageManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.180]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ImageManagementViewModel (0 handlers)
[2025-08-09 11:38:36.185]  	[INFO]		[ImageManagementViewModel]	ImageManagementViewModel disposed
[2025-08-09 11:38:36.190]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.194]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 11:38:36.199]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 11:38:36.203]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:38:36.207]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 11:38:36.211]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 11:38:36.217]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 9 tracked, 12 disposed, 1 cleanups
[2025-08-09 11:38:36.221]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 11:38:36.225]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 11:38:36.231]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 11:38:36.237]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 11:38:36.244]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-09 11:38:36.250]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 11:38:36 ===
