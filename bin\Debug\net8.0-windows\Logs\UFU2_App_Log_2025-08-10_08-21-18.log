=== UFU2 Application Session Started at 2025-08-10 08:21:18 ===
[2025-08-10 08:21:18.158]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-10 08:21:18.162]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-10 08:21:18.165]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-10 08:21:18.169]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-10 08:21:18.185]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-10 08:21:18.188]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-10 08:21:18.191]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-10 08:21:18.197]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-10 08:21:18.200]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-10 08:21:18.203]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-10 08:21:18.206]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-10 08:21:18.208]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-10 08:21:18.212]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-10 08:21:18.215]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-10 08:21:18.218]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-10 08:21:18.222]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-10 08:21:18.224]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-10 08:21:18.227]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-10 08:21:18.236]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-10 08:21:18.239]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-10 08:21:18.242]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-10 08:21:18.246]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-10 08:21:18.249]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-10 08:21:18.253]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-10 08:21:18.257]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-10 08:21:18.261]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-10 08:21:18.265]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-10 08:21:18.269]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-10 08:21:18.273]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-10 08:21:18.278]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-10 08:21:18.281]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-10 08:21:18.285]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-10 08:21:18.289]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-10 08:21:18.292]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-10 08:21:18.300]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-10 08:21:18.305]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-10 08:21:18.312]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-10 08:21:18.319]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-10 08:21:18.336]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 104.15MB working set
[2025-08-10 08:21:18.340]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-10 08:21:18.344]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-10 08:21:18.348]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-10 08:21:18.354]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-10 08:21:18.358]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-10 08:21:18.364]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-10 08:21:18.388]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-10 08:21:18.392]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-10 08:21:18.396]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-10 08:21:18.689]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-10 08:21:18.693]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-10 08:21:18.699]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-10 08:21:18.702]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-10 08:21:18.709]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638904072787081911 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-10 08:21:18.713]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-10 08:21:18.718]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:18.721]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-10 08:21:18.733]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-10 08:21:18.739]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-10 08:21:18.743]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-10 08:21:18.747]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-10 08:21:18.751]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-10 08:21:18.755]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-10 08:21:18.758]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-10 08:21:18.763]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-10 08:21:18.803]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-10 08:21:18.807]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-10 08:21:18.813]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-10 08:21:18.818]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-10 08:21:18.822]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-10 08:21:18.825]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-10 08:21:18.830]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-10 08:21:18.834]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-10 08:21:18.839]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-10 08:21:18.843]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-10 08:21:18.848]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-10 08:21:18.851]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-10 08:21:18.855]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-10 08:21:18.858]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-10 08:21:18.863]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-10 08:21:18.867]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-10 08:21:18.871]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-10 08:21:18.874]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-10 08:21:18.877]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-10 08:21:18.881]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-10 08:21:18.885]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-10 08:21:18.888]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-10 08:21:18.892]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-10 08:21:18.897]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-10 08:21:18.901]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-10 08:21:18.905]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-10 08:21:18.909]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-10 08:21:18.913]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-10 08:21:18.917]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-10 08:21:18.921]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-10 08:21:18.925]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-10 08:21:18.938]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-10 08:21:18.947]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-10 08:21:18.952]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-10 08:21:18.956]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-10 08:21:18.960]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-10 08:21:18.965]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-10 08:21:18.970]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-10 08:21:18.975]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-10 08:21:18.980]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-10 08:21:18.984]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-10 08:21:18.988]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-10 08:21:18.993]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-10 08:21:18.999]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-10 08:21:19.004]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-10 08:21:19.009]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-10 08:21:19.015]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-10 08:21:19.019]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-10 08:21:19.023]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-10 08:21:19.028]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-10 08:21:19.036]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-10 08:21:19.043]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-10 08:21:19.047]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-10 08:21:19.053]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-10 08:21:19.058]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 694ms
[2025-08-10 08:21:19.067]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-10 08:21:19.107]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-10 08:21:19.198]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-10 08:21:19.202]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-10 08:21:19.207]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-10 08:21:19.220]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-10 08:21:19.240]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-10 08:21:19.269]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 08:21:19.270]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 08:21:19.295]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 08:21:19.285]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 08:21:19.325]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 08:21:19.281]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 08:21:19.320]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 08:21:19.277]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-10 08:21:19.350]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 08:21:19.384]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 08:21:19.372]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 08:21:19.393]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-10 08:21:19.397]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 08:21:19.403]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-10 08:21:19.408]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 08:21:19.414]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-10 08:21:19.424]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-10 08:21:19.430]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 08:21:19.449]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:19.454]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 08:21:19.468]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 08:21:19.474]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 08:21:19.479]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 08:21:19.483]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 08:21:19.486]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 08:21:19.490]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 08:21:19.494]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 08:21:19.505]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 6, target version: 6
[2025-08-10 08:21:19.509]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-10 08:21:19.517]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 08:21:19.522]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:19.532]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 08:21:19.573]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 08:21:19.578]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 08:21:19.582]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 08:21:19.586]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 08:21:19.590]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 08:21:19.594]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 08:21:19.598]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 08:21:19.602]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 08:21:19.607]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 08:21:19.612]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 08:21:19.617]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 08:21:19.621]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 08:21:19.626]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 08:21:19.630]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 08:21:19.634]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 08:21:19.638]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 08:21:19.642]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 08:21:19.646]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 08:21:19.650]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 08:21:19.657]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 08:21:19.663]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 08:21:19.668]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 08:21:19.673]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 08:21:19.703]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 08:21:19.724]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 08:21:19.741]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 08:21:19.749]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 08:21:19.758]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 08:21:19.785]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:19.796]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-10 08:21:19.810]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 08:21:19.816]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:19.824]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 08:21:19.829]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 08:21:19.836]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 08:21:19.841]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 08:21:19.848]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 08:21:19.851]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 08:21:19.855]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 08:21:19.858]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 08:21:19.863]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 6, target version: 6
[2025-08-10 08:21:19.866]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-10 08:21:19.870]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 08:21:19.874]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:19.878]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-10 08:21:19.883]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-10 08:21:19.887]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-10 08:21:19.891]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-10 08:21:19.894]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-10 08:21:19.898]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-10 08:21:19.902]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-10 08:21:19.906]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-10 08:21:19.910]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-10 08:21:19.914]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-10 08:21:19.918]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-10 08:21:19.922]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 08:21:19.927]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 08:21:19.931]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-10 08:21:19.935]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-10 08:21:19.940]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-10 08:21:19.944]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-10 08:21:19.948]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 08:21:19.951]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 08:21:19.955]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:19.959]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-10 08:21:19.963]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 08:21:19.966]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:19.970]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 08:21:19.973]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 08:21:19.977]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 08:21:19.981]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 08:21:19.984]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 08:21:19.988]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 08:21:19.992]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 08:21:19.997]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 08:21:20.000]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 6, target version: 6
[2025-08-10 08:21:20.004]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-10 08:21:20.008]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 08:21:20.013]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:20.017]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-10 08:21:20.021]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-10 08:21:20.025]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-10 08:21:20.029]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-10 08:21:20.034]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-10 08:21:20.038]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-10 08:21:20.045]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-10 08:21:20.049]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-10 08:21:20.053]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-10 08:21:20.057]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 08:21:20.062]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 08:21:20.066]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-10 08:21:20.071]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-10 08:21:20.076]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-10 08:21:20.080]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-10 08:21:20.085]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 08:21:20.090]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 08:21:20.094]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:20.097]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 08:21:20.101]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:20.105]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 08:21:20.109]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 08:21:20.114]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 08:21:20.118]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 08:21:20.122]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 08:21:20.125]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 08:21:20.130]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 08:21:20.134]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 08:21:20.138]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 08:21:20.142]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 08:21:20.146]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 08:21:20.150]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 08:21:20.155]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 08:21:20.159]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 08:21:20.163]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 08:21:20.170]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 08:21:20.176]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 08:21:20.183]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 08:21:20.189]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 08:21:20.217]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 08:21:20.295]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 08:21:20.300]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 08:21:20.305]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 08:21:20.310]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 08:21:20.315]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 08:21:20.319]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 08:21:20.324]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 08:21:20.328]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 08:21:20.332]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-10 08:21:20.336]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-10 08:21:20.341]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-10 08:21:20.346]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-10 08:21:20.351]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-10 08:21:20.355]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-10 08:21:20.359]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-10 08:21:20.363]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-10 08:21:20.367]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-10 08:21:20.371]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-10 08:21:20.376]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-10 08:21:20.380]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-10 08:21:20.384]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-10 08:21:20.388]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-10 08:21:20.392]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-10 08:21:20.397]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-10 08:21:20.401]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-10 08:21:20.405]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-10 08:21:20.409]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-10 08:21:20.413]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-10 08:21:20.417]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-10 08:21:20.422]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-10 08:21:20.427]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:20.442]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-10 08:21:20.446]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-10 08:21:20.450]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-10 08:21:20.454]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-10 08:21:20.459]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-10 08:21:20.463]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-10 08:21:20.467]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-10 08:21:20.471]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-10 08:21:20.475]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-10 08:21:20.480]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-10 08:21:20.484]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-10 08:21:20.488]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-10 08:21:20.492]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-10 08:21:20.497]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:20.501]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-10 08:21:20.505]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:20.510]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:20.515]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:20.519]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-10 08:21:20.525]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:20.530]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:20.534]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-10 08:21:20.541]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-10 08:21:20.582]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-10 08:21:20.590]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:20.622]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 08:21:20.626]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 08:21:20.632]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:20.651]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 08:21:20.655]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 08:21:20.659]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-10 08:21:20.663]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-10 08:21:20.668]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 08:21:20.676]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-10 08:21:20.833]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 08:21:20.880]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 08:21:20.885]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-10 08:21:20.890]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 08:21:20.939]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-10 08:21:20.948]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 08:21:20.953]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-10 08:21:20.959]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-10 08:21:20.967]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-10 08:21:20.972]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:20.981]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 2 clients, 2 activities
[2025-08-10 08:21:20.993]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-10 08:21:20.999]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 6, Tables: 12, Indexes: 52
[2025-08-10 08:21:21.004]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-10 08:21:21.011]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-10 08:21:21.016]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-10 08:21:21.021]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-10 08:21:21.025]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-10 08:21:21.030]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-10 08:21:21.035]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-10 08:21:21.039]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-10 08:21:21.043]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-10 08:21:21.048]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-10 08:21:21.053]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-10 08:21:21.057]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-10 08:21:21.062]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-10 08:21:21.066]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-10 08:21:21.070]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-10 08:21:21.078]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-10 08:21:21.083]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-10 08:21:21.089]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.117]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-10 08:21:21.122]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.140]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.150]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-10 08:21:21.155]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.160]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.165]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-10 08:21:21.169]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.174]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.179]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-10 08:21:21.184]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.189]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.195]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-10 08:21:21.199]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.204]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.209]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-10 08:21:21.213]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.218]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.223]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-10 08:21:21.227]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.231]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-10 08:21:21.237]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 153ms
[2025-08-10 08:21:21.242]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-10 08:21:21.254]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-10 08:21:21.258]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 17ms
[2025-08-10 08:21:21.263]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-10 08:21:21.269]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-10 08:21:21.273]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 11ms
[2025-08-10 08:21:21.278]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-10 08:21:21.285]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:21.290]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-10 08:21:21.296]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:21.302]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-10 08:21:21.307]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:21.312]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-10 08:21:21.318]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 08:21:21.341]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-10 08:21:21.348]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-10 08:21:21.355]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 77ms
[2025-08-10 08:21:21.363]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-10 08:21:21.378]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.395]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-10 08:21:21.403]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.409]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.417]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-10 08:21:21.424]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.428]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.433]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-10 08:21:21.437]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.441]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.445]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-10 08:21:21.449]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.453]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.458]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-10 08:21:21.462]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.466]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.470]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-10 08:21:21.474]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.478]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.482]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-10 08:21:21.486]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.490]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.494]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-10 08:21:21.498]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.502]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.506]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-10 08:21:21.510]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.514]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 08:21:21.519]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-10 08:21:21.523]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 08:21:21.527]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-10 08:21:21.531]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 169ms
[2025-08-10 08:21:21.536]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-10 08:21:21.541]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-10 08:21:21.546]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-10 08:21:21.550]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-10 08:21:21.554]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-10 08:21:21.558]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-10 08:21:21.563]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-10 08:21:21.567]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-10 08:21:21.571]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-10 08:21:21.575]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-10 08:21:21.579]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-10 08:21:21.584]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-10 08:21:21.588]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-10 08:21:21.593]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-10 08:21:21.597]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-10 08:21:21.602]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-10 08:21:21.606]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-10 08:21:21.696]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-10 08:21:21.701]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-10 08:21:21.705]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-10 08:21:21.709]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-10 08:21:21.717]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 08:21:21.722]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:21.728]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 08:21:21.732]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:21.738]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 08:21:21.742]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:21.801]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-10 08:21:21.805]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-10 08:21:22.012]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-10 08:21:22.015]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 1181.326ms
[2025-08-10 08:21:22.018]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-10 08:21:22.022]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:22.025]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-10 08:21:22.036]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-10 08:21:22.033]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:22.044]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:22.049]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:22.053]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:22.057]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:22.061]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:22.040]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 15ms
[2025-08-10 08:21:22.026]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-10 08:21:23.022]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2189.201ms
[2025-08-10 08:21:23.027]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:23.721]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-10 08:21:23.932]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-10 08:21:27.332]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:27.337]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:27.341]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:27.348]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:27.352]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:27.356]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:27.427]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-10 08:21:27.435]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-10 08:21:27.441]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-10 08:21:27.473]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-10 08:21:27.478]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_27303998_638904072874780814 (BaseViewModel) for NPersonalViewModel
[2025-08-10 08:21:27.482]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-10 08:21:27.486]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:27.490]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_44409397_638904072874908914 (BaseViewModel) for PersonalInformationViewModel
[2025-08-10 08:21:27.495]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-10 08:21:27.500]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:27.507]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-10 08:21:27.512]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_64140254_638904072875129252 (BaseViewModel) for ContactInformationViewModel
[2025-08-10 08:21:27.517]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-10 08:21:27.521]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:27.525]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-10 08:21:27.531]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-10 08:21:27.554]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-10 08:21:27.620]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-10 08:21:27.665]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_39958021_638904072876658476 (BaseViewModel) for NewClientViewModel
[2025-08-10 08:21:27.669]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-10 08:21:27.673]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:27.678]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_24077873_638904072876782911 (BaseViewModel) for PersonalInformationViewModel
[2025-08-10 08:21:27.682]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-10 08:21:27.686]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:27.690]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-10 08:21:27.696]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_15374270_638904072876964898 (BaseViewModel) for ContactInformationViewModel
[2025-08-10 08:21:27.700]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-10 08:21:27.704]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:27.709]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-10 08:21:27.713]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-10 08:21:27.719]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_4150710_638904072877189888 (BaseViewModel) for ActivityManagementViewModel
[2025-08-10 08:21:27.723]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-10 08:21:27.727]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:27.732]  	[DEBUG]		[ActivityManagementViewModel]	Initialized default activity status for all activity types
[2025-08-10 08:21:27.737]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-10 08:21:27.742]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-10 08:21:27.747]  	[DEBUG]		[ActivityManagementViewModel]	CPI location selections synchronized for MainCommercial: Wilaya=, Daira=
[2025-08-10 08:21:27.752]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-10 08:21:27.757]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_37356392_638904072877573097 (BaseViewModel) for NotesManagementViewModel
[2025-08-10 08:21:27.761]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-10 08:21:27.765]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:27.770]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-10 08:21:27.774]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-10 08:21:27.779]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-10 08:21:27.783]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-10 08:21:27.787]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-10 08:21:27.793]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-10 08:21:27.797]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-10 08:21:27.801]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-10 08:21:27.805]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-10 08:21:27.809]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-10 08:21:27.813]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-10 08:21:27.817]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-10 08:21:27.821]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-10 08:21:27.826]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 384ms (Success: True)
[2025-08-10 08:21:27.830]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 396ms (Success: True)
[2025-08-10 08:21:27.835]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x560 (Height-based width calculation)
[2025-08-10 08:21:27.859]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-10 08:21:27.876]  	[DEBUG]		[NActivityDetailView]	Activity status changed to: قيد
[2025-08-10 08:21:27.888]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 08:21:27.899]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 2, misses: 1)
[2025-08-10 08:21:27.901]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-10 08:21:27.906]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-10 08:21:27.912]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-10 08:21:27.929]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 30ms
[2025-08-10 08:21:27.945]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 47ms
[2025-08-10 08:21:28.408]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-10 08:21:28.415]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-10 08:21:28.577]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 968.0452ms
[2025-08-10 08:21:28.630]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:28.634]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1143.9867ms
[2025-08-10 08:21:28.639]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:28.644]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1131.473ms
[2025-08-10 08:21:28.648]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:28.685]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1020.0366ms
[2025-08-10 08:21:28.702]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:28.748]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1069.8004ms
[2025-08-10 08:21:28.753]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:28.765]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1068.8476ms
[2025-08-10 08:21:28.769]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:28.809]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1090.9924ms
[2025-08-10 08:21:28.832]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:28.879]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1122.5691ms
[2025-08-10 08:21:28.937]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:29.014]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:29.019]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:29.026]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:29.034]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:29.040]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:29.047]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:29.302]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:29.306]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:29.309]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:29.314]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:29.317]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:29.321]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:29.334]  	[DEBUG]		[ActivityManagementViewModel]	CPI location selections synchronized for MainCommercial: Wilaya=, Daira=
[2025-08-10 08:21:29.627]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2018.5554ms
[2025-08-10 08:21:29.633]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:29.644]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2153.4002ms
[2025-08-10 08:21:29.648]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:29.653]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2140.1591ms
[2025-08-10 08:21:29.657]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:29.708]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2042.4833ms
[2025-08-10 08:21:29.712]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:29.755]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2077.3579ms
[2025-08-10 08:21:29.759]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:29.772]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2075.5932ms
[2025-08-10 08:21:29.776]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:29.878]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2159.4182ms
[2025-08-10 08:21:29.882]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:29.993]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2235.9874ms
[2025-08-10 08:21:29.998]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:32.480]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbersCollection(1)
[2025-08-10 08:21:34.766]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:34.807]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:34.811]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:34.815]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:34.820]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:34.824]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:34.845]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:34.849]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:34.854]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:34.858]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:34.863]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:34.868]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:35.051]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:35.056]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:35.059]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:35.064]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:35.067]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:35.071]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:35.393]  	[DEBUG]		[NActivityDetailView]	Activity status changed to: معدل
[2025-08-10 08:21:35.401]  	[INFO]		[NActivityDetailView]	Opening ActivityStatusUpdateDialog - Context: StatusChange, Status: معدل
[2025-08-10 08:21:35.433]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-10 08:21:35.439]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityStatusUpdateDialogViewModel_26712487_638904072954394886 (BaseViewModel) for ActivityStatusUpdateDialogViewModel
[2025-08-10 08:21:35.443]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	BaseViewModel memory management initialized for ActivityStatusUpdateDialogViewModel
[2025-08-10 08:21:35.448]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:35.452]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized
[2025-08-10 08:21:35.457]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with ValidationService integration
[2025-08-10 08:21:35.462]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with existing data - Status: معدل, Type: MainCommercial
[2025-08-10 08:21:35.466]  	[DEBUG]		[ActivityStatusUpdateDialog]	ActivityStatusUpdateDialog initialized successfully
[2025-08-10 08:21:35.549]  	[INFO]		[NActivityDetailView]	Opening ActivityStatusUpdateDialog - Context: StatusChange, Status: معدل
[2025-08-10 08:21:35.556]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-10 08:21:35.561]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityStatusUpdateDialogViewModel_25017536_638904072955615585 (BaseViewModel) for ActivityStatusUpdateDialogViewModel
[2025-08-10 08:21:35.566]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	BaseViewModel memory management initialized for ActivityStatusUpdateDialogViewModel
[2025-08-10 08:21:35.570]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:35.574]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized
[2025-08-10 08:21:35.578]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with ValidationService integration
[2025-08-10 08:21:35.582]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with existing data - Status: معدل, Type: MainCommercial
[2025-08-10 08:21:35.586]  	[DEBUG]		[ActivityStatusUpdateDialog]	ActivityStatusUpdateDialog initialized successfully
[2025-08-10 08:21:35.593]  	[ERROR]		[NActivityDetailView]	Error opening activity status update dialog: DialogHost is already open.
[2025-08-10 08:21:35.717]  	[ERROR]		[NActivityDetailView]	Exception in LogException: DialogHost is already open.
[2025-08-10 08:21:35.725]  	[ERROR]		[NActivityDetailView]	Context - Source: NActivityDetailView, Operation: LogException, Timestamp: 2025-08-10 08:21:35.597, ElapsedMs: 0, UserAgent: XLABZ
[2025-08-10 08:21:35.730]  	[ERROR]		[NActivityDetailView]	Stack trace:    at MaterialDesignThemes.Wpf.DialogHost.ShowInternal(Object content, DialogOpenedEventHandler openedEventHandler, DialogClosingEventHandler closingEventHandler, DialogClosedEventHandler closedEventHandler)
   at MaterialDesignThemes.Wpf.DialogHost.Show(Object content, Object dialogIdentifier, DialogOpenedEventHandler openedEventHandler, DialogClosingEventHandler closingEventHandler, DialogClosedEventHandler closedEventHandler)
   at UFU2.Views.NewClient.NActivityDetailView.OpenActivityStatusUpdateDialog(NewClientViewModel viewModel, String context) in E:\UserFiles\Projects\UFU2\Views\NewClient\NActivityDetailView.xaml.cs:line 433
[2025-08-10 08:21:35.736]  	[INFO]		[ErrorManager]	Displaying user error toast: خطأ - حدث خطأ أثناء فتح نافذة تحديث معلومات النشاط
[2025-08-10 08:21:35.742]  	[DEBUG]		[ToastService]	Displaying Error toast: خطأ - حدث خطأ أثناء فتح نافذة تحديث معلومات النشاط
[2025-08-10 08:21:35.753]  	[INFO]		[ToastNotification]	Toast notification created: Error - خطأ
[2025-08-10 08:21:35.769]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 08:21:35.775]  	[INFO]		[ToastService]	Desktop toast displayed: Error - خطأ
[2025-08-10 08:21:35.892]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Processed 3 Normal priority property notifications
[2025-08-10 08:21:35.898]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Processed 3 Normal priority property notifications
[2025-08-10 08:21:35.967]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:35.977]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:35.982]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:35.988]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:35.992]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:35.996]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:36.029]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:36.038]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:36.042]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:36.046]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:36.051]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:36.056]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:36.095]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:36.099]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:36.103]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:36.107]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:36.112]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:36.116]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:36.439]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 977.1118ms
[2025-08-10 08:21:36.443]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:36.575]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 992.5851ms
[2025-08-10 08:21:36.580]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:37.592]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2010.179ms
[2025-08-10 08:21:37.598]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:38.451]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2989.0219ms
[2025-08-10 08:21:38.455]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:39.812]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:39.818]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:39.823]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:39.827]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:39.833]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:39.837]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:39.922]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:39.926]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:39.930]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:39.934]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:39.938]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:39.942]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:40.444]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: UpdateDate(1), ActivityStatus(1), ActivityType(1)
[2025-08-10 08:21:40.571]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: UpdateDate(1), ActivityStatus(1), ActivityType(1)
[2025-08-10 08:21:43.211]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:43.216]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:43.220]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:43.224]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:43.228]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:43.232]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:44.774]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:44.779]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:44.782]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:44.786]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:44.791]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:44.795]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:44.886]  	[INFO]		[ToastNotification]	Toast notification closed: Error - خطأ
[2025-08-10 08:21:44.890]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:44.895]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:44.899]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:44.903]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:44.906]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:44.910]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:45.195]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:45.200]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:45.205]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:45.209]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:45.218]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:45.225]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:46.345]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:46.353]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:46.357]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:46.365]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:46.370]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:46.376]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:46.664]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-10 08:21:46.670]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	Activity update dialog cancelled
[2025-08-10 08:21:46.675]  	[DEBUG]		[ActivityStatusUpdateDialog]	Activity status update cancelled, keeping original values
[2025-08-10 08:21:46.683]  	[INFO]		[NActivityDetailView]	Activity status update cancelled, default values applied
[2025-08-10 08:21:46.779]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:46.838]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:46.848]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:46.853]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:46.857]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:46.862]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:49.226]  	[INFO]		[NActivityDetailView]	Opening ActivityStatusUpdateDialog - Context: StatusChange, Status: معدل
[2025-08-10 08:21:49.234]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-10 08:21:49.239]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityStatusUpdateDialogViewModel_20130595_638904073092395044 (BaseViewModel) for ActivityStatusUpdateDialogViewModel
[2025-08-10 08:21:49.243]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	BaseViewModel memory management initialized for ActivityStatusUpdateDialogViewModel
[2025-08-10 08:21:49.247]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:49.251]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized
[2025-08-10 08:21:49.255]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with ValidationService integration
[2025-08-10 08:21:49.259]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with existing data - Status: معدل, Type: MainCommercial
[2025-08-10 08:21:49.263]  	[DEBUG]		[ActivityStatusUpdateDialog]	ActivityStatusUpdateDialog initialized successfully
[2025-08-10 08:21:49.463]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Processed 4 Normal priority property notifications
[2025-08-10 08:21:49.484]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:49.490]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:49.498]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:49.503]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:49.508]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:49.513]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:49.545]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:49.555]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:49.564]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:49.569]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:49.573]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:49.577]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:49.835]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:49.905]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:49.926]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:49.955]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:49.992]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:50.014]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:50.118]  	[INFO]		[ToastNotification]	Toast notification closed: Error - خطأ
[2025-08-10 08:21:50.244]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 5, Time since interaction: 985.6513ms
[2025-08-10 08:21:50.249]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:50.745]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:50.752]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:50.756]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:50.761]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:50.765]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:50.768]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:50.823]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-10 08:21:50.828]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	Activity update dialog cancelled
[2025-08-10 08:21:50.833]  	[DEBUG]		[ActivityStatusUpdateDialog]	Activity status update cancelled, keeping original values
[2025-08-10 08:21:50.843]  	[INFO]		[NActivityDetailView]	Activity status update cancelled - Context: StatusChange, keeping existing values
[2025-08-10 08:21:50.950]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:50.955]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:50.959]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:50.963]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:50.967]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:50.971]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:51.282]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 5, Time since interaction: 2023.2735ms
[2025-08-10 08:21:51.286]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:51.671]  	[DEBUG]		[ActivityManagementViewModel]	CPI location selections synchronized for SecondaryCommercial: Wilaya=, Daira=
[2025-08-10 08:21:51.675]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: SecondaryCommercial
[2025-08-10 08:21:51.679]  	[INFO]		[NewClientViewModel]	Switched to activity tab: SecondaryCommercial
[2025-08-10 08:21:51.695]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:51.699]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:51.703]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:51.706]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:51.710]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:51.714]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:51.724]  	[DEBUG]		[NActivityDetailView]	Activity status changed to: قيد
[2025-08-10 08:21:51.731]  	[DEBUG]		[ActivityManagementViewModel]	Processed 7 Normal priority property notifications
[2025-08-10 08:21:51.759]  	[DEBUG]		[NewClientViewModel]	Processed 7 Normal priority property notifications
[2025-08-10 08:21:51.909]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: SecondaryCommercial
[2025-08-10 08:21:52.210]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 9, Time since interaction: 479.303ms
[2025-08-10 08:21:52.215]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:52.226]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 554.9262ms
[2025-08-10 08:21:52.230]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:52.698]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 9, Batched: 9, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: G12DisplayText(2), BISDisplayText(2), SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1)
[2025-08-10 08:21:52.702]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:52.707]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:52.711]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:52.715]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:52.720]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:52.724]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:52.750]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 7, Batched: 7, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1)
[2025-08-10 08:21:52.828]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:52.865]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:52.891]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:52.912]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:52.969]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:52.987]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.005]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.009]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.014]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.018]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.021]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.025]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.029]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.033]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.037]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.041]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.045]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.049]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.053]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.057]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.061]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.067]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.073]  	[DEBUG]		[NActivityDetailView]	Skipping status change tracking - programmatic ComboBox change detected
[2025-08-10 08:21:53.078]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.082]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.085]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.089]  	[DEBUG]		[NActivityDetailView]	Skipping status change tracking - programmatic ComboBox change detected
[2025-08-10 08:21:53.094]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.097]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.101]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.105]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.109]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.113]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.116]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.120]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.124]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.128]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.131]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.135]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.139]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.143]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.147]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.150]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.154]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.158]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.162]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 08:21:53.166]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.169]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.173]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-10 08:21:53.177]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:21:53.181]  	[DEBUG]		[ActivityManagementViewModel]	CPI location selections synchronized for Craft: Wilaya=, Daira=
[2025-08-10 08:21:53.184]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: Craft
[2025-08-10 08:21:53.189]  	[INFO]		[NewClientViewModel]	Switched to activity tab: Craft
[2025-08-10 08:21:53.220]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: Craft
[2025-08-10 08:21:53.229]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:53.234]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:53.240]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:53.246]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:53.250]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:53.254]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:54.244]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	PropertyChanged Performance - Total: 5, Batched: 5, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: UpdateDate(2), UpdateNote(1), ActivityStatus(1), ActivityType(1)
[2025-08-10 08:21:55.286]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 9, Time since interaction: 2123.3849ms
[2025-08-10 08:21:55.290]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:55.349]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 7, Time since interaction: 2190.4412ms
[2025-08-10 08:21:55.353]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 08:21:57.704]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 9, Batched: 9, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: G12DisplayText(2), BISDisplayText(2), SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1)
[2025-08-10 08:21:57.768]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 7, Batched: 7, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1)
[2025-08-10 08:21:58.413]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.418]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:58.421]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.425]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:58.430]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.434]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:58.515]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.521]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:58.525]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.531]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:58.535]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-10 08:21:58.539]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.545]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-10 08:21:58.569]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-10 08:21:58.573]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-10 08:21:58.578]  	[INFO]		[MainWindow]	Application closing
[2025-08-10 08:21:58.587]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_65519169_638904073185878404 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-10 08:21:58.596]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-10 08:21:58.600]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 08:21:58.604]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 08:21:58.608]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 08:21:58.614]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-10 08:21:58.623]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-10 08:21:58.628]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-10 08:21:58.633]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-10 08:21:58.701]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-10 08:21:58.723]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-10 08:21:58.778]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.782]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:58.787]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.790]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:58.796]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:58.800]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:59.611]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 996.6847ms
[2025-08-10 08:21:59.615]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 08:21:59.661]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:59.666]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:59.670]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:59.674]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 08:21:59.679]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 08:21:59.683]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 08:21:59.737]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-10 08:21:59.741]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-10 08:21:59.747]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-10 08:21:59.758]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-10 08:21:59.763]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:21:59.767]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-10 08:21:59.771]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-10 08:21:59.775]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:21:59.779]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 08:21:59.783]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 08:21:59.787]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_65519169_638904073185878404
[2025-08-10 08:21:59.791]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:21:59.795]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-10 08:21:59.800]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-10 08:21:59.803]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:21:59.807]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 08:21:59.811]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 08:21:59.815]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-10 08:21:59.828]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-10 08:21:59.832]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-10 08:21:59.836]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-10 08:21:59.840]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-10 08:21:59.846]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 08:21:59.850]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 08:21:59.854]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:21:59.858]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-10 08:21:59.864]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-10 08:21:59.868]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:21:59.873]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 08:21:59.880]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 08:21:59.884]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_28090709_638904072787081911
[2025-08-10 08:21:59.888]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:21:59.894]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-10 08:21:59.898]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-10 08:21:59.902]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:21:59.906]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 08:21:59.911]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 08:21:59.915]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-10 08:21:59.919]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-10 08:21:59.923]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-10 08:21:59.928]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-10 08:21:59.932]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-10 08:21:59.937]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-10 08:21:59.946]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-10 08:21:59.950]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-10 08:21:59.954]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-10 08:21:59.958]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-10 08:21:59.980]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:21:59.988]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.003]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.007]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.015]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.022]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.035]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.042]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.054]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.061]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.067]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 08:22:00.079]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-10 08:22:00.084]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-10 08:22:00.090]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-10 08:22:00.101]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-10 08:22:00.107]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-10 08:22:00.119]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-10 08:22:00.125]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 08:22:00.132]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 08:22:00.136]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 08:22:00.140]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 08:22:00.148]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-10 08:22:00.153]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-10 08:22:00.158]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-10 08:22:00.165]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-10 08:22:00.170]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-10 08:22:00.175]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-10 08:22:00.182]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-10 08:22:00.187]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-10 08:22:00.192]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-10 08:22:00.201]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-10 08:22:00.207]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-10 08:22:00.214]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-10 08:22:00.221]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 50.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-10 08:22:00.225]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-10 08:22:00.232]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-10 08:22:00.236]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-10 08:22:00.241]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 66.7%
[2025-08-10 08:22:00.246]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-10 08:22:00.251]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-10 08:22:00.255]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 08:22:00.260]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-10 08:22:00.264]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-10 08:22:00.269]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-10 08:22:00.273]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-10 08:22:00.278]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-10 08:22:00.283]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-10 08:22:00.287]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-10 08:22:00.291]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-10 08:22:00.296]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-10 08:22:00.301]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 08:22:00.305]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-10 08:22:00.329]  	[INFO]		[ResourceManager]	Generated memory leak report: 11 alive resources, 0 dead resources
[2025-08-10 08:22:00.341]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 2
[2025-08-10 08:22:00.347]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 2 potential leaks detected
[2025-08-10 08:22:00.352]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-10 08:22:00.359]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-10 08:22:00.366]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-10 08:22:00.373]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-10 08:22:00.382]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-10 08:22:00.388]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-10 08:22:00.399]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-10 08:22:00.420]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-10 08:22:00.436]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.440]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.446]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.451]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.456]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.461]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.466]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-10 08:22:00.471]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-10 08:22:00.475]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.480]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.484]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.488]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_44409397_638904072874908914
[2025-08-10 08:22:00.492]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-10 08:22:00.497]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-10 08:22:00.501]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.505]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.510]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.515]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_24077873_638904072876782911
[2025-08-10 08:22:00.519]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-10 08:22:00.523]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-10 08:22:00.528]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.532]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.536]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.543]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.548]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.553]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.557]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.563]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.567]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.572]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-10 08:22:00.577]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-10 08:22:00.581]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.586]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.590]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.596]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_15374270_638904072876964898
[2025-08-10 08:22:00.603]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-10 08:22:00.618]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-10 08:22:00.641]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.647]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.651]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.657]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_64140254_638904072875129252
[2025-08-10 08:22:00.663]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-10 08:22:00.667]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-10 08:22:00.673]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.682]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.690]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.696]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.702]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.707]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.713]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.723]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.729]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.734]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:22:00.738]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-10 08:22:00.743]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.748]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.752]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-10 08:22:00.756]  	[DEBUG]		[ResourceManager]	Unregistered resource: NPersonalViewModel_27303998_638904072874780814
[2025-08-10 08:22:00.762]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:22:00.766]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-10 08:22:00.770]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.774]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.779]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-10 08:22:00.784]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.788]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.794]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-10 08:22:00.799]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.803]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-10 08:22:00.807]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.815]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:22:00.819]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-10 08:22:00.824]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.829]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.833]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-10 08:22:00.838]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityManagementViewModel_4150710_638904072877189888
[2025-08-10 08:22:00.844]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:22:00.848]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-10 08:22:00.852]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.857]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.863]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-10 08:22:00.868]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-10 08:22:00.873]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-10 08:22:00.879]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.883]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-10 08:22:00.888]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-10 08:22:00.892]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.898]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:22:00.903]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-10 08:22:00.908]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.914]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.919]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-10 08:22:00.923]  	[DEBUG]		[ResourceManager]	Unregistered resource: NotesManagementViewModel_37356392_638904072877573097
[2025-08-10 08:22:00.929]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:22:00.933]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-10 08:22:00.937]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-10 08:22:00.941]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:00.946]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-10 08:22:00.950]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-10 08:22:00.954]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.958]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-10 08:22:00.964]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-10 08:22:00.968]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-10 08:22:00.972]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-10 08:22:00.977]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-10 08:22:00.981]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-10 08:22:00.986]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:22:00.990]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-10 08:22:00.996]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-10 08:22:01.001]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:01.005]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-10 08:22:01.011]  	[DEBUG]		[ResourceManager]	Unregistered resource: NewClientViewModel_39958021_638904072876658476
[2025-08-10 08:22:01.016]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-10 08:22:01.020]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-10 08:22:01.025]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-10 08:22:01.031]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:01.034]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-10 08:22:01.039]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Starting disposal of ActivityStatusUpdateDialogViewModel resources
[2025-08-10 08:22:01.044]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Dialog event handlers cleared successfully
[2025-08-10 08:22:01.049]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposal completed successfully
[2025-08-10 08:22:01.052]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	All batched notifications flushed
[2025-08-10 08:22:01.056]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Starting disposal of ActivityStatusUpdateDialogViewModel resources
[2025-08-10 08:22:01.061]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Dialog event handlers cleared successfully
[2025-08-10 08:22:01.065]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposal completed successfully
[2025-08-10 08:22:01.069]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	All batched notifications flushed
[2025-08-10 08:22:01.074]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Starting disposal of ActivityStatusUpdateDialogViewModel resources
[2025-08-10 08:22:01.082]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Dialog event handlers cleared successfully
[2025-08-10 08:22:01.086]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposal completed successfully
[2025-08-10 08:22:01.090]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	All batched notifications flushed
[2025-08-10 08:22:01.096]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Starting disposal of ActivityStatusUpdateDialogViewModel resources
[2025-08-10 08:22:01.101]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Dialog event handlers cleared successfully
[2025-08-10 08:22:01.105]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposal completed successfully
[2025-08-10 08:22:01.111]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	All batched notifications flushed
[2025-08-10 08:22:01.116]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (3 resources, 0 event subscriptions)
[2025-08-10 08:22:01.120]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-10 08:22:01.125]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Memory management cleanup completed
[2025-08-10 08:22:01.131]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:01.135]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-10 08:22:01.139]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityStatusUpdateDialogViewModel_26712487_638904072954394886
[2025-08-10 08:22:01.147]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (3 resources, 0 event subscriptions)
[2025-08-10 08:22:01.152]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-10 08:22:01.157]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Memory management cleanup completed
[2025-08-10 08:22:01.162]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:01.166]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-10 08:22:01.170]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityStatusUpdateDialogViewModel_20130595_638904073092395044
[2025-08-10 08:22:01.175]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (3 resources, 0 event subscriptions)
[2025-08-10 08:22:01.180]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-10 08:22:01.184]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Memory management cleanup completed
[2025-08-10 08:22:01.188]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:01.193]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-10 08:22:01.197]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityStatusUpdateDialogViewModel_25017536_638904072955615585
[2025-08-10 08:22:01.201]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (3 resources, 0 event subscriptions)
[2025-08-10 08:22:01.205]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-10 08:22:01.210]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Memory management cleanup completed
[2025-08-10 08:22:01.215]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 08:22:01.219]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-10 08:22:01.223]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 13 tracked, 18 disposed, 1 cleanups
[2025-08-10 08:22:01.229]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-10 08:22:01.234]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-10 08:22:01.239]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-10 08:22:01.243]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-10 08:22:01.248]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-10 08:22:01.252]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-10 08:22:01.256]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-10 08:22:01.262]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-10 08:22:01.266]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-10 08:22:01.270]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-10 08:22:01.274]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-10 08:22:01.279]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-10 08:22:01.285]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-10 08:22:01.290]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-10 08:22:01.295]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-10 08:22:01.299]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-10 08:22:01.304]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 94.1%, Total validations: 68
[2025-08-10 08:22:01.308]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-10 08:22:01.314]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-10 08:22:01.318]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-10 08:22:01 ===
