using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Caching.Memory;
using UFU2.Common;
using UFU2.Common.Extensions;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Centralized validation service providing consistent validation logic with Arabic error messages.
    /// Eliminates validation code duplication across ViewModels and ensures consistent user experience.
    /// Supports common validation patterns: required fields, length limits, format validation, and business rules.
    /// Integrates with existing IDataErrorInfo pattern and UFU2 architectural standards.
    /// Enhanced with caching for improved performance and reduced computation overhead.
    ///
    /// IMPLEMENTATION STATUS: ✅ CREATED - Centralized validation service (Task 3.1)
    /// INTEGRATION: Used by ViewModels to replace manual validation logic
    /// BACKUP STRATEGY: ViewModels will be backed up before refactoring to use this service
    /// </summary>
    public class ValidationService : ICacheableService, IDisposable
    {
        #region Private Fields

        // Compiled regex patterns for performance
        private static readonly Regex PhoneDigitsRegex = new Regex(@"\D", RegexOptions.Compiled);
        private static readonly Regex DateFormatRegex = new Regex(@"^(\d{2})/(\d{2})/(\d{4})$", RegexOptions.Compiled);

        // Validation result cache for improved performance
        private readonly IMemoryCache _validationCache;

        // Cache statistics for monitoring
        private int _cacheHits = 0;
        private int _cacheMisses = 0;
        private bool _disposed = false;

        #endregion

        #region ICacheableService Implementation

        /// <summary>
        /// Gets the service name for cache management identification.
        /// </summary>
        public string ServiceName => "ValidationService";

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ValidationService class.
        /// </summary>
        public ValidationService()
        {
            // Initialize validation result cache with 1-hour expiration and 500 item limit
            _validationCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 500,
                CompactionPercentage = 0.25
            });
        }

        #endregion

        #region Required Field Validation

        /// <summary>
        /// Validates that a field is not null, empty, or whitespace.
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="fieldName">The name of the field for error messaging</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateRequired(string? value, string fieldName = "")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(value))
                {
                    return fieldName switch
                    {
                        "NoteContent" => ValidationMessages.NoteContentRequired,
                        "ActivityDescription" => ValidationMessages.ActivityDescriptionRequired,
                        "UpdateDate" => ValidationMessages.UpdateDateRequired,
                        "NameFr" => ValidationMessages.NameFrRequired,
                        _ => ValidationMessages.RequiredField
                    };
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating required field {fieldName}: {ex.Message}", "ValidationService");
                return null;
            }
        }

        /// <summary>
        /// Validates that a note content field is not empty.
        /// Specific validation for note content with appropriate Arabic messages.
        /// </summary>
        /// <param name="noteContent">The note content to validate</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateNoteContent(string? noteContent)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(noteContent))
                {
                    return ValidationMessages.NoteContentRequired;
                }

                if (noteContent.Trim().Length < 1)
                {
                    return ValidationMessages.NoteContentEmpty;
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating note content: {ex.Message}", "ValidationService");
                return null;
            }
        }

        #endregion

        #region Optional Text Validation

        /// <summary>
        /// Validates optional text fields with a maximum length.
        /// Returns null when value is null/empty (treated as valid optional),
        /// or an Arabic error message when length exceeds the limit.
        /// </summary>
        /// <param name="value">The text to validate (optional)</param>
        /// <param name="maxLength">Maximum allowed length</param>
        /// <returns>Error message if invalid, null if valid</returns>
        public string? ValidateOptionalText(string? value, int maxLength)
        {
            try
            {
                if (string.IsNullOrEmpty(value))
                {
                    return null; // Optional and empty is valid
                }

                if (value.Length > maxLength)
                {
                    return ValidationMessages.FormatTextTooLong(maxLength);
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating optional text: {ex.Message}", "ValidationService");
                return null;
            }
        }

        #endregion

        #region Length Validation

        /// <summary>
        /// Validates that a text field does not exceed the maximum length.
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="maxLength">Maximum allowed length</param>
        /// <param name="fieldName">The name of the field for specific error messaging</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateMaxLength(string? value, int maxLength, string fieldName = "")
        {
            try
            {
                if (string.IsNullOrEmpty(value))
                    return null; // Empty values are handled by required validation

                if (value.Length > maxLength)
                {
                    return fieldName switch
                    {
                        "ActivityDescription" when maxLength == 500 => ValidationMessages.ActivityDescriptionTooLong,
                        "UpdateNote" when maxLength == 500 => ValidationMessages.NoteTooLong,
                        "NameFr" when maxLength == 100 => ValidationMessages.NameTooLong,
                        _ => ValidationMessages.FormatTextTooLong(maxLength)
                    };
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating max length for {fieldName}: {ex.Message}", "ValidationService");
                return null;
            }
        }

        /// <summary>
        /// Validates that a text field meets the minimum length requirement.
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="minLength">Minimum required length</param>
        /// <param name="fieldName">The name of the field for specific error messaging</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateMinLength(string? value, int minLength, string fieldName = "")
        {
            try
            {
                if (string.IsNullOrEmpty(value))
                    return null; // Empty values are handled by required validation

                var trimmedValue = value.Trim();
                if (trimmedValue.Length < minLength)
                {
                    return fieldName switch
                    {
                        "NameFr" when minLength == 2 => ValidationMessages.NameTooShort,
                        _ => ValidationMessages.FormatTextTooShort(minLength)
                    };
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating min length for {fieldName}: {ex.Message}", "ValidationService");
                return null;
            }
        }

        /// <summary>
        /// Validates that a text field is within the specified length range.
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="minLength">Minimum required length</param>
        /// <param name="maxLength">Maximum allowed length</param>
        /// <param name="fieldName">The name of the field for specific error messaging</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateLength(string? value, int minLength, int maxLength, string fieldName = "")
        {
            try
            {
                // Check minimum length first
                var minError = ValidateMinLength(value, minLength, fieldName);
                if (minError != null)
                    return minError;

                // Check maximum length
                var maxError = ValidateMaxLength(value, maxLength, fieldName);
                if (maxError != null)
                    return maxError;

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating length for {fieldName}: {ex.Message}", "ValidationService");
                return null;
            }
        }

        #endregion

        #region Format Validation

        /// <summary>
        /// Validates phone number format according to UFU2 business rules with caching.
        /// Checks for minimum 9 digits and maximum 15 digits.
        /// Results are cached for 1 hour to improve performance.
        /// </summary>
        /// <param name="phoneNumber">The phone number to validate</param>
        /// <param name="isRequired">Whether the phone number is required</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidatePhoneNumber(string? phoneNumber, bool isRequired = false)
        {
            // Create cache key
            string cacheKey = $"phone_{phoneNumber ?? "null"}_{isRequired}";

            // Check cache first
            if (_validationCache.TryGetValue(cacheKey, out string? cachedResult))
            {
                _cacheHits++;
                return cachedResult;
            }

            _cacheMisses++;

            try
            {
                string? result;

                if (string.IsNullOrWhiteSpace(phoneNumber))
                {
                    result = isRequired ? ValidationMessages.RequiredField : null;
                }
                else
                {
                    // Extract only digits from the phone number
                    var digitsOnly = PhoneDigitsRegex.Replace(phoneNumber, "");

                    if (digitsOnly.Length < 9)
                    {
                        result = ValidationMessages.InvalidPhoneNumber;
                    }
                    else if (digitsOnly.Length > 15)
                    {
                        result = ValidationMessages.PhoneNumberTooLong;
                    }
                    else
                    {
                        result = null; // Valid
                    }
                }

                // Cache the result for 1 hour
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1),
                    Size = 1,
                    Priority = CacheItemPriority.Normal
                };
                _validationCache.Set(cacheKey, result, cacheOptions);

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating phone number: {ex.Message}", "ValidationService");
                return ValidationMessages.PhoneValidationError;
            }
        }

        /// <summary>
        /// Validates date format according to UFU2 business rules with caching.
        /// Supports DD/MM/YYYY format and placeholder xx/xx/xxxx.
        /// Results are cached for 1 hour to improve performance.
        /// </summary>
        /// <param name="date">The date to validate</param>
        /// <param name="isRequired">Whether the date is required</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateDate(string? date, bool isRequired = true)
        {
            // Check if disposed
            if (_disposed)
            {
                LoggingService.LogWarning("ValidationService is disposed, using fallback validation", "ValidationService");
                return ValidateDateFallback(date, isRequired);
            }

            // Create cache key
            string cacheKey = $"date_{date ?? "null"}_{isRequired}";

            // Check cache first with try-catch for disposed cache
            try
            {
                if (_validationCache.TryGetValue(cacheKey, out string? cachedResult))
                {
                    _cacheHits++;
                    return cachedResult;
                }
            }
            catch (ObjectDisposedException)
            {
                LoggingService.LogWarning("ValidationService cache is disposed, using fallback validation", "ValidationService");
                return ValidateDateFallback(date, isRequired);
            }

            _cacheMisses++;

            try
            {
                string? result;

                if (string.IsNullOrWhiteSpace(date))
                {
                    result = isRequired ? ValidationMessages.UpdateDateRequired : null;
                }
                else
                {
                    var trimmedDate = date.Trim();

                    // Allow placeholder formats: xx/xx/xxxx and xx/xx/yyyy (with actual year)
                    if (trimmedDate == "xx/xx/xxxx")
                    {
                        result = null;
                    }
                    else
                    {
                        // Allow placeholder format with actual year (xx/xx/yyyy)
                        var placeholderYearMatch = System.Text.RegularExpressions.Regex.Match(trimmedDate, @"^xx/xx/(\d{4})$");
                        if (placeholderYearMatch.Success)
                        {
                            var year = placeholderYearMatch.Groups[1].Value;
                            if (int.TryParse(year, out int yearInt) && yearInt >= 1900 && yearInt <= 2100)
                            {
                                result = null; // Valid placeholder with year
                            }
                            else
                            {
                                result = ValidationMessages.InvalidDate;
                            }
                        }
                        else
                        {
                            // Validate DD/MM/YYYY format
                            var match = DateFormatRegex.Match(trimmedDate);
                            if (!match.Success)
                            {
                                result = ValidationMessages.InvalidDateFormat;
                            }
                            else
                            {
                                // Validate actual date if not placeholder
                                if (DateTime.TryParseExact(trimmedDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
                                {
                                    result = null; // Valid
                                }
                                else
                                {
                                    result = ValidationMessages.InvalidDate;
                                }
                            }
                        }
                    }
                }

                // Cache the result for 1 hour with try-catch for disposed cache
                try
                {
                    if (!_disposed)
                    {
                        var cacheOptions = new MemoryCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1),
                            Size = 1,
                            Priority = CacheItemPriority.Normal
                        };
                        _validationCache.Set(cacheKey, result, cacheOptions);
                    }
                }
                catch (ObjectDisposedException)
                {
                    // Cache is disposed, ignore caching
                    LoggingService.LogDebug("Cache is disposed, skipping cache set", "ValidationService");
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating date: {ex.Message}", "ValidationService");
                return ValidationMessages.ValidationError;
            }
        }

        /// <summary>
        /// Validates that a name contains only Latin characters.
        /// Used for NameFr field validation according to UFU2 business rules.
        /// </summary>
        /// <param name="name">The name to validate</param>
        /// <param name="isRequired">Whether the name is required</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateLatinName(string? name, bool isRequired = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                {
                    return isRequired ? ValidationMessages.NameFrRequired : null;
                }

                // Validate Latin characters only
                if (!TextBoxExtensions.IsValidLatinText(name))
                {
                    return ValidationMessages.NameMustBeLatinOnly;
                }

                // Validate length constraints
                var lengthError = ValidateLength(name, 2, 100, "NameFr");
                if (lengthError != null)
                    return lengthError;

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating Latin name: {ex.Message}", "ValidationService");
                return ValidationMessages.NameValidationError;
            }
        }

        #endregion

        #region Business Rule Validation

        /// <summary>
        /// Validates that a phone number is not a duplicate in the provided collection.
        /// </summary>
        /// <param name="phoneNumber">The phone number to validate</param>
        /// <param name="existingPhoneNumbers">Collection of existing phone numbers</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidatePhoneNumberDuplicate(string? phoneNumber, IEnumerable<string>? existingPhoneNumbers)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber) || existingPhoneNumbers == null)
                    return null;

                var normalizedPhone = PhoneDigitsRegex.Replace(phoneNumber, "");

                foreach (var existing in existingPhoneNumbers)
                {
                    if (string.IsNullOrWhiteSpace(existing))
                        continue;

                    var normalizedExisting = PhoneDigitsRegex.Replace(existing, "");
                    if (normalizedPhone.Equals(normalizedExisting, StringComparison.OrdinalIgnoreCase))
                    {
                        return ValidationMessages.DuplicatePhoneNumber;
                    }
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating phone number duplicate: {ex.Message}", "ValidationService");
                return null;
            }
        }

        /// <summary>
        /// Validates an optional note field with length constraints.
        /// Used for update notes and similar optional text fields.
        /// </summary>
        /// <param name="note">The note to validate</param>
        /// <param name="maxLength">Maximum allowed length (default 500)</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateOptionalNote(string? note, int maxLength = 500)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(note))
                    return null; // Optional field

                var trimmedNote = note.Trim();
                if (trimmedNote.Length > maxLength)
                {
                    return ValidationMessages.NoteTooLong;
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating optional note: {ex.Message}", "ValidationService");
                return null;
            }
        }

        #endregion

        #region Composite Validation Methods

        /// <summary>
        /// Validates activity description according to UFU2 business rules.
        /// Combines required field and length validation.
        /// </summary>
        /// <param name="description">The activity description to validate</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateActivityDescription(string? description)
        {
            try
            {
                // Check required
                var requiredError = ValidateRequired(description, "ActivityDescription");
                if (requiredError != null)
                    return requiredError;

                // Check length
                var lengthError = ValidateMaxLength(description, 500, "ActivityDescription");
                if (lengthError != null)
                    return lengthError;

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating activity description: {ex.Message}", "ValidationService");
                return null;
            }
        }

        /// <summary>
        /// Validates update date and note combination for activity status updates.
        /// </summary>
        /// <param name="updateDate">The update date to validate</param>
        /// <param name="updateNote">The update note to validate</param>
        /// <returns>Dictionary of property names to error messages</returns>
        public Dictionary<string, string> ValidateActivityUpdate(string? updateDate, string? updateNote)
        {
            var errors = new Dictionary<string, string>();

            try
            {
                // Validate date
                var dateError = ValidateDate(updateDate, true);
                if (dateError != null)
                {
                    errors["UpdateDate"] = dateError;
                }

                // Validate note (optional)
                var noteError = ValidateOptionalNote(updateNote, 500);
                if (noteError != null)
                {
                    errors["UpdateNote"] = noteError;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating activity update: {ex.Message}", "ValidationService");
                errors["Validation"] = ValidationMessages.ValidationError;
            }

            return errors;
        }

        /// <summary>
        /// Validates a complete phone number entry including format and duplicate checking.
        /// </summary>
        /// <param name="phoneNumber">The phone number to validate</param>
        /// <param name="existingPhoneNumbers">Collection of existing phone numbers for duplicate checking</param>
        /// <param name="isRequired">Whether the phone number is required</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        public string? ValidateCompletePhoneNumber(string? phoneNumber, IEnumerable<string>? existingPhoneNumbers, bool isRequired = false)
        {
            try
            {
                // Check format first
                var formatError = ValidatePhoneNumber(phoneNumber, isRequired);
                if (formatError != null)
                    return formatError;

                // Check for duplicates if format is valid
                if (!string.IsNullOrWhiteSpace(phoneNumber))
                {
                    var duplicateError = ValidatePhoneNumberDuplicate(phoneNumber, existingPhoneNumbers);
                    if (duplicateError != null)
                        return duplicateError;
                }

                return null; // Valid
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating complete phone number: {ex.Message}", "ValidationService");
                return ValidationMessages.PhoneValidationError;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets a generic validation error message for system errors.
        /// </summary>
        /// <returns>Generic Arabic validation error message</returns>
        public string GetGenericValidationError()
        {
            return ValidationMessages.ValidationError;
        }

        /// <summary>
        /// Checks if a validation result indicates a valid state.
        /// </summary>
        /// <param name="validationResult">The validation result to check</param>
        /// <returns>True if valid (null or empty), false otherwise</returns>
        public bool IsValid(string? validationResult)
        {
            return string.IsNullOrEmpty(validationResult);
        }

        /// <summary>
        /// Combines multiple validation results into a single error message.
        /// </summary>
        /// <param name="validationResults">Array of validation results</param>
        /// <returns>Combined error message or null if all valid</returns>
        public string? CombineValidationResults(params string?[] validationResults)
        {
            try
            {
                var errors = validationResults.Where(r => !string.IsNullOrEmpty(r)).ToArray();
                return errors.Length > 0 ? string.Join(Environment.NewLine, errors) : null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error combining validation results: {ex.Message}", "ValidationService");
                return ValidationMessages.ValidationError;
            }
        }

        #endregion

        #region Cache Management

        /// <summary>
        /// Clears the validation result cache.
        /// Useful when validation rules change or cache needs to be invalidated.
        /// </summary>
        public void ClearCache()
        {
            try
            {
                _validationCache?.Dispose();

                // Recreate the cache
                var newCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 500,
                    CompactionPercentage = 0.25
                });

                // Replace the old cache (this is not thread-safe, but ValidationService is typically used in single-threaded scenarios)
                // For thread safety in multi-threaded scenarios, consider using a lock or concurrent cache implementation

                // Reset cache statistics
                _cacheHits = 0;
                _cacheMisses = 0;

                LoggingService.LogDebug("Validation cache cleared and recreated", "ValidationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing validation cache: {ex.Message}", "ValidationService");
            }
        }

        /// <summary>
        /// Gets cache statistics for monitoring and debugging.
        /// </summary>
        /// <returns>Dictionary containing cache hit/miss statistics</returns>
        public Dictionary<string, object> GetCacheStatistics()
        {
            return new Dictionary<string, object>
            {
                ["CacheHits"] = _cacheHits,
                ["CacheMisses"] = _cacheMisses,
                ["CacheHitRatio"] = _cacheHits + _cacheMisses > 0 ?
                    (double)_cacheHits / (_cacheHits + _cacheMisses) : 0.0,
                ["TotalValidations"] = _cacheHits + _cacheMisses
            };
        }

        /// <summary>
        /// Warms up the cache by preloading frequently used validation patterns.
        /// This method should be called during application startup for optimal performance.
        /// </summary>
        public async Task WarmupCacheAsync()
        {
            try
            {
                LoggingService.LogDebug("Starting cache warmup for ValidationService", "ValidationService");

                // Preload common validation patterns
                var commonValidations = new[]
                {
                    ("phone", "0123456789"),
                    ("email", "<EMAIL>"),
                    ("date", "01/01/2024"),
                    ("name", "محمد أحمد"),
                    ("address", "شارع الملك فهد، الرياض")
                };

                foreach (var (type, value) in commonValidations)
                {
                    try
                    {
                        switch (type)
                        {
                            case "phone":
                                ValidatePhoneNumber(value);
                                break;
                            case "email":
                                ValidateEmailFormat(value);
                                break;
                            case "date":
                                ValidateDate(value);
                                break;
                            case "name":
                                ValidateNameFormat(value);
                                break;
                            case "address":
                                ValidateAddressFormat(value);
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogWarning($"Failed to warmup cache for validation type '{type}': {ex.Message}", "ValidationService");
                    }
                }

                LoggingService.LogInfo($"Cache warmup completed. Cache hits: {_cacheHits}, Cache misses: {_cacheMisses}", "ValidationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache warmup: {ex.Message}", "ValidationService");
            }

            await Task.CompletedTask; // Make method async for interface compliance
        }

        /// <summary>
        /// Invalidates specific cache entries based on data change events.
        /// </summary>
        /// <param name="invalidationContext">Context information about what data changed</param>
        public void InvalidateCache(CacheInvalidationContext invalidationContext)
        {
            try
            {
                if (invalidationContext == null)
                    return;

                LoggingService.LogDebug($"Invalidating ValidationService cache for {invalidationContext.DataType} ({invalidationContext.InvalidationType})", "ValidationService");

                switch (invalidationContext.InvalidationType)
                {
                    case CacheInvalidationType.Full:
                        // Clear all caches
                        ClearCache();
                        break;

                    case CacheInvalidationType.Create:
                    case CacheInvalidationType.Update:
                    case CacheInvalidationType.Delete:
                        // For validation rule changes, clear relevant cache entries
                        if (invalidationContext.DataType == "ValidationRule")
                        {
                            // Clear validation cache since rules may have changed
                            ClearCache();
                        }
                        else if (invalidationContext.DataType == "ActivityType" || invalidationContext.DataType == "FileCheckRule")
                        {
                            // These changes might affect validation logic, so clear cache
                            ClearCache();
                        }
                        break;
                }

                LoggingService.LogDebug($"Cache invalidation completed for ValidationService", "ValidationService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache invalidation: {ex.Message}", "ValidationService");
            }
        }

        /// <summary>
        /// Gets cache health information for monitoring.
        /// </summary>
        /// <returns>Cache health metrics</returns>
        public CacheHealthInfo GetCacheHealth()
        {
            try
            {
                var stats = GetCacheStatistics();

                var hitRatio = (double)stats["CacheHitRatio"];
                var totalValidations = (int)stats["TotalValidations"];

                // Estimate memory usage (rough calculation based on cache size)
                var estimatedMemoryUsage = totalValidations * 256; // 256 bytes per validation result estimate

                var isHealthy = hitRatio >= 0.5 && totalValidations > 0; // At least 50% hit ratio and some validations

                return new CacheHealthInfo
                {
                    HitRatio = hitRatio,
                    ItemCount = totalValidations,
                    MemoryUsageBytes = estimatedMemoryUsage,
                    IsHealthy = isHealthy,
                    HealthStatus = isHealthy ? "Healthy" : $"Low hit ratio: {hitRatio:P1}",
                    LastWarmupTime = null // Will be set by coordinator
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting cache health: {ex.Message}", "ValidationService");
                return new CacheHealthInfo
                {
                    HitRatio = 0.0,
                    ItemCount = 0,
                    MemoryUsageBytes = 0,
                    IsHealthy = false,
                    HealthStatus = $"Error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Validates email format.
        /// </summary>
        /// <param name="email">The email to validate</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        private string? ValidateEmailFormat(string? email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return null;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email ? null : "تنسيق البريد الإلكتروني غير صحيح";
            }
            catch
            {
                return "تنسيق البريد الإلكتروني غير صحيح";
            }
        }

        /// <summary>
        /// Validates name format.
        /// </summary>
        /// <param name="name">The name to validate</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        private string? ValidateNameFormat(string? name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return null;

            // Basic name validation - allow Arabic and Latin characters
            if (name.Trim().Length < 2)
                return "الاسم قصير جداً";

            if (name.Trim().Length > 100)
                return "الاسم طويل جداً";

            return null;
        }

        /// <summary>
        /// Validates address format.
        /// </summary>
        /// <param name="address">The address to validate</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        private string? ValidateAddressFormat(string? address)
        {
            if (string.IsNullOrWhiteSpace(address))
                return null;

            // Basic address validation
            if (address.Trim().Length < 5)
                return "العنوان قصير جداً";

            if (address.Trim().Length > 500)
                return "العنوان طويل جداً";

            return null;
        }

        #endregion

        #region Fallback Validation Methods

        /// <summary>
        /// Fallback date validation when the main validation service is disposed.
        /// Provides basic date format validation without caching.
        /// </summary>
        /// <param name="date">The date to validate</param>
        /// <param name="isRequired">Whether the date is required</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        private string? ValidateDateFallback(string? date, bool isRequired)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(date))
                {
                    return isRequired ? "تاريخ التحديث مطلوب" : null;
                }

                var trimmedDate = date.Trim();

                // Allow placeholder formats
                if (trimmedDate == "xx/xx/xxxx")
                {
                    return null;
                }

                // Basic DD/MM/YYYY format check
                if (trimmedDate.Length == 10 && trimmedDate[2] == '/' && trimmedDate[5] == '/')
                {
                    // Try to parse as actual date
                    if (DateTime.TryParseExact(trimmedDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
                    {
                        return null; // Valid
                    }
                }

                return "تنسيق التاريخ غير صحيح";
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in fallback date validation: {ex.Message}", "ValidationService");
                return "خطأ في التحقق من التاريخ";
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the service and its resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        var stats = GetCacheStatistics();
                        LoggingService.LogInfo($"ValidationService disposed. Final cache stats - Hit ratio: {stats["CacheHitRatio"]:P1}, Total validations: {stats["TotalValidations"]}", "ValidationService");

                        _validationCache?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error disposing ValidationService: {ex.Message}", "ValidationService");
                    }
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
