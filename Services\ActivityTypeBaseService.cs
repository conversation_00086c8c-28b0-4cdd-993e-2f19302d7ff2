using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Caching.Memory;
using Dapper;
using Newtonsoft.Json;
using UFU2.Common;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.Services.Interfaces;
using UFU2.Services.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Service class for managing ActivityTypeBase table operations.
    /// Handles table creation, data import from JSON, and CRUD operations.
    /// Includes comprehensive caching for improved performance with cache hit/miss tracking.
    /// </summary>
    public class ActivityTypeBaseService : ICacheableService, IDisposable
    {
        private readonly DatabaseService _databaseService;
        private IMemoryCache _searchCache;
        private IMemoryCache _dataCache;

        // Cache statistics for monitoring
        private int _searchCacheHits = 0;
        private int _searchCacheMisses = 0;
        private int _dataCacheHits = 0;
        private int _dataCacheMisses = 0;

        /// <summary>
        /// Gets the service name for cache management identification.
        /// </summary>
        public string ServiceName => "ActivityTypeBaseService";

        /// <summary>
        /// SQL statement for inserting activity type data.
        /// Note: Table creation is now handled by DatabaseMigrationService using APP_Schema.sql
        /// </summary>
        private const string InsertSql = @"
            INSERT OR REPLACE INTO ActivityTypeBase (Code, Description)
            VALUES (@Code, @Description)";

        /// <summary>
        /// SQL statement for selecting all activity types.
        /// </summary>
        private const string SelectAllSql = @"
            SELECT Code, Description 
            FROM ActivityTypeBase 
            ORDER BY Code";

        /// <summary>
        /// SQL statement for selecting activity type by code.
        /// </summary>
        private const string SelectByCodeSql = @"
            SELECT Code, Description 
            FROM ActivityTypeBase 
            WHERE Code = @Code";

        /// <summary>
        /// SQL statement for counting total records.
        /// </summary>
        private const string CountSql = @"
            SELECT COUNT(*) FROM ActivityTypeBase";

        /// <summary>
        /// SQL statement for searching activity types by description.
        /// </summary>
        private const string SearchByDescriptionSql = @"
            SELECT Code, Description
            FROM ActivityTypeBase
            WHERE Description LIKE @SearchPattern
            ORDER BY Code
            LIMIT @Limit";

        /// <summary>
        /// Initializes a new instance of the ActivityTypeBaseService class.
        /// </summary>
        /// <param name="databaseService">Database service instance (optional - will use reference database from ServiceLocator if null)</param>
        public ActivityTypeBaseService(DatabaseService databaseService = null)
        {
            // Use reference database from ServiceLocator if no specific database service provided
            _databaseService = databaseService ?? ServiceLocator.GetService<DatabaseService>("ReferenceDatabase");

            if (_databaseService == null)
            {
                throw new InvalidOperationException("Reference database service not found. Ensure ServiceLocator is properly initialized with reference database.");
            }

            // Initialize search cache with 5-minute expiration and 100 item limit
            _searchCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 100,
                CompactionPercentage = 0.25
            });

            // Initialize data cache for GetAllAsync results with 30-minute expiration and 50 item limit
            _dataCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 50,
                CompactionPercentage = 0.25
            });

            LoggingService.LogDebug("ActivityTypeBaseService initialized with reference database", "ActivityTypeBaseService");
        }

        /// <summary>
        /// Creates the ActivityTypeBase table if it doesn't exist.
        /// Note: Table creation is now handled by DatabaseMigrationService using APP_Schema.sql.
        /// This method is kept for backward compatibility and now only validates table existence.
        /// </summary>
        public async Task CreateTableAsync()
        {
            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Validate that the table exists (created by migration service)
                const string checkTableSql = @"
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type='table' AND name='ActivityTypeBase'";

                int tableExists = await connection.ExecuteScalarAsync<int>(checkTableSql);

                if (tableExists == 0)
                {
                    LoggingService.LogWarning("ActivityTypeBase table does not exist. It should have been created by DatabaseMigrationService.", "ActivityTypeBaseService");
                    throw new InvalidOperationException("ActivityTypeBase table not found. Database schema may not be properly initialized.");
                }

                LoggingService.LogDebug("ActivityTypeBase table validation completed successfully", "ActivityTypeBaseService");
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في التحقق من جدول أنواع الأنشطة", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "ActivityTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Seeds activity type data from embedded JSON resource.
        /// </summary>
        /// <param name="progress">Progress reporter for import operation</param>
        /// <returns>Number of records imported</returns>
        public async Task<int> SeedActivityTypeData(IProgress<double> progress = null)
        {
            try
            {
                LoggingService.LogDebug("Starting import from embedded JSON resource", "ActivityTypeBaseService");

                // Read JSON content from embedded resource
                string jsonContent = ReadEmbeddedJsonResource("UFU2.Database.activity_Type.json");

                if (string.IsNullOrEmpty(jsonContent))
                {
                    LoggingService.LogWarning("Embedded JSON resource is empty or not found", "ActivityTypeBaseService");
                    return 0;
                }

                var importedCount = await ImportFromJsonContent(jsonContent, progress);
                if (importedCount > 0)
                {
                    ErrorManager.ShowUserSuccessToast($"تم استيراد {importedCount} نوع نشاط بنجاح", "تم الاستيراد", "ActivityTypeBaseService");
                }
                return importedCount;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error seeding activity type data: {ex.Message}", "ActivityTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Reads embedded JSON resource from assembly.
        /// </summary>
        /// <param name="resourceName">Full name of the embedded resource</param>
        /// <returns>JSON content as string, or null if not found</returns>
        private string ReadEmbeddedJsonResource(string resourceName)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                using var stream = assembly.GetManifestResourceStream(resourceName);
                
                if (stream == null)
                {
                    LoggingService.LogWarning($"Embedded resource not found: {resourceName}", "ActivityTypeBaseService");
                    return null;
                }

                using var reader = new StreamReader(stream);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error reading embedded resource {resourceName}: {ex.Message}", "ActivityTypeBaseService");
                return null;
            }
        }

        /// <summary>
        /// Imports activity type data from JSON file.
        /// </summary>
        /// <param name="jsonFilePath">Path to the JSON file</param>
        /// <param name="progress">Progress reporter for import operation</param>
        /// <returns>Number of records imported</returns>
        [Obsolete("Use SeedActivityTypeData() method instead for embedded resource approach")]
        public async Task<int> ImportFromJsonAsync(string jsonFilePath, IProgress<double> progress = null)
        {
            if (!File.Exists(jsonFilePath))
            {
                throw new FileNotFoundException($"JSON file not found: {jsonFilePath}");
            }

            try
            {
                string jsonContent = await File.ReadAllTextAsync(jsonFilePath);
                return await ImportFromJsonContent(jsonContent, progress);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error importing from JSON file {jsonFilePath}: {ex.Message}", "ActivityTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Imports activity type data from JSON content.
        /// </summary>
        /// <param name="jsonContent">JSON content string</param>
        /// <param name="progress">Progress reporter for import operation</param>
        /// <returns>Number of records imported</returns>
        private async Task<int> ImportFromJsonContent(string jsonContent, IProgress<double> progress = null)
        {
            if (string.IsNullOrEmpty(jsonContent))
            {
                throw new ArgumentException("JSON content cannot be null or empty", nameof(jsonContent));
            }

            try
            {
                // Parse JSON content
                var activityTypes = JsonConvert.DeserializeObject<List<ActivityTypeBaseModel>>(jsonContent);
                
                if (activityTypes == null || activityTypes.Count == 0)
                {
                    LoggingService.LogWarning("No activity types found in JSON content", "ActivityTypeBaseService");
                    return 0;
                }

                LoggingService.LogInfo($"Parsed {activityTypes.Count} activity types from JSON", "ActivityTypeBaseService");

                // Import in batches for better performance
                const int batchSize = 100;
                int totalImported = 0;
                int totalBatches = (int)Math.Ceiling((double)activityTypes.Count / batchSize);

                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
                {
                    var batch = activityTypes.Skip(batchIndex * batchSize).Take(batchSize).ToList();
                    
                    using var transaction = connection.BeginTransaction();
                    try
                    {
                        foreach (var activityType in batch)
                        {
                            if (activityType.IsValid())
                            {
                                await connection.ExecuteAsync(InsertSql, activityType, transaction);
                                totalImported++;
                            }
                            else
                            {
                                LoggingService.LogWarning($"Skipping invalid activity type: {activityType?.Code}", "ActivityTypeBaseService");
                            }
                        }

                        transaction.Commit();
                        
                        // Report progress
                        if (progress != null)
                        {
                            double progressPercentage = (double)(batchIndex + 1) / totalBatches * 100;
                            progress.Report(progressPercentage);
                        }

                        LoggingService.LogDebug($"Imported batch {batchIndex + 1}/{totalBatches} ({batch.Count} records)", "ActivityTypeBaseService");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        ErrorManager.HandleErrorToast(ex, "فشل في استيراد دفعة من البيانات", "خطأ في الاستيراد",
                                               LogLevel.Error, "ActivityTypeBaseService");
                        throw;
                    }
                }

                LoggingService.LogInfo($"Successfully imported {totalImported} activity types", "ActivityTypeBaseService");
                return totalImported;
            }
            catch (JsonException ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في استيراد بيانات أنواع الأنشطة", "خطأ في الاستيراد",
                                       LogLevel.Error, "ActivityTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Gets all activity types from the database with caching for improved performance.
        /// Uses connection pooling and caches results for 30 minutes to reduce database load.
        /// </summary>
        /// <returns>List of all activity types</returns>
        public async Task<List<ActivityTypeBaseModel>> GetAllAsync()
        {
            const string cacheKey = "activities_all";

            // Check cache first
            if (_dataCache.TryGetValue(cacheKey, out List<ActivityTypeBaseModel> cachedResult))
            {
                _dataCacheHits++;
                LoggingService.LogDebug($"Returning cached activity types (Cache hits: {_dataCacheHits}, misses: {_dataCacheMisses})", "ActivityTypeBaseService");
                return cachedResult;
            }

            _dataCacheMisses++;

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                var result = await connection.QueryAsync<ActivityTypeBaseModel>(SelectAllSql).ConfigureAwait(false);
                var resultList = result.ToList();

                // Cache the results for 30 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                    Size = 1,
                    Priority = CacheItemPriority.High // High priority for frequently accessed data
                };
                _dataCache.Set(cacheKey, resultList, cacheOptions);

                LoggingService.LogDebug($"Cached {resultList.Count} activity types (Cache hits: {_dataCacheHits}, misses: {_dataCacheMisses})", "ActivityTypeBaseService");
                return resultList;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في جلب بيانات أنواع الأنشطة", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "ActivityTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Gets an activity type by its code.
        /// </summary>
        /// <param name="code">The activity type code</param>
        /// <returns>Activity type if found, null otherwise</returns>
        public async Task<ActivityTypeBaseModel> GetByCodeAsync(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
            {
                throw new ArgumentException("Code cannot be null or empty", nameof(code));
            }

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                var result = await connection.QueryFirstOrDefaultAsync<ActivityTypeBaseModel>(
                    SelectByCodeSql, new { Code = code });

                return result;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في جلب نوع النشاط", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "ActivityTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Searches activity types by description with database-level filtering and caching.
        /// Provides significant performance improvement over loading all activities into memory.
        /// </summary>
        /// <param name="searchTerm">The search term to look for in descriptions</param>
        /// <param name="limit">Maximum number of results to return (default: 10)</param>
        /// <returns>List of matching activity types</returns>
        public async Task<List<ActivityTypeBaseModel>> SearchByDescriptionAsync(string searchTerm, int limit = 10)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
            {
                return new List<ActivityTypeBaseModel>();
            }

            // Normalize search term for better matching
            string normalizedSearchTerm = TextNormalizationHelper.NormalizeForSearch(searchTerm);

            // Create cache key with normalized term
            string cacheKey = $"search_{normalizedSearchTerm.ToLowerInvariant()}_{limit}";

            // Check cache first
            if (_searchCache.TryGetValue(cacheKey, out List<ActivityTypeBaseModel> cachedResult))
            {
                _searchCacheHits++;
                LoggingService.LogDebug($"Returning cached search results for: {searchTerm} (Cache hits: {_searchCacheHits}, misses: {_searchCacheMisses})", "ActivityTypeBaseService");
                return cachedResult;
            }

            _searchCacheMisses++;

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Use normalized search term for better matching
                string searchPattern = $"%{normalizedSearchTerm}%";
                var result = await connection.QueryAsync<ActivityTypeBaseModel>(
                    SearchByDescriptionSql,
                    new { SearchPattern = searchPattern, Limit = limit });

                var resultList = result.ToList();

                // Cache the results for 5 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                    Size = 1
                };
                _searchCache.Set(cacheKey, resultList, cacheOptions);

                LoggingService.LogDebug($"Search for '{searchTerm}' returned {resultList.Count} results (Cache hits: {_searchCacheHits}, misses: {_searchCacheMisses})", "ActivityTypeBaseService");
                return resultList;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في البحث عن أنواع الأنشطة", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "ActivityTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Enhanced search with fuzzy matching and similarity scoring for improved accuracy.
        /// Handles whitespace variations, Arabic character normalization, and typos.
        /// </summary>
        /// <param name="searchTerm">The search term to look for</param>
        /// <param name="limit">Maximum number of results to return (default: 10)</param>
        /// <param name="minSimilarity">Minimum similarity score (0.0 to 1.0, default: 0.3)</param>
        /// <returns>List of matching activity types with similarity scores</returns>
        public async Task<List<ActivityTypeBaseModel>> SearchByDescriptionEnhancedAsync(string searchTerm, int limit = 10, double minSimilarity = 0.3)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
            {
                return new List<ActivityTypeBaseModel>();
            }

            try
            {
                // Get all activity types for word frequency-based search
                var allActivities = await GetAllAsync().ConfigureAwait(false);

                // Use WordFrequencySearchService for enhanced Arabic text analysis and word frequency ranking
                var wordFrequencySearchService = ServiceLocator.GetService<WordFrequencySearchService>();
                if (wordFrequencySearchService != null)
                {
                    var searchRequest = new Models.SearchRequest
                    {
                        SearchTerm = searchTerm,
                        MaxResults = limit,
                        MinSimilarity = 0.7, // Higher threshold for exact prefix matching
                        UseArabicAnalysis = true,
                        IncludePartialMatches = false, // Only exact prefix matches
                        FilterStopWords = false // Keep all words for exact prefix matching
                    };

                    var searchResults = await wordFrequencySearchService.SearchAsync(
                        allActivities,
                        searchRequest,
                        activity => activity.Description
                    ).ConfigureAwait(false);

                    // Extract the activity models from enhanced search results
                    var results = searchResults.Results.Select(sr => sr.Item).ToList();

                    LoggingService.LogDebug($"Exact prefix activity search completed for '{searchTerm}': {results.Count} results with Arabic analysis", "ActivityTypeBaseService");

                    return results;
                }
                else
                {
                    // Fallback to EnhancedSearchService if WordFrequencySearchService is not available
                    var enhancedSearchService = new EnhancedSearchService();
                    var searchResults = await enhancedSearchService.SearchAsync(
                        allActivities,
                        searchTerm,
                        activity => activity.Description,
                        limit,
                        minSimilarity
                    ).ConfigureAwait(false);

                    var results = searchResults.Select(sr => sr.Item).ToList();
                    LoggingService.LogDebug($"Fallback activity search completed for '{searchTerm}': {results.Count} results", "ActivityTypeBaseService");

                    return results;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in enhanced search for '{searchTerm}': {ex.Message}", "ActivityTypeBaseService");
                // Fallback to regular search
                return await SearchByDescriptionAsync(searchTerm, limit).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Gets the total count of activity types in the database.
        /// </summary>
        /// <returns>Total count of records</returns>
        public async Task<int> GetCountAsync()
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var count = await connection.ExecuteScalarAsync<int>(CountSql);
                return count;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في عد أنواع الأنشطة", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "ActivityTypeBaseService");
                throw;
            }
        }

        /// <summary>
        /// Checks if the ActivityTypeBase table has data.
        /// </summary>
        /// <returns>True if table has data, false otherwise</returns>
        public async Task<bool> HasDataAsync()
        {
            var count = await GetCountAsync();
            return count > 0;
        }

        /// <summary>
        /// Inserts a single activity type into the database.
        /// </summary>
        /// <param name="activityType">The activity type to insert</param>
        /// <returns>True if insertion was successful, false otherwise</returns>
        public async Task<bool> InsertAsync(ActivityTypeBaseModel activityType)
        {
            if (activityType == null)
            {
                throw new ArgumentNullException(nameof(activityType));
            }

            if (!activityType.IsValid())
            {
                LoggingService.LogWarning($"Invalid activity type data: {activityType.Code}", "ActivityTypeBaseService");
                return false;
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var rowsAffected = await connection.ExecuteAsync(InsertSql, activityType);

                if (rowsAffected > 0)
                {
                    LoggingService.LogDebug($"Successfully inserted activity type: {activityType.Code} - {activityType.Description}", "ActivityTypeBaseService");
                    ErrorManager.ShowUserSuccessToast($"تم إضافة نوع النشاط بنجاح\nالرمز: {activityType.Code}", "تم الإضافة", "ActivityTypeBaseService");
                    return true;
                }
                else
                {
                    LoggingService.LogWarning($"No rows affected when inserting activity type: {activityType.Code}", "ActivityTypeBaseService");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في إضافة نوع النشاط", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "ActivityTypeBaseService");
                return false;
            }
        }

        /// <summary>
        /// Clears all caches (search and data).
        /// Useful when activity data is modified and caches need to be invalidated.
        /// </summary>
        public void ClearCache()
        {
            try
            {
                // Clear search cache
                _searchCache?.Dispose();
                _searchCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 100,
                    CompactionPercentage = 0.25
                });

                // Clear data cache
                _dataCache?.Dispose();
                _dataCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 50,
                    CompactionPercentage = 0.25
                });

                // Reset cache statistics
                _searchCacheHits = 0;
                _searchCacheMisses = 0;
                _dataCacheHits = 0;
                _dataCacheMisses = 0;

                LoggingService.LogDebug("All caches cleared and recreated", "ActivityTypeBaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing caches: {ex.Message}", "ActivityTypeBaseService");
            }
        }

        /// <summary>
        /// Clears the search result cache.
        /// Useful when activity data is modified and cache needs to be invalidated.
        /// </summary>
        [Obsolete("Use ClearCache() instead for comprehensive cache management")]
        public void ClearSearchCache()
        {
            ClearCache();
        }

        /// <summary>
        /// Clears all caches (search and data) - alias for interface compatibility.
        /// Useful when activity data is modified and caches need to be invalidated.
        /// </summary>
        public void ClearAllCaches()
        {
            ClearCache();
        }

        /// <summary>
        /// Warms up the cache by preloading frequently accessed activity types.
        /// This method should be called during application startup for optimal performance.
        /// </summary>
        public async Task WarmupCacheAsync()
        {
            try
            {
                LoggingService.LogDebug("Starting cache warmup for ActivityTypeBaseService", "ActivityTypeBaseService");

                // Preload all activity types
                await GetAllAsync().ConfigureAwait(false);

                // Preload common search terms (these are typical search patterns in UFU2)
                var commonSearchTerms = new[] { "تجاري", "حرفي", "مهني", "Commercial", "Craft", "Professional" };

                foreach (var searchTerm in commonSearchTerms)
                {
                    try
                    {
                        await SearchByDescriptionAsync(searchTerm, 10).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogWarning($"Failed to warmup cache for search term '{searchTerm}': {ex.Message}", "ActivityTypeBaseService");
                    }
                }

                LoggingService.LogInfo($"Cache warmup completed. Search cache hits: {_searchCacheHits}, Data cache hits: {_dataCacheHits}", "ActivityTypeBaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache warmup: {ex.Message}", "ActivityTypeBaseService");
            }
        }

        /// <summary>
        /// Invalidates specific cache entries based on data change events.
        /// </summary>
        /// <param name="invalidationContext">Context information about what data changed</param>
        public void InvalidateCache(CacheInvalidationContext invalidationContext)
        {
            try
            {
                if (invalidationContext == null)
                    return;

                LoggingService.LogDebug($"Invalidating ActivityTypeBaseService cache for {invalidationContext.DataType} ({invalidationContext.InvalidationType})", "ActivityTypeBaseService");

                switch (invalidationContext.InvalidationType)
                {
                    case CacheInvalidationType.Full:
                        // Clear all caches
                        ClearCache();
                        break;

                    case CacheInvalidationType.Create:
                    case CacheInvalidationType.Update:
                    case CacheInvalidationType.Delete:
                        // For activity type changes, clear the data cache but keep search cache
                        // unless the change affects searchable content
                        if (invalidationContext.DataType == "ActivityType")
                        {
                            // Clear data cache (GetAllAsync results)
                            _dataCache?.Remove("activities_all");
                            _dataCacheMisses++; // Track that we'll have a cache miss

                            // If the change affects description (searchable content), clear search cache too
                            if (invalidationContext.AdditionalContext.ContainsKey("DescriptionChanged") &&
                                (bool)invalidationContext.AdditionalContext["DescriptionChanged"])
                            {
                                // Clear all search cache entries
                                _searchCache?.Dispose();
                                _searchCache = new MemoryCache(new MemoryCacheOptions
                                {
                                    SizeLimit = 100,
                                    CompactionPercentage = 0.25
                                });
                            }
                        }
                        break;
                }

                LoggingService.LogDebug($"Cache invalidation completed for ActivityTypeBaseService", "ActivityTypeBaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache invalidation: {ex.Message}", "ActivityTypeBaseService");
            }
        }

        /// <summary>
        /// Gets cache health information for monitoring.
        /// </summary>
        /// <returns>Cache health metrics</returns>
        public CacheHealthInfo GetCacheHealth()
        {
            try
            {
                var stats = GetCacheStatistics();

                var searchHitRatio = (double)stats["SearchCacheHitRatio"];
                var dataHitRatio = (double)stats["DataCacheHitRatio"];
                var overallHitRatio = (searchHitRatio + dataHitRatio) / 2.0;

                var searchItems = (int)stats["SearchCacheItems"];
                var dataItems = (int)stats["DataCacheItems"];
                var totalItems = searchItems + dataItems;

                // Estimate memory usage (rough calculation)
                var estimatedMemoryUsage = (searchItems * 1024) + (dataItems * 10240); // 1KB per search item, 10KB per data item

                var isHealthy = overallHitRatio >= 0.6 && totalItems > 0; // At least 60% hit ratio and some cached items

                return new CacheHealthInfo
                {
                    HitRatio = overallHitRatio,
                    ItemCount = totalItems,
                    MemoryUsageBytes = estimatedMemoryUsage,
                    IsHealthy = isHealthy,
                    HealthStatus = isHealthy ? "Healthy" : $"Low hit ratio: {overallHitRatio:P1}",
                    LastWarmupTime = null // Will be set by coordinator
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting cache health: {ex.Message}", "ActivityTypeBaseService");
                return new CacheHealthInfo
                {
                    HitRatio = 0.0,
                    ItemCount = 0,
                    MemoryUsageBytes = 0,
                    IsHealthy = false,
                    HealthStatus = $"Error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Gets cache statistics for monitoring and debugging.
        /// </summary>
        /// <returns>Dictionary containing cache hit/miss statistics</returns>
        public Dictionary<string, object> GetCacheStatistics()
        {
            return new Dictionary<string, object>
            {
                ["SearchCacheHits"] = _searchCacheHits,
                ["SearchCacheMisses"] = _searchCacheMisses,
                ["SearchCacheHitRatio"] = _searchCacheHits + _searchCacheMisses > 0 ?
                    (double)_searchCacheHits / (_searchCacheHits + _searchCacheMisses) : 0.0,
                ["DataCacheHits"] = _dataCacheHits,
                ["DataCacheMisses"] = _dataCacheMisses,
                ["DataCacheHitRatio"] = _dataCacheHits + _dataCacheMisses > 0 ?
                    (double)_dataCacheHits / (_dataCacheHits + _dataCacheMisses) : 0.0
            };
        }

        /// <summary>
        /// Disposes of the service and its resources.
        /// </summary>
        public void Dispose()
        {
            try
            {
                _searchCache?.Dispose();
                _dataCache?.Dispose();

                var stats = GetCacheStatistics();
                LoggingService.LogInfo($"ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: {stats["SearchCacheHitRatio"]:P1}, Data hit ratio: {stats["DataCacheHitRatio"]:P1}", "ActivityTypeBaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disposing ActivityTypeBaseService: {ex.Message}", "ActivityTypeBaseService");
            }
        }
    }
}
