using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using UFU2.Common;

namespace UFU2.Models
{
    /// <summary>
    /// Model class for managing a collection of phone numbers for client registration.
    /// Implements INotifyPropertyChanged for proper WPF data binding support.
    /// Provides methods for adding, removing, and managing multiple phone numbers.
    /// 
    /// This model follows UFU2 architectural patterns with:
    /// - Property change notification for real-time UI updates
    /// - ObservableCollection for automatic UI synchronization
    /// - Primary phone number management for main view integration
    /// - Arabic RTL layout compatibility
    /// - MaterialDesign ListView integration
    /// </summary>
    public class PhoneNumbersCollectionModel : INotifyPropertyChanged
    {
        #region Private Fields

        private UFU2BulkObservableCollection<PhoneNumberModel> _phoneNumbers;
        private PhoneNumberModel? _selectedPhoneNumber;

        /// <summary>
        /// Flag to prevent circular PropertyChanged events during synchronization operations.
        /// When true, PropertyChanged events for PrimaryPhoneNumber are suppressed.
        /// </summary>
        private bool _suppressPropertyChanged = false;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the collection of phone numbers.
        /// This collection is bound to the ListView in the dialog.
        /// </summary>
        public UFU2BulkObservableCollection<PhoneNumberModel> PhoneNumbers
        {
            get => _phoneNumbers;
            private set => SetProperty(ref _phoneNumbers, value);
        }

        /// <summary>
        /// Gets or sets the currently selected phone number in the collection.
        /// Used for editing and removing phone numbers.
        /// </summary>
        public PhoneNumberModel? SelectedPhoneNumber
        {
            get => _selectedPhoneNumber;
            set => SetProperty(ref _selectedPhoneNumber, value);
        }

        /// <summary>
        /// Gets the primary phone number (first in the collection).
        /// This is synchronized with the main view's PhoneNumberTextBox.
        /// </summary>
        public string PrimaryPhoneNumber
        {
            get => PhoneNumbers.FirstOrDefault()?.PhoneNumber ?? string.Empty;
        }

        /// <summary>
        /// Gets the count of phone numbers in the collection.
        /// </summary>
        public int Count => PhoneNumbers.Count;

        /// <summary>
        /// Gets whether the collection has any phone numbers.
        /// </summary>
        public bool HasPhoneNumbers => PhoneNumbers.Count > 0;

        /// <summary>
        /// Gets whether a phone number is currently selected.
        /// </summary>
        public bool HasSelectedPhoneNumber => SelectedPhoneNumber != null;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the PhoneNumbersCollectionModel class.
        /// </summary>
        public PhoneNumbersCollectionModel()
        {
            _phoneNumbers = new UFU2BulkObservableCollection<PhoneNumberModel>();
            _phoneNumbers.CollectionChanged += PhoneNumbers_CollectionChanged;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Adds a new phone number to the collection.
        /// The first phone number added is automatically marked as primary.
        /// </summary>
        /// <param name="phoneNumber">The phone number to add</param>
        /// <param name="phoneType">The type of phone number</param>
        /// <returns>The created PhoneNumberModel</returns>
        public PhoneNumberModel AddPhoneNumber(string phoneNumber, PhoneType phoneType)
        {
            var phoneModel = new PhoneNumberModel(phoneNumber, phoneType);

            // Set as primary if this is the first phone number
            phoneModel.IsPrimary = PhoneNumbers.Count == 0;

            PhoneNumbers.Add(phoneModel);
            return phoneModel;
        }

        /// <summary>
        /// Adds a phone number model to the collection.
        /// Ensures proper isPrimary property management - only the first phone number can be primary.
        /// </summary>
        /// <param name="phoneNumberModel">The phone number model to add</param>
        public void AddPhoneNumber(PhoneNumberModel phoneNumberModel)
        {
            if (phoneNumberModel != null)
            {
                // Ensure only the first phone number is marked as primary
                if (PhoneNumbers.Count == 0)
                {
                    phoneNumberModel.IsPrimary = true;
                }
                else
                {
                    phoneNumberModel.IsPrimary = false;
                }

                PhoneNumbers.Add(phoneNumberModel);
            }
        }

        /// <summary>
        /// Removes a phone number from the collection.
        /// </summary>
        /// <param name="phoneNumberModel">The phone number model to remove</param>
        /// <returns>True if the phone number was removed, false otherwise</returns>
        public bool RemovePhoneNumber(PhoneNumberModel phoneNumberModel)
        {
            if (phoneNumberModel != null && PhoneNumbers.Contains(phoneNumberModel))
            {
                return PhoneNumbers.Remove(phoneNumberModel);
            }
            return false;
        }

        /// <summary>
        /// Removes the currently selected phone number.
        /// </summary>
        /// <returns>True if a phone number was removed, false otherwise</returns>
        public bool RemoveSelectedPhoneNumber()
        {
            if (SelectedPhoneNumber != null)
            {
                var removed = RemovePhoneNumber(SelectedPhoneNumber);
                if (removed)
                {
                    SelectedPhoneNumber = null;
                }
                return removed;
            }
            return false;
        }

        /// <summary>
        /// Clears all phone numbers from the collection.
        /// </summary>
        public void Clear()
        {
            SelectedPhoneNumber = null;
            PhoneNumbers.Clear();
        }

        /// <summary>
        /// Sets the primary phone number (first in collection) from the main view.
        /// If the collection is empty, adds a new phone number.
        /// If the collection has items, updates the first phone number.
        /// Uses property change suppression to prevent circular events.
        /// Ensures proper isPrimary property management.
        /// </summary>
        /// <param name="phoneNumber">The phone number from the main view</param>
        public void SetPrimaryPhoneNumber(string phoneNumber)
        {
            try
            {
                _suppressPropertyChanged = true;

                if (string.IsNullOrWhiteSpace(phoneNumber))
                {
                    // If empty phone number and collection has items, remove the first one
                    if (PhoneNumbers.Count > 0)
                    {
                        PhoneNumbers.RemoveAt(0);

                        // Update isPrimary for remaining phone numbers
                        UpdatePrimaryPhoneDesignation();
                    }
                    return;
                }

                if (PhoneNumbers.Count == 0)
                {
                    // Add new phone number as primary (AddPhoneNumber will set IsPrimary = true)
                    AddPhoneNumber(phoneNumber, PhoneType.Mobile);
                }
                else
                {
                    // Update existing primary phone number and ensure it's marked as primary
                    PhoneNumbers[0].PhoneNumber = phoneNumber;
                    PhoneNumbers[0].IsPrimary = true;
                }
            }
            finally
            {
                _suppressPropertyChanged = false;
                // Manually trigger PropertyChanged for PrimaryPhoneNumber after suppression is lifted
                OnPropertyChanged(nameof(PrimaryPhoneNumber));
            }
        }

        /// <summary>
        /// Validates all phone numbers in the collection.
        /// </summary>
        /// <returns>True if all phone numbers are valid, false otherwise</returns>
        public bool IsValid()
        {
            return PhoneNumbers.All(phone => phone.IsValid());
        }

        /// <summary>
        /// Gets a list of all phone numbers as formatted strings.
        /// </summary>
        /// <returns>List of formatted phone numbers</returns>
        public List<string> GetAllPhoneNumbers()
        {
            return PhoneNumbers.Select(phone => phone.PhoneNumber).ToList();
        }

        /// <summary>
        /// Creates a copy of the current collection.
        /// </summary>
        /// <returns>A new PhoneNumbersCollectionModel with cloned phone numbers</returns>
        public PhoneNumbersCollectionModel Clone()
        {
            var cloned = new PhoneNumbersCollectionModel();
            foreach (var phone in PhoneNumbers)
            {
                cloned.AddPhoneNumber(phone.Clone());
            }
            return cloned;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Updates the isPrimary designation for all phone numbers in the collection.
        /// Ensures only the first phone number is marked as primary.
        /// </summary>
        private void UpdatePrimaryPhoneDesignation()
        {
            for (int i = 0; i < PhoneNumbers.Count; i++)
            {
                PhoneNumbers[i].IsPrimary = (i == 0);
            }
        }

        /// <summary>
        /// Handles changes to the phone numbers collection.
        /// Respects property change suppression to prevent circular events.
        /// </summary>
        private void PhoneNumbers_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // Notify UI of property changes when collection changes
            OnPropertyChanged(nameof(Count));
            OnPropertyChanged(nameof(HasPhoneNumbers));

            // Only notify PrimaryPhoneNumber changes if not suppressed
            if (!_suppressPropertyChanged)
            {
                OnPropertyChanged(nameof(PrimaryPhoneNumber));
            }
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Occurs when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Sets the property and raises PropertyChanged event if the value has changed.
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">The new value</param>
        /// <param name="propertyName">Name of the property (automatically provided)</param>
        /// <returns>True if the property was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Raises the PropertyChanged event.
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
