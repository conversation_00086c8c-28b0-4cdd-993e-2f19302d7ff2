---
description: Repository Information Overview
alwaysApply: true
---

# UFU2 Information

## Summary
UFU2 is a sophisticated Windows desktop application designed for Algerian business registration and client management. Built with WPF and .NET 8.0, it implements a robust MVVM architecture with MaterialDesign theming, Arabic RTL support, and comprehensive business logic for managing client data, activities, and regulatory compliance.

## Structure
- **Commands**: MVVM command implementations
- **Common**: Shared utilities, behaviors, converters, and extensions
- **Controls**: Custom WPF controls including image cropping
- **Converters**: WPF value converters for UI transformations
- **Database**: Schema definitions and reference data
- **Documentation**: Comprehensive technical documentation
- **Models**: Data models and entity definitions
- **Resources**: UI assets, styles, themes, and fonts
- **Services**: Business logic and data access services
- **ViewModels**: MVVM view models with smart batching
- **Views**: WPF views, dialogs, and user controls
- **Windows**: Custom window implementations

## Language & Runtime
**Language**: C#
**Version**: .NET 8.0
**Framework**: WPF (Windows Presentation Foundation)
**Build System**: MSBuild
**Package Manager**: NuGet

## Dependencies
**Main Dependencies**:
- MaterialDesignColors 5.2.1
- MaterialDesignThemes 5.2.1
- MaterialDesignThemes.MahApps 5.2.1
- Microsoft.Data.Sqlite 9.0.7
- Dapper 2.1.66
- Newtonsoft.Json 13.0.3
- Microsoft.Extensions.Caching.Memory 9.0.7

## Build & Installation
```bash
# Build the solution
dotnet build "UFU2.sln" -c Release

# Run the application
dotnet run --project "UFU2.csproj"

# Publish the application
dotnet publish "UFU2.csproj" -c Release -r win-x64 --self-contained
```

## Database
**Type**: SQLite
**ORM**: Dapper
**Schema Files**:
- Database/UFU2_Schema.sql
- Database/APP_Schema.sql
- Database/Archive_Schema.sql
**Reference Data**:
- Database/activity_Type.json
- Database/craft_Type.json
- Database/cpi_Location.json

## Architecture
**Pattern**: MVVM (Model-View-ViewModel)
**Key Components**:
- **BaseViewModel**: Sophisticated base class with priority-based PropertyChanged batching
- **ServiceLocator**: Centralized dependency injection container
- **DatabaseService**: Core database operations with Dapper
- **ClientDatabaseService**: Client CRUD operations with audit logging
- **ThemeManager**: MaterialDesign theme management
- **ValidationServices**: Business rule validation

## Performance Features
- **Smart Batching**: Adaptive notification system optimizing UI performance
- **Connection Pooling**: Efficient database connection management
- **Memory Management**: ResourceManager and WeakEventManager integration
- **Background Processing**: Async operations with proper cancellation support
- **Performance Monitoring**: Built-in query performance tracking