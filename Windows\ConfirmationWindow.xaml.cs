using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using UFU2.Common;
using UFU2.ViewModels;
using MaterialDesignThemes.Wpf;

namespace UFU2.Windows
{
    /// <summary>
    /// Standalone confirmation dialog window with MVVM support and MaterialDesign styling.
    /// Provides modal dialog behavior with customizable content, buttons, and keyboard shortcuts.
    /// Integrates with UFU2's theme system and logging infrastructure.
    /// </summary>
    public partial class ConfirmationWindow : Window
    {
        #region Private Fields

        private ConfirmationWindowViewModel? _viewModel;
        private bool _resultSet = false;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the ConfirmationWindow
        /// </summary>
        public ConfirmationWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        /// <summary>
        /// Initializes a new instance of the ConfirmationWindow with a ViewModel
        /// </summary>
        /// <param name="viewModel">The ViewModel to bind to this window</param>
        public ConfirmationWindow(ConfirmationWindowViewModel viewModel) : this()
        {
            SetViewModel(viewModel);
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the result of the confirmation dialog
        /// </summary>
        public bool? ConfirmationResult { get; private set; }

        /// <summary>
        /// Gets or sets whether the window should be displayed as topmost
        /// </summary>
        public bool IsTopmost
        {
            get => Topmost;
            set => Topmost = value;
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes window properties and event handlers
        /// </summary>
        private void InitializeWindow()
        {
            try
            {
                // Set window properties
                WindowStartupLocation = WindowStartupLocation.CenterOwner;
                ShowInTaskbar = false;
                ResizeMode = ResizeMode.NoResize;

                // Subscribe to events
                Loaded += OnWindowLoaded;
                Closing += OnWindowClosing;
                KeyDown += OnWindowKeyDown;

                LoggingService.LogInfo("ConfirmationWindow initialized", "ConfirmationWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing ConfirmationWindow: {ex.Message}", "ConfirmationWindow");
            }
        }

        /// <summary>
        /// Sets the ViewModel for this window
        /// </summary>
        /// <param name="viewModel">The ViewModel to set</param>
        public void SetViewModel(ConfirmationWindowViewModel viewModel)
        {
            try
            {
                _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
                DataContext = _viewModel;

                // Subscribe to ViewModel events
                _viewModel.CloseRequested += OnCloseRequested;
                _viewModel.PropertyChanged += OnViewModelPropertyChanged;

                LoggingService.LogInfo("ViewModel set for ConfirmationWindow", "ConfirmationWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting ViewModel: {ex.Message}", "ConfirmationWindow");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the window loaded event
        /// </summary>
        private void OnWindowLoaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Set focus to the primary button by default
                var primaryButton = FindName("PrimaryButton") as FrameworkElement;
                primaryButton?.Focus();

                // Apply theme-specific adjustments if needed
                ApplyThemeAdjustments();

                LoggingService.LogInfo("ConfirmationWindow loaded successfully", "ConfirmationWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in window loaded event: {ex.Message}", "ConfirmationWindow");
            }
        }

        /// <summary>
        /// Handles the window closing event
        /// </summary>
        private void OnWindowClosing(object? sender, CancelEventArgs e)
        {
            try
            {
                // If no result was explicitly set, treat as cancelled
                if (!_resultSet)
                {
                    ConfirmationResult = false;
                    LoggingService.LogInfo("ConfirmationWindow closed without explicit result - treating as cancelled", "ConfirmationWindow");
                }

                // Cleanup ViewModel if present
                if (_viewModel != null)
                {
                    _viewModel.CloseRequested -= OnCloseRequested;
                    _viewModel.PropertyChanged -= OnViewModelPropertyChanged;
                    _viewModel.Dispose();
                }

                LoggingService.LogInfo("ConfirmationWindow closing", "ConfirmationWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in window closing event: {ex.Message}", "ConfirmationWindow");
            }
        }

        /// <summary>
        /// Handles key down events for additional keyboard shortcuts
        /// </summary>
        private void OnWindowKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // Handle additional keyboard shortcuts
                switch (e.Key)
                {
                    case Key.F1:
                        // Show help or additional information if needed
                        e.Handled = true;
                        break;

                    case Key.Tab:
                        // Ensure proper tab navigation
                        HandleTabNavigation(e);
                        break;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling key down event: {ex.Message}", "ConfirmationWindow");
            }
        }

        /// <summary>
        /// Handles close requests from the ViewModel
        /// </summary>
        private void OnCloseRequested(object? sender, bool result)
        {
            try
            {
                ConfirmationResult = result;
                _resultSet = true;

                LoggingService.LogInfo($"Close requested with result: {result}", "ConfirmationWindow");

                // Close the window
                DialogResult = result;
                Close();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling close request: {ex.Message}", "ConfirmationWindow");
            }
        }

        /// <summary>
        /// Handles ViewModel property changes
        /// </summary>
        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {
                // Handle specific property changes if needed
                switch (e.PropertyName)
                {
                    case nameof(ConfirmationWindowViewModel.WindowTitle):
                        // Update window title if changed
                        break;

                    case nameof(ConfirmationWindowViewModel.IsTopmost):
                        // Update topmost property
                        if (_viewModel != null)
                        {
                            Topmost = _viewModel.IsTopmost;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling ViewModel property change: {ex.Message}", "ConfirmationWindow");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Applies theme-specific adjustments to the window
        /// </summary>
        private void ApplyThemeAdjustments()
        {
            try
            {
                // Apply any theme-specific adjustments here
                // This could include updating colors, fonts, or other visual elements
                // based on the current theme
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying theme adjustments: {ex.Message}", "ConfirmationWindow");
            }
        }

        /// <summary>
        /// Handles tab navigation within the dialog
        /// </summary>
        private void HandleTabNavigation(KeyEventArgs e)
        {
            try
            {
                // Ensure proper tab order and focus management
                // This helps with accessibility and keyboard navigation
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling tab navigation: {ex.Message}", "ConfirmationWindow");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Shows the confirmation window as a modal dialog
        /// </summary>
        /// <param name="owner">The owner window (optional)</param>
        /// <returns>True if confirmed, false if cancelled, null if closed without action</returns>
        public bool? ShowConfirmationDialog(Window? owner = null)
        {
            try
            {
                if (owner != null)
                {
                    Owner = owner;
                    WindowStartupLocation = WindowStartupLocation.CenterOwner;
                }
                else
                {
                    WindowStartupLocation = WindowStartupLocation.CenterScreen;
                }

                LoggingService.LogInfo("Showing ConfirmationWindow as modal dialog", "ConfirmationWindow");
                return ShowDialog();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing confirmation dialog: {ex.Message}", "ConfirmationWindow");
                return false;
            }
        }

        /// <summary>
        /// Shows the confirmation window as a non-modal window
        /// </summary>
        /// <param name="owner">The owner window (optional)</param>
        public void ShowConfirmationWindow(Window? owner = null)
        {
            try
            {
                if (owner != null)
                {
                    Owner = owner;
                }

                LoggingService.LogInfo("Showing ConfirmationWindow as non-modal window", "ConfirmationWindow");
                Show();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing confirmation window: {ex.Message}", "ConfirmationWindow");
            }
        }

        #endregion

        #region Static Factory Methods

        /// <summary>
        /// Shows a Yes/No confirmation dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="content">Dialog content</param>
        /// <param name="owner">Owner window (optional)</param>
        /// <param name="isTopmost">Whether the dialog should be topmost</param>
        /// <returns>True if Yes was clicked, false if No was clicked, null if cancelled</returns>
        public static bool? ShowYesNoDialog(string title, string content, Window? owner = null, bool isTopmost = false)
        {
            try
            {
                var viewModel = ConfirmationWindowViewModel.CreateYesNoDialog(title, content, isTopmost);
                var window = new ConfirmationWindow(viewModel);
                return window.ShowConfirmationDialog(owner);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing Yes/No dialog: {ex.Message}", "ConfirmationWindow");
                return false;
            }
        }

        /// <summary>
        /// Shows an exit confirmation dialog
        /// </summary>
        /// <param name="applicationName">Name of the application</param>
        /// <param name="owner">Owner window (optional)</param>
        /// <param name="isTopmost">Whether the dialog should be topmost</param>
        /// <returns>True if Exit was clicked, false if Cancel was clicked</returns>
        public static bool ShowExitConfirmation(string applicationName = "UFU2", Window? owner = null, bool isTopmost = true)
        {
            try
            {
                var viewModel = ConfirmationWindowViewModel.CreateExitConfirmation(applicationName, isTopmost);
                var window = new ConfirmationWindow(viewModel);
                return window.ShowConfirmationDialog(owner) == true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing exit confirmation: {ex.Message}", "ConfirmationWindow");
                return false;
            }
        }

        /// <summary>
        /// Shows a delete confirmation dialog
        /// </summary>
        /// <param name="itemName">Name of the item to delete</param>
        /// <param name="owner">Owner window (optional)</param>
        /// <param name="isTopmost">Whether the dialog should be topmost</param>
        /// <returns>True if Delete was clicked, false if Cancel was clicked</returns>
        public static bool ShowDeleteConfirmation(string itemName, Window? owner = null, bool isTopmost = false)
        {
            try
            {
                var viewModel = ConfirmationWindowViewModel.CreateDeleteConfirmation(itemName, isTopmost);
                var window = new ConfirmationWindow(viewModel);
                return window.ShowConfirmationDialog(owner) == true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing delete confirmation: {ex.Message}", "ConfirmationWindow");
                return false;
            }
        }

        /// <summary>
        /// Shows a save confirmation dialog
        /// </summary>
        /// <param name="fileName">Name of the file to save</param>
        /// <param name="owner">Owner window (optional)</param>
        /// <param name="isTopmost">Whether the dialog should be topmost</param>
        /// <returns>True if Save was clicked, false if Don't Save was clicked</returns>
        public static bool ShowSaveConfirmation(string fileName, Window? owner = null, bool isTopmost = false)
        {
            try
            {
                var viewModel = ConfirmationWindowViewModel.CreateSaveConfirmation(fileName, isTopmost);
                var window = new ConfirmationWindow(viewModel);
                return window.ShowConfirmationDialog(owner) == true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing save confirmation: {ex.Message}", "ConfirmationWindow");
                return false;
            }
        }

        /// <summary>
        /// Shows a custom confirmation dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="content">Dialog content</param>
        /// <param name="primaryText">Primary button text</param>
        /// <param name="secondaryText">Secondary button text</param>
        /// <param name="owner">Owner window (optional)</param>
        /// <param name="icon">Icon to display</param>
        /// <param name="isTopmost">Whether the dialog should be topmost</param>
        /// <returns>True if primary button was clicked, false if secondary button was clicked</returns>
        public static bool ShowCustomDialog(
            string title,
            string content,
            string primaryText,
            string secondaryText,
            Window? owner = null,
            PackIconKind icon = PackIconKind.Information,
            bool isTopmost = false)
        {
            try
            {
                var viewModel = ConfirmationWindowViewModel.CreateCustomDialog(
                    title, content, primaryText, secondaryText, icon, isTopmost);
                var window = new ConfirmationWindow(viewModel);
                return window.ShowConfirmationDialog(owner) == true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing custom dialog: {ex.Message}", "ConfirmationWindow");
                return false;
            }
        }

        #endregion
    }
}
