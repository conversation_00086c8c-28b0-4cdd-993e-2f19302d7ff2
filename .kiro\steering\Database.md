---
inclusion: always
---

# UFU2 Database Best Practices & Performance Rules

This document outlines the mandatory best practices and performance rules for all database operations within the UFU2 application. These rules are derived from the existing UFU2 architecture and general SQLite performance principles to ensure data integrity, optimal performance, and maintainability.

## 1. Core Database Architecture Principles (UFU2 Specific)

*   **Multi-Database Design:** UFU2 uses three distinct SQLite databases:
    *   `UFU2_ClientData.db`: For primary operational client data.
    *   `UFU2_ReferenceData.db`: For static reference data (e.g., activity codes).
    *   `UFU2_ArchiveData.db`: For audit logs and historical data.
*   **ORM:** Use **Dapper** for all data access operations. It provides a good balance of performance and simplicity.
*   **Centralized Access:** All database interactions **must** go through the `DatabaseService`. Direct `SqliteConnection` usage outside this service is prohibited.
*   **Service Interaction:** Business logic services (e.g., `ClientDatabaseService`) **must** use `DatabaseService` to perform database operations.

## 2. Connection Management

*   **Use `DatabaseService`:** Never create `SqliteConnection` instances directly. Always use `DatabaseService` methods which manage connections internally.
*   **Async/Await:** All database calls **must** be `async/await` to prevent blocking the UI thread. (`DatabaseService` provides `ExecuteAsync`, `QueryAsync`).
*   **Parameterized Queries:** **Always** use parameterized queries with Dapper to prevent SQL injection. Never concatenate user input into SQL strings.
    ```csharp
    // Correct
    var clients = await _databaseService.QueryAsync<Client>("SELECT * FROM Clients WHERE Name = @Name", new { Name = searchTerm });
    // Incorrect (DON'T DO THIS)
    // var clients = await _databaseService.QueryAsync<Client>($"SELECT * FROM Clients WHERE Name = '{searchTerm}'");
    ```
*   **Transactions:** Wrap multi-step, related database operations in transactions using `DatabaseService.BeginTransactionAsync()`, `CommitTransactionAsync()`, and `RollbackTransactionAsync()` to ensure data consistency.
*   **PRAGMA Configuration:** Rely on the `DatabaseService` to apply necessary PRAGMA settings (like `WAL`, `synchronous`, `foreign_keys`) during its initialization. Do not re-configure these per connection unless explicitly required for a special case and documented.

## 3. Query Optimization & Performance

*   **Specific Columns:** Select only the columns you need. Avoid `SELECT *` in production queries, especially for large tables or tables with many columns.
    ```csharp
    // Prefer
    var summaries = await _databaseService.QueryAsync<ClientSummary>("SELECT Uid, NameAr FROM Clients WHERE ...");
    ```
*   **Async Operations:** All database calls (`QueryAsync`, `ExecuteAsync`) must be used asynchronously. Move heavy database work off the UI thread.
*   **Indexing:** Understand that indexes exist (as defined in schema files) and write queries that can utilize them effectively (filtering, sorting on indexed columns). New schema changes should consider necessary indexes.
*   **Batching for UI Updates:** Leverage the `BaseViewModel`'s smart batching system for UI updates triggered by database changes, not for the database operations themselves.

## 4. Data Integrity & Security

*   **Foreign Keys:** The database schema enforces foreign key constraints. Code must respect these relationships (e.g., deleting a client should cascade delete related activities).
*   **Input Validation:** Validate and sanitize data *before* attempting database insertion/updating. Use services like `ClientValidationService`.
*   **SQL Injection:** Prevented by using parameterized queries (see Connection Management).

## 5. Error Handling & Monitoring

*   **Use `ErrorManager`:** Handle database-related exceptions using the `ErrorManager` for consistent user-facing messages (especially in Arabic) and logging.
*   **Use `LoggingService`:** Log significant database operations, performance metrics, and errors using the `LoggingService`.
*   **Retry Logic:** For transient failures (like database locks), consider using retry mechanisms like the one potentially found in `ClientDatabaseService.ExecuteWithRetryAsync`.

## 6. Specific UFU2 Patterns & Practices

*   **Service Locator for DB Services:** Obtain instances of `DatabaseService` (and specialized services like `ClientDatabaseService`) via `ServiceLocator.GetService<T>()`.
*   **Schema Management:** Database schema is managed via SQL files (`*_Schema.sql`). Any structural changes must be made through these files and potentially a migration strategy.
*   **Performance Monitoring:** Utilize `DatabasePerformanceMonitoringService` for tracking query performance.
*   **Audit Logging:** Use the Archive database (`UFU2_ArchiveData.db`) via the appropriate service for logging data changes.

## 7. Anti-Patterns to Avoid (Based on General Standards)

*   ❌ **Sharing Connections:** Do not share `DatabaseService` instances or underlying connections across threads or ViewModels in an unsafe manner. `DatabaseService` is designed for safe concurrent use.
*   ❌ **Missing Transactions:** Do not perform multiple related writes (e.g., creating a client and their activities) outside of a transaction.
*   ❌ **Synchronous Calls:** Avoid synchronous database calls (`Query`, `Execute`) on the UI thread.
*   ❌ **Inefficient LIKE Searches:** While not explicitly configured in the provided analysis, be cautious with `LIKE '%term%'`. If full-text search capabilities are needed, investigate FTS5.
*   ❌ **Ignoring Errors:** Do not silently ignore database exceptions. Always handle them appropriately.
