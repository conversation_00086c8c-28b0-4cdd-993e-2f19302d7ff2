using System;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;
using UFU2.Models;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter to determine if a phone number is the primary phone number in a collection.
    /// Used to show the star indicator for the first phone number in the list.
    /// </summary>
    public class IsPrimaryPhoneConverter : IMultiValueConverter
    {
        /// <summary>
        /// Converts phone number and collection to Visibility indicating if it's primary.
        /// </summary>
        /// <param name="values">Array containing [PhoneNumberModel, PhoneNumbersCollectionModel]</param>
        /// <param name="targetType">Target type (not used)</param>
        /// <param name="parameter">Parameter (not used)</param>
        /// <param name="culture">Culture (not used)</param>
        /// <returns>Visibility.Visible if the phone number is primary, Visibility.Collapsed otherwise</returns>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (values?.Length >= 2 &&
                    values[0] is PhoneNumberModel phoneNumber &&
                    values[1] is PhoneNumbersCollectionModel collection)
                {
                    bool isPrimary = collection.PhoneNumbers.FirstOrDefault() == phoneNumber;
                    return isPrimary ? Visibility.Visible : Visibility.Collapsed;
                }
            }
            catch
            {
                // Return collapsed on any error
            }

            return Visibility.Collapsed;
        }

        /// <summary>
        /// Not implemented for this converter.
        /// </summary>
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
