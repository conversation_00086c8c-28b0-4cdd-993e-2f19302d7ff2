using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Database performance monitoring service for UFU2 application.
    /// Provides query performance analysis, slow query detection, index effectiveness monitoring,
    /// and automated database maintenance operations.
    /// </summary>
    public class DatabasePerformanceMonitoringService : IDisposable
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;
        private readonly Timer _maintenanceTimer;
        private readonly object _performanceLock = new object();
        private readonly List<QueryPerformanceMetric> _performanceMetrics = new List<QueryPerformanceMetric>();
        private bool _disposed = false;

        // Performance thresholds
        private const int SlowQueryThresholdMs = 100;
        private const int MaxPerformanceMetricsCount = 1000;
        private const int MaintenanceIntervalHours = 24;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the DatabasePerformanceMonitoringService.
        /// </summary>
        /// <param name="databaseService">The database service instance</param>
        public DatabasePerformanceMonitoringService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));

            // Initialize maintenance timer (runs every 24 hours)
            _maintenanceTimer = new Timer(
                callback: async _ => await PerformScheduledMaintenanceAsync(),
                state: null,
                dueTime: TimeSpan.FromHours(MaintenanceIntervalHours),
                period: TimeSpan.FromHours(MaintenanceIntervalHours)
            );

            LoggingService.LogInfo("DatabasePerformanceMonitoringService initialized", "DatabasePerformanceMonitoringService");
        }

        #endregion

        #region Query Performance Monitoring

        /// <summary>
        /// Executes a query with performance monitoring and analysis.
        /// </summary>
        /// <typeparam name="T">The return type</typeparam>
        /// <param name="query">The SQL query</param>
        /// <param name="parameters">Query parameters</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <returns>Query results</returns>
        public async Task<IEnumerable<T>> ExecuteQueryWithMonitoringAsync<T>(
            string query, 
            object? parameters = null, 
            string operationName = "Unknown")
        {
            var stopwatch = Stopwatch.StartNew();
            var startTime = DateTime.UtcNow;

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Execute query plan analysis if it's a SELECT query
                QueryPlanAnalysis? planAnalysis = null;
                if (query.TrimStart().StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
                {
                    planAnalysis = await AnalyzeQueryPlanAsync(connection, query, parameters);
                }

                // Execute the actual query
                var results = await connection.QueryAsync<T>(query, parameters);
                stopwatch.Stop();

                // Record performance metrics
                var metric = new QueryPerformanceMetric
                {
                    Query = query,
                    OperationName = operationName,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                    StartTime = startTime,
                    EndTime = DateTime.UtcNow,
                    RowCount = results.Count(),
                    QueryPlan = planAnalysis
                };

                RecordPerformanceMetric(metric);

                // Log slow queries
                if (stopwatch.ElapsedMilliseconds > SlowQueryThresholdMs)
                {
                    LoggingService.LogWarning(
                        $"Slow query detected ({stopwatch.ElapsedMilliseconds}ms): {operationName} - {query.Substring(0, Math.Min(100, query.Length))}...",
                        "DatabasePerformanceMonitoringService");
                }

                return results;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                LoggingService.LogError(
                    $"Query execution failed after {stopwatch.ElapsedMilliseconds}ms: {operationName} - {ex.Message}",
                    "DatabasePerformanceMonitoringService");
                throw;
            }
        }

        /// <summary>
        /// Executes a non-query command with performance monitoring.
        /// </summary>
        /// <param name="command">The SQL command</param>
        /// <param name="parameters">Command parameters</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <returns>Number of affected rows</returns>
        public async Task<int> ExecuteCommandWithMonitoringAsync(
            string command, 
            object? parameters = null, 
            string operationName = "Unknown")
        {
            var stopwatch = Stopwatch.StartNew();
            var startTime = DateTime.UtcNow;

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var affectedRows = await connection.ExecuteAsync(command, parameters);
                stopwatch.Stop();

                // Record performance metrics
                var metric = new QueryPerformanceMetric
                {
                    Query = command,
                    OperationName = operationName,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                    StartTime = startTime,
                    EndTime = DateTime.UtcNow,
                    RowCount = affectedRows
                };

                RecordPerformanceMetric(metric);

                // Log slow commands
                if (stopwatch.ElapsedMilliseconds > SlowQueryThresholdMs)
                {
                    LoggingService.LogWarning(
                        $"Slow command detected ({stopwatch.ElapsedMilliseconds}ms): {operationName} - {command.Substring(0, Math.Min(100, command.Length))}...",
                        "DatabasePerformanceMonitoringService");
                }

                return affectedRows;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                LoggingService.LogError(
                    $"Command execution failed after {stopwatch.ElapsedMilliseconds}ms: {operationName} - {ex.Message}",
                    "DatabasePerformanceMonitoringService");
                throw;
            }
        }

        /// <summary>
        /// Analyzes query execution plan using EXPLAIN QUERY PLAN.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="query">The query to analyze</param>
        /// <param name="parameters">Query parameters</param>
        /// <returns>Query plan analysis</returns>
        private async Task<QueryPlanAnalysis> AnalyzeQueryPlanAsync(
            SqliteConnection connection, 
            string query, 
            object? parameters)
        {
            try
            {
                var explainQuery = $"EXPLAIN QUERY PLAN {query}";
                var planRows = await connection.QueryAsync<QueryPlanRow>(explainQuery, parameters);

                var analysis = new QueryPlanAnalysis
                {
                    PlanRows = planRows.ToList(),
                    HasTableScan = planRows.Any(r => r.Detail.Contains("SCAN TABLE", StringComparison.OrdinalIgnoreCase)),
                    HasIndexUsage = planRows.Any(r => r.Detail.Contains("USING INDEX", StringComparison.OrdinalIgnoreCase)),
                    TablesAccessed = ExtractTablesFromPlan(planRows),
                    IndexesUsed = ExtractIndexesFromPlan(planRows)
                };

                return analysis;
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to analyze query plan: {ex.Message}", "DatabasePerformanceMonitoringService");
                return new QueryPlanAnalysis { PlanRows = new List<QueryPlanRow>() };
            }
        }

        #endregion

        #region Performance Metrics Management

        /// <summary>
        /// Records a performance metric with thread safety.
        /// </summary>
        /// <param name="metric">The performance metric to record</param>
        private void RecordPerformanceMetric(QueryPerformanceMetric metric)
        {
            lock (_performanceLock)
            {
                _performanceMetrics.Add(metric);

                // Maintain maximum metrics count
                if (_performanceMetrics.Count > MaxPerformanceMetricsCount)
                {
                    var toRemove = _performanceMetrics.Count - MaxPerformanceMetricsCount;
                    _performanceMetrics.RemoveRange(0, toRemove);
                }
            }

            LoggingService.LogDebug(
                $"Performance metric recorded: {metric.OperationName} - {metric.ExecutionTimeMs}ms",
                "DatabasePerformanceMonitoringService");
        }

        /// <summary>
        /// Gets performance metrics for a specific time period.
        /// </summary>
        /// <param name="fromTime">Start time (UTC)</param>
        /// <param name="toTime">End time (UTC)</param>
        /// <returns>Performance metrics within the time period</returns>
        public List<QueryPerformanceMetric> GetPerformanceMetrics(DateTime fromTime, DateTime toTime)
        {
            lock (_performanceLock)
            {
                return _performanceMetrics
                    .Where(m => m.StartTime >= fromTime && m.StartTime <= toTime)
                    .OrderByDescending(m => m.StartTime)
                    .ToList();
            }
        }

        /// <summary>
        /// Gets slow query metrics above the threshold.
        /// </summary>
        /// <param name="thresholdMs">Execution time threshold in milliseconds</param>
        /// <returns>Slow query metrics</returns>
        public List<QueryPerformanceMetric> GetSlowQueries(int thresholdMs = SlowQueryThresholdMs)
        {
            lock (_performanceLock)
            {
                return _performanceMetrics
                    .Where(m => m.ExecutionTimeMs > thresholdMs)
                    .OrderByDescending(m => m.ExecutionTimeMs)
                    .ToList();
            }
        }

        /// <summary>
        /// Generates a performance report for the specified time period.
        /// </summary>
        /// <param name="fromTime">Start time (UTC)</param>
        /// <param name="toTime">End time (UTC)</param>
        /// <returns>Performance report</returns>
        public PerformanceReport GeneratePerformanceReport(DateTime fromTime, DateTime toTime)
        {
            var metrics = GetPerformanceMetrics(fromTime, toTime);

            if (!metrics.Any())
            {
                return new PerformanceReport
                {
                    ReportPeriod = $"{fromTime:yyyy-MM-dd HH:mm} - {toTime:yyyy-MM-dd HH:mm}",
                    TotalQueries = 0,
                    AverageExecutionTimeMs = 0,
                    SlowQueryCount = 0,
                    TopSlowQueries = new List<QueryPerformanceMetric>()
                };
            }

            return new PerformanceReport
            {
                ReportPeriod = $"{fromTime:yyyy-MM-dd HH:mm} - {toTime:yyyy-MM-dd HH:mm}",
                TotalQueries = metrics.Count,
                AverageExecutionTimeMs = metrics.Average(m => m.ExecutionTimeMs),
                MaxExecutionTimeMs = metrics.Max(m => m.ExecutionTimeMs),
                MinExecutionTimeMs = metrics.Min(m => m.ExecutionTimeMs),
                SlowQueryCount = metrics.Count(m => m.ExecutionTimeMs > SlowQueryThresholdMs),
                TopSlowQueries = metrics
                    .Where(m => m.ExecutionTimeMs > SlowQueryThresholdMs)
                    .OrderByDescending(m => m.ExecutionTimeMs)
                    .Take(10)
                    .ToList(),
                TableScanCount = metrics.Count(m => m.QueryPlan?.HasTableScan == true),
                IndexUsageCount = metrics.Count(m => m.QueryPlan?.HasIndexUsage == true)
            };
        }

        #endregion

        #region Index Effectiveness Analysis

        /// <summary>
        /// Analyzes index effectiveness and provides recommendations.
        /// </summary>
        /// <returns>Index effectiveness analysis</returns>
        public async Task<IndexEffectivenessAnalysis> AnalyzeIndexEffectivenessAsync()
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var analysis = new IndexEffectivenessAnalysis();

                // Get all indexes
                var indexes = await GetAllIndexesAsync(connection);
                analysis.TotalIndexes = indexes.Count;

                // Analyze each index
                foreach (var index in indexes)
                {
                    var indexStats = await GetIndexStatisticsAsync(connection, index.Name);
                    if (indexStats != null)
                    {
                        analysis.IndexStatistics.Add(indexStats);
                    }
                }

                // Identify unused indexes
                analysis.UnusedIndexes = analysis.IndexStatistics
                    .Where(stats => stats.UsageCount == 0)
                    .Select(stats => stats.IndexName)
                    .ToList();

                // Generate recommendations
                analysis.Recommendations = GenerateIndexRecommendations(analysis);

                LoggingService.LogInfo($"Index effectiveness analysis completed: {analysis.TotalIndexes} indexes analyzed", "DatabasePerformanceMonitoringService");
                return analysis;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Index effectiveness analysis failed: {ex.Message}", "DatabasePerformanceMonitoringService");
                throw;
            }
        }

        /// <summary>
        /// Gets all indexes in the database.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <returns>List of database indexes</returns>
        private async Task<List<DatabaseIndex>> GetAllIndexesAsync(SqliteConnection connection)
        {
            const string query = @"
                SELECT name, tbl_name as TableName, sql as Definition
                FROM sqlite_master 
                WHERE type = 'index' 
                AND name NOT LIKE 'sqlite_%'
                ORDER BY name";

            var results = await connection.QueryAsync<DatabaseIndex>(query);
            return results.ToList();
        }

        /// <summary>
        /// Gets statistics for a specific index.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="indexName">Name of the index</param>
        /// <returns>Index statistics</returns>
        private async Task<IndexStatistics?> GetIndexStatisticsAsync(SqliteConnection connection, string indexName)
        {
            try
            {
                // Get basic index information
                const string indexInfoQuery = @"
                    SELECT name, tbl_name as TableName, sql as Definition
                    FROM sqlite_master 
                    WHERE type = 'index' AND name = @IndexName";

                var indexInfo = await connection.QueryFirstOrDefaultAsync<DatabaseIndex>(
                    indexInfoQuery, new { IndexName = indexName });

                if (indexInfo == null) return null;

                // Estimate usage based on recent performance metrics
                int usageCount;
                lock (_performanceLock)
                {
                    usageCount = _performanceMetrics
                        .Count(m => m.QueryPlan?.IndexesUsed.Contains(indexName) == true);
                }

                return new IndexStatistics
                {
                    IndexName = indexName,
                    TableName = indexInfo.TableName,
                    Definition = indexInfo.Definition ?? string.Empty,
                    UsageCount = usageCount,
                    LastUsed = GetLastIndexUsage(indexName)
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to get statistics for index {indexName}: {ex.Message}", "DatabasePerformanceMonitoringService");
                return null;
            }
        }

        /// <summary>
        /// Gets the last usage time for an index.
        /// </summary>
        /// <param name="indexName">Name of the index</param>
        /// <returns>Last usage time or null if never used</returns>
        private DateTime? GetLastIndexUsage(string indexName)
        {
            lock (_performanceLock)
            {
                return _performanceMetrics
                    .Where(m => m.QueryPlan?.IndexesUsed.Contains(indexName) == true)
                    .OrderByDescending(m => m.StartTime)
                    .FirstOrDefault()?.StartTime;
            }
        }

        /// <summary>
        /// Generates index optimization recommendations.
        /// </summary>
        /// <param name="analysis">Index effectiveness analysis</param>
        /// <returns>List of recommendations</returns>
        private List<string> GenerateIndexRecommendations(IndexEffectivenessAnalysis analysis)
        {
            var recommendations = new List<string>();

            // Recommend dropping unused indexes
            if (analysis.UnusedIndexes.Any())
            {
                recommendations.Add($"Consider dropping {analysis.UnusedIndexes.Count} unused indexes: {string.Join(", ", analysis.UnusedIndexes.Take(5))}");
            }

            // Recommend indexes for frequently scanned tables
            var frequentTableScans = GetFrequentTableScans();
            foreach (var table in frequentTableScans.Take(3))
            {
                recommendations.Add($"Consider adding indexes for table '{table}' which frequently uses table scans");
            }

            // Recommend composite indexes for common query patterns
            var commonPatterns = AnalyzeCommonQueryPatterns();
            foreach (var pattern in commonPatterns.Take(2))
            {
                recommendations.Add($"Consider composite index for common query pattern: {pattern}");
            }

            return recommendations;
        }

        #endregion

        #region Database Maintenance

        /// <summary>
        /// Performs scheduled database maintenance operations.
        /// </summary>
        private async Task PerformScheduledMaintenanceAsync()
        {
            try
            {
                LoggingService.LogInfo("Starting scheduled database maintenance", "DatabasePerformanceMonitoringService");

                await PerformDatabaseMaintenanceAsync();

                // Clean up old performance metrics (keep last 7 days)
                CleanupOldPerformanceMetrics(TimeSpan.FromDays(7));

                LoggingService.LogInfo("Scheduled database maintenance completed successfully", "DatabasePerformanceMonitoringService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Scheduled database maintenance failed: {ex.Message}", "DatabasePerformanceMonitoringService");
            }
        }

        /// <summary>
        /// Performs comprehensive database maintenance operations.
        /// </summary>
        /// <returns>Maintenance operation results</returns>
        public async Task<MaintenanceResult> PerformDatabaseMaintenanceAsync()
        {
            var result = new MaintenanceResult { StartTime = DateTime.UtcNow };
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                LoggingService.LogInfo("Starting comprehensive database maintenance", "DatabasePerformanceMonitoringService");

                // Get database size before maintenance
                result.DatabaseSizeBeforeMB = await GetDatabaseSizeMBAsync(connection);

                // Perform VACUUM operation
                var vacuumStopwatch = Stopwatch.StartNew();
                await connection.ExecuteAsync("VACUUM");
                vacuumStopwatch.Stop();
                result.VacuumDurationMs = vacuumStopwatch.ElapsedMilliseconds;
                LoggingService.LogInfo($"VACUUM completed in {vacuumStopwatch.ElapsedMilliseconds}ms", "DatabasePerformanceMonitoringService");

                // Perform ANALYZE operation
                var analyzeStopwatch = Stopwatch.StartNew();
                await connection.ExecuteAsync("ANALYZE");
                analyzeStopwatch.Stop();
                result.AnalyzeDurationMs = analyzeStopwatch.ElapsedMilliseconds;
                LoggingService.LogInfo($"ANALYZE completed in {analyzeStopwatch.ElapsedMilliseconds}ms", "DatabasePerformanceMonitoringService");

                // Update database statistics
                await UpdateDatabaseStatisticsAsync(connection);

                // Get database size after maintenance
                result.DatabaseSizeAfterMB = await GetDatabaseSizeMBAsync(connection);
                result.SpaceReclaimedMB = result.DatabaseSizeBeforeMB - result.DatabaseSizeAfterMB;

                stopwatch.Stop();
                result.TotalDurationMs = stopwatch.ElapsedMilliseconds;
                result.Success = true;
                result.EndTime = DateTime.UtcNow;

                LoggingService.LogInfo(
                    $"Database maintenance completed successfully in {stopwatch.ElapsedMilliseconds}ms. " +
                    $"Space reclaimed: {result.SpaceReclaimedMB:F2}MB",
                    "DatabasePerformanceMonitoringService");

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.TotalDurationMs = stopwatch.ElapsedMilliseconds;
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;

                LoggingService.LogError($"Database maintenance failed: {ex.Message}", "DatabasePerformanceMonitoringService");
                throw;
            }
        }

        /// <summary>
        /// Gets the database size in megabytes.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <returns>Database size in MB</returns>
        private async Task<double> GetDatabaseSizeMBAsync(SqliteConnection connection)
        {
            try
            {
                var pageCount = await connection.ExecuteScalarAsync<long>("PRAGMA page_count");
                var pageSize = await connection.ExecuteScalarAsync<long>("PRAGMA page_size");
                var sizeBytes = pageCount * pageSize;
                return sizeBytes / (1024.0 * 1024.0);
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to get database size: {ex.Message}", "DatabasePerformanceMonitoringService");
                return 0;
            }
        }

        /// <summary>
        /// Updates database statistics for query optimization.
        /// </summary>
        /// <param name="connection">Database connection</param>
        private async Task UpdateDatabaseStatisticsAsync(SqliteConnection connection)
        {
            try
            {
                // Get all user tables
                const string tablesQuery = @"
                    SELECT name FROM sqlite_master 
                    WHERE type = 'table' 
                    AND name NOT LIKE 'sqlite_%'";

                var tables = await connection.QueryAsync<string>(tablesQuery);

                foreach (var table in tables)
                {
                    try
                    {
                        await connection.ExecuteAsync($"ANALYZE {table}");
                        LoggingService.LogDebug($"Updated statistics for table: {table}", "DatabasePerformanceMonitoringService");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogWarning($"Failed to update statistics for table {table}: {ex.Message}", "DatabasePerformanceMonitoringService");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to update database statistics: {ex.Message}", "DatabasePerformanceMonitoringService");
            }
        }

        /// <summary>
        /// Cleans up old performance metrics to prevent memory bloat.
        /// </summary>
        /// <param name="maxAge">Maximum age of metrics to keep</param>
        private void CleanupOldPerformanceMetrics(TimeSpan maxAge)
        {
            lock (_performanceLock)
            {
                var cutoffTime = DateTime.UtcNow - maxAge;
                var initialCount = _performanceMetrics.Count;

                _performanceMetrics.RemoveAll(m => m.StartTime < cutoffTime);

                var removedCount = initialCount - _performanceMetrics.Count;
                if (removedCount > 0)
                {
                    LoggingService.LogInfo($"Cleaned up {removedCount} old performance metrics", "DatabasePerformanceMonitoringService");
                }
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Extracts table names from query plan rows.
        /// </summary>
        /// <param name="planRows">Query plan rows</param>
        /// <returns>List of table names</returns>
        private List<string> ExtractTablesFromPlan(IEnumerable<QueryPlanRow> planRows)
        {
            var tables = new List<string>();
            
            foreach (var row in planRows)
            {
                // Extract table names from plan details
                var detail = row.Detail.ToUpperInvariant();
                if (detail.Contains("TABLE"))
                {
                    var parts = detail.Split(' ');
                    for (int i = 0; i < parts.Length - 1; i++)
                    {
                        if (parts[i] == "TABLE" && !string.IsNullOrEmpty(parts[i + 1]))
                        {
                            tables.Add(parts[i + 1].ToLowerInvariant());
                        }
                    }
                }
            }

            return tables.Distinct().ToList();
        }

        /// <summary>
        /// Extracts index names from query plan rows.
        /// </summary>
        /// <param name="planRows">Query plan rows</param>
        /// <returns>List of index names</returns>
        private List<string> ExtractIndexesFromPlan(IEnumerable<QueryPlanRow> planRows)
        {
            var indexes = new List<string>();
            
            foreach (var row in planRows)
            {
                var detail = row.Detail.ToUpperInvariant();
                if (detail.Contains("USING INDEX"))
                {
                    var indexStart = detail.IndexOf("USING INDEX") + "USING INDEX".Length;
                    var remaining = detail.Substring(indexStart).Trim();
                    var indexName = remaining.Split(' ')[0];
                    if (!string.IsNullOrEmpty(indexName))
                    {
                        indexes.Add(indexName.ToLowerInvariant());
                    }
                }
            }

            return indexes.Distinct().ToList();
        }

        /// <summary>
        /// Gets frequently scanned tables from performance metrics.
        /// </summary>
        /// <returns>List of table names with frequent table scans</returns>
        private List<string> GetFrequentTableScans()
        {
            lock (_performanceLock)
            {
                return _performanceMetrics
                    .Where(m => m.QueryPlan?.HasTableScan == true)
                    .SelectMany(m => m.QueryPlan?.TablesAccessed ?? new List<string>())
                    .GroupBy(table => table)
                    .OrderByDescending(g => g.Count())
                    .Select(g => g.Key)
                    .Take(10)
                    .ToList();
            }
        }

        /// <summary>
        /// Analyzes common query patterns for index recommendations.
        /// </summary>
        /// <returns>List of common query patterns</returns>
        private List<string> AnalyzeCommonQueryPatterns()
        {
            // This is a simplified implementation
            // In a real scenario, you would analyze WHERE clauses, JOIN conditions, etc.
            return new List<string>
            {
                "Consider indexes on frequently filtered columns",
                "Consider composite indexes for multi-column WHERE clauses"
            };
        }

        #endregion

        #region Phase 2B Index Performance Validation

        /// <summary>
        /// Validates the performance improvement from high-priority database indexes.
        /// Tests Arabic search, activity queries, and phone number operations.
        /// </summary>
        /// <param name="testClientUid">Client UID to use for testing</param>
        /// <param name="iterations">Number of test iterations to perform</param>
        /// <returns>Performance comparison results</returns>
        public async Task<Dictionary<string, PerformanceComparisonResult>> ValidateHighPriorityIndexPerformanceAsync(
            string testClientUid,
            int iterations = 10)
        {
            LoggingService.LogInfo($"Starting high-priority index performance validation with {iterations} iterations", "DatabasePerformanceMonitoringService");

            var results = new Dictionary<string, PerformanceComparisonResult>();

            try
            {
                // Test 1: Arabic Search Performance (idx_clients_arabic_search)
                results["ArabicSearch"] = await TestArabicSearchPerformanceAsync(testClientUid, iterations);

                // Test 2: Activity Type + Client Query Performance (idx_activities_type_client)
                results["ActivityTypeClient"] = await TestActivityTypeClientPerformanceAsync(testClientUid, iterations);

                // Test 3: Phone Number Composite Performance (idx_phone_numbers_composite)
                results["PhoneNumberComposite"] = await TestPhoneNumberCompositePerformanceAsync(testClientUid, iterations);

                // Log summary results
                foreach (var kvp in results)
                {
                    LoggingService.LogInfo($"Phase 2B {kvp.Key} Performance: {kvp.Value.Summary}", "DatabasePerformanceMonitoringService");
                }

                return results;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Phase 2B index performance validation failed: {ex.Message}", "DatabasePerformanceMonitoringService");
                throw;
            }
        }

        /// <summary>
        /// Tests Arabic search performance with and without the composite index.
        /// </summary>
        private async Task<PerformanceComparisonResult> TestArabicSearchPerformanceAsync(string testClientUid, int iterations)
        {
            var result = new PerformanceComparisonResult
            {
                ClientUID = testClientUid,
                Iterations = iterations
            };

            using var connection = _databaseService.CreateConnection();
            await connection.OpenAsync();

            // Get test data for Arabic search
            var testClient = await connection.QueryFirstOrDefaultAsync<dynamic>(
                "SELECT NameAr, NationalId FROM Clients WHERE Uid = @Uid",
                new { Uid = testClientUid });

            if (testClient?.NameAr == null)
            {
                LoggingService.LogWarning("No Arabic name found for test client, using fallback test", "DatabasePerformanceMonitoringService");
                testClient = new { NameAr = "محمد", NationalId = "123456789" };
            }

            // Test with index (optimized query)
            const string optimizedQuery = @"
                SELECT Uid, NameFr, NameAr, NationalId, Address
                FROM Clients
                WHERE NameAr LIKE @NamePattern
                   OR NationalId = @NationalId
                   OR Address LIKE @AddressPattern
                ORDER BY NameAr, NationalId";

            // Test without index (force table scan)
            const string legacyQuery = @"
                SELECT Uid, NameFr, NameAr, NationalId, Address
                FROM Clients
                WHERE (NameAr LIKE @NamePattern OR NameAr IS NULL)
                   AND (NationalId = @NationalId OR NationalId IS NULL)
                   AND (Address LIKE @AddressPattern OR Address IS NULL)
                ORDER BY Uid";

            var parameters = new
            {
                NamePattern = $"%{testClient.NameAr}%",
                NationalId = testClient.NationalId?.ToString() ?? "123456789",
                AddressPattern = "%الجزائر%"
            };

            // Run optimized tests
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                await connection.QueryAsync(optimizedQuery, parameters);
                stopwatch.Stop();
                result.OptimizedTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Run legacy tests
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                await connection.QueryAsync(legacyQuery, parameters);
                stopwatch.Stop();
                result.LegacyTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Calculate averages and improvement
            result.OptimizedAverageMs = result.OptimizedTimes.Average();
            result.LegacyAverageMs = result.LegacyTimes.Average();
            result.ImprovementPercentage = ((result.LegacyAverageMs - result.OptimizedAverageMs) / result.LegacyAverageMs) * 100;

            LoggingService.LogDebug($"Arabic search performance test completed: {result.Summary}", "DatabasePerformanceMonitoringService");
            return result;
        }

        /// <summary>
        /// Tests activity type + client query performance with composite index.
        /// </summary>
        private async Task<PerformanceComparisonResult> TestActivityTypeClientPerformanceAsync(string testClientUid, int iterations)
        {
            var result = new PerformanceComparisonResult
            {
                ClientUID = testClientUid,
                Iterations = iterations
            };

            using var connection = _databaseService.CreateConnection();
            await connection.OpenAsync();

            // Test with composite index (optimized query)
            const string optimizedQuery = @"
                SELECT a.Uid, a.ActivityType, a.ClientUid, a.CreatedAt, a.ActivityStatus
                FROM Activities a
                WHERE a.ActivityType = @ActivityType
                  AND a.ClientUid = @ClientUid
                ORDER BY a.CreatedAt DESC";

            // Test without index optimization (different order to avoid index)
            const string legacyQuery = @"
                SELECT a.Uid, a.ActivityType, a.ClientUid, a.CreatedAt, a.ActivityStatus
                FROM Activities a
                WHERE a.ClientUid = @ClientUid
                  AND a.ActivityType = @ActivityType
                ORDER BY a.Uid";

            var parameters = new
            {
                ActivityType = "MainCommercial",
                ClientUid = testClientUid
            };

            // Run optimized tests
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                await connection.QueryAsync(optimizedQuery, parameters);
                stopwatch.Stop();
                result.OptimizedTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Run legacy tests
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                await connection.QueryAsync(legacyQuery, parameters);
                stopwatch.Stop();
                result.LegacyTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Calculate averages and improvement
            result.OptimizedAverageMs = result.OptimizedTimes.Average();
            result.LegacyAverageMs = result.LegacyTimes.Average();
            result.ImprovementPercentage = ((result.LegacyAverageMs - result.OptimizedAverageMs) / result.LegacyAverageMs) * 100;

            LoggingService.LogDebug($"Activity type + client performance test completed: {result.Summary}", "DatabasePerformanceMonitoringService");
            return result;
        }

        /// <summary>
        /// Tests phone number composite index performance.
        /// </summary>
        private async Task<PerformanceComparisonResult> TestPhoneNumberCompositePerformanceAsync(string testClientUid, int iterations)
        {
            var result = new PerformanceComparisonResult
            {
                ClientUID = testClientUid,
                Iterations = iterations
            };

            using var connection = _databaseService.CreateConnection();
            await connection.OpenAsync();

            // Test with composite index (optimized query)
            const string optimizedQuery = @"
                SELECT p.Uid, p.PhoneNumber, p.PhoneType, p.IsPrimary
                FROM PhoneNumbers p
                WHERE p.ClientUid = @ClientUid
                  AND p.IsPrimary = @IsPrimary
                  AND p.PhoneType = @PhoneType
                ORDER BY p.IsPrimary DESC, p.PhoneType";

            // Test without index optimization (different order)
            const string legacyQuery = @"
                SELECT p.Uid, p.PhoneNumber, p.PhoneType, p.IsPrimary
                FROM PhoneNumbers p
                WHERE p.PhoneType = @PhoneType
                  AND p.ClientUid = @ClientUid
                  AND p.IsPrimary = @IsPrimary
                ORDER BY p.Uid";

            var parameters = new
            {
                ClientUid = testClientUid,
                IsPrimary = 1,
                PhoneType = 0 // Mobile phone type
            };

            // Run optimized tests
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                await connection.QueryAsync(optimizedQuery, parameters);
                stopwatch.Stop();
                result.OptimizedTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Run legacy tests
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                await connection.QueryAsync(legacyQuery, parameters);
                stopwatch.Stop();
                result.LegacyTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Calculate averages and improvement
            result.OptimizedAverageMs = result.OptimizedTimes.Average();
            result.LegacyAverageMs = result.LegacyTimes.Average();
            result.ImprovementPercentage = ((result.LegacyAverageMs - result.OptimizedAverageMs) / result.LegacyAverageMs) * 100;

            LoggingService.LogDebug($"Phone number composite performance test completed: {result.Summary}", "DatabasePerformanceMonitoringService");
            return result;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the service resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _maintenanceTimer?.Dispose();
                    LoggingService.LogInfo("DatabasePerformanceMonitoringService disposed", "DatabasePerformanceMonitoringService");
                }
                _disposed = true;
            }
        }

        #endregion
    }
}