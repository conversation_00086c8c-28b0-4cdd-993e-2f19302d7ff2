<UserControl
    x:Class="UFU2.Views.Dialogs.DuplicateClientDetectionDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:UFU2.Models"
    d:DesignHeight="500"
    d:DesignWidth="600"
    FlowDirection="RightToLeft"
    FontFamily="{DynamicResource MaterialDesignFont}"
    mc:Ignorable="d">

    <Grid>
        <!--  Main dialog content  -->
        <materialDesign:Card
            MaxWidth="550"
            MaxHeight="600"
            Padding="0"
            Style="{StaticResource DialogBaseCardStyle}">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Header  -->
                <materialDesign:Card
                    Grid.Row="0"
                    Margin="0"
                    Style="{DynamicResource HeaderCardStyle}">
                    <TextBlock
                        HorizontalAlignment="Center"
                        Style="{StaticResource HeadlineStyle}"
                        Text="{Binding HeaderText}" />
                </materialDesign:Card>

                <!--  Content message  -->
                <TextBlock
                    Grid.Row="1"
                    Padding="12,8"
                    Style="{StaticResource BodyTextStyle}"
                    Text="{Binding ContentMessage}"
                    TextWrapping="Wrap" />

                <!--  Duplicate clients list  -->
                <ScrollViewer
                    Grid.Row="2"
                    Margin="24,8,24,16"
                    HorizontalScrollBarVisibility="Disabled"
                    VerticalScrollBarVisibility="Auto">
                    <ListBox
                        BorderThickness="0"
                        ItemsSource="{Binding DuplicateClients}"
                        ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                        SelectedItem="{Binding SelectedClient}">
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Padding" Value="0" />
                                <Setter Property="Margin" Value="0" />
                                <Setter Property="Background" Value="Transparent" />
                                <Setter Property="BorderThickness" Value="0" />
                                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ListBoxItem">
                                            <ContentPresenter />
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </ListBox.ItemContainerStyle>
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <RadioButton
                                    GroupName="DuplicateClients"
                                    IsChecked="{Binding IsSelected, RelativeSource={RelativeSource AncestorType=ListBoxItem}}"
                                    Style="{StaticResource DuplicateRadioButtonStyle}">
                                    <RadioButton.Content>
                                        <Grid Width="360" FlowDirection="LeftToRight">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>

                                            <Grid Grid.Column="0">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>

                                                <!--  Client info line  -->
                                                <TextBlock Grid.Row="0" Style="{StaticResource LabelTextStyle}">
                                                    <Run FontWeight="SemiBold" Text="{Binding ClientUid}" />
                                                    <Run Text=" - " />
                                                    <Run FontWeight="SemiBold" Text="{Binding NameFr}" />
                                                </TextBlock>

                                                <!--  Creation date  -->
                                                <TextBlock
                                                    Grid.Row="1"
                                                    Margin="0,4,0,0"
                                                    FlowDirection="LeftToRight"
                                                    FontFamily="Consolas"
                                                    FontSize="11"
                                                    FontWeight="Bold"
                                                    Style="{StaticResource CaptionTextStyle}"
                                                    Text="{Binding CreatedAt}" />
                                            </Grid>

                                            <!--  Activity info line  -->
                                            <TextBlock
                                                Grid.Column="1"
                                                HorizontalAlignment="Right"
                                                VerticalAlignment="Top"
                                                FlowDirection="RightToLeft"
                                                Style="{StaticResource LabelTextStyle}"
                                                Text="{Binding ActivityDescription}"
                                                TextWrapping="Wrap" />
                                        </Grid>
                                    </RadioButton.Content>
                                </RadioButton>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </ScrollViewer>

                <!--  Action buttons  -->
                <StackPanel
                    Grid.Row="3"
                    Margin="24,0,24,16"
                    HorizontalAlignment="Right"
                    Orientation="Horizontal">

                    <!--  Create new client button  -->
                    <Button
                        Margin="0,0,16,0"
                        Padding="12,0"
                        Command="{Binding CreateNewClientCommand}"
                        Style="{StaticResource SecondaryButtonStyle}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    Kind="AccountPlus"
                                    Visibility="Visible" />
                                <ProgressBar
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,8,0"
                                    IsIndeterminate="True"
                                    Maximum="100"
                                    Minimum="0"
                                    Style="{StaticResource MaterialDesignCircularProgressBar}"
                                    Visibility="Collapsed"
                                    Value="0" />
                                <TextBlock VerticalAlignment="Center" Text="إنشاء عميل جديد" />
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <!--  Use selected client button  -->
                    <Button
                        Padding="12,0"
                        Command="{Binding UseSelectedClientCommand}"
                        IsDefault="True"
                        Style="{StaticResource PrimaryButtonStyle}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    Kind="AccountCheck"
                                    Visibility="Visible" />
                                <ProgressBar
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,8,0"
                                    IsIndeterminate="True"
                                    Maximum="100"
                                    Minimum="0"
                                    Style="{StaticResource MaterialDesignCircularProgressBar}"
                                    Visibility="Collapsed"
                                    Value="0" />
                                <TextBlock VerticalAlignment="Center" Text="استخدام العميل المحدد" />
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
