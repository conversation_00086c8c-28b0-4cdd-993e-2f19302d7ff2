using System;
using System.Globalization;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter for PaymentYearsSelectionDialog CheckBox binding.
    /// Handles the binding between PaymentYearModel selection properties and CheckBox IsChecked
    /// based on the currently selected tab (G12 or BIS).
    /// </summary>
    public class PaymentYearSelectionConverter : IMultiValueConverter
    {
        /// <summary>
        /// Converts values from the source to the target.
        /// </summary>
        /// <param name="values">Array containing: [0] IsSelectedG12, [1] IsSelectedBIS, [2] SelectedTab</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">Converter parameter</param>
        /// <param name="culture">Culture info</param>
        /// <returns>Boolean indicating if the year is selected for the current tab</returns>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 3)
                return false;

            if (values[0] is not bool isSelectedG12 || 
                values[1] is not bool isSelectedBIS || 
                values[2] is not string selectedTab)
                return false;

            return selectedTab switch
            {
                "G12" => isSelectedG12,
                "BIS" => isSelectedBIS,
                _ => false
            };
        }

        /// <summary>
        /// Converts values from the target back to the source.
        /// </summary>
        /// <param name="value">The value to convert back</param>
        /// <param name="targetTypes">Array of target types</param>
        /// <param name="parameter">Converter parameter</param>
        /// <param name="culture">Culture info</param>
        /// <returns>Array of values for the source properties</returns>
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            // This converter is designed for one-way binding only
            // The CheckBox selection will be handled through commands or direct property binding
            return new object[] { Binding.DoNothing, Binding.DoNothing, Binding.DoNothing };
        }
    }
}
