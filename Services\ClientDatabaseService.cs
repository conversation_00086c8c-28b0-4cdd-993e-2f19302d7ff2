// BACKUP: Original ClientDatabaseService.cs created on 2025-07-29 before nested transaction refactoring
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;
using UFU2.Models;
using System.Threading;

namespace UFU2.Services
{
    /// <summary>
    /// Client database service for UFU2 client management system.
    /// Provides complete CRUD operations for clients, activities, phone numbers, file check states,
    /// G12Check and BisCheck payment year tracking, and notes management.
    /// Integrates with UIDGenerationService and follows UFU2 architectural patterns.
    /// </summary>
    public class ClientDatabaseService : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private readonly UIDGenerationService _uidGenerationService;
        private readonly FileCheckBusinessRuleService _fileCheckBusinessRuleService;
        private readonly ArchiveDatabaseService _archiveDatabaseService;
        private bool _disposed = false;

        // Retry configuration for database operations
        private const int MaxRetryAttempts = 3;
        private const int BaseRetryDelayMs = 100;
        private const int TransactionTimeoutSeconds = 30;

        /// <summary>
        /// Initializes a new instance of the ClientDatabaseService.
        /// </summary>
        /// <param name="databaseService">The database service for connection management</param>
        /// <param name="uidGenerationService">The UID generation service for creating unique identifiers</param>
        /// <param name="archiveDatabaseService">The archive database service for audit logging</param>
        public ClientDatabaseService(DatabaseService databaseService, UIDGenerationService uidGenerationService, ArchiveDatabaseService archiveDatabaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _uidGenerationService = uidGenerationService ?? throw new ArgumentNullException(nameof(uidGenerationService));
            _archiveDatabaseService = archiveDatabaseService ?? throw new ArgumentNullException(nameof(archiveDatabaseService));
            _fileCheckBusinessRuleService = new FileCheckBusinessRuleService(databaseService);
            LoggingService.LogDebug("ClientDatabaseService initialized with audit logging support", "ClientDatabaseService");
        }

        #region Client CRUD Operations

        /// <summary>
        /// Executes a database operation with retry logic for handling transient failures.
        /// Implements exponential backoff for SQLite locking and busy errors.
        /// </summary>
        /// <typeparam name="T">Return type of the operation</typeparam>
        /// <param name="operation">The database operation to execute</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="operationId">Optional operation ID for error tracking</param>
        /// <returns>Result of the operation</returns>
        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, string? operationId = null)
        {
            int attempt = 0;
            Exception lastException = null;

            while (attempt < MaxRetryAttempts)
            {
                try
                {
                    return await operation();
                }
                catch (SqliteException ex) when (ex.SqliteErrorCode == 5 || ex.SqliteErrorCode == 6) // SQLITE_BUSY or SQLITE_LOCKED
                {
                    lastException = ex;
                    attempt++;

                    if (attempt >= MaxRetryAttempts)
                    {
                        LoggingService.LogError($"{operationName} failed after {MaxRetryAttempts} attempts: {ex.Message}", "ClientDatabaseService");
                        break;
                    }

                    int delayMs = BaseRetryDelayMs * (int)Math.Pow(2, attempt - 1); // Exponential backoff
                    LoggingService.LogWarning($"{operationName} attempt {attempt} failed (SQLite error {ex.SqliteErrorCode}), retrying in {delayMs}ms", "ClientDatabaseService");

                    await Task.Delay(delayMs);
                }
                catch (Exception ex)
                {
                    // Non-retryable exception, throw immediately
                    LoggingService.LogError($"{operationName} failed with non-retryable exception: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }

            // All retries exhausted
            var errorMessage = $"{operationName} فشل بعد {MaxRetryAttempts} محاولات";
            ErrorManager.HandleErrorToast(lastException, errorMessage, "خطأ في قاعدة البيانات", LogLevel.Error, "ClientDatabaseService", operationId);
            throw new InvalidOperationException(errorMessage, lastException);
        }

        /// <summary>
        /// Creates a new client with complete data including activities, phone numbers, and related entities.
        /// Wraps all operations in a database transaction for data integrity with retry logic.
        /// Uses connection pooling for enhanced performance.
        /// </summary>
        /// <param name="clientData">The client creation data</param>
        /// <param name="operationId">Optional operation ID for error deduplication tracking</param>
        /// <param name="useAlternateActivityUidFormat">When true, appends 's' suffix to generated Activity UIDs</param>
        /// <returns>The generated Client UID</returns>
        /// <exception cref="ArgumentNullException">Thrown when clientData is null</exception>
        /// <exception cref="InvalidOperationException">Thrown when client creation fails</exception>
        public async Task<string> CreateClientAsync(ClientCreationData clientData, string? operationId = null, bool useAlternateActivityUidFormat = false)
        {
            if (clientData == null)
            {
                var errorMessage = "بيانات العميل مطلوبة";
                LoggingService.LogError("Client data is required for creation", "ClientDatabaseService");
                throw new ArgumentNullException(nameof(clientData), errorMessage);
            }

            if (string.IsNullOrWhiteSpace(clientData.NameFr))
            {
                var errorMessage = "الاسم الفرنسي مطلوب";
                LoggingService.LogError("French name is required for client creation", "ClientDatabaseService");
                throw new ArgumentException(errorMessage, nameof(clientData));
            }

            // Execute with retry logic for database locking scenarios
            return await ExecuteWithRetryAsync(async () =>
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Set transaction timeout
                connection.DefaultTimeout = TransactionTimeoutSeconds;
                using var transaction = connection.BeginTransaction();
                
                try
                {
                    // Generate Client UID within existing transaction (eliminates nested transactions)
                    string clientUID = await _uidGenerationService.GenerateClientUIDAsync(connection, transaction, clientData.NameFr, operationId);

                    // Create client entity with NULL standardization for empty strings
                    var currentTimestamp = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
                    var clientEntity = new ClientEntity
                    {
                        Uid = clientUID,
                        NameFr = clientData.NameFr, // Required field - never convert to NULL
                        NameAr = ConvertEmptyStringToNull(clientData.NameAr),
                        BirthDate = ConvertEmptyStringToNull(clientData.BirthDate),
                        BirthPlace = ConvertEmptyStringToNull(clientData.BirthPlace),
                        Gender = clientData.Gender,
                        Address = ConvertEmptyStringToNull(clientData.Address),
                        NationalId = ConvertEmptyStringToNull(clientData.NationalId),
                        CreatedAt = currentTimestamp,
                        UpdatedAt = currentTimestamp
                    };

                    // Insert client
                    await InsertClientEntityAsync(connection, transaction, clientEntity);

                    // Create phone numbers if provided
                    if (clientData.PhoneNumbers != null && clientData.PhoneNumbers.Count > 0)
                    {
                        await CreatePhoneNumbersAsync(connection, transaction, clientUID, clientData.PhoneNumbers);
                    }

                    // Create activities if provided
                    if (clientData.Activities != null && clientData.Activities.Count > 0)
                    {
                        foreach (var activityData in clientData.Activities)
                        {
                            await CreateActivityAsync(connection, transaction, clientUID, activityData, useAlternateActivityUidFormat, operationId);
                        }
                    }

                    transaction.Commit();

                    // Clear UID sequence cache after successful transaction
                    _uidGenerationService.ClearSequenceCache();

                    // Log audit trail for client creation (async, non-blocking)
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await _archiveDatabaseService.LogDataAdditionAsync("Client", clientUID, "ClientData", clientEntity);

                            // Log phone numbers addition if any
                            if (clientData.PhoneNumbers != null && clientData.PhoneNumbers.Count > 0)
                            {
                                foreach (var phoneData in clientData.PhoneNumbers)
                                {
                                    await _archiveDatabaseService.LogDataAdditionAsync("Client", clientUID, "PhoneNumber", phoneData);
                                }
                            }

                            // Log activities addition if any
                            if (clientData.Activities != null && clientData.Activities.Count > 0)
                            {
                                foreach (var activityData in clientData.Activities)
                                {
                                    await _archiveDatabaseService.LogDataAdditionAsync("Client", clientUID, "Activity", activityData);
                                }
                            }
                        }
                        catch (Exception auditEx)
                        {
                            LoggingService.LogError($"Failed to log audit trail for client creation {clientUID}: {auditEx.Message}", "ClientDatabaseService");
                        }
                    });

                    LoggingService.LogInfo($"Client created successfully with UID: {clientUID}", "ClientDatabaseService");
                    return clientUID;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();

                    // Clear UID sequence cache after transaction rollback
                    _uidGenerationService.ClearSequenceCache();

                    LoggingService.LogError($"Transaction rolled back during client creation: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }, "Client Creation", operationId);
        }

        /// <summary>
        /// Retrieves a client by UID with all related data using optimized JOIN query.
        /// Uses connection pooling and single database round trip for enhanced performance.
        /// Eliminates N+1 query pattern for 40-50% performance improvement.
        /// </summary>
        /// <param name="clientUID">The Client UID</param>
        /// <returns>Complete client data or null if not found</returns>
        public async Task<ClientData?> GetClientAsync(string clientUID)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogWarning("Client UID is required for retrieval", "ClientDatabaseService");
                return null;
            }

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Use optimized single JOIN query to get all data in one round trip
                var clientData = await GetClientWithRelatedDataAsync(connection, clientUID);

                stopwatch.Stop();

                if (clientData == null)
                {
                    LoggingService.LogInfo($"Client not found with UID: {clientUID}", "ClientDatabaseService");
                    return null;
                }

                // Log performance improvement
                LoggingService.LogDebug($"Client retrieved successfully in {stopwatch.ElapsedMilliseconds}ms (optimized): {clientUID}", "ClientDatabaseService");

                // Track performance metrics for optimization validation
                await TrackDatabaseOperationPerformanceAsync("GetClientAsync_Optimized", stopwatch.ElapsedMilliseconds, 1);

                return clientData;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var errorMessage = "فشل في استرجاع بيانات العميل";
                LoggingService.LogError($"GetClientAsync failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}", "ClientDatabaseService");
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في استرجاع البيانات", LogLevel.Error, "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        /// <summary>
        /// Retrieves a client by UID with all related data using optimized JOIN query (legacy method for compatibility).
        /// This method maintains the original N+1 pattern for comparison and fallback scenarios.
        /// </summary>
        /// <param name="clientUID">The Client UID</param>
        /// <returns>Complete client data or null if not found</returns>
        public async Task<ClientData?> GetClientAsync_Legacy(string clientUID)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogWarning("Client UID is required for retrieval", "ClientDatabaseService");
                return null;
            }

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Get client entity (original N+1 pattern)
                var clientEntity = await GetClientEntityAsync(connection, clientUID);
                if (clientEntity == null)
                {
                    LoggingService.LogInfo($"Client not found with UID: {clientUID}", "ClientDatabaseService");
                    return null;
                }

                // Get related data (original N+1 pattern)
                var phoneNumbers = await GetClientPhoneNumbersAsync(connection, clientUID);
                var activities = await GetClientActivitiesAsync(connection, clientUID);

                var clientData = new ClientData
                {
                    Uid = clientEntity.Uid,
                    NameFr = clientEntity.NameFr,
                    NameAr = clientEntity.NameAr,
                    BirthDate = clientEntity.BirthDate,
                    BirthPlace = clientEntity.BirthPlace,
                    Gender = clientEntity.Gender,
                    Address = clientEntity.Address,
                    NationalId = clientEntity.NationalId,
                    CreatedAt = ParseTimestamp(clientEntity.CreatedAt),
                    UpdatedAt = ParseTimestamp(clientEntity.UpdatedAt),
                    PhoneNumbers = phoneNumbers,
                    Activities = activities
                };

                stopwatch.Stop();
                LoggingService.LogDebug($"Client retrieved successfully in {stopwatch.ElapsedMilliseconds}ms (legacy): {clientUID}", "ClientDatabaseService");

                // Track performance metrics for comparison
                await TrackDatabaseOperationPerformanceAsync("GetClientAsync_Legacy", stopwatch.ElapsedMilliseconds, 1);

                return clientData;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var errorMessage = "فشل في استرجاع بيانات العميل";
                LoggingService.LogError($"GetClientAsync_Legacy failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}", "ClientDatabaseService");
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في استرجاع البيانات", LogLevel.Error, "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        /// <summary>
        /// Updates an existing client's basic information.
        /// Uses connection pooling for enhanced performance.
        /// </summary>
        /// <param name="clientUID">The Client UID</param>
        /// <param name="updateData">The client update data</param>
        /// <returns>True if update was successful</returns>
        public async Task<bool> UpdateClientAsync(string clientUID, ClientUpdateData updateData)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogError("Client UID is required for update", "ClientDatabaseService");
                return false;
            }

            if (updateData == null)
            {
                LoggingService.LogError("Update data is required", "ClientDatabaseService");
                return false;
            }

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                using var transaction = connection.BeginTransaction();
                
                try
                {
                    // Verify client exists
                    var existingClient = await GetClientEntityAsync(connection, clientUID);
                    if (existingClient == null)
                    {
                        LoggingService.LogWarning($"Client not found for update: {clientUID}", "ClientDatabaseService");
                        return false;
                    }

                    // Update client entity
                    const string updateSql = @"
                        UPDATE Clients
                        SET NameAr = @NameAr, BirthDate = @BirthDate, BirthPlace = @BirthPlace,
                            Gender = @Gender, Address = @Address, NationalId = @NationalId,
                            UpdatedAt = @UpdatedAt
                        WHERE Uid = @Uid";

                    var rowsAffected = await connection.ExecuteAsync(updateSql, new
                    {
                        NameAr = ConvertEmptyStringToNull(updateData.NameAr),
                        BirthDate = ConvertEmptyStringToNull(updateData.BirthDate),
                        BirthPlace = ConvertEmptyStringToNull(updateData.BirthPlace),
                        Gender = updateData.Gender,
                        Address = ConvertEmptyStringToNull(updateData.Address),
                        NationalId = ConvertEmptyStringToNull(updateData.NationalId),
                        UpdatedAt = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"),
                        Uid = clientUID
                    }, transaction);

                    transaction.Commit();

                    var success = rowsAffected > 0;
                    if (success)
                    {
                        // Log audit trail for client update (async, non-blocking)
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                // Log each field that was potentially changed
                                if (!string.Equals(existingClient.NameAr, ConvertEmptyStringToNull(updateData.NameAr)))
                                {
                                    await _archiveDatabaseService.LogDataUpdateAsync("Client", clientUID, "NameAr",
                                        existingClient.NameAr ?? "", ConvertEmptyStringToNull(updateData.NameAr) ?? "");
                                }

                                if (!string.Equals(existingClient.BirthDate, ConvertEmptyStringToNull(updateData.BirthDate)))
                                {
                                    await _archiveDatabaseService.LogDataUpdateAsync("Client", clientUID, "BirthDate",
                                        existingClient.BirthDate ?? "", ConvertEmptyStringToNull(updateData.BirthDate) ?? "");
                                }

                                if (!string.Equals(existingClient.BirthPlace, ConvertEmptyStringToNull(updateData.BirthPlace)))
                                {
                                    await _archiveDatabaseService.LogDataUpdateAsync("Client", clientUID, "BirthPlace",
                                        existingClient.BirthPlace ?? "", ConvertEmptyStringToNull(updateData.BirthPlace) ?? "");
                                }

                                if (existingClient.Gender != updateData.Gender)
                                {
                                    await _archiveDatabaseService.LogDataUpdateAsync("Client", clientUID, "Gender",
                                        existingClient.Gender, updateData.Gender);
                                }

                                if (!string.Equals(existingClient.Address, ConvertEmptyStringToNull(updateData.Address)))
                                {
                                    await _archiveDatabaseService.LogDataUpdateAsync("Client", clientUID, "Address",
                                        existingClient.Address ?? "", ConvertEmptyStringToNull(updateData.Address) ?? "");
                                }

                                if (!string.Equals(existingClient.NationalId, ConvertEmptyStringToNull(updateData.NationalId)))
                                {
                                    await _archiveDatabaseService.LogDataUpdateAsync("Client", clientUID, "NationalId",
                                        existingClient.NationalId ?? "", ConvertEmptyStringToNull(updateData.NationalId) ?? "");
                                }
                            }
                            catch (Exception auditEx)
                            {
                                LoggingService.LogError($"Failed to log audit trail for client update {clientUID}: {auditEx.Message}", "ClientDatabaseService");
                            }
                        });

                        LoggingService.LogInfo($"Client updated successfully: {clientUID}", "ClientDatabaseService");
                    }
                    else
                    {
                        LoggingService.LogWarning($"No rows affected during client update: {clientUID}", "ClientDatabaseService");
                    }

                    return success;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    LoggingService.LogError($"Transaction rolled back during client update: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }
            catch (Exception ex)
            {
                var errorMessage = "فشل في تحديث بيانات العميل";
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في التحديث", LogLevel.Error, "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        /// <summary>
        /// Updates phone numbers for an existing client.
        /// Replaces all existing phone numbers with the provided list.
        /// </summary>
        /// <param name="clientUID">The client UID</param>
        /// <param name="phoneNumbers">The new list of phone numbers</param>
        /// <returns>True if update was successful</returns>
        public async Task<bool> UpdateClientPhoneNumbersAsync(string clientUID, List<PhoneNumberData> phoneNumbers)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogError("Client UID is required for phone number update", "ClientDatabaseService");
                return false;
            }

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                using var transaction = connection.BeginTransaction();

                try
                {
                    // Get existing phone numbers for audit logging
                    var existingPhoneNumbers = await GetClientPhoneNumbersAsync(connection, clientUID);

                    // Delete existing phone numbers for this client
                    const string deleteSql = "DELETE FROM PhoneNumbers WHERE ClientUid = @ClientUid";
                    await connection.ExecuteAsync(deleteSql, new { ClientUid = clientUID }, transaction);

                    // Insert new phone numbers
                    if (phoneNumbers?.Count > 0)
                    {
                        const string insertSql = @"
                            INSERT INTO PhoneNumbers (Uid, ClientUid, PhoneNumber, PhoneType, IsPrimary)
                            VALUES (@Uid, @ClientUid, @PhoneNumber, @PhoneType, @IsPrimary)";

                        foreach (var phoneData in phoneNumbers)
                        {
                            // Validate phone type before database insertion
                            int phoneTypeInt = ConvertPhoneTypeToInt(phoneData.PhoneType);
                            if (phoneTypeInt < 0 || phoneTypeInt > 3)
                            {
                                LoggingService.LogWarning($"Invalid phone type '{phoneData.PhoneType}' for client {clientUID}, defaulting to Mobile", "ClientDatabaseService");
                                phoneTypeInt = 0; // Default to Mobile
                            }

                            // Generate unique UID for each phone number
                            string phoneUID = _uidGenerationService.GeneratePhoneNumberUID(clientUID);

                            await connection.ExecuteAsync(insertSql, new
                            {
                                Uid = phoneUID,
                                ClientUid = clientUID,
                                PhoneNumber = phoneData.PhoneNumber,
                                PhoneType = phoneTypeInt,
                                IsPrimary = phoneData.IsPrimary ? 1 : 0
                            }, transaction);
                        }
                    }

                    transaction.Commit();

                    // Log audit trail for phone number changes (async, non-blocking)
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            // Log deletion of old phone numbers
                            foreach (var oldPhone in existingPhoneNumbers)
                            {
                                await _archiveDatabaseService.LogDataDeletionAsync("Client", clientUID, "PhoneNumber", oldPhone);
                            }

                            // Log addition of new phone numbers
                            if (phoneNumbers?.Count > 0)
                            {
                                foreach (var newPhone in phoneNumbers)
                                {
                                    await _archiveDatabaseService.LogDataAdditionAsync("Client", clientUID, "PhoneNumber", newPhone);
                                }
                            }
                        }
                        catch (Exception auditEx)
                        {
                            LoggingService.LogError($"Failed to log audit trail for phone number update {clientUID}: {auditEx.Message}", "ClientDatabaseService");
                        }
                    });

                    LoggingService.LogInfo($"Phone numbers updated successfully for client: {clientUID} (count: {phoneNumbers?.Count ?? 0})", "ClientDatabaseService");
                    return true;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    LoggingService.LogError($"Transaction rolled back during phone number update: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }
            catch (Exception ex)
            {
                string errorMessage;
                if (ex.Message.Contains("CHECK constraint failed: chk_phone_type"))
                {
                    errorMessage = "فشل في تحديث أرقام الهاتف: نوع الهاتف غير صحيح";
                    LoggingService.LogError($"Phone type CHECK constraint violation: {ex.Message}", "ClientDatabaseService");
                }
                else if (ex.Message.Contains("CHECK constraint"))
                {
                    errorMessage = "فشل في تحديث أرقام الهاتف: البيانات غير صحيحة";
                    LoggingService.LogError($"Phone number CHECK constraint violation: {ex.Message}", "ClientDatabaseService");
                }
                else
                {
                    errorMessage = "فشل في تحديث أرقام الهاتف";
                    LoggingService.LogError($"Phone number update failed: {ex.Message}", "ClientDatabaseService");
                }

                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في التحديث", LogLevel.Error, "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        /// <summary>
        /// Creates a new activity for an existing client with proper UID generation.
        /// This method is used when adding activities to existing clients (e.g., from duplicate detection workflow).
        /// </summary>
        /// <param name="clientUID">The existing client UID</param>
        /// <param name="activityData">The activity creation data</param>
        /// <param name="useAlternateActivityUidFormat">When true, appends 's' suffix to generated Activity UIDs</param>
        /// <param name="operationId">Optional operation ID for error deduplication tracking</param>
        /// <returns>The generated Activity UID</returns>
        public async Task<string> CreateActivityForExistingClientAsync(string clientUID, ActivityCreationData activityData,
            bool useAlternateActivityUidFormat = false, string? operationId = null)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogError("Client UID is required for activity creation", "ClientDatabaseService");
                return string.Empty;
            }

            if (activityData == null)
            {
                LoggingService.LogError("Activity data is required", "ClientDatabaseService");
                return string.Empty;
            }

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                using var transaction = connection.BeginTransaction();

                try
                {
                    // Verify client exists
                    var existingClient = await GetClientEntityAsync(connection, clientUID);
                    if (existingClient == null)
                    {
                        LoggingService.LogWarning($"Client not found for activity creation: {clientUID}", "ClientDatabaseService");
                        return string.Empty;
                    }

                    // Create the activity using the existing CreateActivityAsync method
                    // This will automatically use UIDGenerationService to generate proper incremental Activity UIDs
                    string activityUID = await CreateActivityAsync(connection, transaction, clientUID, activityData, useAlternateActivityUidFormat, operationId);

                    transaction.Commit();

                    LoggingService.LogInfo($"Activity created successfully for existing client: {clientUID}, Activity UID: {activityUID}", "ClientDatabaseService");
                    return activityUID;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    LoggingService.LogError($"Transaction rolled back during activity creation for existing client: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }
            catch (Exception ex)
            {
                var errorMessage = "فشل في إنشاء النشاط للعميل الموجود";
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في الإنشاء", LogLevel.Error, "ClientDatabaseService", operationId);
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        /// <summary>
        /// Hard deletes a client and all related data using CASCADE DELETE.
        /// All related activities, phone numbers, notes, etc. are permanently removed.
        /// Uses connection pooling for enhanced performance.
        /// </summary>
        /// <param name="clientUID">The Client UID</param>
        /// <returns>True if deletion was successful</returns>
        public async Task<bool> DeleteClientAsync(string clientUID)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogError("Client UID is required for deletion", "ClientDatabaseService");
                return false;
            }

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                using var transaction = connection.BeginTransaction();
                
                try
                {
                    // Delete client (CASCADE will handle related records)
                    const string deleteSql = @"
                        DELETE FROM Clients
                        WHERE Uid = @Uid";

                    var rowsAffected = await connection.ExecuteAsync(deleteSql, new
                    {
                        Uid = clientUID
                    }, transaction);

                    transaction.Commit();
                    
                    var success = rowsAffected > 0;
                    if (success)
                    {
                        LoggingService.LogInfo($"Client deleted successfully: {clientUID}", "ClientDatabaseService");
                    }
                    else
                    {
                        LoggingService.LogWarning($"Client not found or already deleted: {clientUID}", "ClientDatabaseService");
                    }
                    
                    return success;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    LoggingService.LogError($"Transaction rolled back during client deletion: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }
            catch (Exception ex)
            {
                var errorMessage = "فشل في حذف العميل";
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في الحذف", LogLevel.Error, "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        #endregion

        #region Activity Management

        /// <summary>
        /// Creates a new activity for an existing client.
        /// </summary>
        /// <param name="clientUID">The Client UID</param>
        /// <param name="activityData">The activity creation data</param>
        /// <param name="useAlternateUidFormat">When true, appends 's' suffix to the generated Activity UID</param>
        /// <returns>The generated Activity UID</returns>
        public async Task<string> CreateActivityAsync(string clientUID, ActivityCreationData activityData, bool useAlternateUidFormat = false)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                var errorMessage = "معرف العميل مطلوب";
                LoggingService.LogError("Client UID is required for activity creation", "ClientDatabaseService");
                throw new ArgumentException(errorMessage, nameof(clientUID));
            }

            if (activityData == null)
            {
                var errorMessage = "بيانات النشاط مطلوبة";
                LoggingService.LogError("Activity data is required", "ClientDatabaseService");
                throw new ArgumentNullException(nameof(activityData), errorMessage);
            }

            // Execute with retry logic for database locking scenarios
            return await ExecuteWithRetryAsync(async () =>
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Set transaction timeout
                connection.DefaultTimeout = TransactionTimeoutSeconds;
                using var transaction = connection.BeginTransaction();
                
                try
                {
                    var result = await CreateActivityAsync(connection, transaction, clientUID, activityData, useAlternateUidFormat);
                    transaction.Commit();

                    // Clear UID sequence cache after successful transaction
                    _uidGenerationService.ClearSequenceCache();

                    return result;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();

                    // Clear UID sequence cache after transaction rollback
                    _uidGenerationService.ClearSequenceCache();

                    LoggingService.LogError($"Transaction rolled back during activity creation: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }, "Activity Creation");
        }

        /// <summary>
        /// Internal method to create an activity within an existing transaction.
        /// </summary>
        private async Task<string> CreateActivityAsync(SqliteConnection connection, SqliteTransaction transaction,
            string clientUID, ActivityCreationData activityData, bool useAlternateUidFormat = false, string? operationId = null)
        {
            // Generate Activity UID within existing transaction (eliminates nested transactions)
            string activityUID = await _uidGenerationService.GenerateActivityUIDAsync(connection, transaction, clientUID, useAlternateUidFormat);
            
            // Create activity entity
            var currentTimestamp = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            var activityEntity = new ActivityEntity
            {
                Uid = activityUID,
                ClientUid = clientUID,
                ActivityType = activityData.ActivityType,
                ActivityStatus = activityData.ActivityStatus,
                ActivityStartDate = activityData.ActivityStartDate,
                CommercialRegister = activityData.CommercialRegister,
                ActivityLocation = activityData.ActivityLocation,
                NifNumber = activityData.NifNumber,
                NisNumber = activityData.NisNumber,
                ArtNumber = activityData.ArtNumber,
                CpiDaira = activityData.CpiDaira,
                CpiWilaya = activityData.CpiWilaya,
                ActivityUpdateDate = activityData.ActivityUpdateDate,
                ActivityUpdateNote = activityData.ActivityUpdateNote,
                CreatedAt = currentTimestamp,
                UpdatedAt = currentTimestamp
            };

            // Insert activity
            await InsertActivityEntityAsync(connection, transaction, activityEntity);
            
            // Create commercial activity codes if provided
            if (activityData.ActivityCodes != null && activityData.ActivityCodes.Count > 0)
            {
                await CreateCommercialActivityCodesAsync(connection, transaction, activityUID, activityData.ActivityCodes);
            }

            // Create craft activity code for Craft activities
            if (!string.IsNullOrWhiteSpace(activityData.CraftCode) && activityData.ActivityType == "Craft")
            {
                await CreateCraftActivityCodeAsync(connection, transaction, activityUID, activityData.CraftCode);
            }

            // Create profession name description ONLY for Professional activities
            // (Craft activities now use CraftActivityCodes instead of descriptions)
            if (!string.IsNullOrWhiteSpace(activityData.ActivityDescription) && activityData.ActivityType == "Professional")
            {
                await CreateProfessionNameAsync(connection, transaction, activityUID, activityData.ActivityDescription);
            }
            
            // Create file check states with business rule enforcement
            if (activityData.FileCheckStates != null && activityData.FileCheckStates.Count > 0)
            {
                // Validate file check states against business rules before creation
                var validation = _fileCheckBusinessRuleService.ValidateFileCheckStates(activityData.ActivityType, activityData.FileCheckStates, false);
                if (!validation.IsValid)
                {
                    var errorMessages = string.Join("; ", validation.Errors.SelectMany(e => e.Value));
                    throw new InvalidOperationException($"File check validation failed: {errorMessages}");
                }

                await CreateFileCheckStatesAsync(connection, transaction, activityUID, activityData.FileCheckStates);
            }
            
            // Ensure all required file check states exist for the activity type
            if (!string.IsNullOrWhiteSpace(activityData.ActivityType))
            {
                await _fileCheckBusinessRuleService.EnsureRequiredFileCheckStatesAsync(connection, transaction, activityUID, activityData.ActivityType);
            }
            
            // Create G12 check years if provided
            if (activityData.G12CheckYears != null && activityData.G12CheckYears.Count > 0)
            {
                await CreateG12CheckYearsAsync(connection, transaction, activityUID, activityData.G12CheckYears);
            }
            
            // Create BIS check years if provided
            if (activityData.BisCheckYears != null && activityData.BisCheckYears.Count > 0)
            {
                await CreateBisCheckYearsAsync(connection, transaction, activityUID, activityData.BisCheckYears);
            }
            
            // Create notes if provided
            if (activityData.Notes != null && activityData.Notes.Count > 0)
            {
                await CreateNotesAsync(connection, transaction, activityUID, activityData.Notes);
            }
            
            // Log audit trail for activity creation (async, non-blocking)
            _ = Task.Run(async () =>
            {
                try
                {
                    await _archiveDatabaseService.LogDataAdditionAsync("Activity", activityUID, "ActivityData", activityEntity);

                    // Log activity codes if any
                    if (activityData.ActivityCodes != null && activityData.ActivityCodes.Count > 0)
                    {
                        await _archiveDatabaseService.LogDataAdditionAsync("Activity", activityUID, "ActivityCodes", activityData.ActivityCodes);
                    }

                    // Log craft code if any
                    if (!string.IsNullOrWhiteSpace(activityData.CraftCode))
                    {
                        await _archiveDatabaseService.LogDataAdditionAsync("Activity", activityUID, "CraftCode", activityData.CraftCode);
                    }

                    // Log activity description if any
                    if (!string.IsNullOrWhiteSpace(activityData.ActivityDescription))
                    {
                        await _archiveDatabaseService.LogDataAdditionAsync("Activity", activityUID, "ActivityDescription", activityData.ActivityDescription);
                    }

                    // Log file check states if any
                    if (activityData.FileCheckStates != null && activityData.FileCheckStates.Count > 0)
                    {
                        await _archiveDatabaseService.LogDataAdditionAsync("Activity", activityUID, "FileCheckStates", activityData.FileCheckStates);
                    }

                    // Log G12 check years if any
                    if (activityData.G12CheckYears != null && activityData.G12CheckYears.Count > 0)
                    {
                        await _archiveDatabaseService.LogDataAdditionAsync("Activity", activityUID, "G12CheckYears", activityData.G12CheckYears);
                    }

                    // Log BIS check years if any
                    if (activityData.BisCheckYears != null && activityData.BisCheckYears.Count > 0)
                    {
                        await _archiveDatabaseService.LogDataAdditionAsync("Activity", activityUID, "BisCheckYears", activityData.BisCheckYears);
                    }

                    // Log notes if any
                    if (activityData.Notes != null && activityData.Notes.Count > 0)
                    {
                        await _archiveDatabaseService.LogDataAdditionAsync("Activity", activityUID, "Notes", activityData.Notes);
                    }
                }
                catch (Exception auditEx)
                {
                    LoggingService.LogError($"Failed to log audit trail for activity creation {activityUID}: {auditEx.Message}", "ClientDatabaseService");
                }
            });

            LoggingService.LogInfo($"Activity created successfully with UID: {activityUID}", "ClientDatabaseService");
            return activityUID;
        }

        #endregion

        #region Phone Number Management

        /// <summary>
        /// Creates phone numbers for a client within an existing transaction.
        /// </summary>
        private async Task CreatePhoneNumbersAsync(SqliteConnection connection, SqliteTransaction transaction, 
            string clientUID, List<PhoneNumberData> phoneNumbers)
        {
            // Validate primary phone constraint (only one primary phone allowed)
            var primaryPhones = phoneNumbers.Where(p => p.IsPrimary).ToList();
            if (primaryPhones.Count > 1)
            {
                var errorMessage = "يُسمح بهاتف أساسي واحد فقط";
                LoggingService.LogError("Only one primary phone number is allowed", "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage);
            }

            const string insertSql = @"
                INSERT INTO PhoneNumbers (Uid, ClientUid, PhoneNumber, PhoneType, IsPrimary)
                VALUES (@Uid, @ClientUid, @PhoneNumber, @PhoneType, @IsPrimary)";

            foreach (var phoneData in phoneNumbers)
            {
                // Generate UID for phone number using new pattern: {ClientUID}_Phone_{8-char-guid}
                string phoneUID = _uidGenerationService.GeneratePhoneNumberUID(clientUID);

                await connection.ExecuteAsync(insertSql, new
                {
                    Uid = phoneUID,
                    ClientUid = clientUID,
                    PhoneNumber = phoneData.PhoneNumber,
                    PhoneType = ConvertPhoneTypeToInt(phoneData.PhoneType),
                    IsPrimary = phoneData.IsPrimary ? 1 : 0
                }, transaction);
            }

            LoggingService.LogDebug($"Created {phoneNumbers.Count} phone numbers for client: {clientUID}", "ClientDatabaseService");
        }

        /// <summary>
        /// Retrieves all phone numbers for a client.
        /// </summary>
        private async Task<List<PhoneNumberData>> GetClientPhoneNumbersAsync(SqliteConnection connection, string clientUID)
        {
            const string selectSql = @"
                SELECT Uid, ClientUid, PhoneNumber, PhoneType, IsPrimary
                FROM PhoneNumbers
                WHERE ClientUid = @ClientUID
                ORDER BY IsPrimary DESC";

            var phoneEntities = await connection.QueryAsync<PhoneNumberEntity>(selectSql, new { ClientUID = clientUID });
            
            return phoneEntities.Select(entity => new PhoneNumberData
            {
                ClientUid = entity.ClientUid,
                PhoneNumber = entity.PhoneNumber ?? string.Empty,
                PhoneType = ConvertPhoneTypeToString(entity.PhoneType),
                IsPrimary = entity.IsPrimary == 1
            }).ToList();
        }

        #endregion

        #region File Check State Management

        /// <summary>
        /// Creates file check states for an activity within an existing transaction.
        /// Validates activity type-specific business rules.
        /// </summary>
        private async Task CreateFileCheckStatesAsync(SqliteConnection connection, SqliteTransaction transaction,
            string activityUID, Dictionary<string, bool> fileCheckStates)
        {
            const string insertSql = @"
                INSERT INTO FileCheckStates (Uid, ActivityUid, FileCheckType, IsChecked, CheckedDate)
                VALUES (@Uid, @ActivityUid, @FileCheckType, @IsChecked, @CheckedDate)";

            foreach (var fileCheck in fileCheckStates)
            {
                // Generate unique UID for each FileCheckState record using activity UID as prefix
                string fileCheckStateUID = $"{activityUID}_{fileCheck.Key}";

                await connection.ExecuteAsync(insertSql, new
                {
                    Uid = fileCheckStateUID,
                    ActivityUid = activityUID,
                    FileCheckType = fileCheck.Key,
                    IsChecked = fileCheck.Value ? 1 : 0,
                    CheckedDate = fileCheck.Value ? DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") : (string?)null
                }, transaction);
            }

            LoggingService.LogDebug($"Created {fileCheckStates.Count} file check states for activity: {activityUID}", "ClientDatabaseService");
        }

        /// <summary>
        /// Updates file check states for an activity.
        /// </summary>
        public async Task<bool> UpdateFileCheckStatesAsync(string activityUID, Dictionary<string, bool> fileCheckStates)
        {
            if (string.IsNullOrWhiteSpace(activityUID))
            {
                LoggingService.LogError("Activity UID is required for file check state update", "ClientDatabaseService");
                return false;
            }

            if (fileCheckStates == null || fileCheckStates.Count == 0)
            {
                LoggingService.LogWarning("No file check states provided for update", "ClientDatabaseService");
                return false;
            }

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                using var transaction = connection.BeginTransaction();
                
                try
                {
                    const string upsertSql = @"
                        INSERT INTO FileCheckStates (Uid, ActivityUid, FileCheckType, IsChecked, CheckedDate)
                        VALUES (@Uid, @ActivityUid, @FileCheckType, @IsChecked, @CheckedDate)
                        ON CONFLICT(ActivityUid, FileCheckType) DO UPDATE SET
                            IsChecked = excluded.IsChecked,
                            CheckedDate = excluded.CheckedDate";

                    foreach (var fileCheck in fileCheckStates)
                    {
                        // Generate unique UID for each FileCheckState record using activity UID as prefix
                        string fileCheckStateUID = $"{activityUID}_{fileCheck.Key}";

                        await connection.ExecuteAsync(upsertSql, new
                        {
                            Uid = fileCheckStateUID,
                            ActivityUid = activityUID,
                            FileCheckType = fileCheck.Key,
                            IsChecked = fileCheck.Value ? 1 : 0,
                            CheckedDate = fileCheck.Value ? DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") : (string?)null
                        }, transaction);
                    }

                    transaction.Commit();
                    
                    LoggingService.LogInfo($"File check states updated successfully for activity: {activityUID}", "ClientDatabaseService");
                    return true;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    LoggingService.LogError($"Transaction rolled back during file check state update: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }
            catch (Exception ex)
            {
                var errorMessage = "فشل في تحديث حالات فحص الملفات";
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في التحديث", LogLevel.Error, "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        #endregion

        #region G12Check and BisCheck Payment Year Tracking

        /// <summary>
        /// Creates G12 check years for an activity within an existing transaction.
        /// </summary>
        private async Task CreateG12CheckYearsAsync(SqliteConnection connection, SqliteTransaction transaction,
            string activityUID, List<int> years)
        {
            const string insertSql = @"
                INSERT INTO G12Check (Uid, ActivityUid, Year)
                VALUES (@Uid, @ActivityUid, @PaymentYear)";

            foreach (var year in years)
            {
                // Generate unique UID for each G12Check record
                string g12CheckUID = $"{activityUID}_G12_{year}";

                await connection.ExecuteAsync(insertSql, new
                {
                    Uid = g12CheckUID,
                    ActivityUid = activityUID,
                    PaymentYear = year
                }, transaction);
            }

            LoggingService.LogDebug($"Created {years.Count} G12 check years for activity: {activityUID}", "ClientDatabaseService");
        }

        /// <summary>
        /// Creates BIS check years for an activity within an existing transaction.
        /// </summary>
        private async Task CreateBisCheckYearsAsync(SqliteConnection connection, SqliteTransaction transaction,
            string activityUID, List<int> years)
        {
            const string insertSql = @"
                INSERT INTO BisCheck (Uid, ActivityUid, Year)
                VALUES (@Uid, @ActivityUid, @PaymentYear)";

            foreach (var year in years)
            {
                // Generate unique UID for each BisCheck record
                string bisCheckUID = $"{activityUID}_BIS_{year}";

                await connection.ExecuteAsync(insertSql, new
                {
                    Uid = bisCheckUID,
                    ActivityUid = activityUID,
                    PaymentYear = year
                }, transaction);
            }

            LoggingService.LogDebug($"Created {years.Count} BIS check years for activity: {activityUID}", "ClientDatabaseService");
        }

        /// <summary>
        /// Updates G12 check years for an activity.
        /// </summary>
        public async Task<bool> UpdateG12CheckYearsAsync(string activityUID, List<int> years)
        {
            return await UpdatePaymentYearsAsync(activityUID, years, "G12Check", "G12");
        }

        /// <summary>
        /// Updates BIS check years for an activity.
        /// </summary>
        public async Task<bool> UpdateBisCheckYearsAsync(string activityUID, List<int> years)
        {
            return await UpdatePaymentYearsAsync(activityUID, years, "BisCheck", "BIS");
        }

        /// <summary>
        /// Internal method to update payment years for either G12 or BIS checks.
        /// </summary>
        private async Task<bool> UpdatePaymentYearsAsync(string activityUID, List<int> years, string tableName, string checkType)
        {
            if (string.IsNullOrWhiteSpace(activityUID))
            {
                LoggingService.LogError($"Activity UID is required for {checkType} check year update", "ClientDatabaseService");
                return false;
            }

            if (years == null)
            {
                LoggingService.LogWarning($"Years list is null for {checkType} check update", "ClientDatabaseService");
                return false;
            }

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                using var transaction = connection.BeginTransaction();
                
                try
                {
                    // Delete existing years
                    var deleteSql = $"DELETE FROM {tableName} WHERE ActivityUid = @ActivityUid";
                    await connection.ExecuteAsync(deleteSql, new { ActivityUid = activityUID }, transaction);

                    // Insert new years
                    if (years.Count > 0)
                    {
                        var insertSql = $@"
                            INSERT INTO {tableName} (Uid, ActivityUid, Year)
                            VALUES (@Uid, @ActivityUid, @PaymentYear)";

                        foreach (var year in years)
                        {
                            // Generate unique UID for each payment year record
                            string paymentYearUID = $"{activityUID}_{checkType}_{year}";

                            await connection.ExecuteAsync(insertSql, new
                            {
                                Uid = paymentYearUID,
                                ActivityUid = activityUID,
                                PaymentYear = year
                            }, transaction);
                        }
                    }

                    transaction.Commit();
                    
                    LoggingService.LogInfo($"{checkType} check years updated successfully for activity: {activityUID}", "ClientDatabaseService");
                    return true;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    LoggingService.LogError($"Transaction rolled back during {checkType} check year update: {ex.Message}", "ClientDatabaseService");
                    throw;
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"فشل في تحديث سنوات فحص {checkType}";
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في التحديث", LogLevel.Error, "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        #endregion

        #region Notes Management

        /// <summary>
        /// Creates notes for an activity within an existing transaction.
        /// </summary>
        private async Task CreateNotesAsync(SqliteConnection connection, SqliteTransaction transaction,
            string activityUID, List<NoteCreationData> notes)
        {
            const string insertSql = @"
                INSERT INTO Notes (Uid, ActivityUid, Content, Priority, CreatedAt, UpdatedAt)
                VALUES (@Uid, @ActivityUid, @NoteContent, @Priority, @CreatedAt, @UpdatedAt)";

            var currentTimestamp = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            foreach (var noteData in notes)
            {
                // Generate unique UID for each Note record using activity UID as prefix
                string noteUID = $"{activityUID}_Note_{Guid.NewGuid().ToString("N")[..8]}";

                await connection.ExecuteAsync(insertSql, new
                {
                    Uid = noteUID,
                    ActivityUid = activityUID,
                    NoteContent = noteData.Content,
                    Priority = noteData.Priority,
                    CreatedAt = currentTimestamp,
                    UpdatedAt = currentTimestamp
                }, transaction);
            }

            LoggingService.LogDebug($"Created {notes.Count} notes for activity: {activityUID}", "ClientDatabaseService");
        }

        /// <summary>
        /// Adds a new note to an activity.
        /// </summary>
        public async Task<bool> AddNoteAsync(string activityUID, NoteCreationData noteData)
        {
            if (string.IsNullOrWhiteSpace(activityUID))
            {
                LoggingService.LogError("Activity UID is required for note creation", "ClientDatabaseService");
                return false;
            }

            if (noteData == null || string.IsNullOrWhiteSpace(noteData.Content))
            {
                LoggingService.LogError("Note content is required", "ClientDatabaseService");
                return false;
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                const string insertSql = @"
                    INSERT INTO Notes (Uid, ActivityUid, Content, Priority, CreatedAt, UpdatedAt)
                    VALUES (@Uid, @ActivityUid, @NoteContent, @Priority, @CreatedAt, @UpdatedAt)";

                // Generate unique UID for the Note record
                string noteUID = $"{activityUID}_Note_{Guid.NewGuid().ToString("N")[..8]}";
                var currentTimestamp = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");

                var rowsAffected = await connection.ExecuteAsync(insertSql, new
                {
                    Uid = noteUID,
                    ActivityUid = activityUID,
                    NoteContent = noteData.Content,
                    Priority = noteData.Priority,
                    CreatedAt = currentTimestamp,
                    UpdatedAt = currentTimestamp
                });

                var success = rowsAffected > 0;
                if (success)
                {
                    LoggingService.LogInfo($"Note added successfully to activity: {activityUID}", "ClientDatabaseService");
                }

                return success;
            }
            catch (Exception ex)
            {
                var errorMessage = "فشل في إضافة الملاحظة";
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في إضافة الملاحظة", LogLevel.Error, "ClientDatabaseService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Converts integer phone type from database to string phone type for data model.
        /// </summary>
        /// <param name="phoneType">Integer phone type from database (0=Mobile, 1=Home, 2=Work, 3=Other)</param>
        /// <returns>String phone type for data model</returns>
        private static string ConvertPhoneTypeToString(int? phoneType)
        {
            return phoneType switch
            {
                0 => "Mobile",
                1 => "Home",
                2 => "Work",
                3 => "Fax", // Map "Other" to "Fax" to match PhoneNumberData expectations
                _ => "Mobile" // Default to Mobile for null or invalid values
            };
        }

        /// <summary>
        /// Converts string phone type from data model to integer phone type for database.
        /// </summary>
        /// <param name="phoneType">String phone type from data model</param>
        /// <returns>Integer phone type for database</returns>
        private static int ConvertPhoneTypeToInt(string phoneType)
        {
            return phoneType switch
            {
                "Mobile" => 0,
                "Home" => 1,
                "Work" => 2,
                "Fax" => 3,
                _ => 0 // Default to Mobile for invalid values
            };
        }

        /// <summary>
        /// Inserts a client entity into the database.
        /// </summary>
        private async Task InsertClientEntityAsync(SqliteConnection connection, SqliteTransaction transaction, ClientEntity clientEntity)
        {
            const string insertSql = @"
                INSERT INTO Clients (Uid, NameFr, NameAr, BirthDate, BirthPlace, Gender, Address, NationalId, CreatedAt, UpdatedAt)
                VALUES (@Uid, @NameFr, @NameAr, @BirthDate, @BirthPlace, @Gender, @Address, @NationalId, @CreatedAt, @UpdatedAt)";

            // Ensure timestamps are in the correct format
            var currentTimestamp = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            clientEntity.CreatedAt = currentTimestamp;
            clientEntity.UpdatedAt = currentTimestamp;

            await connection.ExecuteAsync(insertSql, clientEntity, transaction);
        }

        /// <summary>
        /// Retrieves a client with all related data using optimized JOIN query.
        /// Eliminates N+1 pattern by combining client, phone numbers, and activities in single query.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="clientUID">The Client UID</param>
        /// <returns>Complete client data or null if not found</returns>
        private async Task<ClientData?> GetClientWithRelatedDataAsync(SqliteConnection connection, string clientUID)
        {
            // Optimized JOIN query combining client, phone numbers, and activities
            const string optimizedQuery = @"
                SELECT
                    -- Client data
                    c.Uid as ClientUid,
                    c.NameFr,
                    c.NameAr,
                    c.BirthDate,
                    c.BirthPlace,
                    c.Gender,
                    c.Address,
                    c.NationalId,
                    c.CreatedAt as ClientCreatedAt,
                    c.UpdatedAt as ClientUpdatedAt,

                    -- Phone number data
                    p.Uid as PhoneUid,
                    p.PhoneNumber,
                    p.PhoneType,
                    p.IsPrimary,

                    -- Activity data
                    a.Uid as ActivityUid,
                    a.ActivityType,
                    a.ActivityStatus,
                    a.ActivityStartDate,
                    a.CommercialRegister,
                    a.ActivityLocation,
                    a.NifNumber,
                    a.NisNumber,
                    a.ArtNumber,
                    a.CpiDaira,
                    a.CpiWilaya,
                    a.ActivityUpdateDate,
                    a.ActivityUpdateNote,
                    a.CreatedAt as ActivityCreatedAt,
                    a.UpdatedAt as ActivityUpdatedAt
                FROM Clients c
                LEFT JOIN PhoneNumbers p ON c.Uid = p.ClientUid
                LEFT JOIN Activities a ON c.Uid = a.ClientUid
                WHERE c.Uid = @ClientUID
                ORDER BY p.IsPrimary DESC, a.CreatedAt ASC";

            var queryResults = await connection.QueryAsync<dynamic>(optimizedQuery, new { ClientUID = clientUID });

            if (!queryResults.Any())
            {
                return null;
            }

            // Process results and build ClientData object
            return await BuildClientDataFromJoinResults(connection, queryResults, clientUID);
        }

        /// <summary>
        /// Builds ClientData object from JOIN query results.
        /// Handles data aggregation and related entity loading.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="queryResults">JOIN query results</param>
        /// <param name="clientUID">Client UID</param>
        /// <returns>Complete ClientData object</returns>
        private async Task<ClientData> BuildClientDataFromJoinResults(SqliteConnection connection, IEnumerable<dynamic> queryResults, string clientUID)
        {
            var firstRow = queryResults.First();

            // Build client data from first row
            var clientData = new ClientData
            {
                Uid = firstRow.ClientUid,
                NameFr = firstRow.NameFr ?? string.Empty,
                NameAr = firstRow.NameAr,
                BirthDate = firstRow.BirthDate,
                BirthPlace = firstRow.BirthPlace,
                Gender = firstRow.Gender,
                Address = firstRow.Address,
                NationalId = firstRow.NationalId,
                CreatedAt = ParseTimestamp(firstRow.ClientCreatedAt),
                UpdatedAt = ParseTimestamp(firstRow.ClientUpdatedAt),
                PhoneNumbers = new List<PhoneNumberData>(),
                Activities = new List<ActivityData>()
            };

            // Process phone numbers (deduplicate)
            var phoneNumbers = new Dictionary<string, PhoneNumberData>();
            var activityUids = new HashSet<string>();

            foreach (var row in queryResults)
            {
                // Process phone numbers
                if (row.PhoneUid != null && !phoneNumbers.ContainsKey(row.PhoneUid))
                {
                    phoneNumbers[row.PhoneUid] = new PhoneNumberData
                    {
                        ClientUid = clientUID,
                        PhoneNumber = row.PhoneNumber ?? string.Empty,
                        PhoneType = ConvertPhoneTypeToString(row.PhoneType),
                        IsPrimary = row.IsPrimary == 1
                    };
                }

                // Collect activity UIDs for batch loading
                if (row.ActivityUid != null)
                {
                    activityUids.Add(row.ActivityUid);
                }
            }

            clientData.PhoneNumbers = phoneNumbers.Values.OrderByDescending(p => p.IsPrimary).ToList();

            // Load activities with related data in batch (optimized)
            if (activityUids.Any())
            {
                clientData.Activities = await GetActivitiesWithRelatedDataBatchAsync(connection, activityUids.ToList());
            }

            return clientData;
        }

        /// <summary>
        /// Retrieves a client entity by UID.
        /// </summary>
        private async Task<ClientEntity?> GetClientEntityAsync(SqliteConnection connection, string clientUID)
        {
            const string selectSql = @"
                SELECT Uid, NameFr, NameAr, BirthDate, BirthPlace, Gender, Address, NationalId, CreatedAt, UpdatedAt
                FROM Clients
                WHERE Uid = @ClientUID";

            return await connection.QueryFirstOrDefaultAsync<ClientEntity>(selectSql, new { ClientUID = clientUID });
        }

        /// <summary>
        /// Inserts an activity entity into the database.
        /// </summary>
        private async Task InsertActivityEntityAsync(SqliteConnection connection, SqliteTransaction transaction, ActivityEntity activityEntity)
        {
            const string insertSql = @"
                INSERT INTO Activities (Uid, ClientUid, ActivityType, ActivityStatus, ActivityStartDate,
                                      CommercialRegister, ActivityLocation, NifNumber, NisNumber, ArtNumber,
                                      CpiDaira, CpiWilaya, ActivityUpdateDate, ActivityUpdateNote,
                                      CreatedAt, UpdatedAt)
                VALUES (@Uid, @ClientUid, @ActivityType, @ActivityStatus, @ActivityStartDate,
                        @CommercialRegister, @ActivityLocation, @NifNumber, @NisNumber, @ArtNumber,
                        @CpiDaira, @CpiWilaya, @ActivityUpdateDate, @ActivityUpdateNote,
                        @CreatedAt, @UpdatedAt)";

            // Ensure timestamps are in the correct format
            var currentTimestamp = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            activityEntity.CreatedAt = currentTimestamp;
            activityEntity.UpdatedAt = currentTimestamp;

            await connection.ExecuteAsync(insertSql, activityEntity, transaction);
        }

        /// <summary>
        /// Creates commercial activity codes for an activity.
        /// </summary>
        private async Task CreateCommercialActivityCodesAsync(SqliteConnection connection, SqliteTransaction transaction,
            string activityUID, List<int> activityCodes)
        {
            const string insertSql = @"
                INSERT INTO CommercialActivityCodes (Uid, ActivityUid, ActivityCode)
                VALUES (@Uid, @ActivityUid, @ActivityCode)";

            foreach (var code in activityCodes)
            {
                // Generate unique UID for each CommercialActivityCode record
                string codeUID = $"{activityUID}_Code_{code}";

                await connection.ExecuteAsync(insertSql, new
                {
                    Uid = codeUID,
                    ActivityUid = activityUID,
                    ActivityCode = code
                }, transaction);
            }
        }

        /// <summary>
        /// Creates craft activity code for a craft activity.
        /// Enforces one-to-one relationship: each activity can have only one craft code.
        /// </summary>
        private async Task CreateCraftActivityCodeAsync(SqliteConnection connection, SqliteTransaction transaction,
            string activityUID, string craftCode)
        {
            try
            {
                // Generate unique UID following CommercialActivityCodes pattern: {ActivityUid}_Craft_{CraftCode}
                string craftCodeUID = $"{activityUID}_Craft_{craftCode}";

                const string insertSql = @"
                    INSERT INTO CraftActivityCodes (Uid, ActivityUid, CraftCode)
                    VALUES (@Uid, @ActivityUid, @CraftCode)";

                await connection.ExecuteAsync(insertSql, new
                {
                    Uid = craftCodeUID,
                    ActivityUid = activityUID,
                    CraftCode = craftCode
                }, transaction);

                LoggingService.LogDebug($"Created craft activity code: {craftCode} for activity: {activityUID}", "ClientDatabaseService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating craft activity code: {ex.Message}", "ClientDatabaseService");
                ErrorManager.HandleErrorToast(ex, "فشل في إنشاء رمز النشاط الحرفي", "خطأ في قاعدة البيانات",
                                       UFU2.Common.LogLevel.Error, "ClientDatabaseService");
                throw;
            }
        }

        /// <summary>
        /// Creates profession name description for an activity.
        /// </summary>
        private async Task CreateProfessionNameAsync(SqliteConnection connection, SqliteTransaction transaction,
            string activityUID, string description)
        {
            const string insertSql = @"
                INSERT INTO ProfessionNames (ActivityUid, ActivityDescription)
                VALUES (@ActivityUid, @ActivityDescription)";

            await connection.ExecuteAsync(insertSql, new
            {
                ActivityUid = activityUID,
                ActivityDescription = description
            }, transaction);
        }

        /// <summary>
        /// Retrieves activities with related data using batch loading for performance optimization.
        /// Eliminates N+1 pattern in activity-related data loading.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activityUids">List of activity UIDs to load</param>
        /// <returns>List of complete activity data</returns>
        private async Task<List<ActivityData>> GetActivitiesWithRelatedDataBatchAsync(SqliteConnection connection, List<string> activityUids)
        {
            if (!activityUids.Any())
            {
                return new List<ActivityData>();
            }

            // Build IN clause for batch loading
            var uidParameters = string.Join(",", activityUids.Select((uid, index) => $"@uid{index}"));
            var parameters = new DynamicParameters();
            for (int i = 0; i < activityUids.Count; i++)
            {
                parameters.Add($"uid{i}", activityUids[i]);
            }

            // Load all activities in batch
            const string activitiesSql = @"
                SELECT Uid, ClientUid, ActivityType, ActivityStatus, ActivityStartDate,
                       CommercialRegister, ActivityLocation, NifNumber, NisNumber, ArtNumber,
                       CpiDaira, CpiWilaya, ActivityUpdateDate, ActivityUpdateNote,
                       CreatedAt, UpdatedAt
                FROM Activities
                WHERE Uid IN (" + "{0}" + @")
                ORDER BY CreatedAt ASC";

            var activityEntities = await connection.QueryAsync<ActivityEntity>(
                string.Format(activitiesSql, uidParameters), parameters);

            // Load all related data in batch to eliminate N+1 patterns
            var allActivityCodes = await GetActivityCodesBatchAsync(connection, activityUids);
            var allActivityDescriptions = await GetActivityDescriptionsBatchAsync(connection, activityUids);
            var allFileCheckStates = await GetFileCheckStatesBatchAsync(connection, activityUids);
            var allG12CheckYears = await GetG12CheckYearsBatchAsync(connection, activityUids);
            var allBisCheckYears = await GetBisCheckYearsBatchAsync(connection, activityUids);
            var allNotes = await GetNotesBatchAsync(connection, activityUids);

            // Build activity data objects
            var activities = new List<ActivityData>();

            foreach (var entity in activityEntities)
            {
                var activityData = new ActivityData
                {
                    Uid = entity.Uid,
                    ClientUid = entity.ClientUid,
                    ActivityType = entity.ActivityType,
                    ActivityStatus = entity.ActivityStatus,
                    ActivityStartDate = entity.ActivityStartDate,
                    CommercialRegister = entity.CommercialRegister,
                    ActivityLocation = entity.ActivityLocation,
                    NifNumber = entity.NifNumber,
                    NisNumber = entity.NisNumber,
                    ArtNumber = entity.ArtNumber,
                    CpiDaira = entity.CpiDaira,
                    CpiWilaya = entity.CpiWilaya,
                    ActivityUpdateDate = entity.ActivityUpdateDate,
                    ActivityUpdateNote = entity.ActivityUpdateNote,
                    CreatedAt = ParseTimestamp(entity.CreatedAt),
                    UpdatedAt = ParseTimestamp(entity.UpdatedAt),

                    // Assign related data from batch results
                    ActivityCodes = allActivityCodes.ContainsKey(entity.Uid) ? allActivityCodes[entity.Uid] : new List<int>(),
                    ActivityDescription = allActivityDescriptions.ContainsKey(entity.Uid) ? allActivityDescriptions[entity.Uid] : string.Empty,
                    FileCheckStates = allFileCheckStates.ContainsKey(entity.Uid) ? allFileCheckStates[entity.Uid] : new Dictionary<string, bool>(),
                    G12CheckYears = allG12CheckYears.ContainsKey(entity.Uid) ? allG12CheckYears[entity.Uid] : new List<int>(),
                    BisCheckYears = allBisCheckYears.ContainsKey(entity.Uid) ? allBisCheckYears[entity.Uid] : new List<int>(),
                    Notes = allNotes.ContainsKey(entity.Uid) ? allNotes[entity.Uid] : new List<NoteData>()
                };

                activities.Add(activityData);
            }

            return activities;
        }

        /// <summary>
        /// Retrieves all activities for a client with related data (legacy method for compatibility).
        /// This method maintains the original N+1 pattern for comparison and fallback scenarios.
        /// </summary>
        private async Task<List<ActivityData>> GetClientActivitiesAsync(SqliteConnection connection, string clientUID)
        {
            const string selectSql = @"
                SELECT Uid, ClientUid, ActivityType, ActivityStatus, ActivityStartDate,
                       CommercialRegister, ActivityLocation, NifNumber, NisNumber, ArtNumber,
                       CpiDaira, CpiWilaya, ActivityUpdateDate, ActivityUpdateNote,
                       CreatedAt, UpdatedAt
                FROM Activities
                WHERE ClientUid = @ClientUID
                ORDER BY CreatedAt ASC";

            var activityEntities = await connection.QueryAsync<ActivityEntity>(selectSql, new { ClientUID = clientUID });

            var activities = new List<ActivityData>();

            foreach (var entity in activityEntities)
            {
                var activityData = new ActivityData
                {
                    Uid = entity.Uid,
                    ClientUid = entity.ClientUid,
                    ActivityType = entity.ActivityType,
                    ActivityStatus = entity.ActivityStatus,
                    ActivityStartDate = entity.ActivityStartDate,
                    CommercialRegister = entity.CommercialRegister,
                    ActivityLocation = entity.ActivityLocation,
                    NifNumber = entity.NifNumber,
                    NisNumber = entity.NisNumber,
                    ArtNumber = entity.ArtNumber,
                    CpiDaira = entity.CpiDaira,
                    CpiWilaya = entity.CpiWilaya,
                    ActivityUpdateDate = entity.ActivityUpdateDate,
                    ActivityUpdateNote = entity.ActivityUpdateNote,
                    CreatedAt = ParseTimestamp(entity.CreatedAt),
                    UpdatedAt = ParseTimestamp(entity.UpdatedAt)
                };

                // Load related data for each activity (original N+1 pattern)
                activityData.ActivityCodes = await GetActivityCodesAsync(connection, entity.Uid);
                activityData.ActivityDescription = await GetActivityDescriptionAsync(connection, entity.Uid);
                activityData.CraftCode = await GetCraftCodeAsync(connection, entity.Uid);
                activityData.FileCheckStates = await GetFileCheckStatesAsync(connection, entity.Uid);
                activityData.G12CheckYears = await GetG12CheckYearsAsync(connection, entity.Uid);
                activityData.BisCheckYears = await GetBisCheckYearsAsync(connection, entity.Uid);
                activityData.Notes = await GetNotesAsync(connection, entity.Uid);

                activities.Add(activityData);
            }

            return activities;
        }

        /// <summary>
        /// Retrieves activity codes for an activity.
        /// </summary>
        private async Task<List<int>> GetActivityCodesAsync(SqliteConnection connection, string activityUID)
        {
            const string selectSql = @"
                SELECT ActivityCode
                FROM CommercialActivityCodes
                WHERE ActivityUid = @ActivityUID
                ORDER BY ActivityCode";

            var codes = await connection.QueryAsync<int>(selectSql, new { ActivityUID = activityUID });
            return codes.ToList();
        }

        /// <summary>
        /// Retrieves craft code for a craft activity.
        /// </summary>
        private async Task<string?> GetCraftCodeAsync(SqliteConnection connection, string activityUID)
        {
            const string selectSql = @"
                SELECT CraftCode
                FROM CraftActivityCodes
                WHERE ActivityUid = @ActivityUID";

            return await connection.QueryFirstOrDefaultAsync<string>(selectSql, new { ActivityUID = activityUID });
        }

        /// <summary>
        /// Retrieves activity description for an activity.
        /// </summary>
        private async Task<string?> GetActivityDescriptionAsync(SqliteConnection connection, string activityUID)
        {
            const string selectSql = @"
                SELECT ActivityDescription
                FROM ProfessionNames
                WHERE ActivityUid = @ActivityUID";

            return await connection.QueryFirstOrDefaultAsync<string>(selectSql, new { ActivityUID = activityUID });
        }

        /// <summary>
        /// Retrieves file check states for an activity.
        /// </summary>
        private async Task<Dictionary<string, bool>> GetFileCheckStatesAsync(SqliteConnection connection, string activityUID)
        {
            const string selectSql = @"
                SELECT FileCheckType, IsChecked
                FROM FileCheckStates
                WHERE ActivityUid = @ActivityUID";

            var states = await connection.QueryAsync(selectSql, new { ActivityUID = activityUID });
            
            return states.ToDictionary(
                state => (string)state.FileCheckType,
                state => (int)state.IsChecked == 1
            );
        }

        /// <summary>
        /// Retrieves G12 check years for an activity.
        /// </summary>
        private async Task<List<int>> GetG12CheckYearsAsync(SqliteConnection connection, string activityUID)
        {
            const string selectSql = @"
                SELECT Year
                FROM G12Check
                WHERE ActivityUid = @ActivityUID
                ORDER BY Year";

            var years = await connection.QueryAsync<int>(selectSql, new { ActivityUID = activityUID });
            return years.ToList();
        }

        /// <summary>
        /// Retrieves BIS check years for an activity.
        /// </summary>
        private async Task<List<int>> GetBisCheckYearsAsync(SqliteConnection connection, string activityUID)
        {
            const string selectSql = @"
                SELECT Year
                FROM BisCheck
                WHERE ActivityUid = @ActivityUID
                ORDER BY Year";

            var years = await connection.QueryAsync<int>(selectSql, new { ActivityUID = activityUID });
            return years.ToList();
        }

        /// <summary>
        /// Retrieves notes for an activity.
        /// </summary>
        private async Task<List<NoteData>> GetNotesAsync(SqliteConnection connection, string activityUID)
        {
            const string selectSql = @"
                SELECT ActivityUid, Content, Priority, CreatedAt, UpdatedAt
                FROM Notes
                WHERE ActivityUid = @ActivityUID
                ORDER BY Priority DESC, CreatedAt DESC";

            var noteEntities = await connection.QueryAsync<NoteEntity>(selectSql, new { ActivityUID = activityUID });
            
            return noteEntities.Select(entity => new NoteData
            {
                ActivityUid = entity.ActivityUid,
                Content = entity.Content ?? string.Empty,
                Priority = entity.Priority,
                CreatedAt = ParseTimestamp(entity.CreatedAt),
                UpdatedAt = ParseTimestamp(entity.UpdatedAt)
            }).ToList();
        }

        #endregion

        #region Batch Loading Methods for Performance Optimization

        /// <summary>
        /// Retrieves activity codes for multiple activities in batch to eliminate N+1 pattern.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activityUids">List of activity UIDs</param>
        /// <returns>Dictionary mapping activity UID to list of activity codes</returns>
        private async Task<Dictionary<string, List<int>>> GetActivityCodesBatchAsync(SqliteConnection connection, List<string> activityUids)
        {
            if (!activityUids.Any())
            {
                return new Dictionary<string, List<int>>();
            }

            var uidParameters = string.Join(",", activityUids.Select((uid, index) => $"@uid{index}"));
            var parameters = new DynamicParameters();
            for (int i = 0; i < activityUids.Count; i++)
            {
                parameters.Add($"uid{i}", activityUids[i]);
            }

            const string batchSql = @"
                SELECT ActivityUid, ActivityCode
                FROM CommercialActivityCodes
                WHERE ActivityUid IN (" + "{0}" + @")
                ORDER BY ActivityUid, ActivityCode";

            var results = await connection.QueryAsync(string.Format(batchSql, uidParameters), parameters);

            return results
                .GroupBy(r => (string)r.ActivityUid)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(r => (int)r.ActivityCode).ToList()
                );
        }

        /// <summary>
        /// Retrieves craft codes for multiple activities in batch to eliminate N+1 pattern.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activityUids">List of activity UIDs</param>
        /// <returns>Dictionary mapping activity UID to craft code</returns>
        private async Task<Dictionary<string, string>> GetCraftCodesBatchAsync(SqliteConnection connection, List<string> activityUids)
        {
            if (!activityUids.Any())
            {
                return new Dictionary<string, string>();
            }

            var uidParameters = string.Join(",", activityUids.Select((uid, index) => $"@uid{index}"));
            var parameters = new DynamicParameters();
            for (int i = 0; i < activityUids.Count; i++)
            {
                parameters.Add($"uid{i}", activityUids[i]);
            }

            const string batchSql = @"
                SELECT ActivityUid, CraftCode
                FROM CraftActivityCodes
                WHERE ActivityUid IN (" + "{0}" + @")";

            var results = await connection.QueryAsync(string.Format(batchSql, uidParameters), parameters);

            return results.ToDictionary(
                r => (string)r.ActivityUid,
                r => (string)r.CraftCode ?? string.Empty
            );
        }

        /// <summary>
        /// Retrieves activity descriptions for multiple activities in batch to eliminate N+1 pattern.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activityUids">List of activity UIDs</param>
        /// <returns>Dictionary mapping activity UID to activity description</returns>
        private async Task<Dictionary<string, string>> GetActivityDescriptionsBatchAsync(SqliteConnection connection, List<string> activityUids)
        {
            if (!activityUids.Any())
            {
                return new Dictionary<string, string>();
            }

            var uidParameters = string.Join(",", activityUids.Select((uid, index) => $"@uid{index}"));
            var parameters = new DynamicParameters();
            for (int i = 0; i < activityUids.Count; i++)
            {
                parameters.Add($"uid{i}", activityUids[i]);
            }

            const string batchSql = @"
                SELECT ActivityUid, ActivityDescription
                FROM ProfessionNames
                WHERE ActivityUid IN (" + "{0}" + @")";

            var results = await connection.QueryAsync(string.Format(batchSql, uidParameters), parameters);

            return results.ToDictionary(
                r => (string)r.ActivityUid,
                r => (string)r.ActivityDescription ?? string.Empty
            );
        }

        /// <summary>
        /// Retrieves file check states for multiple activities in batch to eliminate N+1 pattern.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activityUids">List of activity UIDs</param>
        /// <returns>Dictionary mapping activity UID to file check states dictionary</returns>
        private async Task<Dictionary<string, Dictionary<string, bool>>> GetFileCheckStatesBatchAsync(SqliteConnection connection, List<string> activityUids)
        {
            if (!activityUids.Any())
            {
                return new Dictionary<string, Dictionary<string, bool>>();
            }

            var uidParameters = string.Join(",", activityUids.Select((uid, index) => $"@uid{index}"));
            var parameters = new DynamicParameters();
            for (int i = 0; i < activityUids.Count; i++)
            {
                parameters.Add($"uid{i}", activityUids[i]);
            }

            const string batchSql = @"
                SELECT ActivityUid, FileCheckType, IsChecked
                FROM FileCheckStates
                WHERE ActivityUid IN (" + "{0}" + @")";

            var results = await connection.QueryAsync(string.Format(batchSql, uidParameters), parameters);

            return results
                .GroupBy(r => (string)r.ActivityUid)
                .ToDictionary(
                    g => g.Key,
                    g => g.ToDictionary(
                        r => (string)r.FileCheckType,
                        r => (int)r.IsChecked == 1
                    )
                );
        }

        /// <summary>
        /// Retrieves G12 check years for multiple activities in batch to eliminate N+1 pattern.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activityUids">List of activity UIDs</param>
        /// <returns>Dictionary mapping activity UID to list of G12 check years</returns>
        private async Task<Dictionary<string, List<int>>> GetG12CheckYearsBatchAsync(SqliteConnection connection, List<string> activityUids)
        {
            if (!activityUids.Any())
            {
                return new Dictionary<string, List<int>>();
            }

            var uidParameters = string.Join(",", activityUids.Select((uid, index) => $"@uid{index}"));
            var parameters = new DynamicParameters();
            for (int i = 0; i < activityUids.Count; i++)
            {
                parameters.Add($"uid{i}", activityUids[i]);
            }

            const string batchSql = @"
                SELECT ActivityUid, Year
                FROM G12Check
                WHERE ActivityUid IN (" + "{0}" + @")
                ORDER BY ActivityUid, Year";

            var results = await connection.QueryAsync(string.Format(batchSql, uidParameters), parameters);

            return results
                .GroupBy(r => (string)r.ActivityUid)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(r => (int)r.Year).ToList()
                );
        }

        /// <summary>
        /// Retrieves BIS check years for multiple activities in batch to eliminate N+1 pattern.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activityUids">List of activity UIDs</param>
        /// <returns>Dictionary mapping activity UID to list of BIS check years</returns>
        private async Task<Dictionary<string, List<int>>> GetBisCheckYearsBatchAsync(SqliteConnection connection, List<string> activityUids)
        {
            if (!activityUids.Any())
            {
                return new Dictionary<string, List<int>>();
            }

            var uidParameters = string.Join(",", activityUids.Select((uid, index) => $"@uid{index}"));
            var parameters = new DynamicParameters();
            for (int i = 0; i < activityUids.Count; i++)
            {
                parameters.Add($"uid{i}", activityUids[i]);
            }

            const string batchSql = @"
                SELECT ActivityUid, Year
                FROM BisCheck
                WHERE ActivityUid IN (" + "{0}" + @")
                ORDER BY ActivityUid, Year";

            var results = await connection.QueryAsync(string.Format(batchSql, uidParameters), parameters);

            return results
                .GroupBy(r => (string)r.ActivityUid)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(r => (int)r.Year).ToList()
                );
        }

        /// <summary>
        /// Retrieves notes for multiple activities in batch to eliminate N+1 pattern.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activityUids">List of activity UIDs</param>
        /// <returns>Dictionary mapping activity UID to list of notes</returns>
        private async Task<Dictionary<string, List<NoteData>>> GetNotesBatchAsync(SqliteConnection connection, List<string> activityUids)
        {
            if (!activityUids.Any())
            {
                return new Dictionary<string, List<NoteData>>();
            }

            var uidParameters = string.Join(",", activityUids.Select((uid, index) => $"@uid{index}"));
            var parameters = new DynamicParameters();
            for (int i = 0; i < activityUids.Count; i++)
            {
                parameters.Add($"uid{i}", activityUids[i]);
            }

            const string batchSql = @"
                SELECT ActivityUid, Content, Priority, CreatedAt, UpdatedAt
                FROM Notes
                WHERE ActivityUid IN (" + "{0}" + @")
                ORDER BY ActivityUid, Priority DESC, CreatedAt DESC";

            var results = await connection.QueryAsync(string.Format(batchSql, uidParameters), parameters);

            return results
                .GroupBy(r => (string)r.ActivityUid)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(r => new NoteData
                    {
                        ActivityUid = (string)r.ActivityUid,
                        Content = (string)r.Content ?? string.Empty,
                        Priority = (int)r.Priority,
                        CreatedAt = ParseTimestamp((string)r.CreatedAt),
                        UpdatedAt = ParseTimestamp((string)r.UpdatedAt)
                    }).ToList()
                );
        }

        /// <summary>
        /// Tracks database operation performance for optimization validation.
        /// Integrates with DatabasePerformanceMonitoringService for comprehensive monitoring.
        /// </summary>
        /// <param name="operationName">Name of the database operation</param>
        /// <param name="executionTimeMs">Execution time in milliseconds</param>
        /// <param name="recordCount">Number of records processed</param>
        private async Task TrackDatabaseOperationPerformanceAsync(string operationName, long executionTimeMs, int recordCount)
        {
            try
            {
                // Log performance metrics for analysis
                LoggingService.LogInfo($"Database operation performance: {operationName} - {executionTimeMs}ms - {recordCount} records", "ClientDatabaseService");

                // Additional performance tracking can be added here
                // Integration with DatabasePerformanceMonitoringService if needed
                if (executionTimeMs > 200) // Log slow operations
                {
                    LoggingService.LogWarning($"Slow database operation detected: {operationName} took {executionTimeMs}ms", "ClientDatabaseService");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error tracking database performance: {ex.Message}", "ClientDatabaseService");
            }
        }

        /// <summary>
        /// Performance validation method to test the GetClientAsync optimization.
        /// Compares optimized vs legacy implementation performance.
        /// </summary>
        /// <param name="clientUID">Client UID to test with</param>
        /// <param name="iterations">Number of test iterations (default: 5)</param>
        /// <returns>Performance comparison results</returns>
        public async Task<PerformanceComparisonResult> ValidateGetClientAsyncPerformanceAsync(string clientUID, int iterations = 5)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                throw new ArgumentException("Client UID is required for performance validation", nameof(clientUID));
            }

            var optimizedTimes = new List<long>();
            var legacyTimes = new List<long>();

            LoggingService.LogInfo($"Starting GetClientAsync performance validation with {iterations} iterations for client: {clientUID}", "ClientDatabaseService");

            // Test optimized implementation
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var optimizedResult = await GetClientAsync(clientUID);
                stopwatch.Stop();
                optimizedTimes.Add(stopwatch.ElapsedMilliseconds);

                if (optimizedResult == null)
                {
                    throw new InvalidOperationException($"Client not found during optimized test: {clientUID}");
                }

                // Small delay between iterations
                await Task.Delay(10);
            }

            // Test legacy implementation
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var legacyResult = await GetClientAsync_Legacy(clientUID);
                stopwatch.Stop();
                legacyTimes.Add(stopwatch.ElapsedMilliseconds);

                if (legacyResult == null)
                {
                    throw new InvalidOperationException($"Client not found during legacy test: {clientUID}");
                }

                // Small delay between iterations
                await Task.Delay(10);
            }

            // Calculate performance metrics
            var optimizedAvg = optimizedTimes.Average();
            var legacyAvg = legacyTimes.Average();
            var improvementPercentage = ((legacyAvg - optimizedAvg) / legacyAvg) * 100;

            var result = new PerformanceComparisonResult
            {
                OptimizedAverageMs = optimizedAvg,
                LegacyAverageMs = legacyAvg,
                ImprovementPercentage = improvementPercentage,
                OptimizedTimes = optimizedTimes,
                LegacyTimes = legacyTimes,
                Iterations = iterations,
                ClientUID = clientUID,
                TestTimestamp = DateTime.UtcNow
            };

            LoggingService.LogInfo($"Performance validation completed - Optimized: {optimizedAvg:F2}ms, Legacy: {legacyAvg:F2}ms, Improvement: {improvementPercentage:F1}%", "ClientDatabaseService");

            return result;
        }

        #endregion

        #region File Check Business Rule Management

        /// <summary>
        /// Handles activity type changes by updating file check states according to business rules.
        /// Removes invalid file check states and ensures required ones exist.
        /// </summary>
        /// <param name="activityUID">The activity UID</param>
        /// <param name="oldActivityType">The old activity type</param>
        /// <param name="newActivityType">The new activity type</param>
        /// <returns>True if operation was successful</returns>
        public async Task<bool> HandleActivityTypeChangeAsync(string activityUID, string oldActivityType, string newActivityType)
        {
            if (string.IsNullOrWhiteSpace(activityUID) || string.IsNullOrWhiteSpace(newActivityType))
            {
                LoggingService.LogError("Activity UID and new activity type are required", "ClientDatabaseService");
                return false;
            }

            try
            {
                return await _fileCheckBusinessRuleService.HandleActivityTypeChangeAsync(activityUID, oldActivityType, newActivityType);
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في معالجة تغيير نوع النشاط", "خطأ في المعالجة", 
                                       LogLevel.Error, "ClientDatabaseService");
                return false;
            }
        }

        /// <summary>
        /// Gets file check completion statistics for an activity.
        /// </summary>
        /// <param name="activityUID">The activity UID</param>
        /// <param name="activityType">The activity type</param>
        /// <returns>File check completion statistics</returns>
        public async Task<FileCheckCompletionStats> GetFileCheckCompletionStatsAsync(string activityUID, string activityType)
        {
            try
            {
                return await _fileCheckBusinessRuleService.GetFileCheckCompletionStatsAsync(activityUID, activityType);
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في الحصول على إحصائيات فحص الملفات", "خطأ في قاعدة البيانات", 
                                       LogLevel.Error, "ClientDatabaseService");
                return new FileCheckCompletionStats();
            }
        }

        /// <summary>
        /// Validates file check states for an activity against business rules.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckStates">The file check states to validate</param>
        /// <param name="enforceCompletion">Whether to enforce completion of all required checks</param>
        /// <returns>Validation result</returns>
        public GeneralValidationResult ValidateFileCheckStatesForActivity(string activityType, Dictionary<string, bool> fileCheckStates, bool enforceCompletion = false)
        {
            try
            {
                return _fileCheckBusinessRuleService.ValidateFileCheckStates(activityType, fileCheckStates, enforceCompletion);
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في التحقق من حالات فحص الملفات", "خطأ في التحقق", 
                                       LogLevel.Error, "ClientDatabaseService");
                var result = new GeneralValidationResult();
                result.AddError("Validation", "حدث خطأ أثناء التحقق من حالات فحص الملفات");
                return result;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the ClientDatabaseService resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources if any
                    LoggingService.LogDebug("ClientDatabaseService disposed", "ClientDatabaseService");
                }
                _disposed = true;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Parses a timestamp string in "dd/MM/yyyy HH:mm:ss" format to DateTime.
        /// Returns DateTime.MinValue if parsing fails.
        /// </summary>
        private static DateTime ParseTimestamp(string timestamp)
        {
            if (string.IsNullOrWhiteSpace(timestamp))
            {
                return DateTime.MinValue;
            }

            if (DateTime.TryParseExact(timestamp, "dd/MM/yyyy HH:mm:ss", null, System.Globalization.DateTimeStyles.None, out DateTime result))
            {
                return result;
            }

            // Fallback: try parsing with default format
            if (DateTime.TryParse(timestamp, out result))
            {
                return result;
            }

            LoggingService.LogWarning($"Failed to parse timestamp: {timestamp}", "ClientDatabaseService");
            return DateTime.MinValue;
        }

        /// <summary>
        /// Converts empty or whitespace-only strings to NULL for database storage.
        /// This ensures proper database normalization by storing NULL instead of empty strings
        /// for optional columns, improving query performance and data consistency.
        /// </summary>
        /// <param name="value">The string value to check</param>
        /// <returns>NULL if the string is empty or whitespace-only, otherwise the original value</returns>
        private static string? ConvertEmptyStringToNull(string? value)
        {
            return string.IsNullOrWhiteSpace(value) ? null : value;
        }

        #endregion
    }
}
