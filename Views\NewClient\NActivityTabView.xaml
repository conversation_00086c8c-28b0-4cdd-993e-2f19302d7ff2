<UserControl
    x:Class="UFU2.Views.NewClient.NActivityTabView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    d:DesignWidth="800"
    AutomationProperties.HelpText="Activity information form section"
    AutomationProperties.ItemType="Form"
    AutomationProperties.Name="Activity Information Form"
    FlowDirection="RightToLeft"
    Focusable="True"
    IsTabStop="True"
    SnapsToDevicePixels="True"
    UseLayoutRounding="True"
    mc:Ignorable="d">

    <Grid Margin="0,12,8,0">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <!--  Header Text - Left Side  -->
        <TextBlock
            Grid.Column="0"
            Style="{DynamicResource SubtitleStyle}"
            Text="المعلومات خاصة بــ :" />

        <!--  Activity Type Tab Headers - Center  -->
        <Grid Grid.Column="1" Margin="8,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  Main Commercial Activity Tab  -->
            <RadioButton
                x:Name="MainCommercialActivityTab"
                Grid.Column="0"
                Command="{Binding SwitchActivityTabCommand}"
                CommandParameter="MainCommercial"
                Content="النشاط التجاري الرئيسي"
                GroupName="ActivityTabs"
                IsChecked="True"
                Style="{StaticResource TabRadioButtonStyle}" />

            <!--  Secondary Commercial Activity Tab  -->
            <RadioButton
                x:Name="SecondaryCommercialActivityTab"
                Grid.Column="1"
                Command="{Binding SwitchActivityTabCommand}"
                CommandParameter="SecondaryCommercial"
                Content="النشاط التجاري الثانوي"
                GroupName="ActivityTabs"
                Style="{StaticResource TabRadioButtonStyle}" />

            <!--  Craft Activity Tab  -->
            <RadioButton
                x:Name="CraftActivityTab"
                Grid.Column="2"
                Command="{Binding SwitchActivityTabCommand}"
                CommandParameter="Craft"
                Content="النشاط الحرفي"
                GroupName="ActivityTabs"
                Style="{StaticResource TabRadioButtonStyle}" />

            <!--  Professional Activity Tab  -->
            <RadioButton
                x:Name="ProfessionalActivityTab"
                Grid.Column="3"
                Command="{Binding SwitchActivityTabCommand}"
                CommandParameter="Professional"
                Content="النشاط المهني"
                GroupName="ActivityTabs"
                Style="{StaticResource TabRadioButtonStyle}" />
        </Grid>

        <!--  Activity ID Format Toggle - Right Side  -->
        <ToggleButton
            x:Name="ActivityIdFormat"
            Grid.Column="2"
            AutomationProperties.HelpText="Toggle between activity ID formats"
            AutomationProperties.Name="Activity ID Format Toggle"
            IsChecked="{Binding ActivityIdFormatEnabled, Mode=TwoWay}"
            Style="{StaticResource ToggleSwitchStyle}"
            ToolTip="تبديل تنسيق معرف النشاط" />


    </Grid>
</UserControl>
