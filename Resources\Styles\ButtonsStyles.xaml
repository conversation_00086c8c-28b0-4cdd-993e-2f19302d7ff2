﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls;assembly=UFU2">

    <!--  Converters  -->
    <userControls:BoolToMaximizeRestoreIconConverter x:Key="BoolToMaximizeRestoreIconConverter" />
    <userControls:BoolToMaximizeRestoreTooltipConverter x:Key="BoolToMaximizeRestoreTooltipConverter" />

    <!--
        ========================================
        BUTTON COMPONENT STYLES
        ========================================
    -->

    <!--  ========== Primary Button Style ==========  -->
    <Style
        x:Key="PrimaryButtonStyle"
        BasedOn="{StaticResource MaterialDesignRaisedButton}"
        TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundPrimary}" />
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundPrimary}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderPrimary}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Height" Value="30" />
        <Setter Property="MinWidth" Value="36" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3" />
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="2" />
        <Setter Property="Cursor" Value="Hand" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ButtonHoverPrimary}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  ========== Secondary Button Style ==========  -->
    <Style
        x:Key="SecondaryButtonStyle"
        BasedOn="{StaticResource MaterialDesignRaisedButton}"
        TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundSecondary}" />
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundSecondary}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderSecondary}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3" />
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="2" />
        <Setter Property="Cursor" Value="Hand" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ButtonHoverSecondary}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  ========== Container Button Style ==========  -->
    <Style
        x:Key="ContainerButtonStyle"
        BasedOn="{StaticResource MaterialDesignRaisedButton}"
        TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundContainer}" />
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundContainer}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderContainer}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3" />
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="2" />
        <Setter Property="Panel.ZIndex" Value="1" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Cursor" Value="Hand" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ButtonBackgroundHoverContainer}" />
                <Setter Property="BorderBrush" Value="{DynamicResource ButtonBackgroundHoverContainer}" />
            </Trigger>
        </Style.Triggers>
    </Style>


    <!--  ========== Dialog Back Button Style ==========  -->
    <Style
        x:Key="BackButtonStyle"
        BasedOn="{StaticResource MaterialDesignFlatButton}"
        TargetType="Button">
        <Setter Property="Foreground" Value="{DynamicResource BackIconForeground}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Width" Value="40" />
        <Setter Property="Height" Value="40" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Panel.ZIndex" Value="1" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="20" />
    </Style>

    <!--
        ========================================
        ICON BUTTON COMPONENT STYLES
        ========================================
    -->

    <!--  ========== Icon Button Style ==========  -->
    <Style x:Key="IconButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{DynamicResource IconButtonBackgroundBase}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Width" Value="40" />
        <Setter Property="Height" Value="40" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="border"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Content="{TemplateBinding Content}"
                            ContentStringFormat="{TemplateBinding ContentStringFormat}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"
                            Focusable="False"
                            RecognizesAccessKey="True" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="{DynamicResource IconButtonBackgroundHover}" />
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Foreground" Value="{DynamicResource IconButtonBackgroundHover}" />
                            <Setter TargetName="border" Property="Background" Value="Transparent" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="{DynamicResource BorderRadiusRound}" />
    </Style>

    <!--  ========== Icon Plus Button Style ==========  -->
    <Style
        x:Key="PlusButtonStyle"
        BasedOn="{StaticResource ContainerButtonStyle}"
        TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource PlusBorderBackgroundBase}" />
        <Setter Property="Foreground" Value="{DynamicResource PlusForegroundBase}" />
        <Setter Property="Width" Value="24" />
        <Setter Property="Height" Value="24" />
        <Setter Property="HorizontalAlignment" Value="Right" />
        <Setter Property="VerticalAlignment" Value="Bottom" />
        <Setter Property="Panel.ZIndex" Value="1" />
        <Setter Property="RenderTransformOrigin" Value="0.5, 0.5" />
    </Style>

    <!--  ========== Icon Delete Button Style ==========  -->
    <Style
        x:Key="IconDeleteButtonStyle"
        BasedOn="{StaticResource IconButtonStyle}"
        TargetType="Button">
        <Setter Property="Width" Value="32" />
        <Setter Property="Height" Value="32" />
        <Setter Property="Foreground" Value="{DynamicResource DeleteButtonForeground}" />
        <Setter Property="Content">
            <Setter.Value>
                <materialDesign:PackIcon
                    Width="16"
                    Height="16"
                    Kind="Delete" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" Value="{DynamicResource DeleteButtonForegroundHover}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Foreground" Value="{DynamicResource DeleteButtonForegroundHover}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  ========== Icon Edit Button Style ==========  -->
    <Style
        x:Key="IconEditButtonStyle"
        BasedOn="{StaticResource IconButtonStyle}"
        TargetType="Button">
        <Setter Property="Width" Value="32" />
        <Setter Property="Height" Value="32" />
        <Setter Property="Foreground" Value="{DynamicResource EditButtonForeground}" />
        <Setter Property="Content">
            <Setter.Value>
                <materialDesign:PackIcon
                    Width="16"
                    Height="16"
                    Kind="Delete" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" Value="{DynamicResource EditButtonForegroundHover}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Foreground" Value="{DynamicResource EditButtonForegroundHover}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  ========== PopupBox Button Style ==========  -->
    <Style
        x:Key="ContainerSplitButtonStyle"
        BasedOn="{StaticResource MaterialDesignRaisedButton}"
        TargetType="materialDesign:SplitButton">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundContainer}" />
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundContainer}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderContainer}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3" />
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="2" />
        <Setter Property="Panel.ZIndex" Value="1" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Cursor" Value="Hand" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ButtonBackgroundHoverContainer}" />
                <Setter Property="BorderBrush" Value="{DynamicResource ButtonBackgroundHoverContainer}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--
        ========================================
        CHROME BUTTON COMPONENT STYLES
        ========================================
    -->
    <!--  ========== Chrome Button Style ==========  -->

    <Style
        x:Key="ChromButtonStyle"
        BasedOn="{StaticResource MaterialDesignRaisedButton}"
        TargetType="Button">
        <Setter Property="Width" Value="48" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp0" />
        <Setter Property="Height" Value="27" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="0" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="{DynamicResource WindowButtonForegroud}" />
    </Style>

    <!--  ========== Chrome Close Button Style ==========  -->
    <Style
        x:Key="CloseButtonStyle"
        BasedOn="{StaticResource ChromButtonStyle}"
        TargetType="Button">
        <Setter Property="Content">
            <Setter.Value>
                <materialDesign:PackIcon
                    Width="16"
                    Height="16"
                    Kind="WindowClose" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource WindowCloseButtonHoverBackgroud}" />
                <Setter Property="Foreground" Value="White" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource WindowCloseButtonPressBackgroud}" />
                <Setter Property="Foreground" Value="White" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  ========== Chrome Minimize Button Style ==========  -->
    <Style
        x:Key="MinmizeButtonStyle"
        BasedOn="{StaticResource ChromButtonStyle}"
        TargetType="Button">
        <Setter Property="Content">
            <Setter.Value>
                <materialDesign:PackIcon
                    Width="16"
                    Height="16"
                    AutomationProperties.Name="أيقونة التصغير"
                    Kind="WindowMinimize" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource WindowButtonHoverBackgroud}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource WindowButtonPressBackgroud}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  ========== Chrome Maximize Button Style ==========  -->
    <Style
        x:Key="MaximizeButtonStyle"
        BasedOn="{StaticResource ChromButtonStyle}"
        TargetType="Button">
        <Setter Property="ToolTip">
            <Setter.Value>
                <TextBlock Text="{Binding IsMaximized, Converter={StaticResource BoolToMaximizeRestoreTooltipConverter}}" />
            </Setter.Value>
        </Setter>
        <Setter Property="Content">
            <Setter.Value>
                <materialDesign:PackIcon
                    Width="16"
                    Height="16"
                    AutomationProperties.Name="{Binding IsMaximized, Converter={StaticResource BoolToMaximizeRestoreTooltipConverter}}"
                    Kind="{Binding IsMaximized, Converter={StaticResource BoolToMaximizeRestoreIconConverter}}"
                    RenderTransformOrigin=".5,.5">
                    <materialDesign:PackIcon.RenderTransform>
                        <RotateTransform Angle="180" />
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource WindowButtonHoverBackgroud}" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource WindowButtonPressBackgroud}" />
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>