using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Centralized resource management service for UFU2 memory optimization.
    /// Tracks disposable resources, event subscriptions, and provides automatic cleanup.
    /// Implements Phase 2D Task 2.1 requirements for centralized resource tracking.
    /// Integrates with UFU2 ServiceLocator, BaseViewModel, and logging infrastructure.
    /// </summary>
    public class ResourceManager : IDisposable
    {
        #region Private Fields

        private readonly ConcurrentDictionary<string, WeakReference> _trackedResources;
        private readonly ConcurrentDictionary<string, List<EventSubscription>> _eventSubscriptions;
        private readonly ConcurrentDictionary<string, ResourceInfo> _resourceMetadata;
        private readonly Timer _cleanupTimer;
        private readonly Timer _memoryMonitoringTimer;
        private readonly object _cleanupLock = new object();
        private bool _disposed = false;

        // Performance tracking
        private long _totalResourcesTracked = 0;
        private long _totalResourcesDisposed = 0;
        private long _totalEventSubscriptions = 0;
        private long _totalEventUnsubscriptions = 0;
        private long _memoryLeaksDetected = 0;
        private long _automaticCleanups = 0;

        // Configuration
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _memoryMonitoringInterval = TimeSpan.FromMinutes(2);
        private readonly int _maxResourcesPerOwner = 100;
        private readonly double _memoryPressureThresholdMB = 400.0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ResourceManager service.
        /// Sets up automatic cleanup timers and memory monitoring.
        /// </summary>
        public ResourceManager()
        {
            _trackedResources = new ConcurrentDictionary<string, WeakReference>();
            _eventSubscriptions = new ConcurrentDictionary<string, List<EventSubscription>>();
            _resourceMetadata = new ConcurrentDictionary<string, ResourceInfo>();

            // Initialize cleanup timer
            _cleanupTimer = new Timer(PerformAutomaticCleanup, null, _cleanupInterval, _cleanupInterval);
            
            // Initialize memory monitoring timer
            _memoryMonitoringTimer = new Timer(MonitorMemoryUsage, null, _memoryMonitoringInterval, _memoryMonitoringInterval);

            LoggingService.LogInfo("ResourceManager initialized with automatic cleanup and memory monitoring", "ResourceManager");
        }

        #endregion

        #region Public Methods - Resource Registration

        /// <summary>
        /// Registers a disposable resource for automatic tracking and cleanup.
        /// </summary>
        /// <typeparam name="T">Type of resource to register</typeparam>
        /// <param name="resourceId">Unique identifier for the resource</param>
        /// <param name="resource">The resource instance to track</param>
        /// <param name="ownerType">Type of the owner (ViewModel, UserControl, etc.)</param>
        /// <param name="category">Category of resource for organization</param>
        public void RegisterResource<T>(string resourceId, T resource, Type ownerType, ResourceCategory category = ResourceCategory.General) where T : class
        {
            if (string.IsNullOrEmpty(resourceId) || resource == null || ownerType == null)
                return;

            try
            {
                var weakRef = new WeakReference(resource);
                var resourceInfo = new ResourceInfo
                {
                    ResourceId = resourceId,
                    ResourceType = typeof(T),
                    OwnerType = ownerType,
                    Category = category,
                    RegisteredAt = DateTime.UtcNow,
                    IsDisposable = resource is IDisposable
                };

                _trackedResources.AddOrUpdate(resourceId, weakRef, (key, oldValue) => weakRef);
                _resourceMetadata.AddOrUpdate(resourceId, resourceInfo, (key, oldValue) => resourceInfo);

                System.Threading.Interlocked.Increment(ref _totalResourcesTracked);

                LoggingService.LogDebug($"Registered resource: {resourceId} ({typeof(T).Name}) for {ownerType.Name}", "ResourceManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error registering resource {resourceId}: {ex.Message}", "ResourceManager");
            }
        }

        /// <summary>
        /// Registers an event subscription for automatic cleanup.
        /// </summary>
        /// <param name="sourceId">Identifier for the event source</param>
        /// <param name="eventName">Name of the event</param>
        /// <param name="handler">Event handler delegate</param>
        /// <param name="ownerType">Type of the owner subscribing to the event</param>
        public void RegisterEventSubscription(string sourceId, string eventName, Delegate handler, Type ownerType)
        {
            if (string.IsNullOrEmpty(sourceId) || string.IsNullOrEmpty(eventName) || handler == null || ownerType == null)
                return;

            try
            {
                var subscription = new EventSubscription
                {
                    SourceId = sourceId,
                    EventName = eventName,
                    Handler = new WeakReference(handler),
                    OwnerType = ownerType,
                    SubscribedAt = DateTime.UtcNow
                };

                var subscriptionKey = $"{sourceId}_{eventName}_{ownerType.Name}";
                _eventSubscriptions.AddOrUpdate(subscriptionKey, 
                    new List<EventSubscription> { subscription },
                    (key, existingList) =>
                    {
                        existingList.Add(subscription);
                        return existingList;
                    });

                System.Threading.Interlocked.Increment(ref _totalEventSubscriptions);

                LoggingService.LogDebug($"Registered event subscription: {eventName} for {ownerType.Name}", "ResourceManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error registering event subscription {eventName}: {ex.Message}", "ResourceManager");
            }
        }

        #endregion

        #region Public Methods - Resource Cleanup

        /// <summary>
        /// Unregisters and disposes a tracked resource.
        /// </summary>
        /// <param name="resourceId">Identifier of the resource to unregister</param>
        /// <param name="forceDispose">Whether to force disposal even if resource is still alive</param>
        public void UnregisterResource(string resourceId, bool forceDispose = false)
        {
            if (string.IsNullOrEmpty(resourceId))
                return;

            try
            {
                if (_trackedResources.TryRemove(resourceId, out var weakRef))
                {
                    if (weakRef.IsAlive && (forceDispose || weakRef.Target is IDisposable))
                    {
                        if (weakRef.Target is IDisposable disposable)
                        {
                            disposable.Dispose();
                            System.Threading.Interlocked.Increment(ref _totalResourcesDisposed);
                        }
                    }

                    _resourceMetadata.TryRemove(resourceId, out _);
                    LoggingService.LogDebug($"Unregistered resource: {resourceId}", "ResourceManager");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error unregistering resource {resourceId}: {ex.Message}", "ResourceManager");
            }
        }

        /// <summary>
        /// Unregisters an event subscription.
        /// </summary>
        /// <param name="sourceId">Identifier for the event source</param>
        /// <param name="eventName">Name of the event</param>
        /// <param name="ownerType">Type of the owner that subscribed to the event</param>
        public void UnregisterEventSubscription(string sourceId, string eventName, Type ownerType)
        {
            if (string.IsNullOrEmpty(sourceId) || string.IsNullOrEmpty(eventName) || ownerType == null)
                return;

            try
            {
                var subscriptionKey = $"{sourceId}_{eventName}_{ownerType.Name}";
                if (_eventSubscriptions.TryRemove(subscriptionKey, out var subscriptions))
                {
                    System.Threading.Interlocked.Add(ref _totalEventUnsubscriptions, subscriptions.Count);
                    LoggingService.LogDebug($"Unregistered event subscriptions: {eventName} for {ownerType.Name} ({subscriptions.Count} handlers)", "ResourceManager");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error unregistering event subscription {eventName}: {ex.Message}", "ResourceManager");
            }
        }

        /// <summary>
        /// Unregisters all resources and event subscriptions for a specific owner type.
        /// </summary>
        /// <param name="ownerType">Type of the owner to clean up</param>
        public void UnregisterOwnerResources(Type ownerType)
        {
            if (ownerType == null)
                return;

            try
            {
                var resourcesToRemove = _resourceMetadata
                    .Where(kvp => kvp.Value.OwnerType == ownerType)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var resourceId in resourcesToRemove)
                {
                    UnregisterResource(resourceId, true);
                }

                var subscriptionsToRemove = _eventSubscriptions
                    .Where(kvp => kvp.Value.Any(s => s.OwnerType == ownerType))
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var subscriptionKey in subscriptionsToRemove)
                {
                    _eventSubscriptions.TryRemove(subscriptionKey, out _);
                }

                LoggingService.LogInfo($"Cleaned up all resources for owner type: {ownerType.Name} ({resourcesToRemove.Count} resources, {subscriptionsToRemove.Count} event subscriptions)", "ResourceManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error cleaning up resources for owner type {ownerType.Name}: {ex.Message}", "ResourceManager");
            }
        }

        #endregion

        #region Public Methods - Memory Management

        /// <summary>
        /// Forces immediate cleanup of dead references and unused resources.
        /// </summary>
        public void ForceCleanup()
        {
            try
            {
                lock (_cleanupLock)
                {
                    CleanupDeadReferences();
                    CleanupDeadEventSubscriptions();
                    System.Threading.Interlocked.Increment(ref _automaticCleanups);
                }

                LoggingService.LogInfo("Forced cleanup completed", "ResourceManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during forced cleanup: {ex.Message}", "ResourceManager");
            }
        }

        /// <summary>
        /// Generates a comprehensive memory leak report.
        /// </summary>
        /// <returns>Memory leak report with detailed analysis</returns>
        public MemoryLeakReport GenerateLeakReport()
        {
            try
            {
                var report = new MemoryLeakReport
                {
                    GeneratedAt = DateTime.UtcNow,
                    TotalResourcesTracked = _totalResourcesTracked,
                    TotalResourcesDisposed = _totalResourcesDisposed,
                    TotalEventSubscriptions = _totalEventSubscriptions,
                    TotalEventUnsubscriptions = _totalEventUnsubscriptions,
                    MemoryLeaksDetected = _memoryLeaksDetected,
                    AutomaticCleanups = _automaticCleanups,
                    CurrentMemoryUsageMB = GetCurrentMemoryUsageMB()
                };

                // Analyze current tracked resources
                report.AliveResources = _trackedResources.Count(kvp => kvp.Value.IsAlive);
                report.DeadResources = _trackedResources.Count(kvp => !kvp.Value.IsAlive);

                // Analyze event subscriptions
                report.ActiveEventSubscriptions = _eventSubscriptions.Sum(kvp => kvp.Value.Count(s => s.Handler.IsAlive));
                report.DeadEventSubscriptions = _eventSubscriptions.Sum(kvp => kvp.Value.Count(s => !s.Handler.IsAlive));

                // Resource breakdown by category
                report.ResourcesByCategory = _resourceMetadata.Values
                    .GroupBy(r => r.Category)
                    .ToDictionary(g => g.Key, g => g.Count());

                // Owner type analysis
                report.ResourcesByOwnerType = _resourceMetadata.Values
                    .GroupBy(r => r.OwnerType.Name)
                    .ToDictionary(g => g.Key, g => g.Count());

                LoggingService.LogInfo($"Generated memory leak report: {report.AliveResources} alive resources, {report.DeadResources} dead resources", "ResourceManager");
                return report;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating leak report: {ex.Message}", "ResourceManager");
                return new MemoryLeakReport { GeneratedAt = DateTime.UtcNow };
            }
        }

        #endregion

        #region Private Methods - Automatic Cleanup

        /// <summary>
        /// Performs automatic cleanup of dead references and unused resources.
        /// Called periodically by the cleanup timer.
        /// </summary>
        private void PerformAutomaticCleanup(object? state)
        {
            try
            {
                lock (_cleanupLock)
                {
                    CleanupDeadReferences();
                    CleanupDeadEventSubscriptions();
                    System.Threading.Interlocked.Increment(ref _automaticCleanups);
                }

                LoggingService.LogDebug("Automatic cleanup completed", "ResourceManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during automatic cleanup: {ex.Message}", "ResourceManager");
            }
        }

        /// <summary>
        /// Cleans up dead weak references from tracked resources.
        /// </summary>
        private void CleanupDeadReferences()
        {
            var deadResources = _trackedResources
                .Where(kvp => !kvp.Value.IsAlive)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var resourceId in deadResources)
            {
                _trackedResources.TryRemove(resourceId, out _);
                _resourceMetadata.TryRemove(resourceId, out _);
            }

            if (deadResources.Count > 0)
            {
                LoggingService.LogDebug($"Cleaned up {deadResources.Count} dead resource references", "ResourceManager");
            }
        }

        /// <summary>
        /// Cleans up dead event subscriptions.
        /// </summary>
        private void CleanupDeadEventSubscriptions()
        {
            var subscriptionsToUpdate = new List<string>();

            foreach (var kvp in _eventSubscriptions)
            {
                var aliveSubscriptions = kvp.Value.Where(s => s.Handler.IsAlive).ToList();
                if (aliveSubscriptions.Count != kvp.Value.Count)
                {
                    subscriptionsToUpdate.Add(kvp.Key);
                }
            }

            foreach (var key in subscriptionsToUpdate)
            {
                if (_eventSubscriptions.TryGetValue(key, out var subscriptions))
                {
                    var aliveSubscriptions = subscriptions.Where(s => s.Handler.IsAlive).ToList();
                    if (aliveSubscriptions.Count == 0)
                    {
                        _eventSubscriptions.TryRemove(key, out _);
                    }
                    else
                    {
                        _eventSubscriptions[key] = aliveSubscriptions;
                    }
                }
            }

            if (subscriptionsToUpdate.Count > 0)
            {
                LoggingService.LogDebug($"Cleaned up dead event subscriptions in {subscriptionsToUpdate.Count} subscription groups", "ResourceManager");
            }
        }

        /// <summary>
        /// Monitors memory usage and triggers cleanup when thresholds are exceeded.
        /// </summary>
        private void MonitorMemoryUsage(object? state)
        {
            try
            {
                var currentMemoryMB = GetCurrentMemoryUsageMB();

                if (currentMemoryMB > _memoryPressureThresholdMB)
                {
                    LoggingService.LogWarning($"Memory pressure detected: {currentMemoryMB}MB. Triggering cleanup.", "ResourceManager");
                    ForceCleanup();

                    // Trigger garbage collection if memory pressure is high
                    if (currentMemoryMB > _memoryPressureThresholdMB * 1.5)
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        GC.Collect();
                        LoggingService.LogInfo("Forced garbage collection due to high memory pressure", "ResourceManager");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error monitoring memory usage: {ex.Message}", "ResourceManager");
            }
        }

        /// <summary>
        /// Gets the current memory usage in megabytes.
        /// </summary>
        private double GetCurrentMemoryUsageMB()
        {
            try
            {
                using (var process = Process.GetCurrentProcess())
                {
                    return process.WorkingSet64 / (1024.0 * 1024.0);
                }
            }
            catch
            {
                return 0.0;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the ResourceManager and cleans up all tracked resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method for proper disposal pattern.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        LoggingService.LogInfo("Disposing ResourceManager and cleaning up all tracked resources", "ResourceManager");

                        // Stop timers
                        _cleanupTimer?.Dispose();
                        _memoryMonitoringTimer?.Dispose();

                        // Force cleanup of all resources
                        ForceCleanup();

                        // Dispose all remaining tracked resources
                        foreach (var kvp in _trackedResources)
                        {
                            if (kvp.Value.IsAlive && kvp.Value.Target is IDisposable disposable)
                            {
                                try
                                {
                                    disposable.Dispose();
                                    System.Threading.Interlocked.Increment(ref _totalResourcesDisposed);
                                }
                                catch (Exception ex)
                                {
                                    LoggingService.LogWarning($"Error disposing resource {kvp.Key}: {ex.Message}", "ResourceManager");
                                }
                            }
                        }

                        // Clear all collections
                        _trackedResources.Clear();
                        _eventSubscriptions.Clear();
                        _resourceMetadata.Clear();

                        LoggingService.LogInfo($"ResourceManager disposed. Final stats: {_totalResourcesTracked} tracked, {_totalResourcesDisposed} disposed, {_automaticCleanups} cleanups", "ResourceManager");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error during ResourceManager disposal: {ex.Message}", "ResourceManager");
                    }
                }
                _disposed = true;
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Information about a tracked resource.
    /// </summary>
    public class ResourceInfo
    {
        public string ResourceId { get; set; } = string.Empty;
        public Type ResourceType { get; set; } = typeof(object);
        public Type OwnerType { get; set; } = typeof(object);
        public ResourceCategory Category { get; set; } = ResourceCategory.General;
        public DateTime RegisteredAt { get; set; }
        public bool IsDisposable { get; set; }
    }

    /// <summary>
    /// Information about an event subscription.
    /// </summary>
    public class EventSubscription
    {
        public string SourceId { get; set; } = string.Empty;
        public string EventName { get; set; } = string.Empty;
        public WeakReference Handler { get; set; } = new WeakReference(null);
        public Type OwnerType { get; set; } = typeof(object);
        public DateTime SubscribedAt { get; set; }
    }

    /// <summary>
    /// Categories for organizing tracked resources.
    /// </summary>
    public enum ResourceCategory
    {
        General,
        Timer,
        Collection,
        Service,
        ViewModel,
        UserControl,
        Database,
        UI,
        Cache,
        Network
    }

    /// <summary>
    /// Comprehensive memory leak report.
    /// </summary>
    public class MemoryLeakReport
    {
        public DateTime GeneratedAt { get; set; }
        public long TotalResourcesTracked { get; set; }
        public long TotalResourcesDisposed { get; set; }
        public long TotalEventSubscriptions { get; set; }
        public long TotalEventUnsubscriptions { get; set; }
        public long MemoryLeaksDetected { get; set; }
        public long AutomaticCleanups { get; set; }
        public double CurrentMemoryUsageMB { get; set; }
        public int AliveResources { get; set; }
        public int DeadResources { get; set; }
        public int ActiveEventSubscriptions { get; set; }
        public int DeadEventSubscriptions { get; set; }
        public Dictionary<ResourceCategory, int> ResourcesByCategory { get; set; } = new Dictionary<ResourceCategory, int>();
        public Dictionary<string, int> ResourcesByOwnerType { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Gets the resource disposal efficiency percentage.
        /// </summary>
        public double DisposalEfficiency => TotalResourcesTracked > 0 ? (double)TotalResourcesDisposed / TotalResourcesTracked * 100 : 0;

        /// <summary>
        /// Gets the event cleanup efficiency percentage.
        /// </summary>
        public double EventCleanupEfficiency => TotalEventSubscriptions > 0 ? (double)TotalEventUnsubscriptions / TotalEventSubscriptions * 100 : 0;

        /// <summary>
        /// Indicates if there are potential memory leaks.
        /// </summary>
        public bool HasPotentialLeaks => DeadResources > 0 || DeadEventSubscriptions > 0 || DisposalEfficiency < 90;
    }

    #endregion
}
