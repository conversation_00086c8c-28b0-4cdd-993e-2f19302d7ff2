using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace UFU2.Models
{
    /// <summary>
    /// Represents a payment year with selection state for G12 and BIS tabs.
    /// Used in PaymentYearsSelectionDialog for tab-specific year selections.
    /// Follows UFU2 MVVM patterns with INotifyPropertyChanged implementation.
    /// </summary>
    public class PaymentYearModel : INotifyPropertyChanged
    {
        #region Private Fields
        private int _year;
        private bool _isSelectedG12;
        private bool _isSelectedBIS;
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the PaymentYearModel class.
        /// All years are selected by default for both G12 and BIS tabs.
        /// </summary>
        /// <param name="year">The year value (e.g., 2020, 2021, 2022)</param>
        public PaymentYearModel(int year)
        {
            _year = year;
            _isSelectedG12 = true;  // Default to selected for G12
            _isSelectedBIS = true;  // Default to selected for BIS
        }
        #endregion

        #region Properties
        /// <summary>
        /// Gets or sets the year value.
        /// </summary>
        public int Year
        {
            get => _year;
            set => SetProperty(ref _year, value);
        }

        /// <summary>
        /// Gets or sets whether this year is selected in the G12 tab.
        /// </summary>
        public bool IsSelectedG12
        {
            get => _isSelectedG12;
            set => SetProperty(ref _isSelectedG12, value);
        }

        /// <summary>
        /// Gets or sets whether this year is selected in the BIS tab.
        /// </summary>
        public bool IsSelectedBIS
        {
            get => _isSelectedBIS;
            set => SetProperty(ref _isSelectedBIS, value);
        }

        /// <summary>
        /// Gets the year as a string for display purposes.
        /// </summary>
        public string YearText => _year.ToString();

        /// <summary>
        /// Gets or sets the current selection state based on the active tab.
        /// This property is used for binding to CheckBox IsChecked.
        /// </summary>
        public bool IsCurrentTabSelected
        {
            get => CurrentTab == "G12" ? IsSelectedG12 : IsSelectedBIS;
            set
            {
                if (CurrentTab == "G12")
                    IsSelectedG12 = value;
                else
                    IsSelectedBIS = value;
            }
        }

        /// <summary>
        /// Gets or sets the current active tab (G12 or BIS).
        /// This is used to determine which selection property to use.
        /// </summary>
        public string CurrentTab { get; set; } = "G12";
        #endregion

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Sets the property value and raises PropertyChanged event if the value has changed.
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">The new value</param>
        /// <param name="propertyName">The name of the property (automatically provided)</param>
        /// <returns>True if the property value was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Raises the PropertyChanged event for the specified property.
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        public virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion

        #region Methods
        /// <summary>
        /// Clears both G12 and BIS selections for this year.
        /// </summary>
        public void ClearSelections()
        {
            IsSelectedG12 = false;
            IsSelectedBIS = false;
        }

        /// <summary>
        /// Returns a string representation of the payment year.
        /// </summary>
        /// <returns>String representation showing year and selection states</returns>
        public override string ToString()
        {
            return $"Year: {Year}, G12: {IsSelectedG12}, BIS: {IsSelectedBIS}";
        }
        #endregion
    }
}
