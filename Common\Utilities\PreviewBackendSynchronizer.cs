using System;
using System.Windows;
using UFU2.Services;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Synchronizes transformations between the 500x320 PreviewContainer and the scaled backend container.
    /// Ensures pixel-perfect accuracy for WYSIWYG functionality by maintaining mathematical precision
    /// throughout the transformation pipeline.
    /// Follows UFU2 patterns with comprehensive error handling and logging.
    /// </summary>
    public class PreviewBackendSynchronizer
    {
        #region Private Fields

        private readonly Size _originalImageSize;
        private readonly Size _previewSize;
        private readonly BackendContainerResult _backendContainer;
        private readonly double _previewBaseScale;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the PreviewBackendSynchronizer.
        /// </summary>
        /// <param name="originalImageSize">Size of the original image in pixels</param>
        public PreviewBackendSynchronizer(Size originalImageSize)
        {
            try
            {
                _originalImageSize = originalImageSize;
                _previewSize = new Size(500, 320);
                
                // Calculate backend container
                _backendContainer = BackendContainerCalculator.CalculateOptimalBackendContainer(originalImageSize);
                
                if (!_backendContainer.IsValid)
                {
                    LoggingService.LogWarning("Backend container calculation failed, using fallback values", "PreviewBackendSynchronizer");
                }

                // Calculate base scale for fitting image in preview
                double scaleX = _previewSize.Width / _originalImageSize.Width;
                double scaleY = _previewSize.Height / _originalImageSize.Height;
                _previewBaseScale = Math.Min(scaleX, scaleY);

                LoggingService.LogDebug($"PreviewBackendSynchronizer initialized - Image: {originalImageSize.Width}x{originalImageSize.Height}, " +
                    $"Backend: {_backendContainer.BackendSize.Width}x{_backendContainer.BackendSize.Height}, " +
                    $"BaseScale: {_previewBaseScale:F6}", "PreviewBackendSynchronizer");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing PreviewBackendSynchronizer: {ex.Message}", "PreviewBackendSynchronizer");
                throw;
            }
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the backend container information
        /// </summary>
        public BackendContainerResult BackendContainer => _backendContainer;

        /// <summary>
        /// Gets the original image size
        /// </summary>
        public Size OriginalImageSize => _originalImageSize;

        /// <summary>
        /// Gets the preview container size
        /// </summary>
        public Size PreviewSize => _previewSize;

        /// <summary>
        /// Gets the base scale factor for fitting the image in preview
        /// </summary>
        public double PreviewBaseScale => _previewBaseScale;

        #endregion

        #region Public Methods

        /// <summary>
        /// Synchronizes all transformations from preview to backend coordinates.
        /// Maintains pixel-perfect accuracy for zoom, rotation, drag, and crop operations.
        /// </summary>
        /// <param name="previewZoomPercentage">Zoom percentage from preview (25-400%)</param>
        /// <param name="rotationAngle">Rotation angle in degrees</param>
        /// <param name="previewDragOffset">Drag offset in preview coordinates</param>
        /// <param name="previewCropRect">Crop rectangle in preview coordinates</param>
        /// <returns>Backend transformation state with synchronized values</returns>
        public BackendTransformState SynchronizeTransformations(
            double previewZoomPercentage,
            double rotationAngle,
            Point previewDragOffset,
            Rect previewCropRect)
        {
            try
            {
                LoggingService.LogDebug($"Synchronizing transformations - Zoom: {previewZoomPercentage}%, " +
                    $"Rotation: {rotationAngle}°, Drag: ({previewDragOffset.X:F2}, {previewDragOffset.Y:F2}), " +
                    $"Crop: {previewCropRect}", "PreviewBackendSynchronizer");

                // Calculate preview image display properties
                var previewImageProps = CalculatePreviewImageProperties(previewZoomPercentage);

                // Calculate backend zoom scale
                double backendZoomScale = CalculateBackendZoomScale(previewZoomPercentage);

                // Map drag offset to backend coordinates
                var backendDragOffset = MapDragOffsetToBackend(previewDragOffset);

                // Map crop rectangle to backend coordinates
                var backendCropRect = MapCropRectangleToBackend(previewCropRect);

                // Calculate backend image display size
                var backendImageDisplaySize = new Size(
                    _originalImageSize.Width * backendZoomScale,
                    _originalImageSize.Height * backendZoomScale
                );

                var result = new BackendTransformState
                {
                    ZoomScale = backendZoomScale,
                    RotationAngle = rotationAngle, // Rotation is identical in both spaces
                    DragOffset = backendDragOffset,
                    CropRectangle = backendCropRect,
                    ImageDisplaySize = backendImageDisplaySize,
                    PreviewImageProperties = previewImageProps
                };

                LoggingService.LogDebug($"Backend transformation state: Zoom: {backendZoomScale:F6}, " +
                    $"Drag: ({backendDragOffset.X:F2}, {backendDragOffset.Y:F2}), " +
                    $"Crop: {backendCropRect}", "PreviewBackendSynchronizer");

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error synchronizing transformations: {ex.Message}", "PreviewBackendSynchronizer");
                throw;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Calculates preview image display properties for the given zoom percentage
        /// </summary>
        private PreviewImageProperties CalculatePreviewImageProperties(double zoomPercentage)
        {
            try
            {
                double zoomScale = zoomPercentage / 100.0;
                double displayScale = _previewBaseScale * zoomScale;

                var displaySize = new Size(
                    _originalImageSize.Width * displayScale,
                    _originalImageSize.Height * displayScale
                );

                // Calculate centering offset in preview
                var centerOffset = new Point(
                    (_previewSize.Width - displaySize.Width) / 2,
                    (_previewSize.Height - displaySize.Height) / 2
                );

                return new PreviewImageProperties
                {
                    DisplayScale = displayScale,
                    DisplaySize = displaySize,
                    CenterOffset = centerOffset,
                    ZoomScale = zoomScale
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating preview image properties: {ex.Message}", "PreviewBackendSynchronizer");
                throw;
            }
        }

        /// <summary>
        /// Calculates the backend zoom scale from preview zoom percentage
        /// </summary>
        private double CalculateBackendZoomScale(double previewZoomPercentage)
        {
            try
            {
                double previewZoomScale = previewZoomPercentage / 100.0;
                
                // Backend scale is the preview base scale multiplied by zoom and backend scale factor
                double backendBaseScale = _previewBaseScale * _backendContainer.ScaleFactor;
                double backendZoomScale = backendBaseScale * previewZoomScale;

                return backendZoomScale;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating backend zoom scale: {ex.Message}", "PreviewBackendSynchronizer");
                return _previewBaseScale;
            }
        }

        /// <summary>
        /// Maps drag offset from preview coordinates to backend coordinates
        /// </summary>
        private Point MapDragOffsetToBackend(Point previewDragOffset)
        {
            try
            {
                return new Point(
                    previewDragOffset.X * _backendContainer.ScaleFactor,
                    previewDragOffset.Y * _backendContainer.ScaleFactor
                );
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error mapping drag offset to backend: {ex.Message}", "PreviewBackendSynchronizer");
                return new Point(0, 0);
            }
        }

        /// <summary>
        /// Maps crop rectangle from preview coordinates to backend coordinates
        /// </summary>
        private Rect MapCropRectangleToBackend(Rect previewCropRect)
        {
            try
            {
                return new Rect(
                    previewCropRect.X * _backendContainer.ScaleFactor,
                    previewCropRect.Y * _backendContainer.ScaleFactor,
                    previewCropRect.Width * _backendContainer.ScaleFactor,
                    previewCropRect.Height * _backendContainer.ScaleFactor
                );
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error mapping crop rectangle to backend: {ex.Message}", "PreviewBackendSynchronizer");
                return previewCropRect;
            }
        }

        #endregion
    }

    /// <summary>
    /// Represents the transformation state in backend coordinates
    /// </summary>
    public class BackendTransformState
    {
        /// <summary>
        /// Zoom scale in backend coordinates
        /// </summary>
        public double ZoomScale { get; set; }

        /// <summary>
        /// Rotation angle in degrees (identical in both spaces)
        /// </summary>
        public double RotationAngle { get; set; }

        /// <summary>
        /// Drag offset in backend coordinates
        /// </summary>
        public Point DragOffset { get; set; }

        /// <summary>
        /// Crop rectangle in backend coordinates
        /// </summary>
        public Rect CropRectangle { get; set; }

        /// <summary>
        /// Image display size in backend coordinates
        /// </summary>
        public Size ImageDisplaySize { get; set; }

        /// <summary>
        /// Preview image properties for reference
        /// </summary>
        public PreviewImageProperties PreviewImageProperties { get; set; }
    }

    /// <summary>
    /// Properties of the image as displayed in the preview container
    /// </summary>
    public class PreviewImageProperties
    {
        /// <summary>
        /// Display scale factor (base scale × zoom scale)
        /// </summary>
        public double DisplayScale { get; set; }

        /// <summary>
        /// Displayed image size in preview coordinates
        /// </summary>
        public Size DisplaySize { get; set; }

        /// <summary>
        /// Centering offset in preview coordinates
        /// </summary>
        public Point CenterOffset { get; set; }

        /// <summary>
        /// Zoom scale factor (zoom percentage / 100)
        /// </summary>
        public double ZoomScale { get; set; }
    }
}
