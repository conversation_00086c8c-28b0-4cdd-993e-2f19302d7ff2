=== UFU2 Application Session Started at 2025-08-10 00:35:25 ===
[2025-08-10 00:35:25.140]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-10 00:35:25.146]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-10 00:35:25.149]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-10 00:35:25.152]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-10 00:35:25.162]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-10 00:35:25.165]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-10 00:35:25.169]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-10 00:35:25.174]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-10 00:35:25.178]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-10 00:35:25.181]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-10 00:35:25.185]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-10 00:35:25.188]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-10 00:35:25.191]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-10 00:35:25.194]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-10 00:35:25.197]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-10 00:35:25.199]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-10 00:35:25.202]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-10 00:35:25.205]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-10 00:35:25.220]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-10 00:35:25.226]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-10 00:35:25.231]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-10 00:35:25.234]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-10 00:35:25.236]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-10 00:35:25.240]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-10 00:35:25.243]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-10 00:35:25.246]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-10 00:35:25.249]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-10 00:35:25.253]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-10 00:35:25.255]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-10 00:35:25.259]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-10 00:35:25.261]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-10 00:35:25.265]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-10 00:35:25.268]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-10 00:35:25.271]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-10 00:35:25.274]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-10 00:35:25.277]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-10 00:35:25.280]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-10 00:35:25.283]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-10 00:35:25.295]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 103.85MB working set
[2025-08-10 00:35:25.299]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-10 00:35:25.301]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-10 00:35:25.305]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-10 00:35:25.308]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-10 00:35:25.311]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-10 00:35:25.316]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-10 00:35:25.337]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-10 00:35:25.341]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-10 00:35:25.344]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-10 00:35:25.572]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-10 00:35:25.575]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-10 00:35:25.579]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-10 00:35:25.581]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-10 00:35:25.588]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638903793255868709 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-10 00:35:25.591]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-10 00:35:25.595]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 00:35:25.598]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-10 00:35:25.608]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-10 00:35:25.613]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-10 00:35:25.617]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-10 00:35:25.621]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-10 00:35:25.625]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-10 00:35:25.628]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-10 00:35:25.632]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-10 00:35:25.634]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-10 00:35:25.637]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-10 00:35:25.641]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-10 00:35:25.645]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-10 00:35:25.648]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-10 00:35:25.651]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-10 00:35:25.654]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-10 00:35:25.657]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-10 00:35:25.661]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-10 00:35:25.664]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-10 00:35:25.668]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-10 00:35:25.671]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-10 00:35:25.674]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-10 00:35:25.677]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-10 00:35:25.680]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-10 00:35:25.683]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-10 00:35:25.687]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-10 00:35:25.691]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-10 00:35:25.694]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-10 00:35:25.697]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-10 00:35:25.700]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-10 00:35:25.703]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-10 00:35:25.706]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-10 00:35:25.710]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-10 00:35:25.713]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-10 00:35:25.717]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-10 00:35:25.720]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-10 00:35:25.724]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-10 00:35:25.727]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-10 00:35:25.730]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-10 00:35:25.734]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-10 00:35:25.737]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-10 00:35:25.742]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-10 00:35:25.745]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-10 00:35:25.748]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-10 00:35:25.751]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-10 00:35:25.754]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-10 00:35:25.757]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-10 00:35:25.761]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-10 00:35:25.765]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-10 00:35:25.768]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-10 00:35:25.772]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-10 00:35:25.775]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-10 00:35:25.778]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-10 00:35:25.783]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-10 00:35:25.786]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-10 00:35:25.790]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-10 00:35:25.795]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-10 00:35:25.798]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-10 00:35:25.801]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-10 00:35:25.805]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-10 00:35:25.812]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-10 00:35:25.816]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-10 00:35:25.820]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-10 00:35:25.826]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 511ms
[2025-08-10 00:35:25.830]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-10 00:35:25.836]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-10 00:35:25.852]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-10 00:35:25.856]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-10 00:35:25.860]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-10 00:35:25.863]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-10 00:35:25.868]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-10 00:35:25.873]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-10 00:35:25.877]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:35:25.881]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-10 00:35:25.885]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:35:25.888]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-10 00:35:25.893]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:35:25.898]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-10 00:35:25.901]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-10 00:35:25.906]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:35:25.953]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:35:25.961]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:35:25.965]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:35:25.966]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:35:25.966]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:35:25.966]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:35:25.969]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:35:25.973]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:35:25.976]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:35:25.979]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-10 00:35:25.982]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:35:25.993]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:35:25.999]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:35:26.003]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:35:26.006]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:35:26.009]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:35:26.013]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:35:26.016]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:35:26.020]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:35:26.029]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-10 00:35:26.034]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 00:35:26.039]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 00:35:26.045]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.UFU2_Schema.sql
[2025-08-10 00:35:26.050]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.UFU2_Schema.sql (19126 characters)
[2025-08-10 00:35:26.053]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 00:35:26.057]  	[DEBUG]		[DatabaseMigrationService]	Executing 55 SQL statements
[2025-08-10 00:35:26.062]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/55 statements
[2025-08-10 00:35:26.067]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/55 statements
[2025-08-10 00:35:26.071]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/55 statements
[2025-08-10 00:35:26.076]  	[DEBUG]		[DatabaseMigrationService]	Executed 40/55 statements
[2025-08-10 00:35:26.080]  	[DEBUG]		[DatabaseMigrationService]	Executed 50/55 statements
[2025-08-10 00:35:26.103]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-10 00:35:26.122]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (55 statements executed)
[2025-08-10 00:35:26.126]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 00:35:26.132]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:35:26.136]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:26.140]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 00:35:26.144]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 00:35:26.148]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 00:35:26.151]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 00:35:26.155]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 00:35:26.158]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 00:35:26.163]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 00:35:26.167]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 00:35:26.170]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 00:35:26.174]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 00:35:26.181]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 00:35:26.185]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 00:35:26.189]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 00:35:26.194]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 00:35:26.198]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 00:35:26.201]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 00:35:26.205]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 00:35:26.209]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 00:35:26.213]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 00:35:26.216]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 00:35:26.220]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 00:35:26.227]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:35:26.231]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:35:26.235]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 00:35:26.243]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 00:35:26.248]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 00:35:26.259]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 00:35:26.265]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:35:26.273]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:35:26.280]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:26.285]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-10 00:35:26.299]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:35:26.307]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:26.317]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:35:26.321]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:35:26.325]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:35:26.329]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:35:26.333]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:35:26.337]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:35:26.341]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:35:26.345]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:35:26.351]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-10 00:35:26.354]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 00:35:26.358]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 00:35:26.362]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.APP_Schema.sql
[2025-08-10 00:35:26.366]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.APP_Schema.sql (9406 characters)
[2025-08-10 00:35:26.372]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 00:35:26.377]  	[DEBUG]		[DatabaseMigrationService]	Executing 30 SQL statements
[2025-08-10 00:35:26.383]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/30 statements
[2025-08-10 00:35:26.417]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/30 statements
[2025-08-10 00:35:26.423]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/30 statements
[2025-08-10 00:35:26.428]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-10 00:35:26.444]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (30 statements executed)
[2025-08-10 00:35:26.449]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 00:35:26.453]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:35:26.457]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:26.462]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-10 00:35:26.466]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-10 00:35:26.470]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-10 00:35:26.475]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-10 00:35:26.483]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-10 00:35:26.490]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-10 00:35:26.496]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-10 00:35:26.546]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-10 00:35:26.562]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-10 00:35:26.567]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-10 00:35:26.591]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-10 00:35:26.598]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:35:26.605]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:35:26.613]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-10 00:35:26.620]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-10 00:35:26.625]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-10 00:35:26.630]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-10 00:35:26.635]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:35:26.639]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:35:26.643]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:26.648]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-10 00:35:26.652]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:35:26.656]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:26.661]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:35:26.665]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:35:26.670]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:35:26.674]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:35:26.678]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:35:26.682]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:35:26.686]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:35:26.699]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:35:26.705]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-10 00:35:26.709]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 00:35:26.713]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 00:35:26.719]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.Archive_Schema.sql
[2025-08-10 00:35:26.723]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.Archive_Schema.sql (7159 characters)
[2025-08-10 00:35:26.728]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 00:35:26.732]  	[DEBUG]		[DatabaseMigrationService]	Executing 16 SQL statements
[2025-08-10 00:35:26.737]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/16 statements
[2025-08-10 00:35:26.742]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-10 00:35:26.760]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (16 statements executed)
[2025-08-10 00:35:26.765]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 00:35:26.769]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:35:26.773]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:26.778]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-10 00:35:26.782]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-10 00:35:26.786]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-10 00:35:26.790]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-10 00:35:26.794]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-10 00:35:26.798]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-10 00:35:26.802]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-10 00:35:26.806]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-10 00:35:26.810]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-10 00:35:26.814]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:35:26.817]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:35:26.821]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-10 00:35:26.826]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-10 00:35:26.830]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-10 00:35:26.834]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-10 00:35:26.837]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:35:26.841]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:35:26.845]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:26.849]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:35:26.852]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:26.856]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 00:35:26.860]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 00:35:26.863]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 00:35:26.867]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 00:35:26.871]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 00:35:26.875]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 00:35:26.879]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 00:35:26.883]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 00:35:26.887]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 00:35:26.891]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 00:35:26.895]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 00:35:26.900]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 00:35:26.904]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 00:35:26.908]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 00:35:26.912]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 00:35:26.917]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 00:35:26.921]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 00:35:26.925]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 00:35:26.930]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 00:35:26.934]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 00:35:26.938]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 00:35:26.942]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:35:26.946]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:35:26.952]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 00:35:26.959]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 00:35:26.963]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 00:35:26.968]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 00:35:26.972]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:35:26.976]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-10 00:35:26.981]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-10 00:35:26.986]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-10 00:35:26.990]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-10 00:35:26.996]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-10 00:35:27.001]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-10 00:35:27.007]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-10 00:35:27.013]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-10 00:35:27.023]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-10 00:35:27.034]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-10 00:35:27.039]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-10 00:35:27.044]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-10 00:35:27.052]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-10 00:35:27.056]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-10 00:35:27.061]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-10 00:35:27.065]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-10 00:35:27.069]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-10 00:35:27.073]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-10 00:35:27.078]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-10 00:35:27.082]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-10 00:35:27.086]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-10 00:35:27.091]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-10 00:35:27.095]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:27.100]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-10 00:35:27.104]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-10 00:35:27.108]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-10 00:35:27.112]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-10 00:35:27.117]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-10 00:35:27.121]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-10 00:35:27.137]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-10 00:35:27.142]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-10 00:35:27.147]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-10 00:35:27.151]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-10 00:35:27.155]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-10 00:35:27.160]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-10 00:35:27.165]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-10 00:35:27.170]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:27.174]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-10 00:35:27.179]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:27.185]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:27.189]  	[INFO]		[ServiceLocator]	Seeding activity type data from embedded resource
[2025-08-10 00:35:27.196]  	[DEBUG]		[ActivityTypeBaseService]	Starting import from embedded JSON resource
[2025-08-10 00:35:27.342]  	[INFO]		[ActivityTypeBaseService]	Parsed 1028 activity types from JSON
[2025-08-10 00:35:27.348]  	[INFO]		[ActivityTypeBaseService]	Starting import of 1028 activity types in 3 batches
[2025-08-10 00:35:27.353]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:27.519]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 1/3 (500 records, 500 total imported, 0 skipped)
[2025-08-10 00:35:27.569]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 2/3 (500 records, 1000 total imported, 0 skipped)
[2025-08-10 00:35:27.577]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 3/3 (28 records, 1028 total imported, 0 skipped)
[2025-08-10 00:35:27.589]  	[INFO]		[ActivityTypeBaseService]	Successfully imported 1028 activity types
[2025-08-10 00:35:27.593]  	[INFO]		[ActivityTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-10 00:35:27.601]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-10 00:35:27.749]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 00:35:27.812]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:35:27.817]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-10 00:35:27.821]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-10 00:35:27.863]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-10 00:35:27.870]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:35:27.874]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-10 00:35:27.882]  	[INFO]		[ServiceLocator]	Successfully imported 1028 activity types
[2025-08-10 00:35:27.888]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:27.895]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-10 00:35:27.899]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:27.904]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:27.909]  	[INFO]		[ServiceLocator]	Seeding craft type data from embedded resource
[2025-08-10 00:35:27.914]  	[DEBUG]		[CraftTypeBaseService]	Starting import from embedded JSON resource
[2025-08-10 00:35:27.953]  	[INFO]		[CraftTypeBaseService]	Found 337 craft types to import
[2025-08-10 00:35:27.956]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.030]  	[INFO]		[CraftTypeBaseService]	Successfully imported 337 craft types
[2025-08-10 00:35:28.034]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.038]  	[INFO]		[CraftTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-10 00:35:28.042]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-10 00:35:28.053]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-10 00:35:28.059]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:35:28.064]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-10 00:35:28.068]  	[INFO]		[ServiceLocator]	Successfully imported 337 craft types
[2025-08-10 00:35:28.072]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-10 00:35:28.078]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-10 00:35:28.102]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-10 00:35:28.108]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:28.122]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 00:35:28.126]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 00:35:28.132]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:28.143]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 00:35:28.147]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 00:35:28.152]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-10 00:35:28.156]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-10 00:35:28.162]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 00:35:28.166]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 00:35:28.181]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-10 00:35:28.194]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:35:28.198]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-10 00:35:28.203]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-10 00:35:28.211]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-10 00:35:28.215]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:28.222]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-10 00:35:28.234]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.00 MB MB size
[2025-08-10 00:35:28.238]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-10 00:35:28.244]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-10 00:35:28.250]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-10 00:35:28.254]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-10 00:35:28.260]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-10 00:35:28.264]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-10 00:35:28.268]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-10 00:35:28.272]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-10 00:35:28.276]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-10 00:35:28.280]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-10 00:35:28.285]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-10 00:35:28.289]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-10 00:35:28.299]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-10 00:35:28.303]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-10 00:35:28.307]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-10 00:35:28.311]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-10 00:35:28.317]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-10 00:35:28.323]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-10 00:35:28.329]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.353]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-10 00:35:28.357]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.374]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.383]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-10 00:35:28.387]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.391]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.395]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-10 00:35:28.399]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.402]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.407]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-10 00:35:28.412]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.417]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.422]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-10 00:35:28.427]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.432]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.437]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-10 00:35:28.442]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.447]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.452]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-10 00:35:28.456]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.468]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-10 00:35:28.483]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 160ms
[2025-08-10 00:35:28.491]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-10 00:35:28.503]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-10 00:35:28.507]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 16ms
[2025-08-10 00:35:28.511]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-10 00:35:28.517]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-10 00:35:28.521]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 11ms
[2025-08-10 00:35:28.526]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-10 00:35:28.531]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:28.536]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-10 00:35:28.541]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:28.546]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-10 00:35:28.550]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:28.555]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-10 00:35:28.559]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:35:28.563]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-10 00:35:28.566]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-10 00:35:28.570]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 45ms
[2025-08-10 00:35:28.575]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-10 00:35:28.580]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.586]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-10 00:35:28.590]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.595]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.600]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-10 00:35:28.604]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.608]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.612]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-10 00:35:28.617]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.621]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.625]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-10 00:35:28.629]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.633]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.638]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-10 00:35:28.641]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.645]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.649]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-10 00:35:28.653]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.657]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.661]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-10 00:35:28.665]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.669]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.673]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-10 00:35:28.677]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.680]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.684]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-10 00:35:28.688]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.692]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:35:28.696]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-10 00:35:28.700]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:35:28.704]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-10 00:35:28.708]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 134ms
[2025-08-10 00:35:28.713]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-10 00:35:28.717]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-10 00:35:28.720]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-10 00:35:28.724]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-10 00:35:28.728]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-10 00:35:28.732]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-10 00:35:28.735]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-10 00:35:28.739]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-10 00:35:28.743]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-10 00:35:28.747]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-10 00:35:28.750]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-10 00:35:28.754]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-10 00:35:28.758]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-10 00:35:28.762]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-10 00:35:28.766]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-10 00:35:28.769]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-10 00:35:28.773]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-10 00:35:28.779]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 3,333%
[2025-08-10 00:35:28.784]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 6,667%
[2025-08-10 00:35:28.788]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 10,000%
[2025-08-10 00:35:28.792]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 0%
[2025-08-10 00:35:28.796]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 00:35:28.800]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 00:35:28.803]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 00:35:28.807]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 00:35:28.808]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-10 00:35:28.811]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 00:35:28.816]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-10 00:35:28.819]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 00:35:28.823]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-10 00:35:28.826]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 00:35:28.836]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 00:35:28.840]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 00:35:28.844]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 00:35:28.848]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 00:35:28.852]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 00:35:28.856]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 00:35:28.861]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 00:35:28.865]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 00:35:28.869]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 00:35:28.873]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 00:35:28.877]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 00:35:28.881]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 00:35:28.885]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 00:35:28.889]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 00:35:28.889]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-10 00:35:28.893]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 00:35:28.897]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-10 00:35:28.901]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 00:35:28.905]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 15ms
[2025-08-10 00:35:28.908]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 00:35:28.916]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 00:35:28.920]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 00:35:28.924]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 00:35:28.928]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 00:35:28.932]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 00:35:28.936]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 00:35:28.940]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 00:35:28.944]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 00:35:28.948]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 00:35:28.952]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 00:35:28.956]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 00:35:28.960]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 00:35:28.963]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 00:35:28.967]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 00:35:28.971]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 00:35:28.974]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 00:35:28.978]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 00:35:28.982]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 00:35:28.985]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 00:35:28.989]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 00:35:28.992]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 00:35:28.996]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 00:35:29.000]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 00:35:29.003]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 00:35:29.007]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 00:35:29.011]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 00:35:29.015]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 00:35:29.021]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 00:35:29.025]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 00:35:29.029]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 00:35:29.032]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 00:35:29.036]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 00:35:29.040]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 00:35:29.043]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 00:35:29.047]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 00:35:29.051]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 00:35:29.055]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 00:35:29.059]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 00:35:29.062]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 00:35:29.066]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 00:35:29.070]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 00:35:29.074]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 00:35:29.077]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 00:35:29.082]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 00:35:29.085]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 00:35:29.090]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 00:35:29.093]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 00:35:29.097]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 00:35:29.102]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 00:35:29.106]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 00:35:29.109]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 00:35:29.114]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 00:35:29.118]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 00:35:29.122]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 00:35:29.126]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 00:35:29.130]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 00:35:29.134]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 00:35:29.137]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 00:35:29.141]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 00:35:29.146]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 00:35:29.150]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 00:35:29.154]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 00:35:29.158]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 00:35:29.162]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 00:35:29.166]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 00:35:29.170]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 00:35:29.174]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 00:35:29.179]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 00:35:29.183]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 00:35:29.190]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 00:35:29.195]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 00:35:29.198]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 00:35:29.202]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 00:35:29.206]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 00:35:29.210]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 00:35:29.214]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 00:35:29.217]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 00:35:29.221]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 00:35:29.225]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 00:35:29.228]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 00:35:29.232]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 00:35:29.235]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 00:35:29.239]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 00:35:29.243]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 00:35:29.246]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 00:35:29.250]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 00:35:29.253]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 00:35:29.257]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 00:35:29.261]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 00:35:29.264]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 00:35:29.268]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 00:35:29.271]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 00:35:29.275]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 00:35:29.278]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 00:35:29.282]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 00:35:29.286]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 00:35:29.289]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 00:35:29.293]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 00:35:29.296]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 00:35:29.300]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 00:35:29.304]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 00:35:29.308]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 00:35:29.312]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 00:35:29.315]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 00:35:29.319]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 00:35:29.323]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 00:35:29.328]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 00:35:29.335]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 00:35:29.341]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 00:35:29.348]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 00:35:29.355]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 00:35:29.362]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 00:35:29.366]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 00:35:29.371]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 00:35:29.375]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 00:35:29.380]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 00:35:29.384]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 00:35:29.388]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 00:35:29.393]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 00:35:29.396]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 00:35:29.400]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 00:35:29.404]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 00:35:29.409]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 00:35:29.413]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 00:35:29.417]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 00:35:29.421]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 00:35:29.424]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 00:35:29.428]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 00:35:29.432]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 00:35:29.436]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 00:35:29.440]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 00:35:29.443]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 00:35:29.448]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 00:35:29.451]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 00:35:29.455]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 00:35:29.459]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 00:35:29.463]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 00:35:29.467]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 00:35:29.471]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 00:35:29.474]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 00:35:29.483]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 00:35:29.486]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 00:35:29.490]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 00:35:29.495]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 00:35:29.502]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 00:35:29.663]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 00:35:29.681]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 00:35:29.689]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 00:35:29.693]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 00:35:29.697]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 00:35:29.702]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 00:35:29.706]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 00:35:29.710]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 00:35:29.714]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 00:35:29.718]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 00:35:29.723]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 00:35:29.728]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 00:35:29.737]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 00:35:29.752]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 00:35:29.772]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 00:35:29.783]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 00:35:29.799]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 00:35:29.822]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 00:35:29.845]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 00:35:29.861]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 00:35:29.874]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 00:35:29.885]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 00:35:29.902]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 00:35:29.915]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 00:35:29.931]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 00:35:29.937]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 00:35:29.950]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 00:35:29.957]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 00:35:29.969]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 00:35:29.974]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 00:35:29.980]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 00:35:29.984]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 00:35:29.990]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 00:35:30.000]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 00:35:30.004]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 00:35:30.013]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 00:35:30.017]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 00:35:30.022]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 00:35:30.029]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 00:35:30.034]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 00:35:30.038]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 00:35:30.046]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 00:35:30.052]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 00:35:30.056]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 00:35:30.060]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 00:35:30.064]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 00:35:30.068]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 00:35:30.072]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 00:35:30.078]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 00:35:30.082]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 00:35:30.085]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 00:35:30.089]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 00:35:30.093]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 00:35:30.097]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 00:35:30.101]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 00:35:30.104]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 00:35:30.108]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 00:35:30.112]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 00:35:30.116]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 00:35:30.119]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 00:35:30.123]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 00:35:30.127]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 00:35:30.131]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 00:35:30.134]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 00:35:30.138]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 00:35:30.142]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 00:35:30.146]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 00:35:30.149]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 00:35:30.153]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 00:35:30.157]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 00:35:30.161]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 00:35:30.165]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 00:35:30.169]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 00:35:30.173]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 00:35:30.176]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 00:35:30.180]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 00:35:30.183]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 00:35:30.187]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 00:35:30.191]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 00:35:30.194]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 00:35:30.198]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 00:35:30.201]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 00:35:30.205]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 00:35:30.208]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 00:35:30.212]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 00:35:30.215]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 00:35:30.219]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 00:35:30.222]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 00:35:30.226]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 00:35:30.229]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 00:35:30.233]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 00:35:30.239]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 00:35:30.243]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 00:35:30.248]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 00:35:30.252]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 00:35:30.256]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 00:35:30.264]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 00:35:30.269]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 00:35:30.273]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 00:35:30.276]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 00:35:30.280]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 00:35:30.284]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 00:35:30.292]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 00:35:30.296]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 00:35:30.300]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 00:35:30.304]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 00:35:30.361]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 00:35:30.386]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 00:35:30.430]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 00:35:30.448]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 00:35:30.453]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 00:35:30.474]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 00:35:30.487]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 00:35:30.491]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 00:35:30.495]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 00:35:30.498]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 00:35:30.502]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 00:35:30.506]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 00:35:30.509]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 00:35:30.515]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 00:35:30.524]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 00:35:30.531]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 00:35:30.535]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 00:35:30.539]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 00:35:30.544]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 00:35:30.547]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 00:35:30.551]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 00:35:30.555]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 00:35:30.558]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 00:35:30.562]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 00:35:30.567]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 00:35:30.572]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 00:35:30.577]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 00:35:30.582]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 00:35:30.587]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 00:35:30.590]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 00:35:30.595]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 00:35:30.598]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 00:35:30.602]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 00:35:30.606]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 00:35:30.610]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 00:35:30.614]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 00:35:30.617]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 00:35:30.621]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 00:35:30.624]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 00:35:30.629]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 00:35:30.632]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 00:35:30.636]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 00:35:30.639]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 00:35:30.643]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 00:35:30.648]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 00:35:30.652]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 00:35:30.656]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 00:35:30.659]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 00:35:30.664]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 00:35:30.668]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 00:35:30.671]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 00:35:30.675]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 00:35:30.681]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 00:35:30.684]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 00:35:30.688]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 00:35:30.692]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 00:35:30.700]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 00:35:30.707]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 00:35:30.717]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 00:35:30.731]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-10 00:35:30.747]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-10 00:35:30.788]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-10 00:35:30.918]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-10 00:35:30.923]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-10 00:35:30.927]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-10 00:35:30.931]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-10 00:35:30.940]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:35:30.945]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:30.952]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:35:30.959]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:30.966]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:35:30.978]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:35:31.119]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-10 00:35:31.126]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-10 00:35:31.344]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-10 00:35:31.349]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-10 00:35:31.354]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-10 00:35:31.421]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 00:35:31.458]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-10 00:35:31.464]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 3713.4334ms
[2025-08-10 00:35:31.469]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-10 00:35:31.473]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:31.479]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:31.483]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:31.487]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:31.491]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:31.495]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:35:41.734]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 00:35:41.928]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:41.932]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:41.935]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:41.939]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:41.943]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:41.947]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:35:42.473]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 739.629ms
[2025-08-10 00:35:42.477]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-10 00:35:43.362]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.367]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:43.370]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.374]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:43.378]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.381]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:35:43.457]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.461]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:35:43.465]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.468]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:35:43.472]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-10 00:35:43.476]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.480]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-10 00:35:43.484]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-10 00:35:43.488]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-10 00:35:43.492]  	[INFO]		[MainWindow]	Application closing
[2025-08-10 00:35:43.496]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_61825926_638903793434969283 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-10 00:35:43.500]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-10 00:35:43.504]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 00:35:43.508]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 00:35:43.511]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 00:35:43.516]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-10 00:35:43.527]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-10 00:35:43.531]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-10 00:35:43.536]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-10 00:35:43.608]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-10 00:35:43.631]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-10 00:35:43.643]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.647]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:43.652]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.656]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:43.660]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:43.664]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:35:44.507]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 990.8741ms
[2025-08-10 00:35:44.511]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 00:35:44.537]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:44.541]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:44.545]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:44.549]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:35:44.552]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:35:44.556]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:35:44.616]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-10 00:35:44.620]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-10 00:35:44.626]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-10 00:35:44.635]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-10 00:35:44.639]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:35:44.643]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-10 00:35:44.647]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-10 00:35:44.650]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:35:44.654]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:35:44.658]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:35:44.661]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_61825926_638903793434969283
[2025-08-10 00:35:44.665]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:35:44.668]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-10 00:35:44.672]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-10 00:35:44.675]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:35:44.679]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:35:44.682]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:35:44.686]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-10 00:35:44.698]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-10 00:35:44.702]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-10 00:35:44.706]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-10 00:35:44.710]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-10 00:35:44.714]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 00:35:44.717]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 00:35:44.721]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:35:44.724]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-10 00:35:44.728]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-10 00:35:44.731]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:35:44.735]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:35:44.739]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:35:44.742]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_28090709_638903793255868709
[2025-08-10 00:35:44.746]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:35:44.749]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-10 00:35:44.753]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-10 00:35:44.756]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:35:44.760]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:35:44.764]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:35:44.768]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-10 00:35:44.772]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-10 00:35:44.776]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-10 00:35:44.780]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-10 00:35:44.784]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-10 00:35:44.788]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-10 00:35:44.798]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-10 00:35:44.802]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-10 00:35:44.805]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-10 00:35:44.810]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-10 00:35:44.933]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-10 00:35:45.000]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-10 00:35:45.006]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-10 00:35:45.019]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-10 00:35:45.028]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-10 00:35:45.036]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-10 00:35:45.043]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:35:45.048]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:35:45.052]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:35:45.056]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:35:45.061]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-10 00:35:45.065]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-10 00:35:45.070]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-10 00:35:45.074]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-10 00:35:45.078]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-10 00:35:45.082]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-10 00:35:45.087]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-10 00:35:45.091]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-10 00:35:45.096]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-10 00:35:45.101]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-10 00:35:45.107]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-10 00:35:45.115]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-10 00:35:45.121]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 0.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-10 00:35:45.126]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-10 00:35:45.133]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-10 00:35:45.138]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-10 00:35:45.145]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 50.0%
[2025-08-10 00:35:45.150]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-10 00:35:45.155]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-10 00:35:45.160]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:35:45.165]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-10 00:35:45.169]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-10 00:35:45.173]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-10 00:35:45.178]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-10 00:35:45.183]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-10 00:35:45.187]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-10 00:35:45.192]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-10 00:35:45.197]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-10 00:35:45.201]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-10 00:35:45.205]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:35:45.211]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-10 00:35:45.231]  	[INFO]		[ResourceManager]	Generated memory leak report: 0 alive resources, 0 dead resources
[2025-08-10 00:35:45.244]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 1
[2025-08-10 00:35:45.248]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 1 potential leaks detected
[2025-08-10 00:35:45.253]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-10 00:35:45.259]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-10 00:35:45.264]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-10 00:35:45.270]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-10 00:35:45.275]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-10 00:35:45.280]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-10 00:35:45.285]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-10 00:35:45.296]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-10 00:35:45.304]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 2 tracked, 2 disposed, 1 cleanups
[2025-08-10 00:35:45.311]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-10 00:35:45.316]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-10 00:35:45.335]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-10 00:35:45.340]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-10 00:35:45.347]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-10 00:35:45.353]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-10 00:35:45.362]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-10 00:35:45.369]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-10 00:35:45.374]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-10 00:35:45.383]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-10 00:35:45.388]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-10 00:35:45.395]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-10 00:35:45.403]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-10 00:35:45.409]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-10 00:35:45.415]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-10 00:35:45.419]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-10 00:35:45.428]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-10 00:35:45.432]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-10 00:35:45.436]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-10 00:35:45.440]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-10 00:35:45 ===
