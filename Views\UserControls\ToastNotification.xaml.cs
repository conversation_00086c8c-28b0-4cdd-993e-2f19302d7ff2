using System;
using System.Media;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using MaterialDesignThemes.Wpf;
using UFU2.Common;
using UFU2.Services;

namespace UFU2.Views.UserControls
{
    /// <summary>
    /// Toast notification UserControl with MaterialDesign styling and Arabic RTL support
    /// </summary>
    public partial class ToastNotification : UserControl
    {
        #region Private Fields

        private DispatcherTimer? _lifeTimer;
        private DispatcherTimer? _progressTimer;
        private double _progressValue = 0;
        private int _duration;
        private Action<ToastNotification>? _onClose;
        private Storyboard? _entranceStoryboard;
        private Storyboard? _exitStoryboard;
        private string _logMessage;
        private string? _detailMessage;
        private bool _isExpanded = false;
        private string _title;
        private ToastType _toastType;

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the detail button is clicked
        /// </summary>
        public event ToastDetailEventHandler? DetailButtonClicked;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the title of the toast notification
        /// </summary>
        public string Title => _title;

        /// <summary>
        /// Gets the toast type
        /// </summary>
        public ToastType ToastType => _toastType;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ToastNotification UserControl
        /// </summary>
        /// <param name="title">The title of the notification</param>
        /// <param name="message">The message content</param>
        /// <param name="type">The type of notification</param>
        /// <param name="duration">Duration in milliseconds</param>
        /// <param name="position">Position of the toast</param>
        /// <param name="onClose">Callback when toast is closed</param>
        /// <param name="detailMessage">Optional detailed message</param>
        public ToastNotification(string title, string message, ToastType type, int duration = 3000, 
            ToastPosition position = ToastPosition.BottomRight, Action<ToastNotification>? onClose = null, 
            string? detailMessage = null)
        {
            InitializeComponent();

            _title = title;
            _duration = duration;
            _onClose = onClose;
            _logMessage = message;
            _detailMessage = detailMessage ?? message;
            _toastType = type;

            // Get the storyboards from resources
            _entranceStoryboard = (Storyboard)FindResource("EntranceStoryboard");        
            _exitStoryboard = (Storyboard)FindResource("ExitStoryboard");

            SetType(type);
            TitleText.Text = title;
            MessageText.Text = message;
            ToastProgressBar.Value = 0;
            ToastProgressBar.Maximum = 100;

            // Set up button click handlers
            CloseButton.Click += (s, e) => CloseToast();

            // Add mouse enter/leave events to pause/resume timers on hover
            MouseEnter += (s, e) => PauseTimers();
            MouseLeave += (s, e) => ResumeTimers();

            // Make the entire toast clickable to show details for error notifications  
            if (type == ToastType.Error && !string.IsNullOrEmpty(detailMessage))
            {
                MouseLeftButtonUp += (s, e) =>
                {
                    if (!_isExpanded)
                    {
                        ShowDetailMessage();
                    }
                };
                Cursor = System.Windows.Input.Cursors.Hand;
            }

            PlaySound(type);
            StartTimers();
            PlayEntranceAnimation();

            LoggingService.LogInfo($"Toast notification created: {type} - {title}", "ToastNotification");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Sets the visual style based on toast type using DynamicResource for theme support
        /// </summary>
        /// <param name="type">The toast type</param>
        private void SetType(ToastType type)
        {
            string accentResourceKey;
            PackIconKind iconKind;

            switch (type)
            {
                case ToastType.Success:
                    accentResourceKey = "SuccessBrush";
                    iconKind = PackIconKind.CheckCircle;
                    break;
                case ToastType.Info:
                    accentResourceKey = "InfoBrush";
                    iconKind = PackIconKind.InformationVariantCircle;
                    break;
                case ToastType.Warning:
                    accentResourceKey = "WarningBrush";
                    iconKind = PackIconKind.AlertCircle;
                    break;
                case ToastType.Error:
                    accentResourceKey = "ErrorBrush";
                    iconKind = PackIconKind.CloseCircle;
                    break;
                default:
                    accentResourceKey = "InfoBrush";
                    iconKind = PackIconKind.InformationVariantCircle;
                    break;
            }

            // Apply DynamicResource bindings for theme support
            try
            {
                var accentBrush = FindResource(accentResourceKey) as Brush;
                if (accentBrush != null)
                {
                    ColorBorder.Background = accentBrush;
                    ToastIcon.Foreground = accentBrush;
                    ToastProgressBar.Foreground = accentBrush;
                    TitleText.Foreground = accentBrush; // Set title text color to match toast type
                }
                else
                {
                    // Fallback to default colors if resource not found
                    LoggingService.LogWarning($"Toast accent resource '{accentResourceKey}' not found, using fallback", "ToastNotification");
                    SetFallbackColors(type);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Error setting toast colors: {ex.Message}", "ToastNotification");
                SetFallbackColors(type);
            }

            ToastIcon.Kind = iconKind;
        }

        /// <summary>
        /// Sets fallback colors when DynamicResource is not available
        /// </summary>
        /// <param name="type">The toast type</param>
        private void SetFallbackColors(ToastType type)
        {
            Color accentColor = type switch
            {
                ToastType.Success => Color.FromRgb(76, 175, 80), // Green
                ToastType.Info => Color.FromRgb(33, 150, 243), // Blue
                ToastType.Warning => Color.FromRgb(255, 152, 0), // Orange
                ToastType.Error => Color.FromRgb(244, 67, 54), // Red
                _ => Color.FromRgb(33, 150, 243) // Blue
            };

            var accentBrush = new SolidColorBrush(accentColor);
            ColorBorder.Background = accentBrush;
            ToastIcon.Foreground = accentBrush;
            ToastProgressBar.Foreground = accentBrush;
            TitleText.Foreground = accentBrush; // Set title text color to match toast type
        }

        /// <summary>
        /// Plays system sound based on toast type
        /// </summary>
        /// <param name="type">The toast type</param>
        private void PlaySound(ToastType type)
        {
            try
            {
                switch (type)
                {
                    case ToastType.Success:
                        SystemSounds.Asterisk.Play();
                        break;
                    case ToastType.Info:
                        SystemSounds.Beep.Play();
                        break;
                    case ToastType.Warning:
                        SystemSounds.Exclamation.Play();
                        break;
                    case ToastType.Error:
                        SystemSounds.Hand.Play();
                        break;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to play toast sound: {ex.Message}", "ToastNotification");
            }
        }

        /// <summary>
        /// Shows the detailed message
        /// </summary>
        private void ShowDetailMessage()
        {
            _isExpanded = true;
            PauseTimers();

            var args = new ToastDetailEventArgs(this, _detailMessage ?? _logMessage);
            DetailButtonClicked?.Invoke(this, args);
        }

        /// <summary>
        /// Starts the auto-close and progress timers
        /// </summary>
        private void StartTimers()
        {
            // Life timer for auto-close
            _lifeTimer = new DispatcherTimer(DispatcherPriority.Normal)
            {
                Interval = TimeSpan.FromMilliseconds(_duration)
            };
            _lifeTimer.Tick += (s, e) => CloseToast();
            _lifeTimer.Start();

            // Progress timer for visual countdown
            _progressTimer = new DispatcherTimer(DispatcherPriority.Render)
            {
                Interval = TimeSpan.FromMilliseconds(Math.Max(16, _duration / 100.0)) // 60 FPS or duration/100
            };
            _progressTimer.Tick += (s, e) =>
            {
                if (_progressValue < 100)
                {
                    _progressValue += 100.0 / (_duration / _progressTimer.Interval.TotalMilliseconds);
                    ToastProgressBar.Value = Math.Min(_progressValue, 100);
                }
                else
                {
                    _progressTimer?.Stop();
                }
            };
            _progressTimer.Start();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Pauses the timers to prevent the toast from automatically closing
        /// </summary>
        public void PauseTimers()
        {
            _lifeTimer?.Stop();
            _progressTimer?.Stop();
        }

        /// <summary>
        /// Resumes the timers to allow the toast to automatically close
        /// </summary>
        public void ResumeTimers()
        {
            // Only resume if we haven't reached 100% yet
            if (_progressValue < 100)
            {
                _lifeTimer?.Start();
                _progressTimer?.Start();
            }
        }

        /// <summary>
        /// Closes the toast notification with exit animation
        /// </summary>
        public void CloseToast()
        {
            try
            {
                _lifeTimer?.Stop();
                _progressTimer?.Stop();
                PlayExitAnimation();

                LoggingService.LogInfo($"Toast notification closed: {_toastType} - {_title}", "ToastNotification");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing toast: {ex.Message}", "ToastNotification");
                // Force close if animation fails
                _onClose?.Invoke(this);
            }
        }

        #endregion

        #region Animation Methods

        /// <summary>
        /// Plays the entrance animation
        /// </summary>
        private void PlayEntranceAnimation()
        {
            try
            {
                _entranceStoryboard?.Begin(this);
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to play entrance animation: {ex.Message}", "ToastNotification");
            }
        }

        /// <summary>
        /// Plays the exit animation and calls onClose when completed
        /// </summary>
        private void PlayExitAnimation()
        {
            try
            {
                if (_exitStoryboard != null)
                {
                    _exitStoryboard.Completed += (s, e) =>
                    {
                        _onClose?.Invoke(this);
                    };
                    _exitStoryboard.Begin(this);
                }
                else
                {
                    // If no animation, close immediately
                    _onClose?.Invoke(this);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to play exit animation: {ex.Message}", "ToastNotification");
                // Force close if animation fails
                _onClose?.Invoke(this);
            }
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// Disposes of resources when the toast is removed
        /// </summary>
        public void Dispose()
        {
            try
            {
                _lifeTimer?.Stop();
                _progressTimer?.Stop();
                _lifeTimer = null;
                _progressTimer = null;
                _onClose = null;
                DetailButtonClicked = null;
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Error disposing toast resources: {ex.Message}", "ToastNotification");
            }
        }

        #endregion
    }
}
