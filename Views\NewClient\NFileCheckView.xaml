<UserControl
    x:Class="UFU2.Views.NewClient.NFileCheckView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignWidth="800"
    AutomationProperties.HelpText="File check form section"
    AutomationProperties.ItemType="Form"
    AutomationProperties.Name="File Check Form"
    FlowDirection="RightToLeft"
    Focusable="True"
    IsTabStop="True"
    SnapsToDevicePixels="True"
    UseLayoutRounding="True"
    mc:Ignorable="d">



    <!--  Main Grid with 3 equal height rows  -->
    <Grid Margin="12,0,12,12">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  Row 1: Header Section  -->
        <Grid Grid.Row="0" Margin="0,0,0,8">
            <TextBlock
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Style="{DynamicResource SubtitleStyle}"
                Text="فحص الملفات المطلوبة" />
        </Grid>

        <!--  Row 2: File Check Chips Section  -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <CheckBox
                x:Name="CasChip"
                Grid.Column="0"
                Content="الضمان الاجتماعي (CAS)"
                IsChecked="{Binding ActivityManagement.CurrentFileCheckStates.CasChipChecked}"
                Style="{StaticResource FileCheckChipFirstStyle}"
                ToolTip="فحص وثيقة الضمان الاجتماعي"
                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityFileCheckVisibilityConverter}, ConverterParameter=CAS}" />

            <CheckBox
                x:Name="NifChip"
                Grid.Column="1"
                Content="رقم التعريف الجبائي (NIF)"
                IsChecked="{Binding ActivityManagement.CurrentFileCheckStates.NifChipChecked}"
                Style="{StaticResource FileCheckChipMiddleStyle}"
                ToolTip="فحص رقم التعريف الجبائي"
                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityFileCheckVisibilityConverter}, ConverterParameter=NIF}" />

            <CheckBox
                x:Name="NisChip"
                Grid.Column="2"
                Content="رقم التعريف الاحصائي (NIS)"
                IsChecked="{Binding ActivityManagement.CurrentFileCheckStates.NisChipChecked}"
                Style="{StaticResource FileCheckChipMiddleStyle}"
                ToolTip="فحص رقم التعريف الاحصائي"
                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityFileCheckVisibilityConverter}, ConverterParameter=NIS}" />

            <CheckBox
                x:Name="RcChip"
                Grid.Column="3"
                Content="السجل التجاري (RC)"
                IsChecked="{Binding ActivityManagement.CurrentFileCheckStates.RcChipChecked}"
                Style="{StaticResource FileCheckChipMiddleStyle}"
                ToolTip="فحص السجل التجاري"
                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityFileCheckVisibilityConverter}, ConverterParameter=RC}" />

            <CheckBox
                x:Name="ArtChip"
                Grid.Column="3"
                Content="بطاقة الحرفي (ART)"
                IsChecked="{Binding ActivityManagement.CurrentFileCheckStates.ArtChipChecked}"
                Style="{StaticResource FileCheckChipMiddleStyle}"
                ToolTip="فحص بطاقة الحرفي"
                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityFileCheckVisibilityConverter}, ConverterParameter=ART}" />

            <CheckBox
                x:Name="AgrChip"
                Grid.Column="3"
                Content="شهادة مهني (AGR)"
                IsChecked="{Binding ActivityManagement.CurrentFileCheckStates.AgrChipChecked}"
                Style="{StaticResource FileCheckChipMiddleStyle}"
                ToolTip="فحص شهادة مهني"
                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityFileCheckVisibilityConverter}, ConverterParameter=AGR}" />

            <CheckBox
                x:Name="DexChip"
                Grid.Column="4"
                Content="تصريح بالوجود (DEx)"
                IsChecked="{Binding ActivityManagement.CurrentFileCheckStates.DexChipChecked}"
                Style="{StaticResource FileCheckChipLastStyle}"
                ToolTip="فحص تصريح بالوجود"
                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityFileCheckVisibilityConverter}, ConverterParameter=DEX}" />

        </Grid>


        <!--  Notes and G12/Bis Buttons  -->
        <Grid Grid.Row="2" Margin="0,8,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid Grid.Column="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="2*" />
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <TextBlock
                        Grid.Row="0"
                        Margin="0"
                        VerticalAlignment="Top"
                        Style="{DynamicResource SubtitleStyle}"
                        Text="ملاحظات" />
                    <Button
                        x:Name="AddNotes"
                        Grid.Row="1"
                        Click="AddNotes_Click"
                        Style="{DynamicResource ContainerButtonStyle}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    Kind="NotePlus" />
                                <TextBlock VerticalAlignment="Center" Text="اضافة" />
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </Grid>

                <materialDesign:Card
                    Grid.Column="1"
                    Margin="7,2"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Style="{DynamicResource ContentCardStyle}">
                    <TextBlock
                        Grid.Column="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Style="{DynamicResource LabelTextStyle}"
                        Text="{Binding NotesManagement.NotesDisplayText}"
                        TextTrimming="CharacterEllipsis"
                        TextWrapping="Wrap" />
                </materialDesign:Card>
            </Grid>

            <!--  Row 3: Action Buttons Section  -->
            <Grid Grid.Column="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <materialDesign:Card
                        Grid.Row="0"
                        Height="33"
                        Margin="0,2"
                        Padding="0"
                        Style="{StaticResource ContentCardStyle}">
                        <TextBlock
                            x:Name="G12Text"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Style="{DynamicResource LabelTextStyle}"
                            Text="{Binding ActivityManagement.G12DisplayText}" />
                    </materialDesign:Card>
                    <materialDesign:Card
                        Grid.Row="1"
                        Height="33"
                        Margin="0,2"
                        Padding="0"
                        Style="{StaticResource ContentCardStyle}">
                        <TextBlock
                            x:Name="BISText"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Style="{DynamicResource LabelTextStyle}"
                            Text="{Binding ActivityManagement.BISDisplayText}" />
                    </materialDesign:Card>
                </Grid>
                <Button
                    x:Name="YearSelectorButton"
                    Grid.Column="1"
                    Width="36"
                    Height="36"
                    Margin="7,0,0,0"
                    Padding="0"
                    Click="YearSelectorButton_Click"
                    Style="{DynamicResource ContainerButtonStyle}"
                    ToolTip="تحديد السنوات G12/BIS">
                    <materialDesign:PackIcon
                        Width="24"
                        Height="24"
                        Kind="PlaylistPlus" />
                </Button>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
