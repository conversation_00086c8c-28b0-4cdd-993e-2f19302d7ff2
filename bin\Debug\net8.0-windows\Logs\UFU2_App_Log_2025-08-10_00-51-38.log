=== UFU2 Application Session Started at 2025-08-10 00:51:38 ===
[2025-08-10 00:51:38.643]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-10 00:51:38.646]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-10 00:51:38.649]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-10 00:51:38.652]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-10 00:51:38.662]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-10 00:51:38.664]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-10 00:51:38.667]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-10 00:51:38.671]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-10 00:51:38.675]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-10 00:51:38.678]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-10 00:51:38.682]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-10 00:51:38.684]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-10 00:51:38.687]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-10 00:51:38.689]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-10 00:51:38.691]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-10 00:51:38.694]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-10 00:51:38.697]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-10 00:51:38.700]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-10 00:51:38.707]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-10 00:51:38.711]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-10 00:51:38.714]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-10 00:51:38.717]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-10 00:51:38.719]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-10 00:51:38.723]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-10 00:51:38.727]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-10 00:51:38.731]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-10 00:51:38.733]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-10 00:51:38.736]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-10 00:51:38.739]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-10 00:51:38.743]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-10 00:51:38.746]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-10 00:51:38.749]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-10 00:51:38.752]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-10 00:51:38.755]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-10 00:51:38.757]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-10 00:51:38.762]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-10 00:51:38.765]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-10 00:51:38.768]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-10 00:51:38.781]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 104.25MB working set
[2025-08-10 00:51:38.784]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-10 00:51:38.787]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-10 00:51:38.790]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-10 00:51:38.794]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-10 00:51:38.797]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-10 00:51:38.802]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-10 00:51:38.826]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-10 00:51:38.830]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-10 00:51:38.833]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-10 00:51:39.039]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-10 00:51:39.043]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-10 00:51:39.046]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-10 00:51:39.049]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-10 00:51:39.055]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638903802990539193 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-10 00:51:39.058]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-10 00:51:39.063]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 00:51:39.066]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-10 00:51:39.076]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-10 00:51:39.081]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-10 00:51:39.085]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-10 00:51:39.088]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-10 00:51:39.093]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-10 00:51:39.096]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-10 00:51:39.099]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-10 00:51:39.102]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-10 00:51:39.105]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-10 00:51:39.109]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-10 00:51:39.114]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-10 00:51:39.117]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-10 00:51:39.120]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-10 00:51:39.123]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-10 00:51:39.127]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-10 00:51:39.130]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-10 00:51:39.134]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-10 00:51:39.138]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-10 00:51:39.140]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-10 00:51:39.144]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-10 00:51:39.147]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-10 00:51:39.150]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-10 00:51:39.153]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-10 00:51:39.157]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-10 00:51:39.162]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-10 00:51:39.165]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-10 00:51:39.168]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-10 00:51:39.172]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-10 00:51:39.175]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-10 00:51:39.179]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-10 00:51:39.182]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-10 00:51:39.186]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-10 00:51:39.191]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-10 00:51:39.195]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-10 00:51:39.198]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-10 00:51:39.202]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-10 00:51:39.205]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-10 00:51:39.208]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-10 00:51:39.212]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-10 00:51:39.216]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-10 00:51:39.219]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-10 00:51:39.222]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-10 00:51:39.226]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-10 00:51:39.229]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-10 00:51:39.233]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-10 00:51:39.236]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-10 00:51:39.239]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-10 00:51:39.242]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-10 00:51:39.246]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-10 00:51:39.250]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-10 00:51:39.253]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-10 00:51:39.257]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-10 00:51:39.261]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-10 00:51:39.264]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-10 00:51:39.269]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-10 00:51:39.273]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-10 00:51:39.277]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-10 00:51:39.282]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-10 00:51:39.289]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-10 00:51:39.295]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-10 00:51:39.299]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-10 00:51:39.306]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 503ms
[2025-08-10 00:51:39.310]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-10 00:51:39.316]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-10 00:51:39.332]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-10 00:51:39.336]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-10 00:51:39.340]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-10 00:51:39.345]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-10 00:51:39.349]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-10 00:51:39.368]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-10 00:51:39.373]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:51:39.383]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-10 00:51:39.392]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:51:39.401]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:51:39.401]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:51:39.401]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:51:39.403]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-10 00:51:39.425]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:51:39.434]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-10 00:51:39.440]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-10 00:51:39.412]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:51:39.416]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:51:39.421]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:51:39.447]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:51:39.452]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:51:39.458]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:51:39.462]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:51:39.466]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:39.481]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:51:39.484]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:51:39.488]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:51:39.491]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:51:39.495]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:51:39.499]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:51:39.503]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:51:39.506]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:51:39.517]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-10 00:51:39.521]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-10 00:51:39.527]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:51:39.531]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:39.536]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 00:51:39.560]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 00:51:39.564]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 00:51:39.568]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 00:51:39.572]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 00:51:39.576]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 00:51:39.580]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 00:51:39.584]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 00:51:39.587]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 00:51:39.591]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 00:51:39.596]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 00:51:39.600]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 00:51:39.603]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 00:51:39.608]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 00:51:39.612]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 00:51:39.616]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 00:51:39.620]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 00:51:39.623]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 00:51:39.628]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 00:51:39.632]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 00:51:39.636]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 00:51:39.642]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:51:39.647]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:51:39.652]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 00:51:39.657]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 00:51:39.662]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 00:51:39.666]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 00:51:39.669]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:51:39.674]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:51:39.679]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:39.682]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-10 00:51:39.686]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:51:39.690]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:39.694]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:51:39.698]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:51:39.701]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:51:39.705]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:51:39.710]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:51:39.714]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:51:39.717]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:51:39.721]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:51:39.725]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-10 00:51:39.729]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-10 00:51:39.733]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:51:39.736]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:39.740]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-10 00:51:39.745]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-10 00:51:39.749]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-10 00:51:39.753]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-10 00:51:39.756]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-10 00:51:39.761]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-10 00:51:39.765]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-10 00:51:39.769]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-10 00:51:39.773]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-10 00:51:39.777]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-10 00:51:39.781]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-10 00:51:39.785]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:51:39.790]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:51:39.794]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-10 00:51:39.799]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-10 00:51:39.804]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-10 00:51:39.808]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-10 00:51:39.812]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:51:39.816]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:51:39.820]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:39.823]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-10 00:51:39.827]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:51:39.831]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:39.835]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:51:39.838]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:51:39.842]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:51:39.847]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:51:39.850]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:51:39.854]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:51:39.858]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:51:39.862]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:51:39.866]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-10 00:51:39.870]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-10 00:51:39.873]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:51:39.878]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:39.882]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-10 00:51:39.886]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-10 00:51:39.890]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-10 00:51:39.894]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-10 00:51:39.898]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-10 00:51:39.901]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-10 00:51:39.906]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-10 00:51:39.910]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-10 00:51:39.914]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-10 00:51:39.918]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:51:39.922]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:51:39.927]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-10 00:51:39.932]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-10 00:51:39.936]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-10 00:51:39.939]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-10 00:51:39.944]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:51:39.947]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:51:39.951]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:39.955]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:51:39.958]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:39.963]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 00:51:39.967]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 00:51:39.971]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 00:51:39.975]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 00:51:39.980]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 00:51:39.985]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 00:51:39.990]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 00:51:39.996]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 00:51:40.000]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 00:51:40.004]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 00:51:40.009]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 00:51:40.014]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 00:51:40.018]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 00:51:40.021]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 00:51:40.027]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 00:51:40.031]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 00:51:40.035]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 00:51:40.040]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 00:51:40.044]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 00:51:40.048]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 00:51:40.052]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 00:51:40.057]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:51:40.062]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:51:40.066]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 00:51:40.070]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 00:51:40.075]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 00:51:40.079]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 00:51:40.083]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:51:40.087]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-10 00:51:40.091]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-10 00:51:40.096]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-10 00:51:40.100]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-10 00:51:40.104]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-10 00:51:40.108]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-10 00:51:40.114]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-10 00:51:40.118]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-10 00:51:40.122]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-10 00:51:40.127]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-10 00:51:40.131]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-10 00:51:40.136]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-10 00:51:40.141]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-10 00:51:40.146]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-10 00:51:40.150]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-10 00:51:40.155]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-10 00:51:40.159]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-10 00:51:40.164]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-10 00:51:40.168]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-10 00:51:40.172]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-10 00:51:40.177]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-10 00:51:40.182]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-10 00:51:40.187]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:40.202]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-10 00:51:40.206]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-10 00:51:40.210]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-10 00:51:40.214]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-10 00:51:40.218]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-10 00:51:40.222]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-10 00:51:40.226]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-10 00:51:40.230]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-10 00:51:40.234]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-10 00:51:40.238]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-10 00:51:40.242]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-10 00:51:40.247]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-10 00:51:40.251]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-10 00:51:40.255]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.260]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-10 00:51:40.264]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.269]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:40.273]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.278]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-10 00:51:40.282]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.287]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:40.291]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-10 00:51:40.299]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-10 00:51:40.339]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-10 00:51:40.346]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:40.368]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 00:51:40.372]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 00:51:40.378]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:40.400]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 00:51:40.404]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 00:51:40.410]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-10 00:51:40.414]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-10 00:51:40.418]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 00:51:40.427]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-10 00:51:40.601]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 00:51:40.660]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:51:40.665]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-10 00:51:40.669]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 00:51:40.714]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-10 00:51:40.721]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:51:40.725]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-10 00:51:40.730]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-10 00:51:40.736]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-10 00:51:40.740]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:40.748]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 1 clients, 1 activities
[2025-08-10 00:51:40.757]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-10 00:51:40.763]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-10 00:51:40.767]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-10 00:51:40.773]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-10 00:51:40.777]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-10 00:51:40.781]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-10 00:51:40.785]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-10 00:51:40.789]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-10 00:51:40.793]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-10 00:51:40.797]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-10 00:51:40.802]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-10 00:51:40.806]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-10 00:51:40.811]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-10 00:51:40.815]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-10 00:51:40.819]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-10 00:51:40.823]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-10 00:51:40.828]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-10 00:51:40.834]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-10 00:51:40.840]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-10 00:51:40.847]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.874]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-10 00:51:40.879]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.898]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.907]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-10 00:51:40.912]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.916]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.921]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-10 00:51:40.925]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.929]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.934]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-10 00:51:40.938]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.942]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.947]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-10 00:51:40.950]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.954]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.959]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-10 00:51:40.963]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.967]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:40.971]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-10 00:51:40.975]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:40.980]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-10 00:51:40.984]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 144ms
[2025-08-10 00:51:40.989]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-10 00:51:41.001]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-10 00:51:41.005]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 17ms
[2025-08-10 00:51:41.010]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-10 00:51:41.016]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-10 00:51:41.020]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 10ms
[2025-08-10 00:51:41.024]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-10 00:51:41.030]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:41.035]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-10 00:51:41.040]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:41.047]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-10 00:51:41.051]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:41.055]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-10 00:51:41.059]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:51:41.064]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-10 00:51:41.068]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-10 00:51:41.072]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 49ms
[2025-08-10 00:51:41.078]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-10 00:51:41.083]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.089]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-10 00:51:41.094]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.099]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.104]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-10 00:51:41.108]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.113]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.117]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-10 00:51:41.121]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.125]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.129]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-10 00:51:41.133]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.137]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.141]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-10 00:51:41.147]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.151]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.155]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-10 00:51:41.159]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.163]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.168]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-10 00:51:41.172]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.175]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.180]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-10 00:51:41.184]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.188]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.192]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-10 00:51:41.197]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.201]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:51:41.205]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-10 00:51:41.210]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:51:41.214]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-10 00:51:41.218]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 142ms
[2025-08-10 00:51:41.223]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-10 00:51:41.228]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-10 00:51:41.232]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-10 00:51:41.236]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-10 00:51:41.239]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-10 00:51:41.244]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-10 00:51:41.248]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-10 00:51:41.252]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-10 00:51:41.255]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-10 00:51:41.259]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-10 00:51:41.263]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-10 00:51:41.267]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-10 00:51:41.271]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-10 00:51:41.275]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-10 00:51:41.279]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-10 00:51:41.283]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-10 00:51:41.287]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-10 00:51:41.295]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-10 00:51:41.386]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-10 00:51:41.391]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-10 00:51:41.396]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-10 00:51:41.400]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-10 00:51:41.407]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:51:41.412]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:41.418]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:51:41.422]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:41.428]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:51:41.433]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:41.496]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-10 00:51:41.500]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-10 00:51:41.762]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 1160.5864ms
[2025-08-10 00:51:41.768]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 00:51:41.773]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:41.778]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:41.782]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:41.786]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:41.790]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:41.795]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:42.267]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-10 00:51:42.272]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-10 00:51:42.278]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-10 00:51:42.301]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-10 00:51:42.305]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-10 00:51:42.311]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 10ms
[2025-08-10 00:51:42.779]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2178.3887ms
[2025-08-10 00:51:42.784]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-10 00:51:43.712]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-10 00:51:44.068]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-10 00:51:44.793]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 00:51:44.822]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 4220.7045ms
[2025-08-10 00:51:44.827]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-10 00:51:44.832]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:44.835]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:44.840]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:44.846]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:44.850]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:44.856]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:55.943]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 00:51:55.963]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:55.969]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:55.974]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:55.982]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:55.986]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:55.990]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:56.830]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 887.2209ms
[2025-08-10 00:51:56.835]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-10 00:51:57.287]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.291]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:57.296]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.300]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:57.304]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.308]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:57.342]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.347]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:57.351]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.356]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:57.362]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-10 00:51:57.366]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.370]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-10 00:51:57.375]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-10 00:51:57.380]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-10 00:51:57.384]  	[INFO]		[MainWindow]	Application closing
[2025-08-10 00:51:57.389]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_26059286_638903803173893961 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-10 00:51:57.394]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-10 00:51:57.398]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 00:51:57.402]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 00:51:57.406]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 00:51:57.412]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-10 00:51:57.426]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-10 00:51:57.431]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-10 00:51:57.436]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-10 00:51:57.511]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-10 00:51:57.535]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-10 00:51:57.552]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.557]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:57.564]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.570]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:57.575]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:57.580]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:58.395]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 982.3386ms
[2025-08-10 00:51:58.400]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 00:51:58.528]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:58.532]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:58.536]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:58.540]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:51:58.546]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:51:58.550]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:51:58.610]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-10 00:51:58.615]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-10 00:51:58.620]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-10 00:51:58.629]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-10 00:51:58.633]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:51:58.637]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-10 00:51:58.641]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-10 00:51:58.646]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:51:58.650]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:51:58.654]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:51:58.657]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_26059286_638903803173893961
[2025-08-10 00:51:58.662]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:51:58.666]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-10 00:51:58.670]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-10 00:51:58.674]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:51:58.679]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:51:58.682]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:51:58.686]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-10 00:51:58.699]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-10 00:51:58.703]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-10 00:51:58.707]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-10 00:51:58.712]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-10 00:51:58.715]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 00:51:58.719]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 00:51:58.724]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:51:58.728]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-10 00:51:58.732]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-10 00:51:58.736]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:51:58.740]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:51:58.745]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:51:58.748]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_28090709_638903802990539193
[2025-08-10 00:51:58.752]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:51:58.756]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-10 00:51:58.761]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-10 00:51:58.765]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:51:58.769]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:51:58.772]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:51:58.777]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-10 00:51:58.781]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-10 00:51:58.786]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-10 00:51:58.790]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-10 00:51:58.795]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-10 00:51:58.800]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-10 00:51:58.811]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-10 00:51:58.815]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-10 00:51:58.819]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-10 00:51:58.823]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-10 00:51:58.864]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-10 00:51:58.874]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-10 00:51:58.883]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-10 00:51:58.889]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-10 00:51:58.899]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-10 00:51:58.905]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-10 00:51:58.913]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:51:58.917]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:51:58.922]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:51:58.928]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:51:58.932]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-10 00:51:58.936]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-10 00:51:58.940]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-10 00:51:58.947]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-10 00:51:58.951]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-10 00:51:58.957]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-10 00:51:58.963]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-10 00:51:58.968]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-10 00:51:58.973]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-10 00:51:58.981]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-10 00:51:58.985]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-10 00:51:58.989]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-10 00:51:58.995]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 0.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-10 00:51:58.999]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-10 00:51:59.005]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-10 00:51:59.011]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-10 00:51:59.016]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 50.0%
[2025-08-10 00:51:59.021]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-10 00:51:59.029]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-10 00:51:59.034]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:51:59.040]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-10 00:51:59.047]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-10 00:51:59.052]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-10 00:51:59.067]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-10 00:51:59.083]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-10 00:51:59.095]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-10 00:51:59.100]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-10 00:51:59.105]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-10 00:51:59.110]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-10 00:51:59.115]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:51:59.120]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-10 00:51:59.139]  	[INFO]		[ResourceManager]	Generated memory leak report: 0 alive resources, 0 dead resources
[2025-08-10 00:51:59.153]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 1
[2025-08-10 00:51:59.158]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 1 potential leaks detected
[2025-08-10 00:51:59.164]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-10 00:51:59.168]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-10 00:51:59.176]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-10 00:51:59.184]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-10 00:51:59.189]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-10 00:51:59.197]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-10 00:51:59.204]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-10 00:51:59.211]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-10 00:51:59.215]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 2 tracked, 2 disposed, 1 cleanups
[2025-08-10 00:51:59.220]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-10 00:51:59.224]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-10 00:51:59.229]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-10 00:51:59.233]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-10 00:51:59.237]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-10 00:51:59.242]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-10 00:51:59.246]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-10 00:51:59.250]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-10 00:51:59.255]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-10 00:51:59.259]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-10 00:51:59.264]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-10 00:51:59.269]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-10 00:51:59.279]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-10 00:51:59.285]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-10 00:51:59.291]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-10 00:51:59.297]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-10 00:51:59.304]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-10 00:51:59.310]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-10 00:51:59.317]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-10 00:51:59.323]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-10 00:51:59 ===
