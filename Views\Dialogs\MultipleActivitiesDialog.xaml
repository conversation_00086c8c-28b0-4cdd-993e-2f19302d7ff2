<UserControl
    x:Class="UFU2.Views.Dialogs.MultipleActivitiesDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    d:DesignHeight="500"
    d:DesignWidth="620"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  Converters  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <!--  DialogHost for nested dialogs  -->
    <materialDesign:DialogHost
        materialDesign:TransitionAssist.DisableTransitions="True"
        CloseOnClickAway="False"
        Identifier="MultipleActivitiesDialogHost"
        IsTabStop="False">

        <!--  Main Dialog Card  -->
        <materialDesign:Card
            Width="620"
            Height="500"
            Padding="0"
            materialDesign:ElevationAssist.Elevation="Dp8"
            Style="{StaticResource DialogBaseCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <!--  Header  -->
                <materialDesign:Card
                    Grid.Row="0"
                    Margin="0"
                    Style="{DynamicResource HeaderCardStyle}">
                    <TextBlock
                        HorizontalAlignment="Center"
                        Style="{StaticResource HeadlineStyle}"
                        Text="إضافة أنشطة متعددة" />
                </materialDesign:Card>

                <!--  Input Section  -->
                <Grid Grid.Row="1" Margin="12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="81" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--  Manual Code Entry  -->
                    <TextBox
                        x:Name="ManualCodeTextBox"
                        Grid.Column="0"
                        materialDesign:HintAssist.Hint="رمز النشاط"
                        materialDesign:TextFieldAssist.CharacterCounterVisibility="Hidden"
                        MaxLength="6"
                        Style="{StaticResource UnderlineTextBoxStyle}"
                        Text="{Binding ManualActivityCode, UpdateSourceTrigger=PropertyChanged}"
                        ToolTip="أدخل رمز النشاط المكون من 6 أرقام" />

                    <!--  AutoSuggestBox Search  -->
                    <materialDesign:AutoSuggestBox
                        x:Name="SearchAutoSuggestBox"
                        Grid.Column="1"
                        materialDesign:HintAssist.Hint="ابحث عن النشاط بالوصف"
                        materialDesign:TextFieldAssist.HasClearButton="True"
                        SelectedItem="{Binding SelectedSearchResult, Mode=TwoWay}"
                        Style="{StaticResource UnderlineAutoSuggestBoxStyle}"
                        Suggestions="{Binding SearchResults}"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                        ToolTip="ابحث عن النشاط بكتابة جزء من الوصف"
                        ValueMember="Description">
                        <materialDesign:AutoSuggestBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock IsHitTestVisible="False">
                                    <Run FontWeight="Bold" Text="{Binding Code}" />
                                    <Run Text=" - " />
                                    <Run Text="{Binding Description}" />
                                </TextBlock>
                            </DataTemplate>
                        </materialDesign:AutoSuggestBox.ItemTemplate>
                    </materialDesign:AutoSuggestBox>

                </Grid>
                <!--  Activities List Section  -->
                <Grid Grid.Row="2" Margin="24,12">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  Section Header  -->
                    <TextBlock
                        Grid.Row="0"
                        Margin="0,0,0,12"
                        Style="{StaticResource BodyTextStyle}"
                        Text="الأنشطة المضافة" />

                    <!--  Activities Content Area  -->
                    <ScrollViewer
                        Grid.Row="1"
                        Style="{StaticResource DialogScrollViewerStyle}"
                        VerticalScrollBarVisibility="Auto">

                        <Grid>
                            <!--  Empty State  -->
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Style="{StaticResource HeadlineStyle}"
                                Text="لم يتم إضافة أي أنشطة بعد"
                                Visibility="{Binding HasActivities, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />

                            <!--  Activities List  -->
                            <ItemsControl ItemsSource="{Binding AddedActivities}" Visibility="{Binding HasActivities, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <materialDesign:Card
                                            Margin="0,0,0,8"
                                            Padding="12,0"
                                            materialDesign:ElevationAssist.Elevation="Dp2"
                                            Style="{StaticResource ContentCardStyle}">
                                            <Grid Margin="16,12">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>

                                                <!--  Activity Info  -->
                                                <StackPanel Grid.Column="0" Orientation="Horizontal">
                                                    <TextBlock Style="{StaticResource BodyTextStyle}" Text="{Binding Code}" />
                                                    <TextBlock
                                                        Margin="8,0,0,0"
                                                        Style="{StaticResource BodyTextStyle}"
                                                        Text="{Binding Description}" />
                                                </StackPanel>

                                                <!--  Delete Button  -->
                                                <Button
                                                    Grid.Column="1"
                                                    Command="{Binding DataContext.RemoveActivityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource IconDeleteButtonStyle}"
                                                    ToolTip="حذف النشاط">
                                                    <materialDesign:PackIcon Kind="Delete" />
                                                </Button>
                                            </Grid>
                                        </materialDesign:Card>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </Grid>
                    </ScrollViewer>
                </Grid>

                <!--  Action buttons section  -->
                <userControls:SaveCancelButtonsControl
                    Grid.Row="3"
                    CancelClick="CancelButton_Click"
                    CancelTooltip="إلغاء التغييرات"
                    IsSaveEnabled="{Binding HasActivities}"
                    SaveClick="SaveButton_Click"
                    SaveTooltip="حفظ الأنشطة" />
            </Grid>
        </materialDesign:Card>

    </materialDesign:DialogHost>
</UserControl>
