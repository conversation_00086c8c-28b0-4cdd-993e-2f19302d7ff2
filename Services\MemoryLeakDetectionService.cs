using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Runtime;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Advanced memory leak detection service for UFU2 application.
    /// Monitors memory usage patterns, detects potential leaks, and provides detailed analysis.
    /// Implements Phase 2D Task 2.3 requirements for memory leak detection.
    /// Integrates with ResourceManager and WeakEventManager for comprehensive monitoring.
    /// </summary>
    public class MemoryLeakDetectionService : IDisposable
    {
        #region Private Fields

        private readonly ResourceManager? _resourceManager;
        private readonly WeakEventManager? _weakEventManager;
        private readonly ConcurrentDictionary<string, MemorySnapshot> _memorySnapshots;
        private readonly ConcurrentDictionary<Type, TypeMemoryInfo> _typeMemoryTracking;
        private readonly Timer _detectionTimer;
        private readonly Timer _snapshotTimer;
        private readonly Process _currentProcess;
        private bool _disposed = false;

        // Performance tracking
        private long _totalSnapshots = 0;
        private long _leaksDetected = 0;
        private long _alertsGenerated = 0;
        private long _gcCollectionsForced = 0;

        // Configuration
        private readonly TimeSpan _detectionInterval = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _snapshotInterval = TimeSpan.FromMinutes(2);
        private readonly double _memoryGrowthThresholdMB = 50.0; // Alert if memory grows by 50MB
        private readonly double _memoryGrowthPercentageThreshold = 20.0; // Alert if memory grows by 20%
        private readonly int _maxSnapshots = 50; // Keep last 50 snapshots
        private readonly double _criticalMemoryThresholdMB = 600.0; // Critical threshold

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the MemoryLeakDetectionService.
        /// </summary>
        /// <param name="resourceManager">ResourceManager for integration</param>
        /// <param name="weakEventManager">WeakEventManager for integration</param>
        public MemoryLeakDetectionService(ResourceManager? resourceManager = null, WeakEventManager? weakEventManager = null)
        {
            _resourceManager = resourceManager;
            _weakEventManager = weakEventManager;
            _memorySnapshots = new ConcurrentDictionary<string, MemorySnapshot>();
            _typeMemoryTracking = new ConcurrentDictionary<Type, TypeMemoryInfo>();
            _currentProcess = Process.GetCurrentProcess();

            // Initialize timers
            _detectionTimer = new Timer(PerformLeakDetection, null, (int)_detectionInterval.TotalMilliseconds, (int)_detectionInterval.TotalMilliseconds);
            _snapshotTimer = new Timer(TakeMemorySnapshot, null, (int)_snapshotInterval.TotalMilliseconds, (int)_snapshotInterval.TotalMilliseconds);

            // Take initial snapshot
            TakeMemorySnapshot(null);

            LoggingService.LogInfo("MemoryLeakDetectionService initialized with automatic monitoring", "MemoryLeakDetectionService");
        }

        #endregion

        #region Public Methods - Leak Detection

        /// <summary>
        /// Performs immediate memory leak detection and returns results.
        /// </summary>
        /// <returns>Memory leak detection report</returns>
        public MemoryLeakDetectionReport PerformImmediateDetection()
        {
            try
            {
                var report = new MemoryLeakDetectionReport
                {
                    GeneratedAt = DateTime.UtcNow,
                    TotalSnapshots = _totalSnapshots,
                    LeaksDetected = _leaksDetected,
                    AlertsGenerated = _alertsGenerated,
                    GcCollectionsForced = _gcCollectionsForced
                };

                // Take current snapshot
                var currentSnapshot = CreateMemorySnapshot();
                
                // Analyze memory growth
                AnalyzeMemoryGrowth(report, currentSnapshot);

                // Analyze resource leaks
                AnalyzeResourceLeaks(report);

                // Analyze event subscription leaks
                AnalyzeEventSubscriptionLeaks(report);

                // Analyze type-specific memory usage
                AnalyzeTypeMemoryUsage(report);

                // Generate recommendations
                GenerateRecommendations(report);

                LoggingService.LogInfo($"Memory leak detection completed. Potential leaks: {report.PotentialLeaks.Count}", "MemoryLeakDetectionService");
                return report;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error performing memory leak detection: {ex.Message}", "MemoryLeakDetectionService");
                return new MemoryLeakDetectionReport { GeneratedAt = DateTime.UtcNow };
            }
        }

        /// <summary>
        /// Registers a type for memory tracking.
        /// </summary>
        /// <param name="type">Type to track</param>
        /// <param name="category">Category for organization</param>
        public void RegisterTypeForTracking(Type type, string category = "General")
        {
            if (type == null)
                return;

            try
            {
                var typeInfo = new TypeMemoryInfo
                {
                    Type = type,
                    Category = category,
                    FirstTrackedAt = DateTime.UtcNow,
                    InstanceCount = 0,
                    EstimatedMemoryUsageBytes = 0
                };

                _typeMemoryTracking.AddOrUpdate(type, typeInfo, (key, oldValue) => typeInfo);
                LoggingService.LogDebug($"Registered type for memory tracking: {type.Name} ({category})", "MemoryLeakDetectionService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error registering type for tracking {type.Name}: {ex.Message}", "MemoryLeakDetectionService");
            }
        }

        /// <summary>
        /// Updates instance count for a tracked type.
        /// </summary>
        /// <param name="type">Type to update</param>
        /// <param name="instanceCount">Current instance count</param>
        /// <param name="estimatedMemoryBytes">Estimated memory usage in bytes</param>
        public void UpdateTypeInstanceCount(Type type, int instanceCount, long estimatedMemoryBytes = 0)
        {
            if (type == null)
                return;

            try
            {
                if (_typeMemoryTracking.TryGetValue(type, out var typeInfo))
                {
                    typeInfo.InstanceCount = instanceCount;
                    typeInfo.EstimatedMemoryUsageBytes = estimatedMemoryBytes;
                    typeInfo.LastUpdatedAt = DateTime.UtcNow;

                    // Check for potential leak
                    if (instanceCount > typeInfo.PeakInstanceCount)
                    {
                        typeInfo.PeakInstanceCount = instanceCount;
                        typeInfo.PeakReachedAt = DateTime.UtcNow;

                        // Alert if instance count is growing rapidly
                        if (instanceCount > 100 && instanceCount > typeInfo.BaselineInstanceCount * 2)
                        {
                            LoggingService.LogWarning($"Potential memory leak detected for type {type.Name}: {instanceCount} instances (peak: {typeInfo.PeakInstanceCount})", "MemoryLeakDetectionService");
                            System.Threading.Interlocked.Increment(ref _leaksDetected);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating type instance count for {type.Name}: {ex.Message}", "MemoryLeakDetectionService");
            }
        }

        /// <summary>
        /// Forces garbage collection and analyzes memory before/after.
        /// </summary>
        /// <returns>Garbage collection analysis results</returns>
        public GarbageCollectionAnalysis ForceGarbageCollectionAnalysis()
        {
            try
            {
                var analysis = new GarbageCollectionAnalysis
                {
                    AnalysisStartedAt = DateTime.UtcNow
                };

                // Take before snapshot
                analysis.MemoryBeforeGC = GetCurrentMemoryUsageMB();
                analysis.Gen0CollectionsBeforeGC = GC.CollectionCount(0);
                analysis.Gen1CollectionsBeforeGC = GC.CollectionCount(1);
                analysis.Gen2CollectionsBeforeGC = GC.CollectionCount(2);

                // Force garbage collection
                var stopwatch = Stopwatch.StartNew();
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                stopwatch.Stop();

                // Take after snapshot
                analysis.MemoryAfterGC = GetCurrentMemoryUsageMB();
                analysis.Gen0CollectionsAfterGC = GC.CollectionCount(0);
                analysis.Gen1CollectionsAfterGC = GC.CollectionCount(1);
                analysis.Gen2CollectionsAfterGC = GC.CollectionCount(2);
                analysis.GcDurationMs = stopwatch.ElapsedMilliseconds;

                // Calculate results
                analysis.MemoryFreedMB = analysis.MemoryBeforeGC - analysis.MemoryAfterGC;
                analysis.MemoryFreedPercentage = analysis.MemoryBeforeGC > 0 ? (analysis.MemoryFreedMB / analysis.MemoryBeforeGC) * 100 : 0;

                System.Threading.Interlocked.Increment(ref _gcCollectionsForced);

                LoggingService.LogInfo($"Forced GC analysis: {analysis.MemoryFreedMB:F2}MB freed ({analysis.MemoryFreedPercentage:F1}%) in {analysis.GcDurationMs}ms", "MemoryLeakDetectionService");
                return analysis;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error performing GC analysis: {ex.Message}", "MemoryLeakDetectionService");
                return new GarbageCollectionAnalysis { AnalysisStartedAt = DateTime.UtcNow };
            }
        }

        #endregion

        #region Public Methods - Memory Monitoring

        /// <summary>
        /// Gets current memory usage statistics.
        /// </summary>
        /// <returns>Current memory usage information</returns>
        public MemoryUsageInfo GetCurrentMemoryUsage()
        {
            try
            {
                var memoryInfo = new MemoryUsageInfo
                {
                    Timestamp = DateTime.UtcNow,
                    WorkingSetMB = GetCurrentMemoryUsageMB(),
                    PrivateMemoryMB = _currentProcess.PrivateMemorySize64 / (1024.0 * 1024.0),
                    VirtualMemoryMB = _currentProcess.VirtualMemorySize64 / (1024.0 * 1024.0),
                    Gen0Collections = GC.CollectionCount(0),
                    Gen1Collections = GC.CollectionCount(1),
                    Gen2Collections = GC.CollectionCount(2),
                    TotalMemoryMB = GC.GetTotalMemory(false) / (1024.0 * 1024.0)
                };

                // Add resource manager statistics if available
                if (_resourceManager != null)
                {
                    var resourceReport = _resourceManager.GenerateLeakReport();
                    memoryInfo.TrackedResources = resourceReport.AliveResources;
                    memoryInfo.DeadResources = resourceReport.DeadResources;
                }

                // Add weak event manager statistics if available
                if (_weakEventManager != null)
                {
                    var eventStats = _weakEventManager.GetStatistics();
                    memoryInfo.ActiveEventSubscriptions = eventStats.ActiveSubscriptions;
                    memoryInfo.DeadEventSubscriptions = eventStats.DeadSubscriptions;
                }

                return memoryInfo;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting current memory usage: {ex.Message}", "MemoryLeakDetectionService");
                return new MemoryUsageInfo { Timestamp = DateTime.UtcNow };
            }
        }

        /// <summary>
        /// Gets memory usage trend analysis.
        /// </summary>
        /// <param name="timeSpan">Time span to analyze</param>
        /// <returns>Memory trend analysis</returns>
        public MemoryTrendAnalysis GetMemoryTrend(TimeSpan timeSpan)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow - timeSpan;
                var recentSnapshots = _memorySnapshots.Values
                    .Where(s => s.Timestamp >= cutoffTime)
                    .OrderBy(s => s.Timestamp)
                    .ToList();

                if (recentSnapshots.Count < 2)
                {
                    return new MemoryTrendAnalysis
                    {
                        AnalysisStartTime = cutoffTime,
                        AnalysisEndTime = DateTime.UtcNow,
                        SnapshotCount = recentSnapshots.Count,
                        TrendDirection = MemoryTrendDirection.Insufficient_Data
                    };
                }

                var analysis = new MemoryTrendAnalysis
                {
                    AnalysisStartTime = recentSnapshots.First().Timestamp,
                    AnalysisEndTime = recentSnapshots.Last().Timestamp,
                    SnapshotCount = recentSnapshots.Count,
                    StartMemoryMB = recentSnapshots.First().WorkingSetMB,
                    EndMemoryMB = recentSnapshots.Last().WorkingSetMB,
                    MinMemoryMB = recentSnapshots.Min(s => s.WorkingSetMB),
                    MaxMemoryMB = recentSnapshots.Max(s => s.WorkingSetMB),
                    AverageMemoryMB = recentSnapshots.Average(s => s.WorkingSetMB)
                };

                // Calculate trend
                analysis.MemoryChangeMB = analysis.EndMemoryMB - analysis.StartMemoryMB;
                analysis.MemoryChangePercentage = analysis.StartMemoryMB > 0 ? (analysis.MemoryChangeMB / analysis.StartMemoryMB) * 100 : 0;

                // Determine trend direction
                if (Math.Abs(analysis.MemoryChangePercentage) < 5)
                    analysis.TrendDirection = MemoryTrendDirection.Stable;
                else if (analysis.MemoryChangeMB > 0)
                    analysis.TrendDirection = analysis.MemoryChangePercentage > 20 ? MemoryTrendDirection.Rapidly_Increasing : MemoryTrendDirection.Increasing;
                else
                    analysis.TrendDirection = analysis.MemoryChangePercentage < -20 ? MemoryTrendDirection.Rapidly_Decreasing : MemoryTrendDirection.Decreasing;

                return analysis;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error analyzing memory trend: {ex.Message}", "MemoryLeakDetectionService");
                return new MemoryTrendAnalysis
                {
                    AnalysisStartTime = DateTime.UtcNow - timeSpan,
                    AnalysisEndTime = DateTime.UtcNow,
                    TrendDirection = MemoryTrendDirection.Error
                };
            }
        }

        #endregion

        #region Private Methods - Detection Logic

        /// <summary>
        /// Performs automatic memory leak detection.
        /// Called periodically by the detection timer.
        /// </summary>
        private void PerformLeakDetection(object? state)
        {
            try
            {
                var report = PerformImmediateDetection();

                // Generate alerts for critical issues
                if (report.HasCriticalLeaks)
                {
                    System.Threading.Interlocked.Increment(ref _alertsGenerated);
                    LoggingService.LogWarning($"Critical memory leaks detected: {report.PotentialLeaks.Count} issues found", "MemoryLeakDetectionService");

                    // Force cleanup if memory usage is critical
                    var currentMemory = GetCurrentMemoryUsageMB();
                    if (currentMemory > _criticalMemoryThresholdMB)
                    {
                        LoggingService.LogWarning($"Critical memory usage: {currentMemory}MB. Forcing cleanup.", "MemoryLeakDetectionService");
                        _resourceManager?.ForceCleanup();
                        _weakEventManager?.ForceCleanup();

                        // Force GC as last resort
                        ForceGarbageCollectionAnalysis();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during automatic leak detection: {ex.Message}", "MemoryLeakDetectionService");
            }
        }

        /// <summary>
        /// Takes a memory snapshot for trend analysis.
        /// Called periodically by the snapshot timer.
        /// </summary>
        private void TakeMemorySnapshot(object? state)
        {
            try
            {
                var snapshot = CreateMemorySnapshot();
                var snapshotKey = snapshot.Timestamp.ToString("yyyy-MM-dd_HH-mm-ss");

                _memorySnapshots.AddOrUpdate(snapshotKey, snapshot, (key, oldValue) => snapshot);
                System.Threading.Interlocked.Increment(ref _totalSnapshots);

                // Cleanup old snapshots
                if (_memorySnapshots.Count > _maxSnapshots)
                {
                    var oldestSnapshots = _memorySnapshots.Values
                        .OrderBy(s => s.Timestamp)
                        .Take(_memorySnapshots.Count - _maxSnapshots)
                        .ToList();

                    foreach (var oldSnapshot in oldestSnapshots)
                    {
                        var oldKey = oldSnapshot.Timestamp.ToString("yyyy-MM-dd_HH-mm-ss");
                        _memorySnapshots.TryRemove(oldKey, out _);
                    }
                }

                LoggingService.LogDebug($"Memory snapshot taken: {snapshot.WorkingSetMB:F2}MB working set", "MemoryLeakDetectionService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error taking memory snapshot: {ex.Message}", "MemoryLeakDetectionService");
            }
        }

        /// <summary>
        /// Creates a memory snapshot with current system information.
        /// </summary>
        private MemorySnapshot CreateMemorySnapshot()
        {
            return new MemorySnapshot
            {
                Timestamp = DateTime.UtcNow,
                WorkingSetMB = GetCurrentMemoryUsageMB(),
                PrivateMemoryMB = _currentProcess.PrivateMemorySize64 / (1024.0 * 1024.0),
                VirtualMemoryMB = _currentProcess.VirtualMemorySize64 / (1024.0 * 1024.0),
                ManagedMemoryMB = GC.GetTotalMemory(false) / (1024.0 * 1024.0),
                Gen0Collections = GC.CollectionCount(0),
                Gen1Collections = GC.CollectionCount(1),
                Gen2Collections = GC.CollectionCount(2),
                ThreadCount = _currentProcess.Threads.Count,
                HandleCount = _currentProcess.HandleCount
            };
        }

        /// <summary>
        /// Gets the current memory usage in megabytes.
        /// </summary>
        private double GetCurrentMemoryUsageMB()
        {
            try
            {
                _currentProcess.Refresh();
                return _currentProcess.WorkingSet64 / (1024.0 * 1024.0);
            }
            catch
            {
                return 0.0;
            }
        }

        #endregion

        #region Private Methods - Analysis

        /// <summary>
        /// Analyzes memory growth patterns for potential leaks.
        /// </summary>
        private void AnalyzeMemoryGrowth(MemoryLeakDetectionReport report, MemorySnapshot currentSnapshot)
        {
            var recentSnapshots = _memorySnapshots.Values
                .Where(s => s.Timestamp >= DateTime.UtcNow.AddHours(-1))
                .OrderBy(s => s.Timestamp)
                .ToList();

            if (recentSnapshots.Count < 2)
                return;

            var firstSnapshot = recentSnapshots.First();
            var memoryGrowthMB = currentSnapshot.WorkingSetMB - firstSnapshot.WorkingSetMB;
            var memoryGrowthPercentage = firstSnapshot.WorkingSetMB > 0 ? (memoryGrowthMB / firstSnapshot.WorkingSetMB) * 100 : 0;

            if (memoryGrowthMB > _memoryGrowthThresholdMB || memoryGrowthPercentage > _memoryGrowthPercentageThreshold)
            {
                report.PotentialLeaks.Add(new MemoryLeakInfo
                {
                    LeakType = MemoryLeakType.Memory_Growth,
                    Severity = memoryGrowthMB > _memoryGrowthThresholdMB * 2 ? LeakSeverity.Critical : LeakSeverity.Warning,
                    Description = $"Memory growth detected: {memoryGrowthMB:F2}MB ({memoryGrowthPercentage:F1}%) in the last hour",
                    DetectedAt = DateTime.UtcNow,
                    AffectedComponent = "System Memory",
                    RecommendedAction = memoryGrowthMB > _memoryGrowthThresholdMB * 2 ? "Force garbage collection and resource cleanup" : "Monitor memory usage patterns"
                });
            }
        }

        /// <summary>
        /// Analyzes resource leaks using ResourceManager data.
        /// </summary>
        private void AnalyzeResourceLeaks(MemoryLeakDetectionReport report)
        {
            if (_resourceManager == null)
                return;

            var resourceReport = _resourceManager.GenerateLeakReport();

            if (resourceReport.HasPotentialLeaks)
            {
                report.PotentialLeaks.Add(new MemoryLeakInfo
                {
                    LeakType = MemoryLeakType.Resource_Leak,
                    Severity = resourceReport.DeadResources > 10 ? LeakSeverity.Critical : LeakSeverity.Warning,
                    Description = $"Resource leaks detected: {resourceReport.DeadResources} dead resources, {resourceReport.DisposalEfficiency:F1}% disposal efficiency",
                    DetectedAt = DateTime.UtcNow,
                    AffectedComponent = "Resource Management",
                    RecommendedAction = "Review resource disposal patterns and ensure proper cleanup"
                });
            }
        }

        /// <summary>
        /// Analyzes event subscription leaks using WeakEventManager data.
        /// </summary>
        private void AnalyzeEventSubscriptionLeaks(MemoryLeakDetectionReport report)
        {
            if (_weakEventManager == null)
                return;

            var eventStats = _weakEventManager.GetStatistics();

            if (eventStats.HasPotentialLeaks)
            {
                report.PotentialLeaks.Add(new MemoryLeakInfo
                {
                    LeakType = MemoryLeakType.Event_Subscription_Leak,
                    Severity = eventStats.DeadSubscriptions > 20 ? LeakSeverity.Critical : LeakSeverity.Warning,
                    Description = $"Event subscription leaks detected: {eventStats.DeadSubscriptions} dead subscriptions, {eventStats.CleanupEfficiency:F1}% cleanup efficiency",
                    DetectedAt = DateTime.UtcNow,
                    AffectedComponent = "Event Management",
                    RecommendedAction = "Review event subscription patterns and ensure proper unsubscription"
                });
            }
        }

        /// <summary>
        /// Analyzes type-specific memory usage patterns.
        /// </summary>
        private void AnalyzeTypeMemoryUsage(MemoryLeakDetectionReport report)
        {
            foreach (var kvp in _typeMemoryTracking)
            {
                var typeInfo = kvp.Value;

                // Check for excessive instance counts
                if (typeInfo.InstanceCount > 100 && typeInfo.InstanceCount > typeInfo.BaselineInstanceCount * 3)
                {
                    report.PotentialLeaks.Add(new MemoryLeakInfo
                    {
                        LeakType = MemoryLeakType.Type_Instance_Leak,
                        Severity = typeInfo.InstanceCount > typeInfo.BaselineInstanceCount * 5 ? LeakSeverity.Critical : LeakSeverity.Warning,
                        Description = $"Excessive instances of {typeInfo.Type.Name}: {typeInfo.InstanceCount} instances (baseline: {typeInfo.BaselineInstanceCount})",
                        DetectedAt = DateTime.UtcNow,
                        AffectedComponent = typeInfo.Type.Name,
                        RecommendedAction = $"Review {typeInfo.Type.Name} lifecycle and disposal patterns"
                    });
                }
            }
        }

        /// <summary>
        /// Generates recommendations based on detected issues.
        /// </summary>
        private void GenerateRecommendations(MemoryLeakDetectionReport report)
        {
            if (report.PotentialLeaks.Count == 0)
            {
                report.Recommendations.Add("No memory leaks detected. Continue monitoring.");
                return;
            }

            var criticalLeaks = report.PotentialLeaks.Count(l => l.Severity == LeakSeverity.Critical);
            var warningLeaks = report.PotentialLeaks.Count(l => l.Severity == LeakSeverity.Warning);

            if (criticalLeaks > 0)
            {
                report.Recommendations.Add($"CRITICAL: {criticalLeaks} critical memory leaks detected. Immediate action required.");
                report.Recommendations.Add("Force garbage collection and resource cleanup.");
                report.Recommendations.Add("Review disposal patterns in affected components.");
            }

            if (warningLeaks > 0)
            {
                report.Recommendations.Add($"WARNING: {warningLeaks} potential memory leaks detected. Monitor closely.");
                report.Recommendations.Add("Review resource and event subscription management.");
            }

            // Add specific recommendations based on leak types
            var leakTypes = report.PotentialLeaks.Select(l => l.LeakType).Distinct();
            foreach (var leakType in leakTypes)
            {
                switch (leakType)
                {
                    case MemoryLeakType.Memory_Growth:
                        report.Recommendations.Add("Monitor memory usage patterns and implement memory pressure handling.");
                        break;
                    case MemoryLeakType.Resource_Leak:
                        report.Recommendations.Add("Ensure all IDisposable resources are properly disposed in using statements or try-finally blocks.");
                        break;
                    case MemoryLeakType.Event_Subscription_Leak:
                        report.Recommendations.Add("Use weak event patterns and ensure event handlers are unsubscribed when objects are disposed.");
                        break;
                    case MemoryLeakType.Type_Instance_Leak:
                        report.Recommendations.Add("Review object lifecycle management and ensure objects are not being retained unnecessarily.");
                        break;
                }
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the MemoryLeakDetectionService and cleans up resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method for proper disposal pattern.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        LoggingService.LogInfo("Disposing MemoryLeakDetectionService", "MemoryLeakDetectionService");

                        // Stop timers
                        _detectionTimer?.Dispose();
                        _snapshotTimer?.Dispose();

                        // Generate final report
                        var finalReport = PerformImmediateDetection();
                        LoggingService.LogInfo($"Final memory leak report: {finalReport.PotentialLeaks.Count} potential leaks detected", "MemoryLeakDetectionService");

                        // Clear collections
                        _memorySnapshots.Clear();
                        _typeMemoryTracking.Clear();

                        // Dispose process reference
                        _currentProcess?.Dispose();

                        LoggingService.LogInfo($"MemoryLeakDetectionService disposed. Final stats: {_totalSnapshots} snapshots, {_leaksDetected} leaks detected, {_alertsGenerated} alerts generated", "MemoryLeakDetectionService");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error during MemoryLeakDetectionService disposal: {ex.Message}", "MemoryLeakDetectionService");
                    }
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
