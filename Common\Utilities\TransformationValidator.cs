using System;
using System.Collections.Generic;
using System.Windows;
using UFU2.Services;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Validates transformation operations to ensure crop rectangles stay within image bounds,
    /// maintain the 127:145 aspect ratio, and meet minimum size requirements.
    /// Provides automatic correction capabilities for invalid transformations.
    /// Follows UFU2 patterns with comprehensive error handling and logging.
    /// </summary>
    public static class TransformationValidator
    {
        #region Constants

        /// <summary>
        /// Target aspect ratio for profile images (127:145)
        /// </summary>
        private const double TargetAspectRatio = 127.0 / 145.0;

        /// <summary>
        /// Tolerance for aspect ratio validation
        /// </summary>
        private const double AspectRatioTolerance = 0.001;

        /// <summary>
        /// Minimum crop size in pixels
        /// </summary>
        private const double MinimumCropSize = 50.0;

        /// <summary>
        /// Maximum crop size as percentage of image dimensions
        /// </summary>
        private const double MaximumCropPercentage = 0.95;

        #endregion

        #region Public Methods

        /// <summary>
        /// Validates a transformation operation including crop area bounds, aspect ratio, and size constraints.
        /// Provides automatic correction for invalid transformations when possible.
        /// </summary>
        /// <param name="originalImageSize">Size of the original image</param>
        /// <param name="backendState">Backend transformation state</param>
        /// <param name="finalCropArea">Final crop area in image coordinates</param>
        /// <returns>Validation result with errors, warnings, and corrections</returns>
        public static ValidationResult ValidateTransformation(
            Size originalImageSize,
            BackendTransformState backendState,
            Rect finalCropArea)
        {
            try
            {
                LoggingService.LogDebug($"Validating transformation - Image: {originalImageSize.Width}x{originalImageSize.Height}, " +
                    $"Crop: {finalCropArea}", "TransformationValidator");

                var result = new ValidationResult();

                // Validate input parameters
                if (!ValidateInputParameters(originalImageSize, finalCropArea, result))
                {
                    return result;
                }

                // Validate crop area bounds
                ValidateCropBounds(originalImageSize, finalCropArea, result);

                // Validate aspect ratio
                ValidateAspectRatio(finalCropArea, result);

                // Validate crop size constraints
                ValidateCropSize(finalCropArea, originalImageSize, result);

                // Validate backend transformation state
                ValidateBackendState(backendState, result);

                LoggingService.LogDebug($"Transformation validation completed - Errors: {result.Errors.Count}, " +
                    $"Warnings: {result.Warnings.Count}, Valid: {result.IsValid}", "TransformationValidator");

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating transformation: {ex.Message}", "TransformationValidator");
                
                var errorResult = new ValidationResult();
                errorResult.AddError($"خطأ في التحقق من صحة التحويل: {ex.Message}");
                return errorResult;
            }
        }

        /// <summary>
        /// Corrects a crop rectangle to fit within image bounds while maintaining aspect ratio.
        /// </summary>
        /// <param name="cropArea">Original crop area</param>
        /// <param name="imageBounds">Image boundary rectangle</param>
        /// <returns>Corrected crop area</returns>
        public static Rect CorrectCropAreaBounds(Rect cropArea, Rect imageBounds)
        {
            try
            {
                LoggingService.LogDebug($"Correcting crop area bounds - Original: {cropArea}, Bounds: {imageBounds}", 
                    "TransformationValidator");

                // Ensure crop area is within bounds
                double left = Math.Max(cropArea.Left, imageBounds.Left);
                double top = Math.Max(cropArea.Top, imageBounds.Top);
                double right = Math.Min(cropArea.Right, imageBounds.Right);
                double bottom = Math.Min(cropArea.Bottom, imageBounds.Bottom);

                // Check if the corrected area is too small
                if (right - left < MinimumCropSize || bottom - top < MinimumCropSize)
                {
                    LoggingService.LogWarning("Corrected crop area too small, using centered fallback", "TransformationValidator");
                    return CreateCenteredCropArea(imageBounds);
                }

                var correctedArea = new Rect(left, top, right - left, bottom - top);

                // Adjust to maintain aspect ratio
                correctedArea = AdjustAspectRatio(correctedArea, imageBounds);

                LoggingService.LogDebug($"Crop area corrected to: {correctedArea}", "TransformationValidator");

                return correctedArea;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error correcting crop area bounds: {ex.Message}", "TransformationValidator");
                return imageBounds;
            }
        }

        /// <summary>
        /// Adjusts a crop rectangle to maintain the target aspect ratio while staying within bounds.
        /// </summary>
        /// <param name="cropArea">Original crop area</param>
        /// <param name="bounds">Boundary rectangle</param>
        /// <returns>Aspect ratio corrected crop area</returns>
        public static Rect AdjustAspectRatio(Rect cropArea, Rect bounds)
        {
            try
            {
                LoggingService.LogDebug($"Adjusting aspect ratio - Crop: {cropArea}, Target: {TargetAspectRatio:F6}", 
                    "TransformationValidator");

                double currentAspectRatio = cropArea.Width / cropArea.Height;

                // Check if adjustment is needed
                if (Math.Abs(currentAspectRatio - TargetAspectRatio) <= AspectRatioTolerance)
                {
                    return cropArea; // Already correct
                }

                double newWidth, newHeight;

                if (currentAspectRatio > TargetAspectRatio)
                {
                    // Too wide, adjust height
                    newHeight = cropArea.Width / TargetAspectRatio;
                    newWidth = cropArea.Width;
                }
                else
                {
                    // Too tall, adjust width
                    newWidth = cropArea.Height * TargetAspectRatio;
                    newHeight = cropArea.Height;
                }

                // Center the adjusted rectangle
                double newX = cropArea.X + (cropArea.Width - newWidth) / 2;
                double newY = cropArea.Y + (cropArea.Height - newHeight) / 2;

                var adjustedArea = new Rect(newX, newY, newWidth, newHeight);

                // Ensure it fits within bounds
                if (!bounds.Contains(adjustedArea))
                {
                    adjustedArea = FitRectangleInBounds(adjustedArea, bounds, TargetAspectRatio);
                }

                LoggingService.LogDebug($"Aspect ratio adjusted to: {adjustedArea} (ratio: {adjustedArea.Width / adjustedArea.Height:F6})", 
                    "TransformationValidator");

                return adjustedArea;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adjusting aspect ratio: {ex.Message}", "TransformationValidator");
                return cropArea;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validates input parameters for transformation validation
        /// </summary>
        private static bool ValidateInputParameters(Size imageSize, Rect cropArea, ValidationResult result)
        {
            if (imageSize.Width <= 0 || imageSize.Height <= 0)
            {
                result.AddError("حجم الصورة غير صالح");
                return false;
            }

            if (cropArea.IsEmpty || cropArea.Width <= 0 || cropArea.Height <= 0)
            {
                result.AddError("منطقة القص غير صالحة");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Validates that the crop area is within image bounds
        /// </summary>
        private static void ValidateCropBounds(Size imageSize, Rect cropArea, ValidationResult result)
        {
            var imageBounds = new Rect(0, 0, imageSize.Width, imageSize.Height);

            if (!imageBounds.Contains(cropArea))
            {
                result.AddError("منطقة القص تتجاوز حدود الصورة");
                result.CorrectedCropArea = CorrectCropAreaBounds(cropArea, imageBounds);
            }
        }

        /// <summary>
        /// Validates the aspect ratio of the crop area
        /// </summary>
        private static void ValidateAspectRatio(Rect cropArea, ValidationResult result)
        {
            double actualAspectRatio = cropArea.Width / cropArea.Height;

            if (Math.Abs(actualAspectRatio - TargetAspectRatio) > AspectRatioTolerance)
            {
                result.AddWarning($"نسبة العرض إلى الارتفاع غير صحيحة: {actualAspectRatio:F6} بدلاً من {TargetAspectRatio:F6}");
            }
        }

        /// <summary>
        /// Validates crop size constraints
        /// </summary>
        private static void ValidateCropSize(Rect cropArea, Size imageSize, ValidationResult result)
        {
            // Check minimum size
            if (cropArea.Width < MinimumCropSize || cropArea.Height < MinimumCropSize)
            {
                result.AddError($"منطقة القص صغيرة جداً (الحد الأدنى: {MinimumCropSize} بكسل)");
            }

            // Check maximum size
            double maxWidth = imageSize.Width * MaximumCropPercentage;
            double maxHeight = imageSize.Height * MaximumCropPercentage;

            if (cropArea.Width > maxWidth || cropArea.Height > maxHeight)
            {
                result.AddWarning($"منطقة القص كبيرة جداً (الحد الأقصى: {MaximumCropPercentage * 100:F0}% من الصورة)");
            }
        }

        /// <summary>
        /// Validates backend transformation state with enhanced bidirectional rotation support
        /// </summary>
        private static void ValidateBackendState(BackendTransformState backendState, ValidationResult result)
        {
            if (backendState == null)
            {
                result.AddError("حالة التحويل الخلفية غير صالحة");
                return;
            }

            if (backendState.ZoomScale <= 0)
            {
                result.AddError("معامل التكبير غير صالح");
            }

            // Enhanced rotation angle validation for bidirectional support (-180° to +180°)
            if (double.IsNaN(backendState.RotationAngle) || double.IsInfinity(backendState.RotationAngle))
            {
                result.AddError("زاوية الدوران غير صالحة");
            }
            else if (backendState.RotationAngle < -180 || backendState.RotationAngle > 180)
            {
                result.AddWarning($"زاوية الدوران خارج النطاق المدعوم (-180° إلى +180°): {backendState.RotationAngle:F1}°");
            }
        }

        /// <summary>
        /// Creates a centered crop area within the given bounds
        /// </summary>
        private static Rect CreateCenteredCropArea(Rect bounds)
        {
            double width = Math.Min(bounds.Width * 0.8, bounds.Height * 0.8 * TargetAspectRatio);
            double height = width / TargetAspectRatio;

            double x = bounds.X + (bounds.Width - width) / 2;
            double y = bounds.Y + (bounds.Height - height) / 2;

            return new Rect(x, y, width, height);
        }

        /// <summary>
        /// Fits a rectangle within bounds while maintaining aspect ratio
        /// </summary>
        private static Rect FitRectangleInBounds(Rect rect, Rect bounds, double aspectRatio)
        {
            double maxWidth = bounds.Width;
            double maxHeight = bounds.Height;

            double width, height;

            if (aspectRatio > bounds.Width / bounds.Height)
            {
                // Constrained by width
                width = maxWidth;
                height = width / aspectRatio;
            }
            else
            {
                // Constrained by height
                height = maxHeight;
                width = height * aspectRatio;
            }

            double x = bounds.X + (bounds.Width - width) / 2;
            double y = bounds.Y + (bounds.Height - height) / 2;

            return new Rect(x, y, width, height);
        }

        /// <summary>
        /// Validates rotation angle bounds for bidirectional rotation support.
        /// </summary>
        /// <param name="rotationAngle">Rotation angle in degrees</param>
        /// <returns>Validation result with errors or warnings</returns>
        public static ValidationResult ValidateRotationBounds(double rotationAngle)
        {
            var result = new ValidationResult();

            try
            {
                if (double.IsNaN(rotationAngle) || double.IsInfinity(rotationAngle))
                {
                    result.AddError("زاوية الدوران غير صالحة - قيمة غير رقمية");
                    return result;
                }

                if (rotationAngle < -180 || rotationAngle > 180)
                {
                    result.AddError($"زاوية الدوران خارج النطاق المدعوم (-180° إلى +180°): {rotationAngle:F1}°");
                }
                else if (Math.Abs(rotationAngle) > 170)
                {
                    result.AddWarning($"زاوية الدوران قريبة من الحد الأقصى: {rotationAngle:F1}°");
                }

                LoggingService.LogDebug($"Rotation bounds validation completed - Angle: {rotationAngle:F1}°, Valid: {result.IsValid}",
                    "TransformationValidator");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating rotation bounds: {ex.Message}", "TransformationValidator");
                result.AddError($"خطأ في التحقق من حدود الدوران: {ex.Message}");
            }

            return result;
        }

        #endregion
    }

    /// <summary>
    /// Result of transformation validation with errors, warnings, and corrections
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// List of validation errors
        /// </summary>
        public List<string> Errors { get; } = new List<string>();

        /// <summary>
        /// List of validation warnings
        /// </summary>
        public List<string> Warnings { get; } = new List<string>();

        /// <summary>
        /// Corrected crop area if validation failed
        /// </summary>
        public Rect? CorrectedCropArea { get; set; }

        /// <summary>
        /// Whether the validation passed without errors
        /// </summary>
        public bool IsValid => Errors.Count == 0;

        /// <summary>
        /// Adds an error message to the result
        /// </summary>
        public void AddError(string message) => Errors.Add(message);

        /// <summary>
        /// Adds a warning message to the result
        /// </summary>
        public void AddWarning(string message) => Warnings.Add(message);
    }
}
