using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Utility class for calculating payment year ranges and generating year lists.
    /// Provides shared functionality for PaymentYearsSelectionDialog and auto-population features.
    /// Follows UFU2 patterns for date validation and year range calculation.
    /// </summary>
    public static class PaymentYearRangeCalculator
    {
        #region Public Methods

        /// <summary>
        /// Calculates the year range from the activity start date.
        /// Handles special "x" placeholder dates (xx/xx/YYYY) and full date formats.
        /// </summary>
        /// <param name="activityStartDate">The activity start date in DD/MM/YYYY format</param>
        /// <returns>A tuple containing the start and end years</returns>
        public static (int StartYear, int EndYear) CalculateYearRange(string activityStartDate)
        {
            var currentYear = DateTime.Now.Year;
            var startYear = currentYear; // Default to current year if parsing fails

            if (!string.IsNullOrEmpty(activityStartDate))
            {
                try
                {
                    // Handle special "x" placeholder dates (xx/xx/YYYY)
                    var dateParts = activityStartDate.Split('/');
                    if (dateParts.Length == 3)
                    {
                        // Try to parse the year part (last element)
                        if (int.TryParse(dateParts[2], out int yearFromDate))
                        {
                            startYear = yearFromDate;
                        }
                        else
                        {
                            // Try full date parsing as fallback
                            if (DateTime.TryParseExact(activityStartDate, "dd/MM/yyyy",
                                CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
                            {
                                startYear = parsedDate.Year;
                            }
                        }
                    }
                }
                catch
                {
                    // Use current year as fallback if parsing fails
                    startYear = currentYear;
                }
            }

            // Ensure start year is not in the future
            if (startYear > currentYear)
            {
                startYear = currentYear;
            }

            return (startYear, currentYear);
        }

        /// <summary>
        /// Validates if the provided activity start date is valid.
        /// Supports both full dates (DD/MM/YYYY) and placeholder dates (xx/xx/YYYY).
        /// </summary>
        /// <param name="activityStartDate">The activity start date to validate</param>
        /// <returns>True if the date is valid, false otherwise</returns>
        public static bool IsValidActivityStartDate(string activityStartDate)
        {
            if (string.IsNullOrWhiteSpace(activityStartDate))
            {
                return false;
            }

            try
            {
                // Handle special "x" placeholder dates (xx/xx/YYYY)
                var dateParts = activityStartDate.Split('/');
                if (dateParts.Length == 3)
                {
                    // Check if year part is valid
                    if (int.TryParse(dateParts[2], out int year))
                    {
                        return year >= 1900 && year <= DateTime.Now.Year;
                    }
                }

                // Try full date parsing
                return DateTime.TryParseExact(activityStartDate, "dd/MM/yyyy",
                    CultureInfo.InvariantCulture, DateTimeStyles.None, out _);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Generates a list of years from the activity start date to current year.
        /// Returns all years in the calculated range.
        /// </summary>
        /// <param name="activityStartDate">The activity start date in DD/MM/YYYY format</param>
        /// <returns>List of years from start year to current year</returns>
        public static List<int> GenerateYearList(string activityStartDate)
        {
            var yearRange = CalculateYearRange(activityStartDate);
            var years = new List<int>();

            for (int year = yearRange.StartYear; year <= yearRange.EndYear; year++)
            {
                years.Add(year);
            }

            return years;
        }

        /// <summary>
        /// Gets the last N years from the calculated year range in descending order.
        /// Used for auto-populating display text with recent years.
        /// Handles edge cases like year ranges with fewer than N years.
        /// </summary>
        /// <param name="activityStartDate">The activity start date in DD/MM/YYYY format</param>
        /// <param name="count">Number of recent years to return (default: 3)</param>
        /// <returns>List of the most recent years in descending order (may contain fewer than count if range is small)</returns>
        public static List<int> GetLastNYears(string activityStartDate, int count = 3)
        {
            var allYears = GenerateYearList(activityStartDate);
            return allYears.OrderByDescending(y => y).Take(count).ToList();
        }

        /// <summary>
        /// Formats a list of years for display text following UFU2 patterns.
        /// Returns years in descending order separated by commas.
        /// </summary>
        /// <param name="years">List of years to format</param>
        /// <returns>Formatted string like "2024, 2023, 2022" or "لم يتم التحديد بعد" if empty</returns>
        public static string FormatYearsForDisplay(List<int> years)
        {
            if (years == null || years.Count == 0)
            {
                return "لم يتم التحديد بعد";
            }

            var sortedYears = years.OrderByDescending(y => y).ToList();
            return string.Join(", ", sortedYears);
        }

        /// <summary>
        /// Auto-populates payment years based on activity start date.
        /// Returns all years from start date to current year for both G12 and BIS.
        /// </summary>
        /// <param name="activityStartDate">The activity start date in DD/MM/YYYY format</param>
        /// <returns>Tuple containing G12 and BIS year lists (both contain all years by default)</returns>
        public static (List<int> G12Years, List<int> BISYears) AutoPopulatePaymentYears(string activityStartDate)
        {
            if (!IsValidActivityStartDate(activityStartDate))
            {
                return (new List<int>(), new List<int>());
            }

            var allYears = GenerateYearList(activityStartDate);

            // Return all years for both G12 and BIS (following new default behavior)
            return (new List<int>(allYears), new List<int>(allYears));
        }
        /// <summary>
        /// Calculates a single list of payment years from start date to current year.
        /// Convenience wrapper used by ActivityManagementViewModel.
        /// </summary>
        /// <param name="activityStartDate">The activity start date</param>
        /// <returns>List of years from start to current year; empty if invalid</returns>
        public static List<int> CalculatePaymentYears(string activityStartDate)
        {
            if (!IsValidActivityStartDate(activityStartDate))
            {
                return new List<int>();
            }
            return GenerateYearList(activityStartDate);
        }


        #endregion
    }
}
