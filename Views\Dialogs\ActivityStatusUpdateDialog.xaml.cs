using System;
using System.Windows;
using System.Windows.Controls;
using UFU2.Common;
using UFU2.Common.Extensions;
using UFU2.ViewModels;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for ActivityStatusUpdateDialog.xaml
    /// A UserControl that provides a dialog for updating activity status information.
    /// Integrates with MaterialDesign DialogHost for consistent modal behavior
    /// and follows UFU2 design patterns with Arabic RTL layout support.
    /// </summary>
    public partial class ActivityStatusUpdateDialog : UserControl
    {
        #region Private Fields
        private ActivityStatusUpdateDialogViewModel _viewModel;
        private bool _dialogResult = false;
        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the dialog result indicating whether the user saved or cancelled.
        /// </summary>
        public bool DialogResult => _dialogResult;

        /// <summary>
        /// Gets the update date entered by the user.
        /// </summary>
        public string UpdateDate => _viewModel?.UpdateDate ?? string.Empty;

        /// <summary>
        /// Gets the update note entered by the user.
        /// </summary>
        public string UpdateNote => _viewModel?.UpdateNote ?? string.Empty;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the ActivityStatusUpdateDialog class.
        /// </summary>
        public ActivityStatusUpdateDialog()
        {
            InitializeComponent();
            InitializeViewModel(new ActivityStatusUpdateDialogViewModel());
        }

        /// <summary>
        /// Initializes a new instance with existing data.
        /// </summary>
        /// <param name="existingDate">Existing update date</param>
        /// <param name="existingNote">Existing update note</param>
        /// <param name="activityStatus">Current activity status</param>
        /// <param name="activityType">Current activity type</param>
        public ActivityStatusUpdateDialog(string existingDate, string existingNote, string activityStatus, string activityType)
        {
            InitializeComponent();
            InitializeViewModel(new ActivityStatusUpdateDialogViewModel(existingDate, existingNote, activityStatus, activityType));
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes the ViewModel and sets up event handlers.
        /// </summary>
        /// <param name="viewModel">The ViewModel instance to use</param>
        private void InitializeViewModel(ActivityStatusUpdateDialogViewModel viewModel)
        {
            try
            {
                _viewModel = viewModel;
                DataContext = _viewModel;

                // Subscribe to ViewModel events
                _viewModel.SaveRequested += OnSaveRequested;
                _viewModel.CancelRequested += OnCancelRequested;

                // Set up date formatting for the UpdateDateTextBox
                TextBoxExtensions.AttachDateFormatting(UpdateDateTextBox);

                // Set initial focus to the date textbox
                Loaded += (s, e) => UpdateDateTextBox.Focus();

                LoggingService.LogDebug("ActivityStatusUpdateDialog initialized successfully", "ActivityStatusUpdateDialog");
            }
            catch (Exception ex)
            {
                ErrorManager.LogException(ex, LogLevel.Error, "ActivityStatusUpdateDialog");
                ErrorManager.ShowUserError("حدث خطأ أثناء تحميل نافذة تحديث معلومات النشاط", "خطأ");
            }
        }

        /// <summary>
        /// Handles the save request from the ViewModel.
        /// </summary>
        private void OnSaveRequested()
        {
            try
            {
                if (_viewModel?.IsValid == true)
                {
                    _dialogResult = true;
                    LoggingService.LogDebug($"Activity status update saved - Date: {_viewModel.UpdateDate}, Note length: {_viewModel.UpdateNote?.Length ?? 0}", "ActivityStatusUpdateDialog");
                    CloseDialog(true);
                }
                else
                {
                    LoggingService.LogWarning("Save requested but ViewModel validation failed", "ActivityStatusUpdateDialog");
                    ErrorManager.ShowUserErrorToast("يرجى التأكد من صحة البيانات المدخلة", "خطأ في التحقق");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling save request: {ex.Message}", "ActivityStatusUpdateDialog");
                ErrorManager.LogException(ex, LogLevel.Error, "ActivityStatusUpdateDialog");
                ErrorManager.ShowUserError("حدث خطأ أثناء حفظ معلومات التحديث", "خطأ");
            }
        }

        /// <summary>
        /// Handles the cancel request from the ViewModel.
        /// </summary>
        private void OnCancelRequested()
        {
            try
            {
                // Check if fields are empty and set default values if needed
                if (_viewModel?.ShouldUseDefaults() == true)
                {
                    var (defaultDate, defaultNote) = _viewModel.GetDefaultValues();
                    _viewModel.UpdateDate = defaultDate;
                    _viewModel.UpdateNote = defaultNote;
                    
                    LoggingService.LogDebug("Activity status update cancelled with default values applied", "ActivityStatusUpdateDialog");
                }
                else
                {
                    LoggingService.LogDebug("Activity status update cancelled, keeping original values", "ActivityStatusUpdateDialog");
                }

                _dialogResult = false;
                CloseDialog(false);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling cancel request: {ex.Message}", "ActivityStatusUpdateDialog");
                ErrorManager.LogException(ex, LogLevel.Error, "ActivityStatusUpdateDialog");
                CloseDialog(false);
            }
        }

        /// <summary>
        /// Closes the dialog with the specified result.
        /// </summary>
        /// <param name="result">The dialog result</param>
        private void CloseDialog(bool result)
        {
            try
            {
                // Unsubscribe from ViewModel events to prevent memory leaks
                if (_viewModel != null)
                {
                    _viewModel.SaveRequested -= OnSaveRequested;
                    _viewModel.CancelRequested -= OnCancelRequested;
                }

                // Close the dialog using MaterialDesign DialogHost
                MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(result, this);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing dialog: {ex.Message}", "ActivityStatusUpdateDialog");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the final update data after dialog completion.
        /// Should be called after the dialog closes with DialogResult = true.
        /// </summary>
        /// <returns>Tuple containing the update date and note</returns>
        public (string UpdateDate, string UpdateNote) GetUpdateData()
        {
            if (_viewModel == null)
                return (string.Empty, string.Empty);

            return (_viewModel.UpdateDate ?? string.Empty, _viewModel.UpdateNote ?? string.Empty);
        }

        /// <summary>
        /// Validates the current input without closing the dialog.
        /// </summary>
        /// <returns>True if all input is valid</returns>
        public bool ValidateInput()
        {
            return _viewModel?.IsValid == true;
        }

        #endregion
    }
}
