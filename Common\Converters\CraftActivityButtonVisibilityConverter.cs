using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that controls the visibility of craft-specific buttons based on the selected activity type.
    /// Shows craft-specific buttons (search and information) only when the Craft activity tab is selected.
    /// Returns Visibility.Visible for Craft activity type, Visibility.Collapsed for others.
    /// </summary>
    public class CraftActivityButtonVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts the selected activity type to a Visibility value for craft-specific buttons.
        /// </summary>
        /// <param name="value">The selected activity type (MainCommercial, SecondaryCommercial, Craft, Professional)</param>
        /// <param name="targetType">The target type (should be Visibility)</param>
        /// <param name="parameter">Optional parameter for button type (Search, Information)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Visibility.Visible if craft buttons should be shown, Visibility.Collapsed otherwise</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var activityType = value?.ToString() ?? "MainCommercial";
                var buttonType = parameter?.ToString() ?? string.Empty;

                // Only show craft buttons for Craft activity type
                if (activityType == "Craft")
                {
                    return Visibility.Visible;
                }

                return Visibility.Collapsed;
            }
            catch (Exception)
            {
                // Default to collapsed if any error occurs
                return Visibility.Collapsed;
            }
        }

        /// <summary>
        /// Not implemented for this converter as it's one-way only.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("CraftActivityButtonVisibilityConverter is a one-way converter.");
        }

        /// <summary>
        /// Determines whether craft-specific buttons should be visible for the given activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>True if craft buttons should be visible, false otherwise</returns>
        public static bool ShouldShowCraftButtons(string activityType)
        {
            return activityType == "Craft";
        }

        /// <summary>
        /// Gets user-friendly explanation for craft button functionality.
        /// This can be used for tooltips or help text.
        /// </summary>
        /// <param name="buttonType">The type of craft button (Search, Information)</param>
        /// <returns>Arabic explanation text for craft button functionality</returns>
        public static string GetCraftButtonExplanation(string buttonType)
        {
            return buttonType switch
            {
                "Search" => "البحث في قاعدة بيانات أنواع الحرف",
                "Information" => "عرض معلومات إضافية عن الحرفة المحددة",
                _ => "وظائف إضافية للأنشطة الحرفية"
            };
        }

        /// <summary>
        /// Gets the appropriate tooltip text for craft buttons.
        /// </summary>
        /// <param name="buttonType">The type of craft button (Search, Information)</param>
        /// <returns>Appropriate Arabic tooltip for the craft button</returns>
        public static string GetCraftButtonTooltip(string buttonType)
        {
            return buttonType switch
            {
                "Search" => "البحث عن أنواع الحرف",
                "Information" => "معلومات إضافية عن الحرفة",
                _ => "وظائف الحرف"
            };
        }
    }
}
