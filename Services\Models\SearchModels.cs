using System;
using System.Collections.Generic;

namespace UFU2.Services.Models
{
    /// <summary>
    /// Enhanced search result with word frequency-based ranking and detailed match information.
    /// Supports both the existing search functionality and new Arabic text analysis features.
    /// </summary>
    /// <typeparam name="T">Type of the search result item</typeparam>
    public class EnhancedSearchResult<T>
    {
        /// <summary>
        /// The actual item that matched the search criteria.
        /// </summary>
        public T Item { get; set; }

        /// <summary>
        /// Overall similarity score between 0.0 and 1.0.
        /// </summary>
        public double SimilarityScore { get; set; }

        /// <summary>
        /// Word frequency-based relevance score between 0.0 and 1.0.
        /// </summary>
        public double RelevanceScore { get; set; }

        /// <summary>
        /// Type of match found (exact, starts with, contains, etc.).
        /// </summary>
        public MatchType MatchType { get; set; }

        /// <summary>
        /// Original text from the item that was searched.
        /// </summary>
        public string OriginalText { get; set; } = string.Empty;

        /// <summary>
        /// Normalized version of the text used for matching.
        /// </summary>
        public string NormalizedText { get; set; } = string.Empty;

        /// <summary>
        /// Number of exact word matches found.
        /// </summary>
        public int ExactWordMatches { get; set; }

        /// <summary>
        /// Number of partial word matches found.
        /// </summary>
        public int PartialWordMatches { get; set; }

        /// <summary>
        /// Total number of search terms that were matched.
        /// </summary>
        public int TotalMatchedTerms { get; set; }

        /// <summary>
        /// Array of individual words that matched the search criteria.
        /// </summary>
        public string[] MatchedWords { get; set; } = Array.Empty<string>();

        /// <summary>
        /// Detailed match information for debugging and analysis.
        /// </summary>
        public MatchInfo[] MatchDetails { get; set; } = Array.Empty<MatchInfo>();

        /// <summary>
        /// Indicates whether this result was processed using Arabic text analysis.
        /// </summary>
        public bool UsedArabicAnalysis { get; set; }
    }

    /// <summary>
    /// Detailed information about a specific match within the search result.
    /// </summary>
    public class MatchInfo
    {
        /// <summary>
        /// The search term that produced this match.
        /// </summary>
        public string SearchTerm { get; set; } = string.Empty;

        /// <summary>
        /// The matched text from the target item.
        /// </summary>
        public string MatchedText { get; set; } = string.Empty;

        /// <summary>
        /// Type of match (exact, partial, etc.).
        /// </summary>
        public MatchType MatchType { get; set; }

        /// <summary>
        /// Position where the match was found in the text.
        /// </summary>
        public int Position { get; set; }

        /// <summary>
        /// Length of the matched text.
        /// </summary>
        public int Length { get; set; }

        /// <summary>
        /// Confidence score for this specific match.
        /// </summary>
        public double MatchScore { get; set; }
    }

    /// <summary>
    /// Enumeration of match types for result ranking.
    /// Ordered by priority (lower values = higher priority).
    /// </summary>
    public enum MatchType
    {
        /// <summary>
        /// Exact match of the entire search term.
        /// </summary>
        Exact = 0,

        /// <summary>
        /// Text starts with the search term.
        /// </summary>
        StartsWith = 1,

        /// <summary>
        /// Text contains the search term.
        /// </summary>
        Contains = 2,

        /// <summary>
        /// Word-level exact match.
        /// </summary>
        WordExact = 3,

        /// <summary>
        /// Partial word match.
        /// </summary>
        WordPartial = 4,

        /// <summary>
        /// Multiple word matches.
        /// </summary>
        MultipleWords = 5,

        /// <summary>
        /// Fuzzy match (deprecated - not used in word frequency ranking).
        /// </summary>
        Fuzzy = 6
    }

    /// <summary>
    /// Search request parameters for enhanced search operations.
    /// </summary>
    public class SearchRequest
    {
        /// <summary>
        /// The search term or query.
        /// </summary>
        public string SearchTerm { get; set; } = string.Empty;

        /// <summary>
        /// Maximum number of results to return.
        /// </summary>
        public int MaxResults { get; set; } = 50;

        /// <summary>
        /// Minimum similarity score for results to be included.
        /// </summary>
        public double MinSimilarity { get; set; } = 0.3;

        /// <summary>
        /// Whether to use Arabic text analysis for this search.
        /// </summary>
        public bool UseArabicAnalysis { get; set; } = true;

        /// <summary>
        /// Whether to include partial word matches.
        /// </summary>
        public bool IncludePartialMatches { get; set; } = true;

        /// <summary>
        /// Whether to filter out stop words from search terms.
        /// </summary>
        public bool FilterStopWords { get; set; } = true;

        /// <summary>
        /// Custom search options for advanced scenarios.
        /// </summary>
        public Dictionary<string, object> Options { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Collection of search results with metadata and performance information.
    /// </summary>
    /// <typeparam name="T">Type of the search result items</typeparam>
    public class SearchResultCollection<T>
    {
        /// <summary>
        /// Array of search results ordered by relevance.
        /// </summary>
        public EnhancedSearchResult<T>[] Results { get; set; } = Array.Empty<EnhancedSearchResult<T>>();

        /// <summary>
        /// Total number of items that were searched.
        /// </summary>
        public int TotalSearched { get; set; }

        /// <summary>
        /// Number of items that matched the search criteria.
        /// </summary>
        public int TotalMatches { get; set; }

        /// <summary>
        /// Time taken to execute the search.
        /// </summary>
        public TimeSpan ExecutionTime { get; set; }

        /// <summary>
        /// Whether the results were retrieved from cache.
        /// </summary>
        public bool FromCache { get; set; }

        /// <summary>
        /// Search term that was used for this search.
        /// </summary>
        public string SearchTerm { get; set; } = string.Empty;

        /// <summary>
        /// Normalized search terms that were actually used for matching.
        /// </summary>
        public string[] NormalizedSearchTerms { get; set; } = Array.Empty<string>();

        /// <summary>
        /// Whether Arabic text analysis was used for this search.
        /// </summary>
        public bool UsedArabicAnalysis { get; set; }

        /// <summary>
        /// Additional metadata about the search operation.
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }
}
