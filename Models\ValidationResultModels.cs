using System;
using System.Collections.Generic;
using System.Linq;

namespace UFU2.Models
{
    /// <summary>
    /// Simple validation result class for general validation operations.
    /// Provides field-specific error tracking and validation status.
    /// </summary>
    public class GeneralValidationResult
    {
        private readonly Dictionary<string, List<string>> _errors = new();

        /// <summary>
        /// Gets whether the validation is valid (no errors).
        /// </summary>
        public bool IsValid => !_errors.Any();

        /// <summary>
        /// Gets the validation errors by field.
        /// </summary>
        public IReadOnlyDictionary<string, List<string>> Errors => _errors;

        /// <summary>
        /// Adds an error for a specific field.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <param name="message">The error message</param>
        public void AddError(string field, string message)
        {
            if (string.IsNullOrWhiteSpace(field) || string.IsNullOrWhiteSpace(message))
                return;

            if (!_errors.ContainsKey(field))
                _errors[field] = new List<string>();

            _errors[field].Add(message);
        }

        /// <summary>
        /// Merges another validation result into this one.
        /// </summary>
        /// <param name="other">The other validation result to merge</param>
        public void Merge(GeneralValidationResult other)
        {
            if (other == null)
                return;

            foreach (var error in other.Errors)
            {
                foreach (var message in error.Value)
                {
                    AddError(error.Key, message);
                }
            }
        }

        /// <summary>
        /// Gets all errors as a formatted string.
        /// </summary>
        /// <returns>Formatted error string</returns>
        public string GetErrorsAsString()
        {
            if (IsValid)
                return string.Empty;

            var errorMessages = new List<string>();
            foreach (var error in _errors)
            {
                foreach (var message in error.Value)
                {
                    errorMessages.Add($"{error.Key}: {message}");
                }
            }

            return string.Join(Environment.NewLine, errorMessages);
        }

        /// <summary>
        /// Clears all validation errors.
        /// </summary>
        public void Clear()
        {
            _errors.Clear();
        }
    }

    /// <summary>
    /// Base class for validation results with common properties and methods.
    /// </summary>
    public abstract class BaseValidationResult
    {
        /// <summary>
        /// Indicates if the validation passed.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation errors.
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// List of validation warnings.
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// Additional validation details.
        /// </summary>
        public List<string> ValidationDetails { get; set; } = new List<string>();

        /// <summary>
        /// Time when validation was performed (UTC).
        /// </summary>
        public DateTime ValidationTime { get; set; }

        /// <summary>
        /// Gets the total number of issues (errors + warnings).
        /// </summary>
        public int TotalIssues => Errors.Count + Warnings.Count;

        /// <summary>
        /// Gets a summary of the validation result.
        /// </summary>
        /// <returns>Validation summary string</returns>
        public virtual string GetSummary()
        {
            var status = IsValid ? "PASSED" : "FAILED";
            return $"Validation {status}: {Errors.Count} errors, {Warnings.Count} warnings";
        }

        /// <summary>
        /// Gets all issues as a formatted string.
        /// </summary>
        /// <returns>Formatted issues string</returns>
        public string GetIssuesAsString()
        {
            var issues = new List<string>();
            
            if (Errors.Any())
            {
                issues.Add("ERRORS:");
                issues.AddRange(Errors.Select(e => $"  - {e}"));
            }

            if (Warnings.Any())
            {
                if (issues.Any()) issues.Add("");
                issues.Add("WARNINGS:");
                issues.AddRange(Warnings.Select(w => $"  - {w}"));
            }

            return string.Join(Environment.NewLine, issues);
        }
    }

    /// <summary>
    /// Represents the result of UID generation validation.
    /// </summary>
    public class UIDValidationResult : BaseValidationResult
    {
        /// <summary>
        /// Number of Client UIDs validated.
        /// </summary>
        public int ClientUIDsValidated { get; set; }

        /// <summary>
        /// Number of Activity UIDs validated.
        /// </summary>
        public int ActivityUIDsValidated { get; set; }

        /// <summary>
        /// Number of UID sequences validated.
        /// </summary>
        public int SequencesValidated { get; set; }



        /// <summary>
        /// Gets a detailed summary of UID validation.
        /// </summary>
        /// <returns>Detailed summary string</returns>
        public override string GetSummary()
        {
            var baseSummary = base.GetSummary();
            return $"{baseSummary}. Validated {ClientUIDsValidated} client UIDs, {ActivityUIDsValidated} activity UIDs, {SequencesValidated} sequences.";
        }
    }

    /// <summary>
    /// Represents the result of database operation validation.
    /// </summary>
    public class DatabaseOperationValidationResult : BaseValidationResult
    {


        /// <summary>
        /// Results of data consistency checks.
        /// </summary>
        public List<string> ConsistencyCheckResults { get; set; } = new List<string>();

        /// <summary>
        /// Gets a detailed summary of database operation validation.
        /// </summary>
        /// <returns>Detailed summary string</returns>
        public override string GetSummary()
        {
            var baseSummary = base.GetSummary();
            var checkCount = ConsistencyCheckResults.Count;
            return $"{baseSummary}. Performed {checkCount} database operation checks.";
        }
    }

    /// <summary>
    /// Represents the result of business rule validation.
    /// </summary>
    public class BusinessRuleValidationResult : BaseValidationResult
    {
        /// <summary>
        /// Number of activities validated for type rules.
        /// </summary>
        public int ActivitiesValidated { get; set; }

        /// <summary>
        /// Number of file check rules validated.
        /// </summary>
        public int FileCheckRulesValidated { get; set; }

        /// <summary>
        /// Number of phone number rules validated.
        /// </summary>
        public int PhoneNumberRulesValidated { get; set; }

        /// <summary>
        /// Number of payment year rules validated.
        /// </summary>
        public int PaymentYearRulesValidated { get; set; }

        /// <summary>
        /// Activity type rule violations.
        /// </summary>
        public Dictionary<string, List<string>> ActivityTypeViolations { get; set; } = new Dictionary<string, List<string>>();

        /// <summary>
        /// File check rule violations by activity type.
        /// </summary>
        public Dictionary<string, List<string>> FileCheckViolations { get; set; } = new Dictionary<string, List<string>>();

        /// <summary>
        /// Gets a detailed summary of business rule validation.
        /// </summary>
        /// <returns>Detailed summary string</returns>
        public override string GetSummary()
        {
            var baseSummary = base.GetSummary();
            return $"{baseSummary}. Validated {ActivitiesValidated} activities, {FileCheckRulesValidated} file check rules, " +
                   $"{PhoneNumberRulesValidated} phone number rules, {PaymentYearRulesValidated} payment year rules.";
        }
    }

    /// <summary>
    /// Represents the result of comprehensive database validation.
    /// </summary>
    public class ComprehensiveValidationResult
    {
        /// <summary>
        /// Start time of the comprehensive validation (UTC).
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End time of the comprehensive validation (UTC).
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Total duration of the validation in milliseconds.
        /// </summary>
        public long TotalDurationMs { get; set; }

        /// <summary>
        /// Indicates if the overall validation passed.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Error message if the validation failed with an exception.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Schema validation result.
        /// </summary>
        public SchemaValidationResult? SchemaValidation { get; set; }

        /// <summary>
        /// UID validation result.
        /// </summary>
        public UIDValidationResult? UIDValidation { get; set; }

        /// <summary>
        /// Database operation validation result.
        /// </summary>
        public DatabaseOperationValidationResult? DatabaseOperationValidation { get; set; }

        /// <summary>
        /// Business rule validation result.
        /// </summary>
        public BusinessRuleValidationResult? BusinessRuleValidation { get; set; }

        /// <summary>
        /// Gets the total number of errors across all validations.
        /// </summary>
        public int TotalErrors
        {
            get
            {
                var total = 0;
                if (SchemaValidation != null) total += SchemaValidation.Errors.Count;
                if (UIDValidation != null) total += UIDValidation.Errors.Count;
                if (DatabaseOperationValidation != null) total += DatabaseOperationValidation.Errors.Count;
                if (BusinessRuleValidation != null) total += BusinessRuleValidation.Errors.Count;
                return total;
            }
        }

        /// <summary>
        /// Gets the total number of warnings across all validations.
        /// </summary>
        public int TotalWarnings
        {
            get
            {
                var total = 0;
                if (SchemaValidation != null) total += SchemaValidation.Warnings.Count;
                if (UIDValidation != null) total += UIDValidation.Warnings.Count;
                if (DatabaseOperationValidation != null) total += DatabaseOperationValidation.Warnings.Count;
                if (BusinessRuleValidation != null) total += BusinessRuleValidation.Warnings.Count;
                return total;
            }
        }

        /// <summary>
        /// Gets a comprehensive summary of all validation results.
        /// </summary>
        /// <returns>Comprehensive summary string</returns>
        public string GetComprehensiveSummary()
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                return $"Comprehensive validation FAILED with exception: {ErrorMessage}";
            }

            var status = IsValid ? "PASSED" : "FAILED";
            var summary = $"Comprehensive validation {status} in {TotalDurationMs}ms. " +
                         $"Total: {TotalErrors} errors, {TotalWarnings} warnings.";

            var details = new List<string>();

            if (SchemaValidation != null)
            {
                var schemaStatus = SchemaValidation.IsValid ? "PASSED" : "FAILED";
                details.Add($"Schema: {schemaStatus} ({SchemaValidation.Errors.Count} errors, {SchemaValidation.Warnings.Count} warnings)");
            }

            if (UIDValidation != null)
            {
                var uidStatus = UIDValidation.IsValid ? "PASSED" : "FAILED";
                details.Add($"UID Generation: {uidStatus} ({UIDValidation.Errors.Count} errors, {UIDValidation.Warnings.Count} warnings)");
            }

            if (DatabaseOperationValidation != null)
            {
                var dbOpStatus = DatabaseOperationValidation.IsValid ? "PASSED" : "FAILED";
                details.Add($"Database Operations: {dbOpStatus} ({DatabaseOperationValidation.Errors.Count} errors, {DatabaseOperationValidation.Warnings.Count} warnings)");
            }

            if (BusinessRuleValidation != null)
            {
                var businessStatus = BusinessRuleValidation.IsValid ? "PASSED" : "FAILED";
                details.Add($"Business Rules: {businessStatus} ({BusinessRuleValidation.Errors.Count} errors, {BusinessRuleValidation.Warnings.Count} warnings)");
            }

            if (details.Any())
            {
                summary += Environment.NewLine + string.Join(Environment.NewLine, details);
            }

            return summary;
        }

        /// <summary>
        /// Gets all validation issues as a formatted report.
        /// </summary>
        /// <returns>Formatted validation report</returns>
        public string GetDetailedReport()
        {
            var report = new List<string>
            {
                "=== COMPREHENSIVE DATABASE VALIDATION REPORT ===",
                $"Validation Time: {StartTime:yyyy-MM-dd HH:mm:ss} UTC",
                $"Duration: {TotalDurationMs}ms",
                $"Overall Status: {(IsValid ? "PASSED" : "FAILED")}",
                $"Total Issues: {TotalErrors} errors, {TotalWarnings} warnings",
                ""
            };

            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                report.Add($"EXCEPTION: {ErrorMessage}");
                report.Add("");
            }

            // Schema validation details
            if (SchemaValidation != null)
            {
                report.Add("--- SCHEMA VALIDATION ---");
                report.Add(SchemaValidation.GetSummary());
                if (SchemaValidation.Errors.Any() || SchemaValidation.Warnings.Any())
                {
                    report.Add(SchemaValidation.GetIssuesAsString());
                }
                report.Add("");
            }

            // UID validation details
            if (UIDValidation != null)
            {
                report.Add("--- UID GENERATION VALIDATION ---");
                report.Add(UIDValidation.GetSummary());
                if (UIDValidation.Errors.Any() || UIDValidation.Warnings.Any())
                {
                    report.Add(UIDValidation.GetIssuesAsString());
                }
                report.Add("");
            }

            // Database operation validation details
            if (DatabaseOperationValidation != null)
            {
                report.Add("--- DATABASE OPERATION VALIDATION ---");
                report.Add(DatabaseOperationValidation.GetSummary());
                if (DatabaseOperationValidation.Errors.Any() || DatabaseOperationValidation.Warnings.Any())
                {
                    report.Add(DatabaseOperationValidation.GetIssuesAsString());
                }
                report.Add("");
            }

            // Business rule validation details
            if (BusinessRuleValidation != null)
            {
                report.Add("--- BUSINESS RULE VALIDATION ---");
                report.Add(BusinessRuleValidation.GetSummary());
                if (BusinessRuleValidation.Errors.Any() || BusinessRuleValidation.Warnings.Any())
                {
                    report.Add(BusinessRuleValidation.GetIssuesAsString());
                }
                report.Add("");
            }

            report.Add("=== END OF REPORT ===");

            return string.Join(Environment.NewLine, report);
        }
    }

    /// <summary>
    /// Represents the result of database schema validation.
    /// </summary>
    public class SchemaValidationResult : BaseValidationResult
    {
        /// <summary>
        /// Number of tables validated.
        /// </summary>
        public int TablesValidated { get; set; }

        /// <summary>
        /// Number of indexes validated.
        /// </summary>
        public int IndexesValidated { get; set; }

        /// <summary>
        /// Number of foreign key constraints validated.
        /// </summary>
        public int ForeignKeysValidated { get; set; }

        /// <summary>
        /// Number of check constraints validated.
        /// </summary>
        public int CheckConstraintsValidated { get; set; }

        /// <summary>
        /// Schema validation details by category.
        /// </summary>
        public Dictionary<string, List<string>> ValidationDetailsByCategory { get; set; } = new Dictionary<string, List<string>>();

        /// <summary>
        /// Gets a detailed summary of schema validation.
        /// </summary>
        /// <returns>Detailed summary string</returns>
        public override string GetSummary()
        {
            var baseSummary = base.GetSummary();
            return $"{baseSummary}. Validated {TablesValidated} tables, {IndexesValidated} indexes, " +
                   $"{ForeignKeysValidated} foreign keys, {CheckConstraintsValidated} check constraints.";
        }
    }

    /// <summary>
    /// Represents performance monitoring validation results.
    /// </summary>
    public class PerformanceMonitoringValidationResult : BaseValidationResult
    {
        /// <summary>
        /// Number of queries analyzed.
        /// </summary>
        public int QueriesAnalyzed { get; set; }

        /// <summary>
        /// Number of slow queries detected.
        /// </summary>
        public int SlowQueriesDetected { get; set; }

        /// <summary>
        /// Number of indexes analyzed.
        /// </summary>
        public int IndexesAnalyzed { get; set; }

        /// <summary>
        /// Number of unused indexes found.
        /// </summary>
        public int UnusedIndexesFound { get; set; }

        /// <summary>
        /// Performance recommendations.
        /// </summary>
        public List<string> PerformanceRecommendations { get; set; } = new List<string>();

        /// <summary>
        /// Query optimization suggestions.
        /// </summary>
        public List<string> QueryOptimizationSuggestions { get; set; } = new List<string>();

        /// <summary>
        /// Index optimization suggestions.
        /// </summary>
        public List<string> IndexOptimizationSuggestions { get; set; } = new List<string>();

        /// <summary>
        /// Gets a detailed summary of performance monitoring validation.
        /// </summary>
        /// <returns>Detailed summary string</returns>
        public override string GetSummary()
        {
            var baseSummary = base.GetSummary();
            return $"{baseSummary}. Analyzed {QueriesAnalyzed} queries ({SlowQueriesDetected} slow), " +
                   $"{IndexesAnalyzed} indexes ({UnusedIndexesFound} unused). " +
                   $"Generated {PerformanceRecommendations.Count} recommendations.";
        }
    }
}