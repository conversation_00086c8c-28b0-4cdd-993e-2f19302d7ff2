<UserControl
    x:Class="UFU2.Views.UserControls.ClientProfileImage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:commonConverters="clr-namespace:UFU2.Common.Converters"
    xmlns:converters="clr-namespace:UFU2.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    x:Name="ProfileImageControl"
    Width="127"
    Height="145"
    AutomationProperties.HelpText="Profile image control with click to edit functionality"
    AutomationProperties.ItemType="Button"
    AutomationProperties.Name="Profile Image"
    FlowDirection="RightToLeft"
    Focusable="True"
    IsTabStop="True"
    SnapsToDevicePixels="True"
    UseLayoutRounding="True"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  Profile Image Converter for handling both custom and gender-based images  -->
            <converters:ProfileImageConverter x:Key="ProfileImageConverter" />

            <!--  Boolean to Visibility Converters  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!--  Inverse Boolean to Visibility Converter  -->
            <commonConverters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <!--  Main Button Container with MaterialDesign styling  -->
    <Button
        x:Name="ProfileImageButton"
        Width="127"
        Height="145"
        Padding="0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        AutomationProperties.HelpText="Click to manage profile image"
        AutomationProperties.Name="Profile Image Button"
        Background="Transparent"
        BorderBrush="{DynamicResource MaterialDesignDivider}"
        BorderThickness="1"
        Click="ProfileImageButton_Click"
        Cursor="Hand"
        Style="{StaticResource MaterialDesignFlatButton}"
        ToolTip="انقر لإدارة الصورة الشخصية">

        <!--  Button Content Grid  -->
        <Grid>
            <!--  Profile Image Display - OPTIMIZED: Changed to Fant for better performance  -->
            <Image
                x:Name="ProfileImageDisplay"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                RenderOptions.BitmapScalingMode="Fant"
                RenderOptions.CachingHint="Cache"
                Stretch="UniformToFill"
                StretchDirection="Both">
                <Image.Source>
                    <MultiBinding Converter="{StaticResource ProfileImageConverter}" FallbackValue="{x:Null}">
                        <Binding Path="ProfileImageSource" RelativeSource="{RelativeSource AncestorType=UserControl}" />
                        <Binding Path="Gender" RelativeSource="{RelativeSource AncestorType=UserControl}" />
                    </MultiBinding>
                </Image.Source>
            </Image>

            <!--  Hover Overlay with MaterialDesign styling  -->
            <Border
                x:Name="HoverOverlay"
                Background="{DynamicResource MaterialDesignPaper}"
                Opacity="0"
                RenderTransformOrigin="0.5,0.5">

                <Border.RenderTransform>
                    <ScaleTransform ScaleX="1.0" ScaleY="1.0" />
                </Border.RenderTransform>

                <!--  Hover Content  -->
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  Camera Icon  -->
                    <materialDesign:PackIcon
                        Grid.Row="1"
                        Width="32"
                        Height="32"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        Kind="Camera" />

                    <!--  Arabic Text  -->
                    <TextBlock
                        Grid.Row="2"
                        Margin="0,4,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Top"
                        FontFamily="{DynamicResource MaterialDesignFont}"
                        FontSize="12"
                        FontWeight="Medium"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        Text="إدارة الصورة"
                        TextAlignment="Center" />
                </Grid>
            </Border>

            <!--  Loading Indicator (hidden by default)  -->
            <Grid
                x:Name="LoadingIndicator"
                Background="{DynamicResource MaterialDesignPaper}"
                Opacity="0.9"
                Visibility="Collapsed">

                <ProgressBar
                    Width="40"
                    Height="40"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    IsIndeterminate="True"
                    Style="{StaticResource MaterialDesignCircularProgressBar}" />
            </Grid>
        </Grid>

        <!--  Button Style Triggers for Hover Effects  -->
        <Button.Triggers>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation
                            Storyboard.TargetName="HoverOverlay"
                            Storyboard.TargetProperty="Opacity"
                            To="0.85"
                            Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut" />
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation
                            Storyboard.TargetName="HoverOverlay"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                            To="1.0"
                            Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut" />
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation
                            Storyboard.TargetName="HoverOverlay"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                            To="1.0"
                            Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut" />
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>

            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation
                            Storyboard.TargetName="HoverOverlay"
                            Storyboard.TargetProperty="Opacity"
                            To="0"
                            Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut" />
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation
                            Storyboard.TargetName="HoverOverlay"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                            To="1"
                            Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut" />
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation
                            Storyboard.TargetName="HoverOverlay"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                            To="1"
                            Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut" />
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Button.Triggers>
    </Button>
</UserControl>
