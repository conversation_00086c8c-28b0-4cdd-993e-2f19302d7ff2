﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!--
        ========================================
        CARD COMPONENT STYLES
        ========================================
    -->

    <!--  ========== Dialog Base Card Style ==========  -->
    <Style x:Key="DialogBaseCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Margin" Value="2" />
        <Setter Property="Background" Value="{DynamicResource CardBackgroundBase}" />
        <Setter Property="Foreground" Value="{DynamicResource CardForegroundBase}" />
        <Setter Property="UniformCornerRadius" Value="3" />
        <Setter Property="Padding" Value="16" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4" />
        <Setter Property="BorderThickness" Value="0" />
    </Style>

    <!--  ========== Bar Card Side/Window Style ==========  -->
    <Style x:Key="BarCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Margin" Value="2" />
        <Setter Property="Background" Value="{DynamicResource MainWindowBackgoundAccent}" />
        <Setter Property="Foreground" Value="{DynamicResource MainWindowForegroundBase}" />
        <Setter Property="UniformCornerRadius" Value="0" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8" />
        <Setter Property="BorderThickness" Value="0" />
    </Style>

    <!--  ========== Confirmation Window Style ==========  -->
    <Style x:Key="ConfirmationWindowCard" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{DynamicResource ConfirmWindowBackground}" />
        <Setter Property="Foreground" Value="{DynamicResource ConfirmWindowForeground}" />
        <Setter Property="UniformCornerRadius" Value="7" />
        <Setter Property="Margin" Value="1" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
    </Style>

    <!--  Header Card Style  -->
    <Style x:Key="HeaderCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundAccent}" />
        <Setter Property="Foreground" Value="{DynamicResource CardForegroundBase}" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8" />
        <Setter Property="Height" Value="54" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="Padding" Value="18,0" />
    </Style>

    <!--  Footer Card Style  -->
    <Style x:Key="FooterCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundAccent}" />
        <Setter Property="Foreground" Value="{DynamicResource CardForegroundBase}" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8" />
        <Setter Property="Height" Value="54" />
        <Setter Property="Padding" Value="8" />
    </Style>

    <!--  Content Card Style  -->
    <Style x:Key="ContentCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundAccent}" />
        <Setter Property="Foreground" Value="{DynamicResource CardForegroundBase}" />
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="UniformCornerRadius" Value="5" />
    </Style>

</ResourceDictionary>