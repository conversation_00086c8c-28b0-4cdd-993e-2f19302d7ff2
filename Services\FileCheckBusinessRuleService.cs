using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Caching.Memory;
using Dapper;
using UFU2.Models;
using UFU2.Common;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Service for enforcing file check business rules based on activity types.
    /// Provides validation, enforcement, and management of file check requirements
    /// according to Algerian business registration regulations.
    /// Enhanced with caching for improved performance of business rule lookups.
    ///
    /// Business Rules:
    /// - MainCommercial/SecondaryCommercial: CAS, NIF, NIS, RC, DEX
    /// - Craft: CAS, NIF, NIS, ART, DEX
    /// - Professional: CAS, NIF, NIS, AGR, DEX
    /// </summary>
    public class FileCheckBusinessRuleService : ICacheableService, IDisposable
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;
        private readonly IMemoryCache _businessRuleCache;

        // Cache statistics for monitoring
        private int _cacheHits = 0;
        private int _cacheMisses = 0;
        private bool _disposed = false;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the FileCheckBusinessRuleService.
        /// </summary>
        /// <param name="databaseService">The database service for data access</param>
        public FileCheckBusinessRuleService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));

            // Initialize business rule cache with application lifetime (static data)
            _businessRuleCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 100,
                CompactionPercentage = 0.25
            });
        }

        #endregion

        #region ICacheableService Implementation

        /// <summary>
        /// Gets the service name for cache management identification.
        /// </summary>
        public string ServiceName => "FileCheckBusinessRuleService";

        /// <summary>
        /// Clears the business rule cache.
        /// Useful when business rules change or cache needs to be invalidated.
        /// </summary>
        public void ClearCache()
        {
            try
            {
                _businessRuleCache?.Dispose();

                // Recreate the cache
                var newCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 100,
                    CompactionPercentage = 0.25
                });

                // Reset cache statistics
                _cacheHits = 0;
                _cacheMisses = 0;

                LoggingService.LogDebug("Business rule cache cleared and recreated", "FileCheckBusinessRuleService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing business rule cache: {ex.Message}", "FileCheckBusinessRuleService");
            }
        }

        /// <summary>
        /// Gets cache statistics for monitoring and debugging.
        /// </summary>
        /// <returns>Dictionary containing cache hit/miss statistics</returns>
        public Dictionary<string, object> GetCacheStatistics()
        {
            return new Dictionary<string, object>
            {
                ["CacheHits"] = _cacheHits,
                ["CacheMisses"] = _cacheMisses,
                ["CacheHitRatio"] = _cacheHits + _cacheMisses > 0 ?
                    (double)_cacheHits / (_cacheHits + _cacheMisses) : 0.0,
                ["TotalLookups"] = _cacheHits + _cacheMisses
            };
        }

        /// <summary>
        /// Warms up the cache by preloading frequently used business rules.
        /// This method should be called during application startup for optimal performance.
        /// </summary>
        public async Task WarmupCacheAsync()
        {
            try
            {
                LoggingService.LogDebug("Starting cache warmup for FileCheckBusinessRuleService", "FileCheckBusinessRuleService");

                // Preload business rules for common activity types
                var commonActivityTypes = new[] { "MainCommercial", "SecondaryCommercial", "Craft", "Professional" };

                foreach (var activityType in commonActivityTypes)
                {
                    try
                    {
                        // Warm up required file check types cache
                        GetRequiredFileCheckTypesCached(activityType);

                        // Warm up valid file check types cache
                        GetValidFileCheckTypesCached(activityType);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogWarning($"Failed to warmup cache for activity type '{activityType}': {ex.Message}", "FileCheckBusinessRuleService");
                    }
                }

                LoggingService.LogInfo($"Cache warmup completed. Cache hits: {_cacheHits}, Cache misses: {_cacheMisses}", "FileCheckBusinessRuleService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache warmup: {ex.Message}", "FileCheckBusinessRuleService");
            }

            await Task.CompletedTask; // Make method async for interface compliance
        }

        /// <summary>
        /// Invalidates specific cache entries based on data change events.
        /// </summary>
        /// <param name="invalidationContext">Context information about what data changed</param>
        public void InvalidateCache(CacheInvalidationContext invalidationContext)
        {
            try
            {
                if (invalidationContext == null)
                    return;

                LoggingService.LogDebug($"Invalidating FileCheckBusinessRuleService cache for {invalidationContext.DataType} ({invalidationContext.InvalidationType})", "FileCheckBusinessRuleService");

                switch (invalidationContext.InvalidationType)
                {
                    case CacheInvalidationType.Full:
                        // Clear all caches
                        ClearCache();
                        break;

                    case CacheInvalidationType.Create:
                    case CacheInvalidationType.Update:
                    case CacheInvalidationType.Delete:
                        // For business rule or activity type changes, clear relevant cache entries
                        if (invalidationContext.DataType == "FileCheckRule" || invalidationContext.DataType == "BusinessRule")
                        {
                            // Clear business rule cache since rules may have changed
                            ClearCache();
                        }
                        else if (invalidationContext.DataType == "ActivityType")
                        {
                            // Activity type changes might affect business rule mappings
                            ClearCache();
                        }
                        break;
                }

                LoggingService.LogDebug($"Cache invalidation completed for FileCheckBusinessRuleService", "FileCheckBusinessRuleService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache invalidation: {ex.Message}", "FileCheckBusinessRuleService");
            }
        }

        /// <summary>
        /// Gets cache health information for monitoring.
        /// </summary>
        /// <returns>Cache health metrics</returns>
        public CacheHealthInfo GetCacheHealth()
        {
            try
            {
                var stats = GetCacheStatistics();

                var hitRatio = (double)stats["CacheHitRatio"];
                var totalLookups = (int)stats["TotalLookups"];

                // Estimate memory usage (rough calculation based on cache size)
                var estimatedMemoryUsage = totalLookups * 512; // 512 bytes per business rule lookup estimate

                var isHealthy = hitRatio >= 0.7 && totalLookups > 0; // At least 70% hit ratio for business rules

                return new CacheHealthInfo
                {
                    HitRatio = hitRatio,
                    ItemCount = totalLookups,
                    MemoryUsageBytes = estimatedMemoryUsage,
                    IsHealthy = isHealthy,
                    HealthStatus = isHealthy ? "Healthy" : $"Low hit ratio: {hitRatio:P1}",
                    LastWarmupTime = null // Will be set by coordinator
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting cache health: {ex.Message}", "FileCheckBusinessRuleService");
                return new CacheHealthInfo
                {
                    HitRatio = 0.0,
                    ItemCount = 0,
                    MemoryUsageBytes = 0,
                    IsHealthy = false,
                    HealthStatus = $"Error: {ex.Message}"
                };
            }
        }

        #endregion

        #region Cached Business Rule Helpers

        /// <summary>
        /// Gets valid file check types for an activity type with caching.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>List of valid file check types</returns>
        private List<string> GetValidFileCheckTypesCached(string activityType)
        {
            string cacheKey = $"valid_types_{activityType?.ToLowerInvariant() ?? "null"}";

            if (_businessRuleCache.TryGetValue(cacheKey, out List<string> cachedResult))
            {
                _cacheHits++;
                return cachedResult;
            }

            _cacheMisses++;

            var result = FileCheckTypeRules.GetValidFileCheckTypes(activityType);

            // Cache for application lifetime (business rules are static)
            var cacheOptions = new MemoryCacheEntryOptions
            {
                Priority = CacheItemPriority.High,
                Size = 1
            };
            _businessRuleCache.Set(cacheKey, result, cacheOptions);

            return result;
        }

        /// <summary>
        /// Gets required file check types for an activity type with caching.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>List of required file check types</returns>
        private List<string> GetRequiredFileCheckTypesCached(string activityType)
        {
            string cacheKey = $"required_types_{activityType?.ToLowerInvariant() ?? "null"}";

            if (_businessRuleCache.TryGetValue(cacheKey, out List<string> cachedResult))
            {
                _cacheHits++;
                return cachedResult;
            }

            _cacheMisses++;

            var result = FileCheckTypeRules.GetRequiredFileCheckTypes(activityType);

            // Cache for application lifetime (business rules are static)
            var cacheOptions = new MemoryCacheEntryOptions
            {
                Priority = CacheItemPriority.High,
                Size = 1
            };
            _businessRuleCache.Set(cacheKey, result, cacheOptions);

            return result;
        }

        /// <summary>
        /// Gets activity type display name with caching.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>Display name for the activity type</returns>
        private string GetActivityTypeDisplayNameCached(string activityType)
        {
            string cacheKey = $"display_name_{activityType?.ToLowerInvariant() ?? "null"}";

            if (_businessRuleCache.TryGetValue(cacheKey, out string cachedResult))
            {
                _cacheHits++;
                return cachedResult;
            }

            _cacheMisses++;

            var result = FileCheckTypeRules.GetActivityTypeDisplayName(activityType);

            // Cache for application lifetime (business rules are static)
            var cacheOptions = new MemoryCacheEntryOptions
            {
                Priority = CacheItemPriority.High,
                Size = 1
            };
            _businessRuleCache.Set(cacheKey, result, cacheOptions);

            return result;
        }

        #endregion

        #region File Check Type Validation

        /// <summary>
        /// Validates file check types against activity type business rules.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckTypes">The file check types to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        public GeneralValidationResult ValidateFileCheckTypes(string activityType, IEnumerable<string> fileCheckTypes)
        {
            var result = new GeneralValidationResult();

            try
            {
                if (string.IsNullOrWhiteSpace(activityType))
                {
                    result.AddError("ActivityType", "نوع النشاط مطلوب للتحقق من صحة أنواع فحص الملفات");
                    return result;
                }

                if (fileCheckTypes == null)
                {
                    return result; // No file check types to validate
                }

                var providedTypes = fileCheckTypes.ToList();
                var validTypes = GetValidFileCheckTypesCached(activityType);
                var invalidTypes = providedTypes.Where(type => !validTypes.Contains(type, StringComparer.OrdinalIgnoreCase)).ToList();

                if (invalidTypes.Any())
                {
                    var activityDisplayName = GetActivityTypeDisplayNameCached(activityType);
                    var invalidTypesDisplay = string.Join(", ", invalidTypes.Select(FileCheckTypeRules.GetFileCheckTypeDisplayName));
                    result.AddError("FileCheckTypes",
                        $"أنواع فحص الملفات التالية غير صحيحة لنوع النشاط '{activityDisplayName}': {invalidTypesDisplay}");
                }

                LoggingService.LogDebug($"Validated {providedTypes.Count} file check types for activity type: {activityType}", "FileCheckBusinessRuleService");
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في التحقق من صحة أنواع فحص الملفات", "خطأ في التحقق", 
                                       LogLevel.Error, "FileCheckBusinessRuleService");
                result.AddError("Validation", "حدث خطأ أثناء التحقق من صحة أنواع فحص الملفات");
            }

            return result;
        }

        /// <summary>
        /// Validates that all required file check types are present for an activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckTypes">The provided file check types</param>
        /// <param name="enforceCompletion">Whether to enforce completion of all required checks</param>
        /// <returns>Validation result with detailed error information</returns>
        public GeneralValidationResult ValidateRequiredFileCheckTypes(string activityType, IEnumerable<string> fileCheckTypes, bool enforceCompletion = false)
        {
            var result = new GeneralValidationResult();

            try
            {
                if (string.IsNullOrWhiteSpace(activityType))
                {
                    result.AddError("ActivityType", "نوع النشاط مطلوب للتحقق من الملفات المطلوبة");
                    return result;
                }

                var providedTypes = fileCheckTypes?.ToList() ?? new List<string>();
                var requiredTypes = GetRequiredFileCheckTypesCached(activityType);
                var missingTypes = requiredTypes.Where(required =>
                    !providedTypes.Contains(required, StringComparer.OrdinalIgnoreCase)).ToList();

                if (missingTypes.Any())
                {
                    var activityDisplayName = GetActivityTypeDisplayNameCached(activityType);
                    var missingTypesDisplay = string.Join(", ", missingTypes.Select(FileCheckTypeRules.GetFileCheckTypeDisplayName));
                    
                    if (enforceCompletion)
                    {
                        result.AddError("RequiredFileCheckTypes", 
                            $"أنواع فحص الملفات التالية مطلوبة لنوع النشاط '{activityDisplayName}': {missingTypesDisplay}");
                    }
                    else
                    {
                        LoggingService.LogInfo($"Missing file check types for activity type '{activityType}': {string.Join(", ", missingTypes)}", "FileCheckBusinessRuleService");
                    }
                }

                LoggingService.LogDebug($"Validated required file check types for activity type: {activityType}", "FileCheckBusinessRuleService");
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في التحقق من الملفات المطلوبة", "خطأ في التحقق", 
                                       LogLevel.Error, "FileCheckBusinessRuleService");
                result.AddError("Validation", "حدث خطأ أثناء التحقق من الملفات المطلوبة");
            }

            return result;
        }

        /// <summary>
        /// Validates file check states (completion status) for an activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckStates">The file check states to validate</param>
        /// <param name="enforceCompletion">Whether to enforce completion of all required checks</param>
        /// <returns>Validation result with detailed error information</returns>
        public GeneralValidationResult ValidateFileCheckStates(string activityType, Dictionary<string, bool> fileCheckStates, bool enforceCompletion = false)
        {
            var result = new GeneralValidationResult();

            try
            {
                if (string.IsNullOrWhiteSpace(activityType))
                {
                    result.AddError("ActivityType", "نوع النشاط مطلوب للتحقق من حالة فحص الملفات");
                    return result;
                }

                if (fileCheckStates == null || !fileCheckStates.Any())
                {
                    if (enforceCompletion)
                    {
                        result.AddError("FileCheckStates", "حالة فحص الملفات مطلوبة");
                    }
                    return result;
                }

                // Validate file check types
                var typeValidation = ValidateFileCheckTypes(activityType, fileCheckStates.Keys);
                result.Merge(typeValidation);

                // Validate required types are present
                var requiredValidation = ValidateRequiredFileCheckTypes(activityType, fileCheckStates.Keys, enforceCompletion);
                result.Merge(requiredValidation);

                // Validate completion if enforced
                if (enforceCompletion)
                {
                    var requiredTypes = FileCheckTypeRules.GetRequiredFileCheckTypes(activityType);
                    var incompleteTypes = requiredTypes.Where(required => 
                        fileCheckStates.ContainsKey(required) && !fileCheckStates[required]).ToList();

                    if (incompleteTypes.Any())
                    {
                        var activityDisplayName = FileCheckTypeRules.GetActivityTypeDisplayName(activityType);
                        var incompleteTypesDisplay = string.Join(", ", incompleteTypes.Select(FileCheckTypeRules.GetFileCheckTypeDisplayName));
                        result.AddError("IncompleteFileChecks", 
                            $"فحص الملفات التالية غير مكتمل لنوع النشاط '{activityDisplayName}': {incompleteTypesDisplay}");
                    }
                }

                LoggingService.LogDebug($"Validated file check states for activity type: {activityType}", "FileCheckBusinessRuleService");
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في التحقق من حالة فحص الملفات", "خطأ في التحقق", 
                                       LogLevel.Error, "FileCheckBusinessRuleService");
                result.AddError("Validation", "حدث خطأ أثناء التحقق من حالة فحص الملفات");
            }

            return result;
        }

        #endregion

        #region Database Operations

        /// <summary>
        /// Ensures file check states exist for all required types for an activity.
        /// Creates missing file check states with default unchecked status.
        /// </summary>
        /// <param name="activityUID">The activity UID</param>
        /// <param name="activityType">The activity type</param>
        /// <returns>True if operation was successful</returns>
        public async Task<bool> EnsureRequiredFileCheckStatesAsync(string activityUID, string activityType)
        {
            if (string.IsNullOrWhiteSpace(activityUID) || string.IsNullOrWhiteSpace(activityType))
            {
                LoggingService.LogError("Activity UID and type are required", "FileCheckBusinessRuleService");
                return false;
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();

                try
                {
                    var result = await EnsureRequiredFileCheckStatesAsync(connection, transaction, activityUID, activityType);
                    transaction.Commit();
                    return result;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في إنشاء حالات فحص الملفات المطلوبة", "خطأ في قاعدة البيانات",
                                       LogLevel.Error, "FileCheckBusinessRuleService");
                return false;
            }
        }

        /// <summary>
        /// Ensures file check states exist for all required types for an activity within an existing transaction.
        /// Creates missing file check states with default unchecked status.
        /// This overload prevents database locking by using an existing connection and transaction.
        /// </summary>
        /// <param name="connection">Existing database connection</param>
        /// <param name="transaction">Existing database transaction</param>
        /// <param name="activityUID">The activity UID</param>
        /// <param name="activityType">The activity type</param>
        /// <returns>True if operation was successful</returns>
        public async Task<bool> EnsureRequiredFileCheckStatesAsync(SqliteConnection connection, SqliteTransaction transaction,
            string activityUID, string activityType)
        {
            if (string.IsNullOrWhiteSpace(activityUID) || string.IsNullOrWhiteSpace(activityType))
            {
                LoggingService.LogError("Activity UID and type are required", "FileCheckBusinessRuleService");
                return false;
            }

            if (connection == null)
            {
                LoggingService.LogError("Database connection is required", "FileCheckBusinessRuleService");
                return false;
            }

            if (transaction == null)
            {
                LoggingService.LogError("Database transaction is required", "FileCheckBusinessRuleService");
                return false;
            }

            try
            {
                var requiredTypes = FileCheckTypeRules.GetRequiredFileCheckTypes(activityType);
                var existingTypes = await GetExistingFileCheckTypesAsync(connection, transaction, activityUID);
                var missingTypes = requiredTypes.Where(required => !existingTypes.Contains(required, StringComparer.OrdinalIgnoreCase)).ToList();

                if (missingTypes.Any())
                {
                    const string insertSql = @"
                        INSERT INTO FileCheckStates (Uid, ActivityUid, FileCheckType, IsChecked, CheckedDate)
                        VALUES (@Uid, @ActivityUid, @FileCheckType, 0, NULL)";

                    foreach (var missingType in missingTypes)
                    {
                        // Generate unique UID for each FileCheckState record using activity UID as prefix
                        string fileCheckStateUID = $"{activityUID}_{missingType}";

                        await connection.ExecuteAsync(insertSql, new
                        {
                            Uid = fileCheckStateUID,
                            ActivityUid = activityUID,
                            FileCheckType = missingType
                        }, transaction);
                    }

                    LoggingService.LogInfo($"Created {missingTypes.Count} missing file check states for activity: {activityUID}", "FileCheckBusinessRuleService");
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to ensure required file check states for activity {activityUID}: {ex.Message}", "FileCheckBusinessRuleService");
                throw;
            }
        }

        /// <summary>
        /// Removes invalid file check states for an activity based on its type.
        /// </summary>
        /// <param name="activityUID">The activity UID</param>
        /// <param name="activityType">The activity type</param>
        /// <returns>Number of removed file check states</returns>
        public async Task<int> RemoveInvalidFileCheckStatesAsync(string activityUID, string activityType)
        {
            if (string.IsNullOrWhiteSpace(activityUID) || string.IsNullOrWhiteSpace(activityType))
            {
                LoggingService.LogError("Activity UID and type are required", "FileCheckBusinessRuleService");
                return 0;
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var validTypes = FileCheckTypeRules.GetValidFileCheckTypes(activityType);
                var validTypesParam = string.Join(",", validTypes.Select(t => $"'{t}'"));

                const string deleteSql = @"
                    DELETE FROM FileCheckStates
                    WHERE ActivityUid = @ActivityUid
                    AND FileCheckType NOT IN ({0})";

                var sql = string.Format(deleteSql, validTypesParam);
                var removedCount = await connection.ExecuteAsync(sql, new { ActivityUid = activityUID });

                if (removedCount > 0)
                {
                    LoggingService.LogInfo($"Removed {removedCount} invalid file check states for activity: {activityUID}", "FileCheckBusinessRuleService");
                }

                return removedCount;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في إزالة حالات فحص الملفات غير الصحيحة", "خطأ في قاعدة البيانات", 
                                       LogLevel.Error, "FileCheckBusinessRuleService");
                return 0;
            }
        }

        /// <summary>
        /// Gets existing file check types for an activity.
        /// </summary>
        /// <param name="connection">The database connection</param>
        /// <param name="transaction">The database transaction</param>
        /// <param name="activityUID">The activity UID</param>
        /// <returns>List of existing file check types</returns>
        private async Task<List<string>> GetExistingFileCheckTypesAsync(SqliteConnection connection, SqliteTransaction transaction, string activityUID)
        {
            const string selectSql = @"
                SELECT FileCheckType
                FROM FileCheckStates
                WHERE ActivityUid = @ActivityUid";

            var existingTypes = await connection.QueryAsync<string>(selectSql, new { ActivityUid = activityUID }, transaction);
            return existingTypes.ToList();
        }

        #endregion

        #region Activity Type Change Handling

        /// <summary>
        /// Handles activity type changes by updating file check states accordingly.
        /// Removes invalid file check states and ensures required ones exist.
        /// </summary>
        /// <param name="activityUID">The activity UID</param>
        /// <param name="oldActivityType">The old activity type</param>
        /// <param name="newActivityType">The new activity type</param>
        /// <returns>True if operation was successful</returns>
        public async Task<bool> HandleActivityTypeChangeAsync(string activityUID, string oldActivityType, string newActivityType)
        {
            if (string.IsNullOrWhiteSpace(activityUID) || string.IsNullOrWhiteSpace(newActivityType))
            {
                LoggingService.LogError("Activity UID and new activity type are required", "FileCheckBusinessRuleService");
                return false;
            }

            try
            {
                LoggingService.LogInfo($"Handling activity type change for {activityUID}: {oldActivityType} -> {newActivityType}", "FileCheckBusinessRuleService");

                // Remove invalid file check states for the new activity type
                var removedCount = await RemoveInvalidFileCheckStatesAsync(activityUID, newActivityType);

                // Ensure all required file check states exist for the new activity type
                var ensureResult = await EnsureRequiredFileCheckStatesAsync(activityUID, newActivityType);

                if (ensureResult)
                {
                    LoggingService.LogInfo($"Successfully handled activity type change for {activityUID}. Removed {removedCount} invalid states.", "FileCheckBusinessRuleService");
                }

                return ensureResult;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في معالجة تغيير نوع النشاط", "خطأ في المعالجة", 
                                       LogLevel.Error, "FileCheckBusinessRuleService");
                return false;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets file check completion statistics for an activity.
        /// </summary>
        /// <param name="activityUID">The activity UID</param>
        /// <param name="activityType">The activity type</param>
        /// <returns>File check completion statistics</returns>
        public async Task<FileCheckCompletionStats> GetFileCheckCompletionStatsAsync(string activityUID, string activityType)
        {
            var stats = new FileCheckCompletionStats();

            if (string.IsNullOrWhiteSpace(activityUID) || string.IsNullOrWhiteSpace(activityType))
            {
                return stats;
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                const string selectSql = @"
                    SELECT FileCheckType, IsChecked, CheckedDate
                    FROM FileCheckStates
                    WHERE ActivityUid = @ActivityUid";

                var fileCheckStates = await connection.QueryAsync<dynamic>(selectSql, new { ActivityUid = activityUID });
                var requiredTypes = FileCheckTypeRules.GetRequiredFileCheckTypes(activityType);

                stats.RequiredCount = requiredTypes.Count;
                stats.CompletedCount = fileCheckStates.Count(fcs => fcs.IsChecked == 1);
                stats.TotalCount = fileCheckStates.Count();
                stats.CompletionPercentage = stats.RequiredCount > 0 ? (double)stats.CompletedCount / stats.RequiredCount : 0.0;
                stats.IsComplete = stats.CompletedCount == stats.RequiredCount;

                LoggingService.LogDebug($"File check completion stats for {activityUID}: {stats.CompletedCount}/{stats.RequiredCount} ({stats.CompletionPercentage:P0})", "FileCheckBusinessRuleService");
            }
            catch (Exception ex)
            {
                ErrorManager.HandleError(ex, "فشل في الحصول على إحصائيات فحص الملفات", "خطأ في قاعدة البيانات", 
                                       LogLevel.Error, "FileCheckBusinessRuleService");
            }

            return stats;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the service and its resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        var stats = GetCacheStatistics();
                        LoggingService.LogInfo($"FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: {stats["CacheHitRatio"]:P1}, Total lookups: {stats["TotalLookups"]}", "FileCheckBusinessRuleService");

                        _businessRuleCache?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error disposing FileCheckBusinessRuleService: {ex.Message}", "FileCheckBusinessRuleService");
                    }
                }
                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// Statistics for file check completion.
    /// </summary>
    public class FileCheckCompletionStats
    {
        /// <summary>
        /// Number of required file checks for the activity type.
        /// </summary>
        public int RequiredCount { get; set; }

        /// <summary>
        /// Number of completed file checks.
        /// </summary>
        public int CompletedCount { get; set; }

        /// <summary>
        /// Total number of file check states (may include invalid ones).
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Completion percentage (0.0 to 1.0).
        /// </summary>
        public double CompletionPercentage { get; set; }

        /// <summary>
        /// Whether all required file checks are completed.
        /// </summary>
        public bool IsComplete { get; set; }
    }
}