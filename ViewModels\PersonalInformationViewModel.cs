using System.ComponentModel;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for managing client personal information.
    /// Handles basic client details including names, birth information, gender, address, and profile image.
    /// Extracted from NewClientViewModel to improve maintainability and separation of concerns.
    /// Implements IDataErrorInfo for validation support.
    /// </summary>
    public class PersonalInformationViewModel : BaseViewModel, IDataErrorInfo
    {
        #region Private Fields

        private string _nameFr = string.Empty;
        private string _nameAr = string.Empty;
        private string _birthDate = string.Empty;
        private string _birthPlace = string.Empty;
        private int _gender = 0;
        private string _address = string.Empty;
        private string _nationalId = string.Empty;
        private BitmapSource? _profileImage;
        private string? _profileImageOriginalExtension;

        // Validation support
        private readonly ValidationService _validationService;
        private readonly Dictionary<string, string> _validationErrors = new Dictionary<string, string>();

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the French name (Latin characters) for the client.
        /// This property is used for validation and is required for client creation.
        /// </summary>
        public string NameFr
        {
            get => _nameFr;
            set => SetProperty(ref _nameFr, value);
        }

        /// <summary>
        /// Gets or sets the Arabic name for the client.
        /// Optional field for client creation.
        /// </summary>
        public string NameAr
        {
            get => _nameAr;
            set => SetProperty(ref _nameAr, value);
        }

        /// <summary>
        /// Gets or sets the birth date in DD/MM/YYYY format.
        /// Supports partial dates like "xx/xx/1993" and complete dates like "23/03/1993".
        /// Optional field for client creation.
        /// </summary>
        public string BirthDate
        {
            get => _birthDate;
            set => SetProperty(ref _birthDate, value);
        }

        /// <summary>
        /// Gets or sets the birth place.
        /// Optional field for client creation.
        /// </summary>
        public string BirthPlace
        {
            get => _birthPlace;
            set => SetProperty(ref _birthPlace, value);
        }

        /// <summary>
        /// Gets or sets the gender.
        /// 0 = Male (ذكر), 1 = Female (أنثى)
        /// </summary>
        public int Gender
        {
            get => _gender;
            set => SetProperty(ref _gender, value);
        }

        /// <summary>
        /// Gets or sets the address.
        /// Optional field for client creation.
        /// </summary>
        public string Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        /// <summary>
        /// Gets or sets the national ID number.
        /// Optional field for client creation.
        /// </summary>
        public string NationalId
        {
            get => _nationalId;
            set => SetProperty(ref _nationalId, value);
        }

        /// <summary>
        /// Gets or sets the profile image for the client.
        /// Optional field for client creation.
        /// </summary>
        public BitmapSource? ProfileImage
        {
            get => _profileImage;
            set => SetProperty(ref _profileImage, value);
        }
        /// <summary>
        /// Gets whether the client has a profile image set.
        /// </summary>
        public bool HasProfileImage => _profileImage != null;


        /// <summary>
        /// Gets or sets the original file extension of the profile image.
        /// Used for maintaining image format during save operations.
        /// </summary>
        public string? ProfileImageOriginalExtension
        {
            get => _profileImageOriginalExtension;
            set => SetProperty(ref _profileImageOriginalExtension, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to open the image management dialog for profile image selection.
        /// </summary>
        public ICommand OpenImageManagementCommand { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the PersonalInformationViewModel class.
        /// </summary>
        public PersonalInformationViewModel()
        {
            // Initialize validation service
            _validationService = ServiceLocator.GetService<ValidationService>();

            OpenImageManagementCommand = new RelayCommand(
                execute: OpenImageManagement,
                commandName: "OpenImageManagement"
            );

            LoggingService.LogDebug("PersonalInformationViewModel initialized", "PersonalInformationViewModel");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Validates the personal information data.
        /// </summary>
        /// <returns>True if all required fields are valid, false otherwise</returns>
        public bool IsValid()
        {
            // NameFr is required for client creation
            if (string.IsNullOrWhiteSpace(NameFr))
            {
                LoggingService.LogDebug("Personal information validation failed: NameFr is required", "PersonalInformationViewModel");
                return false;
            }

            LoggingService.LogDebug("Personal information validation passed", "PersonalInformationViewModel");
            return true;
        }

        /// <summary>
        /// Clears all personal information fields.
        /// </summary>
        public void Clear()
        {
            NameFr = string.Empty;
            NameAr = string.Empty;
            BirthDate = string.Empty;
            BirthPlace = string.Empty;
            Gender = 0;
            Address = string.Empty;
            NationalId = string.Empty;
            ProfileImage = null;
            ProfileImageOriginalExtension = null;

            LoggingService.LogDebug("Personal information cleared", "PersonalInformationViewModel");
        }

        /// <summary>
        /// Loads personal information from a client data object.
        /// </summary>
        /// <param name="clientData">The client data to load from</param>
        public void LoadFromClientData(DuplicateClientData clientData)
        {
            if (clientData == null)
            {
                LoggingService.LogWarning("Cannot load personal information from null client data", "PersonalInformationViewModel");
                return;
            }

            try
            {
                NameFr = clientData.NameFr ?? string.Empty;
                NameAr = clientData.NameAr ?? string.Empty;
                BirthDate = clientData.BirthDate ?? string.Empty;
                BirthPlace = clientData.BirthPlace ?? string.Empty;
                Gender = clientData.Gender;
                Address = clientData.Address ?? string.Empty;
                NationalId = clientData.NationalId ?? string.Empty;

                LoggingService.LogInfo($"Personal information loaded for client: {clientData.ClientUid}", "PersonalInformationViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading personal information from client data: {ex.Message}", "PersonalInformationViewModel");
            }
        }

        /// <summary>
        /// Checks if the current personal information has changes compared to the original client data.
        /// </summary>
        /// <param name="originalData">The original client data to compare against</param>
        /// <returns>True if there are changes, false if no changes detected</returns>
        public bool HasChanges(DuplicateClientData? originalData)
        {
            if (originalData == null)
            {
                return true; // If no original data, treat as changes (new client)
            }

            try
            {
                // Compare each field that can be updated
                bool hasChanges = false;

                // Compare NameAr (trimmed and null-normalized)
                var currentNameAr = string.IsNullOrWhiteSpace(NameAr) ? null : NameAr.Trim();
                var originalNameAr = string.IsNullOrWhiteSpace(originalData.NameAr) ? null : originalData.NameAr.Trim();
                if (!string.Equals(currentNameAr, originalNameAr, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"NameAr changed: '{originalNameAr}' -> '{currentNameAr}'", "PersonalInformationViewModel");
                }

                // Compare BirthDate (trimmed and null-normalized)
                var currentBirthDate = string.IsNullOrWhiteSpace(BirthDate) ? null : BirthDate.Trim();
                var originalBirthDate = string.IsNullOrWhiteSpace(originalData.BirthDate) ? null : originalData.BirthDate.Trim();
                if (!string.Equals(currentBirthDate, originalBirthDate, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"BirthDate changed: '{originalBirthDate}' -> '{currentBirthDate}'", "PersonalInformationViewModel");
                }

                // Compare BirthPlace (trimmed and null-normalized)
                var currentBirthPlace = string.IsNullOrWhiteSpace(BirthPlace) ? null : BirthPlace.Trim();
                var originalBirthPlace = string.IsNullOrWhiteSpace(originalData.BirthPlace) ? null : originalData.BirthPlace.Trim();
                if (!string.Equals(currentBirthPlace, originalBirthPlace, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"BirthPlace changed: '{originalBirthPlace}' -> '{currentBirthPlace}'", "PersonalInformationViewModel");
                }

                // Compare Gender
                if (Gender != originalData.Gender)
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"Gender changed: {originalData.Gender} -> {Gender}", "PersonalInformationViewModel");
                }

                // Compare Address (trimmed and null-normalized)
                var currentAddress = string.IsNullOrWhiteSpace(Address) ? null : Address.Trim();
                var originalAddress = string.IsNullOrWhiteSpace(originalData.Address) ? null : originalData.Address.Trim();
                if (!string.Equals(currentAddress, originalAddress, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"Address changed: '{originalAddress}' -> '{currentAddress}'", "PersonalInformationViewModel");
                }

                // Compare NationalId (trimmed and null-normalized)
                var currentNationalId = string.IsNullOrWhiteSpace(NationalId) ? null : NationalId.Trim();
                var originalNationalId = string.IsNullOrWhiteSpace(originalData.NationalId) ? null : originalData.NationalId.Trim();
                if (!string.Equals(currentNationalId, originalNationalId, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"NationalId changed: '{originalNationalId}' -> '{currentNationalId}'", "PersonalInformationViewModel");
                }

                LoggingService.LogDebug($"Personal info changes detected: {hasChanges}", "PersonalInformationViewModel");
                return hasChanges;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking for personal info changes: {ex.Message}", "PersonalInformationViewModel");
                return true; // Assume changes on error to be safe
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Opens the image management dialog for profile image selection.
        /// </summary>
        private void OpenImageManagement()
        {
            try
            {
                // This will be implemented to open the ImageManagementViewModel dialog
                // For now, just log the action
                LoggingService.LogInfo("Image management dialog requested", "PersonalInformationViewModel");

                // TODO: Implement image management dialog opening
                // This should integrate with the existing ImageManagementViewModel
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening image management dialog: {ex.Message}", "PersonalInformationViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح نافذة إدارة الصور",
                    "خطأ في إدارة الصور",
                    LogLevel.Error,
                    "PersonalInformationViewModel");
            }
        }

        #endregion

        #region IDataErrorInfo Implementation

        /// <summary>
        /// Gets an error message indicating what is wrong with this object.
        /// Returns null if there are no validation errors.
        /// </summary>
        public string? Error
        {
            get
            {
                // Return the first validation error if any exist
                return _validationErrors.Values.FirstOrDefault();
            }
        }

        /// <summary>
        /// Gets the error message for the property with the given name.
        /// </summary>
        /// <param name="columnName">The name of the property whose error message to get</param>
        /// <returns>The error message for the property, or null if valid</returns>
        public string? this[string columnName]
        {
            get
            {
                try
                {
                    // Clear previous error for this property
                    _validationErrors.Remove(columnName);

                    // Validate the specific property
                    string? errorMessage = GetValidationError(columnName);

                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        _validationErrors[columnName] = errorMessage;
                        return errorMessage;
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error validating property {columnName}: {ex.Message}", "PersonalInformationViewModel");
                    return null;
                }
            }
        }

        /// <summary>
        /// Gets the validation error message for the specified property.
        /// </summary>
        /// <param name="propertyName">The name of the property to validate</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        private string? GetValidationError(string propertyName)
        {
            try
            {
                switch (propertyName)
                {
                    case nameof(NameFr):
                        return _validationService.ValidateLatinName(NameFr, true);
                    case nameof(NameAr):
                        // Validate optional Arabic name length
                        if (!string.IsNullOrWhiteSpace(NameAr))
                        {
                            return _validationService.ValidateMaxLength(NameAr, 100, "NameAr");
                        }
                        return null;
                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in GetValidationError for {propertyName}: {ex.Message}", "PersonalInformationViewModel");
                return null;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the ViewModel resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Clear image references to help with memory management
                ProfileImage = null;
                LoggingService.LogDebug("PersonalInformationViewModel disposed", "PersonalInformationViewModel");
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}