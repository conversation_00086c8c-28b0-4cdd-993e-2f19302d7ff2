using System;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that controls the visibility of activity code fields based on the selected activity type.
    /// Some activity types require activity codes while others do not.
    /// Returns Visibility.Visible for activity types that require codes, Visibility.Collapsed for others.
    /// </summary>
    public class ActivityCodeVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts the selected activity type to a Visibility value for activity code fields.
        /// </summary>
        /// <param name="value">The selected activity type (MainCommercial, SecondaryCommercial, Craft, Professional)</param>
        /// <param name="targetType">The target type (should be Visibility)</param>
        /// <param name="parameter">Optional parameter for inversion (use "Invert" to reverse the logic)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Visibility.Visible if activity code is required, Visibility.Collapsed otherwise</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var activityType = value?.ToString() ?? "MainCommercial";
                var shouldShowCode = ShouldShowActivityCode(activityType);
                
                // Check if inversion is requested
                var isInverted = parameter?.ToString()?.Equals("Invert", StringComparison.OrdinalIgnoreCase) == true;
                if (isInverted)
                {
                    shouldShowCode = !shouldShowCode;
                }

                return shouldShowCode ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception)
            {
                // Default to visible if any error occurs
                return Visibility.Visible;
            }
        }

        /// <summary>
        /// Not implemented for this converter as it's one-way only.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ActivityCodeVisibilityConverter is a one-way converter.");
        }

        /// <summary>
        /// Determines whether activity code fields should be visible for the given activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>True if activity code should be visible, false otherwise</returns>
        private static bool ShouldShowActivityCode(string activityType)
        {
            return activityType switch
            {
                // Main commercial activities typically don't require specific activity codes
                "MainCommercial" => true,
                
                // Secondary commercial activities typically don't require specific activity codes
                "SecondaryCommercial" => true,
                
                // Craft activities require specific craft codes for classification
                "Craft" => true,
                
                // Professional activities require professional license codes
                "Professional" => false,
                
                // Default to showing code for unknown types (safer approach)
                _ => true
            };
        }

        /// <summary>
        /// Gets user-friendly explanation for activity code requirements.
        /// This can be used for tooltips or help text.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Arabic explanation text for activity code requirements</returns>
        public static string GetActivityCodeExplanation(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "الأنشطة التجارية الرئيسية لا تتطلب رمز نشاط محدد",
                "SecondaryCommercial" => "الأنشطة التجارية الثانوية لا تتطلب رمز نشاط محدد",
                "Craft" => "الأنشطة الحرفية تتطلب رمز تصنيف الحرفة",
                "Professional" => "الأنشطة المهنية تتطلب رمز الترخيص المهني",
                _ => "قد يتطلب هذا النوع من النشاط رمز تصنيف"
            };
        }

        /// <summary>
        /// Gets the label text for activity code field based on activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Appropriate Arabic label for the activity code field</returns>
        public static string GetActivityCodeLabel(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "رمز النشاط التجاري",
                "SecondaryCommercial" => "رمز النشاط الثانوي",
                "Craft" => "رمز تصنيف الحرفة",
                "Professional" => "رمز الترخيص المهني",
                _ => "رمز النشاط"
            };
        }

        /// <summary>
        /// Gets the placeholder text for activity code field based on activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Appropriate Arabic placeholder for the activity code field</returns>
        public static string GetActivityCodePlaceholder(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "أدخل رمز النشاط التجاري",
                "SecondaryCommercial" => "أدخل رمز النشاط الثانوي",
                "Craft" => "أدخل رمز تصنيف الحرفة",
                "Professional" => "أدخل رمز الترخيص المهني",
                _ => "أدخل رمز النشاط"
            };
        }

        /// <summary>
        /// Validates activity code format based on activity type.
        /// Different activity types may have different code format requirements.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <param name="activityCode">The activity code to validate</param>
        /// <returns>True if the code format is valid for the activity type, false otherwise</returns>
        public static bool IsValidActivityCodeFormat(string activityType, string activityCode)
        {
            if (string.IsNullOrWhiteSpace(activityCode))
            {
                // If activity code is not required for this type, empty is valid
                return !ShouldShowActivityCode(activityType);
            }

            return activityType switch
            {
                "Craft" => ValidateCraftCode(activityCode),
                "Professional" => ValidateProfessionalCode(activityCode),
                "MainCommercial" => true, // No specific format required
                "SecondaryCommercial" => true, // No specific format required
                _ => true // Default to valid for unknown types
            };
        }

        /// <summary>
        /// Validates craft activity code format.
        /// </summary>
        /// <param name="code">The craft code to validate</param>
        /// <returns>True if valid craft code format, false otherwise</returns>
        private static bool ValidateCraftCode(string code)
        {
            // Craft codes follow XX-XX-XXX format (9 digits with dashes)
            if (string.IsNullOrWhiteSpace(code))
                return false;

            // Remove dashes for validation
            var digitsOnly = code.Replace("-", "");

            // Must be exactly 7 digits
            if (digitsOnly.Length != 7 || !digitsOnly.All(char.IsDigit))
                return false;

            // Check format: XX-XX-XXX
            return code.Length == 9 &&
                   code[2] == '-' &&
                   code[5] == '-' &&
                   char.IsDigit(code[0]) && char.IsDigit(code[1]) &&
                   char.IsDigit(code[3]) && char.IsDigit(code[4]) &&
                   char.IsDigit(code[6]) && char.IsDigit(code[7]) && char.IsDigit(code[8]);
        }

        /// <summary>
        /// Validates professional activity code format.
        /// </summary>
        /// <param name="code">The professional code to validate</param>
        /// <returns>True if valid professional code format, false otherwise</returns>
        private static bool ValidateProfessionalCode(string code)
        {
            // Example validation: Professional codes might be alphanumeric 6-8 characters
            return !string.IsNullOrWhiteSpace(code) && 
                   code.Length >= 6 && 
                   code.Length <= 8 && 
                   code.All(char.IsLetterOrDigit);
        }
    }
}
