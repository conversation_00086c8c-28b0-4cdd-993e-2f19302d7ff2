using System;
using System.ComponentModel.DataAnnotations;

namespace UFU2.Models
{
    /// <summary>
    /// Model representing a CPI Wilaya (administrative province) in Algeria.
    /// Based on official Ministry of Finance data from mfdgi.gov.dz.
    /// </summary>
    public class CpiWilaya
    {
        /// <summary>
        /// Gets or sets the wilaya code (e.g., "01", "02", etc.).
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the Arabic name of the wilaya.
        /// </summary>
        [Required]
        [StringLength(100)]
        public string NameAr { get; set; }

        /// <summary>
        /// Gets or sets the French name of the wilaya.
        /// </summary>
        [Required]
        [StringLength(100)]
        public string NameFr { get; set; }

        /// <summary>
        /// Gets or sets the display value for UI binding (e.g., "01 - أدرار").
        /// </summary>
        [Required]
        [StringLength(150)]
        public string DisplayValue { get; set; }

        /// <summary>
        /// Returns a string representation of the wilaya.
        /// </summary>
        /// <returns>Display value of the wilaya</returns>
        public override string ToString()
        {
            return DisplayValue ?? $"{Code} - {NameAr}";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current wilaya.
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if the objects are equal</returns>
        public override bool Equals(object obj)
        {
            if (obj is CpiWilaya other)
            {
                return string.Equals(Code, other.Code, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the wilaya.
        /// </summary>
        /// <returns>Hash code based on the Code property</returns>
        public override int GetHashCode()
        {
            return Code?.GetHashCode() ?? 0;
        }
    }

    /// <summary>
    /// Model representing a CPI Daira (administrative district) in Algeria.
    /// Based on official Ministry of Finance data from mfdgi.gov.dz.
    /// </summary>
    public class CpiDaira
    {
        /// <summary>
        /// Gets or sets the daira code (e.g., "01001", "01002", etc.).
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the wilaya code this daira belongs to.
        /// </summary>
        [Required]
        [StringLength(10)]
        public string WilayaCode { get; set; }

        /// <summary>
        /// Gets or sets the Arabic name of the daira.
        /// </summary>
        [Required]
        [StringLength(100)]
        public string NameAr { get; set; }

        /// <summary>
        /// Gets or sets the French name of the daira.
        /// </summary>
        [Required]
        [StringLength(100)]
        public string NameFr { get; set; }

        /// <summary>
        /// Gets or sets the display value for UI binding.
        /// </summary>
        [Required]
        [StringLength(150)]
        public string DisplayValue { get; set; }

        /// <summary>
        /// Returns a string representation of the daira.
        /// </summary>
        /// <returns>Display value of the daira</returns>
        public override string ToString()
        {
            return DisplayValue ?? NameAr;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current daira.
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if the objects are equal</returns>
        public override bool Equals(object obj)
        {
            if (obj is CpiDaira other)
            {
                return string.Equals(Code, other.Code, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the daira.
        /// </summary>
        /// <returns>Hash code based on the Code property</returns>
        public override int GetHashCode()
        {
            return Code?.GetHashCode() ?? 0;
        }
    }

    /// <summary>
    /// Model for CPI location search results combining wilaya and daira information.
    /// </summary>
    public class CpiLocationSearchResult
    {
        /// <summary>
        /// Gets or sets the wilaya information.
        /// </summary>
        public CpiWilaya Wilaya { get; set; }

        /// <summary>
        /// Gets or sets the daira information (null for wilaya-only results).
        /// </summary>
        public CpiDaira Daira { get; set; }

        /// <summary>
        /// Gets or sets the search relevance score.
        /// </summary>
        public double RelevanceScore { get; set; }

        /// <summary>
        /// Gets or sets the match type (e.g., "WilayaName", "DairaName", "Code").
        /// </summary>
        public string MatchType { get; set; }

        /// <summary>
        /// Gets the display text for the search result.
        /// </summary>
        public string DisplayText
        {
            get
            {
                if (Daira != null)
                {
                    return $"{Wilaya?.DisplayValue} - {Daira.DisplayValue}";
                }
                return Wilaya?.DisplayValue ?? "";
            }
        }

        /// <summary>
        /// Returns a string representation of the search result.
        /// </summary>
        /// <returns>Display text of the search result</returns>
        public override string ToString()
        {
            return DisplayText;
        }
    }

    /// <summary>
    /// JSON model for deserializing cpi_Location.json file structure.
    /// </summary>
    public class CpiLocationJsonData
    {
        /// <summary>
        /// Gets or sets the metadata information.
        /// </summary>
        public CpiLocationMetadata Metadata { get; set; }

        /// <summary>
        /// Gets or sets the list of wilayas.
        /// </summary>
        public CpiWilayaJson[] Wilayas { get; set; }

        /// <summary>
        /// Gets or sets the list of dairas.
        /// </summary>
        public CpiDairaJson[] Dairas { get; set; }
    }

    /// <summary>
    /// JSON model for metadata in cpi_Location.json.
    /// </summary>
    public class CpiLocationMetadata
    {
        /// <summary>
        /// Gets or sets the version of the data.
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the last updated date.
        /// </summary>
        public string LastUpdated { get; set; }

        /// <summary>
        /// Gets or sets the data source.
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Gets or sets the total number of wilayas.
        /// </summary>
        public int TotalWilayas { get; set; }

        /// <summary>
        /// Gets or sets the description of the data.
        /// </summary>
        public string Description { get; set; }
    }

    /// <summary>
    /// JSON model for wilaya data in cpi_Location.json.
    /// </summary>
    public class CpiWilayaJson
    {
        /// <summary>
        /// Gets or sets the wilaya key/code.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets the display value.
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the Arabic name.
        /// </summary>
        public string NameAr { get; set; }

        /// <summary>
        /// Gets or sets the French name.
        /// </summary>
        public string NameFr { get; set; }
    }

    /// <summary>
    /// JSON model for daira data in cpi_Location.json.
    /// </summary>
    public class CpiDairaJson
    {
        /// <summary>
        /// Gets or sets the daira key/code.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets the display value.
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the wilaya code this daira belongs to.
        /// </summary>
        public string WilayaCode { get; set; }

        /// <summary>
        /// Gets or sets the Arabic name.
        /// </summary>
        public string NameAr { get; set; }

        /// <summary>
        /// Gets or sets the French name.
        /// </summary>
        public string NameFr { get; set; }
    }
}
