using System;
using System.Collections.Generic;
using System.Linq;

namespace UFU2.Models
{
    /// <summary>
    /// Represents the result of a validation operation with support for multiple field errors.
    /// Provides structured error reporting with Arabic error messages for UFU2 business rules.
    /// Supports aggregation of validation results from multiple sources.
    /// </summary>
    public class ClientValidationResult
    {
        #region Private Fields

        private readonly Dictionary<string, List<string>> _errors = new();

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets a value indicating whether the validation result is valid (no errors).
        /// </summary>
        public bool IsValid => !_errors.Any();

        /// <summary>
        /// Gets a read-only dictionary of field names to their error messages.
        /// </summary>
        public IReadOnlyDictionary<string, List<string>> Errors => _errors;

        /// <summary>
        /// Gets the total number of validation errors.
        /// </summary>
        public int ErrorCount => _errors.Values.Sum(list => list.Count);

        /// <summary>
        /// Gets all error messages as a flat list.
        /// </summary>
        public IEnumerable<string> AllErrors => _errors.Values.SelectMany(list => list);

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the ClientValidationResult class.
        /// Creates a valid result with no errors.
        /// </summary>
        public ClientValidationResult()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ClientValidationResult class with a single error.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <param name="message">The error message</param>
        public ClientValidationResult(string field, string message)
        {
            AddError(field, message);
        }

        #endregion

        #region Error Management Methods

        /// <summary>
        /// Adds an error message for the specified field.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <param name="message">The error message</param>
        public void AddError(string field, string message)
        {
            if (string.IsNullOrWhiteSpace(field))
                throw new ArgumentException("Field name cannot be null or empty", nameof(field));

            if (string.IsNullOrWhiteSpace(message))
                throw new ArgumentException("Error message cannot be null or empty", nameof(message));

            if (!_errors.ContainsKey(field))
            {
                _errors[field] = new List<string>();
            }

            if (!_errors[field].Contains(message))
            {
                _errors[field].Add(message);
            }
        }

        /// <summary>
        /// Adds multiple error messages for the specified field.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <param name="messages">The error messages</param>
        public void AddErrors(string field, IEnumerable<string> messages)
        {
            if (messages == null)
                return;

            foreach (var message in messages.Where(m => !string.IsNullOrWhiteSpace(m)))
            {
                AddError(field, message);
            }
        }

        /// <summary>
        /// Adds an error message for a general validation error (not field-specific).
        /// </summary>
        /// <param name="message">The error message</param>
        public void AddGeneralError(string message)
        {
            AddError("General", message);
        }

        /// <summary>
        /// Clears all errors for the specified field.
        /// </summary>
        /// <param name="field">The field name</param>
        public void ClearErrors(string field)
        {
            if (_errors.ContainsKey(field))
            {
                _errors.Remove(field);
            }
        }

        /// <summary>
        /// Clears all validation errors.
        /// </summary>
        public void ClearAllErrors()
        {
            _errors.Clear();
        }

        #endregion

        #region Query Methods

        /// <summary>
        /// Gets the error messages for the specified field.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <returns>List of error messages for the field, or empty list if no errors</returns>
        public List<string> GetErrors(string field)
        {
            return _errors.ContainsKey(field) ? new List<string>(_errors[field]) : new List<string>();
        }

        /// <summary>
        /// Gets the first error message for the specified field.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <returns>First error message for the field, or null if no errors</returns>
        public string? GetFirstError(string field)
        {
            return _errors.ContainsKey(field) && _errors[field].Any() ? _errors[field].First() : null;
        }

        /// <summary>
        /// Checks if the specified field has any errors.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <returns>True if the field has errors, false otherwise</returns>
        public bool HasErrors(string field)
        {
            return _errors.ContainsKey(field) && _errors[field].Any();
        }

        /// <summary>
        /// Gets all field names that have errors.
        /// </summary>
        /// <returns>Collection of field names with errors</returns>
        public IEnumerable<string> GetFieldsWithErrors()
        {
            return _errors.Where(kvp => kvp.Value.Any()).Select(kvp => kvp.Key);
        }

        #endregion

        #region Aggregation Methods

        /// <summary>
        /// Merges another validation result into this one.
        /// </summary>
        /// <param name="other">The validation result to merge</param>
        public void Merge(ClientValidationResult other)
        {
            if (other == null)
                return;

            foreach (var kvp in other._errors)
            {
                AddErrors(kvp.Key, kvp.Value);
            }
        }

        /// <summary>
        /// Creates a new validation result by merging multiple validation results.
        /// </summary>
        /// <param name="results">The validation results to merge</param>
        /// <returns>A new validation result containing all errors</returns>
        public static ClientValidationResult Merge(params ClientValidationResult[] results)
        {
            var merged = new ClientValidationResult();
            
            foreach (var result in results.Where(r => r != null))
            {
                merged.Merge(result);
            }

            return merged;
        }

        /// <summary>
        /// Creates a new validation result by merging a collection of validation results.
        /// </summary>
        /// <param name="results">The validation results to merge</param>
        /// <returns>A new validation result containing all errors</returns>
        public static ClientValidationResult Merge(IEnumerable<ClientValidationResult> results)
        {
            return Merge(results.ToArray());
        }

        #endregion

        #region String Representation Methods

        /// <summary>
        /// Gets all error messages as a single formatted string.
        /// Each error is on a separate line.
        /// </summary>
        /// <returns>Formatted error string</returns>
        public string GetErrorsAsString()
        {
            if (IsValid)
                return string.Empty;

            var errorLines = new List<string>();

            foreach (var kvp in _errors.Where(kvp => kvp.Value.Any()))
            {
                foreach (var error in kvp.Value)
                {
                    errorLines.Add($"{kvp.Key}: {error}");
                }
            }

            return string.Join(Environment.NewLine, errorLines);
        }

        /// <summary>
        /// Gets all error messages as a single formatted string without field names.
        /// Each error is on a separate line.
        /// </summary>
        /// <returns>Formatted error string without field names</returns>
        public string GetErrorMessagesAsString()
        {
            if (IsValid)
                return string.Empty;

            return string.Join(Environment.NewLine, AllErrors);
        }

        /// <summary>
        /// Gets error messages for the specified field as a single formatted string.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <returns>Formatted error string for the field</returns>
        public string GetFieldErrorsAsString(string field)
        {
            var errors = GetErrors(field);
            return errors.Any() ? string.Join(Environment.NewLine, errors) : string.Empty;
        }

        #endregion

        #region Factory Methods

        /// <summary>
        /// Creates a valid validation result (no errors).
        /// </summary>
        /// <returns>A valid validation result</returns>
        public static ClientValidationResult Valid()
        {
            return new ClientValidationResult();
        }

        /// <summary>
        /// Creates an invalid validation result with a single error.
        /// </summary>
        /// <param name="field">The field name</param>
        /// <param name="message">The error message</param>
        /// <returns>An invalid validation result</returns>
        public static ClientValidationResult Invalid(string field, string message)
        {
            return new ClientValidationResult(field, message);
        }

        /// <summary>
        /// Creates an invalid validation result with a general error.
        /// </summary>
        /// <param name="message">The error message</param>
        /// <returns>An invalid validation result</returns>
        public static ClientValidationResult InvalidGeneral(string message)
        {
            return new ClientValidationResult("General", message);
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// Returns a string representation of the validation result.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            if (IsValid)
                return "Valid";

            return $"Invalid ({ErrorCount} errors): {GetErrorMessagesAsString()}";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current validation result.
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not ClientValidationResult other)
                return false;

            if (IsValid && other.IsValid)
                return true;

            if (IsValid != other.IsValid)
                return false;

            if (_errors.Count != other._errors.Count)
                return false;

            foreach (var kvp in _errors)
            {
                if (!other._errors.ContainsKey(kvp.Key))
                    return false;

                var otherErrors = other._errors[kvp.Key];
                if (kvp.Value.Count != otherErrors.Count)
                    return false;

                if (!kvp.Value.All(error => otherErrors.Contains(error)))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Returns a hash code for the validation result.
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            if (IsValid)
                return 0;

            var hash = 17;
            foreach (var kvp in _errors.OrderBy(kvp => kvp.Key))
            {
                hash = hash * 23 + kvp.Key.GetHashCode();
                foreach (var error in kvp.Value.OrderBy(e => e))
                {
                    hash = hash * 23 + error.GetHashCode();
                }
            }

            return hash;
        }

        #endregion
    }
}