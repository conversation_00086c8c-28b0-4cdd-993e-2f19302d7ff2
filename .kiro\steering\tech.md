# UFU2 Technical Stack

## Framework & Platform
- **Target Framework**: .NET 8.0 Windows
- **UI Framework**: WPF (Windows Presentation Foundation)
- **Language**: C# with nullable reference types enabled
- **Platform**: Windows desktop application

## Key Libraries & Dependencies
- **MaterialDesignThemes** (5.2.1) - Material Design UI components
- **MaterialDesignColors** (5.2.1) - Material Design color system
- **MaterialDesignThemes.MahApps** (5.2.1) - MahApps integration
- **Microsoft.Data.Sqlite** (9.0.7) - SQLite database access
- **Dapper** (2.1.66) - Micro ORM for database operations
- **Newtonsoft.Json** (13.0.3) - JSON serialization
- **Microsoft.Extensions.Caching.Memory** (9.0.7) - In-memory caching

## Architecture Patterns
- **MVVM Pattern**: Model-View-ViewModel architecture
- **Service Locator Pattern**: Centralized service management
- **Command Pattern**: RelayCommand implementation for UI interactions
- **Repository Pattern**: Database service abstraction

## Build & Development Commands
```bash
# Build the solution
dotnet build UFU2.sln

# Run the application
dotnet run --project UFU2.csproj

# Build for release
dotnet build UFU2.sln --configuration Release

# Clean build artifacts
dotnet clean UFU2.sln

# Restore NuGet packages
dotnet restore UFU2.sln
```

## Development Environment
- **IDE**: Visual Studio 2022 (recommended)
- **Project Type**: WPF Application (.NET 8.0)
- **Output Type**: Windows Executable (WinExe)

## Database
- **Type**: SQLite embedded database
- **ORM**: Dapper for data access
- **JSON Data**: Activity types stored in embedded JSON file