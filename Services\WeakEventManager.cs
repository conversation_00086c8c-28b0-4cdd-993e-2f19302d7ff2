using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.ComponentModel;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Enhanced weak event manager for UFU2 memory optimization.
    /// Provides automatic event subscription tracking and cleanup to prevent memory leaks.
    /// Implements Phase 2D Task 2.2 requirements for automatic event handler cleanup.
    /// Integrates with ResourceManager for comprehensive memory management.
    /// </summary>
    public class WeakEventManager : IDisposable
    {
        #region Private Fields

        private readonly ConcurrentDictionary<string, List<WeakEventSubscription>> _eventSubscriptions;
        private readonly ConcurrentDictionary<string, WeakReference> _eventSources;
        private readonly Timer _cleanupTimer;
        private readonly ResourceManager? _resourceManager;
        private readonly object _subscriptionLock = new object();
        private bool _disposed = false;

        // Performance tracking
        private long _totalSubscriptions = 0;
        private long _totalUnsubscriptions = 0;
        private long _automaticCleanups = 0;
        private long _deadReferencesRemoved = 0;

        // Configuration
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(3);

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the WeakEventManager.
        /// </summary>
        /// <param name="resourceManager">Optional ResourceManager for integration</param>
        public WeakEventManager(ResourceManager? resourceManager = null)
        {
            _eventSubscriptions = new ConcurrentDictionary<string, List<WeakEventSubscription>>();
            _eventSources = new ConcurrentDictionary<string, WeakReference>();
            _resourceManager = resourceManager;

            // Initialize cleanup timer
            _cleanupTimer = new Timer(PerformAutomaticCleanup, null, _cleanupInterval, _cleanupInterval);

            LoggingService.LogInfo("WeakEventManager initialized with automatic cleanup", "WeakEventManager");
        }

        #endregion

        #region Public Methods - Event Subscription

        /// <summary>
        /// Adds a weak event handler for PropertyChanged events.
        /// </summary>
        /// <typeparam name="T">Type of the event source</typeparam>
        /// <param name="source">Event source object</param>
        /// <param name="handler">Event handler to add</param>
        /// <param name="ownerType">Type of the owner subscribing to the event</param>
        public void AddPropertyChangedHandler<T>(T source, PropertyChangedEventHandler handler, Type ownerType) 
            where T : class, INotifyPropertyChanged
        {
            if (source == null || handler == null || ownerType == null)
                return;

            try
            {
                var sourceId = GetSourceId(source);
                var eventName = nameof(INotifyPropertyChanged.PropertyChanged);
                
                // Register with ResourceManager if available
                _resourceManager?.RegisterEventSubscription(sourceId, eventName, handler, ownerType);

                // Create weak subscription
                var subscription = new WeakEventSubscription
                {
                    SourceId = sourceId,
                    EventName = eventName,
                    Handler = new WeakReference(handler),
                    Target = new WeakReference(handler.Target),
                    OwnerType = ownerType,
                    SubscribedAt = DateTime.UtcNow
                };

                lock (_subscriptionLock)
                {
                    var subscriptionKey = $"{sourceId}_{eventName}";
                    _eventSubscriptions.AddOrUpdate(subscriptionKey,
                        new List<WeakEventSubscription> { subscription },
                        (key, existingList) =>
                        {
                            existingList.Add(subscription);
                            return existingList;
                        });

                    // Track the source
                    _eventSources.AddOrUpdate(sourceId, new WeakReference(source), (key, oldValue) => new WeakReference(source));
                }

                // Subscribe to the actual event
                source.PropertyChanged += handler;

                System.Threading.Interlocked.Increment(ref _totalSubscriptions);
                LoggingService.LogDebug($"Added PropertyChanged handler for {ownerType.Name} on {typeof(T).Name}", "WeakEventManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding PropertyChanged handler: {ex.Message}", "WeakEventManager");
            }
        }

        /// <summary>
        /// Adds a weak event handler for collection changed events.
        /// </summary>
        /// <typeparam name="T">Type of the event source</typeparam>
        /// <param name="source">Event source object</param>
        /// <param name="handler">Event handler to add</param>
        /// <param name="ownerType">Type of the owner subscribing to the event</param>
        public void AddCollectionChangedHandler<T>(T source, System.Collections.Specialized.NotifyCollectionChangedEventHandler handler, Type ownerType) 
            where T : class, System.Collections.Specialized.INotifyCollectionChanged
        {
            if (source == null || handler == null || ownerType == null)
                return;

            try
            {
                var sourceId = GetSourceId(source);
                var eventName = nameof(System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged);
                
                // Register with ResourceManager if available
                _resourceManager?.RegisterEventSubscription(sourceId, eventName, handler, ownerType);

                // Create weak subscription
                var subscription = new WeakEventSubscription
                {
                    SourceId = sourceId,
                    EventName = eventName,
                    Handler = new WeakReference(handler),
                    Target = new WeakReference(handler.Target),
                    OwnerType = ownerType,
                    SubscribedAt = DateTime.UtcNow
                };

                lock (_subscriptionLock)
                {
                    var subscriptionKey = $"{sourceId}_{eventName}";
                    _eventSubscriptions.AddOrUpdate(subscriptionKey,
                        new List<WeakEventSubscription> { subscription },
                        (key, existingList) =>
                        {
                            existingList.Add(subscription);
                            return existingList;
                        });

                    // Track the source
                    _eventSources.AddOrUpdate(sourceId, new WeakReference(source), (key, oldValue) => new WeakReference(source));
                }

                // Subscribe to the actual event
                source.CollectionChanged += handler;

                System.Threading.Interlocked.Increment(ref _totalSubscriptions);
                LoggingService.LogDebug($"Added CollectionChanged handler for {ownerType.Name} on {typeof(T).Name}", "WeakEventManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding CollectionChanged handler: {ex.Message}", "WeakEventManager");
            }
        }

        /// <summary>
        /// Adds a generic weak event handler.
        /// </summary>
        /// <typeparam name="T">Type of the event source</typeparam>
        /// <param name="source">Event source object</param>
        /// <param name="eventName">Name of the event</param>
        /// <param name="handler">Event handler to add</param>
        /// <param name="ownerType">Type of the owner subscribing to the event</param>
        public void AddEventHandler<T>(T source, string eventName, Delegate handler, Type ownerType) where T : class
        {
            if (source == null || string.IsNullOrEmpty(eventName) || handler == null || ownerType == null)
                return;

            try
            {
                var sourceId = GetSourceId(source);
                
                // Register with ResourceManager if available
                _resourceManager?.RegisterEventSubscription(sourceId, eventName, handler, ownerType);

                // Create weak subscription
                var subscription = new WeakEventSubscription
                {
                    SourceId = sourceId,
                    EventName = eventName,
                    Handler = new WeakReference(handler),
                    Target = new WeakReference(handler.Target),
                    OwnerType = ownerType,
                    SubscribedAt = DateTime.UtcNow
                };

                lock (_subscriptionLock)
                {
                    var subscriptionKey = $"{sourceId}_{eventName}";
                    _eventSubscriptions.AddOrUpdate(subscriptionKey,
                        new List<WeakEventSubscription> { subscription },
                        (key, existingList) =>
                        {
                            existingList.Add(subscription);
                            return existingList;
                        });

                    // Track the source
                    _eventSources.AddOrUpdate(sourceId, new WeakReference(source), (key, oldValue) => new WeakReference(source));
                }

                System.Threading.Interlocked.Increment(ref _totalSubscriptions);
                LoggingService.LogDebug($"Added {eventName} handler for {ownerType.Name} on {typeof(T).Name}", "WeakEventManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding {eventName} handler: {ex.Message}", "WeakEventManager");
            }
        }

        #endregion

        #region Public Methods - Event Unsubscription

        /// <summary>
        /// Removes a PropertyChanged event handler.
        /// </summary>
        /// <typeparam name="T">Type of the event source</typeparam>
        /// <param name="source">Event source object</param>
        /// <param name="handler">Event handler to remove</param>
        /// <param name="ownerType">Type of the owner that subscribed to the event</param>
        public void RemovePropertyChangedHandler<T>(T source, PropertyChangedEventHandler handler, Type ownerType) 
            where T : class, INotifyPropertyChanged
        {
            if (source == null || handler == null || ownerType == null)
                return;

            try
            {
                var sourceId = GetSourceId(source);
                var eventName = nameof(INotifyPropertyChanged.PropertyChanged);
                
                // Unregister from ResourceManager if available
                _resourceManager?.UnregisterEventSubscription(sourceId, eventName, ownerType);

                // Remove from weak subscriptions
                RemoveWeakSubscription(sourceId, eventName, handler, ownerType);

                // Unsubscribe from the actual event
                source.PropertyChanged -= handler;

                System.Threading.Interlocked.Increment(ref _totalUnsubscriptions);
                LoggingService.LogDebug($"Removed PropertyChanged handler for {ownerType.Name} on {typeof(T).Name}", "WeakEventManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing PropertyChanged handler: {ex.Message}", "WeakEventManager");
            }
        }

        /// <summary>
        /// Removes a CollectionChanged event handler.
        /// </summary>
        /// <typeparam name="T">Type of the event source</typeparam>
        /// <param name="source">Event source object</param>
        /// <param name="handler">Event handler to remove</param>
        /// <param name="ownerType">Type of the owner that subscribed to the event</param>
        public void RemoveCollectionChangedHandler<T>(T source, System.Collections.Specialized.NotifyCollectionChangedEventHandler handler, Type ownerType) 
            where T : class, System.Collections.Specialized.INotifyCollectionChanged
        {
            if (source == null || handler == null || ownerType == null)
                return;

            try
            {
                var sourceId = GetSourceId(source);
                var eventName = nameof(System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged);
                
                // Unregister from ResourceManager if available
                _resourceManager?.UnregisterEventSubscription(sourceId, eventName, ownerType);

                // Remove from weak subscriptions
                RemoveWeakSubscription(sourceId, eventName, handler, ownerType);

                // Unsubscribe from the actual event
                source.CollectionChanged -= handler;

                System.Threading.Interlocked.Increment(ref _totalUnsubscriptions);
                LoggingService.LogDebug($"Removed CollectionChanged handler for {ownerType.Name} on {typeof(T).Name}", "WeakEventManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing CollectionChanged handler: {ex.Message}", "WeakEventManager");
            }
        }

        #endregion

        #region Public Methods - Bulk Operations

        /// <summary>
        /// Removes all event handlers for a specific owner type.
        /// </summary>
        /// <param name="ownerType">Type of the owner to clean up</param>
        public void RemoveAllHandlersForOwner(Type ownerType)
        {
            if (ownerType == null)
                return;

            try
            {
                var subscriptionsToRemove = new List<(string key, WeakEventSubscription subscription)>();

                lock (_subscriptionLock)
                {
                    foreach (var kvp in _eventSubscriptions)
                    {
                        var subscriptionsForOwner = kvp.Value.Where(s => s.OwnerType == ownerType).ToList();
                        foreach (var subscription in subscriptionsForOwner)
                        {
                            subscriptionsToRemove.Add((kvp.Key, subscription));
                        }
                    }
                }

                foreach (var (key, subscription) in subscriptionsToRemove)
                {
                    // Try to unsubscribe from actual events if source is still alive
                    if (_eventSources.TryGetValue(subscription.SourceId, out var sourceRef) && sourceRef.IsAlive)
                    {
                        var source = sourceRef.Target;
                        if (subscription.Handler.IsAlive && subscription.Handler.Target is Delegate handler)
                        {
                            try
                            {
                                UnsubscribeFromActualEvent(source, subscription.EventName, handler);
                            }
                            catch (Exception ex)
                            {
                                LoggingService.LogWarning($"Error unsubscribing from {subscription.EventName}: {ex.Message}", "WeakEventManager");
                            }
                        }
                    }

                    // Remove from weak subscriptions
                    if (_eventSubscriptions.TryGetValue(key, out var subscriptionList))
                    {
                        subscriptionList.RemoveAll(s => s.OwnerType == ownerType);
                        if (subscriptionList.Count == 0)
                        {
                            _eventSubscriptions.TryRemove(key, out _);
                        }
                    }
                }

                // Unregister from ResourceManager if available
                _resourceManager?.UnregisterOwnerResources(ownerType);

                System.Threading.Interlocked.Add(ref _totalUnsubscriptions, subscriptionsToRemove.Count);
                LoggingService.LogInfo($"Removed all event handlers for owner type: {ownerType.Name} ({subscriptionsToRemove.Count} handlers)", "WeakEventManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing all handlers for owner type {ownerType.Name}: {ex.Message}", "WeakEventManager");
            }
        }

        /// <summary>
        /// Forces immediate cleanup of all dead references.
        /// </summary>
        public void ForceCleanup()
        {
            try
            {
                lock (_subscriptionLock)
                {
                    CleanupDeadReferences();
                    System.Threading.Interlocked.Increment(ref _automaticCleanups);
                }

                LoggingService.LogInfo("Forced cleanup of weak event references completed", "WeakEventManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during forced cleanup: {ex.Message}", "WeakEventManager");
            }
        }

        /// <summary>
        /// Gets statistics about event subscription management.
        /// </summary>
        /// <returns>Event management statistics</returns>
        public WeakEventStatistics GetStatistics()
        {
            try
            {
                var stats = new WeakEventStatistics
                {
                    GeneratedAt = DateTime.UtcNow,
                    TotalSubscriptions = _totalSubscriptions,
                    TotalUnsubscriptions = _totalUnsubscriptions,
                    AutomaticCleanups = _automaticCleanups,
                    DeadReferencesRemoved = _deadReferencesRemoved
                };

                lock (_subscriptionLock)
                {
                    stats.ActiveSubscriptions = _eventSubscriptions.Sum(kvp => kvp.Value.Count(s => s.Handler.IsAlive && s.Target.IsAlive));
                    stats.DeadSubscriptions = _eventSubscriptions.Sum(kvp => kvp.Value.Count(s => !s.Handler.IsAlive || !s.Target.IsAlive));
                    stats.TrackedSources = _eventSources.Count(kvp => kvp.Value.IsAlive);
                    stats.DeadSources = _eventSources.Count(kvp => !kvp.Value.IsAlive);

                    // Event breakdown by type
                    stats.SubscriptionsByEventType = _eventSubscriptions
                        .SelectMany(kvp => kvp.Value)
                        .Where(s => s.Handler.IsAlive)
                        .GroupBy(s => s.EventName)
                        .ToDictionary(g => g.Key, g => g.Count());

                    // Owner type breakdown
                    stats.SubscriptionsByOwnerType = _eventSubscriptions
                        .SelectMany(kvp => kvp.Value)
                        .Where(s => s.Handler.IsAlive)
                        .GroupBy(s => s.OwnerType.Name)
                        .ToDictionary(g => g.Key, g => g.Count());
                }

                return stats;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating weak event statistics: {ex.Message}", "WeakEventManager");
                return new WeakEventStatistics { GeneratedAt = DateTime.UtcNow };
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Gets a unique identifier for an event source.
        /// </summary>
        private string GetSourceId(object source)
        {
            return $"{source.GetType().Name}_{source.GetHashCode()}";
        }

        /// <summary>
        /// Removes a weak subscription from tracking.
        /// </summary>
        private void RemoveWeakSubscription(string sourceId, string eventName, Delegate handler, Type ownerType)
        {
            lock (_subscriptionLock)
            {
                var subscriptionKey = $"{sourceId}_{eventName}";
                if (_eventSubscriptions.TryGetValue(subscriptionKey, out var subscriptions))
                {
                    subscriptions.RemoveAll(s => s.OwnerType == ownerType &&
                                                s.Handler.IsAlive &&
                                                ReferenceEquals(s.Handler.Target, handler));

                    if (subscriptions.Count == 0)
                    {
                        _eventSubscriptions.TryRemove(subscriptionKey, out _);
                    }
                }
            }
        }

        /// <summary>
        /// Unsubscribes from the actual event based on event name.
        /// </summary>
        private void UnsubscribeFromActualEvent(object? source, string eventName, Delegate handler)
        {
            if (source == null || handler == null)
                return;

            switch (eventName)
            {
                case nameof(INotifyPropertyChanged.PropertyChanged):
                    if (source is INotifyPropertyChanged propertySource && handler is PropertyChangedEventHandler propertyHandler)
                    {
                        propertySource.PropertyChanged -= propertyHandler;
                    }
                    break;

                case nameof(System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged):
                    if (source is System.Collections.Specialized.INotifyCollectionChanged collectionSource &&
                        handler is System.Collections.Specialized.NotifyCollectionChangedEventHandler collectionHandler)
                    {
                        collectionSource.CollectionChanged -= collectionHandler;
                    }
                    break;

                default:
                    LoggingService.LogWarning($"Unknown event type for automatic unsubscription: {eventName}", "WeakEventManager");
                    break;
            }
        }

        /// <summary>
        /// Performs automatic cleanup of dead references.
        /// Called periodically by the cleanup timer.
        /// </summary>
        private void PerformAutomaticCleanup(object? state)
        {
            try
            {
                lock (_subscriptionLock)
                {
                    CleanupDeadReferences();
                    System.Threading.Interlocked.Increment(ref _automaticCleanups);
                }

                LoggingService.LogDebug("Automatic weak event cleanup completed", "WeakEventManager");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during automatic weak event cleanup: {ex.Message}", "WeakEventManager");
            }
        }

        /// <summary>
        /// Cleans up dead weak references from event subscriptions.
        /// </summary>
        private void CleanupDeadReferences()
        {
            var subscriptionsToUpdate = new List<string>();
            var deadReferencesCount = 0;

            // Clean up dead event subscriptions
            foreach (var kvp in _eventSubscriptions)
            {
                var aliveSubscriptions = kvp.Value.Where(s => s.Handler.IsAlive && s.Target.IsAlive).ToList();
                deadReferencesCount += kvp.Value.Count - aliveSubscriptions.Count;

                if (aliveSubscriptions.Count != kvp.Value.Count)
                {
                    subscriptionsToUpdate.Add(kvp.Key);
                }
            }

            foreach (var key in subscriptionsToUpdate)
            {
                if (_eventSubscriptions.TryGetValue(key, out var subscriptions))
                {
                    var aliveSubscriptions = subscriptions.Where(s => s.Handler.IsAlive && s.Target.IsAlive).ToList();
                    if (aliveSubscriptions.Count == 0)
                    {
                        _eventSubscriptions.TryRemove(key, out _);
                    }
                    else
                    {
                        _eventSubscriptions[key] = aliveSubscriptions;
                    }
                }
            }

            // Clean up dead event sources
            var deadSources = _eventSources.Where(kvp => !kvp.Value.IsAlive).Select(kvp => kvp.Key).ToList();
            foreach (var sourceId in deadSources)
            {
                _eventSources.TryRemove(sourceId, out _);
            }

            System.Threading.Interlocked.Add(ref _deadReferencesRemoved, deadReferencesCount + deadSources.Count);

            if (deadReferencesCount > 0 || deadSources.Count > 0)
            {
                LoggingService.LogDebug($"Cleaned up {deadReferencesCount} dead event subscriptions and {deadSources.Count} dead sources", "WeakEventManager");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the WeakEventManager and cleans up all tracked event subscriptions.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method for proper disposal pattern.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        LoggingService.LogInfo("Disposing WeakEventManager and cleaning up all event subscriptions", "WeakEventManager");

                        // Stop cleanup timer
                        _cleanupTimer?.Dispose();

                        // Force final cleanup
                        ForceCleanup();

                        // Clear all collections
                        _eventSubscriptions.Clear();
                        _eventSources.Clear();

                        LoggingService.LogInfo($"WeakEventManager disposed. Final stats: {_totalSubscriptions} subscriptions, {_totalUnsubscriptions} unsubscriptions, {_automaticCleanups} cleanups", "WeakEventManager");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error during WeakEventManager disposal: {ex.Message}", "WeakEventManager");
                    }
                }
                _disposed = true;
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Information about a weak event subscription.
    /// </summary>
    public class WeakEventSubscription
    {
        public string SourceId { get; set; } = string.Empty;
        public string EventName { get; set; } = string.Empty;
        public WeakReference Handler { get; set; } = new WeakReference(null);
        public WeakReference Target { get; set; } = new WeakReference(null);
        public Type OwnerType { get; set; } = typeof(object);
        public DateTime SubscribedAt { get; set; }
    }

    /// <summary>
    /// Statistics about weak event management.
    /// </summary>
    public class WeakEventStatistics
    {
        public DateTime GeneratedAt { get; set; }
        public long TotalSubscriptions { get; set; }
        public long TotalUnsubscriptions { get; set; }
        public long AutomaticCleanups { get; set; }
        public long DeadReferencesRemoved { get; set; }
        public int ActiveSubscriptions { get; set; }
        public int DeadSubscriptions { get; set; }
        public int TrackedSources { get; set; }
        public int DeadSources { get; set; }
        public Dictionary<string, int> SubscriptionsByEventType { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> SubscriptionsByOwnerType { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Gets the subscription cleanup efficiency percentage.
        /// </summary>
        public double CleanupEfficiency => TotalSubscriptions > 0 ? (double)TotalUnsubscriptions / TotalSubscriptions * 100 : 0;

        /// <summary>
        /// Indicates if there are potential memory leaks from event subscriptions.
        /// </summary>
        public bool HasPotentialLeaks => DeadSubscriptions > 0 || DeadSources > 0 || CleanupEfficiency < 90;
    }

    #endregion
}
