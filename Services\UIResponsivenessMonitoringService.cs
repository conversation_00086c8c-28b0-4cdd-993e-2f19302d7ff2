using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Threading;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// UI responsiveness monitoring service for UFU2 client data management application.
    /// Provides comprehensive UI thread blocking detection, responsiveness metrics collection,
    /// performance monitoring, and automatic optimization recommendations.
    /// 
    /// Features:
    /// - Real-time UI thread blocking detection
    /// - Responsiveness metrics collection and analysis
    /// - Performance trend monitoring
    /// - Automatic optimization recommendations
    /// - Integration with UFU2 ServiceLocator and logging infrastructure
    /// - Arabic RTL UI compatibility monitoring
    /// </summary>
    public class UIResponsivenessMonitoringService : IDisposable
    {
        #region Constants

        // Monitoring intervals and thresholds
        private const int MonitoringIntervalMs = 500; // Check responsiveness every 500ms
        private const int ResponsivenessTestIntervalMs = 100; // Test UI thread every 100ms
        private const int AcceptableResponseTimeMs = 16; // 60 FPS target
        private const int SlowResponseTimeMs = 33; // 30 FPS threshold
        private const int BlockedResponseTimeMs = 100; // Blocked threshold
        private const int CriticalBlockedResponseTimeMs = 500; // Critical blocking threshold

        // Performance tracking constants
        private const int MaxHistorySize = 100; // Keep last 100 measurements
        private const int RecommendationCooldownMs = 30000; // 30 seconds between recommendations
        private const double PerformanceDegradationThreshold = 0.2; // 20% degradation

        #endregion

        #region Private Fields

        // Core monitoring infrastructure
        private readonly Dispatcher _dispatcher;
        private readonly DispatcherTimer _monitoringTimer;
        private readonly DispatcherTimer _responsivenessTestTimer;
        private bool _disposed = false;

        // Performance metrics
        private readonly Queue<UIResponsivenessMetric> _performanceHistory = new();
        private readonly Queue<double> _recentResponseTimes = new();
        private UIResponsivenessLevel _currentResponsivenessLevel = UIResponsivenessLevel.Excellent;
        private DateTime _lastRecommendationTime = DateTime.MinValue;

        // Blocking detection
        private bool _isUIThreadBlocked = false;
        private DateTime _blockingStartTime = DateTime.MinValue;
        private int _totalBlockingEvents = 0;
        private double _totalBlockingTime = 0;
        private double _longestBlockingTime = 0;

        // Performance trends
        private double _averageResponseTime = 0;
        private double _responseTimeVariance = 0;
        private int _consecutiveSlowFrames = 0;
        private int _consecutiveBlockedFrames = 0;

        #endregion

        #region Constructor and Initialization

        /// <summary>
        /// Initializes a new instance of the UIResponsivenessMonitoringService.
        /// </summary>
        public UIResponsivenessMonitoringService()
        {
            _dispatcher = Dispatcher.CurrentDispatcher ?? System.Windows.Application.Current?.Dispatcher 
                ?? throw new InvalidOperationException("No Dispatcher available for UIResponsivenessMonitoringService");

            // Initialize monitoring timer
            _monitoringTimer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromMilliseconds(MonitoringIntervalMs)
            };
            _monitoringTimer.Tick += OnMonitoringTick;

            // Initialize responsiveness test timer
            _responsivenessTestTimer = new DispatcherTimer(DispatcherPriority.Background)
            {
                Interval = TimeSpan.FromMilliseconds(ResponsivenessTestIntervalMs)
            };
            _responsivenessTestTimer.Tick += OnResponsivenessTestTick;

            StartMonitoring();

            LoggingService.LogDebug("UIResponsivenessMonitoringService initialized", "UIResponsivenessMonitoringService");
        }

        /// <summary>
        /// Starts UI responsiveness monitoring.
        /// </summary>
        public void StartMonitoring()
        {
            if (!_disposed)
            {
                _monitoringTimer.Start();
                _responsivenessTestTimer.Start();
                LoggingService.LogDebug("UI responsiveness monitoring started", "UIResponsivenessMonitoringService");
            }
        }

        /// <summary>
        /// Stops UI responsiveness monitoring.
        /// </summary>
        public void StopMonitoring()
        {
            _monitoringTimer.Stop();
            _responsivenessTestTimer.Stop();
            LoggingService.LogDebug("UI responsiveness monitoring stopped", "UIResponsivenessMonitoringService");
        }

        #endregion

        #region Public API

        /// <summary>
        /// Gets current UI responsiveness metrics.
        /// </summary>
        /// <returns>Current responsiveness information</returns>
        public UIResponsivenessInfo GetCurrentResponsivenessInfo()
        {
            return new UIResponsivenessInfo
            {
                CurrentResponsivenessLevel = _currentResponsivenessLevel,
                IsUIThreadBlocked = _isUIThreadBlocked,
                AverageResponseTime = _averageResponseTime,
                ResponseTimeVariance = _responseTimeVariance,
                TotalBlockingEvents = _totalBlockingEvents,
                TotalBlockingTime = _totalBlockingTime,
                LongestBlockingTime = _longestBlockingTime,
                ConsecutiveSlowFrames = _consecutiveSlowFrames,
                ConsecutiveBlockedFrames = _consecutiveBlockedFrames,
                PerformanceHistory = _performanceHistory.ToList(),
                RecentResponseTimes = _recentResponseTimes.ToList()
            };
        }

        /// <summary>
        /// Gets automatic optimization recommendations based on current performance.
        /// </summary>
        /// <returns>List of optimization recommendations</returns>
        public List<string> GetOptimizationRecommendations()
        {
            var recommendations = new List<string>();
            var now = DateTime.UtcNow;

            // Check cooldown period
            if ((now - _lastRecommendationTime).TotalMilliseconds < RecommendationCooldownMs)
            {
                return recommendations;
            }

            // Analyze current performance and generate recommendations
            if (_currentResponsivenessLevel == UIResponsivenessLevel.Poor || _currentResponsivenessLevel == UIResponsivenessLevel.Critical)
            {
                recommendations.AddRange(GeneratePerformanceRecommendations());
            }

            if (_totalBlockingEvents > 10)
            {
                recommendations.AddRange(GenerateBlockingRecommendations());
            }

            if (_consecutiveSlowFrames > 30)
            {
                recommendations.AddRange(GenerateFrameRateRecommendations());
            }

            if (recommendations.Count > 0)
            {
                _lastRecommendationTime = now;
                LoggingService.LogInfo($"Generated {recommendations.Count} UI optimization recommendations", "UIResponsivenessMonitoringService");
            }

            return recommendations;
        }

        /// <summary>
        /// Forces a responsiveness check and returns the result.
        /// </summary>
        /// <returns>Response time in milliseconds</returns>
        public async Task<double> ForceResponsivenessCheckAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            
            await _dispatcher.InvokeAsync(() => { /* Empty operation */ }, DispatcherPriority.Normal);
            
            stopwatch.Stop();
            return stopwatch.ElapsedMilliseconds;
        }

        #endregion

        #region Private Implementation

        /// <summary>
        /// Handles monitoring timer tick for performance analysis.
        /// </summary>
        private void OnMonitoringTick(object? sender, EventArgs e)
        {
            try
            {
                AnalyzePerformanceTrends();
                UpdateResponsivenessLevel();
                CheckForPerformanceDegradation();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during UI responsiveness monitoring: {ex.Message}", "UIResponsivenessMonitoringService");
            }
        }

        /// <summary>
        /// Handles responsiveness test timer tick.
        /// </summary>
        private void OnResponsivenessTestTick(object? sender, EventArgs e)
        {
            try
            {
                PerformResponsivenessTest();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during UI responsiveness test: {ex.Message}", "UIResponsivenessMonitoringService");
            }
        }

        /// <summary>
        /// Performs a responsiveness test by measuring UI thread response time.
        /// </summary>
        private void PerformResponsivenessTest()
        {
            var stopwatch = Stopwatch.StartNew();
            
            _dispatcher.Invoke(() => { /* Empty operation to test responsiveness */ }, DispatcherPriority.Normal);
            
            stopwatch.Stop();
            var responseTime = stopwatch.ElapsedMilliseconds;

            // Update metrics
            UpdateResponseTimeMetrics(responseTime);
            DetectBlocking(responseTime);
            
            // Record metric
            var metric = new UIResponsivenessMetric
            {
                Timestamp = DateTime.UtcNow,
                ResponseTime = responseTime,
                IsBlocked = responseTime > BlockedResponseTimeMs,
                ResponsivenessLevel = DetermineResponsivenessLevel(responseTime)
            };

            _performanceHistory.Enqueue(metric);
            
            // Keep history size manageable
            while (_performanceHistory.Count > MaxHistorySize)
            {
                _performanceHistory.Dequeue();
            }
        }

        /// <summary>
        /// Updates response time metrics with the latest measurement.
        /// </summary>
        /// <param name="responseTime">Latest response time in milliseconds</param>
        private void UpdateResponseTimeMetrics(double responseTime)
        {
            _recentResponseTimes.Enqueue(responseTime);

            // Keep only recent measurements
            while (_recentResponseTimes.Count > 50)
            {
                _recentResponseTimes.Dequeue();
            }

            // Calculate average and variance
            if (_recentResponseTimes.Count > 0)
            {
                _averageResponseTime = _recentResponseTimes.Average();

                if (_recentResponseTimes.Count > 1)
                {
                    var variance = _recentResponseTimes.Select(x => Math.Pow(x - _averageResponseTime, 2)).Average();
                    _responseTimeVariance = Math.Sqrt(variance);
                }
            }
        }

        /// <summary>
        /// Detects UI thread blocking based on response time.
        /// </summary>
        /// <param name="responseTime">Current response time in milliseconds</param>
        private void DetectBlocking(double responseTime)
        {
            var wasBlocked = _isUIThreadBlocked;
            _isUIThreadBlocked = responseTime > BlockedResponseTimeMs;

            if (_isUIThreadBlocked && !wasBlocked)
            {
                // Blocking started
                _blockingStartTime = DateTime.UtcNow;
                _totalBlockingEvents++;
                LoggingService.LogWarning($"UI thread blocking detected - Response time: {responseTime:F1}ms", "UIResponsivenessMonitoringService");
            }
            else if (!_isUIThreadBlocked && wasBlocked)
            {
                // Blocking ended
                var blockingDuration = (DateTime.UtcNow - _blockingStartTime).TotalMilliseconds;
                _totalBlockingTime += blockingDuration;
                _longestBlockingTime = Math.Max(_longestBlockingTime, blockingDuration);
                LoggingService.LogDebug($"UI thread blocking resolved - Duration: {blockingDuration:F1}ms", "UIResponsivenessMonitoringService");
            }

            // Update consecutive frame counters
            if (responseTime > SlowResponseTimeMs)
            {
                _consecutiveSlowFrames++;
            }
            else
            {
                _consecutiveSlowFrames = 0;
            }

            if (responseTime > BlockedResponseTimeMs)
            {
                _consecutiveBlockedFrames++;
            }
            else
            {
                _consecutiveBlockedFrames = 0;
            }
        }

        /// <summary>
        /// Determines responsiveness level based on response time.
        /// </summary>
        /// <param name="responseTime">Response time in milliseconds</param>
        /// <returns>Responsiveness level</returns>
        private UIResponsivenessLevel DetermineResponsivenessLevel(double responseTime)
        {
            return responseTime switch
            {
                < AcceptableResponseTimeMs => UIResponsivenessLevel.Excellent,
                < SlowResponseTimeMs => UIResponsivenessLevel.Good,
                < BlockedResponseTimeMs => UIResponsivenessLevel.Fair,
                < CriticalBlockedResponseTimeMs => UIResponsivenessLevel.Poor,
                _ => UIResponsivenessLevel.Critical
            };
        }

        /// <summary>
        /// Analyzes performance trends over time.
        /// </summary>
        private void AnalyzePerformanceTrends()
        {
            if (_performanceHistory.Count < 10) return;

            var recentMetrics = _performanceHistory.TakeLast(10).ToList();
            var averageRecentResponseTime = recentMetrics.Average(m => m.ResponseTime);

            // Check for performance degradation
            if (_averageResponseTime > 0)
            {
                var degradation = (averageRecentResponseTime - _averageResponseTime) / _averageResponseTime;
                if (degradation > PerformanceDegradationThreshold)
                {
                    LoggingService.LogWarning($"UI performance degradation detected: {degradation * 100:F1}% increase in response time", "UIResponsivenessMonitoringService");
                }
            }
        }

        /// <summary>
        /// Updates the current responsiveness level based on recent performance.
        /// </summary>
        private void UpdateResponsivenessLevel()
        {
            if (_recentResponseTimes.Count > 0)
            {
                var recentAverage = _recentResponseTimes.TakeLast(10).Average();
                var newLevel = DetermineResponsivenessLevel(recentAverage);

                if (newLevel != _currentResponsivenessLevel)
                {
                    LoggingService.LogDebug($"UI responsiveness level changed from {_currentResponsivenessLevel} to {newLevel}", "UIResponsivenessMonitoringService");
                    _currentResponsivenessLevel = newLevel;
                }
            }
        }

        /// <summary>
        /// Checks for overall performance degradation patterns.
        /// </summary>
        private void CheckForPerformanceDegradation()
        {
            if (_performanceHistory.Count < 20) return;

            var oldMetrics = _performanceHistory.Take(10).ToList();
            var newMetrics = _performanceHistory.TakeLast(10).ToList();

            var oldAverage = oldMetrics.Average(m => m.ResponseTime);
            var newAverage = newMetrics.Average(m => m.ResponseTime);

            if (oldAverage > 0)
            {
                var degradation = (newAverage - oldAverage) / oldAverage;
                if (degradation > PerformanceDegradationThreshold)
                {
                    LoggingService.LogWarning($"Sustained UI performance degradation detected: {degradation * 100:F1}% increase over time", "UIResponsivenessMonitoringService");
                }
            }
        }

        /// <summary>
        /// Generates performance-related optimization recommendations.
        /// </summary>
        /// <returns>List of performance recommendations</returns>
        private List<string> GeneratePerformanceRecommendations()
        {
            var recommendations = new List<string>();

            if (_averageResponseTime > CriticalBlockedResponseTimeMs)
            {
                recommendations.Add("Critical UI performance issue detected. Consider reducing data binding complexity in UFU2 client forms.");
                recommendations.Add("Review database query performance in UFU2 client data operations.");
                recommendations.Add("Consider implementing data virtualization for large UFU2 client collections.");
            }
            else if (_averageResponseTime > BlockedResponseTimeMs)
            {
                recommendations.Add("UI responsiveness below optimal. Consider enabling UFU2 property change batching.");
                recommendations.Add("Review UFU2 collection notification coalescing settings.");
                recommendations.Add("Consider optimizing Arabic RTL text rendering in UFU2 MaterialDesign components.");
            }

            if (_responseTimeVariance > 50)
            {
                recommendations.Add("High UI response time variance detected. Consider stabilizing UFU2 background operations.");
            }

            return recommendations;
        }

        /// <summary>
        /// Generates blocking-related optimization recommendations.
        /// </summary>
        /// <returns>List of blocking recommendations</returns>
        private List<string> GenerateBlockingRecommendations()
        {
            var recommendations = new List<string>();

            recommendations.Add("Frequent UI thread blocking detected. Review UFU2 database operations for async/await patterns.");
            recommendations.Add("Consider moving UFU2 client data processing to background threads.");
            recommendations.Add("Review UFU2 validation logic for performance bottlenecks.");

            if (_longestBlockingTime > 1000)
            {
                recommendations.Add("Extended UI blocking detected. Implement progress indicators for long UFU2 operations.");
            }

            return recommendations;
        }

        /// <summary>
        /// Generates frame rate-related optimization recommendations.
        /// </summary>
        /// <returns>List of frame rate recommendations</returns>
        private List<string> GenerateFrameRateRecommendations()
        {
            var recommendations = new List<string>();

            recommendations.Add("Sustained low frame rate detected. Consider reducing UFU2 UI update frequency.");
            recommendations.Add("Review UFU2 property change notification patterns for optimization opportunities.");
            recommendations.Add("Consider implementing UFU2 smart batching for collection updates.");

            return recommendations;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the UIResponsivenessMonitoringService and cleans up resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method for proper disposal pattern.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    StopMonitoring();
                    LoggingService.LogDebug("UIResponsivenessMonitoringService disposed successfully", "UIResponsivenessMonitoringService");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error during UIResponsivenessMonitoringService disposal: {ex.Message}", "UIResponsivenessMonitoringService");
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// UI responsiveness levels for UFU2 performance classification.
    /// </summary>
    public enum UIResponsivenessLevel
    {
        Excellent = 0,  // < 16ms (60+ FPS)
        Good = 1,       // 16-33ms (30-60 FPS)
        Fair = 2,       // 33-100ms (10-30 FPS)
        Poor = 3,       // 100-500ms (2-10 FPS)
        Critical = 4    // > 500ms (< 2 FPS)
    }

    /// <summary>
    /// UI responsiveness metric for performance tracking.
    /// </summary>
    public class UIResponsivenessMetric
    {
        public DateTime Timestamp { get; set; }
        public double ResponseTime { get; set; }
        public bool IsBlocked { get; set; }
        public UIResponsivenessLevel ResponsivenessLevel { get; set; }
    }

    /// <summary>
    /// Comprehensive UI responsiveness information.
    /// </summary>
    public class UIResponsivenessInfo
    {
        public UIResponsivenessLevel CurrentResponsivenessLevel { get; set; }
        public bool IsUIThreadBlocked { get; set; }
        public double AverageResponseTime { get; set; }
        public double ResponseTimeVariance { get; set; }
        public int TotalBlockingEvents { get; set; }
        public double TotalBlockingTime { get; set; }
        public double LongestBlockingTime { get; set; }
        public int ConsecutiveSlowFrames { get; set; }
        public int ConsecutiveBlockedFrames { get; set; }
        public List<UIResponsivenessMetric> PerformanceHistory { get; set; } = new();
        public List<double> RecentResponseTimes { get; set; } = new();

        public override string ToString()
        {
            return $"UI Responsiveness - Level: {CurrentResponsivenessLevel}, " +
                   $"Blocked: {IsUIThreadBlocked}, Avg Response: {AverageResponseTime:F1}ms, " +
                   $"Blocking Events: {TotalBlockingEvents}, Slow Frames: {ConsecutiveSlowFrames}";
        }
    }
}
