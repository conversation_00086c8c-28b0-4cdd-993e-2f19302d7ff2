using System;
using System.Windows;
using UFU2.Common;
using UFU2.Services;

namespace UFU2.Common
{
    /// <summary>
    /// Error management utility for UFU2 application.
    /// Provides centralized error handling, logging, and user notification functionality.
    /// Integrates with existing LoggingService and follows UFU2 error handling patterns.
    /// </summary>
    public static class ErrorManager
    {
        #region Exception Logging

        /// <summary>
        /// Logs an exception with the specified log level and source
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="logLevel">The log level for the exception</param>
        /// <param name="source">The source component where the exception occurred</param>
        public static void LogException(Exception exception, LogLevel logLevel, string source = "UFU2")
        {
            if (exception == null)
            {
                LoggingService.LogWarning("Attempted to log null exception", source);
                return;
            }

            var errorMessage = $"Exception: {exception.Message}";
            var stackTrace = $"Stack trace: {exception.StackTrace}";

            switch (logLevel)
            {
                case LogLevel.Error:
                    LoggingService.LogError(errorMessage, source);
                    LoggingService.LogError(stackTrace, source);
                    break;
                case LogLevel.Warning:
                    LoggingService.LogWarning(errorMessage, source);
                    LoggingService.LogWarning(stackTrace, source);
                    break;
                case LogLevel.Info:
                    LoggingService.LogInfo(errorMessage, source);
                    LoggingService.LogInfo(stackTrace, source);
                    break;
                case LogLevel.Debug:
                    LoggingService.LogDebug(errorMessage, source);
                    LoggingService.LogDebug(stackTrace, source);
                    break;
                default:
                    LoggingService.LogError(errorMessage, source);
                    LoggingService.LogError(stackTrace, source);
                    break;
            }

            // Log inner exception if present
            if (exception.InnerException != null)
            {
                LoggingService.LogError($"Inner exception: {exception.InnerException.Message}", source);
                LoggingService.LogError($"Inner stack trace: {exception.InnerException.StackTrace}", source);
            }
        }

        #endregion

        #region User Error Display

        /// <summary>
        /// Shows a user-friendly error message dialog
        /// </summary>
        /// <param name="message">The error message to display to the user</param>
        /// <param name="title">The title of the error dialog</param>
        /// <param name="logSource">Optional source for logging the error display</param>
        public static void ShowUserError(string message, string title = "خطأ", string logSource = "ErrorManager")
        {
            try
            {
                LoggingService.LogInfo($"Displaying user error: {title} - {message}", logSource);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        message,
                        title,
                        MessageBoxButton.OK,
                        MessageBoxImage.Error,
                        MessageBoxResult.OK,
                        MessageBoxOptions.RtlReading); // Support for Arabic RTL text
                });
            }
            catch (Exception ex)
            {
                // Fallback logging if UI thread is not available
                LoggingService.LogError($"Failed to show user error dialog: {ex.Message}", logSource);
                
                // Try to show a basic console message as last resort
                Console.WriteLine($"ERROR: {title} - {message}");
            }
        }

        /// <summary>
        /// Shows a user-friendly warning message dialog
        /// </summary>
        /// <param name="message">The warning message to display to the user</param>
        /// <param name="title">The title of the warning dialog</param>
        /// <param name="logSource">Optional source for logging the warning display</param>
        public static void ShowUserWarning(string message, string title = "تحذير", string logSource = "ErrorManager")
        {
            try
            {
                LoggingService.LogInfo($"Displaying user warning: {title} - {message}", logSource);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        message,
                        title,
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning,
                        MessageBoxResult.OK,
                        MessageBoxOptions.RtlReading); // Support for Arabic RTL text
                });
            }
            catch (Exception ex)
            {
                // Fallback logging if UI thread is not available
                LoggingService.LogError($"Failed to show user warning dialog: {ex.Message}", logSource);
                
                // Try to show a basic console message as last resort
                Console.WriteLine($"WARNING: {title} - {message}");
            }
        }

        /// <summary>
        /// Shows a user-friendly information message dialog
        /// </summary>
        /// <param name="message">The information message to display to the user</param>
        /// <param name="title">The title of the information dialog</param>
        /// <param name="logSource">Optional source for logging the information display</param>
        public static void ShowUserInfo(string message, string title = "معلومات", string logSource = "ErrorManager")
        {
            try
            {
                LoggingService.LogInfo($"Displaying user info: {title} - {message}", logSource);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        message,
                        title,
                        MessageBoxButton.OK,
                        MessageBoxImage.Information,
                        MessageBoxResult.OK,
                        MessageBoxOptions.RtlReading); // Support for Arabic RTL text
                });
            }
            catch (Exception ex)
            {
                // Fallback logging if UI thread is not available
                LoggingService.LogError($"Failed to show user info dialog: {ex.Message}", logSource);
                
                // Try to show a basic console message as last resort
                Console.WriteLine($"INFO: {title} - {message}");
            }
        }

        #endregion

        #region Combined Error Handling

        /// <summary>
        /// Logs an exception and shows a user-friendly error message
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the error dialog</param>
        /// <param name="logLevel">The log level for the exception</param>
        /// <param name="source">The source component where the exception occurred</param>
        public static void HandleError(Exception exception, string userMessage, string title = "خطأ", 
            LogLevel logLevel = LogLevel.Error, string source = "UFU2")
        {
            // Log the technical exception details
            LogException(exception, logLevel, source);
            
            // Show user-friendly message
            ShowUserError(userMessage, title, source);
        }

        /// <summary>
        /// Logs a warning and shows a user-friendly warning message
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the warning dialog</param>
        /// <param name="source">The source component where the exception occurred</param>
        public static void HandleWarning(Exception exception, string userMessage, string title = "تحذير", 
            string source = "UFU2")
        {
            // Log the technical exception details
            LogException(exception, LogLevel.Warning, source);
            
            // Show user-friendly message
            ShowUserWarning(userMessage, title, source);
        }

        #endregion

        #region Toast-Based User Notifications

        /// <summary>
        /// Shows a user-friendly error message as a toast notification
        /// </summary>
        /// <param name="message">The error message to display to the user</param>
        /// <param name="title">The title of the error notification (optional, defaults to Arabic "خطأ")</param>
        /// <param name="logSource">Optional source for logging the error display</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        public static void ShowUserErrorToast(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 5000)
        {
            try
            {
                var errorTitle = title ?? "خطأ";
                LoggingService.LogInfo($"Displaying user error toast: {errorTitle} - {message}", logSource);

                if (ToastService.IsInitialized)
                {
                    ToastService.Error(errorTitle, message, duration, null, detailMessage);
                }
                else
                {
                    // Fallback to modal dialog if toast service is not available
                    ShowUserError(message, errorTitle, logSource);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show user error toast: {ex.Message}", logSource);
                // Fallback to modal dialog
                ShowUserError(message, title ?? "خطأ", logSource);
            }
        }

        /// <summary>
        /// Shows a user-friendly warning message as a toast notification
        /// </summary>
        /// <param name="message">The warning message to display to the user</param>
        /// <param name="title">The title of the warning notification (optional, defaults to Arabic "تحذير")</param>
        /// <param name="logSource">Optional source for logging the warning display</param>
        /// <param name="detailMessage">Optional detailed message for warning dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        public static void ShowUserWarningToast(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 5000)
        {
            try
            {
                var warningTitle = title ?? "تحذير";
                LoggingService.LogInfo($"Displaying user warning toast: {warningTitle} - {message}", logSource);

                if (ToastService.IsInitialized)
                {
                    ToastService.Warning(warningTitle, message, duration, null, detailMessage);
                }
                else
                {
                    // Fallback to modal dialog if toast service is not available
                    ShowUserWarning(message, warningTitle, logSource);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show user warning toast: {ex.Message}", logSource);
                // Fallback to modal dialog
                ShowUserWarning(message, title ?? "تحذير", logSource);
            }
        }

        /// <summary>
        /// Shows a user-friendly information message as a toast notification
        /// </summary>
        /// <param name="message">The information message to display to the user</param>
        /// <param name="title">The title of the information notification (optional, defaults to Arabic "معلومات")</param>
        /// <param name="logSource">Optional source for logging the information display</param>
        /// <param name="detailMessage">Optional detailed message for info dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        public static void ShowUserInfoToast(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 3000)
        {
            try
            {
                var infoTitle = title ?? "معلومات";
                LoggingService.LogInfo($"Displaying user info toast: {infoTitle} - {message}", logSource);

                if (ToastService.IsInitialized)
                {
                    ToastService.Info(infoTitle, message, duration, null, detailMessage);
                }
                else
                {
                    // Fallback to modal dialog if toast service is not available
                    ShowUserInfo(message, infoTitle, logSource);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show user info toast: {ex.Message}", logSource);
                // Fallback to modal dialog
                ShowUserInfo(message, title ?? "معلومات", logSource);
            }
        }

        /// <summary>
        /// Shows a user-friendly success message as a toast notification
        /// </summary>
        /// <param name="message">The success message to display to the user</param>
        /// <param name="title">The title of the success notification (optional, defaults to Arabic "نجح")</param>
        /// <param name="logSource">Optional source for logging the success display</param>
        /// <param name="detailMessage">Optional detailed message for success dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        public static void ShowUserSuccessToast(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 3000)
        {
            try
            {
                var successTitle = title ?? "نجح";
                LoggingService.LogInfo($"Displaying user success toast: {successTitle} - {message}", logSource);

                if (ToastService.IsInitialized)
                {
                    ToastService.Success(successTitle, message, duration, null, detailMessage);
                }
                else
                {
                    // Fallback to modal dialog if toast service is not available
                    ShowUserInfo(message, successTitle, logSource);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show user success toast: {ex.Message}", logSource);
                // Fallback to modal dialog
                ShowUserInfo(message, title ?? "نجح", logSource);
            }
        }

        #endregion

        #region Combined Toast-Based Error Handling

        /// <summary>
        /// Logs an exception and shows a user-friendly error message as a toast notification
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the error notification</param>
        /// <param name="logLevel">The log level for the exception</param>
        /// <param name="source">The source component where the exception occurred</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        public static void HandleErrorToast(Exception exception, string userMessage, string title = "خطأ",
            LogLevel logLevel = LogLevel.Error, string source = "UFU2", int duration = 5000)
        {
            // Log the technical exception details
            LogException(exception, logLevel, source);

            // Show user-friendly toast message
            ShowUserErrorToast(userMessage, title, source, exception.Message, duration);
        }

        /// <summary>
        /// Logs a warning and shows a user-friendly warning message as a toast notification
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the warning notification</param>
        /// <param name="source">The source component where the exception occurred</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        public static void HandleWarningToast(Exception exception, string userMessage, string title = "تحذير",
            string source = "UFU2", int duration = 5000)
        {
            // Log the technical exception details
            LogException(exception, LogLevel.Warning, source);

            // Show user-friendly toast message
            ShowUserWarningToast(userMessage, title, source, exception.Message, duration);
        }

        #endregion
    }
}
