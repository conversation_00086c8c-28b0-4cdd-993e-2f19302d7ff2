using System.Windows;
using System.Windows.Controls;
using UFU2.ViewModels;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for AddNotesDialog.xaml
    /// A UserControl that provides a dialog for adding or editing individual notes.
    /// Integrates with MaterialDesign DialogHost for consistent modal behavior
    /// and follows UFU2 design patterns with Arabic RTL layout support.
    /// </summary>
    public partial class AddNotesDialog : UserControl
    {
        #region Private Fields
        private AddNotesDialogViewModel _viewModel;
        private bool _dialogResult = false;
        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the AddNotesDialog class for adding a new note.
        /// </summary>
        public AddNotesDialog()
        {
            InitializeComponent();
            InitializeViewModel(new AddNotesDialogViewModel());
        }

        /// <summary>
        /// Initializes a new instance of the AddNotesDialog class for editing an existing note.
        /// </summary>
        /// <param name="existingNote">The note to edit</param>
        public AddNotesDialog(NoteModel existingNote)
        {
            InitializeComponent();
            InitializeViewModel(new AddNotesDialogViewModel(existingNote));
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets the dialog result indicating whether the user saved or cancelled.
        /// </summary>
        public bool DialogResult => _dialogResult;

        /// <summary>
        /// Gets the note data from the dialog.
        /// </summary>
        public NoteModel? NoteData => _viewModel?.GetNoteData();

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes the ViewModel and sets up event handlers.
        /// </summary>
        /// <param name="viewModel">The ViewModel to initialize</param>
        private void InitializeViewModel(AddNotesDialogViewModel viewModel)
        {
            _viewModel = viewModel;
            DataContext = _viewModel;

            // Subscribe to ViewModel events
            _viewModel.SaveRequested += OnSaveRequested;
            _viewModel.CancelRequested += OnCancelRequested;

            // Focus the content TextBox when dialog opens
            Loaded += (s, e) => NoteContentTextBox.Focus();

            LoggingService.LogDebug($"AddNotesDialog initialized - Mode: {(_viewModel.IsEditMode ? "Edit" : "Add")}", "AddNotesDialog");
        }

        /// <summary>
        /// Handles the save request from the ViewModel.
        /// </summary>
        private void OnSaveRequested(object? sender, EventArgs e)
        {
            try
            {
                if (_viewModel?.IsContentValid == true)
                {
                    _dialogResult = true;
                    LoggingService.LogInfo("Note saved successfully", "AddNotesDialog");
                    CloseDialog(true);
                }
                else
                {
                    LoggingService.LogWarning("Attempted to save note with invalid content", "AddNotesDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error saving note: {ex.Message}", "AddNotesDialog");
            }
        }

        /// <summary>
        /// Handles the cancel request from the ViewModel.
        /// </summary>
        private void OnCancelRequested(object? sender, EventArgs e)
        {
            try
            {
                _dialogResult = false;
                LoggingService.LogInfo("Note editing cancelled", "AddNotesDialog");
                CloseDialog(false);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error cancelling note: {ex.Message}", "AddNotesDialog");
            }
        }

        /// <summary>
        /// Closes the dialog with the specified result.
        /// </summary>
        /// <param name="result">The dialog result</param>
        private void CloseDialog(bool result)
        {
            try
            {
                // Close the dialog using MaterialDesign DialogHost
                MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(result, this);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing dialog: {ex.Message}", "AddNotesDialog");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the green flag button click.
        /// </summary>
        private void GreenFlagButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.SelectedPriority = 0; // Normal priority (Green)
                    FlagSelectionPopupBox.IsPopupOpen = false;
                    LoggingService.LogDebug("Green flag selected", "AddNotesDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error selecting green flag: {ex.Message}", "AddNotesDialog");
            }
        }

        /// <summary>
        /// Handles the orange flag button click.
        /// </summary>
        private void OrangeFlagButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.SelectedPriority = 1; // Medium priority (Orange)
                    FlagSelectionPopupBox.IsPopupOpen = false;
                    LoggingService.LogDebug("Orange flag selected", "AddNotesDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error selecting orange flag: {ex.Message}", "AddNotesDialog");
            }
        }

        /// <summary>
        /// Handles the red flag button click.
        /// </summary>
        private void RedFlagButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.SelectedPriority = 2; // High priority (Red)
                    FlagSelectionPopupBox.IsPopupOpen = false;
                    LoggingService.LogDebug("Red flag selected", "AddNotesDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error selecting red flag: {ex.Message}", "AddNotesDialog");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Sets the dialog to edit mode with the specified note.
        /// </summary>
        /// <param name="note">The note to edit</param>
        public void SetEditMode(NoteModel note)
        {
            try
            {
                if (note != null && _viewModel != null)
                {
                    // Create a new ViewModel for editing
                    var editViewModel = new AddNotesDialogViewModel(note);
                    InitializeViewModel(editViewModel);
                    LoggingService.LogDebug($"Dialog set to edit mode for note ID: {note.Id}", "AddNotesDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting edit mode: {ex.Message}", "AddNotesDialog");
            }
        }

        /// <summary>
        /// Resets the dialog to add mode for creating a new note.
        /// </summary>
        public void SetAddMode()
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.ResetForNewNote();
                    _dialogResult = false;
                    LoggingService.LogDebug("Dialog set to add mode", "AddNotesDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting add mode: {ex.Message}", "AddNotesDialog");
            }
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// Cleans up event subscriptions when the control is unloaded.
        /// </summary>
        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.SaveRequested -= OnSaveRequested;
                    _viewModel.CancelRequested -= OnCancelRequested;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cleanup: {ex.Message}", "AddNotesDialog");
            }
        }

        #endregion
    }
}
