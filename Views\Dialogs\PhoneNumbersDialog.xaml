<UserControl
    x:Class="UFU2.Views.Dialogs.PhoneNumbersDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:UFU2.Models"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    d:DesignHeight="550"
    d:DesignWidth="350"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  Phone Number Converter for formatting  -->
            <converters:PhoneNumberConverter x:Key="PhoneNumberConverter" />

            <!--  Primary phone indicator converter  -->
            <converters:IsPrimaryPhoneConverter x:Key="IsPrimaryPhoneConverter" />

            <!--  Inverse boolean to visibility converter  -->
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />

            <!--  Built-in converters  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <!--  Main dialog card  -->
    <materialDesign:Card
        x:Name="MainCard"
        MinWidth="300"
        MinHeight="400"
        MaxWidth="700"
        MaxHeight="600"
        Padding="0"
        Style="{StaticResource DialogBaseCardStyle}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  Header section  -->
            <materialDesign:Card
                Grid.Row="0"
                Margin="0"
                Style="{DynamicResource HeaderCardStyle}">
                <TextBlock
                    HorizontalAlignment="Center"
                    Style="{StaticResource HeadlineStyle}"
                    Text="إدارة أرقام الهاتف" />
            </materialDesign:Card>

            <!--  Phone number input section  -->
            <Grid Grid.Row="1" Margin="12">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  Phone number input  -->
                <TextBox
                    x:Name="PhoneNumberTextBox"
                    Grid.Row="0"
                    Grid.Column="0"
                    Margin="0,0,8,0"
                    materialDesign:HintAssist.Hint="رقم الهاتف"
                    materialDesign:TextFieldAssist.CharacterCounterVisibility="Hidden"
                    MaxLength="13"
                    Style="{StaticResource UnderlineTextBoxStyle}"
                    Text="{Binding NewPhoneNumber, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                    ToolTip="أدخل رقم الهاتف الجديد" />

                <!--  Phone type selection  -->
                <ComboBox
                    x:Name="PhoneTypeComboBox"
                    Grid.Row="0"
                    Grid.Column="1"
                    Margin="8,0,8,0"
                    materialDesign:HintAssist.Hint="نوع الهاتف"
                    DisplayMemberPath="Value"
                    ItemsSource="{Binding PhoneTypes}"
                    SelectedValue="{Binding NewPhoneType}"
                    SelectedValuePath="Key"
                    Style="{StaticResource UnderlineComboBoxStyle}"
                    ToolTip="اختر نوع الهاتف" />

                <!--  Add button  -->
                <Button
                    x:Name="AddPhoneButton"
                    Grid.Row="0"
                    Grid.Column="2"
                    Width="27"
                    Height="27"
                    Margin="8,0,0,0"
                    Command="{Binding AddPhoneNumberCommand}"
                    IsDefault="True"
                    Style="{StaticResource ContainerButtonStyle}"
                    ToolTip="إضافة رقم الهاتف">
                    <materialDesign:PackIcon
                        Width="18"
                        Height="18"
                        Kind="Plus" />
                </Button>
            </Grid>

            <!--  Phone numbers list section  -->
            <materialDesign:Card
                Grid.Row="2"
                Margin="7"
                Padding="1"
                Style="{DynamicResource ContentCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="225" />
                    </Grid.RowDefinitions>

                    <!--  Phone numbers ListView  -->
                    <ListView
                        x:Name="PhoneNumbersListView"
                        Grid.Row="0"
                        MaxHeight="230"
                        ItemsSource="{Binding PhoneNumbers.PhoneNumbers}"
                        ScrollViewer.CanContentScroll="True"
                        ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectedItem="{Binding PhoneNumbers.SelectedPhoneNumber}"
                        Style="{StaticResource MaterialDesignListView}">
                        <ListView.Resources>
                            <Style BasedOn="{StaticResource ListScrollViewerStyle}" TargetType="ScrollViewer" />
                        </ListView.Resources>
                        <ListView.ItemTemplate>
                            <DataTemplate DataType="{x:Type models:PhoneNumberModel}">
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="181" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <!--  Phone type icon  -->
                                    <materialDesign:PackIcon
                                        Grid.Column="0"
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,18,0"
                                        VerticalAlignment="Center"
                                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                                        Kind="{Binding PhoneType, Converter={StaticResource PhoneTypeMultiValueConverter}, ConverterParameter=Icon}"
                                        ToolTip="{Binding PhoneTypeDisplayName}" />

                                    <!--  Phone number and Primary indicator  -->
                                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                                        <!--  Phone number  -->
                                        <TextBlock
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"
                                            FontSize="14"
                                            FontWeight="Medium"
                                            Text="{Binding PhoneNumber}" />

                                        <!--  Primary indicator  -->
                                        <materialDesign:PackIcon
                                            Grid.Column="1"
                                            Width="16"
                                            Height="16"
                                            Margin="8,0"
                                            VerticalAlignment="Center"
                                            Foreground="Yellow"
                                            Kind="Star"
                                            ToolTip="الرقم الأساسي">
                                            <materialDesign:PackIcon.Visibility>
                                                <MultiBinding Converter="{StaticResource IsPrimaryPhoneConverter}">
                                                    <Binding Path="." />
                                                    <Binding Path="DataContext.PhoneNumbers" RelativeSource="{RelativeSource AncestorType=ListView}" />
                                                </MultiBinding>
                                            </materialDesign:PackIcon.Visibility>
                                        </materialDesign:PackIcon>
                                    </StackPanel>

                                    <!--  Remove button  -->
                                    <Button
                                        Grid.Column="2"
                                        Padding="0"
                                        Command="{Binding DataContext.RemovePhoneNumberCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                        CommandParameter="{Binding}"
                                        Style="{DynamicResource IconDeleteButtonStyle}"
                                        ToolTip="حذف رقم الهاتف">
                                        <materialDesign:PackIcon Kind="Delete" />
                                    </Button>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>

                    <!--  Empty state message  -->
                    <TextBlock
                        Grid.Row="0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontStyle="Italic"
                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                        Text="لا توجد أرقام هاتف مضافة"
                        Visibility="{Binding PhoneNumbers.HasPhoneNumbers, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />
                </Grid>
            </materialDesign:Card>

            <!--  Action buttons section  -->
            <userControls:SaveCancelButtonsControl
                Grid.Row="3"
                CancelClick="CancelButton_Click"
                CancelTooltip="إلغاء التغييرات"
                SaveClick="SaveButton_Click"
                SaveTooltip="حفظ أرقام الهاتف" />
        </Grid>
    </materialDesign:Card>
</UserControl>
