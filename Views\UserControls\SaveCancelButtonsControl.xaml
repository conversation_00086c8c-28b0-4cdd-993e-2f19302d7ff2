<UserControl
    x:Class="UFU2.Views.UserControls.SaveCancelButtonsControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="50"
    d:DesignWidth="200"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <!--  Action buttons section  -->
    <materialDesign:Card
        Margin="{Binding CardMargin, RelativeSource={RelativeSource AncestorType=UserControl}}"
        Padding="{Binding CardPadding, RelativeSource={RelativeSource AncestorType=UserControl}}"
        Style="{DynamicResource ContentCardStyle}">
        <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
            <!--  Cancel button  -->
            <Button
                x:Name="CancelButton"
                Margin="{Binding CancelButtonMargin, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Padding="{Binding ButtonPadding, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Click="CancelButton_Click"
                Command="{Binding CancelCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Content="{Binding CancelText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Style="{Binding CancelButtonStyle, RelativeSource={RelativeSource AncestorType=UserControl}}"
                ToolTip="{Binding CancelTooltip, RelativeSource={RelativeSource AncestorType=UserControl}}"
                ToolTipService.Placement="Mouse" />

            <!--  Save button  -->
            <Button
                x:Name="SaveButton"
                Margin="{Binding SaveButtonMargin, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Padding="{Binding ButtonPadding, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Click="SaveButton_Click"
                Command="{Binding SaveCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Content="{Binding SaveText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                IsEnabled="{Binding IsSaveEnabled, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Style="{Binding SaveButtonStyle, RelativeSource={RelativeSource AncestorType=UserControl}}"
                ToolTip="{Binding SaveTooltip, RelativeSource={RelativeSource AncestorType=UserControl}}"
                ToolTipService.Placement="Mouse" />
        </StackPanel>
    </materialDesign:Card>
</UserControl>
