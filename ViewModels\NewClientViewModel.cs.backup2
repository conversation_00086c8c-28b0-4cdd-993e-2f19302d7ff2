using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Common.Converters;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.Services;
using MaterialDesignThemes.Wpf;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the NewClientView providing MVVM data binding and command handling.
    /// Manages client creation workflow with loading states, validation, and activity management.
    /// Supports activity tab switching and dynamic form behavior based on selected activity type.
    /// </summary>
    public class NewClientViewModel : BaseViewModel
    {
        #region Private Fields

        private bool _isLoading;
        private bool _canSave;
        private string _selectedActivityType = "MainCommercial";
        private bool _activityIdFormatEnabled = false;
        private readonly ActivityTypeBaseService? _activityTypeService;
        private readonly CraftTypeBaseService? _craftTypeService;
        private readonly CpiLocationService? _cpiLocationService;

        // Database services for client management
        private ClientDatabaseService? _clientDatabaseService;
        private UIDGenerationService? _uidGenerationService;
        private ClientValidationService? _clientValidationService;
        private ClientFolderManagementService? _clientFolderManagementService;

        // Multiple activities collections for each tab
        private List<ActivityTypeBaseModel> _mainCommercialActivities = new List<ActivityTypeBaseModel>();
        private List<ActivityTypeBaseModel> _secondaryCommercialActivities = new List<ActivityTypeBaseModel>();

        // Tab-specific activity models for data persistence
        private ActivityModel _mainCommercialActivity = new ActivityModel();
        private ActivityModel _secondaryCommercialActivity = new ActivityModel();
        private ActivityModel _craftActivity = new ActivityModel();
        private ActivityModel _professionalActivity = new ActivityModel();

        // Tab-specific file check state models for data persistence
        private FileCheckStatesModel _mainCommercialFileCheckStates = new FileCheckStatesModel();
        private FileCheckStatesModel _secondaryCommercialFileCheckStates = new FileCheckStatesModel();
        private FileCheckStatesModel _craftFileCheckStates = new FileCheckStatesModel();
        private FileCheckStatesModel _professionalFileCheckStates = new FileCheckStatesModel();

        // Phone numbers collection for personal information
        private PhoneNumbersCollectionModel _phoneNumbers = new PhoneNumbersCollectionModel();

        // CPI Location collections and selected values
        private List<CpiWilaya> _cpiWilayas = new List<CpiWilaya>();
        private ObservableCollection<CpiDaira> _cpiDairas = new ObservableCollection<CpiDaira>();
        private CpiWilaya? _selectedCpiWilaya;
        private CpiDaira? _selectedCpiDaira;
        private bool _isDairasUpdateInProgress = false;

        // Personal information fields
        private string _nameFr = string.Empty;
        private string _nameAr = string.Empty;
        private string _birthDate = string.Empty;
        private string _birthPlace = string.Empty;
        private int _gender = 0;
        private string _address = string.Empty;
        private string _nationalId = string.Empty;

        // Notes collection for client notes management
        private NotesCollectionModel _notes = new NotesCollectionModel();

        // Tab-specific payment years collections for data persistence
        private List<int> _mainCommercialG12SelectedYears = new List<int>();
        private List<int> _mainCommercialBISSelectedYears = new List<int>();
        private List<int> _secondaryCommercialG12SelectedYears = new List<int>();
        private List<int> _secondaryCommercialBISSelectedYears = new List<int>();
        private List<int> _craftG12SelectedYears = new List<int>();
        private List<int> _craftBISSelectedYears = new List<int>();
        private List<int> _professionalG12SelectedYears = new List<int>();
        private List<int> _professionalBISSelectedYears = new List<int>();

        // Tab-specific display text for payment years
        private string _mainCommercialG12DisplayText = "لم يتم التحديد بعد";
        private string _mainCommercialBISDisplayText = "لم يتم التحديد بعد";
        private string _secondaryCommercialG12DisplayText = "لم يتم التحديد بعد";
        private string _secondaryCommercialBISDisplayText = "لم يتم التحديد بعد";
        private string _craftG12DisplayText = "لم يتم التحديد بعد";
        private string _craftBISDisplayText = "لم يتم التحديد بعد";
        private string _professionalG12DisplayText = "لم يتم التحديد بعد";
        private string _professionalBISDisplayText = "لم يتم التحديد بعد";

        // Profile image management
        private System.Windows.Media.Imaging.BitmapSource? _profileImage;
        private string? _profileImageOriginalExtension;

        // State management for existing client editing
        private bool _isEditingExistingClient = false;
        private string? _existingClientUid = null;
        private DuplicateClientData? _originalClientData = null;

        #endregion

        #region Events and Delegates

        /// <summary>
        /// Delegate for requesting data collection from views before save operations.
        /// </summary>
        public Action? RequestDataCollection { get; set; }

        /// <summary>
        /// Event raised when the edit status command is executed.
        /// The view should handle this event to open the ActivityStatusUpdateDialog.
        /// </summary>
        public event Action? EditStatusRequested;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets a value indicating whether the view is in loading state
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    // Update CanSave when loading state changes
                    UpdateCanSave();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the save operation can be executed
        /// </summary>
        public bool CanSave
        {
            get => _canSave;
            set => SetProperty(ref _canSave, value);
        }

        /// <summary>
        /// Gets or sets the currently selected activity type.
        /// Valid values: "MainCommercial", "SecondaryCommercial", "Craft", "Professional"
        /// Data persistence: Switching between tabs preserves previously entered data.
        /// </summary>
        public string SelectedActivityType
        {
            get => _selectedActivityType;
            set
            {
                if (SetProperty(ref _selectedActivityType, value))
                {
                    // Ensure the current activity has a default status if it's empty
                    EnsureDefaultActivityStatus(value);

                    // Notify that all tab-specific properties have changed to switch to the appropriate tab's data
                    OnPropertyChanged(nameof(CurrentActivity));
                    OnPropertyChanged(nameof(CurrentFileCheckStates));
                    OnPropertyChanged(nameof(G12SelectedYears));
                    OnPropertyChanged(nameof(BISSelectedYears));
                    OnPropertyChanged(nameof(G12DisplayText));
                    OnPropertyChanged(nameof(BISDisplayText));

                    // Synchronize CPI location selections with the current activity
                    SynchronizeCpiLocationSelections();

                    LoggingService.LogInfo($"Activity type switched to: {value}", "NewClientViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the alternate Activity UID format is enabled.
        /// When true, Activity UIDs will include an 's' suffix (e.g., A01_Act1s instead of A01_Act1).
        /// This setting affects UID generation during activity creation workflow.
        /// </summary>
        public bool ActivityIdFormatEnabled
        {
            get => _activityIdFormatEnabled;
            set
            {
                if (SetProperty(ref _activityIdFormatEnabled, value))
                {
                    LoggingService.LogDebug($"Activity ID format toggled: {(value ? "Alternate format (with 's' suffix)" : "Standard format")}", "NewClientViewModel");
                }
            }
        }

        /// <summary>
        /// Gets the current activity model containing all activity-related data for the selected tab.
        /// This model is bound to the NActivityDetailView form fields.
        /// Returns the appropriate ActivityModel instance based on SelectedActivityType.
        /// Data persists when switching between tabs.
        /// </summary>
        public ActivityModel CurrentActivity
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialActivity,
                    "SecondaryCommercial" => _secondaryCommercialActivity,
                    "Craft" => _craftActivity,
                    "Professional" => _professionalActivity,
                    _ => _mainCommercialActivity // Default fallback
                };
            }
        }

        /// <summary>
        /// Gets the current multiple activities collection for the selected tab.
        /// Only MainCommercial and SecondaryCommercial tabs support multiple activities.
        /// </summary>
        public List<ActivityTypeBaseModel> CurrentMultipleActivities
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialActivities,
                    "SecondaryCommercial" => _secondaryCommercialActivities,
                    _ => new List<ActivityTypeBaseModel>() // Empty list for unsupported tabs
                };
            }
        }

        /// <summary>
        /// Gets the current file check states model containing all file check completion status for the selected tab.
        /// This model is bound to the NFileCheckView checkbox controls.
        /// Returns the appropriate FileCheckStatesModel instance based on SelectedActivityType.
        /// File check states persist independently when switching between tabs.
        /// </summary>
        public FileCheckStatesModel CurrentFileCheckStates
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialFileCheckStates,
                    "SecondaryCommercial" => _secondaryCommercialFileCheckStates,
                    "Craft" => _craftFileCheckStates,
                    "Professional" => _professionalFileCheckStates,
                    _ => _mainCommercialFileCheckStates // Default fallback
                };
            }
        }

        /// <summary>
        /// Gets or sets the French name (Latin characters) for the client.
        /// This property is used for validation and is required for client creation.
        /// </summary>
        public string NameFr
        {
            get => _nameFr;
            set
            {
                if (SetProperty(ref _nameFr, value))
                {
                    // Update CanSave when NameFr changes
                    UpdateCanSave();
                }
            }
        }

        /// <summary>
        /// Gets or sets the Arabic name for the client.
        /// Optional field for client creation.
        /// </summary>
        public string NameAr
        {
            get => _nameAr;
            set => SetProperty(ref _nameAr, value);
        }

        /// <summary>
        /// Gets or sets the birth date in DD/MM/YYYY format.
        /// Supports partial dates like "xx/xx/1993" and complete dates like "23/03/1993".
        /// Optional field for client creation.
        /// </summary>
        public string BirthDate
        {
            get => _birthDate;
            set => SetProperty(ref _birthDate, value);
        }

        /// <summary>
        /// Gets or sets the birth place.
        /// Optional field for client creation.
        /// </summary>
        public string BirthPlace
        {
            get => _birthPlace;
            set => SetProperty(ref _birthPlace, value);
        }

        /// <summary>
        /// Gets or sets the gender.
        /// 0 = Male (ذكر), 1 = Female (أنثى)
        /// </summary>
        public int Gender
        {
            get => _gender;
            set => SetProperty(ref _gender, value);
        }

        /// <summary>
        /// Gets or sets the address.
        /// Optional field for client creation.
        /// </summary>
        public string Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        /// <summary>
        /// Gets or sets the national ID number.
        /// Optional field for client creation.
        /// </summary>
        public string NationalId
        {
            get => _nationalId;
            set => SetProperty(ref _nationalId, value);
        }

        /// <summary>
        /// Gets the phone numbers collection for personal information.
        /// This collection is used by the NPersonalView for managing multiple phone numbers.
        /// Data persists throughout the client creation process until Save or Cancel.
        /// </summary>
        public PhoneNumbersCollectionModel PhoneNumbers => _phoneNumbers;

        /// <summary>
        /// Gets the notes collection for client notes management.
        /// This collection is used by the NFileCheckView for managing client notes.
        /// Data persists throughout the client creation process until Save or Cancel.
        /// </summary>
        public NotesCollectionModel Notes => _notes;

        /// <summary>
        /// Gets the collection of CPI Wilayas (administrative provinces) for ComboBox binding.
        /// Data is loaded from the APP_Database.db CpiWilayas table via CpiLocationService.
        /// </summary>
        public List<CpiWilaya> CpiWilayas => _cpiWilayas;

        /// <summary>
        /// Gets the collection of CPI Dairas (administrative districts) for ComboBox binding.
        /// This collection is filtered based on the selected wilaya for cascading selection.
        /// Uses ObservableCollection for proper change notifications to the UI.
        /// </summary>
        public ObservableCollection<CpiDaira> CpiDairas => _cpiDairas;

        /// <summary>
        /// Gets or sets the selected CPI Wilaya for the current activity.
        /// When changed, triggers filtering of the CpiDairas collection.
        /// </summary>
        public CpiWilaya? SelectedCpiWilaya
        {
            get => _selectedCpiWilaya;
            set
            {
                if (SetProperty(ref _selectedCpiWilaya, value))
                {
                    // Update the CurrentActivity.CpiWilaya property with the NameAr value
                    CurrentActivity.CpiWilaya = value?.NameAr ?? string.Empty;

                    // Trigger cascading update of dairas
                    _ = UpdateCpiDairasForSelectedWilayaAsync();
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected CPI Daira for the current activity.
        /// Updates the CurrentActivity.CpiDaira property when changed.
        /// Validates that the selected daira belongs to the selected wilaya.
        /// </summary>
        public CpiDaira? SelectedCpiDaira
        {
            get => _selectedCpiDaira;
            set
            {
                if (SetProperty(ref _selectedCpiDaira, value))
                {
                    // Validate that the selected daira belongs to the selected wilaya
                    if (value != null && _selectedCpiWilaya != null && value.WilayaCode != _selectedCpiWilaya.Code)
                    {
                        LoggingService.LogWarning($"Selected daira {value.NameAr} does not belong to selected wilaya {_selectedCpiWilaya.NameAr}", "NewClientViewModel");
                        // Clear the invalid selection
                        _selectedCpiDaira = null;
                        CurrentActivity.CpiDaira = string.Empty;
                        OnPropertyChanged(nameof(SelectedCpiDaira));
                        return;
                    }

                    // Update the CurrentActivity.CpiDaira property with the NameAr value
                    CurrentActivity.CpiDaira = value?.NameAr ?? string.Empty;
                }
            }
        }

        /// <summary>
        /// Gets a value indicating whether the current session is editing an existing client
        /// rather than creating a new one.
        /// </summary>
        public bool IsEditingExistingClient
        {
            get => _isEditingExistingClient;
            private set => SetProperty(ref _isEditingExistingClient, value);
        }

        /// <summary>
        /// Gets the UID of the existing client being edited, if any.
        /// </summary>
        public string? ExistingClientUid
        {
            get => _existingClientUid;
            private set => SetProperty(ref _existingClientUid, value);
        }

        /// <summary>
        /// Gets the display text for the notes card in NFileCheckView.
        /// Shows the most recent note content when notes exist, or fallback message when no notes are present.
        /// </summary>
        public string NotesDisplayText
        {
            get
            {
                if (_notes == null || _notes.Count == 0)
                {
                    return "لايوجد ملاحظات بعد";
                }

                var latestNote = _notes.GetNotesSortedByDate().FirstOrDefault();
                return latestNote?.Content ?? "لايوجد ملاحظات بعد";
            }
        }

        /// <summary>
        /// Refreshes the notes display text by triggering property change notification.
        /// Called from NFileCheckView when notes are updated through dialogs.
        /// </summary>
        public void RefreshNotesDisplay()
        {
            OnPropertyChanged(nameof(NotesDisplayText));
        }

        /// <summary>
        /// Sets the editing state for an existing client loaded from duplicate detection.
        /// This method should be called when a user selects an existing client from the duplicate detection dialog.
        /// </summary>
        /// <param name="clientData">The original client data loaded from the database</param>
        public void SetEditingExistingClient(DuplicateClientData clientData)
        {
            if (clientData == null)
            {
                LoggingService.LogWarning("Cannot set editing state with null client data", "NewClientViewModel");
                return;
            }

            try
            {
                IsEditingExistingClient = true;
                ExistingClientUid = clientData.ClientUid;
                _originalClientData = clientData.Clone(); // Store original data for comparison

                LoggingService.LogInfo($"Set editing state for existing client: {clientData.ClientUid}", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting editing state for existing client: {ex.Message}", "NewClientViewModel");
                // Reset state on error
                ClearEditingState();
            }
        }

        /// <summary>
        /// Clears the editing state, returning to new client creation mode.
        /// </summary>
        public void ClearEditingState()
        {
            IsEditingExistingClient = false;
            ExistingClientUid = null;
            _originalClientData = null;
            LoggingService.LogDebug("Cleared editing state - returned to new client creation mode", "NewClientViewModel");
        }

        /// <summary>
        /// Checks if the current form data has changes compared to the original client data.
        /// Only compares personal information fields that can be updated.
        /// </summary>
        /// <returns>True if there are changes, false if no changes detected</returns>
        private bool HasPersonalInfoChanges()
        {
            if (!IsEditingExistingClient || _originalClientData == null)
            {
                return true; // If not editing existing client, treat as changes (new client)
            }

            try
            {
                // Compare each field that can be updated
                bool hasChanges = false;

                // Compare NameAr (trimmed and null-normalized)
                var currentNameAr = string.IsNullOrWhiteSpace(NameAr) ? null : NameAr.Trim();
                var originalNameAr = string.IsNullOrWhiteSpace(_originalClientData.NameAr) ? null : _originalClientData.NameAr.Trim();
                if (!string.Equals(currentNameAr, originalNameAr, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"NameAr changed: '{originalNameAr}' -> '{currentNameAr}'", "NewClientViewModel");
                }

                // Compare BirthDate (trimmed and null-normalized)
                var currentBirthDate = string.IsNullOrWhiteSpace(BirthDate) ? null : BirthDate.Trim();
                var originalBirthDate = string.IsNullOrWhiteSpace(_originalClientData.BirthDate) ? null : _originalClientData.BirthDate.Trim();
                if (!string.Equals(currentBirthDate, originalBirthDate, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"BirthDate changed: '{originalBirthDate}' -> '{currentBirthDate}'", "NewClientViewModel");
                }

                // Compare BirthPlace (trimmed and null-normalized)
                var currentBirthPlace = string.IsNullOrWhiteSpace(BirthPlace) ? null : BirthPlace.Trim();
                var originalBirthPlace = string.IsNullOrWhiteSpace(_originalClientData.BirthPlace) ? null : _originalClientData.BirthPlace.Trim();
                if (!string.Equals(currentBirthPlace, originalBirthPlace, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"BirthPlace changed: '{originalBirthPlace}' -> '{currentBirthPlace}'", "NewClientViewModel");
                }

                // Compare Gender
                if (Gender != _originalClientData.Gender)
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"Gender changed: {_originalClientData.Gender} -> {Gender}", "NewClientViewModel");
                }

                // Compare Address (trimmed and null-normalized)
                var currentAddress = string.IsNullOrWhiteSpace(Address) ? null : Address.Trim();
                var originalAddress = string.IsNullOrWhiteSpace(_originalClientData.Address) ? null : _originalClientData.Address.Trim();
                if (!string.Equals(currentAddress, originalAddress, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"Address changed: '{originalAddress}' -> '{currentAddress}'", "NewClientViewModel");
                }

                // Compare NationalId (trimmed and null-normalized)
                var currentNationalId = string.IsNullOrWhiteSpace(NationalId) ? null : NationalId.Trim();
                var originalNationalId = string.IsNullOrWhiteSpace(_originalClientData.NationalId) ? null : _originalClientData.NationalId.Trim();
                if (!string.Equals(currentNationalId, originalNationalId, StringComparison.Ordinal))
                {
                    hasChanges = true;
                    LoggingService.LogDebug($"NationalId changed: '{originalNationalId}' -> '{currentNationalId}'", "NewClientViewModel");
                }

                LoggingService.LogDebug($"Personal info changes detected: {hasChanges}", "NewClientViewModel");
                return hasChanges;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking for personal info changes: {ex.Message}", "NewClientViewModel");
                return true; // Assume changes on error to be safe
            }
        }

        /// <summary>
        /// Gets or sets the selected G12 payment years for the current activity tab.
        /// Returns the appropriate tab-specific collection based on SelectedActivityType.
        /// Data persists independently when switching between tabs.
        /// </summary>
        public List<int> G12SelectedYears
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialG12SelectedYears,
                    "SecondaryCommercial" => _secondaryCommercialG12SelectedYears,
                    "Craft" => _craftG12SelectedYears,
                    "Professional" => _professionalG12SelectedYears,
                    _ => _mainCommercialG12SelectedYears // Default fallback
                };
            }
            set
            {
                var currentList = SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialG12SelectedYears,
                    "SecondaryCommercial" => _secondaryCommercialG12SelectedYears,
                    "Craft" => _craftG12SelectedYears,
                    "Professional" => _professionalG12SelectedYears,
                    _ => _mainCommercialG12SelectedYears
                };

                // Update the appropriate tab-specific collection
                currentList.Clear();
                if (value != null)
                {
                    currentList.AddRange(value);
                }

                // Update the corresponding tab-specific collection reference
                switch (SelectedActivityType)
                {
                    case "MainCommercial":
                        _mainCommercialG12SelectedYears = currentList;
                        break;
                    case "SecondaryCommercial":
                        _secondaryCommercialG12SelectedYears = currentList;
                        break;
                    case "Craft":
                        _craftG12SelectedYears = currentList;
                        break;
                    case "Professional":
                        _professionalG12SelectedYears = currentList;
                        break;
                }

                UpdateG12DisplayText();
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets the selected BIS payment years for the current activity tab.
        /// Returns the appropriate tab-specific collection based on SelectedActivityType.
        /// Data persists independently when switching between tabs.
        /// </summary>
        public List<int> BISSelectedYears
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialBISSelectedYears,
                    "SecondaryCommercial" => _secondaryCommercialBISSelectedYears,
                    "Craft" => _craftBISSelectedYears,
                    "Professional" => _professionalBISSelectedYears,
                    _ => _mainCommercialBISSelectedYears // Default fallback
                };
            }
            set
            {
                var currentList = SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialBISSelectedYears,
                    "SecondaryCommercial" => _secondaryCommercialBISSelectedYears,
                    "Craft" => _craftBISSelectedYears,
                    "Professional" => _professionalBISSelectedYears,
                    _ => _mainCommercialBISSelectedYears
                };

                // Update the appropriate tab-specific collection
                currentList.Clear();
                if (value != null)
                {
                    currentList.AddRange(value);
                }

                // Update the corresponding tab-specific collection reference
                switch (SelectedActivityType)
                {
                    case "MainCommercial":
                        _mainCommercialBISSelectedYears = currentList;
                        break;
                    case "SecondaryCommercial":
                        _secondaryCommercialBISSelectedYears = currentList;
                        break;
                    case "Craft":
                        _craftBISSelectedYears = currentList;
                        break;
                    case "Professional":
                        _professionalBISSelectedYears = currentList;
                        break;
                }

                UpdateBISDisplayText();
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets the display text for G12 payment years for the current activity tab.
        /// Returns the appropriate tab-specific display text based on SelectedActivityType.
        /// </summary>
        public string G12DisplayText
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialG12DisplayText,
                    "SecondaryCommercial" => _secondaryCommercialG12DisplayText,
                    "Craft" => _craftG12DisplayText,
                    "Professional" => _professionalG12DisplayText,
                    _ => _mainCommercialG12DisplayText // Default fallback
                };
            }
            set
            {
                switch (SelectedActivityType)
                {
                    case "MainCommercial":
                        SetProperty(ref _mainCommercialG12DisplayText, value);
                        break;
                    case "SecondaryCommercial":
                        SetProperty(ref _secondaryCommercialG12DisplayText, value);
                        break;
                    case "Craft":
                        SetProperty(ref _craftG12DisplayText, value);
                        break;
                    case "Professional":
                        SetProperty(ref _professionalG12DisplayText, value);
                        break;
                }
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets the display text for BIS payment years for the current activity tab.
        /// Returns the appropriate tab-specific display text based on SelectedActivityType.
        /// </summary>
        public string BISDisplayText
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialBISDisplayText,
                    "SecondaryCommercial" => _secondaryCommercialBISDisplayText,
                    "Craft" => _craftBISDisplayText,
                    "Professional" => _professionalBISDisplayText,
                    _ => _mainCommercialBISDisplayText // Default fallback
                };
            }
            set
            {
                switch (SelectedActivityType)
                {
                    case "MainCommercial":
                        SetProperty(ref _mainCommercialBISDisplayText, value);
                        break;
                    case "SecondaryCommercial":
                        SetProperty(ref _secondaryCommercialBISDisplayText, value);
                        break;
                    case "Craft":
                        SetProperty(ref _craftBISDisplayText, value);
                        break;
                    case "Professional":
                        SetProperty(ref _professionalBISDisplayText, value);
                        break;
                }
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets the current profile image stored in memory.
        /// This image is set when the user edits an image in ImageManagementDialog within NewClientView.
        /// </summary>
        public System.Windows.Media.Imaging.BitmapSource? ProfileImage
        {
            get => _profileImage;
            private set => SetProperty(ref _profileImage, value);
        }

        /// <summary>
        /// Gets whether a profile image has been set for the current client.
        /// </summary>
        public bool HasProfileImage => _profileImage != null;

        #endregion

        #region Commands

        /// <summary>
        /// Command for saving the new client
        /// </summary>
        public ICommand SaveCommand { get; }

        /// <summary>
        /// Command for switching between activity tabs.
        /// Parameter should be the activity type string.
        /// </summary>
        public ICommand SwitchActivityTabCommand { get; }

        /// <summary>
        /// Command for closing/canceling the dialog.
        /// Clears all activity data before closing.
        /// </summary>
        public ICommand CloseCommand { get; }

        /// <summary>
        /// Command to open the multiple activities management dialog.
        /// Only available for MainCommercial and SecondaryCommercial activity types.
        /// </summary>
        public ICommand ManageMultipleActivitiesCommand { get; }

        /// <summary>
        /// Command to open the activity status update dialog for editing existing status information.
        /// Available when the current activity has a non-active status.
        /// </summary>
        public ICommand EditStatusCommand { get; }

        /// <summary>
        /// Command to search for craft types in the CraftTypeBase database.
        /// Opens a search dialog for craft type selection.
        /// </summary>
        public ICommand SearchCraftTypesCommand { get; }

        /// <summary>
        /// Command to show additional information about the selected craft type.
        /// Displays content and secondary information from the CraftTypeBase database.
        /// </summary>
        public ICommand ShowCraftInformationCommand { get; }

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the NewClientViewModel
        /// </summary>
        public NewClientViewModel()
        {
            // Initialize commands
            SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave, "SaveClient");
            SwitchActivityTabCommand = new RelayCommand(ExecuteSwitchActivityTab, CanExecuteSwitchActivityTab, "SwitchActivityTab");
            CloseCommand = new RelayCommand(ExecuteClose, CanExecuteClose, "CloseDialog");
            ManageMultipleActivitiesCommand = new RelayCommand(ExecuteManageMultipleActivities, CanExecuteManageMultipleActivities, "ManageMultipleActivities");
            EditStatusCommand = new RelayCommand(ExecuteEditStatus, CanExecuteEditStatus, "EditActivityStatus");
            SearchCraftTypesCommand = new RelayCommand(ExecuteSearchCraftTypes, CanExecuteSearchCraftTypes, "SearchCraftTypes");
            ShowCraftInformationCommand = new RelayCommand(ExecuteShowCraftInformation, CanExecuteShowCraftInformation, "ShowCraftInformation");

            // Initialize default values
            IsLoading = false;
            SelectedActivityType = "MainCommercial";

            // Initialize CanSave based on validation
            UpdateCanSave();

            // Initialize ActivityTypeBase service from ServiceLocator
            if (ServiceLocator.TryGetService<ActivityTypeBaseService>(out var activityTypeService))
            {
                _activityTypeService = activityTypeService;
            }
            else
            {
                LoggingService.LogWarning("ActivityTypeBaseService not available in ServiceLocator", "NewClientViewModel");
            }

            // Initialize CraftTypeBase service from ServiceLocator
            if (ServiceLocator.TryGetService<CraftTypeBaseService>(out var craftTypeService))
            {
                _craftTypeService = craftTypeService;
            }
            else
            {
                LoggingService.LogWarning("CraftTypeBaseService not available in ServiceLocator", "NewClientViewModel");
            }

            // Initialize CpiLocationService from ServiceLocator
            if (ServiceLocator.TryGetService<CpiLocationService>(out var cpiLocationService))
            {
                _cpiLocationService = cpiLocationService;
                LoggingService.LogDebug("CpiLocationService initialized from ServiceLocator", "NewClientViewModel");
            }
            else
            {
                LoggingService.LogWarning("CpiLocationService not available in ServiceLocator", "NewClientViewModel");
            }

            // Initialize new database services from ServiceLocator
            // These services are now available for client management operations
            if (ServiceLocator.TryGetService<ClientDatabaseService>(out var clientDbService))
            {
                _clientDatabaseService = clientDbService;
                LoggingService.LogDebug("ClientDatabaseService initialized from ServiceLocator", "NewClientViewModel");
            }
            else
            {
                LoggingService.LogWarning("ClientDatabaseService not available in ServiceLocator", "NewClientViewModel");
            }

            if (ServiceLocator.TryGetService<UIDGenerationService>(out var uidService))
            {
                _uidGenerationService = uidService;
                LoggingService.LogDebug("UIDGenerationService initialized from ServiceLocator", "NewClientViewModel");
            }
            else
            {
                LoggingService.LogWarning("UIDGenerationService not available in ServiceLocator", "NewClientViewModel");
            }

            if (ServiceLocator.TryGetService<ClientValidationService>(out var validationService))
            {
                _clientValidationService = validationService;
                LoggingService.LogDebug("ClientValidationService initialized from ServiceLocator", "NewClientViewModel");
            }
            else
            {
                LoggingService.LogWarning("ClientValidationService not available in ServiceLocator", "NewClientViewModel");
            }

            if (ServiceLocator.TryGetService<ClientFolderManagementService>(out var folderService))
            {
                _clientFolderManagementService = folderService;
                LoggingService.LogDebug("ClientFolderManagementService initialized from ServiceLocator", "NewClientViewModel");
            }
            else
            {
                LoggingService.LogWarning("ClientFolderManagementService not available in ServiceLocator", "NewClientViewModel");
            }

            // Initialize tab-specific models (already initialized in field declarations)
            // Activity models: _mainCommercialActivity, _secondaryCommercialActivity, _craftActivity, _professionalActivity
            // FileCheck models: _mainCommercialFileCheckStates, _secondaryCommercialFileCheckStates, _craftFileCheckStates, _professionalFileCheckStates

            // Set default ActivityStatus values for each activity type to provide better UX
            InitializeDefaultActivityStatus();

            // Set up ActivityCode change handlers for MainCommercial and SecondaryCommercial tabs
            SetupActivityCodeChangeHandlers();

            // Initialize CPI location data
            _ = InitializeCpiLocationDataAsync();

            OnInitialize();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes default ActivityStatus values for each activity type.
        /// Sets the first item from each activity type's status list as the default selection.
        /// This provides better user experience by having the ComboBox pre-selected.
        /// </summary>
        private void InitializeDefaultActivityStatus()
        {
            try
            {
                // Set default status for each activity type using the converter's GetDefaultStatus method
                _mainCommercialActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus("MainCommercial");
                _secondaryCommercialActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus("SecondaryCommercial");
                _craftActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus("Craft");
                _professionalActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus("Professional");

                LoggingService.LogDebug("Default activity status values initialized for all activity types", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing default activity status: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Updates the CanSave property based on validation rules.
        /// Currently validates that NameFr is not empty.
        /// </summary>
        private void UpdateCanSave()
        {
            try
            {
                // Check if NameFr is provided (required field)
                bool isNameFrValid = !string.IsNullOrWhiteSpace(NameFr);

                // Update CanSave based on validation
                CanSave = isNameFrValid && !IsLoading;

                LoggingService.LogDebug($"CanSave updated: {CanSave} (NameFr valid: {isNameFrValid}, IsLoading: {IsLoading})", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating CanSave: {ex.Message}", "NewClientViewModel");
                CanSave = false;
            }
        }

        /// <summary>
        /// Triggers validation for the ViewModel.
        /// This method is called by UI components for grouped validation optimization.
        /// OPTIMIZATION: Added for Phase 2B UI optimizations to support debounced validation.
        /// </summary>
        public void TriggerValidation()
        {
            try
            {
                // Trigger the main validation logic
                UpdateCanSave();

                LoggingService.LogDebug("Validation triggered from UI component", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error triggering validation: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Ensures that the current activity has a default status value if it's empty.
        /// Called when switching between activity tabs to maintain consistent UX.
        /// </summary>
        /// <param name="activityType">The activity type to check and set default status for</param>
        private void EnsureDefaultActivityStatus(string activityType)
        {
            try
            {
                var currentActivity = CurrentActivity;
                if (string.IsNullOrWhiteSpace(currentActivity.ActivityStatus))
                {
                    currentActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus(activityType);
                    LoggingService.LogDebug($"Set default activity status for {activityType}: {currentActivity.ActivityStatus}", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error ensuring default activity status for {activityType}: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Updates the G12 display text based on selected years for the current activity tab.
        /// Shows "مدفوع" when valid year range exists and all years are unchecked,
        /// shows last 5 selected years as chip-styled text, or default message.
        /// </summary>
        private void UpdateG12DisplayText()
        {
            var currentG12Years = G12SelectedYears;
            if (currentG12Years == null || currentG12Years.Count == 0)
            {
                // Check if we have a valid activity start date (meaning a valid range exists)
                var activityStartDate = CurrentActivity?.ActivityStartDate;
                if (!string.IsNullOrEmpty(activityStartDate) &&
                    PaymentYearRangeCalculator.IsValidActivityStartDate(activityStartDate))
                {
                    // Valid range exists but all years are unchecked - show "مدفوع"
                    G12DisplayText = "مدفوع";
                }
                else
                {
                    // No valid range exists - show default message
                    G12DisplayText = "لم يتم التحديد بعد";
                }
            }
            else
            {
                var sortedYears = currentG12Years.OrderByDescending(y => y).Take(5).ToList();
                G12DisplayText = string.Join(", ", sortedYears);
            }
        }

        /// <summary>
        /// Updates the BIS display text based on selected years for the current activity tab.
        /// Shows "مدفوع" when valid year range exists and all years are unchecked,
        /// shows last 5 selected years as chip-styled text, or default message.
        /// </summary>
        private void UpdateBISDisplayText()
        {
            var currentBISYears = BISSelectedYears;
            if (currentBISYears == null || currentBISYears.Count == 0)
            {
                // Check if we have a valid activity start date (meaning a valid range exists)
                var activityStartDate = CurrentActivity?.ActivityStartDate;
                if (!string.IsNullOrEmpty(activityStartDate) &&
                    PaymentYearRangeCalculator.IsValidActivityStartDate(activityStartDate))
                {
                    // Valid range exists but all years are unchecked - show "مدفوع"
                    BISDisplayText = "مدفوع";
                }
                else
                {
                    // No valid range exists - show default message
                    BISDisplayText = "لم يتم التحديد بعد";
                }
            }
            else
            {
                var sortedYears = currentBISYears.OrderByDescending(y => y).Take(5).ToList();
                BISDisplayText = string.Join(", ", sortedYears);
            }
        }

        /// <summary>
        /// Clears all activity data, file check states, and phone numbers from all tabs.
        /// Called when Save or Back/Cancel actions are performed.
        /// </summary>
        private void ClearAllActivityData()
        {
            try
            {
                // Clear all activity data
                _mainCommercialActivity.Reset();
                _secondaryCommercialActivity.Reset();
                _craftActivity.Reset();
                _professionalActivity.Reset();

                // Clear all file check states
                _mainCommercialFileCheckStates.Reset();
                _secondaryCommercialFileCheckStates.Reset();
                _craftFileCheckStates.Reset();
                _professionalFileCheckStates.Reset();

                // Clear phone numbers collection
                _phoneNumbers.Clear();

                // Clear multiple activities collections
                _mainCommercialActivities.Clear();
                _secondaryCommercialActivities.Clear();

                // Clear all tab-specific payment years data
                _mainCommercialG12SelectedYears.Clear();
                _mainCommercialBISSelectedYears.Clear();
                _secondaryCommercialG12SelectedYears.Clear();
                _secondaryCommercialBISSelectedYears.Clear();
                _craftG12SelectedYears.Clear();
                _craftBISSelectedYears.Clear();
                _professionalG12SelectedYears.Clear();
                _professionalBISSelectedYears.Clear();

                // Reset all tab-specific display texts
                _mainCommercialG12DisplayText = "لم يتم التحديد بعد";
                _mainCommercialBISDisplayText = "لم يتم التحديد بعد";
                _secondaryCommercialG12DisplayText = "لم يتم التحديد بعد";
                _secondaryCommercialBISDisplayText = "لم يتم التحديد بعد";
                _craftG12DisplayText = "لم يتم التحديد بعد";
                _craftBISDisplayText = "لم يتم التحديد بعد";
                _professionalG12DisplayText = "لم يتم التحديد بعد";
                _professionalBISDisplayText = "لم يتم التحديد بعد";

                // Notify UI of payment years changes
                OnPropertyChanged(nameof(G12SelectedYears));
                OnPropertyChanged(nameof(BISSelectedYears));
                OnPropertyChanged(nameof(G12DisplayText));
                OnPropertyChanged(nameof(BISDisplayText));

                // Clear profile image
                ClearProfileImage();

                // Notify UI that properties have changed
                OnPropertyChanged(nameof(CurrentActivity));
                OnPropertyChanged(nameof(CurrentFileCheckStates));
                OnPropertyChanged(nameof(PhoneNumbers));

                LoggingService.LogDebug("All activity data, file check states, phone numbers, and profile image cleared", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing activity data, file check states, and phone numbers: {ex.Message}", "NewClientViewModel");
            }
        }

        #endregion

        #region Command Handlers

        /// <summary>
        /// Executes the manage multiple activities command.
        /// Opens the MultipleActivitiesDialog for MainCommercial and SecondaryCommercial tabs.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        private async void ExecuteManageMultipleActivities(object? parameter)
        {
            try
            {
                // Only allow for MainCommercial and SecondaryCommercial
                if (SelectedActivityType != "MainCommercial" && SelectedActivityType != "SecondaryCommercial")
                {
                    LoggingService.LogWarning($"Multiple activities not supported for {SelectedActivityType}", "NewClientViewModel");
                    return;
                }

                LoggingService.LogInfo($"Opening multiple activities dialog for {SelectedActivityType}", "NewClientViewModel");

                // Create dialog with existing activities
                var dialog = new Views.Dialogs.MultipleActivitiesDialog(CurrentMultipleActivities);

                // Show dialog
                var result = await DialogHost.Show(dialog, "NewClientDialogHost");

                if (result is bool dialogResult && dialogResult)
                {
                    // Update the activities collection
                    var updatedActivities = dialog.GetActivities();
                    UpdateMultipleActivities(updatedActivities);

                    LoggingService.LogInfo($"Multiple activities updated for {SelectedActivityType}: {updatedActivities.Count} activities", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error managing multiple activities: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Determines whether the manage multiple activities command can be executed.
        /// Only available for MainCommercial and SecondaryCommercial activity types.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if the command can be executed</returns>
        private bool CanExecuteManageMultipleActivities(object? parameter)
        {
            return SelectedActivityType == "MainCommercial" || SelectedActivityType == "SecondaryCommercial";
        }

        /// <summary>
        /// Executes the edit status command to open the activity status update dialog.
        /// Opens the dialog with existing update date and note data for editing.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        private void ExecuteEditStatus(object? parameter)
        {
            try
            {
                LoggingService.LogDebug($"Edit status command executed for {SelectedActivityType} with status: {CurrentActivity.ActivityStatus}", "NewClientViewModel");

                // Raise event to notify the view to open the dialog
                // The view will handle the actual dialog opening to maintain proper DialogHost context
                EditStatusRequested?.Invoke();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing edit status command: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح نافذة تعديل معلومات الحالة. يرجى المحاولة مرة أخرى.",
                    "خطأ في فتح النافذة",
                    LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Determines whether the edit status command can be executed.
        /// Available when the current activity has a non-active status.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        /// <returns>True if the command can be executed</returns>
        private bool CanExecuteEditStatus(object? parameter)
        {
            var currentStatus = CurrentActivity?.ActivityStatus?.Trim() ?? string.Empty;
            return IsNonActiveStatus(currentStatus);
        }

        /// <summary>
        /// Determines whether the given status is a non-active status that requires update information.
        /// </summary>
        /// <param name="status">The activity status to check</param>
        /// <returns>True if the status is non-active</returns>
        private static bool IsNonActiveStatus(string status)
        {
            return status switch
            {
                "معدل" => true,        // Modified/Edited
                "غير نشط" => true,     // Inactive
                "شطب" => true,         // Suspended/Cancelled
                _ => false             // Active statuses or unknown
            };
        }

        /// <summary>
        /// Executes the save command
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        private async void ExecuteSave(object? parameter)
        {
            string? operationId = null;
            try
            {
                LoggingService.LogDebug("Starting new client save operation", "NewClientViewModel");

                // Start operation tracking for error deduplication
                operationId = ErrorDeduplicationManager.StartOperation("ClientCreation", "NewClientViewModel");
                LoggingService.LogDebug($"Started client creation operation with ID: {operationId}", "NewClientViewModel");

                IsLoading = true;
                CanSave = false;

                // Request data collection from views before saving
                RequestDataCollection?.Invoke();
                LoggingService.LogDebug("Data collection requested from views", "NewClientViewModel");

                // Implement actual save logic with proper error handling
                string clientUID = await SaveClientDataAsync(operationId);

                // Check if save was successful (non-empty UID returned)
                if (string.IsNullOrEmpty(clientUID))
                {
                    LoggingService.LogWarning("Client save operation returned empty UID - operation may have failed", "NewClientViewModel");
                    return; // Exit without showing success notification or closing dialog
                }

                LoggingService.LogDebug("Client data saved successfully", "NewClientViewModel");

                // Create client folder structure after successful save - ONLY for new clients
                // For existing clients, folder creation is handled within HandleExistingClientUpdateAsync
                if (!IsEditingExistingClient)
                {
                    LoggingService.LogInfo("Creating folder structure for new client", "NewClientViewModel");
                    await CreateClientFolderStructureAsync(clientUID, operationId);
                }
                else
                {
                    LoggingService.LogInfo("Skipping folder creation for existing client - already handled in update workflow", "NewClientViewModel");
                }

                // Enhanced UX: Wait 500ms before showing success notification for polished feel
                await Task.Delay(500);

                // Show success notification with client UID
                ErrorManager.ShowUserSuccessToast($"تم حفظ بيانات العميل بنجاح\nمعرف العميل: {clientUID}", "تم الحفظ");

                // Clear all activity data after successful save
                ClearAllActivityData();

                // Add logging before attempting to close dialog
                LoggingService.LogDebug("Attempting to close NewClientView dialog after successful save", "NewClientViewModel");

                // Close the dialog with success result using MaterialDesign DialogHost static method
                try
                {
                    // Use the static Close method with the RootDialog identifier
                    DialogHost.Close("RootDialog", true);
                    LoggingService.LogDebug("Successfully closed RootDialog with success result", "NewClientViewModel");
                }
                catch (Exception closeEx)
                {
                    LoggingService.LogError($"Error closing RootDialog: {closeEx.Message}", "NewClientViewModel");
                    // Try alternative close method using command
                    try
                    {
                        DialogHost.CloseDialogCommand.Execute(true, null);
                        LoggingService.LogDebug("Successfully executed fallback dialog close command", "NewClientViewModel");
                    }
                    catch (Exception fallbackEx)
                    {
                        LoggingService.LogError($"Error with fallback dialog close: {fallbackEx.Message}", "NewClientViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error saving new client: {ex.Message}", "NewClientViewModel");

                // Show Arabic error message to user with proper ErrorManager integration and deduplication
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء حفظ بيانات العميل الجديد. يرجى التحقق من البيانات والمحاولة مرة أخرى.",
                    "خطأ في الحفظ",
                    LogLevel.Error,
                    "NewClientViewModel",
                    operationId);
            }
            finally
            {
                // Complete operation tracking
                if (operationId != null)
                {
                    ErrorDeduplicationManager.CompleteOperation(operationId);
                    LoggingService.LogDebug($"Completed client creation operation: {operationId}", "NewClientViewModel");
                }

                IsLoading = false;
                // CanSave will be updated automatically by UpdateCanSave() when IsLoading changes
            }
        }

        /// <summary>
        /// Determines whether the save command can be executed
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if the command can be executed; otherwise, false</returns>
        private bool CanExecuteSave(object? parameter)
        {
            return CanSave && !IsLoading;
        }

        /// <summary>
        /// Executes the switch activity tab command
        /// OPTIMIZED: Reduced logging frequency for better performance with NActivityTabView debouncing.
        /// </summary>
        /// <param name="parameter">The activity type to switch to</param>
        private void ExecuteSwitchActivityTab(object? parameter)
        {
            try
            {
                if (parameter is string activityType && !string.IsNullOrWhiteSpace(activityType))
                {
                    // Skip update if already selected (performance optimization)
                    if (SelectedActivityType == activityType)
                    {
                        return;
                    }

                    // Reduced logging frequency - only log when actually changing
                    LoggingService.LogDebug($"Switching to activity tab: {activityType}", "NewClientViewModel");
                    SelectedActivityType = activityType;
                }
                else
                {
                    LoggingService.LogWarning("Invalid activity type parameter for SwitchActivityTabCommand", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error switching activity tab: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Determines whether the switch activity tab command can be executed
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if the command can be executed; otherwise, false</returns>
        private bool CanExecuteSwitchActivityTab(object? parameter)
        {
            // Allow tab switching unless we're in a loading state
            return !IsLoading;
        }

        /// <summary>
        /// Executes the close command
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        private void ExecuteClose(object? parameter)
        {
            try
            {
                LoggingService.LogInfo("User requested dialog close", "NewClientViewModel");

                // Clear all activity data before closing
                ClearAllActivityData();

                // Add logging before attempting to close dialog
                LoggingService.LogDebug("Attempting to close NewClientView dialog (user requested)", "NewClientViewModel");

                // Close the dialog with cancel result using MaterialDesign DialogHost static method
                try
                {
                    // Use the static Close method with the RootDialog identifier
                    DialogHost.Close("RootDialog", false);
                    LoggingService.LogDebug("Successfully closed RootDialog with cancel result", "NewClientViewModel");
                }
                catch (Exception closeEx)
                {
                    LoggingService.LogError($"Error closing RootDialog: {closeEx.Message}", "NewClientViewModel");
                    // Try alternative close method using command
                    try
                    {
                        DialogHost.CloseDialogCommand.Execute(false, null);
                        LoggingService.LogDebug("Successfully executed fallback dialog close command (cancel)", "NewClientViewModel");
                    }
                    catch (Exception fallbackEx)
                    {
                        LoggingService.LogError($"Error with fallback dialog close: {fallbackEx.Message}", "NewClientViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing dialog: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Determines whether the close command can be executed
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if the command can be executed; otherwise, false</returns>
        private bool CanExecuteClose(object? parameter)
        {
            // Allow closing unless we're in a loading state
            return !IsLoading;
        }

        /// <summary>
        /// Executes the search craft types command.
        /// Opens a search dialog for craft type selection from the CraftTypeBase database.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        private async void ExecuteSearchCraftTypes(object? parameter)
        {
            try
            {
                LoggingService.LogInfo("Opening craft types search dialog", "NewClientViewModel");

                // Create and show craft search dialog
                var dialog = new Views.Dialogs.CraftSearchDialog();
                var result = await DialogHost.Show(dialog, "NewClientDialogHost");

                if (result is CraftTypeBaseModel selectedCraft)
                {
                    // Update the current activity with selected craft
                    CurrentActivity.ActivityCode = selectedCraft.Code;
                    CurrentActivity.ActivityDescription = selectedCraft.Description;

                    LoggingService.LogInfo($"Craft selected: {selectedCraft.Code} - {selectedCraft.Description}", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error searching craft types: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء البحث عن أنواع الحرف. يرجى المحاولة مرة أخرى.",
                    "خطأ في البحث",
                    UFU2.Common.LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Determines whether the search craft types command can be executed.
        /// Only available for Craft activity type.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if the command can be executed</returns>
        private bool CanExecuteSearchCraftTypes(object? parameter)
        {
            return SelectedActivityType == "Craft" && !IsLoading;
        }

        /// <summary>
        /// Executes the show craft information command.
        /// Displays additional information about the current craft type.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        private async void ExecuteShowCraftInformation(object? parameter)
        {
            try
            {
                var craftCode = CurrentActivity?.ActivityCode?.Trim();
                if (string.IsNullOrEmpty(craftCode) || !IsValidCraftCodeFormat(craftCode))
                {
                    ErrorManager.ShowUserWarningToast(
                        "يرجى إدخال رمز حرفة صحيح أولاً",
                        "رمز الحرفة مطلوب",
                        "NewClientViewModel");
                    return;
                }

                if (_craftTypeService == null)
                {
                    ErrorManager.ShowUserErrorToast(
                        "خدمة أنواع الحرف غير متوفرة",
                        "خطأ في النظام",
                        "NewClientViewModel");
                    return;
                }

                LoggingService.LogInfo($"Showing craft information for code: {craftCode}", "NewClientViewModel");

                // Get craft information from database
                var craftType = await _craftTypeService.GetByCodeAsync(craftCode);
                if (craftType == null)
                {
                    ErrorManager.ShowUserWarningToast(
                        $"لم يتم العثور على معلومات للحرفة: {craftCode}",
                        "الحرفة غير موجودة",
                        "NewClientViewModel");
                    return;
                }

                // Create and show craft information dialog
                var dialog = new Views.Dialogs.CraftInformationDialog(craftType);
                await DialogHost.Show(dialog, "NewClientDialogHost");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing craft information: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء عرض معلومات الحرفة. يرجى المحاولة مرة أخرى.",
                    "خطأ في عرض المعلومات",
                    UFU2.Common.LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Determines whether the show craft information command can be executed.
        /// Only available for Craft activity type when a valid craft code is entered.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if the command can be executed</returns>
        private bool CanExecuteShowCraftInformation(object? parameter)
        {
            if (SelectedActivityType != "Craft" || IsLoading)
                return false;

            var craftCode = CurrentActivity?.ActivityCode?.Trim();
            return !string.IsNullOrEmpty(craftCode) && IsValidCraftCodeFormat(craftCode);
        }

        #endregion

        #region Overrides

        /// <summary>
        /// Auto-populates G12 and BIS payment years based on activity start date.
        /// Called when ActivityStartDateTextBox loses focus with a valid date.
        /// Maintains tab independence and follows UFU2 data persistence patterns.
        /// </summary>
        /// <param name="activityStartDate">The activity start date in DD/MM/YYYY format</param>
        public void AutoPopulatePaymentYears(string activityStartDate)
        {
            try
            {
                // Use shared utility to auto-populate payment years
                var (g12Years, bisYears) = PaymentYearRangeCalculator.AutoPopulatePaymentYears(activityStartDate);

                // Update the current activity tab's payment years
                G12SelectedYears = g12Years;
                BISSelectedYears = bisYears;

                LoggingService.LogDebug($"Auto-populated payment years for {SelectedActivityType}: G12={g12Years.Count}, BIS={bisYears.Count}", "NewClientViewModel");
            }
            catch (System.Exception ex)
            {
                LoggingService.LogError($"Error auto-populating payment years: {ex.Message}", "NewClientViewModel");
                // Don't throw - just log the error and continue
            }
        }

        /// <summary>
        /// Clears payment years for the current activity tab.
        /// Called when ActivityStartDateTextBox becomes invalid or empty.
        /// </summary>
        public void ClearPaymentYears()
        {
            try
            {
                // Clear payment years for current activity tab
                G12SelectedYears = new List<int>();
                BISSelectedYears = new List<int>();

                LoggingService.LogDebug($"Cleared payment years for {SelectedActivityType}", "NewClientViewModel");
            }
            catch (System.Exception ex)
            {
                LoggingService.LogError($"Error clearing payment years: {ex.Message}", "NewClientViewModel");
                // Don't throw - just log the error and continue
            }
        }

        /// <summary>
        /// Called when the ViewModel is being initialized
        /// </summary>
        protected override void OnInitialize()
        {
            base.OnInitialize();
            LoggingService.LogDebug("NewClientViewModel initialized", "NewClientViewModel");
        }

        /// <summary>
        /// Called when the ViewModel is being disposed
        /// </summary>
        protected override void OnDispose()
        {
            LoggingService.LogInfo("NewClientViewModel disposed", "NewClientViewModel");
            base.OnDispose();
        }

        /// <summary>
        /// Sets up PropertyChanged event handlers for ActivityCode changes in MainCommercial, SecondaryCommercial, and Craft tabs.
        /// Enables automatic ActivityDescription population from ActivityTypeBase and CraftTypeBase databases when valid codes are entered.
        /// </summary>
        private void SetupActivityCodeChangeHandlers()
        {
            // Set up PropertyChanged handlers for MainCommercial, SecondaryCommercial, and Craft activities
            _mainCommercialActivity.PropertyChanged += OnActivityCodeChanged;
            _secondaryCommercialActivity.PropertyChanged += OnActivityCodeChanged;
            _craftActivity.PropertyChanged += OnActivityCodeChanged;
        }

        /// <summary>
        /// Handles PropertyChanged events for activity models to detect ActivityCode changes.
        /// Automatically populates ActivityDescription when a valid code is entered.
        /// Supports commercial codes (6 digits) and craft codes (XX-XX-XXX format).
        /// </summary>
        /// <param name="sender">The activity model that raised the event</param>
        /// <param name="e">Property change event arguments</param>
        private async void OnActivityCodeChanged(object? sender, PropertyChangedEventArgs e)
        {
            // Only handle ActivityCode property changes
            if (e.PropertyName != nameof(ActivityModel.ActivityCode) || sender is not ActivityModel activityModel)
                return;

            // Only process for MainCommercial, SecondaryCommercial, and Craft tabs
            var currentActivityType = SelectedActivityType;
            if (currentActivityType != "MainCommercial" && currentActivityType != "SecondaryCommercial" && currentActivityType != "Craft")
                return;

            // Only process if this is the current activity model
            if (activityModel != CurrentActivity)
                return;

            var activityCode = activityModel.ActivityCode?.Trim();

            // Handle different activity types
            if (currentActivityType == "Craft")
            {
                await HandleCraftCodeChanged(activityCode, activityModel);
            }
            else
            {
                await HandleCommercialCodeChanged(activityCode, activityModel);
            }
        }

        /// <summary>
        /// Handles commercial activity code changes (6-digit codes).
        /// </summary>
        /// <param name="activityCode">The activity code</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task HandleCommercialCodeChanged(string activityCode, ActivityModel activityModel)
        {
            // Check if we have exactly 6 digits
            if (string.IsNullOrEmpty(activityCode) || activityCode.Length != 6 || !activityCode.All(char.IsDigit))
            {
                // Clear description if code is invalid or incomplete
                if (!string.IsNullOrEmpty(activityModel.ActivityDescription))
                {
                    activityModel.ActivityDescription = string.Empty;
                }
                return;
            }

            // Perform database lookup
            await LookupActivityDescriptionAsync(activityCode, activityModel);
        }

        /// <summary>
        /// Handles craft activity code changes (XX-XX-XXX format).
        /// </summary>
        /// <param name="activityCode">The craft code</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task HandleCraftCodeChanged(string activityCode, ActivityModel activityModel)
        {
            // Check if we have valid craft code format (XX-XX-XXX)
            if (!IsValidCraftCodeFormat(activityCode))
            {
                // Clear description if code is invalid or incomplete
                if (!string.IsNullOrEmpty(activityModel.ActivityDescription))
                {
                    activityModel.ActivityDescription = string.Empty;
                }
                return;
            }

            // Perform craft database lookup
            await LookupCraftDescriptionAsync(activityCode, activityModel);
        }

        /// <summary>
        /// Validates craft code format (XX-XX-XXX).
        /// </summary>
        /// <param name="code">The code to validate</param>
        /// <returns>True if valid craft code format</returns>
        private static bool IsValidCraftCodeFormat(string code)
        {
            return CraftCodeFormatter.IsValidCraftCodeFormat(code);
        }

        /// <summary>
        /// Performs asynchronous lookup of activity description from ActivityTypeBase database.
        /// Updates the ActivityDescription property if a matching code is found.
        /// </summary>
        /// <param name="activityCode">The 6-digit activity code to lookup</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task LookupActivityDescriptionAsync(string activityCode, ActivityModel activityModel)
        {
            if (_activityTypeService == null)
            {
                LoggingService.LogWarning("ActivityTypeBaseService not available for code lookup", "NewClientViewModel");
                return;
            }

            try
            {
                var activityType = await _activityTypeService.GetByCodeAsync(activityCode);

                if (activityType != null && !string.IsNullOrEmpty(activityType.Description))
                {
                    // Update the description
                    activityModel.ActivityDescription = activityType.Description;

                    // Also add to multiple activities collection if not already present
                    UpdateMultipleActivitiesFromSingleActivity(activityType);

                    LoggingService.LogDebug($"Activity description found for code {activityCode}: {activityType.Description}", "NewClientViewModel");
                }
                else
                {
                    // Clear description if no match found
                    activityModel.ActivityDescription = string.Empty;
                    LoggingService.LogDebug($"No activity description found for code {activityCode}", "NewClientViewModel");

                    // Open AddActivityDialog to allow user to add the missing activity
                    await OpenAddActivityDialogAsync(activityCode, activityModel);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error looking up activity code {activityCode}: {ex.Message}", "NewClientViewModel");
                // Don't clear the description on error, let user keep what they have
            }
        }

        /// <summary>
        /// Performs asynchronous lookup of craft description from CraftTypeBase database.
        /// Updates the ActivityDescription property if a matching craft code is found.
        /// </summary>
        /// <param name="craftCode">The craft code in XX-XX-XXX format to lookup</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task LookupCraftDescriptionAsync(string craftCode, ActivityModel activityModel)
        {
            if (_craftTypeService == null)
            {
                LoggingService.LogWarning("CraftTypeBaseService not available for craft code lookup", "NewClientViewModel");
                return;
            }

            try
            {
                var craftType = await _craftTypeService.GetByCodeAsync(craftCode);

                if (craftType != null && !string.IsNullOrEmpty(craftType.Description))
                {
                    // Update the description
                    activityModel.ActivityDescription = craftType.Description;

                    LoggingService.LogDebug($"Craft description found for code {craftCode}: {craftType.Description}", "NewClientViewModel");
                }
                else
                {
                    // Clear description if no match found
                    activityModel.ActivityDescription = string.Empty;
                    LoggingService.LogDebug($"No craft description found for code {craftCode}", "NewClientViewModel");

                    // Open AddCraftTypeDialog to allow user to add the missing craft
                    await OpenAddCraftTypeDialogAsync(craftCode, activityModel);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error looking up craft code {craftCode}: {ex.Message}", "NewClientViewModel");
                // Don't clear the description on error, let user keep what they have
            }
        }

        /// <summary>
        /// Opens the AddCraftTypeDialog to allow user to add a missing craft type.
        /// Updates the activity model if a new craft type is successfully created.
        /// </summary>
        /// <param name="craftCode">The craft code that was not found</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task OpenAddCraftTypeDialogAsync(string craftCode, ActivityModel activityModel)
        {
            try
            {
                LoggingService.LogInfo($"Opening AddCraftTypeDialog for missing code: {craftCode}", "NewClientViewModel");

                // Show the AddCraftTypeDialog
                var newCraftType = await Views.Dialogs.AddCraftTypeDialog.ShowDialogAsync(craftCode, "NewClientDialogHost");

                if (newCraftType != null)
                {
                    // Update the activity model with the new craft type
                    activityModel.ActivityDescription = newCraftType.Description;

                    LoggingService.LogInfo($"New craft type added and applied: {newCraftType.Code} - {newCraftType.Description}", "NewClientViewModel");

                    // Show success message
                    ErrorManager.ShowUserSuccessToast(
                        $"تم إضافة نوع الحرفة بنجاح\nالرمز: {newCraftType.Code}",
                        "تم الإضافة",
                        "NewClientViewModel");
                }
                else
                {
                    LoggingService.LogDebug($"AddCraftTypeDialog was cancelled for code: {craftCode}", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening AddCraftTypeDialog for code {craftCode}: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح نافذة إضافة الحرفة. يرجى المحاولة مرة أخرى.",
                    "خطأ في فتح النافذة",
                    UFU2.Common.LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Opens the AddActivityDialog to allow user to add a missing activity code.
        /// </summary>
        /// <param name="activityCode">The activity code that was not found</param>
        /// <param name="activityModel">The activity model to update if save is successful</param>
        private async Task OpenAddActivityDialogAsync(string activityCode, ActivityModel activityModel)
        {
            try
            {
                LoggingService.LogInfo($"Opening AddActivityDialog for missing code: {activityCode}", "NewClientViewModel");

                // Create and show the dialog
                var addActivityDialog = new Views.Dialogs.AddActivityDialog(activityCode);
                var result = await DialogHost.Show(addActivityDialog, "NewClientDialogHost");

                if (result is bool dialogResult && dialogResult && addActivityDialog.DialogResult)
                {
                    // User saved the new activity, update the UI
                    activityModel.ActivityDescription = addActivityDialog.ActivityDescription;

                    // Also add to multiple activities collection
                    var newActivityType = new ActivityTypeBaseModel
                    {
                        Code = addActivityDialog.ActivityCode,
                        Description = addActivityDialog.ActivityDescription
                    };
                    UpdateMultipleActivitiesFromSingleActivity(newActivityType);

                    LoggingService.LogInfo($"Successfully added new activity: {activityCode} - {addActivityDialog.ActivityDescription}", "NewClientViewModel");
                }
                else
                {
                    LoggingService.LogInfo($"AddActivityDialog cancelled for code: {activityCode}", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening AddActivityDialog for code {activityCode}: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Updates the multiple activities collection for the current tab.
        /// </summary>
        /// <param name="activities">The new activities collection</param>
        private void UpdateMultipleActivities(List<ActivityTypeBaseModel> activities)
        {
            switch (SelectedActivityType)
            {
                case "MainCommercial":
                    _mainCommercialActivities = activities ?? new List<ActivityTypeBaseModel>();
                    break;
                case "SecondaryCommercial":
                    _secondaryCommercialActivities = activities ?? new List<ActivityTypeBaseModel>();
                    break;
            }

            // Update the main view ActivityCode/ActivityDescription from first activity
            UpdateMainViewFromMultipleActivities();
        }

        /// <summary>
        /// Updates the main view ActivityCode and ActivityDescription from the first activity in the multiple activities collection.
        /// </summary>
        private void UpdateMainViewFromMultipleActivities()
        {
            var currentActivities = CurrentMultipleActivities;
            var currentActivity = CurrentActivity;

            if (currentActivities.Count > 0)
            {
                var firstActivity = currentActivities[0];
                currentActivity.ActivityCode = firstActivity.Code;
                currentActivity.ActivityDescription = firstActivity.Description;
            }
            else
            {
                // Clear if no activities
                currentActivity.ActivityCode = string.Empty;
                currentActivity.ActivityDescription = string.Empty;
            }
        }

        /// <summary>
        /// Updates the multiple activities collection when a single activity is entered in the main view.
        /// Ensures bidirectional synchronization between single and multiple activity views.
        /// </summary>
        /// <param name="activityType">The activity type to add/update</param>
        private void UpdateMultipleActivitiesFromSingleActivity(ActivityTypeBaseModel activityType)
        {
            var currentActivities = CurrentMultipleActivities;

            // Remove any existing activity with the same code
            currentActivities.RemoveAll(a => a.Code == activityType.Code);

            // Add the new activity as the first item
            currentActivities.Insert(0, activityType.Clone());
        }

        /// <summary>
        /// Saves the client data to the database with comprehensive error handling.
        /// Implements proper validation, database operations, and user feedback.
        /// Handles both new client creation and existing client updates based on editing state.
        /// </summary>
        /// <param name="operationId">Operation ID for error deduplication tracking</param>
        /// <returns>Task containing the client UID (existing UID for updates, new UID for creates)</returns>
        private async Task<string> SaveClientDataAsync(string? operationId = null)
        {
            try
            {
                LoggingService.LogInfo($"Starting client data save operation - Mode: {(IsEditingExistingClient ? "Update" : "Create")}", "NewClientViewModel");

                // Check if database services are available
                if (_clientDatabaseService == null)
                {
                    ErrorManager.ShowUserErrorToast("خدمة قاعدة البيانات غير متوفرة", "خطأ في النظام", "NewClientViewModel", null, operationId);
                    LoggingService.LogError("ClientDatabaseService is not available", "NewClientViewModel");
                    return string.Empty;
                }

                if (_clientValidationService == null)
                {
                    ErrorManager.ShowUserErrorToast("خدمة التحقق من البيانات غير متوفرة", "خطأ في النظام", "NewClientViewModel", null, operationId);
                    LoggingService.LogError("ClientValidationService is not available", "NewClientViewModel");
                    return string.Empty;
                }

                // Handle existing client update scenario
                if (IsEditingExistingClient && !string.IsNullOrEmpty(ExistingClientUid))
                {
                    return await HandleExistingClientUpdateAsync(operationId);
                }

                // Handle new client creation scenario
                return await HandleNewClientCreationAsync(operationId);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in SaveClientDataAsync: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء حفظ البيانات في قاعدة البيانات. يرجى المحاولة مرة أخرى.",
                    "خطأ في قاعدة البيانات",
                    LogLevel.Error,
                    "NewClientViewModel",
                    operationId);
                throw; // Re-throw to be handled by the calling method
            }
        }

        /// <summary>
        /// Handles updating an existing client's personal information, phone numbers, and creating new activities.
        /// This method handles the complete workflow for Scenario 3: using an existing client from duplicate detection.
        /// </summary>
        /// <param name="operationId">Operation ID for error deduplication tracking</param>
        /// <returns>Task containing the existing client UID</returns>
        private async Task<string> HandleExistingClientUpdateAsync(string? operationId = null)
        {
            try
            {
                LoggingService.LogInfo($"Handling existing client update for: {ExistingClientUid}", "NewClientViewModel");

                // Step 1: Update personal information if changes detected
                bool personalInfoUpdated = await UpdatePersonalInfoIfChangedAsync(operationId);
                if (!personalInfoUpdated)
                {
                    LoggingService.LogWarning("Failed to update personal information", "NewClientViewModel");
                    return string.Empty;
                }

                // Step 2: Update phone numbers
                bool phoneNumbersUpdated = await UpdatePhoneNumbersAsync(operationId);
                if (!phoneNumbersUpdated)
                {
                    LoggingService.LogWarning("Failed to update phone numbers", "NewClientViewModel");
                    return string.Empty;
                }

                // Step 3: Create new activity with proper UID generation
                string newActivityUID = await CreateNewActivityForExistingClientAsync(operationId);
                if (string.IsNullOrEmpty(newActivityUID))
                {
                    LoggingService.LogError("Failed to create new activity for existing client", "NewClientViewModel");
                    return string.Empty;
                }

                // Step 4: Create folder structure for the new activity
                await CreateFolderStructureForActivityAsync(ExistingClientUid!, newActivityUID);

                LoggingService.LogInfo($"Successfully updated existing client and created new activity: {newActivityUID}", "NewClientViewModel");
                return ExistingClientUid!;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating existing client: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تحديث بيانات العميل الموجود",
                    "خطأ في التحديث",
                    LogLevel.Error,
                    "NewClientViewModel",
                    operationId);
                throw;
            }
        }

        /// <summary>
        /// Handles creating a new client with all associated data.
        /// This is the original client creation logic.
        /// </summary>
        /// <param name="operationId">Operation ID for error deduplication tracking</param>
        /// <returns>Task containing the generated client UID</returns>
        private async Task<string> HandleNewClientCreationAsync(string? operationId = null)
        {
            try
            {
                LoggingService.LogInfo("Handling new client creation", "NewClientViewModel");

                // Create client data from UI models
                var clientData = await CreateClientDataFromUIAsync();

                // Validate craft activity constraints
                if (!await ValidateCraftActivityConstraintsAsync())
                {
                    LoggingService.LogWarning("Craft activity validation failed", "NewClientViewModel");
                    return string.Empty;
                }

                // Validate client data using ClientValidationService
                var validationResult = _clientValidationService!.ValidateClientCreation(clientData);
                if (!validationResult.IsValid)
                {
                    var errorMessage = string.Join("\n", validationResult.Errors.SelectMany(e => e.Value));
                    ErrorManager.ShowUserWarningToast(errorMessage, "بيانات غير صحيحة", "NewClientViewModel");
                    LoggingService.LogWarning($"Client validation failed: {errorMessage}", "NewClientViewModel");
                    return string.Empty;
                }

                // Save client to database with operation tracking and Activity UID format preference
                string clientUID = await _clientDatabaseService!.CreateClientAsync(clientData, operationId, ActivityIdFormatEnabled);

                LoggingService.LogInfo($"New client created successfully with UID: {clientUID}", "NewClientViewModel");

                // Return the client UID for success notification
                return clientUID;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating new client: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء إنشاء العميل الجديد",
                    "خطأ في الإنشاء",
                    LogLevel.Error,
                    "NewClientViewModel",
                    operationId);
                throw;
            }
        }

        /// <summary>
        /// Updates personal information if changes are detected.
        /// </summary>
        /// <param name="operationId">Operation ID for error deduplication tracking</param>
        /// <returns>True if update was successful or no changes needed</returns>
        private async Task<bool> UpdatePersonalInfoIfChangedAsync(string? operationId = null)
        {
            try
            {
                // Check if there are any changes to personal information
                if (!HasPersonalInfoChanges())
                {
                    LoggingService.LogInfo("No personal information changes detected - skipping database update", "NewClientViewModel");
                    return true; // No changes needed is considered success
                }

                // Create update data from current form values
                var updateData = new ClientUpdateData
                {
                    NameAr = string.IsNullOrWhiteSpace(NameAr) ? null : NameAr.Trim(),
                    BirthDate = string.IsNullOrWhiteSpace(BirthDate) ? null : BirthDate.Trim(),
                    BirthPlace = string.IsNullOrWhiteSpace(BirthPlace) ? null : BirthPlace.Trim(),
                    Gender = Gender,
                    Address = string.IsNullOrWhiteSpace(Address) ? null : Address.Trim(),
                    NationalId = string.IsNullOrWhiteSpace(NationalId) ? null : NationalId.Trim()
                };

                // Validate update data using ClientValidationService
                var validationResult = _clientValidationService!.ValidateClientUpdate(updateData);
                if (!validationResult.IsValid)
                {
                    var errorMessage = string.Join("\n", validationResult.Errors.SelectMany(e => e.Value));
                    ErrorManager.ShowUserWarningToast(errorMessage, "بيانات غير صحيحة", "NewClientViewModel");
                    LoggingService.LogWarning($"Client update validation failed: {errorMessage}", "NewClientViewModel");
                    return false;
                }

                // Update client in database
                bool updateSuccess = await _clientDatabaseService!.UpdateClientAsync(ExistingClientUid!, updateData);

                if (updateSuccess)
                {
                    LoggingService.LogInfo($"Personal information updated successfully for client: {ExistingClientUid}", "NewClientViewModel");
                    return true;
                }
                else
                {
                    LoggingService.LogError($"Failed to update personal information for client: {ExistingClientUid}", "NewClientViewModel");
                    ErrorManager.ShowUserErrorToast("فشل في تحديث البيانات الشخصية", "خطأ في التحديث", "NewClientViewModel", null, operationId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating personal information: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تحديث البيانات الشخصية",
                    "خطأ في التحديث",
                    LogLevel.Error,
                    "NewClientViewModel",
                    operationId);
                return false;
            }
        }

        /// <summary>
        /// Updates phone numbers for the existing client.
        /// This method handles adding, updating, and removing phone numbers as needed.
        /// </summary>
        /// <param name="operationId">Operation ID for error deduplication tracking</param>
        /// <returns>True if update was successful</returns>
        private async Task<bool> UpdatePhoneNumbersAsync(string? operationId = null)
        {
            try
            {
                LoggingService.LogInfo($"Updating phone numbers for existing client: {ExistingClientUid}", "NewClientViewModel");

                // Get current phone numbers from the UI
                var currentPhoneNumbers = new List<PhoneNumberData>();

                if (PhoneNumbers != null && PhoneNumbers.PhoneNumbers.Count > 0)
                {
                    foreach (var phoneModel in PhoneNumbers.PhoneNumbers)
                    {
                        // Validate phone type before conversion
                        string phoneTypeString = ConvertPhoneTypeToString(phoneModel.PhoneType);
                        if (string.IsNullOrEmpty(phoneTypeString))
                        {
                            LoggingService.LogWarning($"Invalid phone type detected: {phoneModel.PhoneType}, defaulting to Mobile", "NewClientViewModel");
                            phoneTypeString = "Mobile";
                        }

                        var phoneData = new PhoneNumberData
                        {
                            ClientUid = ExistingClientUid!,
                            PhoneNumber = phoneModel.PhoneNumber,
                            PhoneType = phoneTypeString,
                            IsPrimary = phoneModel.IsPrimary
                        };
                        currentPhoneNumbers.Add(phoneData);
                    }
                }

                // Update phone numbers in database using ClientDatabaseService
                bool updateSuccess = await _clientDatabaseService!.UpdateClientPhoneNumbersAsync(ExistingClientUid!, currentPhoneNumbers);

                if (updateSuccess)
                {
                    LoggingService.LogInfo($"Phone numbers updated successfully for client: {ExistingClientUid}", "NewClientViewModel");
                    return true;
                }
                else
                {
                    LoggingService.LogError($"Failed to update phone numbers for client: {ExistingClientUid}", "NewClientViewModel");
                    ErrorManager.ShowUserErrorToast("فشل في تحديث أرقام الهاتف", "خطأ في التحديث", "NewClientViewModel", null, operationId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating phone numbers: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تحديث أرقام الهاتف",
                    "خطأ في التحديث",
                    LogLevel.Error,
                    "NewClientViewModel",
                    operationId);
                return false;
            }
        }

        /// <summary>
        /// Converts PhoneType enum to string representation.
        /// </summary>
        /// <param name="phoneType">The phone type enum</param>
        /// <returns>String representation of the phone type</returns>
        private static string ConvertPhoneTypeToString(PhoneType phoneType)
        {
            return phoneType switch
            {
                PhoneType.Mobile => "Mobile",
                PhoneType.Home => "Home",
                PhoneType.Work => "Work",
                PhoneType.Fax => "Fax",
                _ => "Mobile"
            };
        }

        /// <summary>
        /// Creates a new activity for an existing client using proper UID generation.
        /// This method uses the existing client UID and generates the next activity UID in sequence.
        /// </summary>
        /// <param name="operationId">Operation ID for error deduplication tracking</param>
        /// <returns>The generated activity UID, or empty string if failed</returns>
        private async Task<string> CreateNewActivityForExistingClientAsync(string? operationId = null)
        {
            try
            {
                LoggingService.LogInfo($"Creating new activity for existing client: {ExistingClientUid}", "NewClientViewModel");

                // Create activity data from UI models using existing client creation logic
                var activityData = await CreateActivityDataFromUIAsync();
                if (activityData == null || activityData.Count == 0)
                {
                    LoggingService.LogWarning("No activity data to create for existing client", "NewClientViewModel");
                    return string.Empty;
                }

                // Validate craft activity constraints
                if (!await ValidateCraftActivityConstraintsAsync())
                {
                    LoggingService.LogWarning("Craft activity validation failed for existing client", "NewClientViewModel");
                    return string.Empty;
                }

                // Use the existing client UID and create activities with proper UID generation
                string? createdActivityUID = null;
                foreach (var activity in activityData)
                {
                    // Create the activity using ClientDatabaseService with existing client UID
                    // The service will use UIDGenerationService to generate proper incremental Activity UIDs
                    string activityUID = await _clientDatabaseService!.CreateActivityForExistingClientAsync(
                        ExistingClientUid!, activity, ActivityIdFormatEnabled, operationId);

                    if (!string.IsNullOrEmpty(activityUID))
                    {
                        createdActivityUID = activityUID; // Store the last created activity UID
                        LoggingService.LogInfo($"Created activity: {activityUID} for existing client: {ExistingClientUid}", "NewClientViewModel");
                    }
                    else
                    {
                        LoggingService.LogError($"Failed to create activity for existing client: {ExistingClientUid}", "NewClientViewModel");
                        return string.Empty;
                    }
                }

                return createdActivityUID ?? string.Empty;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating new activity for existing client: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء إنشاء النشاط الجديد",
                    "خطأ في الإنشاء",
                    LogLevel.Error,
                    "NewClientViewModel",
                    operationId);
                return string.Empty;
            }
        }

        /// <summary>
        /// Creates activity data from the current UI state.
        /// This is similar to the existing logic but focused on activity creation only.
        /// </summary>
        /// <returns>List of activity creation data</returns>
        private async Task<List<ActivityCreationData>> CreateActivityDataFromUIAsync()
        {
            try
            {
                var activities = new List<ActivityCreationData>();

                // Always create a default activity for the currently active tab
                await CreateDefaultActivityForActiveTab(activities);

                LoggingService.LogDebug($"Created {activities.Count} activities from UI state", "NewClientViewModel");
                return activities;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating activity data from UI: {ex.Message}", "NewClientViewModel");
                return new List<ActivityCreationData>();
            }
        }

        /// <summary>
        /// Creates folder structure for a new activity of an existing client.
        /// This method only creates the activity folder within the existing client folder,
        /// preventing duplicate folder creation issues.
        /// </summary>
        /// <param name="clientUID">The existing client UID</param>
        /// <param name="activityUID">The new activity UID</param>
        /// <returns>Task representing the async operation</returns>
        private async Task CreateFolderStructureForActivityAsync(string clientUID, string activityUID)
        {
            try
            {
                if (_clientFolderManagementService == null)
                {
                    LoggingService.LogWarning("ClientFolderManagementService not available - skipping folder creation", "NewClientViewModel");
                    return;
                }

                // Get client name for folder creation
                string clientNameFr = NameFr?.Trim() ?? "Unknown";

                LoggingService.LogInfo($"Creating activity folder for existing client {clientUID} with new activity {activityUID}", "NewClientViewModel");

                // Create ONLY the activity folder for the existing client (not the complete structure)
                // This prevents creating duplicate client folders and ensures only the new activity folder is created
                bool folderCreated = await _clientFolderManagementService.CreateActivityFolderForExistingClientAsync(
                    clientUID, clientNameFr, activityUID);

                if (folderCreated)
                {
                    LoggingService.LogInfo($"Successfully created activity folder {activityUID} for existing client {clientUID}", "NewClientViewModel");
                }
                else
                {
                    LoggingService.LogWarning($"Failed to create activity folder {activityUID} for existing client {clientUID}", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating activity folder for existing client: {ex.Message}", "NewClientViewModel");
                // Don't throw - folder creation failure shouldn't stop the main workflow
            }
        }

        /// <summary>
        /// Creates ClientCreationData from the current UI state.
        /// Converts UI models to database-compatible data transfer objects.
        /// </summary>
        /// <returns>ClientCreationData ready for database operations</returns>
        private async Task<ClientCreationData> CreateClientDataFromUIAsync()
        {
            try
            {
                LoggingService.LogDebug("Creating client data from UI models", "NewClientViewModel");

                var clientData = new ClientCreationData
                {
                    NameFr = NameFr?.Trim() ?? string.Empty,
                    NameAr = NameAr?.Trim(),
                    BirthDate = BirthDate?.Trim(),
                    BirthPlace = BirthPlace?.Trim(),
                    Gender = Gender,
                    Address = Address?.Trim(),
                    NationalId = NationalId?.Trim()
                };

                // Convert phone numbers from UI model to data model
                if (PhoneNumbers != null && PhoneNumbers.PhoneNumbers.Count > 0)
                {
                    clientData.PhoneNumbers = new List<PhoneNumberData>();
                    foreach (var phoneModel in PhoneNumbers.PhoneNumbers)
                    {
                        var phoneData = new PhoneNumberData
                        {
                            PhoneNumber = phoneModel.PhoneNumber,
                            PhoneType = ConvertPhoneTypeToString(phoneModel.PhoneType),
                            IsPrimary = phoneModel.IsPrimary
                        };
                        clientData.PhoneNumbers.Add(phoneData);
                    }
                    LoggingService.LogDebug($"Added {clientData.PhoneNumbers.Count} phone numbers", "NewClientViewModel");
                }

                // Convert activities from UI models to data models
                clientData.Activities = new List<ActivityCreationData>();

                // Always create a default activity for the currently active tab
                await CreateDefaultActivityForActiveTab(clientData.Activities);

                // Process other activity types that have meaningful data (excluding the active tab to avoid duplicates)
                if (SelectedActivityType != "MainCommercial")
                {
                    await AddActivityDataIfExists("MainCommercial", _mainCommercialActivity, _mainCommercialFileCheckStates,
                        _mainCommercialG12SelectedYears, _mainCommercialBISSelectedYears, clientData.Activities);
                }

                if (SelectedActivityType != "SecondaryCommercial")
                {
                    await AddActivityDataIfExists("SecondaryCommercial", _secondaryCommercialActivity, _secondaryCommercialFileCheckStates,
                        _secondaryCommercialG12SelectedYears, _secondaryCommercialBISSelectedYears, clientData.Activities);
                }

                if (SelectedActivityType != "Craft")
                {
                    await AddActivityDataIfExists("Craft", _craftActivity, _craftFileCheckStates,
                        _craftG12SelectedYears, _craftBISSelectedYears, clientData.Activities);
                }

                if (SelectedActivityType != "Professional")
                {
                    await AddActivityDataIfExists("Professional", _professionalActivity, _professionalFileCheckStates,
                        _professionalG12SelectedYears, _professionalBISSelectedYears, clientData.Activities);
                }

                LoggingService.LogDebug($"Created client data with {clientData.Activities.Count} activities", "NewClientViewModel");
                return clientData;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating client data from UI: {ex.Message}", "NewClientViewModel");
                throw;
            }
        }

        /// <summary>
        /// Adds activity data to the activities list if the activity has meaningful data.
        /// </summary>
        private async Task AddActivityDataIfExists(string activityType, ActivityModel activityModel, 
            FileCheckStatesModel fileCheckModel, List<int> g12Years, List<int> bisYears, 
            List<ActivityCreationData> activities)
        {
            try
            {
                // Check if activity has meaningful data (not just default values)
                if (HasMeaningfulActivityData(activityModel))
                {
                    var activityData = new ActivityCreationData
                    {
                        ActivityType = activityType,
                        ActivityStatus = activityModel.ActivityStatus,
                        ActivityStartDate = activityModel.ActivityStartDate,
                        CommercialRegister = activityModel.CommercialRegister,
                        ActivityLocation = activityModel.ActivityLocation,
                        NifNumber = activityModel.NifNumber,
                        NisNumber = activityModel.NisNumber,
                        ArtNumber = activityModel.ArtNumber,
                        CpiDaira = activityModel.CpiDaira,
                        CpiWilaya = activityModel.CpiWilaya,
                        ActivityUpdateDate = activityModel.ActivityUpdateDate,
                        ActivityUpdateNote = activityModel.ActivityUpdateNote
                    };

                    // Add activity codes for commercial activities
                    if ((activityType == "MainCommercial" || activityType == "SecondaryCommercial") && 
                        CurrentMultipleActivities.Count > 0)
                    {
                        activityData.ActivityCodes = CurrentMultipleActivities
                            .Where(a => int.TryParse(a.Code, out _))
                            .Select(a => int.Parse(a.Code))
                            .ToList();
                    }

                    // Add craft code for craft activities
                    if (activityType == "Craft" && !string.IsNullOrWhiteSpace(activityModel.ActivityCode))
                    {
                        activityData.CraftCode = activityModel.ActivityCode;
                    }

                    // Add activity description for professional activities only
                    // (Craft activities now use CraftCode instead of ActivityDescription)
                    if (activityType == "Professional" && !string.IsNullOrWhiteSpace(activityModel.ActivityDescription))
                    {
                        activityData.ActivityDescription = activityModel.ActivityDescription;
                    }

                    // Convert file check states
                    if (fileCheckModel != null)
                    {
                        activityData.FileCheckStates = ConvertFileCheckStates(fileCheckModel, activityType);
                    }

                    // Add payment years
                    if (g12Years != null && g12Years.Count > 0)
                    {
                        activityData.G12CheckYears = new List<int>(g12Years);
                    }

                    if (bisYears != null && bisYears.Count > 0)
                    {
                        activityData.BisCheckYears = new List<int>(bisYears);
                    }

                    // Convert notes
                    if (Notes != null && Notes.Count > 0)
                    {
                        activityData.Notes = ConvertNotesToCreationData(Notes);
                    }

                    activities.Add(activityData);
                    LoggingService.LogDebug($"Added {activityType} activity data", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding {activityType} activity data: {ex.Message}", "NewClientViewModel");
                throw;
            }
        }

        /// <summary>
        /// Checks if an activity model has meaningful data beyond default values.
        /// </summary>
        private bool HasMeaningfulActivityData(ActivityModel activityModel)
        {
            if (activityModel == null) return false;

            // Check if any field has non-default data
            return !string.IsNullOrWhiteSpace(activityModel.ActivityLocation) ||
                   !string.IsNullOrWhiteSpace(activityModel.CommercialRegister) ||
                   !string.IsNullOrWhiteSpace(activityModel.NifNumber) ||
                   !string.IsNullOrWhiteSpace(activityModel.NisNumber) ||
                   !string.IsNullOrWhiteSpace(activityModel.ArtNumber) ||
                   !string.IsNullOrWhiteSpace(activityModel.CpiDaira) ||
                   !string.IsNullOrWhiteSpace(activityModel.CpiWilaya) ||
                   !string.IsNullOrWhiteSpace(activityModel.ActivityStartDate) ||
                   !string.IsNullOrWhiteSpace(activityModel.ActivityUpdateNote);
        }

        /// <summary>
        /// Creates a default activity for the currently active tab, regardless of whether fields are filled.
        /// This ensures that every client save operation creates at least one activity for the selected tab.
        /// </summary>
        /// <param name="activities">The activities list to add the default activity to</param>
        private async Task CreateDefaultActivityForActiveTab(List<ActivityCreationData> activities)
        {
            try
            {
                // Get the activity model and file check states for the currently active tab
                var (activityModel, fileCheckModel, g12Years, bisYears) = GetCurrentTabData();

                // Create activity data with proper English activity type constant (required for validation)
                // Convert empty strings to null to satisfy database CHECK constraints
                var activityData = new ActivityCreationData
                {
                    ActivityType = SelectedActivityType, // Use English constant for validation
                    ActivityStatus = ConvertEmptyToNull(activityModel.ActivityStatus) ?? ActivityStatusItemsComboBoxConverter.GetDefaultStatus(SelectedActivityType),
                    ActivityLocation = ConvertEmptyToNull(activityModel.ActivityLocation),
                    CommercialRegister = ConvertEmptyToNull(activityModel.CommercialRegister),
                    NifNumber = ConvertEmptyToNull(activityModel.NifNumber),
                    NisNumber = ConvertEmptyToNull(activityModel.NisNumber),
                    ArtNumber = ConvertEmptyToNull(activityModel.ArtNumber),
                    CpiDaira = ConvertEmptyToNull(activityModel.CpiDaira),
                    CpiWilaya = ConvertEmptyToNull(activityModel.CpiWilaya),
                    ActivityStartDate = ConvertEmptyToNull(activityModel.ActivityStartDate),
                    ActivityUpdateDate = ConvertEmptyToNull(activityModel.ActivityUpdateDate),
                    ActivityUpdateNote = ConvertEmptyToNull(activityModel.ActivityUpdateNote)
                };

                // Add activity codes for commercial activities if they exist
                if ((SelectedActivityType == "MainCommercial" || SelectedActivityType == "SecondaryCommercial") &&
                    CurrentMultipleActivities.Count > 0)
                {
                    activityData.ActivityCodes = CurrentMultipleActivities
                        .Where(a => int.TryParse(a.Code, out _))
                        .Select(a => int.Parse(a.Code))
                        .ToList();
                }

                // Add craft code for craft activities
                if (SelectedActivityType == "Craft" && !string.IsNullOrWhiteSpace(activityModel.ActivityCode))
                {
                    activityData.CraftCode = activityModel.ActivityCode;
                }

                // Add non-commercial activity description (convert empty to null for database constraints)
                // For craft activities, only add description if no craft code is provided (fallback)
                if (SelectedActivityType == "Professional" ||
                    (SelectedActivityType == "Craft" && string.IsNullOrWhiteSpace(activityModel.ActivityCode)))
                {
                    activityData.ActivityDescription = ConvertEmptyToNull(activityModel.ActivityDescription);
                }

                // Convert file check states
                activityData.FileCheckStates = ConvertFileCheckStates(fileCheckModel, SelectedActivityType);

                // Add G12 and BIS check data
                activityData.G12CheckYears = g12Years?.ToList() ?? new List<int>();
                activityData.BisCheckYears = bisYears?.ToList() ?? new List<int>();

                // Convert notes - CRITICAL FIX: Notes were missing from default activity
                if (Notes != null && Notes.Count > 0)
                {
                    activityData.Notes = ConvertNotesToCreationData(Notes);
                    LoggingService.LogDebug($"Added {activityData.Notes.Count} notes to default activity for active tab: {SelectedActivityType}", "NewClientViewModel");
                }

                activities.Add(activityData);

                LoggingService.LogInfo($"Created default activity for active tab: {SelectedActivityType} with type: {activityData.ActivityType}", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating default activity for active tab {SelectedActivityType}: {ex.Message}", "NewClientViewModel");
                throw;
            }
        }

        /// <summary>
        /// Gets the activity model and related data for the currently active tab.
        /// </summary>
        /// <returns>Tuple containing activity model, file check model, and year lists</returns>
        private (ActivityModel activityModel, FileCheckStatesModel fileCheckModel, List<int> g12Years, List<int> bisYears) GetCurrentTabData()
        {
            return SelectedActivityType switch
            {
                "MainCommercial" => (_mainCommercialActivity, _mainCommercialFileCheckStates, _mainCommercialG12SelectedYears, _mainCommercialBISSelectedYears),
                "SecondaryCommercial" => (_secondaryCommercialActivity, _secondaryCommercialFileCheckStates, _secondaryCommercialG12SelectedYears, _secondaryCommercialBISSelectedYears),
                "Craft" => (_craftActivity, _craftFileCheckStates, _craftG12SelectedYears, _craftBISSelectedYears),
                "Professional" => (_professionalActivity, _professionalFileCheckStates, _professionalG12SelectedYears, _professionalBISSelectedYears),
                _ => (_mainCommercialActivity, _mainCommercialFileCheckStates, _mainCommercialG12SelectedYears, _mainCommercialBISSelectedYears)
            };
        }



        /// <summary>
        /// Converts FileCheckStatesModel to Dictionary for database operations.
        /// Validates input parameters and handles activity type-specific file check requirements.
        /// </summary>
        private Dictionary<string, bool> ConvertFileCheckStates(FileCheckStatesModel fileCheckModel, string activityType)
        {
            var fileCheckStates = new Dictionary<string, bool>();

            try
            {
                if (fileCheckModel == null)
                {
                    LoggingService.LogWarning($"FileCheckStatesModel is null for activity type: {activityType}", "NewClientViewModel");
                    return fileCheckStates;
                }

                if (string.IsNullOrWhiteSpace(activityType))
                {
                    LoggingService.LogWarning("Activity type is null or empty for file check conversion", "NewClientViewModel");
                    return fileCheckStates;
                }

                // Get the valid file check types for this activity type
                var validTypes = ActivityCreationData.GetRequiredFileCheckTypes(activityType);

                if (validTypes == null || validTypes.Count == 0)
                {
                    LoggingService.LogWarning($"No valid file check types found for activity type: {activityType}", "NewClientViewModel");
                    return fileCheckStates;
                }

                // Map the file check states based on activity type
                foreach (var fileCheckType in validTypes)
                {
                    bool isChecked = fileCheckType switch
                    {
                        "CAS" => fileCheckModel.CasChipChecked,
                        "NIF" => fileCheckModel.NifChipChecked,
                        "NIS" => fileCheckModel.NisChipChecked,
                        "RC" => fileCheckModel.RcChipChecked,
                        "ART" => fileCheckModel.ArtChipChecked,
                        "AGR" => fileCheckModel.AgrChipChecked,
                        "DEX" => fileCheckModel.DexChipChecked,
                        _ => false
                    };

                    fileCheckStates[fileCheckType] = isChecked;
                }

                LoggingService.LogDebug($"Converted {fileCheckStates.Count} file check states for {activityType}", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error converting file check states for {activityType}: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تحويل حالات فحص الملفات. سيتم استخدام القيم الافتراضية.",
                    "خطأ في معالجة فحص الملفات",
                    LogLevel.Warning, "NewClientViewModel");
            }

            return fileCheckStates;
        }

        /// <summary>
        /// Converts empty or whitespace-only strings to null values.
        /// This is required for database CHECK constraints that expect NULL instead of empty strings.
        /// </summary>
        /// <param name="value">The string value to check</param>
        /// <returns>null if the string is null, empty, or whitespace-only; otherwise returns the original string</returns>
        private static string? ConvertEmptyToNull(string? value)
        {
            return string.IsNullOrWhiteSpace(value) ? null : value;
        }

        /// <summary>
        /// Converts NotesCollectionModel to List of NoteCreationData.
        /// Validates note data and filters out invalid notes during conversion.
        /// </summary>
        private List<NoteCreationData> ConvertNotesToCreationData(NotesCollectionModel notesCollection)
        {
            var notesList = new List<NoteCreationData>();

            try
            {
                if (notesCollection?.Notes == null)
                {
                    LoggingService.LogDebug("Notes collection is null or empty", "NewClientViewModel");
                    return notesList;
                }

                foreach (var noteModel in notesCollection.Notes)
                {
                    // Skip invalid notes
                    if (noteModel == null || !noteModel.IsValid())
                    {
                        LoggingService.LogWarning($"Skipping invalid note during conversion", "NewClientViewModel");
                        continue;
                    }

                    var noteData = new NoteCreationData
                    {
                        Content = noteModel.Content,
                        Priority = noteModel.Priority
                    };

                    // Validate the created data
                    if (noteData.IsValid())
                    {
                        notesList.Add(noteData);
                    }
                    else
                    {
                        LoggingService.LogWarning($"Created note data is invalid, skipping", "NewClientViewModel");
                    }
                }

                LoggingService.LogDebug($"Converted {notesList.Count} valid notes to creation data", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error converting notes to creation data: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تحويل بيانات الملاحظات. سيتم تجاهل الملاحظات.",
                    "خطأ في معالجة الملاحظات",
                    LogLevel.Warning, "NewClientViewModel");
            }

            return notesList;
        }



        /// <summary>
        /// Creates the client folder structure after successful client creation.
        /// Generates the activity UID and calls the ClientFolderManagementService to create the folder structure.
        /// </summary>
        /// <param name="clientUID">The client UID that was created</param>
        /// <param name="operationId">Operation ID for error tracking</param>
        private async Task CreateClientFolderStructureAsync(string clientUID, string? operationId = null)
        {
            try
            {
                // Check if folder management service is available
                if (_clientFolderManagementService == null)
                {
                    LoggingService.LogWarning("ClientFolderManagementService not available - skipping folder creation", "NewClientViewModel");
                    return;
                }

                // Get client name for folder creation
                string clientNameFr = NameFr?.Trim() ?? "Unknown";

                // Generate the activity UID for the default activity
                // The default activity is always the first activity (sequence 1) for the client
                string activityUID = ActivityIdFormatEnabled
                    ? $"{clientUID}_Act1s"
                    : $"{clientUID}_Act1";

                LoggingService.LogInfo($"Creating folder structure for client {clientUID} with activity {activityUID}", "NewClientViewModel");

                // Create the folder structure
                bool folderCreated = await _clientFolderManagementService.CreateClientFolderStructureAsync(
                    clientUID, clientNameFr, activityUID);

                if (folderCreated)
                {
                    LoggingService.LogInfo($"Successfully created folder structure for client {clientUID}", "NewClientViewModel");

                    // Copy profile image to client folder if available
                    if (HasProfileImage && _profileImage != null)
                    {
                        LoggingService.LogInfo($"Copying profile image to client folder for {clientUID}", "NewClientViewModel");

                        bool imageCopied = await _clientFolderManagementService.CopyProfileImageToClientFolderAsync(
                            clientUID, clientNameFr, _profileImage, GetProfileImageExtension());

                        if (imageCopied)
                        {
                            LoggingService.LogInfo($"Profile image copied successfully for client {clientUID}", "NewClientViewModel");
                        }
                        else
                        {
                            LoggingService.LogWarning($"Failed to copy profile image for client {clientUID}", "NewClientViewModel");
                            // Note: Error messages are already shown by the ClientFolderManagementService
                            // This is not a critical failure - the client and folder structure were created successfully
                        }
                    }
                    else
                    {
                        LoggingService.LogDebug($"No profile image to copy for client {clientUID}", "NewClientViewModel");
                    }
                }
                else
                {
                    LoggingService.LogWarning($"Failed to create folder structure for client {clientUID}", "NewClientViewModel");
                    // Note: Error messages are already shown by the ClientFolderManagementService
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error creating folder structure for client {clientUID}: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء إنشاء مجلدات العميل. تم حفظ بيانات العميل بنجاح ولكن قد تحتاج لإنشاء المجلدات يدوياً.",
                    "تحذير - خطأ في إنشاء المجلدات",
                    LogLevel.Warning,
                    "NewClientViewModel",
                    operationId);
            }
        }

        /// <summary>
        /// Sets the profile image from ImageManagementDialog.
        /// This method is called when the user successfully edits an image in the ImageManagementDialog.
        /// </summary>
        /// <param name="processedImage">The processed image from ImageManagementDialog</param>
        /// <param name="originalExtension">The original file extension (e.g., "jpg", "png")</param>
        public void SetProfileImage(System.Windows.Media.Imaging.BitmapSource processedImage, string originalExtension)
        {
            try
            {
                if (processedImage == null)
                {
                    LoggingService.LogWarning("Attempted to set null profile image", "NewClientViewModel");
                    return;
                }

                ProfileImage = processedImage;
                _profileImageOriginalExtension = originalExtension?.ToLowerInvariant() ?? "jpg";

                LoggingService.LogInfo($"Profile image set successfully - Size: {processedImage.PixelWidth}x{processedImage.PixelHeight}, Extension: {_profileImageOriginalExtension}", "NewClientViewModel");

                // Notify that HasProfileImage property has changed
                OnPropertyChanged(nameof(HasProfileImage));
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting profile image: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تعيين الصورة الشخصية. يرجى المحاولة مرة أخرى.",
                    "خطأ في تعيين الصورة",
                    LogLevel.Warning,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Clears the current profile image.
        /// This method is called when the client creation is cancelled or completed.
        /// </summary>
        public void ClearProfileImage()
        {
            try
            {
                if (_profileImage != null)
                {
                    LoggingService.LogDebug("Clearing profile image", "NewClientViewModel");
                    ProfileImage = null;
                    _profileImageOriginalExtension = null;
                    OnPropertyChanged(nameof(HasProfileImage));
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing profile image: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Gets the file extension for the profile image.
        /// Returns "jpg" as default if no extension is set.
        /// </summary>
        /// <returns>The file extension without the dot (e.g., "jpg", "png")</returns>
        public string GetProfileImageExtension()
        {
            return _profileImageOriginalExtension ?? "jpg";
        }

        /// <summary>
        /// Validates craft activity constraints to ensure proper format and data integrity.
        /// </summary>
        /// <returns>True if validation passes, false otherwise</returns>
        private async Task<bool> ValidateCraftActivityConstraintsAsync()
        {
            try
            {
                // Validate craft code format if provided
                if (SelectedActivityType == "Craft" && !string.IsNullOrWhiteSpace(_craftActivity.ActivityCode))
                {
                    if (!IsValidCraftCodeFormat(_craftActivity.ActivityCode))
                    {
                        ErrorManager.ShowUserWarningToast(
                            "تنسيق رمز النشاط الحرفي غير صحيح. يجب أن يكون بالتنسيق XX-XX-XXX",
                            "رمز حرفي غير صحيح",
                            "NewClientViewModel");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating craft activity constraints: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء التحقق من قيود النشاط الحرفي",
                    "خطأ في التحقق",
                    UFU2.Common.LogLevel.Error,
                    "NewClientViewModel");
                return false;
            }
        }

        /// <summary>
        /// Checks if an activity model contains meaningful craft data.
        /// </summary>
        /// <param name="activityModel">The activity model to check</param>
        /// <returns>True if the model contains craft data</returns>
        private bool HasMeaningfulCraftData(ActivityModel activityModel)
        {
            return !string.IsNullOrWhiteSpace(activityModel.ActivityCode) &&
                   IsValidCraftCodeFormat(activityModel.ActivityCode);
        }

        /// <summary>
        /// Initializes CPI location data by loading wilayas from the database.
        /// Called during ViewModel construction to populate the ComboBox data.
        /// </summary>
        private async Task InitializeCpiLocationDataAsync()
        {
            try
            {
                if (_cpiLocationService == null)
                {
                    LoggingService.LogWarning("CpiLocationService not available for loading location data", "NewClientViewModel");
                    return;
                }

                // Load all wilayas
                var wilayas = await _cpiLocationService.GetWilayasAsync();
                _cpiWilayas.Clear();
                _cpiWilayas.AddRange(wilayas);
                OnPropertyChanged(nameof(CpiWilayas));

                LoggingService.LogDebug($"Loaded {wilayas.Count} wilayas for CPI location selection", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading CPI location data: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleError(ex, "Failed to load geographical location data");
            }
        }

        /// <summary>
        /// Updates the CpiDairas collection based on the selected wilaya.
        /// Implements cascading selection functionality for geographical location controls.
        /// Uses thread-safe collection updates to prevent ItemsControl inconsistency errors.
        /// Includes race condition prevention for rapid selection changes.
        /// </summary>
        private async Task UpdateCpiDairasForSelectedWilayaAsync()
        {
            try
            {
                // Prevent race conditions during rapid selection changes
                if (_isDairasUpdateInProgress)
                {
                    LoggingService.LogDebug("Dairas update already in progress, skipping duplicate request", "NewClientViewModel");
                    return;
                }

                _isDairasUpdateInProgress = true;

                if (_cpiLocationService == null || _selectedCpiWilaya == null)
                {
                    // Ensure UI updates happen on the UI thread
                    await Dispatcher.CurrentDispatcher.InvokeAsync(() =>
                    {
                        _cpiDairas.Clear();
                        _selectedCpiDaira = null;
                        CurrentActivity.CpiDaira = string.Empty;
                    });
                    return;
                }

                // Load dairas for the selected wilaya (this can happen on background thread)
                var dairas = await _cpiLocationService.GetDairasByWilayaAsync(_selectedCpiWilaya.Code);

                // Ensure collection updates happen on the UI thread to prevent ItemsControl inconsistency
                await Dispatcher.CurrentDispatcher.InvokeAsync(() =>
                {
                    _cpiDairas.Clear();

                    // Add items individually since ObservableCollection doesn't have AddRange
                    foreach (var daira in dairas)
                    {
                        _cpiDairas.Add(daira);
                    }

                    // Clear the selected daira since the list has changed
                    _selectedCpiDaira = null;
                    CurrentActivity.CpiDaira = string.Empty;
                });

                LoggingService.LogDebug($"Loaded {dairas.Count} dairas for wilaya {_selectedCpiWilaya.NameAr}", "NewClientViewModel");
            }
            catch (InvalidOperationException ex)
            {
                // Handle collection modification errors specifically
                LoggingService.LogError($"Collection modification error during daira update: {ex.Message}", "NewClientViewModel");

                // Attempt to recover by clearing the collection on the UI thread
                try
                {
                    await Dispatcher.CurrentDispatcher.InvokeAsync(() =>
                    {
                        _cpiDairas.Clear();
                        _selectedCpiDaira = null;
                        CurrentActivity.CpiDaira = string.Empty;
                    });
                }
                catch (Exception recoveryEx)
                {
                    LoggingService.LogError($"Failed to recover from collection error: {recoveryEx.Message}", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading dairas for selected wilaya: {ex.Message}", "NewClientViewModel");

                // Ensure collection is in a consistent state
                try
                {
                    await Dispatcher.CurrentDispatcher.InvokeAsync(() =>
                    {
                        _cpiDairas.Clear();
                        _selectedCpiDaira = null;
                        CurrentActivity.CpiDaira = string.Empty;
                    });
                }
                catch (Exception cleanupEx)
                {
                    LoggingService.LogError($"Failed to cleanup collection after error: {cleanupEx.Message}", "NewClientViewModel");
                }

                ErrorManager.HandleError(ex, "Failed to load district data for selected province");
            }
            finally
            {
                // Always reset the flag to allow future updates
                _isDairasUpdateInProgress = false;
            }
        }

        /// <summary>
        /// Synchronizes the CPI location ComboBox selections with the current activity's CpiWilaya and CpiDaira values.
        /// Called when switching between activity tabs to maintain proper selection state.
        /// </summary>
        private void SynchronizeCpiLocationSelections()
        {
            try
            {
                var currentActivity = CurrentActivity;
                if (currentActivity == null)
                    return;

                // Find and set the selected wilaya based on the current activity's CpiWilaya value
                var matchingWilaya = _cpiWilayas.FirstOrDefault(w => w.NameAr == currentActivity.CpiWilaya);
                if (matchingWilaya != _selectedCpiWilaya)
                {
                    _selectedCpiWilaya = matchingWilaya;
                    OnPropertyChanged(nameof(SelectedCpiWilaya));

                    // Update dairas for the selected wilaya
                    if (matchingWilaya != null)
                    {
                        _ = UpdateCpiDairasForSelectedWilayaAsync();
                    }
                    else
                    {
                        // Ensure UI updates happen on the UI thread
                        Dispatcher.CurrentDispatcher.Invoke(() =>
                        {
                            _cpiDairas.Clear();
                        });
                    }
                }

                // Find and set the selected daira based on the current activity's CpiDaira value
                var matchingDaira = _cpiDairas.FirstOrDefault(d => d.NameAr == currentActivity.CpiDaira);
                if (matchingDaira != _selectedCpiDaira)
                {
                    _selectedCpiDaira = matchingDaira;
                    OnPropertyChanged(nameof(SelectedCpiDaira));
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error synchronizing CPI location selections: {ex.Message}", "NewClientViewModel");
            }
        }

        #endregion
    }
}
