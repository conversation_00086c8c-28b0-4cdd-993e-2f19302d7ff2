using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter for formatting phone numbers according to UFU2 project requirements.
    /// Formats 10-digit numbers as XXXX-XX-XX-XX and 9-digit numbers as XXX-XX-XX-XX.
    /// Supports bidirectional conversion for proper WPF data binding.
    /// </summary>
    public class PhoneNumberConverter : IValueConverter
    {
        #region Private Fields

        // Compiled regex patterns for performance optimization
        private static readonly Regex DigitsOnlyRegex = new Regex(@"\D", RegexOptions.Compiled);
        private static readonly Regex TenDigitPattern = new Regex(@"^(\d{4})(\d{2})(\d{2})(\d{2})$", RegexOptions.Compiled);
        private static readonly Regex NineDigitPattern = new Regex(@"^(\d{3})(\d{2})(\d{2})(\d{2})$", RegexOptions.Compiled);

        #endregion

        #region IValueConverter Implementation

        /// <summary>
        /// Converts a phone number string to formatted display format.
        /// Formats 10-digit numbers as XXXX-XX-XX-XX and 9-digit numbers as XXX-XX-XX-XX.
        /// </summary>
        /// <param name="value">The phone number string to format</param>
        /// <param name="targetType">The target type (not used)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Formatted phone number string or original value if formatting fails</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                // Handle null or empty input
                if (value == null)
                    return string.Empty;

                var phoneNumber = value.ToString()?.Trim();
                if (string.IsNullOrWhiteSpace(phoneNumber))
                    return string.Empty;

                // Extract only digits from the input
                var digitsOnly = DigitsOnlyRegex.Replace(phoneNumber, "");

                // Format based on digit count according to UFU2 requirements
                return digitsOnly.Length switch
                {
                    10 => FormatTenDigitNumber(digitsOnly),
                    9 => FormatNineDigitNumber(digitsOnly),
                    _ => phoneNumber // Return original if not 9 or 10 digits
                };
            }
            catch (Exception)
            {
                // Return original value if any error occurs during formatting
                return value?.ToString() ?? string.Empty;
            }
        }

        /// <summary>
        /// Converts a formatted phone number back to unformatted string for data storage.
        /// Removes all formatting characters and returns only digits.
        /// </summary>
        /// <param name="value">The formatted phone number string</param>
        /// <param name="targetType">The target type (not used)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Unformatted phone number string containing only digits</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                // Handle null or empty input
                if (value == null)
                    return string.Empty;

                var phoneNumber = value.ToString()?.Trim();
                if (string.IsNullOrWhiteSpace(phoneNumber))
                    return string.Empty;

                // Extract only digits for storage
                return DigitsOnlyRegex.Replace(phoneNumber, "");
            }
            catch (Exception)
            {
                // Return original value if any error occurs during conversion
                return value?.ToString() ?? string.Empty;
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Formats a 10-digit phone number as XXXX-XX-XX-XX.
        /// </summary>
        /// <param name="digits">String containing exactly 10 digits</param>
        /// <returns>Formatted phone number string</returns>
        private static string FormatTenDigitNumber(string digits)
        {
            var match = TenDigitPattern.Match(digits);
            if (match.Success)
            {
                return $"{match.Groups[1].Value}-{match.Groups[2].Value}-{match.Groups[3].Value}-{match.Groups[4].Value}";
            }
            return digits; // Fallback to original if regex fails
        }

        /// <summary>
        /// Formats a 9-digit phone number as XXX-XX-XX-XX.
        /// </summary>
        /// <param name="digits">String containing exactly 9 digits</param>
        /// <returns>Formatted phone number string</returns>
        private static string FormatNineDigitNumber(string digits)
        {
            var match = NineDigitPattern.Match(digits);
            if (match.Success)
            {
                return $"{match.Groups[1].Value}-{match.Groups[2].Value}-{match.Groups[3].Value}-{match.Groups[4].Value}";
            }
            return digits; // Fallback to original if regex fails
        }

        #endregion
    }
}
