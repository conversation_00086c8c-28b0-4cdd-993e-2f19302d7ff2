using System;

namespace UFU2.Models
{
    /// <summary>
    /// Represents an entry for data that was added to an entity.
    /// Used for displaying change history in the UI with Arabic text support.
    /// </summary>
    public class AddedEntityEntry
    {
        /// <summary>
        /// Unique identifier for the added entity entry
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Type of entity (e.g., 'Client', 'Activity', 'PhoneNumber')
        /// </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// UID of the entity that received new data
        /// </summary>
        public string EntityId { get; set; } = string.Empty;

        /// <summary>
        /// Name of the added field/data
        /// </summary>
        public string DataName { get; set; } = string.Empty;

        /// <summary>
        /// The new value that was added (JSON serialized)
        /// </summary>
        public string AddedValue { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the data was added
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Display name for the entity type in Arabic
        /// </summary>
        public string EntityTypeDisplayAr
        {
            get
            {
                return EntityType switch
                {
                    "Client" => "عميل",
                    "Activity" => "نشاط",
                    "PhoneNumber" => "رقم هاتف",
                    _ => EntityType
                };
            }
        }

        /// <summary>
        /// Display name for the data field in Arabic
        /// </summary>
        public string DataNameDisplayAr
        {
            get
            {
                return DataName switch
                {
                    "ClientData" => "بيانات العميل",
                    "PhoneNumber" => "رقم الهاتف",
                    "Activity" => "النشاط",
                    "ActivityData" => "بيانات النشاط",
                    "ActivityCodes" => "رموز النشاط",
                    "CraftCode" => "رمز الحرفة",
                    "ActivityDescription" => "وصف النشاط",
                    "FileCheckStates" => "حالات فحص الملفات",
                    "G12CheckYears" => "سنوات فحص G12",
                    "BisCheckYears" => "سنوات فحص BIS",
                    "Notes" => "الملاحظات",
                    "NameAr" => "الاسم العربي",
                    "BirthDate" => "تاريخ الميلاد",
                    "BirthPlace" => "مكان الميلاد",
                    "Gender" => "الجنس",
                    "Address" => "العنوان",
                    "NationalId" => "رقم الهوية الوطنية",
                    _ => DataName
                };
            }
        }
    }

    /// <summary>
    /// Represents an entry for data that was updated in an entity.
    /// Used for displaying change history in the UI with Arabic text support.
    /// </summary>
    public class UpdatedEntityEntry
    {
        /// <summary>
        /// Unique identifier for the updated entity entry
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Type of entity (e.g., 'Client', 'Activity', 'PhoneNumber')
        /// </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// UID of the entity that was modified
        /// </summary>
        public string EntityId { get; set; } = string.Empty;

        /// <summary>
        /// Name of the modified field/data
        /// </summary>
        public string DataName { get; set; } = string.Empty;

        /// <summary>
        /// Previous value before change (JSON serialized)
        /// </summary>
        public string OldValue { get; set; } = string.Empty;

        /// <summary>
        /// New value after change (JSON serialized)
        /// </summary>
        public string NewValue { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the data was updated
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Display name for the entity type in Arabic
        /// </summary>
        public string EntityTypeDisplayAr
        {
            get
            {
                return EntityType switch
                {
                    "Client" => "عميل",
                    "Activity" => "نشاط",
                    "PhoneNumber" => "رقم هاتف",
                    _ => EntityType
                };
            }
        }

        /// <summary>
        /// Display name for the data field in Arabic
        /// </summary>
        public string DataNameDisplayAr
        {
            get
            {
                return DataName switch
                {
                    "NameAr" => "الاسم العربي",
                    "BirthDate" => "تاريخ الميلاد",
                    "BirthPlace" => "مكان الميلاد",
                    "Gender" => "الجنس",
                    "Address" => "العنوان",
                    "NationalId" => "رقم الهوية الوطنية",
                    "PhoneNumber" => "رقم الهاتف",
                    "ActivityType" => "نوع النشاط",
                    "ActivityStatus" => "حالة النشاط",
                    "ActivityStartDate" => "تاريخ بداية النشاط",
                    "CommercialRegister" => "السجل التجاري",
                    "ActivityLocation" => "موقع النشاط",
                    "NifNumber" => "رقم NIF",
                    "NisNumber" => "رقم NIS",
                    "ArtNumber" => "رقم ART",
                    "CpiDaira" => "دائرة CPI",
                    "CpiWilaya" => "ولاية CPI",
                    _ => DataName
                };
            }
        }
    }

    /// <summary>
    /// Represents an entry for data that was deleted from an entity.
    /// Used for displaying change history in the UI with Arabic text support.
    /// </summary>
    public class DeletedEntityEntry
    {
        /// <summary>
        /// Unique identifier for the deleted entity entry
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Type of entity (e.g., 'Client', 'Activity', 'PhoneNumber')
        /// </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// UID of the entity that lost data
        /// </summary>
        public string EntityId { get; set; } = string.Empty;

        /// <summary>
        /// Name of the removed field/data
        /// </summary>
        public string DataName { get; set; } = string.Empty;

        /// <summary>
        /// The value that was removed (JSON serialized)
        /// </summary>
        public string DeletedValue { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the data was deleted
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Display name for the entity type in Arabic
        /// </summary>
        public string EntityTypeDisplayAr
        {
            get
            {
                return EntityType switch
                {
                    "Client" => "عميل",
                    "Activity" => "نشاط",
                    "PhoneNumber" => "رقم هاتف",
                    _ => EntityType
                };
            }
        }

        /// <summary>
        /// Display name for the data field in Arabic
        /// </summary>
        public string DataNameDisplayAr
        {
            get
            {
                return DataName switch
                {
                    "PhoneNumber" => "رقم الهاتف",
                    "Activity" => "النشاط",
                    "ActivityData" => "بيانات النشاط",
                    "ActivityCodes" => "رموز النشاط",
                    "CraftCode" => "رمز الحرفة",
                    "ActivityDescription" => "وصف النشاط",
                    "FileCheckStates" => "حالات فحص الملفات",
                    "G12CheckYears" => "سنوات فحص G12",
                    "BisCheckYears" => "سنوات فحص BIS",
                    "Notes" => "الملاحظات",
                    "NameAr" => "الاسم العربي",
                    "BirthDate" => "تاريخ الميلاد",
                    "BirthPlace" => "مكان الميلاد",
                    "Gender" => "الجنس",
                    "Address" => "العنوان",
                    "NationalId" => "رقم الهوية الوطنية",
                    _ => DataName
                };
            }
        }
    }
}
