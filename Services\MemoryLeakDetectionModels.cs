using System;
using System.Collections.Generic;

namespace UFU2.Services
{
    #region Memory Leak Detection Models

    /// <summary>
    /// Comprehensive memory leak detection report.
    /// </summary>
    public class MemoryLeakDetectionReport
    {
        public DateTime GeneratedAt { get; set; }
        public long TotalSnapshots { get; set; }
        public long LeaksDetected { get; set; }
        public long AlertsGenerated { get; set; }
        public long GcCollectionsForced { get; set; }
        public List<MemoryLeakInfo> PotentialLeaks { get; set; } = new List<MemoryLeakInfo>();
        public List<string> Recommendations { get; set; } = new List<string>();
        public MemoryUsageInfo CurrentMemoryUsage { get; set; } = new MemoryUsageInfo();

        /// <summary>
        /// Indicates if there are critical memory leaks that require immediate attention.
        /// </summary>
        public bool HasCriticalLeaks => PotentialLeaks.Exists(l => l.Severity == LeakSeverity.Critical);

        /// <summary>
        /// Gets the overall leak severity level.
        /// </summary>
        public LeakSeverity OverallSeverity
        {
            get
            {
                if (HasCriticalLeaks) return LeakSeverity.Critical;
                if (PotentialLeaks.Exists(l => l.Severity == LeakSeverity.Warning)) return LeakSeverity.Warning;
                return LeakSeverity.None;
            }
        }
    }

    /// <summary>
    /// Information about a specific memory leak.
    /// </summary>
    public class MemoryLeakInfo
    {
        public MemoryLeakType LeakType { get; set; }
        public LeakSeverity Severity { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime DetectedAt { get; set; }
        public string AffectedComponent { get; set; } = string.Empty;
        public string RecommendedAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Memory usage information at a specific point in time.
    /// </summary>
    public class MemoryUsageInfo
    {
        public DateTime Timestamp { get; set; }
        public double WorkingSetMB { get; set; }
        public double PrivateMemoryMB { get; set; }
        public double VirtualMemoryMB { get; set; }
        public double TotalMemoryMB { get; set; }
        public int Gen0Collections { get; set; }
        public int Gen1Collections { get; set; }
        public int Gen2Collections { get; set; }
        public int TrackedResources { get; set; }
        public int DeadResources { get; set; }
        public int ActiveEventSubscriptions { get; set; }
        public int DeadEventSubscriptions { get; set; }
    }

    /// <summary>
    /// Memory snapshot for trend analysis.
    /// </summary>
    public class MemorySnapshot
    {
        public DateTime Timestamp { get; set; }
        public double WorkingSetMB { get; set; }
        public double PrivateMemoryMB { get; set; }
        public double VirtualMemoryMB { get; set; }
        public double ManagedMemoryMB { get; set; }
        public int Gen0Collections { get; set; }
        public int Gen1Collections { get; set; }
        public int Gen2Collections { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
    }

    /// <summary>
    /// Memory trend analysis results.
    /// </summary>
    public class MemoryTrendAnalysis
    {
        public DateTime AnalysisStartTime { get; set; }
        public DateTime AnalysisEndTime { get; set; }
        public int SnapshotCount { get; set; }
        public double StartMemoryMB { get; set; }
        public double EndMemoryMB { get; set; }
        public double MinMemoryMB { get; set; }
        public double MaxMemoryMB { get; set; }
        public double AverageMemoryMB { get; set; }
        public double MemoryChangeMB { get; set; }
        public double MemoryChangePercentage { get; set; }
        public MemoryTrendDirection TrendDirection { get; set; }

        /// <summary>
        /// Gets the analysis duration.
        /// </summary>
        public TimeSpan AnalysisDuration => AnalysisEndTime - AnalysisStartTime;
    }

    /// <summary>
    /// Garbage collection analysis results.
    /// </summary>
    public class GarbageCollectionAnalysis
    {
        public DateTime AnalysisStartedAt { get; set; }
        public double MemoryBeforeGC { get; set; }
        public double MemoryAfterGC { get; set; }
        public double MemoryFreedMB { get; set; }
        public double MemoryFreedPercentage { get; set; }
        public long GcDurationMs { get; set; }
        public int Gen0CollectionsBeforeGC { get; set; }
        public int Gen1CollectionsBeforeGC { get; set; }
        public int Gen2CollectionsBeforeGC { get; set; }
        public int Gen0CollectionsAfterGC { get; set; }
        public int Gen1CollectionsAfterGC { get; set; }
        public int Gen2CollectionsAfterGC { get; set; }

        /// <summary>
        /// Gets the number of collections triggered by this analysis.
        /// </summary>
        public int CollectionsTriggered => (Gen0CollectionsAfterGC - Gen0CollectionsBeforeGC) +
                                          (Gen1CollectionsAfterGC - Gen1CollectionsBeforeGC) +
                                          (Gen2CollectionsAfterGC - Gen2CollectionsBeforeGC);
    }

    /// <summary>
    /// Information about memory usage for a specific type.
    /// </summary>
    public class TypeMemoryInfo
    {
        public Type Type { get; set; } = typeof(object);
        public string Category { get; set; } = string.Empty;
        public DateTime FirstTrackedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public int InstanceCount { get; set; }
        public int BaselineInstanceCount { get; set; }
        public int PeakInstanceCount { get; set; }
        public DateTime PeakReachedAt { get; set; }
        public long EstimatedMemoryUsageBytes { get; set; }

        /// <summary>
        /// Gets the estimated memory usage in megabytes.
        /// </summary>
        public double EstimatedMemoryUsageMB => EstimatedMemoryUsageBytes / (1024.0 * 1024.0);

        /// <summary>
        /// Gets the instance count growth percentage from baseline.
        /// </summary>
        public double InstanceGrowthPercentage => BaselineInstanceCount > 0 ? 
            ((double)(InstanceCount - BaselineInstanceCount) / BaselineInstanceCount) * 100 : 0;
    }

    #endregion

    #region Enumerations

    /// <summary>
    /// Types of memory leaks that can be detected.
    /// </summary>
    public enum MemoryLeakType
    {
        Memory_Growth,
        Resource_Leak,
        Event_Subscription_Leak,
        Type_Instance_Leak,
        Collection_Leak,
        Timer_Leak,
        Thread_Leak
    }

    /// <summary>
    /// Severity levels for memory leaks.
    /// </summary>
    public enum LeakSeverity
    {
        None,
        Info,
        Warning,
        Critical
    }

    /// <summary>
    /// Memory trend directions.
    /// </summary>
    public enum MemoryTrendDirection
    {
        Insufficient_Data,
        Stable,
        Increasing,
        Decreasing,
        Rapidly_Increasing,
        Rapidly_Decreasing,
        Error
    }

    #endregion
}
