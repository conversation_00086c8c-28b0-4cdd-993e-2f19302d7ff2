using System;
using System.Globalization;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that provides the appropriate maximum length for activity code fields based on the selected activity type.
    /// Different activity types have different code formats and therefore different maximum lengths.
    /// Returns the appropriate integer value for the MaxLength property of TextBox controls.
    /// </summary>
    public class ActivityCodeMaxLengthConverter : IValueConverter
    {
        /// <summary>
        /// Converts the selected activity type to the appropriate maximum length for activity code input.
        /// </summary>
        /// <param name="value">The selected activity type (MainCommercial, SecondaryCommercial, Craft, Professional)</param>
        /// <param name="targetType">The target type (should be int)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>Maximum length as integer for the activity code field</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var activityType = value?.ToString() ?? "MainCommercial";
                return GetMaxLength(activityType);
            }
            catch (Exception)
            {
                // Default to 6 characters if any error occurs
                return 6;
            }
        }

        /// <summary>
        /// Not implemented for this converter as it's one-way only.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ActivityCodeMaxLengthConverter is a one-way converter.");
        }

        /// <summary>
        /// Determines the maximum length for activity codes based on activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Maximum length for the activity code field</returns>
        private static int GetMaxLength(string activityType)
        {
            return activityType switch
            {
                // Commercial activities use 6-digit codes
                "MainCommercial" => 6,
                "SecondaryCommercial" => 6,
                
                // Craft activities use XX-XX-XXX format (9 characters including dashes)
                "Craft" => 9,
                
                // Professional activities may use different format
                "Professional" => 8,
                
                // Default to 6 for unknown types
                _ => 6
            };
        }

        /// <summary>
        /// Gets the expected format description for activity codes based on activity type.
        /// This can be used for tooltips or help text.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Arabic description of the expected code format</returns>
        public static string GetFormatDescription(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "6 أرقام (مثال: 123456)",
                "SecondaryCommercial" => "6 أرقام (مثال: 123456)",
                "Craft" => "تنسيق XX-XX-XXX (مثال: 01-01-001)",
                "Professional" => "رمز الترخيص المهني",
                _ => "رمز النشاط المطلوب"
            };
        }

        /// <summary>
        /// Gets the input mask or pattern for activity codes based on activity type.
        /// This can be used for input validation or formatting.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Input pattern or mask for the activity code</returns>
        public static string GetInputPattern(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => @"^\d{0,6}$",
                "SecondaryCommercial" => @"^\d{0,6}$",
                "Craft" => @"^\d{0,2}-?\d{0,2}-?\d{0,3}$",
                "Professional" => @"^[A-Za-z0-9]{0,8}$",
                _ => @"^.{0,6}$"
            };
        }
    }
}
