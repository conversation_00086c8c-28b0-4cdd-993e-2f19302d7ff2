using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using UFU2.ViewModels;
using UFU2.Views.Dialogs;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.Common;
using MaterialDesignThemes.Wpf;

namespace UFU2.Views.NewClient
{
    /// <summary>
    /// Interaction logic for NFileCheckView.xaml
    /// A UserControl that provides file check functionality with chip-based selection.
    /// Integrates with UFU2 design system and follows MaterialDesign patterns.
    /// Supports Arabic RTL layout and proper accessibility features.
    /// Uses MVVM data binding with NewClientViewModel for file check management.
    /// 
    /// This component displays file check chips that are dynamically visible based on
    /// the selected activity tab type from NActivityTabView:
    /// - MainCommercial & SecondaryCommercial: CAS, NIF, NIS, RC, DEx
    /// - Craft: CAS, NIF, NIS, ART, DEx  
    /// - Professional: CAS, NIF, NIS, AGR, DEx
    /// 
    /// The component follows UFU2 architectural patterns with:
    /// - MaterialDesign + WPF-UI hybrid framework integration
    /// - Arabic RTL layout support with proper text alignment
    /// - UFU2 design token system integration (ColorTokens.xaml)
    /// - Consistent styling with other UFU2 NewClient components
    /// - MVVM data binding for dynamic visibility and state management
    /// </summary>
    public partial class NFileCheckView : UserControl, IDisposable
    {
        #region Private Fields

        /// <summary>
        /// Cache for frequently accessed values to reduce property access overhead.
        /// </summary>
        private readonly Dictionary<string, object> _cachedValues = new();

        /// <summary>
        /// Flag to track initialization state for performance optimization.
        /// </summary>
        private bool _isInitialized = false;

        /// <summary>
        /// Weak reference to cached ViewModel to avoid repeated DataContext casting.
        /// </summary>
        private WeakReference<NewClientViewModel>? _cachedViewModel;

        /// <summary>
        /// Cache for checkbox states to reduce binding overhead.
        /// </summary>
        private readonly Dictionary<string, bool> _checkboxStateCache = new();

        // OPTIMIZATION: Phase 2B UI optimization fields

        /// <summary>
        /// Pre-computed visibility states for each activity type to avoid converter calls.
        /// </summary>
        private static readonly Dictionary<string, Dictionary<string, Visibility>> _precomputedVisibilityStates = new()
        {
            ["MainCommercial"] = new Dictionary<string, Visibility>
            {
                ["CAS"] = Visibility.Visible,
                ["NIF"] = Visibility.Visible,
                ["NIS"] = Visibility.Visible,
                ["RC"] = Visibility.Visible,
                ["ART"] = Visibility.Collapsed,
                ["AGR"] = Visibility.Collapsed,
                ["DEX"] = Visibility.Visible
            },
            ["SecondaryCommercial"] = new Dictionary<string, Visibility>
            {
                ["CAS"] = Visibility.Visible,
                ["NIF"] = Visibility.Visible,
                ["NIS"] = Visibility.Visible,
                ["RC"] = Visibility.Visible,
                ["ART"] = Visibility.Collapsed,
                ["AGR"] = Visibility.Collapsed,
                ["DEX"] = Visibility.Visible
            },
            ["Craft"] = new Dictionary<string, Visibility>
            {
                ["CAS"] = Visibility.Visible,
                ["NIF"] = Visibility.Visible,
                ["NIS"] = Visibility.Visible,
                ["RC"] = Visibility.Collapsed,
                ["ART"] = Visibility.Visible,
                ["AGR"] = Visibility.Collapsed,
                ["DEX"] = Visibility.Visible
            },
            ["Professional"] = new Dictionary<string, Visibility>
            {
                ["CAS"] = Visibility.Visible,
                ["NIF"] = Visibility.Visible,
                ["NIS"] = Visibility.Visible,
                ["RC"] = Visibility.Collapsed,
                ["ART"] = Visibility.Collapsed,
                ["AGR"] = Visibility.Visible,
                ["DEX"] = Visibility.Visible
            }
        };

        /// <summary>
        /// Visual state transition timer for smooth UI updates.
        /// </summary>
        private readonly DispatcherTimer _visualStateTransitionTimer;

        /// <summary>
        /// Performance monitoring counters.
        /// </summary>
        private static int _activitySwitchCount = 0;
        private static int _optimizedTransitionCount = 0;

        /// <summary>
        /// Current activity type for state tracking.
        /// </summary>
        private string _currentActivityType = "MainCommercial";

        /// <summary>
        /// Transition delay for visual state updates (50ms).
        /// </summary>
        private const int VisualStateTransitionMs = 50;

        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the NFileCheckView class.
        /// Sets up the file check form with proper Arabic RTL support and UFU2 styling.
        /// The DataContext should be set by the parent view (NewClientView) to ensure
        /// proper data sharing between the form fields and activity tab selection.
        /// </summary>
        public NFileCheckView()
        {
            InitializeComponent();

            // OPTIMIZATION: Initialize visual state transition timer
            _visualStateTransitionTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(VisualStateTransitionMs)
            };
            _visualStateTransitionTimer.Tick += VisualStateTransitionTimer_Tick;

            // Performance optimization: Mark as initialized
            _isInitialized = true;

            // Set up optimized activity type change handling
            DataContextChanged += NFileCheckView_DataContextChanged;

            // Note: YearSelectorButton Click handler is defined in XAML
            // Note: DataContext is set by the parent NewClientView to ensure
            // the same ViewModel instance is shared between components for
            // proper activity type-based visibility management
        }
        #endregion

        #region Performance Optimization Methods

        /// <summary>
        /// Gets the cached ViewModel instance to avoid repeated DataContext casting.
        /// Provides significant performance improvement for frequent ViewModel access.
        /// </summary>
        /// <returns>The NewClientViewModel instance or null if not available</returns>
        private NewClientViewModel? GetCachedViewModel()
        {
            // Check if we have a valid cached reference
            if (_cachedViewModel?.TryGetTarget(out var cachedVm) == true)
            {
                return cachedVm;
            }

            // Cache miss - get from DataContext and cache it
            if (DataContext is NewClientViewModel viewModel)
            {
                _cachedViewModel = new WeakReference<NewClientViewModel>(viewModel);
                return viewModel;
            }

            return null;
        }

        /// <summary>
        /// Caches a value to reduce repeated property access overhead.
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        private void CacheValue(string key, object value)
        {
            _cachedValues[key] = value;
        }

        /// <summary>
        /// Gets a cached value if available.
        /// </summary>
        /// <typeparam name="T">Type of the cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached value or default</returns>
        private T? GetCachedValue<T>(string key)
        {
            return _cachedValues.TryGetValue(key, out var value) && value is T typedValue ? typedValue : default;
        }

        /// <summary>
        /// Caches checkbox state to reduce binding overhead.
        /// </summary>
        /// <param name="checkboxName">Name of the checkbox</param>
        /// <param name="isChecked">Checked state</param>
        private void CacheCheckboxState(string checkboxName, bool isChecked)
        {
            _checkboxStateCache[checkboxName] = isChecked;
        }

        /// <summary>
        /// Gets cached checkbox state.
        /// </summary>
        /// <param name="checkboxName">Name of the checkbox</param>
        /// <returns>Cached state or false if not found</returns>
        private bool GetCachedCheckboxState(string checkboxName)
        {
            return _checkboxStateCache.TryGetValue(checkboxName, out var state) && state;
        }

        /// <summary>
        /// Clears all caches to prevent memory leaks.
        /// </summary>
        private void ClearCache()
        {
            _cachedValues.Clear();
            _checkboxStateCache.Clear();
            _cachedViewModel = null;
        }

        #endregion

        #region Event Handlers
        /// <summary>
        /// Handles the YearSelectorButton click event.
        /// Opens the PaymentYearsSelectionDialog if ActivityStartDateTextBox contains a valid date.
        /// Optimized with cached ViewModel access for improved performance.
        /// </summary>
        private async void YearSelectorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("YearSelectorButton_Click: Starting...");

                // Get the cached ViewModel
                var viewModel = GetCachedViewModel();
                if (viewModel == null)
                {
                    System.Diagnostics.Debug.WriteLine("YearSelectorButton_Click: ViewModel not available");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("YearSelectorButton_Click: ViewModel found");

                // Find the ActivityStartDateTextBox in the parent view
                var activityStartDateTextBox = FindActivityStartDateTextBox();
                if (activityStartDateTextBox == null)
                {
                    System.Diagnostics.Debug.WriteLine("YearSelectorButton_Click: ActivityStartDateTextBox not found");
                    return;
                }

                var activityStartDate = activityStartDateTextBox.Text;
                System.Diagnostics.Debug.WriteLine($"YearSelectorButton_Click: ActivityStartDate = '{activityStartDate}'");

                // Validate the activity start date using shared utility
                if (!PaymentYearRangeCalculator.IsValidActivityStartDate(activityStartDate))
                {
                    System.Diagnostics.Debug.WriteLine("YearSelectorButton_Click: Invalid activity start date");

                    // Show toast notification with Arabic warning message (prevent duplicate calls)
                    LoggingService.LogInfo("Showing activity start date validation warning toast", "NFileCheckView");
                    ErrorManager.ShowUserWarningToast(
                        "يجب تحديد بداية النشاط",
                        "بيانات مطلوبة",
                        "NFileCheckView",
                        null,
                        4000
                    );

                    return;
                }

                System.Diagnostics.Debug.WriteLine("YearSelectorButton_Click: Date validation passed, creating dialog");

                // Create and show the dialog
                var dialog = new PaymentYearsSelectionDialog(
                    activityStartDate,
                    viewModel.G12SelectedYears,
                    viewModel.BISSelectedYears);

                System.Diagnostics.Debug.WriteLine("YearSelectorButton_Click: Dialog created, showing...");

                var result = await DialogHost.Show(dialog, "NewClientDialogHost");

                System.Diagnostics.Debug.WriteLine($"YearSelectorButton_Click: Dialog result = {result}");

                // Handle dialog result
                if (result is bool dialogResult && dialogResult && dialog.DialogResult)
                {
                    System.Diagnostics.Debug.WriteLine("YearSelectorButton_Click: Updating ViewModel with selected years");
                    // Update ViewModel with selected years
                    viewModel.G12SelectedYears = dialog.SelectedG12Years;
                    viewModel.BISSelectedYears = dialog.SelectedBISYears;
                }
            }
            catch (System.Exception ex)
            {
                // Log error but don't crash the application
                System.Diagnostics.Debug.WriteLine($"Error opening PaymentYearsSelectionDialog: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handles the AddNotes button click event.
        /// Opens the NoteListDialog for managing client notes.
        /// Optimized with cached ViewModel access for improved performance.
        /// </summary>
        private async void AddNotes_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Disable button during dialog operation to prevent multiple clicks
                AddNotes.IsEnabled = false;

                LoggingService.LogInfo("Opening notes management dialog", "NFileCheckView");

                // Get the cached ViewModel
                var viewModel = GetCachedViewModel();
                if (viewModel == null)
                {
                    LoggingService.LogError("ViewModel not available", "NFileCheckView");
                    return;
                }

                // Create the notes dialog with current notes collection
                var notesDialog = new NoteListDialog(viewModel.Notes);

                // Show the dialog using the UFU2 DialogHost pattern
                var result = await DialogHost.Show(notesDialog, "NewClientDialogHost");

                // Handle dialog result
                if (result is bool dialogResult && dialogResult)
                {
                    LoggingService.LogInfo($"Notes dialog saved successfully. Total notes: {viewModel.Notes.Count}", "NFileCheckView");

                    // Update UI elements that show note count if needed
                    UpdateNotesDisplay();
                }
                else
                {
                    LoggingService.LogInfo("Notes dialog cancelled", "NFileCheckView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening notes dialog: {ex.Message}", "NFileCheckView");

                // TODO: Show user-friendly error message
                // Consider showing a toast notification or message box
            }
            finally
            {
                // Re-enable button after dialog operation
                AddNotes.IsEnabled = true;
            }
        }

        /// <summary>
        /// Updates the notes display elements in the UI.
        /// Triggers property change notification for NotesDisplayText to refresh the note card content.
        /// </summary>
        private void UpdateNotesDisplay()
        {
            try
            {
                if (DataContext is NewClientViewModel viewModel)
                {
                    // Trigger property change notification for NotesDisplayText
                    viewModel.RefreshNotesDisplay();
                    LoggingService.LogInfo($"Notes display updated. Current count: {viewModel.Notes.Count}", "NFileCheckView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating notes display: {ex.Message}", "NFileCheckView");
            }
        }

        /// <summary>
        /// Finds the ActivityStartDateTextBox in the parent view hierarchy.
        /// The ActivityStartDateTextBox is located in the NActivityDetailView component.
        /// </summary>
        /// <returns>The ActivityStartDateTextBox if found, null otherwise</returns>
        private TextBox FindActivityStartDateTextBox()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("FindActivityStartDateTextBox: Starting search...");

                // Navigate up the visual tree to find the parent NewClientView
                DependencyObject parent = this;
                while (parent != null)
                {
                    parent = VisualTreeHelper.GetParent(parent);
                    System.Diagnostics.Debug.WriteLine($"FindActivityStartDateTextBox: Checking parent type: {parent?.GetType().Name}");

                    if (parent is UserControl userControl && userControl.GetType().Name == "NewClientView")
                    {
                        System.Diagnostics.Debug.WriteLine("FindActivityStartDateTextBox: Found NewClientView");

                        // Look for the ActivityDetailView component
                        var activityDetailView = userControl.FindName("ActivityDetailView") as UserControl;
                        if (activityDetailView != null)
                        {
                            System.Diagnostics.Debug.WriteLine("FindActivityStartDateTextBox: Found ActivityDetailView");

                            // Find the ActivityStartDateTextBox within the ActivityDetailView
                            var textBox = activityDetailView.FindName("ActivityStartDateTextBox") as TextBox;
                            if (textBox != null)
                            {
                                System.Diagnostics.Debug.WriteLine("FindActivityStartDateTextBox: Found ActivityStartDateTextBox");
                                return textBox;
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("FindActivityStartDateTextBox: ActivityStartDateTextBox not found in ActivityDetailView");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("FindActivityStartDateTextBox: ActivityDetailView not found");
                        }
                        break;
                    }
                }

                System.Diagnostics.Debug.WriteLine("FindActivityStartDateTextBox: TextBox not found");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FindActivityStartDateTextBox: Error - {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Cleanup and Disposal

        /// <summary>
        /// Disposes of resources and cleans up event handlers to prevent memory leaks.
        /// </summary>
        public void Dispose()
        {
            try
            {
                // Clear caches
                ClearCache();

                // OPTIMIZATION: Clean up visual state transition timer
                _visualStateTransitionTimer?.Stop();

                LoggingService.LogDebug("NFileCheckView disposed successfully", "NFileCheckView");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during NFileCheckView disposal: {ex.Message}", "NFileCheckView");
            }
        }

        #endregion

        #region Optimization Methods

        /// <summary>
        /// Handles DataContext changes to set up optimized activity type monitoring.
        /// OPTIMIZED: Uses PropertyChanged subscription for efficient activity type tracking.
        /// </summary>
        private void NFileCheckView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            try
            {
                // Unsubscribe from old ViewModel
                if (e.OldValue is NewClientViewModel oldViewModel)
                {
                    oldViewModel.PropertyChanged -= ViewModel_PropertyChanged;
                }

                // Subscribe to new ViewModel
                if (e.NewValue is NewClientViewModel newViewModel)
                {
                    newViewModel.PropertyChanged += ViewModel_PropertyChanged;
                    _cachedViewModel = new WeakReference<NewClientViewModel>(newViewModel);

                    // Initialize with current activity type
                    UpdateVisibilityStatesOptimized(newViewModel.SelectedActivityType);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling DataContext change: {ex.Message}", "NFileCheckView");
            }
        }

        /// <summary>
        /// Handles ViewModel property changes with optimized activity type tracking.
        /// OPTIMIZED: Only processes SelectedActivityType changes for better performance.
        /// </summary>
        private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(NewClientViewModel.SelectedActivityType))
            {
                if (sender is NewClientViewModel viewModel)
                {
                    System.Threading.Interlocked.Increment(ref _activitySwitchCount);

                    // Use optimized visibility state update
                    UpdateVisibilityStatesOptimized(viewModel.SelectedActivityType);
                }
            }
        }

        /// <summary>
        /// Updates chip visibility states using pre-computed configurations.
        /// OPTIMIZED: Avoids converter calls and provides smooth transitions.
        /// </summary>
        private void UpdateVisibilityStatesOptimized(string activityType)
        {
            try
            {
                // Skip update if already current
                if (_currentActivityType == activityType)
                {
                    return;
                }

                _currentActivityType = activityType;

                // Start visual state transition timer for smooth updates
                _visualStateTransitionTimer.Stop();
                _visualStateTransitionTimer.Start();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating visibility states: {ex.Message}", "NFileCheckView");
            }
        }

        /// <summary>
        /// Handles the visual state transition timer tick for smooth UI updates.
        /// OPTIMIZED: Batches visibility updates and provides smooth transitions.
        /// </summary>
        private void VisualStateTransitionTimer_Tick(object? sender, EventArgs e)
        {
            _visualStateTransitionTimer.Stop();

            try
            {
                if (_precomputedVisibilityStates.TryGetValue(_currentActivityType, out var visibilityStates))
                {
                    // Update chip visibility using pre-computed states
                    CasChip.Visibility = visibilityStates["CAS"];
                    NifChip.Visibility = visibilityStates["NIF"];
                    NisChip.Visibility = visibilityStates["NIS"];
                    RcChip.Visibility = visibilityStates["RC"];
                    ArtChip.Visibility = visibilityStates["ART"];
                    AgrChip.Visibility = visibilityStates["AGR"];
                    DexChip.Visibility = visibilityStates["DEX"];

                    System.Threading.Interlocked.Increment(ref _optimizedTransitionCount);

                    LoggingService.LogDebug($"Optimized visibility update for activity type: {_currentActivityType}", "NFileCheckView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in visual state transition timer: {ex.Message}", "NFileCheckView");
            }
        }

        /// <summary>
        /// Gets the pre-computed visibility state for a specific file check type and activity type.
        /// Used for external components that need visibility information.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckType">The file check type</param>
        /// <returns>The visibility state</returns>
        public static Visibility GetPrecomputedVisibility(string activityType, string fileCheckType)
        {
            if (_precomputedVisibilityStates.TryGetValue(activityType, out var visibilityStates) &&
                visibilityStates.TryGetValue(fileCheckType, out var visibility))
            {
                return visibility;
            }

            return Visibility.Collapsed; // Safe default
        }

        /// <summary>
        /// Gets performance statistics for the NFileCheckView component.
        /// Used for Phase 2B UI optimization monitoring.
        /// </summary>
        /// <returns>Performance statistics including activity switch counts and optimization effectiveness</returns>
        public static (int ActivitySwitchCount, int OptimizedTransitionCount, double OptimizationEffectiveness) GetPerformanceStats()
        {
            var switchCount = _activitySwitchCount;
            var optimizedCount = _optimizedTransitionCount;
            var optimizationEffectiveness = switchCount > 0 ? (double)optimizedCount / switchCount : 0.0;

            return (switchCount, optimizedCount, optimizationEffectiveness);
        }

        /// <summary>
        /// Resets performance counters for testing and monitoring.
        /// </summary>
        public static void ResetPerformanceCounters()
        {
            System.Threading.Interlocked.Exchange(ref _activitySwitchCount, 0);
            System.Threading.Interlocked.Exchange(ref _optimizedTransitionCount, 0);
        }

        #endregion
    }
}
