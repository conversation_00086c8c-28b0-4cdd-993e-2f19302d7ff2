using System.Collections.Generic;
using System.Threading.Tasks;

namespace UFU2.Services.Interfaces
{
    /// <summary>
    /// Interface for services that support caching functionality.
    /// Provides standardized cache management operations across all cacheable services.
    /// Enhanced with cache coordination and invalidation strategies for Day 3 implementation.
    /// </summary>
    public interface ICacheableService
    {
        /// <summary>
        /// Clears all caches maintained by this service.
        /// </summary>
        void ClearCache();

        /// <summary>
        /// Gets cache statistics for monitoring and debugging.
        /// </summary>
        /// <returns>Dictionary containing cache performance metrics</returns>
        Dictionary<string, object> GetCacheStatistics();

        /// <summary>
        /// Gets the service name for cache management identification.
        /// </summary>
        string ServiceName { get; }

        /// <summary>
        /// Warms up the cache by preloading critical data.
        /// This method should be called during application startup or after cache invalidation.
        /// </summary>
        /// <returns>Task representing the cache warming operation</returns>
        Task WarmupCacheAsync();

        /// <summary>
        /// Invalidates specific cache entries based on data change events.
        /// </summary>
        /// <param name="invalidationContext">Context information about what data changed</param>
        void InvalidateCache(CacheInvalidationContext invalidationContext);

        /// <summary>
        /// Gets cache health information for monitoring.
        /// </summary>
        /// <returns>Cache health metrics</returns>
        CacheHealthInfo GetCacheHealth();
    }

    /// <summary>
    /// Context information for cache invalidation operations.
    /// </summary>
    public class CacheInvalidationContext
    {
        /// <summary>
        /// Type of data that changed (e.g., "ActivityType", "ValidationRule", "FileCheckRule")
        /// </summary>
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// Specific identifier of the changed data (e.g., activity code, rule ID)
        /// </summary>
        public string? DataId { get; set; }

        /// <summary>
        /// Type of change operation (Create, Update, Delete)
        /// </summary>
        public CacheInvalidationType InvalidationType { get; set; }

        /// <summary>
        /// Additional context information for invalidation decisions
        /// </summary>
        public Dictionary<string, object> AdditionalContext { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Types of cache invalidation operations.
    /// </summary>
    public enum CacheInvalidationType
    {
        /// <summary>
        /// Data was created - may need to invalidate list caches
        /// </summary>
        Create,

        /// <summary>
        /// Data was updated - may need to invalidate specific item and list caches
        /// </summary>
        Update,

        /// <summary>
        /// Data was deleted - may need to invalidate specific item and list caches
        /// </summary>
        Delete,

        /// <summary>
        /// Full invalidation - clear all related caches
        /// </summary>
        Full
    }

    /// <summary>
    /// Cache health information for monitoring.
    /// </summary>
    public class CacheHealthInfo
    {
        /// <summary>
        /// Overall cache hit ratio (0.0 to 1.0)
        /// </summary>
        public double HitRatio { get; set; }

        /// <summary>
        /// Number of items currently in cache
        /// </summary>
        public int ItemCount { get; set; }

        /// <summary>
        /// Estimated memory usage in bytes
        /// </summary>
        public long MemoryUsageBytes { get; set; }

        /// <summary>
        /// Whether the cache is healthy (hit ratio above threshold, memory usage reasonable)
        /// </summary>
        public bool IsHealthy { get; set; }

        /// <summary>
        /// Health status message
        /// </summary>
        public string HealthStatus { get; set; } = string.Empty;

        /// <summary>
        /// Last cache warmup time
        /// </summary>
        public DateTime? LastWarmupTime { get; set; }
    }
}
