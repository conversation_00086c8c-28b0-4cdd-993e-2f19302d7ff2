using System;

namespace UFU2.Common
{
    /// <summary>
    /// Centralized repository for all validation error messages in Arabic.
    /// Provides consistent Arabic error messages across the UFU2 application.
    /// Supports RTL text flow and maintains localization standards.
    /// 
    /// IMPLEMENTATION STATUS: ✅ CREATED - Centralized validation messages (Task 3.1)
    /// INTEGRATION: Used by ValidationService for consistent Arabic error messaging
    /// </summary>
    public static class ValidationMessages
    {
        #region Required Field Messages

        /// <summary>
        /// Generic required field message.
        /// </summary>
        public const string RequiredField = "هذا الحقل مطلوب";

        /// <summary>
        /// Note content is required.
        /// </summary>
        public const string NoteContentRequired = "محتوى الملاحظة مطلوب";

        /// <summary>
        /// Note content cannot be empty.
        /// </summary>
        public const string NoteContentEmpty = "محتوى الملاحظة لا يمكن أن يكون فارغاً";

        /// <summary>
        /// Activity description is required.
        /// </summary>
        public const string ActivityDescriptionRequired = "وصف النشاط مطلوب";

        /// <summary>
        /// Update date is required.
        /// </summary>
        public const string UpdateDateRequired = "تاريخ التحديث مطلوب";

        /// <summary>
        /// Name in Latin is required.
        /// </summary>
        public const string NameFrRequired = "الاسم باللاتينية مطلوب";

        #endregion

        #region Length Validation Messages

        /// <summary>
        /// Generic text too long message with placeholder for limit.
        /// Usage: string.Format(ValidationMessages.TextTooLong, maxLength)
        /// </summary>
        public const string TextTooLong = "النص طويل جداً (الحد الأقصى {0} حرف)";

        /// <summary>
        /// Generic text too short message with placeholder for minimum.
        /// Usage: string.Format(ValidationMessages.TextTooShort, minLength)
        /// </summary>
        public const string TextTooShort = "النص قصير جداً (الحد الأدنى {0} حرف)";

        /// <summary>
        /// Activity description must be less than 500 characters.
        /// </summary>
        public const string ActivityDescriptionTooLong = "وصف النشاط يجب أن يكون أقل من 500 حرف";

        /// <summary>
        /// Note cannot exceed 500 characters.
        /// </summary>
        public const string NoteTooLong = "الملاحظة لا يمكن أن تتجاوز 500 حرف";

        /// <summary>
        /// Name must contain at least 2 characters.
        /// </summary>
        public const string NameTooShort = "الاسم يجب أن يحتوي على حرفين على الأقل";

        /// <summary>
        /// Name is too long (maximum 100 characters).
        /// </summary>
        public const string NameTooLong = "الاسم طويل جداً (الحد الأقصى 100 حرف)";

        #endregion

        #region Format Validation Messages

        /// <summary>
        /// Invalid phone number format.
        /// </summary>
        public const string InvalidPhoneNumber = "رقم هاتف غير صحيح";

        /// <summary>
        /// Phone number is too long.
        /// </summary>
        public const string PhoneNumberTooLong = "رقم الهاتف طويل جداً";

        /// <summary>
        /// Invalid date format.
        /// </summary>
        public const string InvalidDate = "التاريخ المدخل غير صحيح";

        /// <summary>
        /// Date format must be DD/MM/YYYY, xx/xx/yyyy, or xx/xx/xxxx.
        /// Enhanced to support flexible date formats including partial dates with known year.
        /// </summary>
        public const string InvalidDateFormat = "تنسيق التاريخ يجب أن يكون DD/MM/YYYY أو xx/xx/yyyy أو xx/xx/xxxx";

        /// <summary>
        /// Name must contain only Latin characters.
        /// </summary>
        public const string NameMustBeLatinOnly = "الاسم يجب أن يحتوي على أحرف لاتينية فقط";

        #endregion

        #region Business Rule Messages

        /// <summary>
        /// Duplicate phone number not allowed.
        /// </summary>
        public const string DuplicatePhoneNumber = "رقم الهاتف موجود مسبقاً";

        /// <summary>
        /// Invalid update date.
        /// </summary>
        public const string InvalidUpdateDate = "تاريخ التحديث غير صحيح";

        /// <summary>
        /// Invalid update note.
        /// </summary>
        public const string InvalidUpdateNote = "ملاحظة التحديث غير صحيحة";

        /// <summary>
        /// Only one primary phone number allowed.
        /// </summary>
        public const string OnlyOnePrimaryPhone = "يمكن تحديد هاتف واحد فقط كهاتف أساسي";

        /// <summary>
        /// Invalid activity type.
        /// </summary>
        public const string InvalidActivityType = "نوع النشاط غير صحيح";

        /// <summary>
        /// Invalid phone type.
        /// </summary>
        public const string InvalidPhoneType = "نوع الهاتف غير صحيح";

        /// <summary>
        /// Invalid gender value.
        /// </summary>
        public const string InvalidGender = "الجنس يجب أن يكون ذكر (0) أو أنثى (1)";

        /// <summary>
        /// Invalid national ID format.
        /// </summary>
        public const string InvalidNationalId = "رقم الهوية الوطنية يجب أن يكون 18 رقم";

        /// <summary>
        /// Invalid file check type.
        /// </summary>
        public const string InvalidFileCheckType = "نوع فحص الملف غير صحيح";

        /// <summary>
        /// Invalid priority value.
        /// </summary>
        public const string InvalidPriority = "مستوى الأولوية يجب أن يكون 0 (عادي)، 1 (متوسط)، أو 2 (عالي)";

        /// <summary>
        /// Invalid year value.
        /// </summary>
        public const string InvalidYear = "السنة غير صحيحة";

        /// <summary>
        /// Duplicate year value.
        /// </summary>
        public const string DuplicateYear = "السنة مكررة";

        /// <summary>
        /// Activity codes must be positive numbers.
        /// </summary>
        public const string ActivityCodesMustBePositive = "رموز الأنشطة يجب أن تكون أرقام موجبة";

        #endregion

        #region System Error Messages

        /// <summary>
        /// Generic validation error occurred.
        /// </summary>
        public const string ValidationError = "حدث خطأ أثناء التحقق من البيانات";

        /// <summary>
        /// Invalid model data.
        /// </summary>
        public const string InvalidModelData = "بيانات النموذج غير صحيحة";

        /// <summary>
        /// Error validating name.
        /// </summary>
        public const string NameValidationError = "حدث خطأ أثناء التحقق من الاسم";

        /// <summary>
        /// Error validating phone number.
        /// </summary>
        public const string PhoneValidationError = "حدث خطأ أثناء التحقق من رقم الهاتف";

        #endregion

        #region Helper Methods

        /// <summary>
        /// Formats a text length error message with the specified limit.
        /// </summary>
        /// <param name="maxLength">Maximum allowed length</param>
        /// <returns>Formatted Arabic error message</returns>
        public static string FormatTextTooLong(int maxLength)
        {
            return string.Format(TextTooLong, maxLength);
        }

        /// <summary>
        /// Formats a text minimum length error message with the specified minimum.
        /// </summary>
        /// <param name="minLength">Minimum required length</param>
        /// <returns>Formatted Arabic error message</returns>
        public static string FormatTextTooShort(int minLength)
        {
            return string.Format(TextTooShort, minLength);
        }

        #endregion
    }
}
