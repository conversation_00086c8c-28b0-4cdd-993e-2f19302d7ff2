<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!--  Enhanced Slider Button Style  -->
    <Style x:Key="UFU2SliderButtonStyle" TargetType="{x:Type RepeatButton}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Border Background="Transparent" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Enhanced Slider Thumb Style  -->
    <Style x:Key="UFU2SliderThumbStyle" TargetType="{x:Type Thumb}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="Height" Value="24" />
        <Setter Property="Width" Value="24" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Grid>
                        <!--  Main Thumb  -->
                        <Ellipse
                            x:Name="ThumbEllipse"
                            Width="15"
                            Height="15"
                            Fill="{DynamicResource SliderThumbBackground}"
                            Stroke="{DynamicResource SliderThumbBorder}"
                            StrokeThickness="2" />

                        <!--  Focus Glow Effect  -->
                        <Ellipse
                            x:Name="FocusGlow"
                            Width="32"
                            Height="32"
                            Fill="{DynamicResource SliderFocusGlow}"
                            Opacity="0" />
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!--  Hover State  -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ThumbEllipse" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.1" ScaleY="1.1" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="ThumbEllipse" Property="RenderTransformOrigin" Value="0.5,0.5" />
                        </Trigger>

                        <!--  Pressed State  -->
                        <Trigger Property="IsDragging" Value="True">
                            <Setter TargetName="ThumbEllipse" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.2" ScaleY="1.2" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="ThumbEllipse" Property="RenderTransformOrigin" Value="0.5,0.5" />
                        </Trigger>

                        <!--  Focus State  -->
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="FocusGlow" Property="Opacity" Value="0.3" />
                        </Trigger>

                        <!--  Disabled State  -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ThumbEllipse" Property="Fill" Value="{DynamicResource TextFillColorDisabledBrush}" />
                            <Setter TargetName="ThumbEllipse" Property="Stroke" Value="{DynamicResource TextFillColorDisabledBrush}" />
                            <Setter TargetName="ThumbEllipse" Property="Opacity" Value="0.6" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Enhanced Horizontal Slider Template  -->
    <ControlTemplate x:Key="UFU2HorizontalSlider" TargetType="{x:Type Slider}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" MinHeight="{TemplateBinding MinHeight}" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  Top Tick Bar  -->
            <TickBar
                x:Name="TopTick"
                Height="6"
                Fill="{DynamicResource SliderTickMarkBrush}"
                Placement="Top"
                SnapsToDevicePixels="True"
                Visibility="Collapsed" />

            <!--  Enhanced Track Background with 8px thickness  -->
            <Border
                x:Name="TrackBackground"
                Grid.Row="1"
                Height="5"
                Margin="0"
                Background="{DynamicResource SliderTrackBackground}"
                BorderBrush="{DynamicResource SliderTrackBorder}"
                BorderThickness="1"
                CornerRadius="2" />

            <!--  Track  -->
            <Track x:Name="PART_Track" Grid.Row="1">
                <Track.DecreaseRepeatButton>
                    <RepeatButton Command="Slider.DecreaseLarge" Style="{StaticResource UFU2SliderButtonStyle}" />
                </Track.DecreaseRepeatButton>
                <Track.Thumb>
                    <Thumb Style="{StaticResource UFU2SliderThumbStyle}" />
                </Track.Thumb>
                <Track.IncreaseRepeatButton>
                    <RepeatButton Command="Slider.IncreaseLarge" Style="{StaticResource UFU2SliderButtonStyle}" />
                </Track.IncreaseRepeatButton>
            </Track>

            <!--  Bottom Tick Bar  -->
            <TickBar
                x:Name="BottomTick"
                Grid.Row="2"
                Height="6"
                Fill="{DynamicResource SliderTickMarkBrush}"
                Placement="Bottom"
                SnapsToDevicePixels="True"
                Visibility="Collapsed" />
        </Grid>

        <ControlTemplate.Triggers>
            <Trigger Property="TickPlacement" Value="TopLeft">
                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="TickPlacement" Value="BottomRight">
                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="TickPlacement" Value="Both">
                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="TrackBackground" Property="Background" Value="{DynamicResource TextFillColorDisabledBrush}" />
                <Setter TargetName="TrackBackground" Property="BorderBrush" Value="{DynamicResource TextFillColorDisabledBrush}" />
                <Setter TargetName="TrackBackground" Property="Opacity" Value="0.4" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  Enhanced Vertical Slider Template  -->
    <ControlTemplate x:Key="UFU2VerticalSlider" TargetType="{x:Type Slider}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" MinWidth="{TemplateBinding MinWidth}" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  Left Tick Bar  -->
            <TickBar
                x:Name="TopTick"
                Width="6"
                Fill="{DynamicResource SliderTickMarkBrush}"
                Placement="Left"
                SnapsToDevicePixels="True"
                Visibility="Collapsed" />

            <!--  Enhanced Track Background with 8px thickness  -->
            <Border
                x:Name="TrackBackground"
                Grid.Column="1"
                Width="5"
                Margin="0"
                Background="{DynamicResource SliderTrackBackground}"
                BorderBrush="{DynamicResource SliderTrackBorder}"
                BorderThickness="1"
                CornerRadius="2" />

            <!--  Track  -->
            <Track x:Name="PART_Track" Grid.Column="1">
                <Track.DecreaseRepeatButton>
                    <RepeatButton Command="Slider.DecreaseLarge" Style="{StaticResource UFU2SliderButtonStyle}" />
                </Track.DecreaseRepeatButton>
                <Track.Thumb>
                    <Thumb Style="{StaticResource UFU2SliderThumbStyle}" />
                </Track.Thumb>
                <Track.IncreaseRepeatButton>
                    <RepeatButton Command="Slider.IncreaseLarge" Style="{StaticResource UFU2SliderButtonStyle}" />
                </Track.IncreaseRepeatButton>
            </Track>

            <!--  Right Tick Bar  -->
            <TickBar
                x:Name="BottomTick"
                Grid.Column="2"
                Width="6"
                Fill="{DynamicResource SliderTickMarkBrush}"
                Placement="Right"
                SnapsToDevicePixels="True"
                Visibility="Collapsed" />
        </Grid>

        <ControlTemplate.Triggers>
            <Trigger Property="TickPlacement" Value="TopLeft">
                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="TickPlacement" Value="BottomRight">
                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="TickPlacement" Value="Both">
                <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
                <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="TrackBackground" Property="Background" Value="{DynamicResource TextFillColorDisabledBrush}" />
                <Setter TargetName="TrackBackground" Property="BorderBrush" Value="{DynamicResource TextFillColorDisabledBrush}" />
                <Setter TargetName="TrackBackground" Property="Opacity" Value="0.4" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  Enhanced UFU2 Slider Style  -->
    <Style x:Key="UFU2EnhancedSliderStyle" TargetType="{x:Type Slider}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="IsTabStop" Value="True" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="materialDesign:SliderAssist.FocusSliderOnClick" Value="False" />
        <Setter Property="materialDesign:SliderAssist.OnlyShowFocusVisualWhileDragging" Value="True" />

        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="MinWidth" Value="104" />
                <Setter Property="MinHeight" Value="32" />
                <Setter Property="Template" Value="{StaticResource UFU2HorizontalSlider}" />
            </Trigger>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="MinWidth" Value="32" />
                <Setter Property="MinHeight" Value="104" />
                <Setter Property="Template" Value="{StaticResource UFU2VerticalSlider}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  Default Slider Style Override  -->
    <Style BasedOn="{StaticResource UFU2EnhancedSliderStyle}" TargetType="{x:Type Slider}" />

</ResourceDictionary>