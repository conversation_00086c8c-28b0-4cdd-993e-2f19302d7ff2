# UFU2 Refactoring Implementation Guide

## Quick Reference

### Files Requiring Immediate Refactoring (>1000 lines)

1. **ImageManagementViewModel.cs** (2,811 lines) → Target: ~300 lines
2. **ClientDatabaseService.cs** (2,093 lines) → Target: ~400 lines  
3. **BaseViewModel.cs** (1,801 lines) → Target: ~300 lines
4. **DatabaseMigrationService.cs** (1,382 lines) → Target: ~300 lines
5. **NewClientViewModel.cs** (1,280 lines) → Target: ~400 lines
6. **ActivityManagementViewModel.cs** (1,229 lines) → Target: ~400 lines
7. **WindowChromeService.cs** (1,146 lines) → Target: ~300 lines

## Implementation Priority

### Phase 1 (Weeks 1-2): Critical Infrastructure
- ImageManagementViewModel.cs - Extract image processing services
- ClientDatabaseService.cs - Split into specialized database services

### Phase 2 (Weeks 3-4): Foundation Components  
- BaseViewModel.cs - Extract property batching and performance monitoring
- NewClientViewModel.cs - Extract command handling and validation

### Phase 3 (Weeks 5-6): Supporting Services
- ActivityManagementViewModel.cs - Extract tab and location management
- DatabaseMigrationService.cs - Split schema and migration logic
- WindowChromeService.cs - Extract chrome rendering components

## Refactoring Strategy

### For Each File:
1. **Identify Concerns** - List all responsibilities in the file
2. **Extract Services** - Move business logic to dedicated services
3. **Extract ViewModels** - Split complex ViewModels into focused components
4. **Refactor Core** - Keep only orchestration logic in original file
5. **Update ServiceLocator** - Register new services
6. **Test & Validate** - Ensure functionality is preserved

## Success Criteria

- All files under 1000 lines
- Maintained functionality
- No performance degradation
- Improved maintainability
- Better separation of concerns

## Monitoring

Use this PowerShell script to check compliance:

```powershell
Get-ChildItem -Path . -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -notlike "*\obj\*" 
} | ForEach-Object { 
    $lineCount = (Get-Content $_.FullName | Measure-Object -Line).Lines
    if ($lineCount -gt 1000) {
        Write-Host "$($_.Name): $lineCount lines - NEEDS REFACTORING" -ForegroundColor Red
    } elseif ($lineCount -gt 500) {
        Write-Host "$($_.Name): $lineCount lines - Review recommended" -ForegroundColor Yellow
    }
}
```

## Next Steps

1. Review analysis with development team
2. Allocate resources for refactoring sprint
3. Begin with highest priority files
4. Implement automated line count monitoring
5. Establish refactoring standards based on SPARC methodology