using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Comprehensive validation service for client management operations.
    /// Provides application-level validation logic with Arabic error messages.
    /// Implements activity type-specific validation and business rule enforcement.
    /// Integrates with UFU2's existing ValidationService and error handling patterns.
    /// 
    /// IMPLEMENTATION STATUS: ✅ CREATED - Client validation service (Task 5)
    /// INTEGRATION: Uses existing ValidationService and ValidationMessages
    /// REQUIREMENTS: 8.1, 8.2, 8.3, 8.4, 8.5, 5.4
    /// </summary>
    public class ClientValidationService
    {
        #region Private Fields

        private readonly ValidationService _validationService;
        private readonly FileCheckBusinessRuleService _fileCheckBusinessRuleService;
        private static readonly Regex PhoneDigitsRegex = new Regex(@"\D", RegexOptions.Compiled);

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ClientValidationService class.
        /// </summary>
        public ClientValidationService()
        {
            _validationService = new ValidationService();
            var databaseService = ServiceLocator.GetService<DatabaseService>();
            _fileCheckBusinessRuleService = new FileCheckBusinessRuleService(databaseService);
        }

        #endregion

        #region Client Validation

        /// <summary>
        /// Validates client creation data according to UFU2 business rules.
        /// Performs comprehensive validation including required fields, formats, and business rules.
        /// </summary>
        /// <param name="clientData">The client creation data to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateClientCreation(ClientCreationData clientData)
        {
            var result = new ClientValidationResult();

            try
            {
                if (clientData == null)
                {
                    result.AddGeneralError("بيانات العميل مطلوبة");
                    return result;
                }

                // Validate required French name
                var nameError = _validationService.ValidateLatinName(clientData.NameFr, true);
                if (nameError != null)
                {
                    result.AddError("NameFr", nameError);
                }

                // Validate optional Arabic name length
                if (!string.IsNullOrWhiteSpace(clientData.NameAr))
                {
                    var arabicNameError = _validationService.ValidateMaxLength(clientData.NameAr, 100, "NameAr");
                    if (arabicNameError != null)
                    {
                        result.AddError("NameAr", arabicNameError);
                    }
                }

                // BirthDate validation removed as per database binding requirements
                // BirthDate should accept any format including partial dates like "xx/xx/1993"

                // Validate optional birth place
                if (!string.IsNullOrWhiteSpace(clientData.BirthPlace))
                {
                    var birthPlaceError = _validationService.ValidateMaxLength(clientData.BirthPlace, 200, "BirthPlace");
                    if (birthPlaceError != null)
                    {
                        result.AddError("BirthPlace", birthPlaceError);
                    }
                }

                // Validate optional gender
                if (clientData.Gender.HasValue && (clientData.Gender < 0 || clientData.Gender > 1))
                {
                    result.AddError("Gender", ValidationMessages.InvalidGender);
                }

                // Validate optional address
                if (!string.IsNullOrWhiteSpace(clientData.Address))
                {
                    var addressError = _validationService.ValidateMaxLength(clientData.Address, 500, "Address");
                    if (addressError != null)
                    {
                        result.AddError("Address", addressError);
                    }
                }

                // NationalId validation removed as per database binding requirements
                // NationalId should accept any number without digit count restrictions

                // Validate phone numbers
                if (clientData.PhoneNumbers != null && clientData.PhoneNumbers.Any())
                {
                    var phoneValidation = ValidatePhoneNumbers(clientData.PhoneNumbers);
                    if (!phoneValidation.IsValid)
                    {
                        result.Merge(phoneValidation);
                    }
                }

                // Validate activities
                if (clientData.Activities != null && clientData.Activities.Any())
                {
                    var activitiesValidation = ValidateActivities(clientData.Activities);
                    if (!activitiesValidation.IsValid)
                    {
                        result.Merge(activitiesValidation);
                    }
                }

                LoggingService.LogInfo($"Client validation completed. Valid: {result.IsValid}, Errors: {result.ErrorCount}", "ClientValidationService");
                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating client creation: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من بيانات العميل");
                return result;
            }
        }

        /// <summary>
        /// Validates client update data according to UFU2 business rules.
        /// Similar to creation validation but excludes required field checks for optional updates.
        /// </summary>
        /// <param name="updateData">The client update data to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateClientUpdate(ClientUpdateData updateData)
        {
            var result = new ClientValidationResult();

            try
            {
                if (updateData == null)
                {
                    result.AddGeneralError("بيانات تحديث العميل مطلوبة");
                    return result;
                }

                // Validate optional Arabic name
                if (!string.IsNullOrWhiteSpace(updateData.NameAr))
                {
                    var arabicNameError = _validationService.ValidateMaxLength(updateData.NameAr, 100, "NameAr");
                    if (arabicNameError != null)
                    {
                        result.AddError("NameAr", arabicNameError);
                    }
                }

                // Validate optional birth date
                if (!string.IsNullOrWhiteSpace(updateData.BirthDate))
                {
                    var birthDateError = _validationService.ValidateDate(updateData.BirthDate, false);
                    if (birthDateError != null)
                    {
                        result.AddError("BirthDate", birthDateError);
                    }
                }

                // Validate optional birth place
                if (!string.IsNullOrWhiteSpace(updateData.BirthPlace))
                {
                    var birthPlaceError = _validationService.ValidateMaxLength(updateData.BirthPlace, 200, "BirthPlace");
                    if (birthPlaceError != null)
                    {
                        result.AddError("BirthPlace", birthPlaceError);
                    }
                }

                // Validate optional gender
                if (updateData.Gender.HasValue && (updateData.Gender < 0 || updateData.Gender > 1))
                {
                    result.AddError("Gender", ValidationMessages.InvalidGender);
                }

                // Validate optional address
                if (!string.IsNullOrWhiteSpace(updateData.Address))
                {
                    var addressError = _validationService.ValidateMaxLength(updateData.Address, 500, "Address");
                    if (addressError != null)
                    {
                        result.AddError("Address", addressError);
                    }
                }

                // Validate optional national ID
                if (!string.IsNullOrWhiteSpace(updateData.NationalId))
                {
                    var nationalIdValidation = ValidateNationalId(updateData.NationalId);
                    if (!nationalIdValidation.IsValid)
                    {
                        result.Merge(nationalIdValidation);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating client update: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من بيانات تحديث العميل");
                return result;
            }
        }

        #endregion

        #region Activity Validation

        /// <summary>
        /// Validates activity creation data with activity type-specific business rules.
        /// Enforces file check requirements based on activity type.
        /// </summary>
        /// <param name="activityData">The activity creation data to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateActivity(ActivityCreationData activityData)
        {
            var result = new ClientValidationResult();

            try
            {
                if (activityData == null)
                {
                    result.AddGeneralError("بيانات النشاط مطلوبة");
                    return result;
                }

                // Validate activity type if provided
                if (!string.IsNullOrWhiteSpace(activityData.ActivityType))
                {
                    var validActivityTypes = new[] { "MainCommercial", "SecondaryCommercial", "Craft", "Professional" };
                    if (!validActivityTypes.Contains(activityData.ActivityType))
                    {
                        result.AddError("ActivityType", ValidationMessages.InvalidActivityType);
                    }
                    else
                    {
                        // Validate activity type-specific requirements
                        var activityTypeValidation = ValidateActivityTypeSpecificData(activityData);
                        if (!activityTypeValidation.IsValid)
                        {
                            result.Merge(activityTypeValidation);
                        }
                    }
                }

                // Validate optional dates
                if (!string.IsNullOrWhiteSpace(activityData.ActivityStartDate))
                {
                    var startDateError = _validationService.ValidateDate(activityData.ActivityStartDate, false);
                    if (startDateError != null)
                    {
                        result.AddError("ActivityStartDate", startDateError);
                    }
                }

                if (!string.IsNullOrWhiteSpace(activityData.ActivityUpdateDate))
                {
                    var updateDateError = _validationService.ValidateDate(activityData.ActivityUpdateDate, false);
                    if (updateDateError != null)
                    {
                        result.AddError("ActivityUpdateDate", updateDateError);
                    }
                }

                // Validate optional text fields
                var textFieldValidations = new[]
                {
                    ("CommercialRegister", activityData.CommercialRegister, 50),
                    ("ActivityLocation", activityData.ActivityLocation, 500),
                    ("NifNumber", activityData.NifNumber, 50),
                    ("NisNumber", activityData.NisNumber, 50),
                    ("ArtNumber", activityData.ArtNumber, 50),
                    ("CpiDaira", activityData.CpiDaira, 100),
                    ("CpiWilaya", activityData.CpiWilaya, 100),
                    ("ActivityUpdateNote", activityData.ActivityUpdateNote, 500),
                    ("ActivityDescription", activityData.ActivityDescription, 500)
                };

                foreach (var (fieldName, value, maxLength) in textFieldValidations)
                {
                    if (!string.IsNullOrWhiteSpace(value))
                    {
                        var lengthError = _validationService.ValidateMaxLength(value, maxLength, fieldName);
                        if (lengthError != null)
                        {
                            result.AddError(fieldName, lengthError);
                        }
                    }
                }

                // Validate file check states
                if (activityData.FileCheckStates != null && activityData.FileCheckStates.Any())
                {
                    var fileCheckValidation = ValidateFileCheckStates(activityData.FileCheckStates, activityData.ActivityType);
                    if (!fileCheckValidation.IsValid)
                    {
                        result.Merge(fileCheckValidation);
                    }
                }

                // Validate payment years
                if (activityData.G12CheckYears != null && activityData.G12CheckYears.Any())
                {
                    var g12Validation = ValidatePaymentYears(activityData.G12CheckYears, "G12");
                    if (!g12Validation.IsValid)
                    {
                        result.Merge(g12Validation);
                    }
                }

                if (activityData.BisCheckYears != null && activityData.BisCheckYears.Any())
                {
                    var bisValidation = ValidatePaymentYears(activityData.BisCheckYears, "BIS");
                    if (!bisValidation.IsValid)
                    {
                        result.Merge(bisValidation);
                    }
                }

                // Validate notes
                if (activityData.Notes != null && activityData.Notes.Any())
                {
                    var notesValidation = ValidateNotes(activityData.Notes);
                    if (!notesValidation.IsValid)
                    {
                        result.Merge(notesValidation);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating activity: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من بيانات النشاط");
                return result;
            }
        }

        /// <summary>
        /// Validates multiple activities for a client.
        /// </summary>
        /// <param name="activities">The activities to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateActivities(List<ActivityCreationData> activities)
        {
            var result = new ClientValidationResult();

            try
            {
                if (activities == null || !activities.Any())
                {
                    return result; // No activities to validate
                }

                for (int i = 0; i < activities.Count; i++)
                {
                    var activityValidation = ValidateActivity(activities[i]);
                    if (!activityValidation.IsValid)
                    {
                        // Prefix field names with activity index
                        foreach (var error in activityValidation.Errors)
                        {
                            foreach (var message in error.Value)
                            {
                                result.AddError($"Activity[{i}].{error.Key}", message);
                            }
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating activities: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من بيانات الأنشطة");
                return result;
            }
        }

        #endregion

        #region Phone Number Validation

        /// <summary>
        /// Validates phone numbers with format checking and uniqueness validation.
        /// Enforces business rule that only one phone can be marked as primary.
        /// </summary>
        /// <param name="phoneNumbers">The phone numbers to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidatePhoneNumbers(List<PhoneNumberData> phoneNumbers)
        {
            var result = new ClientValidationResult();

            try
            {
                if (phoneNumbers == null || !phoneNumbers.Any())
                {
                    return result; // No phone numbers to validate
                }

                var primaryPhoneCount = 0;
                var seenPhoneNumbers = new HashSet<string>();

                for (int i = 0; i < phoneNumbers.Count; i++)
                {
                    var phone = phoneNumbers[i];
                    var phonePrefix = $"PhoneNumber[{i}]";

                    // Validate phone number format
                    var formatError = _validationService.ValidatePhoneNumber(phone.PhoneNumber, true);
                    if (formatError != null)
                    {
                        result.AddError($"{phonePrefix}.PhoneNumber", formatError);
                    }

                    // Validate phone type
                    var validPhoneTypes = new[] { "Mobile", "Home", "Work", "Fax" };
                    if (!validPhoneTypes.Contains(phone.PhoneType))
                    {
                        result.AddError($"{phonePrefix}.PhoneType", ValidationMessages.InvalidPhoneType);
                    }

                    // Check for primary phone count
                    if (phone.IsPrimary)
                    {
                        primaryPhoneCount++;
                    }

                    // Check for duplicate phone numbers
                    if (!string.IsNullOrWhiteSpace(phone.PhoneNumber))
                    {
                        var normalizedPhone = PhoneDigitsRegex.Replace(phone.PhoneNumber, "");
                        if (seenPhoneNumbers.Contains(normalizedPhone))
                        {
                            result.AddError($"{phonePrefix}.PhoneNumber", ValidationMessages.DuplicatePhoneNumber);
                        }
                        else
                        {
                            seenPhoneNumbers.Add(normalizedPhone);
                        }
                    }
                }

                // Validate primary phone business rule
                if (primaryPhoneCount > 1)
                {
                    result.AddError("PhoneNumbers", ValidationMessages.OnlyOnePrimaryPhone);
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating phone numbers: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من أرقام الهواتف");
                return result;
            }
        }

        /// <summary>
        /// Validates a single phone number with uniqueness checking against existing numbers.
        /// </summary>
        /// <param name="phoneNumber">The phone number to validate</param>
        /// <param name="existingPhoneNumbers">Existing phone numbers for uniqueness checking</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidatePhoneNumberUniqueness(string phoneNumber, IEnumerable<string> existingPhoneNumbers)
        {
            var result = new ClientValidationResult();

            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber))
                {
                    return result; // Empty phone numbers are handled by required validation
                }

                var duplicateError = _validationService.ValidatePhoneNumberDuplicate(phoneNumber, existingPhoneNumbers);
                if (duplicateError != null)
                {
                    result.AddError("PhoneNumber", duplicateError);
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating phone number uniqueness: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من تفرد رقم الهاتف");
                return result;
            }
        }

        #endregion

        #region Activity Type-Specific Validation

        /// <summary>
        /// Validates activity type-specific data and requirements.
        /// Enforces business rules for different activity types.
        /// </summary>
        /// <param name="activityData">The activity data to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        private ClientValidationResult ValidateActivityTypeSpecificData(ActivityCreationData activityData)
        {
            var result = new ClientValidationResult();

            try
            {
                switch (activityData.ActivityType)
                {
                    case "MainCommercial":
                    case "SecondaryCommercial":
                        // Commercial activities should have activity codes
                        if (activityData.ActivityCodes == null || !activityData.ActivityCodes.Any())
                        {
                            // Allow empty codes for flexible data entry but log warning
                            LoggingService.LogWarning($"Commercial activity without activity codes", "ClientValidationService");
                        }
                        else
                        {
                            // Validate activity codes are positive integers
                            foreach (var code in activityData.ActivityCodes)
                            {
                                if (code <= 0)
                                {
                                    result.AddError("ActivityCodes", ValidationMessages.ActivityCodesMustBePositive);
                                    break;
                                }
                            }
                        }
                        break;

                    case "Craft":
                    case "Professional":
                        // Non-commercial activities should have description
                        if (string.IsNullOrWhiteSpace(activityData.ActivityDescription))
                        {
                            // Allow empty description for flexible data entry but log warning
                            LoggingService.LogWarning($"Non-commercial activity without description", "ClientValidationService");
                        }
                        break;
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating activity type-specific data: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من بيانات النشاط المحددة");
                return result;
            }
        }

        /// <summary>
        /// Validates file check states based on activity type requirements.
        /// Enforces activity type-specific file check business rules using FileCheckBusinessRuleService.
        /// </summary>
        /// <param name="fileCheckStates">The file check states to validate</param>
        /// <param name="activityType">The activity type</param>
        /// <returns>Validation result with detailed error information</returns>
        private ClientValidationResult ValidateFileCheckStates(Dictionary<string, bool> fileCheckStates, string? activityType)
        {
            var result = new ClientValidationResult();

            try
            {
                if (fileCheckStates == null || !fileCheckStates.Any())
                {
                    return result; // No file check states to validate
                }

                if (string.IsNullOrWhiteSpace(activityType))
                {
                    result.AddError("ActivityType", "نوع النشاط مطلوب للتحقق من حالات فحص الملفات");
                    return result;
                }

                // Use FileCheckBusinessRuleService for comprehensive validation
                var businessRuleValidation = _fileCheckBusinessRuleService.ValidateFileCheckStates(activityType, fileCheckStates, false);
                
                // Convert ValidationResult to ClientValidationResult
                foreach (var errorPair in businessRuleValidation.Errors)
                {
                    foreach (var message in errorPair.Value)
                    {
                        result.AddError(errorPair.Key, message);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating file check states: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من حالات فحص الملفات");
                return result;
            }
        }

        #endregion

        #region Supporting Validation Methods

        /// <summary>
        /// Validates national ID format and business rules.
        /// </summary>
        /// <param name="nationalId">The national ID to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        private ClientValidationResult ValidateNationalId(string nationalId)
        {
            var result = new ClientValidationResult();

            try
            {
                // Validate length (Algerian national ID is typically 18 digits)
                var digitsOnly = PhoneDigitsRegex.Replace(nationalId, "");
                if (digitsOnly.Length != 18)
                {
                    result.AddError("NationalId", ValidationMessages.InvalidNationalId);
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating national ID: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من رقم الهوية الوطنية");
                return result;
            }
        }

        /// <summary>
        /// Validates payment years for G12 and BIS checks.
        /// </summary>
        /// <param name="years">The years to validate</param>
        /// <param name="checkType">The check type (G12 or BIS)</param>
        /// <returns>Validation result with detailed error information</returns>
        private ClientValidationResult ValidatePaymentYears(List<int> years, string checkType)
        {
            var result = new ClientValidationResult();

            try
            {
                var currentYear = DateTime.Now.Year;
                var minYear = 2000; // Reasonable minimum year for business operations

                foreach (var year in years)
                {
                    if (year < minYear || year > currentYear + 1)
                    {
                        result.AddError($"{checkType}CheckYears", $"سنة {checkType} غير صحيحة: {year}");
                    }
                }

                // Check for duplicate years
                var duplicateYears = years.GroupBy(y => y).Where(g => g.Count() > 1).Select(g => g.Key);
                foreach (var duplicateYear in duplicateYears)
                {
                    result.AddError($"{checkType}CheckYears", $"سنة {checkType} مكررة: {duplicateYear}");
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating {checkType} payment years: {ex.Message}", "ClientValidationService");
                result.AddGeneralError($"حدث خطأ أثناء التحقق من سنوات دفع {checkType}");
                return result;
            }
        }

        /// <summary>
        /// Validates notes associated with activities.
        /// </summary>
        /// <param name="notes">The notes to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        private ClientValidationResult ValidateNotes(List<NoteCreationData> notes)
        {
            var result = new ClientValidationResult();

            try
            {
                for (int i = 0; i < notes.Count; i++)
                {
                    var note = notes[i];
                    var notePrefix = $"Note[{i}]";

                    // Validate note content
                    var contentError = _validationService.ValidateNoteContent(note.Content);
                    if (contentError != null)
                    {
                        result.AddError($"{notePrefix}.Content", contentError);
                    }

                    // Validate note content length
                    var lengthError = _validationService.ValidateMaxLength(note.Content, 500, "NoteContent");
                    if (lengthError != null)
                    {
                        result.AddError($"{notePrefix}.Content", lengthError);
                    }

                    // Validate priority level (0, 1, or 2)
                    if (note.Priority < 0 || note.Priority > 2)
                    {
                        result.AddError($"{notePrefix}.Priority", ValidationMessages.InvalidPriority);
                    }

                    // Validate priority (non-negative integer)
                    if (note.Priority < 0)
                    {
                        result.AddError($"{notePrefix}.Priority", ValidationMessages.InvalidPriority);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating notes: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من الملاحظات");
                return result;
            }
        }

        #endregion

        #region Business Rule Validation

        /// <summary>
        /// Validates business rules for file check requirements based on activity type.
        /// Implements UFU2 business logic for different activity types.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckStates">The file check states</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateFileCheckBusinessRules(string activityType, Dictionary<string, bool> fileCheckStates)
        {
            var result = new ClientValidationResult();

            try
            {
                if (string.IsNullOrWhiteSpace(activityType) || fileCheckStates == null)
                {
                    return result; // No validation needed for empty data
                }

                var requiredTypes = ActivityCreationData.GetRequiredFileCheckTypes(activityType);
                var activityDisplayName = ActivityCreationData.GetActivityTypeDisplayName(activityType);

                // Check that only valid file check types are provided
                foreach (var providedType in fileCheckStates.Keys)
                {
                    if (!requiredTypes.Contains(providedType))
                    {
                        result.AddError("FileCheckStates", 
                            $"نوع فحص الملف '{providedType}' غير مطلوب لنوع النشاط '{activityDisplayName}'");
                    }
                }

                // Log business rule validation
                LoggingService.LogInfo($"File check business rules validated for activity type: {activityType}", "ClientValidationService");

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating file check business rules: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من قواعد فحص الملفات");
                return result;
            }
        }

        /// <summary>
        /// Validates that required file checks are completed for the activity type.
        /// Enforces completion requirements based on business rules.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckStates">The file check states</param>
        /// <param name="enforceCompletion">Whether to enforce completion of required checks</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateRequiredFileChecksCompletion(string activityType, Dictionary<string, bool> fileCheckStates, bool enforceCompletion = false)
        {
            var result = new ClientValidationResult();

            try
            {
                if (!enforceCompletion || string.IsNullOrWhiteSpace(activityType) || fileCheckStates == null)
                {
                    return result; // No enforcement needed
                }

                var requiredTypes = ActivityCreationData.GetRequiredFileCheckTypes(activityType);
                var activityDisplayName = ActivityCreationData.GetActivityTypeDisplayName(activityType);

                foreach (var requiredType in requiredTypes)
                {
                    if (!fileCheckStates.ContainsKey(requiredType))
                    {
                        result.AddError("FileCheckStates", 
                            $"فحص الملف '{requiredType}' مطلوب لنوع النشاط '{activityDisplayName}'");
                    }
                    else if (!fileCheckStates[requiredType])
                    {
                        result.AddError("FileCheckStates", 
                            $"فحص الملف '{requiredType}' غير مكتمل لنوع النشاط '{activityDisplayName}'");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating required file checks completion: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من اكتمال فحص الملفات المطلوبة");
                return result;
            }
        }

        #endregion

        #region File Check Business Rule Validation

        /// <summary>
        /// Validates file check types for a specific activity type.
        /// Ensures only valid file check types are provided for the activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckTypes">The file check types to validate</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateFileCheckTypesForActivity(string activityType, IEnumerable<string> fileCheckTypes)
        {
            var result = new ClientValidationResult();

            try
            {
                var businessRuleValidation = _fileCheckBusinessRuleService.ValidateFileCheckTypes(activityType, fileCheckTypes);
                
                // Convert ValidationResult to ClientValidationResult
                foreach (var errorPair in businessRuleValidation.Errors)
                {
                    foreach (var message in errorPair.Value)
                    {
                        result.AddError(errorPair.Key, message);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating file check types for activity: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من أنواع فحص الملفات للنشاط");
                return result;
            }
        }

        /// <summary>
        /// Validates that all required file check types are present for an activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckTypes">The provided file check types</param>
        /// <param name="enforceCompletion">Whether to enforce completion of all required checks</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateRequiredFileCheckTypesForActivity(string activityType, IEnumerable<string> fileCheckTypes, bool enforceCompletion = false)
        {
            var result = new ClientValidationResult();

            try
            {
                var businessRuleValidation = _fileCheckBusinessRuleService.ValidateRequiredFileCheckTypes(activityType, fileCheckTypes, enforceCompletion);
                
                // Convert ValidationResult to ClientValidationResult
                foreach (var errorPair in businessRuleValidation.Errors)
                {
                    foreach (var message in errorPair.Value)
                    {
                        result.AddError(errorPair.Key, message);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating required file check types for activity: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من أنواع فحص الملفات المطلوبة للنشاط");
                return result;
            }
        }

        /// <summary>
        /// Validates file check states with comprehensive business rule enforcement.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckStates">The file check states to validate</param>
        /// <param name="enforceCompletion">Whether to enforce completion of all required checks</param>
        /// <returns>Validation result with detailed error information</returns>
        public ClientValidationResult ValidateFileCheckStatesWithBusinessRules(string activityType, Dictionary<string, bool> fileCheckStates, bool enforceCompletion = false)
        {
            var result = new ClientValidationResult();

            try
            {
                var businessRuleValidation = _fileCheckBusinessRuleService.ValidateFileCheckStates(activityType, fileCheckStates, enforceCompletion);
                
                // Convert ValidationResult to ClientValidationResult
                foreach (var errorPair in businessRuleValidation.Errors)
                {
                    foreach (var message in errorPair.Value)
                    {
                        result.AddError(errorPair.Key, message);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating file check states with business rules: {ex.Message}", "ClientValidationService");
                result.AddGeneralError("حدث خطأ أثناء التحقق من حالات فحص الملفات مع قواعد العمل");
                return result;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets the available file check types for the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>List of available file check types with Arabic display names</returns>
        public Dictionary<string, string> GetAvailableFileCheckTypes(string activityType)
        {
            try
            {
                var types = ActivityCreationData.GetRequiredFileCheckTypes(activityType);
                var result = new Dictionary<string, string>();

                var displayNames = new Dictionary<string, string>
                {
                    { "CAS", "شهادة الإقامة الجبائية" },
                    { "NIF", "رقم التعريف الجبائي" },
                    { "NIS", "رقم التعريف الإحصائي" },
                    { "RC", "السجل التجاري" },
                    { "ART", "بطاقة الحرفي" },
                    { "AGR", "بطاقة المهني" },
                    { "DEX", "إقرار الوجود" }
                };

                foreach (var type in types)
                {
                    if (displayNames.ContainsKey(type))
                    {
                        result[type] = displayNames[type];
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting available file check types: {ex.Message}", "ClientValidationService");
                return new Dictionary<string, string>();
            }
        }

        /// <summary>
        /// Checks if the validation service is properly initialized.
        /// </summary>
        /// <returns>True if the service is ready for use</returns>
        public bool IsServiceReady()
        {
            return _validationService != null;
        }

        #endregion
    }
}