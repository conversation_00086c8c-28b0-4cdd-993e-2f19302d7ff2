using System.ComponentModel;
using System.Runtime.CompilerServices;
using UFU2.Common;

namespace UFU2.ViewModels
{
    /// <summary>
    /// Base ViewModel class providing INotifyPropertyChanged implementation and common functionality.
    /// Serves as the foundation for all ViewModels in the UFU2 application following MVVM pattern.
    /// Includes logging integration and property change notification helpers.
    /// </summary>
    public abstract class BaseViewModel : INotifyPropertyChanged
    {
        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event for the specified property
        /// </summary>
        /// <param name="propertyName">Name of the property that changed. Auto-filled by CallerMemberName.</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            try
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error raising PropertyChanged for {propertyName}: {ex.Message}", GetType().Name);
            }
        }

        /// <summary>
        /// Sets the property value and raises PropertyChanged if the value has changed
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">New value to set</param>
        /// <param name="propertyName">Name of the property. Auto-filled by CallerMemberName.</param>
        /// <returns>True if the property value was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            try
            {
                // Check if the value has actually changed
                if (EqualityComparer<T>.Default.Equals(field, value))
                {
                    return false;
                }

                // Set the new value
                field = value;

                // Raise property changed notification
                OnPropertyChanged(propertyName);

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting property {propertyName}: {ex.Message}", GetType().Name);
                return false;
            }
        }

        #endregion

        #region Property Change Helpers

        /// <summary>
        /// Raises PropertyChanged for multiple properties
        /// </summary>
        /// <param name="propertyNames">Names of the properties that changed</param>
        protected void OnPropertiesChanged(params string[] propertyNames)
        {
            try
            {
                foreach (var propertyName in propertyNames)
                {
                    OnPropertyChanged(propertyName);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error raising PropertyChanged for multiple properties: {ex.Message}", GetType().Name);
            }
        }

        /// <summary>
        /// Sets multiple properties and raises PropertyChanged for all that changed
        /// </summary>
        /// <param name="setters">Array of property setter actions</param>
        protected void SetProperties(params Action[] setters)
        {
            try
            {
                foreach (var setter in setters)
                {
                    setter?.Invoke();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting multiple properties: {ex.Message}", GetType().Name);
            }
        }

        #endregion

        #region Validation Support

        /// <summary>
        /// Validates a property value and returns whether it's valid
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="value">Value to validate</param>
        /// <param name="validator">Validation function</param>
        /// <param name="propertyName">Name of the property being validated</param>
        /// <returns>True if valid, false otherwise</returns>
        protected bool ValidateProperty<T>(T value, Func<T, bool> validator, [CallerMemberName] string? propertyName = null)
        {
            try
            {
                var isValid = validator(value);
                if (!isValid)
                {
                    LoggingService.LogWarning($"Property validation failed for {propertyName}", GetType().Name);
                }
                return isValid;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating property {propertyName}: {ex.Message}", GetType().Name);
                return false;
            }
        }

        #endregion

        #region Lifecycle Methods

        /// <summary>
        /// Called when the ViewModel is being initialized
        /// Override in derived classes for custom initialization logic
        /// </summary>
        protected virtual void OnInitialize()
        {
            LoggingService.LogInfo($"{GetType().Name} initialized", GetType().Name);
        }

        /// <summary>
        /// Called when the ViewModel is being disposed
        /// Override in derived classes for custom cleanup logic
        /// </summary>
        protected virtual void OnDispose()
        {
            LoggingService.LogInfo($"{GetType().Name} disposed", GetType().Name);
        }

        #endregion

        #region IDisposable Support

        private bool _disposed = false;

        /// <summary>
        /// Disposes the ViewModel and releases resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method for proper disposal pattern
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    OnDispose();
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
