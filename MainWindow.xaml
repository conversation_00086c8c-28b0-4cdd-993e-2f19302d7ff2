<Window
    x:Class="UFU2.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:UFU2"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    Title="UFU Client Management"
    Width="1200"
    Height="700"
    AllowsTransparency="False"
    Background="{DynamicResource MainWindowBackgroundBase}"
    Foreground="{DynamicResource MainWindowForegroundBase}"
    Icon="/Resources/logo.ico"
    ResizeMode="CanResizeWithGrip"
    Style="{DynamicResource CustomWindowChromeStyle}"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CustomWindowChromeStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!--  Keyboard Shortcuts for Window Controls  -->
    <Window.InputBindings>
        <!--  Alt+F9: Minimize Window  -->
        <KeyBinding
            Key="F9"
            Command="{Binding MinimizeCommand}"
            Modifiers="Alt" />

        <!--  Alt+F10: Maximize/Restore Window  -->
        <KeyBinding
            Key="F10"
            Command="{Binding MaximizeRestoreCommand}"
            Modifiers="Alt" />
    </Window.InputBindings>

    <materialDesign:DialogHost
        CloseOnClickAway="False"
        Identifier="RootDialog"
        IsTabStop="False">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="60" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  Sidebar  -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource BarCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  Logo  -->
                    <StackPanel Grid.Row="0" Margin="8,16">
                        <Image
                            Width="36"
                            Height="36"
                            HorizontalAlignment="Center"
                            RenderOptions.BitmapScalingMode="HighQuality"
                            RenderOptions.EdgeMode="Unspecified"
                            SnapsToDevicePixels="True"
                            Source="/Resources/logo.png"
                            UseLayoutRounding="True" />
                        <Ellipse
                            Width="8"
                            Height="8"
                            Margin="0,8,0,0"
                            HorizontalAlignment="Center"
                            Fill="Orange" />
                        <Ellipse
                            Width="8"
                            Height="8"
                            Margin="0,4,0,0"
                            HorizontalAlignment="Center"
                            Fill="Orange" />
                        <Ellipse
                            Width="8"
                            Height="8"
                            Margin="0,4,0,0"
                            HorizontalAlignment="Center"
                            Fill="Orange" />
                    </StackPanel>

                    <!--  Navigation Menu  -->
                    <StackPanel Grid.Row="1" VerticalAlignment="Center">
                        <Button
                            Width="40"
                            Height="40"
                            Margin="0,16,0,0"
                            Padding="0"
                            HorizontalAlignment="Center"
                            Style="{StaticResource IconButtonStyle}">
                            <materialDesign:PackIcon
                                Width="24"
                                Height="24"
                                Kind="Home" />
                        </Button>

                        <Button
                            Width="40"
                            Height="40"
                            Margin="0,16,0,0"
                            Padding="0"
                            HorizontalAlignment="Center"
                            Style="{StaticResource IconButtonStyle}">
                            <materialDesign:PackIcon
                                Width="24"
                                Height="24"
                                Kind="ViewList" />
                        </Button>

                        <Button
                            x:Name="AddUserButton"
                            Width="40"
                            Height="40"
                            Margin="0,16,0,0"
                            Padding="0"
                            HorizontalAlignment="Center"
                            Click="AddUserButton_Click"
                            Style="{StaticResource IconButtonStyle}"
                            ToolTip="إضافة عميل جديد">
                            <materialDesign:PackIcon
                                Width="24"
                                Height="24"
                                Kind="AccountPlus" />
                        </Button>

                        <Button
                            Width="40"
                            Height="40"
                            Margin="0,16,0,0"
                            Padding="0"
                            HorizontalAlignment="Center"
                            Style="{StaticResource IconButtonStyle}">
                            <materialDesign:PackIcon
                                Width="24"
                                Height="24"
                                Kind="DatabaseImport" />
                        </Button>

                        <Button
                            Width="40"
                            Height="40"
                            Margin="0,16,0,0"
                            Padding="0"
                            HorizontalAlignment="Center"
                            Style="{StaticResource IconButtonStyle}">
                            <materialDesign:PackIcon
                                Width="24"
                                Height="24"
                                Kind="Settings" />
                        </Button>

                        <Button
                            Width="40"
                            Height="40"
                            Margin="0,16,0,0"
                            Padding="0"
                            HorizontalAlignment="Center"
                            Style="{StaticResource IconButtonStyle}"
                            ToolTip="عرض سجلات التطبيق">
                            <materialDesign:PackIcon
                                Width="24"
                                Height="24"
                                Kind="FileDocumentOutline" />
                        </Button>
                    </StackPanel>

                    <!--  Theme Toggle Control  -->
                    <StackPanel Grid.Row="2" Margin="0,0,0,16">
                        <Button
                            x:Name="ThemeToggleButton"
                            Width="40"
                            Height="40"
                            Margin="0,0,0,8"
                            Padding="0"
                            HorizontalAlignment="Center"
                            Click="ThemeToggleButton_Click"
                            Style="{StaticResource IconButtonStyle}"
                            ToolTip="تبديل المظهر (فاتح/داكن)">
                            <materialDesign:PackIcon
                                x:Name="ThemeToggleIcon"
                                Width="24"
                                Height="24"
                                Kind="Brightness6" />
                        </Button>

                        <!--  Logout Button  -->
                        <Button
                            Width="40"
                            Height="40"
                            Padding="0"
                            HorizontalAlignment="Center"
                            Style="{StaticResource IconButtonStyle}">
                            <materialDesign:PackIcon
                                Width="24"
                                Height="24"
                                Kind="Logout" />
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!--  Main Content Area  -->
            <Grid Grid.Column="1" Margin="20">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock
                        Margin="0,0,0,20"
                        HorizontalAlignment="Center"
                        FontSize="24"
                        FontWeight="Bold"
                        Text="UFU2 Application" />
                    <TextBlock
                        Margin="0,0,0,30"
                        HorizontalAlignment="Center"
                        FontSize="16"
                        Text="Welcome to UFU2" />
                </StackPanel>
            </Grid>

            <!--  Toast Host Panel removed - using desktop-only notifications  -->
        </Grid>

    </materialDesign:DialogHost>
</Window>
