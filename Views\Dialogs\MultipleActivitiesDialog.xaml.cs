using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using UFU2.ViewModels;
using UFU2.Models;
using UFU2.Common;
using UFU2.Common.Extensions;
using MaterialDesignThemes.Wpf;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for MultipleActivitiesDialog.xaml
    /// A UserControl that provides a dialog for managing multiple activities.
    /// Integrates with MaterialDesign DialogHost for consistent modal behavior
    /// and follows UFU2 design patterns with Arabic RTL layout support.
    /// </summary>
    public partial class MultipleActivitiesDialog : UserControl
    {
        #region Private Fields
        private MultipleActivitiesDialogViewModel _viewModel;
        private bool _dialogResult = false;
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the MultipleActivitiesDialog class.
        /// </summary>
        public MultipleActivitiesDialog()
        {
            InitializeComponent();

            // Initialize ViewModel
            _viewModel = new MultipleActivitiesDialogViewModel();
            DataContext = _viewModel;

            // Attach numeric-only input validation to ManualCodeTextBox
            AttachNumericOnlyValidation();
        }

        /// <summary>
        /// Initializes a new instance of the MultipleActivitiesDialog class with existing activities.
        /// </summary>
        /// <param name="existingActivities">Existing activities collection to edit</param>
        public MultipleActivitiesDialog(List<ActivityTypeBaseModel> existingActivities)
            : this()
        {
            // Load existing activities into the dialog
            if (existingActivities != null)
            {
                _viewModel.LoadExistingActivities(existingActivities);
            }
        }
        #endregion
        #region Public API for DialogHost integration
        /// <summary>
        /// Gets the dialog result indicating whether the user saved (true) or cancelled (false).
        /// </summary>
        public bool DialogResult => _dialogResult;

        /// <summary>
        /// Gets the activities added/modified by the user.
        /// </summary>
        public List<ActivityTypeBaseModel> GetAddedActivities()
        {
            return _viewModel.GetActivitiesList();
        }
        #endregion


        #region Public Methods
        /// <summary>
        /// Gets the list of activities that were added/modified in the dialog.
        /// </summary>
        /// <returns>List of ActivityTypeBaseModel representing the activities</returns>
        public List<ActivityTypeBaseModel> GetActivities()
        {
            return _viewModel.GetActivitiesList();
        }

        /// <summary>
        /// Gets the dialog result indicating whether the user saved or cancelled.
        /// </summary>
        /// <returns>True if saved, false if cancelled</returns>
        public bool GetDialogResult()
        {
            return _dialogResult;
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Attaches numeric-only input validation to the ManualCodeTextBox.
        /// Ensures only digits can be entered, following UFU2 input validation patterns.
        /// </summary>
        private void AttachNumericOnlyValidation()
        {
            ManualCodeTextBox.PreviewTextInput += (sender, e) =>
            {
                // Only allow digits
                e.Handled = !char.IsDigit(e.Text, 0);
            };

            // Prevent pasting non-numeric content
            ManualCodeTextBox.PreviewKeyDown += (sender, e) =>
            {
                // Allow Ctrl+V but validate the content in the TextChanged event
                if (e.Key == Key.V && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
                {
                    // Let the paste happen, but validate in TextChanged
                    return;
                }
            };

            // Validate pasted content
            ManualCodeTextBox.TextChanged += (sender, e) =>
            {
                if (sender is TextBox textBox)
                {
                    var text = textBox.Text;
                    var filteredText = string.Empty;

                    // Keep only digits
                    foreach (char c in text)
                    {
                        if (char.IsDigit(c))
                        {
                            filteredText += c;
                        }
                    }

                    // Update text if it was modified
                    if (text != filteredText)
                    {
                        var caretIndex = textBox.CaretIndex;
                        textBox.Text = filteredText;
                        textBox.CaretIndex = Math.Min(caretIndex, filteredText.Length);
                    }
                }
            };
        }
        #endregion

        #region Event Handlers
        /// <summary>
        /// Handles the Save button click event.
        /// Validates the data and closes the dialog with a positive result.
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate that we have activities to save
                if (!_viewModel.HasActivities)
                {
                    LoggingService.LogWarning("Attempted to save with no activities", "MultipleActivitiesDialog");
                    return;
                }

                _dialogResult = true;
                LoggingService.LogInfo($"Multiple activities dialog saved with {_viewModel.AddedActivities.Count} activities", "MultipleActivitiesDialog");

                // Close dialog with positive result
                MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(true, null);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error saving multiple activities dialog: {ex.Message}", "MultipleActivitiesDialog");
            }
        }

        /// <summary>
        /// Handles the Cancel button click event.
        /// Closes the dialog with a negative result without saving changes.
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _dialogResult = false;
                LoggingService.LogInfo("Multiple activities dialog cancelled", "MultipleActivitiesDialog");

                // Close dialog with negative result
                MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(false, null);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error cancelling multiple activities dialog: {ex.Message}", "MultipleActivitiesDialog");
            }
        }
        #endregion
    }
}
