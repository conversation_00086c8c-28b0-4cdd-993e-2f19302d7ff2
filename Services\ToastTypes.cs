using System;

namespace UFU2.Services
{
    /// <summary>
    /// Defines the types of toast notifications available in UFU2
    /// </summary>
    public enum ToastType
    {
        /// <summary>
        /// Success notification with green color scheme and CheckCircle icon
        /// </summary>
        Success,

        /// <summary>
        /// Informational notification with blue color scheme and InformationVariantCircle icon
        /// </summary>
        Info,

        /// <summary>
        /// Warning notification with orange color scheme and AlertCircle icon
        /// </summary>
        Warning,

        /// <summary>
        /// Error notification with red color scheme and CloseCircle icon
        /// </summary>
        Error
    }

    /// <summary>
    /// Defines the positioning options for toast notifications
    /// </summary>
    public enum ToastPosition
    {
        /// <summary>
        /// Bottom right corner of the screen/container
        /// </summary>
        BottomRight,

        /// <summary>
        /// Bottom left corner of the screen/container
        /// </summary>
        BottomLeft,

        /// <summary>
        /// Top right corner of the screen/container
        /// </summary>
        TopRight,

        /// <summary>
        /// Top left corner of the screen/container
        /// </summary>
        TopLeft
    }

    /// <summary>
    /// Interface for toast notification service following UFU2 dependency injection patterns
    /// </summary>
    public interface IToastService
    {
        /// <summary>
        /// Shows a success toast notification with Arabic title
        /// </summary>
        /// <param name="message">The message content to display</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        void ShowSuccess(string message, int duration = 3000, string? detailMessage = null);

        /// <summary>
        /// Shows an error toast notification with Arabic title
        /// </summary>
        /// <param name="message">The message content to display</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        void ShowError(string message, int duration = 5000, string? detailMessage = null);

        /// <summary>
        /// Shows a warning toast notification with Arabic title
        /// </summary>
        /// <param name="message">The message content to display</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        void ShowWarning(string message, int duration = 5000, string? detailMessage = null);

        /// <summary>
        /// Shows an info toast notification with Arabic title
        /// </summary>
        /// <param name="message">The message content to display</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        void ShowInfo(string message, int duration = 3000, string? detailMessage = null);
    }

    /// <summary>
    /// Event arguments for toast detail button clicks
    /// </summary>
    public class ToastDetailEventArgs : EventArgs
    {
        /// <summary>
        /// The toast notification that raised the event
        /// </summary>
        public object Sender { get; }

        /// <summary>
        /// The detailed message to display
        /// </summary>
        public string DetailMessage { get; }

        /// <summary>
        /// Initializes a new instance of ToastDetailEventArgs
        /// </summary>
        /// <param name="sender">The toast notification that raised the event</param>
        /// <param name="detailMessage">The detailed message to display</param>
        public ToastDetailEventArgs(object sender, string detailMessage)
        {
            Sender = sender;
            DetailMessage = detailMessage;
        }
    }

    /// <summary>
    /// Delegate for toast detail button click events
    /// </summary>
    /// <param name="sender">The toast notification that raised the event</param>
    /// <param name="e">Event arguments containing detail message</param>
    public delegate void ToastDetailEventHandler(object sender, ToastDetailEventArgs e);
}
