using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using UFU2.Common;
using UFU2.Models;
using UFU2.ViewModels;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for DuplicateClientDetectionDialog.xaml
    /// A UserControl that provides a dialog for selecting from duplicate clients.
    /// Integrates with MaterialDesign DialogHost for consistent modal behavior
    /// and follows UFU2 design patterns with Arabic RTL layout support.
    /// </summary>
    public partial class DuplicateClientDetectionDialog : UserControl
    {
        #region Private Fields

        private DuplicateClientDetectionDialogViewModel? _viewModel;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the ViewModel for this dialog.
        /// </summary>
        public DuplicateClientDetectionDialogViewModel? ViewModel => _viewModel;

        /// <summary>
        /// Gets the selected client data if user chose to use existing client.
        /// </summary>
        public DuplicateClientData? SelectedClientData => _viewModel?.SelectedClient;

        /// <summary>
        /// Gets whether the user chose to use the selected client (true) or create new (false).
        /// </summary>
        public bool? DialogResult => _viewModel?.DialogResult;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the DuplicateClientDetectionDialog class.
        /// </summary>
        public DuplicateClientDetectionDialog()
        {
            InitializeComponent();
            InitializeViewModel();
        }

        /// <summary>
        /// Initializes a new instance with duplicate clients data.
        /// </summary>
        /// <param name="nameFr">The French name that was searched</param>
        /// <param name="duplicateClients">The list of duplicate clients found</param>
        public DuplicateClientDetectionDialog(string nameFr, List<DuplicateClientData> duplicateClients)
        {
            InitializeComponent();
            InitializeViewModel(nameFr, duplicateClients);
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes the ViewModel for this dialog.
        /// </summary>
        private void InitializeViewModel()
        {
            try
            {
                _viewModel = new DuplicateClientDetectionDialogViewModel();
                DataContext = _viewModel;

                // Subscribe to the CloseRequested event
                _viewModel.CloseRequested += OnCloseRequested;

                LoggingService.LogDebug("DuplicateClientDetectionDialog ViewModel initialized", "DuplicateClientDetectionDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing DuplicateClientDetectionDialog ViewModel: {ex.Message}", "DuplicateClientDetectionDialog");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تهيئة نافذة اختيار العميل",
                    "خطأ في التهيئة",
                    LogLevel.Error,
                    "DuplicateClientDetectionDialog");
            }
        }

        /// <summary>
        /// Initializes the ViewModel with duplicate clients data.
        /// </summary>
        /// <param name="nameFr">The French name that was searched</param>
        /// <param name="duplicateClients">The list of duplicate clients found</param>
        private void InitializeViewModel(string nameFr, List<DuplicateClientData> duplicateClients)
        {
            try
            {
                _viewModel = new DuplicateClientDetectionDialogViewModel(nameFr, duplicateClients);
                DataContext = _viewModel;

                // Subscribe to the CloseRequested event
                _viewModel.CloseRequested += OnCloseRequested;

                LoggingService.LogDebug($"DuplicateClientDetectionDialog ViewModel initialized with {duplicateClients?.Count ?? 0} duplicate clients", "DuplicateClientDetectionDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing DuplicateClientDetectionDialog ViewModel with data: {ex.Message}", "DuplicateClientDetectionDialog");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تهيئة نافذة اختيار العميل",
                    "خطأ في التهيئة",
                    LogLevel.Error,
                    "DuplicateClientDetectionDialog");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Loads duplicate clients for the specified name asynchronously.
        /// </summary>
        /// <param name="nameFr">The French name to search for</param>
        /// <returns>Task representing the async operation</returns>
        public async Task LoadDuplicateClientsAsync(string nameFr)
        {
            if (_viewModel == null)
            {
                LoggingService.LogWarning("ViewModel is null, cannot load duplicate clients", "DuplicateClientDetectionDialog");
                return;
            }

            try
            {
                await _viewModel.LoadDuplicateClientsAsync(nameFr);
                LoggingService.LogInfo($"Loaded duplicate clients for '{nameFr}'", "DuplicateClientDetectionDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading duplicate clients for '{nameFr}': {ex.Message}", "DuplicateClientDetectionDialog");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء البحث عن العملاء المكررين",
                    "خطأ في البحث",
                    LogLevel.Error,
                    "DuplicateClientDetectionDialog");
            }
        }

        /// <summary>
        /// Gets whether there are any duplicate clients loaded.
        /// </summary>
        /// <returns>True if duplicate clients are available, false otherwise</returns>
        public bool HasDuplicateClients()
        {
            return _viewModel?.DuplicateClients?.Count > 0;
        }

        /// <summary>
        /// Gets the count of duplicate clients loaded.
        /// </summary>
        /// <returns>Number of duplicate clients</returns>
        public int GetDuplicateClientCount()
        {
            return _viewModel?.DuplicateClients?.Count ?? 0;
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the Loaded event to set up initial focus.
        /// </summary>
        private void DuplicateClientDetectionDialog_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Set focus to the first RadioButton if available
                if (_viewModel?.DuplicateClients?.Count > 0)
                {
                    // The first item should be pre-selected by the ViewModel
                    LoggingService.LogDebug("Dialog loaded with duplicate clients", "DuplicateClientDetectionDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in dialog loaded event: {ex.Message}", "DuplicateClientDetectionDialog");
            }
        }

        /// <summary>
        /// Handles the CloseRequested event from the ViewModel.
        /// </summary>
        /// <param name="sender">The ViewModel that raised the event</param>
        /// <param name="result">The dialog result</param>
        private void OnCloseRequested(object? sender, bool result)
        {
            try
            {
                LoggingService.LogDebug($"Dialog close requested with result: {result}", "DuplicateClientDetectionDialog");

                // Close the dialog using MaterialDesign DialogHost
                DialogHost.CloseDialogCommand.Execute(result, this);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing dialog: {ex.Message}", "DuplicateClientDetectionDialog");
            }
        }

        #endregion

        #region Static Factory Methods

        /// <summary>
        /// Creates a new DuplicateClientDetectionDialog with the specified duplicate clients.
        /// </summary>
        /// <param name="nameFr">The French name that was searched</param>
        /// <param name="duplicateClients">The list of duplicate clients found</param>
        /// <returns>A new dialog instance</returns>
        public static DuplicateClientDetectionDialog Create(string nameFr, List<DuplicateClientData> duplicateClients)
        {
            try
            {
                var dialog = new DuplicateClientDetectionDialog(nameFr, duplicateClients);
                LoggingService.LogInfo($"Created DuplicateClientDetectionDialog for '{nameFr}' with {duplicateClients?.Count ?? 0} duplicates", "DuplicateClientDetectionDialog");
                return dialog;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating DuplicateClientDetectionDialog: {ex.Message}", "DuplicateClientDetectionDialog");
                throw;
            }
        }

        /// <summary>
        /// Creates a new DuplicateClientDetectionDialog and loads duplicate clients asynchronously.
        /// </summary>
        /// <param name="nameFr">The French name to search for</param>
        /// <returns>A new dialog instance with loaded data</returns>
        public static async Task<DuplicateClientDetectionDialog> CreateAndLoadAsync(string nameFr)
        {
            try
            {
                var dialog = new DuplicateClientDetectionDialog();
                await dialog.LoadDuplicateClientsAsync(nameFr);
                
                LoggingService.LogInfo($"Created and loaded DuplicateClientDetectionDialog for '{nameFr}'", "DuplicateClientDetectionDialog");
                return dialog;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating and loading DuplicateClientDetectionDialog: {ex.Message}", "DuplicateClientDetectionDialog");
                throw;
            }
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// Handles cleanup when the dialog is unloaded.
        /// </summary>
        private void DuplicateClientDetectionDialog_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Unsubscribe from ViewModel events
                if (_viewModel != null)
                {
                    _viewModel.CloseRequested -= OnCloseRequested;
                }

                // Clean up ViewModel if needed
                _viewModel?.Dispose();
                LoggingService.LogDebug("DuplicateClientDetectionDialog unloaded and cleaned up", "DuplicateClientDetectionDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during dialog cleanup: {ex.Message}", "DuplicateClientDetectionDialog");
            }
        }

        #endregion
    }
}
