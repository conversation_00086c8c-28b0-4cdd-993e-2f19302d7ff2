<UserControl
    x:Class="UFU2.Views.Dialogs.NoteListDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:UFU2.Models"
    d:DesignHeight="550"
    d:DesignWidth="450"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  Converters  -->
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <!--  DialogHost for nested dialogs (AddNotesDialog)  -->
    <materialDesign:DialogHost
        materialDesign:TransitionAssist.DisableTransitions="True"
        CloseOnClickAway="False"
        Identifier="NoteListDialogHost"
        IsTabStop="False">

        <!--  Main dialog card  -->
        <materialDesign:Card
            x:Name="MainCard"
            Width="360"
            Height="400"
            Padding="0"
            Style="{StaticResource DialogBaseCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Header section  -->
                <materialDesign:Card
                    Grid.Row="0"
                    Margin="0"
                    Style="{DynamicResource HeaderCardStyle}">
                    <TextBlock
                        HorizontalAlignment="Center"
                        Style="{StaticResource HeadlineStyle}"
                        Text="ملاحظات" />

                </materialDesign:Card>

                <!--  Content section  -->
                <materialDesign:Card
                    Grid.Row="1"
                    Margin="12"
                    Padding="8"
                    Style="{DynamicResource ContentCardStyle}">

                    <ScrollViewer Style="{StaticResource DialogScrollViewerStyle}" VerticalScrollBarVisibility="Auto">

                        <Grid>
                            <!--  Empty State  -->
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Style="{StaticResource HeadlineStyle}"
                                Text="لم يتم إضافة أي ملاحظات بعد"
                                Visibility="{Binding HasNotes, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />

                            <!--  Notes List  -->
                            <ItemsControl ItemsSource="{Binding Notes.Notes}" Visibility="{Binding HasNotes, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <!--  Note Card  -->
                                        <materialDesign:Card
                                            Margin="0,0,7,8"
                                            Padding="0"
                                            HorizontalAlignment="Stretch"
                                            materialDesign:ElevationAssist.Elevation="Dp2"
                                            Background="{DynamicResource CardBackgroundgOverlay}"
                                            Style="{StaticResource ContentCardStyle}">
                                            <DockPanel>
                                                <!--  Priority Color Indicator  -->
                                                <Border
                                                    Width="7"
                                                    Margin="0,0,12,0"
                                                    HorizontalAlignment="Stretch"
                                                    VerticalAlignment="Stretch"
                                                    Background="{Binding Priority, Converter={StaticResource PriorityToThemeColorConverter}}"
                                                    DockPanel.Dock="Left" />

                                                <!--  Note Content  -->
                                                <Grid Margin="0,0,15,0" DockPanel.Dock="Right">
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="*" />
                                                        <RowDefinition Height="Auto" />
                                                    </Grid.RowDefinitions>

                                                    <!--  Note Text  -->
                                                    <TextBlock
                                                        Grid.Row="0"
                                                        Margin="2,8"
                                                        Style="{StaticResource BodyTextStyle}"
                                                        Text="{Binding Content}"
                                                        TextWrapping="Wrap" />

                                                    <!--  Metadata Row  -->
                                                    <Border
                                                        Grid.Row="1"
                                                        VerticalAlignment="Bottom"
                                                        BorderBrush="{DynamicResource CardBackgroundAccent}"
                                                        BorderThickness="0,1,0,0">
                                                        <DockPanel>
                                                            <!--  Action Buttons  -->
                                                            <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                                                                <!--  Edit Button  -->
                                                                <Button
                                                                    Padding="0"
                                                                    Command="{Binding DataContext.EditNoteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                    CommandParameter="{Binding}"
                                                                    Style="{StaticResource IconEditButtonStyle}"
                                                                    ToolTip="تعديل الملاحظة">
                                                                    <materialDesign:PackIcon Kind="Edit" />
                                                                </Button>

                                                                <!--  Delete Button  -->
                                                                <Button
                                                                    Padding="0"
                                                                    Command="{Binding DataContext.DeleteNoteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                    CommandParameter="{Binding}"
                                                                    Style="{StaticResource IconDeleteButtonStyle}"
                                                                    ToolTip="حذف الملاحظة">
                                                                    <materialDesign:PackIcon Kind="Delete" />
                                                                </Button>
                                                            </StackPanel>
                                                            <!--  Date/Time  -->
                                                            <TextBlock
                                                                VerticalAlignment="Center"
                                                                DockPanel.Dock="Right"
                                                                Style="{StaticResource LabelTextStyle}"
                                                                Text="{Binding DisplayDate}" />
                                                        </DockPanel>
                                                    </Border>
                                                </Grid>
                                            </DockPanel>
                                        </materialDesign:Card>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </Grid>
                    </ScrollViewer>
                </materialDesign:Card>
                <!--  Action buttons section  -->
                <materialDesign:Card
                    Grid.Row="2"
                    Margin="7,0,7,7"
                    Padding="7"
                    Style="{DynamicResource ContentCardStyle}">
                    <!--  Close button (left side)  -->
                    <Button
                        x:Name="CloseButton"
                        Grid.Column="0"
                        HorizontalAlignment="Right"
                        Command="{Binding CloseCommand}"
                        Content="إغلاق"
                        Style="{StaticResource SecondaryButtonStyle}"
                        ToolTip="إغلاق النافذة" />
                </materialDesign:Card>
                <Button
                    x:Name="AddNoteButton"
                    Grid.Column="1"
                    Width="36"
                    Height="36"
                    Margin="12,0"
                    Padding="0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Panel.ZIndex="1"
                    Command="{Binding AddNoteCommand}"
                    IsDefault="True"
                    Style="{StaticResource PrimaryButtonStyle}"
                    ToolTip="إضافة ملاحظة جديدة">
                    <materialDesign:PackIcon
                        Width="20"
                        Height="20"
                        Kind="Plus" />
                </Button>
            </Grid>
        </materialDesign:Card>
    </materialDesign:DialogHost>
</UserControl>
