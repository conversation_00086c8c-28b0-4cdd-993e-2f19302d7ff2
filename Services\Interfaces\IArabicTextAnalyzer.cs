using System;

namespace UFU2.Services.Interfaces
{
    /// <summary>
    /// Interface for advanced Arabic text processing with linguistic analysis and pattern recognition.
    /// Provides comprehensive Arabic text analysis capabilities for enhanced search functionality.
    /// </summary>
    public interface IArabicTextAnalyzer
    {
        /// <summary>
        /// Analyzes Arabic text and provides comprehensive linguistic information.
        /// </summary>
        /// <param name="text">Text to analyze</param>
        /// <returns>Detailed analysis of the Arabic text</returns>
        ArabicTextAnalysis AnalyzeText(string text);

        /// <summary>
        /// Generates search variations for a given search term to improve matching.
        /// </summary>
        /// <param name="searchTerm">Original search term</param>
        /// <returns>Array of search variations</returns>
        string[] GenerateSearchVariations(string searchTerm);

        /// <summary>
        /// Calculates exact prefix matching similarity between search term and target text.
        /// Prioritizes exact prefix matches over any other type of matching.
        /// </summary>
        /// <param name="searchTerm">Search term to match as prefix</param>
        /// <param name="targetText">Target text to check for prefix match</param>
        /// <returns>Similarity score between 0.0 and 1.0</returns>
        double CalculateExactPrefixSimilarity(string searchTerm, string targetText);

        /// <summary>
        /// Extracts and analyzes individual words from Arabic text.
        /// </summary>
        /// <param name="text">Text to extract words from</param>
        /// <returns>Array of word information</returns>
        ArabicWordInfo[] ExtractWords(string text);

        /// <summary>
        /// Calculates relevance score based on exact prefix matching.
        /// Prioritizes results that start with the exact search term sequence.
        /// </summary>
        /// <param name="searchTerms">Array of search terms (typically contains the original search term)</param>
        /// <param name="targetText">Text to score against search terms</param>
        /// <returns>Relevance score between 0.0 and 1.0</returns>
        double CalculateRelevanceScore(string[] searchTerms, string targetText);
    }

    /// <summary>
    /// Comprehensive analysis result for Arabic text processing.
    /// </summary>
    public class ArabicTextAnalysis
    {
        /// <summary>
        /// Normalized version of the original text.
        /// </summary>
        public string NormalizedText { get; set; } = string.Empty;

        /// <summary>
        /// Array of search variations generated from the text.
        /// </summary>
        public string[] SearchVariations { get; set; } = Array.Empty<string>();

        /// <summary>
        /// Individual words extracted from the text.
        /// </summary>
        public ArabicWordInfo[] Words { get; set; } = Array.Empty<ArabicWordInfo>();

        /// <summary>
        /// Complexity assessment of the text.
        /// </summary>
        public TextComplexity Complexity { get; set; } = TextComplexity.Simple;

        /// <summary>
        /// Indicates whether the text contains Arabic conjunctions.
        /// </summary>
        public bool ContainsConjunctions { get; set; }

        /// <summary>
        /// Indicates whether the text contains Arabic characters.
        /// </summary>
        public bool ContainsArabicText { get; set; }

        /// <summary>
        /// Word frequency map for the analyzed text.
        /// </summary>
        public Dictionary<string, int> WordFrequency { get; set; } = new Dictionary<string, int>();
    }

    /// <summary>
    /// Information about an individual Arabic word.
    /// </summary>
    public class ArabicWordInfo
    {
        /// <summary>
        /// Original word as it appears in the text.
        /// </summary>
        public string OriginalWord { get; set; } = string.Empty;

        /// <summary>
        /// Normalized version of the word.
        /// </summary>
        public string NormalizedWord { get; set; } = string.Empty;

        /// <summary>
        /// Position of the word in the original text.
        /// </summary>
        public int Position { get; set; }

        /// <summary>
        /// Length of the word.
        /// </summary>
        public int Length { get; set; }

        /// <summary>
        /// Indicates whether this word is an Arabic conjunction.
        /// </summary>
        public bool IsConjunction { get; set; }

        /// <summary>
        /// Indicates whether this word is an Arabic stop word.
        /// </summary>
        public bool IsStopWord { get; set; }

        /// <summary>
        /// Indicates whether this word contains Arabic characters.
        /// </summary>
        public bool IsArabicWord { get; set; }
    }

    /// <summary>
    /// Text complexity levels for processing optimization.
    /// </summary>
    public enum TextComplexity
    {
        /// <summary>
        /// Simple text with basic vocabulary.
        /// </summary>
        Simple = 0,

        /// <summary>
        /// Moderate complexity with mixed vocabulary.
        /// </summary>
        Moderate = 1,

        /// <summary>
        /// Complex text with advanced vocabulary and structures.
        /// </summary>
        Complex = 2
    }
}
