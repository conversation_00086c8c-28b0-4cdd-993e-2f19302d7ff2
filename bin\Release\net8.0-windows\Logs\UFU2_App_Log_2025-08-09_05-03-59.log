=== UFU2 Application Session Started at 2025-08-09 05:03:59 ===
[2025-08-09 05:03:59.964]  	[INFO]		[LoggingService]	Log level set to Info
[2025-08-09 05:03:59.977]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 05:04:00.003]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 05:04:00.036]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 05:04:00.099]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 05:04:00.137]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 05:04:00.145]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 05:04:00.149]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 05:04:00.152]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 05:04:00.157]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 05:04:00.162]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 05:04:00.186]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 05:04:00.196]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 05:04:00.554]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 05:04:00.619]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 05:04:00.675]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 05:04:00.757]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 05:04:00.836]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 05:04:00.967]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 05:04:01.016]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 05:04:02.236]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 05:04:02.260]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 05:04:02.283]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 05:04:02.327]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 05:04:02.327]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 05:04:02.329]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 05:04:02.332]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 05:04:02.405]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 05:04:02.465]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 05:04:02.547]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 05:04:02.566]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 05:04:02.610]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 05:04:02.644]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 05:04:02.670]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 05:04:02.712]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 05:04:02.744]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 05:04:02.772]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 05:04:02.808]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 05:04:02.835]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 05:04:02.861]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 05:04:02.915]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 05:04:02.950]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 05:04:03.009]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 05:04:03.054]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 05:04:03.072]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 05:04:03.096]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 05:04:03.130]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 05:04:03.175]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 05:04:03.184]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 05:04:03.232]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 05:04:03.278]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 05:04:03.295]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 05:04:03.299]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 05:04:03.302]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 05:04:03.387]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 05:04:03.398]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 05:04:03.403]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 05:04:03.420]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 05:04:03.427]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 05:04:03.431]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 05:04:03.435]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 05:04:03.439]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 05:04:03.442]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 05:04:03.446]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 05:04:03.451]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 05:04:03.513]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-09 05:04:03.520]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 61ms
[2025-08-09 05:04:03.532]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 05:04:03.536]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 12ms
[2025-08-09 05:04:03.541]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 05:04:03.545]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 5ms
[2025-08-09 05:04:03.554]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 6ms
[2025-08-09 05:04:03.566]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 05:04:03.569]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 12ms
[2025-08-09 05:04:03.573]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 05:04:03.576]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 05:04:03.580]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 05:04:03.583]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 05:04:03.586]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 05:04:03.589]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 05:04:03.592]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 05:04:03.595]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 05:04:03.598]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 05:04:03.601]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 05:04:03.604]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 05:04:03.608]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 05:04:03.611]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 05:04:03.614]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 05:04:03.617]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 05:04:03.948]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 05:04:04.013]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 05:04:06.378]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 05:04:08.955]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 05:04:09.042]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 05:04:09.089]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 05:04:09.095]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 05:04:09.100]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 05:04:09.103]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 05:04:09.111]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 05:04:09.690]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 05:04:11.354]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 05:04:11.361]  	[INFO]		[ImageManagementViewModel]	Initializing ImageManagementViewModel
[2025-08-09 05:04:11.367]  	[INFO]		[ImageManagementViewModel]	ImageManagementViewModel initialized successfully - Zoom: 100%, Rotation: 0°
[2025-08-09 05:04:11.373]  	[INFO]		[ImageManagementDialog]	ImageManagementDialog initialized - Current Memory Usage: 22.54 MB (23,640,120 bytes)
[2025-08-09 05:04:11.378]  	[INFO]		[NPersonalView]	Opening ImageManagementDialog in memory-only mode
[2025-08-09 05:04:11.390]  	[INFO]		[ClientProfileImage]	Image edit request delegated to parent control
[2025-08-09 05:04:13.092]  	[INFO]		[ImageManagementDialog]	LoadImage button clicked
[2025-08-09 05:04:36.535]  	[INFO]		[ImageManagementDialog]	User selected image file: photo_3712252582143567829_c.jpg
[2025-08-09 05:04:36.539]  	[INFO]		[ImageManagementViewModel]	Image loading started successfully
[2025-08-09 05:04:36.586]  	[INFO]		[ImageManagementDialog]	Original Image Loaded - File: photo_3712252582143567829_c.jpg, Size: 34.98 KB (35,824 bytes), Path: E:\UserFiles\Pictures\photo_3712252582143567829_c.jpg
[2025-08-09 05:04:36.593]  	[INFO]		[ImageManagementDialog]	Preview Image Generated - Dimensions: 800x800, Memory: 2.44 MB (2,560,000 bytes), Decode Width: 800px
[2025-08-09 05:04:36.604]  	[INFO]		[ImageManagementDialog]	Image Processing Metrics - Scaling: 100.0%, Compression Ratio: 0.01:1, Memory vs File: 71.46x
[2025-08-09 05:04:36.613]  	[INFO]		[ImageManagementViewModel]	Preview Image Validated - Dimensions: 800x800, Aspect Ratio: 1.00, Megapixels: 0.6MP, Total Pixels: 640,000
[2025-08-09 05:04:36.623]  	[INFO]		[ImageManagementViewModel]	Creating original image backup - Dimensions: 800x800
[2025-08-09 05:04:36.632]  	[INFO]		[ImageManagementViewModel]	Resetting image transforms to default values
[2025-08-09 05:04:36.640]  	[INFO]		[ImageManagementViewModel]	Image transforms reset successfully
[2025-08-09 05:04:36.647]  	[INFO]		[ImageManagementViewModel]	Crop rectangle reset to default position
[2025-08-09 05:04:36.655]  	[INFO]		[ImageManagementViewModel]	Backend simulation initialized for 800x800 image - Backend container: 1500x960
[2025-08-09 05:04:36.661]  	[INFO]		[ImageManagementViewModel]	Image loading completed successfully - Success: True
[2025-08-09 05:04:36.669]  	[INFO]		[ImageManagementDialog]	Image loading process completed successfully for: photo_3712252582143567829_c.jpg
[2025-08-09 05:04:37.922]  	[INFO]		[ImageManagementDialog]	Crop Image button clicked - Starting crop operation workflow
[2025-08-09 05:04:37.939]  	[INFO]		[ImageManagementDialog]	Crop validation passed - Image: 800x800, Crop: 123,15,254,290
[2025-08-09 05:04:37.946]  	[INFO]		[ImageManagementDialog]	Proceeding with direct crop operation
[2025-08-09 05:04:37.952]  	[INFO]		[ImageManagementViewModel]	Image loading started successfully
[2025-08-09 05:04:38.461]  	[INFO]		[ImageManagementViewModel]	Starting interactive image crop operation
[2025-08-09 05:04:38.528]  	[INFO]		[ImageManagementViewModel]	Using WYSIWYG cropping system
[2025-08-09 05:04:38.589]  	[INFO]		[ImageManagementViewModel]	Creating WYSIWYG cropped image
[2025-08-09 05:04:38.602]  	[INFO]		[ImageManagementViewModel]	WYSIWYG cropped bitmap created successfully - Final size: 634x725
[2025-08-09 05:04:38.607]  	[INFO]		[ImageManagementViewModel]	WYSIWYG cropped image created successfully - Size: 634x725
[2025-08-09 05:04:38.614]  	[INFO]		[ImageManagementViewModel]	Image cropped successfully using interactive rectangle - New size: 634x725
[2025-08-09 05:04:38.621]  	[INFO]		[ImageManagementDialog]	Crop operation successful - Result: 634x725
[2025-08-09 05:04:38.627]  	[INFO]		[ImageManagementViewModel]	Applying real-time crop preview
[2025-08-09 05:04:38.662]  	[INFO]		[ImageManagementViewModel]	Applying crop preview - New dimensions: 634x725
[2025-08-09 05:04:38.671]  	[INFO]		[ImageManagementViewModel]	Original image backup preserved - Dimensions: 800x800
[2025-08-09 05:04:38.683]  	[INFO]		[ImageManagementViewModel]	Applying post-crop visual fit for cropped image: 634x725
[2025-08-09 05:04:38.689]  	[INFO]		[ImageManagementViewModel]	Post-crop visual fit applied:
[2025-08-09 05:04:38.696]  	[INFO]		[ImageManagementViewModel]	  • Cropped image: 634x725 pixels
[2025-08-09 05:04:38.702]  	[INFO]		[ImageManagementViewModel]	  • Natural display size (100% zoom): 279.8x320.0
[2025-08-09 05:04:38.709]  	[INFO]		[ImageManagementViewModel]	  • Crop guide target: 254x290
[2025-08-09 05:04:38.714]  	[INFO]		[ImageManagementViewModel]	  • Scale factors - X: 0.908, Y: 0.906
[2025-08-09 05:04:38.721]  	[INFO]		[ImageManagementViewModel]	  • Applied visual scale: 0.906
[2025-08-09 05:04:38.726]  	[INFO]		[ImageManagementViewModel]	  • Final visual size: 253.6x290.0
[2025-08-09 05:04:38.734]  	[INFO]		[ImageManagementViewModel]	  • ZoomPercentage maintained at: 100%
[2025-08-09 05:04:38.740]  	[INFO]		[ImageManagementViewModel]	✓ Visual fit validation PASSED - Image will visually fit within crop guide boundaries
[2025-08-09 05:04:38.747]  	[INFO]		[ImageManagementViewModel]	Crop rectangle reset to default position
[2025-08-09 05:04:38.755]  	[INFO]		[ImageManagementViewModel]	Backend simulation initialized for 634x725 image - Backend container: 1500x960
[2025-08-09 05:04:38.763]  	[INFO]		[ImageManagementViewModel]	Real-time crop preview applied successfully - New image size: 634x725
[2025-08-09 05:04:38.774]  	[INFO]		[ImageManagementViewModel]	Loading completed successfully with original image backup preserved
[2025-08-09 05:04:38.781]  	[INFO]		[ImageManagementDialog]	Real-time crop preview applied successfully - Image replaced with cropped result
[2025-08-09 05:04:39.478]  	[INFO]		[SaveCancelButtonsControl]	Save button clicked in SaveCancelButtonsControl
[2025-08-09 05:04:39.485]  	[INFO]		[ImageManagementDialog]	Save button clicked - Starting save operation workflow
[2025-08-09 05:04:39.493]  	[INFO]		[ImageManagementDialog]	Preparing to save image with standardized dimensions - Original size: 634x725, Target: 127x145 pixels, Format: Bgr32
[2025-08-09 05:04:39.498]  	[INFO]		[ImageManagementViewModel]	Image loading started successfully
[2025-08-09 05:04:39.504]  	[INFO]		[ImageManagementDialog]	Starting in-memory image processing - Original size: 634x725
[2025-08-09 05:04:39.510]  	[INFO]		[ImageManagementDialog]	High-quality resize completed successfully - Final size: 127x145
[2025-08-09 05:04:39.514]  	[INFO]		[ImageManagementDialog]	Image processed and stored in memory successfully - Final size: 127x145 pixels
[2025-08-09 05:04:39.519]  	[INFO]		[ImageManagementViewModel]	Preview Image Validated - Dimensions: 634x725, Aspect Ratio: 0.87, Megapixels: 0.5MP, Total Pixels: 459,650
[2025-08-09 05:04:39.528]  	[INFO]		[ImageManagementViewModel]	Creating original image backup - Dimensions: 634x725
[2025-08-09 05:04:39.536]  	[INFO]		[ImageManagementViewModel]	Resetting image transforms to default values
[2025-08-09 05:04:39.541]  	[INFO]		[ImageManagementViewModel]	Image transforms reset successfully
[2025-08-09 05:04:39.547]  	[INFO]		[ImageManagementViewModel]	Crop rectangle reset to default position
[2025-08-09 05:04:39.553]  	[INFO]		[ImageManagementViewModel]	Backend simulation initialized for 634x725 image - Backend container: 1500x960
[2025-08-09 05:04:39.557]  	[INFO]		[ImageManagementViewModel]	Image loading completed successfully - Success: True
[2025-08-09 05:04:39.561]  	[INFO]		[ImageManagementDialog]	Image processed and stored in memory successfully - Standardized to 127x145 pixels
[2025-08-09 05:04:39.566]  	[INFO]		[ImageManagementDialog]	Dialog closing - Final Memory Usage: 22.23 MB (23,304,640 bytes), Result: True
[2025-08-09 05:04:39.605]  	[INFO]		[ImageManagementDialog]	Memory cleanup completed - Freed: 1.65 MB (1,726,880 bytes), After GC: 20.58 MB
[2025-08-09 05:04:39.611]  	[INFO]		[ImageManagementDialog]	ImageManagementDialog session completed with result: True
[2025-08-09 05:04:39.616]  	[INFO]		[NPersonalView]	ImageManagementDialog completed successfully
[2025-08-09 05:04:39.621]  	[INFO]		[ClientProfileImage]	Profile image updated successfully
[2025-08-09 05:04:39.625]  	[INFO]		[NPersonalView]	Profile image updated successfully in NewClientViewModel
[2025-08-09 05:04:44.799]  	[INFO]		[NPersonalView]	Opening phone numbers dialog
[2025-08-09 05:04:44.827]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 05:04:46.032]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 05:04:46.039]  	[INFO]		[NPersonalView]	Phone numbers dialog cancelled - no changes made
[2025-08-09 05:05:05.597]  	[INFO]		[NPersonalView]	Opening phone numbers dialog
[2025-08-09 05:05:05.613]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 05:05:06.860]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 05:05:06.866]  	[INFO]		[NPersonalView]	Phone numbers dialog cancelled - no changes made
[2025-08-09 05:05:12.787]  	[INFO]		[NPersonalView]	Opening phone numbers dialog
[2025-08-09 05:05:12.809]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 05:05:15.273]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 05:05:15.280]  	[INFO]		[NPersonalView]	Phone numbers dialog cancelled - no changes made
[2025-08-09 05:05:34.256]  	[INFO]		[NActivityDetailView]	Opening ActivityStatusUpdateDialog - Context: StatusChange, Status: معدل
[2025-08-09 05:05:34.267]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 05:05:34.294]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized
[2025-08-09 05:05:36.234]  	[INFO]		[SaveCancelButtonsControl]	Save button clicked in SaveCancelButtonsControl
[2025-08-09 05:05:36.244]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	Auto-populated empty UpdateNote with Arabic default text
[2025-08-09 05:05:36.248]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	Saving activity update - Date: 09/08/2025, Note length: 22
[2025-08-09 05:05:36.255]  	[INFO]		[NActivityDetailView]	Activity status update saved - Context: StatusChange, Date: 09/08/2025, Note length: 22
[2025-08-09 05:05:41.857]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 05:05:42.363]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 05:05:42.869]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 05:05:49.853]  	[INFO]		[ActivityManagementViewModel]	Updated CPI Dairas for wilaya سطيف: 10 dairas
[2025-08-09 05:05:59.245]  	[INFO]		[NewClientViewModel]	Auto-populate payment years requested for date: xx/xx/2020
[2025-08-09 05:06:00.140]  	[INFO]		[ViewMemoryOptimizationService]	High memory usage detected: 333MB
[2025-08-09 05:06:00.148]  	[INFO]		[ViewMemoryOptimizationService]	Performed selective cleanup of 0 views
[2025-08-09 05:06:03.442]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 05:06:03.457]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CpiLocationService: Needs Attention
[2025-08-09 05:06:03.465]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ActivityTypeBaseService: Error: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 05:06:03.472]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for CraftTypeBaseService: Hit ratio: 0.0%
[2025-08-09 05:06:03.477]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CraftTypeBaseService: Poor Performance
[2025-08-09 05:06:03.482]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for FileCheckBusinessRuleService: Hit ratio: 0.0%
[2025-08-09 05:06:03.488]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for FileCheckBusinessRuleService: Low hit ratio: 0.0%
[2025-08-09 05:06:03.497]  	[INFO]		[CacheMonitoringService]	Cache monitoring summary - Services: 5, Unhealthy: 4, Total memory: 0.0MB, Avg hit ratio: 56.5%
[2025-08-09 05:06:09.440]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 05:06:09.446]  	[INFO]		[PaymentYearsSelectionDialogViewModel]	PaymentYearsSelectionDialogViewModel initialized
[2025-08-09 05:06:09.451]  	[INFO]		[PaymentYearsSelectionDialogViewModel]	PaymentYearsSelectionDialogViewModel initialized for years 2020-2025
[2025-08-09 05:06:15.032]  	[INFO]		[SaveCancelButtonsControl]	Save button clicked in SaveCancelButtonsControl
[2025-08-09 05:06:17.415]  	[INFO]		[NFileCheckView]	Opening notes management dialog
[2025-08-09 05:06:17.429]  	[INFO]		[NoteListDialogViewModel]	NoteListDialogViewModel initialized
[2025-08-09 05:06:18.626]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 05:06:18.631]  	[INFO]		[AddNotesDialogViewModel]	AddNotesDialogViewModel initialized
[2025-08-09 05:06:18.635]  	[INFO]		[AddNotesDialogViewModel]	AddNotesDialogViewModel initialized with ValidationService integration
[2025-08-09 05:06:19.529]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 05:06:19.533]  	[INFO]		[AddNotesDialogViewModel]	Note editing cancelled
[2025-08-09 05:06:19.537]  	[INFO]		[AddNotesDialog]	Note editing cancelled
[2025-08-09 05:06:19.542]  	[INFO]		[NoteListDialog]	Add note cancelled
[2025-08-09 05:06:20.456]  	[INFO]		[NoteListDialogViewModel]	Close dialog command executed
[2025-08-09 05:06:20.461]  	[INFO]		[NoteListDialog]	Notes dialog closed
[2025-08-09 05:06:20.466]  	[INFO]		[NFileCheckView]	Notes dialog saved successfully. Total notes: 0
[2025-08-09 05:06:20.471]  	[INFO]		[NFileCheckView]	Notes display updated. Current count: 0
[2025-08-09 05:06:48.315]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 05:06:48.324]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 05:06:48.344]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 05:06:48.349]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 05:06:48.360]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 05:06:48.433]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 05:06:49.579]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 05:06:49.589]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:49.598]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 05:06:49.610]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 05:06:49.618]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:49.622]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 05:06:49.630]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 05:06:49.638]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 05:06:49.653]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:49.660]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 05:06:49.664]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 05:06:49.671]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:49.682]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 05:06:49.692]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 05:06:49.697]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 05:06:49.702]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 05:06:49.721]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 05:06:49.727]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 05:06:49.732]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 05:06:49.761]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 05:06:49.782]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 05:06:49.805]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 05:06:49.814]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 05:06:49.820]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 05:06:49.831]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-09 05:06:49.843]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 50.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-09 05:06:49.850]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-09 05:06:49.856]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 66.7%
[2025-08-09 05:06:49.865]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 05:06:49.875]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 05:06:49.888]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 05:06:49.922]  	[INFO]		[ResourceManager]	Generated memory leak report: 14 alive resources, 0 dead resources
[2025-08-09 05:06:49.952]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 3
[2025-08-09 05:06:49.978]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 3 potential leaks detected
[2025-08-09 05:06:49.989]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 2 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-09 05:06:50.001]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 05:06:50.018]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 05:06:50.031]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-09 05:06:50.042]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 05:06:50.049]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 05:06:50.062]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.067]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 05:06:50.071]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 05:06:50.078]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.086]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 05:06:50.096]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 05:06:50.101]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddNotesDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.106]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddNotesDialogViewModel (0 handlers)
[2025-08-09 05:06:50.110]  	[INFO]		[AddNotesDialogViewModel]	AddNotesDialogViewModel disposed
[2025-08-09 05:06:50.116]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddNotesDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.120]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddNotesDialogViewModel (0 handlers)
[2025-08-09 05:06:50.125]  	[INFO]		[AddNotesDialogViewModel]	AddNotesDialogViewModel disposed
[2025-08-09 05:06:50.130]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PhoneNumbersDialogViewModel (3 resources, 0 event subscriptions)
[2025-08-09 05:06:50.136]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PhoneNumbersDialogViewModel (0 handlers)
[2025-08-09 05:06:50.140]  	[INFO]		[PhoneNumbersDialogViewModel]	PhoneNumbersDialogViewModel disposed
[2025-08-09 05:06:50.144]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PhoneNumbersDialogViewModel (3 resources, 0 event subscriptions)
[2025-08-09 05:06:50.156]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PhoneNumbersDialogViewModel (0 handlers)
[2025-08-09 05:06:50.162]  	[INFO]		[PhoneNumbersDialogViewModel]	PhoneNumbersDialogViewModel disposed
[2025-08-09 05:06:50.175]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PhoneNumbersDialogViewModel (3 resources, 0 event subscriptions)
[2025-08-09 05:06:50.181]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PhoneNumbersDialogViewModel (0 handlers)
[2025-08-09 05:06:50.186]  	[INFO]		[PhoneNumbersDialogViewModel]	PhoneNumbersDialogViewModel disposed
[2025-08-09 05:06:50.197]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PhoneNumbersDialogViewModel (3 resources, 0 event subscriptions)
[2025-08-09 05:06:50.207]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PhoneNumbersDialogViewModel (0 handlers)
[2025-08-09 05:06:50.226]  	[INFO]		[PhoneNumbersDialogViewModel]	PhoneNumbersDialogViewModel disposed
[2025-08-09 05:06:50.236]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NoteListDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.255]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NoteListDialogViewModel (0 handlers)
[2025-08-09 05:06:50.269]  	[INFO]		[NoteListDialogViewModel]	NoteListDialogViewModel disposed
[2025-08-09 05:06:50.275]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NoteListDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.283]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NoteListDialogViewModel (0 handlers)
[2025-08-09 05:06:50.295]  	[INFO]		[NoteListDialogViewModel]	NoteListDialogViewModel disposed
[2025-08-09 05:06:50.300]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ImageManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.304]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ImageManagementViewModel (0 handlers)
[2025-08-09 05:06:50.308]  	[INFO]		[ImageManagementViewModel]	ImageManagementViewModel disposed
[2025-08-09 05:06:50.312]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ImageManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.316]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ImageManagementViewModel (0 handlers)
[2025-08-09 05:06:50.320]  	[INFO]		[ImageManagementViewModel]	ImageManagementViewModel disposed
[2025-08-09 05:06:50.324]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.328]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 05:06:50.333]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 05:06:50.336]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.340]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 05:06:50.344]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 05:06:50.353]  	[INFO]		[PaymentYearsSelectionDialogViewModel]	PaymentYearsSelectionDialogViewModel disposed
[2025-08-09 05:06:50.357]  	[INFO]		[PaymentYearsSelectionDialogViewModel]	PaymentYearsSelectionDialogViewModel disposed
[2025-08-09 05:06:50.361]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PaymentYearsSelectionDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.365]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PaymentYearsSelectionDialogViewModel (0 handlers)
[2025-08-09 05:06:50.369]  	[INFO]		[PaymentYearsSelectionDialogViewModel]	PaymentYearsSelectionDialogViewModel disposed
[2025-08-09 05:06:50.373]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PaymentYearsSelectionDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.377]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PaymentYearsSelectionDialogViewModel (0 handlers)
[2025-08-09 05:06:50.380]  	[INFO]		[PaymentYearsSelectionDialogViewModel]	PaymentYearsSelectionDialogViewModel disposed
[2025-08-09 05:06:50.389]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.393]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 05:06:50.397]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 05:06:50.401]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.405]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 05:06:50.415]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 05:06:50.420]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.424]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-09 05:06:50.428]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-09 05:06:50.432]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.437]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-09 05:06:50.442]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-09 05:06:50.447]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.452]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 05:06:50.459]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 05:06:50.464]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.468]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 05:06:50.472]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 05:06:50.477]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.481]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 05:06:50.485]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 05:06:50.491]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.497]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 05:06:50.509]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 05:06:50.516]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.524]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 05:06:50.533]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 05:06:50.542]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 05:06:50.552]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 05:06:50.561]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 05:06:50.579]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 16 tracked, 27 disposed, 1 cleanups
[2025-08-09 05:06:50.587]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 05:06:50.595]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 05:06:50.604]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 05:06:50.613]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 05:06:50.631]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 98.2%, Total validations: 165
[2025-08-09 05:06:50.642]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 05:06:50 ===
