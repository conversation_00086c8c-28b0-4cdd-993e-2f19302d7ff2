using System;
using System.Text;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using UFU2.Services;
using UFU2.Services.Interfaces;
using UFU2.Common;
using UFU2.Common.Behaviors;
using UFU2.Windows;
using UFU2.ViewModels;
using MaterialDesignThemes.Wpf;

namespace UFU2
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        #region Private Fields

        private CustomWindowChromeViewModel? _windowChromeViewModel;
        private IWindowChromeService? _windowChromeService;

        #endregion
        public MainWindow()
        {
            InitializeComponent();

            // Initialize custom window chrome
            InitializeCustomWindowChrome();

            // Initialize keyboard support for accessibility
            InitializeKeyboardSupport();

            // Initialize ToastService for desktop-only notifications
            try
            {
                ToastService.Initialize(ToastPosition.BottomRight);
                LoggingService.LogDebug("ToastService initialized successfully in desktop-only mode", "MainWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to initialize ToastService: {ex.Message}", "MainWindow");
            }

            // Subscribe to theme changes for dynamic icon updates
            ThemeService.ThemeChanged += OnThemeChanged;

            // Set initial icon based on current theme
            UpdateThemeIcon();

            // Start background initialization for performance optimization
            _ = Task.Run(InitializeBackgroundServicesAsync);
        }

        /// <summary>
        /// Initializes background services for improved performance.
        /// </summary>
        private async Task InitializeBackgroundServicesAsync()
        {
            try
            {
                await Task.Delay(3000); // Wait 3 seconds after startup to avoid blocking initialization

                var backgroundService = ServiceLocator.GetService<BackgroundViewInitializationService>();
                var memoryService = ServiceLocator.GetService<ViewMemoryOptimizationService>();
                var monitoringService = ServiceLocator.GetService<ViewLoadingMonitoringService>();

                if (backgroundService != null)
                {
                    // Queue background preloading of common data
                    backgroundService.QueueBackgroundInitialization(
                        "MainWindow_CommonData",
                        async (cancellationToken) =>
                        {
                            // Preload common services data
                            var activityTypeService = ServiceLocator.GetService<ActivityTypeBaseService>();
                            var validationService = ServiceLocator.GetService<ValidationService>();

                            if (activityTypeService != null)
                            {
                                await activityTypeService.GetAllAsync();
                            }

                            LoggingService.LogInfo("Background common data preloading completed", "MainWindow");
                        },
                        BackgroundTaskPriority.Low);
                }

                if (memoryService != null)
                {
                    // Register MainWindow for memory tracking
                    memoryService.RegisterView("MainWindow", this, 25.0); // Estimated 25MB
                }

                LoggingService.LogInfo("Background services initialized successfully", "MainWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing background services: {ex.Message}", "MainWindow");
            }
        }

        #region Custom Window Chrome Initialization

        /// <summary>
        /// Initializes the custom window chrome functionality
        /// </summary>
        private void InitializeCustomWindowChrome()
        {
            try
            {
                LoggingService.LogDebug("Initializing custom window chrome for MainWindow", "MainWindow");

                // Register WindowChromeService if not already registered
                if (!ServiceLocator.IsServiceRegistered<IWindowChromeService>())
                {
                    LoggingService.LogDebug("Registering WindowChromeService", "MainWindow");
                    ServiceLocator.RegisterService<IWindowChromeService>(new WindowChromeService());
                    LoggingService.LogDebug("WindowChromeService registered in ServiceLocator", "MainWindow");
                }

                // Get WindowChromeService from ServiceLocator
                LoggingService.LogDebug("Getting WindowChromeService from ServiceLocator", "MainWindow");
                _windowChromeService = ServiceLocator.GetService<IWindowChromeService>();
                LoggingService.LogDebug("WindowChromeService retrieved successfully", "MainWindow");

                // Create and configure CustomWindowChromeViewModel
                LoggingService.LogDebug("Creating CustomWindowChromeViewModel", "MainWindow");
                _windowChromeViewModel = new CustomWindowChromeViewModel(this);
                LoggingService.LogDebug("CustomWindowChromeViewModel created successfully", "MainWindow");
                
                // Set window properties from ViewModel
                LoggingService.LogDebug("Setting window properties", "MainWindow");
                _windowChromeViewModel.WindowTitle = this.Title;
                _windowChromeViewModel.WindowIcon = this.Icon;
                _windowChromeViewModel.UpdateWindowState(this.WindowState);
                LoggingService.LogDebug("Window properties set successfully", "MainWindow");

                // Set the ViewModel as DataContext for window chrome bindings
                LoggingService.LogDebug("Setting DataContext", "MainWindow");
                this.DataContext = _windowChromeViewModel;
                LoggingService.LogDebug("DataContext set successfully", "MainWindow");

                // Apply WindowChrome using WindowChromeService
                LoggingService.LogDebug("Applying WindowChrome", "MainWindow");
                _windowChromeService.ConfigureWindowChrome(this, _windowChromeViewModel);
                LoggingService.LogDebug("WindowChrome applied successfully", "MainWindow");

                // Subscribe to window state changes for ViewModel synchronization
                this.StateChanged += OnMainWindowStateChanged;
                LoggingService.LogDebug("Subscribed to window StateChanged event", "MainWindow");

                // Initialize title bar behaviors after the window is loaded
                this.Loaded += OnMainWindowLoaded;

                LoggingService.LogDebug("Custom window chrome initialized successfully", "MainWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error initializing custom window chrome: {ex.Message}", "MainWindow");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "MainWindow");
                
                // Show Arabic error message to user
                ErrorManager.HandleErrorToast(ex,
                    "فشل في تهيئة إطار النافذة المخصص. سيتم استخدام الإطار الافتراضي",
                    "خطأ في تهيئة النافذة",
                    LogLevel.Warning,
                    "MainWindow"
                );
                
                // Clean up any partially initialized resources
                try
                {
                    if (_windowChromeService != null && _windowChromeService is IDisposable disposableService)
                    {
                        disposableService.Dispose();
                        _windowChromeService = null;
                        LoggingService.LogDebug("Disposed partially initialized WindowChromeService", "MainWindow");
                    }
                    
                    if (_windowChromeViewModel != null && _windowChromeViewModel is IDisposable disposableViewModel)
                    {
                        disposableViewModel.Dispose();
                        _windowChromeViewModel = null;
                        LoggingService.LogDebug("Disposed partially initialized CustomWindowChromeViewModel", "MainWindow");
                    }
                }
                catch (Exception cleanupEx)
                {
                    LoggingService.LogError($"Error during chrome initialization cleanup: {cleanupEx.Message}", "MainWindow");
                }
                
                // Continue without custom chrome - window will use default chrome
                LoggingService.LogDebug("Continuing without custom window chrome - using default system chrome", "MainWindow");
            }
        }

        /// <summary>
        /// Cleans up custom window chrome resources and event subscriptions
        /// </summary>
        private void CleanupCustomWindowChrome()
        {
            try
            {
                LoggingService.LogDebug("Cleaning up custom window chrome", "MainWindow");

                // Unsubscribe from window state changes to prevent memory leaks
                this.StateChanged -= OnMainWindowStateChanged;
                LoggingService.LogDebug("Unsubscribed from window StateChanged event", "MainWindow");

                // Dispose WindowChromeService if it implements IDisposable
                if (_windowChromeService is IDisposable disposableService)
                {
                    disposableService.Dispose();
                    LoggingService.LogDebug("WindowChromeService disposed", "MainWindow");
                }

                // Dispose CustomWindowChromeViewModel if it implements IDisposable
                if (_windowChromeViewModel is IDisposable disposableViewModel)
                {
                    disposableViewModel.Dispose();
                    LoggingService.LogDebug("CustomWindowChromeViewModel disposed", "MainWindow");
                }

                // Clear references
                _windowChromeService = null;
                _windowChromeViewModel = null;

                LoggingService.LogDebug("Custom window chrome cleanup completed", "MainWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during custom window chrome cleanup: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Handles the window loaded event to initialize title bar behaviors
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Event arguments</param>
        private void OnMainWindowLoaded(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug("MainWindow loaded, initializing title bar behaviors", "MainWindow");

                // Find the title bar element in the window template
                var titleBar = this.Template?.FindName("TitleBar", this) as FrameworkElement;
                if (titleBar != null)
                {
                    // Set the target window for the title bar behavior
                    TitleBarBehavior.SetTargetWindow(titleBar, this);
                    LoggingService.LogDebug("Title bar behavior target window set successfully", "MainWindow");
                }
                else
                {
                    LoggingService.LogWarning("Could not find TitleBar element in window template", "MainWindow");
                }

                // Unsubscribe from the loaded event as we only need to do this once
                this.Loaded -= OnMainWindowLoaded;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing title bar behaviors: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Handles window state changes to synchronize the CustomWindowChromeViewModel
        /// This ensures the maximize/restore button icon updates correctly for all interaction methods
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Event arguments</param>
        private void OnMainWindowStateChanged(object sender, EventArgs e)
        {
            try
            {
                if (_windowChromeViewModel != null)
                {
                    LoggingService.LogDebug($"MainWindow state changed to: {this.WindowState}, synchronizing ViewModel", "MainWindow");

                    // Update ViewModel to reflect the new window state
                    _windowChromeViewModel.UpdateWindowState(this.WindowState);

                    LoggingService.LogDebug($"ViewModel synchronized with window state: {this.WindowState}", "MainWindow");
                }
                else
                {
                    LoggingService.LogWarning("Cannot synchronize window state: CustomWindowChromeViewModel is null", "MainWindow");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error synchronizing window state with ViewModel: {ex.Message}", "MainWindow");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "MainWindow");

                // Show Arabic error message to user if this affects UI functionality
                try
                {
                    ErrorManager.HandleWarningToast(ex,
                        "فشل في تحديث حالة النافذة. قد لا تعمل أزرار التحكم بالنافذة بشكل صحيح",
                        "خطأ في تحديث واجهة المستخدم",
                        "MainWindow"
                    );
                }
                catch (Exception toastEx)
                {
                    LoggingService.LogError($"Error showing state synchronization warning toast: {toastEx.Message}", "MainWindow");
                    
                    // Fallback to console/debug output
                    LoggingService.LogError("Failed to show user notification about window state synchronization error", "MainWindow");
                }

                // Attempt to recover by resetting the ViewModel state
                try
                {
                    if (_windowChromeViewModel != null)
                    {
                        LoggingService.LogDebug("Attempting to recover window state synchronization", "MainWindow");
                        
                        // Force update the ViewModel with current window state
                        Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            try
                            {
                                _windowChromeViewModel.UpdateWindowState(this.WindowState);
                                LoggingService.LogDebug("Window state synchronization recovered successfully", "MainWindow");
                            }
                            catch (Exception recoveryEx)
                            {
                                LoggingService.LogError($"Failed to recover window state synchronization: {recoveryEx.Message}", "MainWindow");
                            }
                        }));
                    }
                }
                catch (Exception recoveryEx)
                {
                    LoggingService.LogError($"Error during window state synchronization recovery: {recoveryEx.Message}", "MainWindow");
                }
            }
        }

        #endregion

        #region Keyboard Support and Accessibility

        /// <summary>
        /// Initializes keyboard support and accessibility features for the main window
        /// </summary>
        private void InitializeKeyboardSupport()
        {
            try
            {
                LoggingService.LogDebug("Initializing keyboard support and accessibility features", "MainWindow");

                // Set up global keyboard event handlers
                this.PreviewKeyDown += OnMainWindowPreviewKeyDown;
                this.KeyDown += OnMainWindowKeyDown;

                // Set up accessibility properties for the main window
                AutomationProperties.SetName(this, "UFU2 - نظام إدارة العملاء");
                AutomationProperties.SetHelpText(this, "نافذة التطبيق الرئيسية لنظام إدارة العملاء UFU2");

                // Ensure the window is properly configured for accessibility
                this.IsTabStop = false; // Main window shouldn't be a tab stop
                
                LoggingService.LogDebug("Keyboard support and accessibility features initialized successfully", "MainWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing keyboard support: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Handles preview key down events for system-wide keyboard shortcuts
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Key event arguments</param>
        private void OnMainWindowPreviewKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // Handle system keyboard shortcuts
                if (e.Key == Key.F4 && (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    // Alt+F4 - Close application
                    LoggingService.LogDebug("Alt+F4 detected in MainWindow, closing application", "MainWindow");
                    this.Close();
                    e.Handled = true;
                }
                else if (e.Key == Key.Space && (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    // Alt+Space - Show system menu
                    LoggingService.LogDebug("Alt+Space detected, showing system menu", "MainWindow");
                    ShowSystemMenu();
                    e.Handled = true;
                }
                else if (e.Key == Key.F9 && (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    // Alt+F9 - Minimize window
                    LoggingService.LogDebug("Alt+F9 detected, minimizing window", "MainWindow");
                    this.WindowState = WindowState.Minimized;
                    e.Handled = true;
                }
                else if (e.Key == Key.F10 && (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    // Alt+F10 - Toggle maximize/restore
                    LoggingService.LogDebug("Alt+F10 detected, toggling maximize/restore", "MainWindow");
                    this.WindowState = this.WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
                    e.Handled = true;
                }
                else if (e.Key == Key.F1)
                {
                    // F1 - Show help (future implementation)
                    LoggingService.LogDebug("F1 detected, help requested", "MainWindow");
                    ShowHelp();
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling main window preview key down: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Handles key down events for additional keyboard functionality
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Key event arguments</param>
        private void OnMainWindowKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // Handle additional keyboard shortcuts
                if (e.Key == Key.N && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
                {
                    // Ctrl+N - New client (same as clicking AddUserButton)
                    LoggingService.LogDebug("Ctrl+N detected, opening new client dialog", "MainWindow");
                    AddUserButton_Click(AddUserButton, new RoutedEventArgs());
                    e.Handled = true;
                }
                else if (e.Key == Key.T && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
                {
                    // Ctrl+T - Toggle theme
                    LoggingService.LogDebug("Ctrl+T detected, toggling theme", "MainWindow");
                    ThemeToggleButton_Click(ThemeToggleButton, new RoutedEventArgs());
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling main window key down: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Shows the system menu for the window
        /// </summary>
        private void ShowSystemMenu()
        {
            try
            {
                LoggingService.LogDebug("Showing system menu", "MainWindow");
                
                // Get the window handle
                var hwnd = new System.Windows.Interop.WindowInteropHelper(this).Handle;
                
                if (hwnd != IntPtr.Zero)
                {
                    // Show system menu at the top-left corner of the window
                    var point = this.PointToScreen(new Point(0, 0));
                    
                    // Use Windows API to show system menu
                    ShowSystemMenuAtPoint(hwnd, (int)point.X, (int)point.Y);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing system menu: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Shows help information (placeholder for future implementation)
        /// </summary>
        private void ShowHelp()
        {
            try
            {
                LoggingService.LogDebug("Help requested by user", "MainWindow");
                
                // For now, show a simple message
                ErrorManager.ShowUserInfoToast(
                    "مساعدة التطبيق ستكون متاحة في الإصدارات القادمة",
                    "المساعدة",
                    "MainWindow"
                );
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing help: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Native method to show system menu at specific coordinates
        /// </summary>
        /// <param name="hwnd">Window handle</param>
        /// <param name="x">X coordinate</param>
        /// <param name="y">Y coordinate</param>
        private void ShowSystemMenuAtPoint(IntPtr hwnd, int x, int y)
        {
            try
            {
                // This would require P/Invoke to show the actual system menu
                // For now, we'll log that the system menu was requested
                LoggingService.LogDebug($"System menu requested at coordinates ({x}, {y})", "MainWindow");
                
                // The actual system menu will be handled by Windows when Alt+Space is not handled
                // We're just logging the request here for accessibility compliance
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing system menu at point: {ex.Message}", "MainWindow");
            }
        }

        

        #endregion

        /// <summary>
        /// Event handler for theme toggle button click
        /// Switches between Light and Dark themes with proper error handling
        /// </summary>
        /// <param name="sender">The button that was clicked</param>
        /// <param name="e">Event arguments</param>
        private async void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug("User initiated theme toggle", "MainWindow");

                // Disable button during theme switch to prevent multiple clicks
                ThemeToggleButton.IsEnabled = false;

                // Toggle the theme
                var success = await ThemeService.ToggleThemeAsync();

                if (!success)
                {
                    LoggingService.LogWarning("Theme toggle failed", "MainWindow");
                    ErrorManager.ShowUserWarningToast("فشل في تبديل المظهر. يرجى المحاولة مرة أخرى.", "خطأ في تبديل المظهر", "MainWindow");
                }
                else
                {
                    LoggingService.LogDebug($"Theme successfully switched to {ThemeService.CurrentTheme}", "MainWindow");
                    ErrorManager.ShowUserInfoToast($"تم تبديل المظهر إلى {(ThemeService.CurrentTheme == ApplicationTheme.Dark ? "الداكن" : "الفاتح")} بنجاح", "تبديل المظهر", "MainWindow");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during theme toggle: {ex.Message}", "MainWindow");

                // Use ErrorManager for consistent Arabic RTL error handling
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تبديل المظهر. يرجى المحاولة مرة أخرى أو إعادة تشغيل التطبيق.",
                    "خطأ في تبديل المظهر",
                    LogLevel.Error,
                    "MainWindow");
            }
            finally
            {
                // Re-enable button
                ThemeToggleButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Event handler for theme changes
        /// Updates the theme toggle icon based on the current theme
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Theme change event arguments</param>
        private void OnThemeChanged(object? sender, UFU2.Services.ThemeChangedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo($"Theme changed from {e.PreviousTheme} to {e.NewTheme}, updating icon", "MainWindow");
                UpdateThemeIcon();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating theme icon: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Updates the theme toggle icon based on the current theme
        /// Sun icon for dark theme (suggests switching to light)
        /// Moon icon for light theme (suggests switching to dark)
        /// </summary>
        private void UpdateThemeIcon()
        {
            try
            {
                if (ThemeService.IsInitialized)
                {
                    // Update icon based on current theme
                    // Show sun icon when in dark theme (suggests switching to light)
                    // Show moon icon when in light theme (suggests switching to dark)
                    ThemeToggleIcon.Kind = ThemeService.CurrentTheme == ApplicationTheme.Dark
                        ? PackIconKind.WeatherSunny  // Sun icon for dark theme
                        : PackIconKind.WeatherNight; // Moon icon for light theme

                    // Update tooltip based on current theme
                    ThemeToggleButton.ToolTip = ThemeService.CurrentTheme == ApplicationTheme.Dark
                        ? "تبديل إلى المظهر الفاتح" // Switch to light theme
                        : "تبديل إلى المظهر الداكن"; // Switch to dark theme
                }
                else
                {
                    // Default icon if theme manager not initialized
                    ThemeToggleIcon.Kind = PackIconKind.Brightness6;
                    ThemeToggleButton.ToolTip = "تبديل المظهر (فاتح/داكن)";
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating theme icon: {ex.Message}", "MainWindow");
                // Fallback to default icon
                ThemeToggleIcon.Kind = PackIconKind.Brightness6;
            }
        }

        /// <summary>
        /// Cleanup when window is closing
        /// </summary>
        /// <param name="e">Closing event arguments</param>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Application closing", "MainWindow");

                // Show exit confirmation dialog
                bool shouldExit = ConfirmationWindow.ShowExitConfirmation("UFU2", this, true);

                if (!shouldExit)
                {
                    // User cancelled the exit
                    e.Cancel = true;
                    LoggingService.LogInfo("Application exit cancelled by user", "MainWindow");
                    return;
                }

                // Cleanup custom window chrome
                CleanupCustomWindowChrome();

                // Unsubscribe from theme changes to prevent memory leaks
                ThemeService.ThemeChanged -= OnThemeChanged;

                // Cleanup keyboard event handlers
                CleanupKeyboardSupport();

                // Perform any additional cleanup here
                PerformApplicationCleanup();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during window closing: {ex.Message}", "MainWindow");
            }

            base.OnClosing(e);
        }

        /// <summary>
        /// Cleans up keyboard support event handlers
        /// </summary>
        private void CleanupKeyboardSupport()
        {
            try
            {
                LoggingService.LogDebug("Cleaning up keyboard support", "MainWindow");

                // Remove keyboard event handlers
                this.PreviewKeyDown -= OnMainWindowPreviewKeyDown;
                this.KeyDown -= OnMainWindowKeyDown;

                LoggingService.LogDebug("Keyboard support cleanup completed", "MainWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error cleaning up keyboard support: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Performs application cleanup operations before exit
        /// </summary>
        private void PerformApplicationCleanup()
        {
            try
            {
                LoggingService.LogInfo("Performing application cleanup", "MainWindow");

                // Close all toast notifications
                try
                {
                    ToastService.CloseAll();
                    LoggingService.LogInfo("Toast notifications closed", "MainWindow");
                }
                catch (Exception ex)
                {
                    LoggingService.LogWarning($"Error closing toast notifications: {ex.Message}", "MainWindow");
                }

                // Additional cleanup operations can be added here
                // For example: saving user preferences, closing connections, etc.

                LoggingService.LogInfo("Application cleanup completed", "MainWindow");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during application cleanup: {ex.Message}", "MainWindow");
            }
        }

        /// <summary>
        /// Event handler for AddUserButton click
        /// Opens the NewClientView as a modal dialog
        /// </summary>
        /// <param name="sender">The button that was clicked</param>
        /// <param name="e">Event arguments</param>
        private async void AddUserButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("User clicked AddUserButton", "MainWindow");

                // Disable button during dialog operation to prevent multiple clicks
                AddUserButton.IsEnabled = false;

                // Start performance monitoring for dialog creation
                var monitoringService = ServiceLocator.GetService<ViewLoadingMonitoringService>();
                var dialogStopwatch = monitoringService?.StartViewLoading("NewClientDialog", "NewClientView", ViewLoadingType.OnDemand) ?? System.Diagnostics.Stopwatch.StartNew();

                // Create NewClientView
                var newClientView = new UFU2.Views.NewClientView();

                // Complete dialog creation monitoring
                monitoringService?.CompleteViewLoading("NewClientDialog", dialogStopwatch, true);

                // Calculate optimized dialog size using DialogSizeCalculator service
                var dialogSize = DialogSizeCalculator.GetNewClientDialogSize(this);

                // Apply the calculated size to the main card inside the NewClientView
                if (newClientView.FindName("MainCard") is FrameworkElement mainCard)
                {
                    dialogSize.ApplyTo(mainCard);
                }
                else
                {
                    // Fallback: apply size to the UserControl itself
                    dialogSize.ApplyTo(newClientView);
                }

                LoggingService.LogDebug($"Opening NewClientView dialog with optimized size: {dialogSize.Width:F0}x{dialogSize.Height:F0} (Height-based width calculation)", "MainWindow");

                // Show the dialog using MaterialDesign DialogHost
                var result = await MaterialDesignThemes.Wpf.DialogHost.Show(newClientView, "RootDialog");

                // Handle dialog result if needed
                if (result is bool dialogResult && dialogResult)
                {
                    LoggingService.LogInfo("NewClientView dialog completed successfully", "MainWindow");
                    // TODO: Handle successful client creation (e.g., refresh client list)
                }
                else
                {
                    LoggingService.LogInfo("NewClientView dialog was cancelled", "MainWindow");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening NewClientView dialog: {ex.Message}", "MainWindow");

                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح نافذة إضافة العميل. يرجى المحاولة مرة أخرى.",
                    "خطأ في فتح النافذة",
                    LogLevel.Error,
                    "MainWindow");
            }
            finally
            {
                // Re-enable button
                AddUserButton.IsEnabled = true;
            }
        }



    }
}