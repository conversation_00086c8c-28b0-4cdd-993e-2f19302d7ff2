using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UFU2.Common.Models;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Manages error toast deduplication to prevent cascading error notifications.
    /// Tracks operation contexts and ensures only one error toast is displayed per operation
    /// while maintaining complete technical logging for debugging purposes.
    /// 
    /// Thread-safe implementation using ConcurrentDictionary for concurrent operation handling.
    /// </summary>
    public static class ErrorDeduplicationManager
    {
        #region Private Fields

        /// <summary>
        /// Thread-safe dictionary to track active operations and their contexts.
        /// Key: OperationId, Value: ErrorOperationContext
        /// </summary>
        private static readonly ConcurrentDictionary<string, ErrorOperationContext> _activeOperations 
            = new ConcurrentDictionary<string, ErrorOperationContext>();

        /// <summary>
        /// Lock object for thread-safe cleanup operations.
        /// </summary>
        private static readonly object _cleanupLock = new object();

        /// <summary>
        /// Timer for periodic cleanup of expired operations.
        /// </summary>
        private static Timer? _cleanupTimer;

        /// <summary>
        /// Default operation timeout in seconds.
        /// </summary>
        private const int DefaultTimeoutSeconds = 30;

        /// <summary>
        /// Cleanup interval in milliseconds.
        /// </summary>
        private const int CleanupIntervalMs = 60000; // 1 minute

        /// <summary>
        /// Flag to track if the cleanup timer has been initialized.
        /// </summary>
        private static bool _isInitialized = false;

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes the ErrorDeduplicationManager with periodic cleanup.
        /// Called automatically on first use.
        /// </summary>
        private static void EnsureInitialized()
        {
            if (_isInitialized) return;

            lock (_cleanupLock)
            {
                if (_isInitialized) return;

                // Start periodic cleanup timer
                _cleanupTimer = new Timer(CleanupCallback, null, CleanupIntervalMs, CleanupIntervalMs);
                _isInitialized = true;

                LoggingService.LogDebug("ErrorDeduplicationManager initialized with periodic cleanup", "ErrorDeduplicationManager");
            }
        }

        /// <summary>
        /// Cleanup timer callback to remove expired operations.
        /// </summary>
        private static void CleanupCallback(object? state)
        {
            try
            {
                CleanupExpiredOperations();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cleanup: {ex.Message}", "ErrorDeduplicationManager");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Starts tracking a new operation and returns a unique operation ID.
        /// </summary>
        /// <param name="operationType">The type of operation (e.g., "ClientCreation")</param>
        /// <param name="source">The source component initiating the operation</param>
        /// <param name="properties">Optional additional properties for context</param>
        /// <returns>Unique operation ID for tracking</returns>
        public static string StartOperation(string operationType, string source, Dictionary<string, object>? properties = null)
        {
            EnsureInitialized();

            if (string.IsNullOrWhiteSpace(operationType))
                throw new ArgumentException("Operation type cannot be null or empty", nameof(operationType));

            if (string.IsNullOrWhiteSpace(source))
                throw new ArgumentException("Source cannot be null or empty", nameof(source));

            // Generate unique operation ID
            string operationId = Guid.NewGuid().ToString("N")[..8]; // Use first 8 characters for readability

            // Create operation context
            var context = new ErrorOperationContext(operationId, operationType, source);
            
            if (properties != null)
            {
                foreach (var prop in properties)
                {
                    context.Properties[prop.Key] = prop.Value;
                }
            }

            // Add to active operations
            _activeOperations.TryAdd(operationId, context);

            LoggingService.LogDebug($"Started operation tracking: {context.GetSummary()}", "ErrorDeduplicationManager");
            return operationId;
        }

        /// <summary>
        /// Determines whether an error toast should be displayed for the given operation.
        /// Implements severity-based prioritization and deduplication logic.
        /// </summary>
        /// <param name="operationId">The operation ID (can be null for non-tracked operations)</param>
        /// <param name="severity">The severity of the current error</param>
        /// <param name="source">The source component reporting the error</param>
        /// <param name="exception">The exception that occurred</param>
        /// <param name="userMessage">The user-friendly message</param>
        /// <returns>True if the toast should be displayed, false if it should be suppressed</returns>
        public static bool ShouldDisplayToast(string? operationId, ErrorSeverity severity, string source, 
            Exception? exception = null, string userMessage = "")
        {
            // If no operation ID provided, allow the toast (backward compatibility)
            if (string.IsNullOrWhiteSpace(operationId))
            {
                LoggingService.LogDebug($"No operation ID provided, allowing toast from {source}", "ErrorDeduplicationManager");
                return true;
            }

            // Try to get the operation context
            if (!_activeOperations.TryGetValue(operationId, out var context))
            {
                LoggingService.LogWarning($"Operation ID {operationId} not found, allowing toast from {source}", "ErrorDeduplicationManager");
                return true;
            }

            // Add error to history
            if (exception != null)
            {
                context.AddError(exception, source, severity, userMessage);
            }

            // Check if a toast has already been displayed
            if (context.HasDisplayedToast)
            {
                // Allow higher severity errors to override lower severity ones
                if (severity > context.DisplayedSeverity)
                {
                    LoggingService.LogInfo($"Higher severity error ({severity}) overriding previous toast ({context.DisplayedSeverity}) for operation {operationId}", 
                        "ErrorDeduplicationManager");
                    return true;
                }
                else
                {
                    LoggingService.LogInfo($"Suppressing duplicate/lower severity error toast for operation {operationId}. " +
                        $"Current: {severity}, Displayed: {context.DisplayedSeverity}", "ErrorDeduplicationManager");
                    return false;
                }
            }

            // No toast displayed yet, allow this one
            LoggingService.LogDebug($"Allowing first error toast for operation {operationId} with severity {severity}", "ErrorDeduplicationManager");
            return true;
        }

        /// <summary>
        /// Marks that an error toast has been displayed for the given operation.
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <param name="severity">The severity of the displayed error</param>
        /// <param name="source">The source that displayed the toast</param>
        /// <param name="message">The message that was displayed</param>
        public static void MarkToastDisplayed(string? operationId, ErrorSeverity severity, string source, string message)
        {
            if (string.IsNullOrWhiteSpace(operationId)) return;

            if (_activeOperations.TryGetValue(operationId, out var context))
            {
                context.MarkToastDisplayed(severity, source, message);
                LoggingService.LogDebug($"Marked toast as displayed for operation {operationId}: {severity} from {source}", 
                    "ErrorDeduplicationManager");
            }
        }

        /// <summary>
        /// Completes an operation and removes it from tracking.
        /// Should be called when an operation finishes (success or failure).
        /// </summary>
        /// <param name="operationId">The operation ID to complete</param>
        public static void CompleteOperation(string? operationId)
        {
            if (string.IsNullOrWhiteSpace(operationId)) return;

            if (_activeOperations.TryRemove(operationId, out var context))
            {
                LoggingService.LogDebug($"Completed operation tracking: {context.GetSummary()}", "ErrorDeduplicationManager");
            }
        }

        /// <summary>
        /// Removes expired operations from tracking to prevent memory leaks.
        /// Called automatically by the cleanup timer.
        /// </summary>
        public static void CleanupExpiredOperations()
        {
            var expiredOperations = new List<string>();

            // Find expired operations
            foreach (var kvp in _activeOperations)
            {
                if (kvp.Value.IsExpired(DefaultTimeoutSeconds))
                {
                    expiredOperations.Add(kvp.Key);
                }
            }

            // Remove expired operations
            int removedCount = 0;
            foreach (var operationId in expiredOperations)
            {
                if (_activeOperations.TryRemove(operationId, out var context))
                {
                    removedCount++;
                    LoggingService.LogDebug($"Removed expired operation: {context.GetSummary()}", "ErrorDeduplicationManager");
                }
            }

            if (removedCount > 0)
            {
                LoggingService.LogInfo($"Cleaned up {removedCount} expired operations. Active operations: {_activeOperations.Count}", 
                    "ErrorDeduplicationManager");
            }
        }

        /// <summary>
        /// Gets the current number of active operations being tracked.
        /// </summary>
        /// <returns>Number of active operations</returns>
        public static int GetActiveOperationCount()
        {
            return _activeOperations.Count;
        }

        /// <summary>
        /// Gets a summary of all active operations for debugging purposes.
        /// </summary>
        /// <returns>List of operation summaries</returns>
        public static List<string> GetActiveOperationSummaries()
        {
            return _activeOperations.Values.Select(context => context.GetSummary()).ToList();
        }

        /// <summary>
        /// Gets the operation context for a given operation ID.
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <returns>The operation context, or null if not found</returns>
        public static ErrorOperationContext? GetOperationContext(string? operationId)
        {
            if (string.IsNullOrWhiteSpace(operationId)) return null;

            _activeOperations.TryGetValue(operationId, out var context);
            return context;
        }

        /// <summary>
        /// Forces cleanup of all active operations. Used for testing and shutdown scenarios.
        /// </summary>
        public static void ClearAllOperations()
        {
            var count = _activeOperations.Count;
            _activeOperations.Clear();
            LoggingService.LogInfo($"Cleared all {count} active operations", "ErrorDeduplicationManager");
        }

        #endregion

        #region Shutdown

        /// <summary>
        /// Shuts down the ErrorDeduplicationManager and cleans up resources.
        /// Should be called during application shutdown.
        /// </summary>
        public static void Shutdown()
        {
            lock (_cleanupLock)
            {
                _cleanupTimer?.Dispose();
                _cleanupTimer = null;
                _isInitialized = false;
                
                ClearAllOperations();
                LoggingService.LogInfo("ErrorDeduplicationManager shut down", "ErrorDeduplicationManager");
            }
        }

        #endregion
    }
}
