using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that maps Priority integer values to theme color resources.
    /// Maps integer values (0=Normal, 1=Medium, 2=High) to corresponding StaticResource color keys.
    /// Replaces the previous FlagTypeToThemeColorConverter with Priority-based system.
    /// 
    /// Priority Mapping:
    /// - 0 = Normal (Green) → NormaleFlag
    /// - 1 = Medium (Orange) → ImportantFlag  
    /// - 2 = High (Red) → VeryImportantFlag
    /// </summary>
    public class PriorityToThemeColorConverter : IValueConverter
    {
        /// <summary>
        /// Converts a Priority integer value to the corresponding theme color brush.
        /// </summary>
        /// <param name="value">The Priority integer value (0, 1, or 2)</param>
        /// <param name="targetType">The target type (SolidColorBrush)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>SolidColorBrush from theme resources</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value is int priority)
                {
                    // Get the theme color resource key based on priority level
                    string resourceKey = GetThemeColorResourceKey(priority);
                    
                    // Try to find the color resource in the application resources
                    if (Application.Current?.Resources[resourceKey] is Color color)
                    {
                        return new SolidColorBrush(color);
                    }
                }

                // Default fallback to NormaleFlag color if not found
                if (Application.Current?.Resources["NormaleFlag"] is Color defaultColor)
                {
                    return new SolidColorBrush(defaultColor);
                }
                
                // Ultimate fallback to green
                return new SolidColorBrush(Colors.Green);
            }
            catch (Exception)
            {
                // Fallback to green color on any error
                return new SolidColorBrush(Colors.Green);
            }
        }

        /// <summary>
        /// Converts back from color to Priority (not implemented as this is one-way).
        /// </summary>
        /// <param name="value">The color value</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">Optional parameter</param>
        /// <param name="culture">The culture info</param>
        /// <returns>0 (Normal priority) as default</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // This converter is designed for one-way binding only
            return 0; // Default to Normal priority
        }

        /// <summary>
        /// Gets the theme color resource key for the specified priority level.
        /// Maps priority integer values directly to theme resource keys.
        /// </summary>
        /// <param name="priority">The priority level (0, 1, or 2)</param>
        /// <returns>Theme color resource key</returns>
        private static string GetThemeColorResourceKey(int priority)
        {
            return priority switch
            {
                0 => "NormaleFlag",        // Normal (Green) → #2EF47F
                1 => "ImportantFlag",      // Medium (Orange) → #FACC15
                2 => "VeryImportantFlag",  // High (Red) → #FF4C4C
                _ => "NormaleFlag"         // Default to normal/green
            };
        }
    }
}
