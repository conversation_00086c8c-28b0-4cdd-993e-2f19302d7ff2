-- ============================================================================
-- UFU2 Archive Database Schema
-- ============================================================================
-- This schema defines the audit trail and change tracking system for UFU2.
-- The archive database captures all modifications to client data with
-- specialized tables for different types of changes.
-- ============================================================================

-- ============================================================================
-- ARCHIVE AUDIT TABLES
-- ============================================================================

-- Table for tracking new data additions to existing entities
-- Use case: Client created with missing address, later address is added
CREATE TABLE IF NOT EXISTS AddedEntities (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    EntityType TEXT NOT NULL,           -- 'Client', 'Activity', 'PhoneNumber', etc.
    EntityId TEXT NOT NULL,             -- UID of the entity receiving new data
    DataName TEXT NOT NULL,             -- Name of the added field/data
    AddedValue TEXT NOT NULL,           -- The new value that was added
    CreatedAt TEXT DEFAULT (datetime('now')),

    -- Data validation constraints
    CONSTRAINT chk_added_entity_type_not_empty CHECK (length(trim(EntityType)) > 0),
    CONSTRAINT chk_added_entity_id_not_empty CHECK (length(trim(EntityId)) > 0),
    CONSTRAINT chk_added_data_name_not_empty CHECK (length(trim(DataName)) > 0),
    CONSTRAINT chk_added_value_not_empty CHECK (length(trim(AddedValue)) > 0)
);

-- Table for tracking data modifications with old/new value pairs
-- Use case: User removes old phone number and adds new phone number
CREATE TABLE IF NOT EXISTS UpdatedEntities (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    EntityType TEXT NOT NULL,           -- 'Client', 'Activity', 'PhoneNumber', etc.
    EntityId TEXT NOT NULL,             -- UID of the modified entity
    DataName TEXT NOT NULL,             -- Name of the modified field/data
    OldValue TEXT NOT NULL,             -- Previous value before change
    NewValue TEXT NOT NULL,             -- New value after change
    CreatedAt TEXT DEFAULT (datetime('now')),

    -- Data validation constraints
    CONSTRAINT chk_updated_entity_type_not_empty CHECK (length(trim(EntityType)) > 0),
    CONSTRAINT chk_updated_entity_id_not_empty CHECK (length(trim(EntityId)) > 0),
    CONSTRAINT chk_updated_data_name_not_empty CHECK (length(trim(DataName)) > 0),
    CONSTRAINT chk_updated_old_value_not_empty CHECK (length(trim(OldValue)) > 0),
    CONSTRAINT chk_updated_new_value_not_empty CHECK (length(trim(NewValue)) > 0)
);

-- Table for tracking data removals without replacement
-- Use case: User removes old phone number without adding a new one
CREATE TABLE IF NOT EXISTS DeletedEntities (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    EntityType TEXT NOT NULL,           -- 'Client', 'Activity', 'PhoneNumber', etc.
    EntityId TEXT NOT NULL,             -- UID of the entity losing data
    DataName TEXT NOT NULL,             -- Name of the removed field/data
    DeletedValue TEXT NOT NULL,         -- The value that was removed
    CreatedAt TEXT DEFAULT (datetime('now')),

    -- Data validation constraints
    CONSTRAINT chk_deleted_entity_type_not_empty CHECK (length(trim(EntityType)) > 0),
    CONSTRAINT chk_deleted_entity_id_not_empty CHECK (length(trim(EntityId)) > 0),
    CONSTRAINT chk_deleted_data_name_not_empty CHECK (length(trim(DataName)) > 0),
    CONSTRAINT chk_deleted_value_not_empty CHECK (length(trim(DeletedValue)) > 0)
);

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Indexes for AddedEntities table
CREATE INDEX IF NOT EXISTS idx_added_entities_lookup 
ON AddedEntities(EntityType, EntityId, CreatedAt);

CREATE INDEX IF NOT EXISTS idx_added_entities_data 
ON AddedEntities(DataName, CreatedAt);

CREATE INDEX IF NOT EXISTS idx_added_entities_timestamp 
ON AddedEntities(CreatedAt);

-- Indexes for UpdatedEntities table
CREATE INDEX IF NOT EXISTS idx_updated_entities_lookup 
ON UpdatedEntities(EntityType, EntityId, CreatedAt);

CREATE INDEX IF NOT EXISTS idx_updated_entities_data 
ON UpdatedEntities(DataName, CreatedAt);

CREATE INDEX IF NOT EXISTS idx_updated_entities_timestamp 
ON UpdatedEntities(CreatedAt);

-- Indexes for DeletedEntities table
CREATE INDEX IF NOT EXISTS idx_deleted_entities_lookup 
ON DeletedEntities(EntityType, EntityId, CreatedAt);

CREATE INDEX IF NOT EXISTS idx_deleted_entities_data 
ON DeletedEntities(DataName, CreatedAt);

CREATE INDEX IF NOT EXISTS idx_deleted_entities_timestamp 
ON DeletedEntities(CreatedAt);

-- ============================================================================
-- SCHEMA VERSION TRACKING
-- ============================================================================

-- Schema version table for migration tracking
CREATE TABLE IF NOT EXISTS ArchiveSchemaVersion (
    Version INTEGER PRIMARY KEY,
    AppliedAt TEXT DEFAULT (datetime('now')),
    Description TEXT
);

-- Insert initial schema version
INSERT OR REPLACE INTO ArchiveSchemaVersion (Version, Description) 
VALUES (1, 'Initial archive database schema with specialized audit tables');

-- ============================================================================
-- VIEWS FOR CONVENIENT QUERYING
-- ============================================================================

-- View to get complete change history for an entity
CREATE VIEW IF NOT EXISTS EntityChangeHistory AS
SELECT 
    'ADDED' as ChangeType,
    EntityType,
    EntityId,
    DataName,
    NULL as OldValue,
    AddedValue as NewValue,
    CreatedAt
FROM AddedEntities

UNION ALL

SELECT 
    'UPDATED' as ChangeType,
    EntityType,
    EntityId,
    DataName,
    OldValue,
    NewValue,
    CreatedAt
FROM UpdatedEntities

UNION ALL

SELECT 
    'DELETED' as ChangeType,
    EntityType,
    EntityId,
    DataName,
    DeletedValue as OldValue,
    NULL as NewValue,
    CreatedAt
FROM DeletedEntities

ORDER BY CreatedAt DESC;

-- View to get recent changes (last 30 days)
CREATE VIEW IF NOT EXISTS RecentChanges AS
SELECT * FROM EntityChangeHistory
WHERE CreatedAt >= datetime('now', '-30 days')
ORDER BY CreatedAt DESC;

-- ============================================================================
-- MAINTENANCE PROCEDURES
-- ============================================================================

-- Note: SQLite doesn't support stored procedures, but these are the SQL commands
-- that should be used for maintenance operations:

-- Clean up old audit records (older than 2 years)
-- DELETE FROM AddedEntities WHERE CreatedAt < datetime('now', '-2 years');
-- DELETE FROM UpdatedEntities WHERE CreatedAt < datetime('now', '-2 years');
-- DELETE FROM DeletedEntities WHERE CreatedAt < datetime('now', '-2 years');

-- Analyze tables for query optimization
-- ANALYZE AddedEntities;
-- ANALYZE UpdatedEntities;
-- ANALYZE DeletedEntities;

-- Vacuum database to reclaim space
-- VACUUM;
