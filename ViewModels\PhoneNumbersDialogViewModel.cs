using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Input;
using System.Windows.Threading;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the PhoneNumbersDialog providing MVVM data binding and command handling.
    /// Manages phone number collection operations including adding, removing, and editing phone numbers.
    /// Supports phone type selection with Arabic display names and primary phone number management.
    /// Implements IDataErrorInfo for real-time validation feedback with Arabic error messages.
    ///
    /// REFACTORING STATUS: ✅ COMPLETED - ValidationService integration (Task 3.4)
    /// BACKUP CREATED: PhoneNumbersDialogViewModel.cs.backup2 - Implementation before ValidationService integration
    /// </summary>
    public class PhoneNumbersDialogViewModel : BaseViewModel, IDataErrorInfo
    {
        #region Private Fields

        private PhoneNumbersCollectionModel _phoneNumbers;
        private string _newPhoneNumber = string.Empty;
        private PhoneType _newPhoneType = PhoneType.Mobile;
        private Dictionary<PhoneType, string> _phoneTypes;

        // Cache the last validation state to prevent unnecessary CanExecuteChanged events
        private bool _lastCanExecuteState = false;

        // Debouncing timer for command validation to reduce excessive event firing
        private DispatcherTimer _validationDebounceTimer;
        private const int VALIDATION_DEBOUNCE_DELAY_MS = 300;

        // Validation error storage for IDataErrorInfo implementation
        private Dictionary<string, string> _validationErrors = new Dictionary<string, string>();

        // Regex for extracting digits from phone numbers (reused from TextBoxExtensions pattern)
        private static readonly Regex PhoneDigitsRegex = new Regex(@"\D", RegexOptions.Compiled);

        // Flag to track event subscription state for proper disposal
        private bool _phoneNumbersEventSubscribed = false;

        // ValidationService for centralized validation logic
        private readonly ValidationService _validationService;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the phone numbers collection model.
        /// This is bound to the ListView in the dialog.
        /// </summary>
        public PhoneNumbersCollectionModel PhoneNumbers
        {
            get => _phoneNumbers;
            private set => SetProperty(ref _phoneNumbers, value);
        }

        /// <summary>
        /// Gets or sets the new phone number being entered.
        /// This is bound to the phone number TextBox.
        /// Uses smart validation to only trigger CanExecuteChanged when validation state actually changes.
        /// </summary>
        public string NewPhoneNumber
        {
            get => _newPhoneNumber;
            set
            {
                if (SetProperty(ref _newPhoneNumber, value))
                {
                    // Only trigger CanExecuteChanged if the validation state actually changed
                    RefreshCommandStateIfNeeded();
                }
            }
        }

        /// <summary>
        /// Gets or sets the type of the new phone number being entered.
        /// This is bound to the phone type ComboBox.
        /// </summary>
        public PhoneType NewPhoneType
        {
            get => _newPhoneType;
            set => SetProperty(ref _newPhoneType, value);
        }

        /// <summary>
        /// Gets the dictionary of phone types with Arabic display names.
        /// This is bound to the ComboBox ItemsSource.
        /// </summary>
        public Dictionary<PhoneType, string> PhoneTypes
        {
            get => _phoneTypes;
            private set => SetProperty(ref _phoneTypes, value);
        }

        /// <summary>
        /// Gets whether the currently selected phone number is the primary phone number.
        /// Used to show the star indicator in the ListView.
        /// </summary>
        public bool IsPrimaryPhone
        {
            get
            {
                if (PhoneNumbers.SelectedPhoneNumber == null || PhoneNumbers.Count == 0)
                    return false;

                return PhoneNumbers.PhoneNumbers.FirstOrDefault() == PhoneNumbers.SelectedPhoneNumber;
            }
        }

        /// <summary>
        /// Determines if a specific phone number is the primary phone number.
        /// Used by the ListView item template to show the star indicator.
        /// </summary>
        /// <param name="phoneNumber">The phone number to check</param>
        /// <returns>True if it's the primary phone number</returns>
        public bool IsPhonePrimary(PhoneNumberModel phoneNumber)
        {
            if (phoneNumber == null || PhoneNumbers.Count == 0)
                return false;

            return PhoneNumbers.PhoneNumbers.FirstOrDefault() == phoneNumber;
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to add a new phone number to the collection.
        /// </summary>
        public RelayCommand AddPhoneNumberCommand { get; private set; }

        /// <summary>
        /// Command to remove a phone number from the collection.
        /// </summary>
        public RelayCommand RemovePhoneNumberCommand { get; private set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the PhoneNumbersDialogViewModel class.
        /// </summary>
        public PhoneNumbersDialogViewModel()
        {
            // Initialize ValidationService through ServiceLocator
            _validationService = ServiceLocator.GetService<ValidationService>();

            InitializeViewModel(new PhoneNumbersCollectionModel());
        }

        /// <summary>
        /// Initializes a new instance of the PhoneNumbersDialogViewModel class with existing phone numbers.
        /// </summary>
        /// <param name="existingPhoneNumbers">Existing phone numbers to edit</param>
        public PhoneNumbersDialogViewModel(PhoneNumbersCollectionModel existingPhoneNumbers)
        {
            // Initialize ValidationService through ServiceLocator
            _validationService = ServiceLocator.GetService<ValidationService>();

            InitializeViewModel(existingPhoneNumbers?.Clone() ?? new PhoneNumbersCollectionModel());
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes the ViewModel with the provided phone numbers collection.
        /// </summary>
        /// <param name="phoneNumbers">The phone numbers collection to use</param>
        private void InitializeViewModel(PhoneNumbersCollectionModel phoneNumbers)
        {
            // Initialize phone numbers collection
            PhoneNumbers = phoneNumbers;
            
            // Subscribe to selection changes to update IsPrimaryPhone
            PhoneNumbers.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(PhoneNumbers.SelectedPhoneNumber))
                {
                    OnPropertyChanged(nameof(IsPrimaryPhone));
                }
            };
            _phoneNumbersEventSubscribed = true;

            // Initialize phone types dictionary with Arabic names
            PhoneTypes = PhoneNumberModel.GetAllPhoneTypes();

            // Initialize commands
            InitializeCommands();

            // Initialize validation state cache
            _lastCanExecuteState = CanExecuteAddPhoneNumber(null);
        }

        /// <summary>
        /// Initializes the commands for the ViewModel.
        /// </summary>
        private void InitializeCommands()
        {
            AddPhoneNumberCommand = new RelayCommand(
                execute: ExecuteAddPhoneNumber,
                canExecute: CanExecuteAddPhoneNumber,
                commandName: "AddPhoneNumber"
            );

            RemovePhoneNumberCommand = new RelayCommand(
                execute: ExecuteRemovePhoneNumber,
                canExecute: CanExecuteRemovePhoneNumber,
                commandName: "RemovePhoneNumber"
            );
        }

        #endregion

        #region Command Implementations

        /// <summary>
        /// Determines whether the AddPhoneNumber command can be executed.
        /// Includes validation for minimum length and duplicate checking.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        /// <returns>True if the command can be executed, false otherwise</returns>
        private bool CanExecuteAddPhoneNumber(object? parameter)
        {
            try
            {
                // Can't add if phone number is empty
                if (string.IsNullOrWhiteSpace(NewPhoneNumber))
                    return false;

                // Can't add if there are validation errors
                var validationError = ValidateNewPhoneNumber();
                return string.IsNullOrEmpty(validationError);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in CanExecuteAddPhoneNumber: {ex.Message}", "PhoneNumbersDialogViewModel");
                return false; // Fail safe - don't allow execution if there's an error
            }
        }

        /// <summary>
        /// Executes the AddPhoneNumber command.
        /// Validates the phone number before adding and handles validation errors gracefully.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        private void ExecuteAddPhoneNumber(object? parameter)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(NewPhoneNumber))
                    return;

                // Validate the phone number before adding
                var validationError = ValidateNewPhoneNumber();
                if (!string.IsNullOrEmpty(validationError))
                {
                    // Validation failed - the error will be shown through IDataErrorInfo
                    // Log the validation failure for debugging
                    LoggingService.LogWarning($"Phone number validation failed: {validationError}", "PhoneNumbersDialogViewModel");
                    return;
                }

                // Add the new phone number (validation passed)
                var newPhone = PhoneNumbers.AddPhoneNumber(NewPhoneNumber.Trim(), NewPhoneType);

                // Clear the input fields
                NewPhoneNumber = string.Empty;
                NewPhoneType = PhoneType.Mobile;

                // Select the newly added phone number
                PhoneNumbers.SelectedPhoneNumber = newPhone;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding phone number: {ex.Message}", "PhoneNumbersDialogViewModel");
            }
        }

        /// <summary>
        /// Determines whether the RemovePhoneNumber command can be executed.
        /// </summary>
        /// <param name="parameter">The phone number model to remove</param>
        /// <returns>True if the command can be executed, false otherwise</returns>
        private bool CanExecuteRemovePhoneNumber(object? parameter)
        {
            // Can remove if we have a phone number to remove
            return parameter is PhoneNumberModel;
        }

        /// <summary>
        /// Executes the RemovePhoneNumber command.
        /// </summary>
        /// <param name="parameter">The phone number model to remove</param>
        private void ExecuteRemovePhoneNumber(object? parameter)
        {
            try
            {
                if (parameter is PhoneNumberModel phoneToRemove)
                {
                    PhoneNumbers.RemovePhoneNumber(phoneToRemove);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing phone number: {ex.Message}", "PhoneNumbersDialogViewModel");
            }
        }

        /// <summary>
        /// Refreshes the command state with debouncing to prevent excessive CanExecuteChanged events.
        /// Uses a timer to delay validation until user stops typing for a short period.
        /// Provides immediate validation when input is cleared.
        /// </summary>
        private void RefreshCommandStateIfNeeded()
        {
            try
            {
                // If input is empty, validate immediately (user cleared the field)
                if (string.IsNullOrWhiteSpace(_newPhoneNumber))
                {
                    _validationDebounceTimer?.Stop();
                    PerformValidationCheck();
                    return;
                }

                // Stop the existing timer if it's running
                _validationDebounceTimer?.Stop();

                // Start a new timer for debounced validation
                _validationDebounceTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(VALIDATION_DEBOUNCE_DELAY_MS)
                };

                _validationDebounceTimer.Tick += (s, e) =>
                {
                    _validationDebounceTimer.Stop();
                    PerformValidationCheck();
                };

                _validationDebounceTimer.Start();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting up debounced validation: {ex.Message}", "PhoneNumbersDialogViewModel");
                // Fallback to immediate validation if debouncing fails
                PerformValidationCheck();
            }
        }

        /// <summary>
        /// Performs the actual validation check and triggers CanExecuteChanged only if state changed.
        /// </summary>
        private void PerformValidationCheck()
        {
            try
            {
                // Check current validation state
                bool currentCanExecuteState = CanExecuteAddPhoneNumber(null);

                // Only trigger CanExecuteChanged if the state actually changed
                if (currentCanExecuteState != _lastCanExecuteState)
                {
                    _lastCanExecuteState = currentCanExecuteState;
                    AddPhoneNumberCommand.RaiseCanExecuteChanged();
                }
                // If state hasn't changed, don't trigger unnecessary events
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error performing validation check: {ex.Message}", "PhoneNumbersDialogViewModel");
                // Fallback to always refresh if there's an error
                AddPhoneNumberCommand.RaiseCanExecuteChanged();
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Sets the primary phone number from the main view.
        /// If the collection is empty and a phone number is provided, adds it as the first entry.
        /// </summary>
        /// <param name="phoneNumber">The phone number from the main view</param>
        public void SetPrimaryPhoneNumber(string phoneNumber)
        {
            try
            {
                PhoneNumbers.SetPrimaryPhoneNumber(phoneNumber);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting primary phone number: {ex.Message}", "PhoneNumbersDialogViewModel");
            }
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// Gets the validation error message for the specified property.
        /// </summary>
        /// <param name="propertyName">The name of the property to validate</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        private string GetValidationError(string propertyName)
        {
            try
            {
                switch (propertyName)
                {
                    case nameof(NewPhoneNumber):
                        return ValidateNewPhoneNumber();
                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in GetValidationError for {propertyName}: {ex.Message}", "PhoneNumbersDialogViewModel");
                return null;
            }
        }

        /// <summary>
        /// Validates the new phone number for minimum length and duplicate checking.
        /// </summary>
        /// <returns>Error message if validation fails, null if valid</returns>
        private string ValidateNewPhoneNumber()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(NewPhoneNumber))
                    return null; // Empty is allowed, just can't add

                // Get existing phone numbers for duplicate checking
                var existingNumbers = PhoneNumbers?.PhoneNumbers?.Select(p => p.PhoneNumber ?? string.Empty) ?? Enumerable.Empty<string>();

                // Use ValidationService for complete phone number validation (format + duplicates)
                return _validationService?.ValidateCompletePhoneNumber(NewPhoneNumber, existingNumbers, false);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating new phone number: {ex.Message}", "PhoneNumbersDialogViewModel");
                return null;
            }
        }



        #endregion

        #region IDataErrorInfo Implementation

        /// <summary>
        /// Gets an error message indicating what is wrong with this object.
        /// Returns null if there are no validation errors.
        /// </summary>
        public string Error
        {
            get
            {
                // Return the first validation error if any exist
                return _validationErrors.Values.FirstOrDefault();
            }
        }

        /// <summary>
        /// Gets the error message for the property with the given name.
        /// </summary>
        /// <param name="columnName">The name of the property whose error message to get</param>
        /// <returns>The error message for the property, or null if valid</returns>
        public string this[string columnName]
        {
            get
            {
                try
                {
                    // Clear previous error for this property
                    _validationErrors.Remove(columnName);

                    // Validate the specific property
                    string errorMessage = GetValidationError(columnName);

                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        _validationErrors[columnName] = errorMessage;
                        return errorMessage;
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error validating property {columnName}: {ex.Message}", "PhoneNumbersDialogViewModel");
                    return null;
                }
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of resources used by the PhoneNumbersDialogViewModel.
        /// Implements enhanced IDisposable pattern with BaseViewModel integration.
        /// Prevents memory leaks from phone number collections, validation timers, and event subscriptions.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources, false if called from finalizer</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    LoggingService.LogDebug("Starting disposal of PhoneNumbersDialogViewModel resources", GetType().Name);

                    // Dispose and stop validation debounce timer
                    if (_validationDebounceTimer != null)
                    {
                        try
                        {
                            _validationDebounceTimer.Stop();
                            _validationDebounceTimer = null;
                            LoggingService.LogDebug("Validation debounce timer disposed successfully", GetType().Name);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error disposing validation debounce timer: {ex.Message}", GetType().Name);
                        }
                    }

                    // Unsubscribe from PhoneNumbers PropertyChanged events
                    if (_phoneNumbers != null && _phoneNumbersEventSubscribed)
                    {
                        try
                        {
                            // Clear all event handlers for PropertyChanged
                            var propertyChangedField = typeof(PhoneNumbersCollectionModel).GetField("PropertyChanged",
                                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                            if (propertyChangedField != null)
                            {
                                propertyChangedField.SetValue(_phoneNumbers, null);
                            }
                            _phoneNumbersEventSubscribed = false;
                            LoggingService.LogDebug("PhoneNumbers PropertyChanged events unsubscribed successfully", GetType().Name);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error unsubscribing from PhoneNumbers PropertyChanged events: {ex.Message}", GetType().Name);
                        }
                    }

                    // Clean up phone numbers collection
                    if (_phoneNumbers?.PhoneNumbers != null)
                    {
                        try
                        {
                            _phoneNumbers.PhoneNumbers.Clear();
                            LoggingService.LogDebug("Phone numbers collection cleared successfully", GetType().Name);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error clearing phone numbers collection: {ex.Message}", GetType().Name);
                        }
                    }

                    // Clear validation errors dictionary
                    if (_validationErrors != null)
                    {
                        try
                        {
                            _validationErrors.Clear();
                            LoggingService.LogDebug("Validation errors dictionary cleared successfully", GetType().Name);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error clearing validation errors: {ex.Message}", GetType().Name);
                        }
                    }

                    // Clear phone types dictionary
                    if (_phoneTypes != null)
                    {
                        try
                        {
                            _phoneTypes.Clear();
                            LoggingService.LogDebug("Phone types dictionary cleared successfully", GetType().Name);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error clearing phone types dictionary: {ex.Message}", GetType().Name);
                        }
                    }

                    // Clear property references to aid garbage collection
                    _phoneNumbers = null;
                    _validationErrors = null;
                    _phoneTypes = null;
                    _newPhoneNumber = null;

                    LoggingService.LogDebug("PhoneNumbersDialogViewModel disposal completed successfully", GetType().Name);
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Unexpected error during PhoneNumbersDialogViewModel disposal: {ex.Message}", GetType().Name);
                }
                finally
                {
                    // Always call base disposal to maintain inheritance chain
                    base.Dispose(disposing);
                }
            }
        }

        #endregion
    }
}
