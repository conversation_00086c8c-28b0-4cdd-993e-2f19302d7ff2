=== UFU2 Application Session Started at 2025-08-09 22:01:32 ===
[2025-08-09 22:01:32.358]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 22:01:32.366]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 22:01:32.370]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 22:01:32.374]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 22:01:32.388]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 22:01:32.392]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 22:01:32.395]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 22:01:32.401]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 22:01:32.405]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 22:01:32.408]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 22:01:32.412]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 22:01:32.415]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 22:01:32.418]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 22:01:32.421]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 22:01:32.424]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 22:01:32.427]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 22:01:32.435]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 22:01:32.438]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 22:01:32.447]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 22:01:32.451]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 22:01:32.455]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 22:01:32.458]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 22:01:32.461]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 22:01:32.467]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 22:01:32.471]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 22:01:32.475]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 22:01:32.479]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 22:01:32.485]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 22:01:32.488]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 22:01:32.492]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 22:01:32.496]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 22:01:32.501]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 22:01:32.506]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 22:01:32.510]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 22:01:32.514]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 22:01:32.519]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 22:01:32.522]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 22:01:32.527]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 22:01:32.555]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 104.04MB working set
[2025-08-09 22:01:32.603]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 22:01:32.661]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 22:01:32.688]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 22:01:32.702]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 22:01:32.712]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 22:01:32.724]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 22:01:32.764]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 22:01:32.777]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 22:01:32.789]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 22:01:33.383]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 22:01:33.443]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 22:01:33.488]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 22:01:33.496]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 22:01:33.524]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638903700935226981 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 22:01:33.547]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 22:01:33.558]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:33.585]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 22:01:33.607]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 22:01:33.620]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 22:01:33.635]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 22:01:33.650]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 22:01:33.701]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 22:01:33.969]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 22:01:33.978]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 22:01:33.985]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 22:01:33.989]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 22:01:33.995]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 22:01:34.004]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 22:01:34.008]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 22:01:34.013]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 22:01:34.022]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 22:01:34.029]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 22:01:34.037]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 22:01:34.042]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 22:01:34.046]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 22:01:34.051]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 22:01:34.056]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 22:01:34.060]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 22:01:34.064]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 22:01:34.071]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 22:01:34.075]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 22:01:34.080]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 22:01:34.086]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 22:01:34.090]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 22:01:34.095]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 22:01:34.100]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 22:01:34.105]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 22:01:34.109]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 22:01:34.113]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 22:01:34.123]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 22:01:34.129]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 22:01:34.144]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 22:01:34.168]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 22:01:34.204]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 22:01:34.224]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 22:01:34.244]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 22:01:34.261]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 22:01:34.278]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 22:01:34.296]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 22:01:34.369]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 22:01:34.458]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 22:01:34.509]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 22:01:34.526]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 22:01:34.560]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 22:01:34.574]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 22:01:34.600]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 22:01:34.617]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 22:01:34.629]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 22:01:34.641]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 22:01:34.664]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 22:01:34.671]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 22:01:34.680]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 22:01:34.688]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 22:01:34.696]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 22:01:34.704]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 22:01:34.722]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 22:01:34.729]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 22:01:34.736]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 22:01:34.746]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 2022ms
[2025-08-09 22:01:34.753]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 22:01:34.761]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 22:01:34.788]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 22:01:34.793]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 22:01:34.797]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 22:01:34.806]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 22:01:34.811]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 22:01:34.845]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 22:01:34.887]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 22:01:34.887]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 22:01:34.911]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 22:01:34.919]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 22:01:34.904]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 22:01:34.937]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 22:01:34.888]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 22:01:34.952]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 22:01:34.959]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 22:01:34.976]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 22:01:34.989]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 22:01:35.003]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 22:01:35.030]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 22:01:34.887]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 22:01:35.047]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 22:01:35.074]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 22:01:35.089]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 22:01:35.085]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:35.103]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 22:01:35.110]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 22:01:35.117]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 22:01:35.122]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 22:01:35.128]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 22:01:35.134]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 22:01:35.139]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 22:01:35.143]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 22:01:35.160]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 22:01:35.167]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 22:01:35.175]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 22:01:35.182]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:35.188]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 22:01:35.244]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 22:01:35.258]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 22:01:35.274]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 22:01:35.352]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 22:01:35.371]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 22:01:35.386]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 22:01:35.422]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 22:01:35.445]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 22:01:35.476]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 22:01:35.514]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 22:01:35.593]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 22:01:35.641]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 22:01:35.675]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 22:01:35.680]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 22:01:35.693]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 22:01:35.700]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 22:01:35.711]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 22:01:35.722]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 22:01:35.728]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 22:01:35.737]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 22:01:35.754]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 22:01:35.762]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 22:01:35.771]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 22:01:35.780]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 22:01:35.789]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 22:01:35.797]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 22:01:35.811]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 22:01:35.821]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 22:01:35.834]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:35.842]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 22:01:35.873]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 22:01:35.927]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:35.988]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 22:01:36.040]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 22:01:36.063]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 22:01:36.080]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 22:01:36.110]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 22:01:36.127]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 22:01:36.147]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 22:01:36.177]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 22:01:36.206]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 22:01:36.229]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 22:01:36.245]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 22:01:36.260]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:36.275]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 22:01:36.299]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 22:01:36.311]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 22:01:36.328]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 22:01:36.340]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 22:01:36.357]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 22:01:36.368]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 22:01:36.405]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 22:01:36.488]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 22:01:36.539]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 22:01:36.574]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 22:01:36.600]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 22:01:36.610]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 22:01:36.629]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 22:01:36.657]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 22:01:36.707]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 22:01:36.762]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 22:01:36.790]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 22:01:36.806]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 22:01:36.814]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:36.844]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 22:01:36.874]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 22:01:36.881]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:36.890]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 22:01:36.895]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 22:01:36.906]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 22:01:36.911]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 22:01:36.921]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 22:01:36.928]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 22:01:36.937]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 22:01:36.944]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 22:01:36.953]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 22:01:36.959]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 22:01:36.970]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 22:01:36.979]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:36.988]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 22:01:36.996]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 22:01:37.010]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 22:01:37.019]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 22:01:37.026]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 22:01:37.037]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 22:01:37.045]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 22:01:37.058]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 22:01:37.069]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 22:01:37.078]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 22:01:37.088]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 22:01:37.096]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 22:01:37.105]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 22:01:37.110]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 22:01:37.118]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 22:01:37.126]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 22:01:37.136]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 22:01:37.142]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:37.152]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 22:01:37.160]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:37.172]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 22:01:37.179]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 22:01:37.235]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 22:01:37.290]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 22:01:37.342]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 22:01:37.368]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 22:01:37.392]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 22:01:37.419]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 22:01:37.445]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 22:01:37.463]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 22:01:37.483]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 22:01:37.502]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 22:01:37.525]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 22:01:37.546]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 22:01:37.561]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 22:01:37.597]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 22:01:37.618]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 22:01:37.638]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 22:01:37.651]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 22:01:37.680]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 22:01:37.685]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 22:01:37.690]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 22:01:37.703]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 22:01:37.697]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 22:01:37.718]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 22:01:37.712]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 22:01:37.735]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 22:01:37.743]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 22:01:37.750]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 22:01:37.762]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 22:01:37.773]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 22:01:37.788]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 22:01:37.794]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 22:01:37.809]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 22:01:37.764]  	[ERROR]		[BackgroundViewInitializationService]	Background task failed: MainWindow_CommonData - Service ActivityTypeBaseService is not registered
[2025-08-09 22:01:37.844]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 22:01:37.891]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 22:01:37.913]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 22:01:37.929]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 22:01:37.938]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 22:01:37.944]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 22:01:37.950]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 22:01:37.957]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 22:01:37.962]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 22:01:37.969]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 22:01:37.975]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 22:01:37.980]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 22:01:37.986]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 22:01:37.993]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 22:01:37.999]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 22:01:38.004]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 22:01:38.010]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 22:01:38.018]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 22:01:38.024]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 22:01:38.030]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:38.049]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 22:01:38.059]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 22:01:38.064]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 22:01:38.073]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 22:01:38.079]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 22:01:38.088]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 22:01:38.093]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 22:01:38.097]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 22:01:38.105]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 22:01:38.111]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 22:01:38.119]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 22:01:38.123]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 22:01:38.128]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 22:01:38.135]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:38.140]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 22:01:38.144]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:38.151]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:38.158]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:38.162]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 22:01:38.169]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:38.175]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:38.186]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 22:01:38.200]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 22:01:38.328]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 22:01:38.345]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:38.389]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 22:01:38.394]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 22:01:38.405]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:38.425]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 22:01:38.429]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 22:01:38.437]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 22:01:38.442]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 22:01:38.447]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 22:01:38.460]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 22:01:38.781]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 22:01:38.838]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 22:01:38.843]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 22:01:38.854]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 22:01:38.927]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 22:01:38.939]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 22:01:38.945]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 22:01:38.954]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 22:01:38.967]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 22:01:38.975]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:38.988]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-09 22:01:39.012]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-09 22:01:39.020]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 22:01:39.028]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 22:01:39.039]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 22:01:39.044]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 22:01:39.053]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 22:01:39.058]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 22:01:39.066]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 22:01:39.075]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 22:01:39.080]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 22:01:39.086]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 22:01:39.092]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 22:01:39.097]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 22:01:39.104]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 22:01:39.109]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 22:01:39.117]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 22:01:39.122]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 22:01:39.130]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 22:01:39.138]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 22:01:39.146]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.188]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-09 22:01:39.242]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.296]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.311]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 22:01:39.317]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.322]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.328]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 22:01:39.334]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.339]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.345]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 22:01:39.352]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.358]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.364]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 22:01:39.370]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.376]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.385]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 22:01:39.390]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.396]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.404]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 22:01:39.410]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.417]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-09 22:01:39.423]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 285ms
[2025-08-09 22:01:39.430]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 22:01:39.444]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 22:01:39.451]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 22ms
[2025-08-09 22:01:39.458]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 22:01:39.468]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 22:01:39.473]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 17ms
[2025-08-09 22:01:39.479]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 22:01:39.490]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:39.500]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 22:01:39.509]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:39.520]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 22:01:39.526]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:39.537]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 22:01:39.543]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 22:01:39.554]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 22:01:39.560]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 22:01:39.569]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 91ms
[2025-08-09 22:01:39.577]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 22:01:39.588]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.603]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 22:01:39.609]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.624]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.634]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 22:01:39.641]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.652]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.661]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 22:01:39.679]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.691]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.702]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 22:01:39.710]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.724]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.736]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 22:01:39.744]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.754]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.761]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 22:01:39.773]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.795]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.813]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 22:01:39.846]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:39.937]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:39.995]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 22:01:40.038]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:40.045]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:40.058]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 22:01:40.076]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:40.086]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 22:01:40.093]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 22:01:40.104]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 22:01:40.110]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 22:01:40.119]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 543ms
[2025-08-09 22:01:40.125]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 22:01:40.134]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 22:01:40.140]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 22:01:40.145]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 22:01:40.152]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 22:01:40.157]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 22:01:40.162]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 22:01:40.167]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 22:01:40.172]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 22:01:40.177]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 22:01:40.183]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 22:01:40.188]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 22:01:40.193]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 22:01:40.200]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 22:01:40.205]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 22:01:40.210]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 22:01:40.218]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 22:01:40.228]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 22:01:40.347]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 22:01:40.353]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 22:01:40.358]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 22:01:40.363]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 22:01:40.374]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 22:01:40.381]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:40.388]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 22:01:40.394]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:40.403]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 22:01:40.407]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:40.500]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 22:01:40.505]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 22:01:40.744]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 22:01:40.756]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 1974.6857ms
[2025-08-09 22:01:40.761]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:40.770]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:40.775]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:40.779]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:40.786]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:40.791]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:40.796]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:41.791]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 3010.4509ms
[2025-08-09 22:01:41.796]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 22:01:41.931]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 22:01:44.747]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 270.0% increase in response time
[2025-08-09 22:01:45.273]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 310.0% increase in response time
[2025-08-09 22:01:45.718]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:45.724]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:45.728]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:45.735]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:45.740]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:45.745]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:45.799]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 22:01:45.807]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-09 22:01:45.831]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-09 22:01:45.867]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 22:01:45.874]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_17946858_638903701058743100 (BaseViewModel) for NPersonalViewModel
[2025-08-09 22:01:45.879]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-09 22:01:45.886]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:45.893]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_27303998_638903701058931890 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 22:01:45.900]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 22:01:45.905]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:45.910]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 22:01:45.916]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_44409397_638903701059167836 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 22:01:45.921]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 22:01:45.926]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:45.933]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 22:01:45.938]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 22:01:45.955]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-09 22:01:46.015]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 22:01:46.060]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_26809401_638903701060601581 (BaseViewModel) for NewClientViewModel
[2025-08-09 22:01:46.067]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-09 22:01:46.072]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:46.077]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_39958021_638903701060771417 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 22:01:46.084]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 22:01:46.089]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:46.095]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 22:01:46.103]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_24077873_638903701061033954 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 22:01:46.108]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 22:01:46.112]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:46.120]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 22:01:46.125]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 22:01:46.131]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_15374270_638903701061317947 (BaseViewModel) for ActivityManagementViewModel
[2025-08-09 22:01:46.137]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-09 22:01:46.142]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:46.147]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 22:01:46.155]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-09 22:01:46.160]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 22:01:46.168]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-09 22:01:46.174]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_4150710_638903701061746124 (BaseViewModel) for NotesManagementViewModel
[2025-08-09 22:01:46.180]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-09 22:01:46.187]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:46.194]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 22:01:46.201]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-09 22:01:46.206]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-09 22:01:46.211]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 22:01:46.218]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-09 22:01:46.224]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 22:01:46.229]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 22:01:46.234]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 22:01:46.239]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 22:01:46.244]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-09 22:01:46.250]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-09 22:01:46.254]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-09 22:01:46.258]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-09 22:01:46.264]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 432ms (Success: True)
[2025-08-09 22:01:46.270]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 464ms (Success: True)
[2025-08-09 22:01:46.277]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x560 (Height-based width calculation)
[2025-08-09 22:01:46.307]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-09 22:01:46.333]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-09 22:01:46.334]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 22:01:46.336]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 22:01:46.344]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-09 22:01:46.368]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-09 22:01:46.391]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 59ms
[2025-08-09 22:01:46.409]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 77ms
[2025-08-09 22:01:46.936]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-09 22:01:46.946]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 22:01:47.100]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1096.5572ms
[2025-08-09 22:01:47.106]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:47.111]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1218.3368ms
[2025-08-09 22:01:47.117]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:47.122]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1205.9832ms
[2025-08-09 22:01:47.127]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:47.135]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1075.0522ms
[2025-08-09 22:01:47.140]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:47.146]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1069.5485ms
[2025-08-09 22:01:47.155]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:47.160]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1057.2098ms
[2025-08-09 22:01:47.169]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:47.178]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1046.3588ms
[2025-08-09 22:01:47.187]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:47.192]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1018.129ms
[2025-08-09 22:01:47.202]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 22:01:47.226]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.241]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:47.254]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.261]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:47.268]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.273]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:47.370]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.376]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:47.384]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.389]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:47.394]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.399]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:47.919]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.925]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:47.930]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.937]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:47.943]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:47.950]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:48.005]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.010]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.016]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.021]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.026]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.031]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.037]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.042]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.046]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.054]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.059]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.063]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.069]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.074]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.079]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.085]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.093]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.100]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.106]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.110]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.118]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.122]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.164]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.206]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.230]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for SecondaryCommercial
[2025-08-09 22:01:48.241]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.261]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.284]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.289]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.295]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.301]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.306]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.310]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.316]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.320]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.325]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.329]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.334]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.339]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.343]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.349]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.354]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.358]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.362]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.369]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:48.373]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.377]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.384]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:48.388]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:48.393]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: SecondaryCommercial
[2025-08-09 22:01:48.399]  	[INFO]		[NewClientViewModel]	Switched to activity tab: SecondaryCommercial
[2025-08-09 22:01:48.417]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2413.0499ms
[2025-08-09 22:01:48.422]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 22:01:48.430]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2537.1172ms
[2025-08-09 22:01:48.437]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 22:01:48.444]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2527.3057ms
[2025-08-09 22:01:48.454]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 22:01:48.475]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2397.9517ms
[2025-08-09 22:01:48.480]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 22:01:48.486]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2382.6161ms
[2025-08-09 22:01:48.490]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 22:01:48.494]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2320.2725ms
[2025-08-09 22:01:48.501]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 22:01:48.506]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: SecondaryCommercial
[2025-08-09 22:01:48.511]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:48.516]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:48.521]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:48.525]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:48.529]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:48.535]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:48.939]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:48.980]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:49.024]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:49.071]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:49.081]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:49.090]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:49.105]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.111]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.120]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.125]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.131]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.137]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.142]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.147]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.152]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.157]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.164]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.170]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.176]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.184]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.191]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.201]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.208]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.214]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.221]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.227]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.234]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.240]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.248]  	[DEBUG]		[NActivityDetailView]	Skipping dialog opening - programmatic ComboBox change detected
[2025-08-09 22:01:49.255]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.260]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.267]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for Craft
[2025-08-09 22:01:49.272]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.277]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.282]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.287]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.292]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.296]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.304]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.309]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.313]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.320]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.324]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.329]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.336]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.340]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.345]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.351]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.356]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.361]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.366]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.371]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:49.375]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.379]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.386]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:49.391]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:49.395]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: Craft
[2025-08-09 22:01:49.402]  	[INFO]		[NewClientViewModel]	Switched to activity tab: Craft
[2025-08-09 22:01:49.427]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: Craft
[2025-08-09 22:01:49.437]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:49.443]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:49.452]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:49.458]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:49.464]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:49.473]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:49.893]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:49.899]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:49.904]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:49.909]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:49.919]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:49.924]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:50.013]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.024]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.046]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.055]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.060]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.069]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.075]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.083]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.090]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.097]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.103]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.109]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.114]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.120]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.125]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.130]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.136]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.142]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.150]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.159]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.170]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.179]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.191]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.196]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.206]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for Professional
[2025-08-09 22:01:50.211]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.218]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.223]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.228]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.235]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.240]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.245]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.250]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.254]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.258]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.263]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.270]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.275]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.279]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.285]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.289]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.294]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.299]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.303]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.308]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:50.312]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.318]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.323]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:50.327]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:50.332]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: Professional
[2025-08-09 22:01:50.336]  	[INFO]		[NewClientViewModel]	Switched to activity tab: Professional
[2025-08-09 22:01:50.373]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: Professional
[2025-08-09 22:01:50.419]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:50.423]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:50.428]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:50.434]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:50.438]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:50.442]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:51.009]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbersCollection(1)
[2025-08-09 22:01:51.089]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 27, Batched: 27, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: G12DisplayText(6), BISDisplayText(6), SelectedActivityType(3), CurrentActivity(3), CurrentFileCheckStates(3)
[2025-08-09 22:01:51.135]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 21, Batched: 21, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedActivityType(3), CurrentActivity(3), CurrentFileCheckStates(3), G12SelectedYears(3), BISSelectedYears(3)
[2025-08-09 22:01:51.503]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.508]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:51.513]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.519]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:51.523]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.527]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:51.588]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.592]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:51.597]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.603]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:51.607]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-09 22:01:51.612]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.617]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-09 22:01:51.623]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-09 22:01:51.628]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-09 22:01:51.634]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 22:01:51.639]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_23890167_638903701116392236 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-09 22:01:51.643]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-09 22:01:51.649]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 22:01:51.654]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 22:01:51.658]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 22:01:51.663]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-09 22:01:51.674]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 22:01:51.679]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 22:01:51.684]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 22:01:51.741]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 22:01:51.763]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 22:01:51.879]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.884]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:51.889]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.893]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:51.899]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:51.903]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:52.401]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:52.417]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:52.427]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:52.437]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 22:01:52.446]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 22:01:52.452]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 22:01:52.487]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-09 22:01:52.492]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 22:01:52.502]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 22:01:52.512]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 22:01:52.519]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:52.524]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 22:01:52.528]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 22:01:52.534]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:52.539]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 22:01:52.545]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 22:01:52.551]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_23890167_638903701116392236
[2025-08-09 22:01:52.555]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:52.560]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 22:01:52.565]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 22:01:52.570]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:52.574]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 22:01:52.585]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 22:01:52.591]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 22:01:52.605]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-09 22:01:52.610]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-09 22:01:52.616]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 22:01:52.621]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-09 22:01:52.626]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 22:01:52.632]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 22:01:52.637]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:52.641]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 22:01:52.646]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 22:01:52.651]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:52.656]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 22:01:52.660]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 22:01:52.666]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_28090709_638903700935226981
[2025-08-09 22:01:52.671]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:52.678]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 22:01:52.686]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 22:01:52.693]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:52.703]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 22:01:52.710]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 22:01:52.717]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-09 22:01:52.723]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-09 22:01:52.730]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-09 22:01:52.739]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-09 22:01:52.747]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 22:01:52.756]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 22:01:52.769]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-09 22:01:52.776]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 22:01:52.782]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 22:01:52.787]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 22:01:52.812]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 22:01:52.828]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 22:01:52.855]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 22:01:52.860]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 22:01:52.866]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 22:01:52.871]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 22:01:52.878]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 22:01:52.889]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 22:01:52.900]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 22:01:52.905]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-09 22:01:52.911]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 22:01:52.917]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-09 22:01:52.921]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-09 22:01:52.928]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-09 22:01:52.935]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 22:01:52.939]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 22:01:52.944]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 22:01:52.952]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 22:01:52.957]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 22:01:52.962]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-09 22:01:52.968]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 22:01:52.973]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-09 22:01:52.978]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 22:01:52.983]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-09 22:01:52.988]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-09 22:01:52.993]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-09 22:01:52.999]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-09 22:01:53.007]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-09 22:01:53.012]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-09 22:01:53.021]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-09 22:01:53.027]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 50.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-09 22:01:53.035]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-09 22:01:53.042]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-09 22:01:53.049]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-09 22:01:53.056]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 50.0%
[2025-08-09 22:01:53.063]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-09 22:01:53.074]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 22:01:53.079]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 22:01:53.088]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 22:01:53.097]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-09 22:01:53.105]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-09 22:01:53.110]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-09 22:01:53.117]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-09 22:01:53.122]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-09 22:01:53.126]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-09 22:01:53.132]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-09 22:01:53.136]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-09 22:01:53.140]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 22:01:53.145]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 22:01:53.183]  	[INFO]		[ResourceManager]	Generated memory leak report: 8 alive resources, 0 dead resources
[2025-08-09 22:01:53.209]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 2
[2025-08-09 22:01:53.220]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 2 potential leaks detected
[2025-08-09 22:01:53.229]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-09 22:01:53.237]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-09 22:01:53.243]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 22:01:53.253]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 22:01:53.259]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-09 22:01:53.266]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-09 22:01:53.274]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 22:01:53.286]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 22:01:53.297]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:53.329]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 22:01:53.371]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:53.388]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 22:01:53.401]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:53.412]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 22:01:53.434]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 22:01:53.454]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 22:01:53.471]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 22:01:53.496]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:53.519]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:53.555]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_24077873_638903701061033954
[2025-08-09 22:01:53.580]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 22:01:53.622]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 22:01:53.640]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 22:01:53.656]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:53.745]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:53.787]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_44409397_638903701059167836
[2025-08-09 22:01:53.801]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 22:01:53.811]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 22:01:53.820]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 22:01:53.828]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:53.845]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:53.860]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 22:01:53.868]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:53.874]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 22:01:53.879]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:53.887]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:53.892]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 22:01:53.897]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-09 22:01:53.905]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:53.910]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 22:01:53.917]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityManagementViewModel_15374270_638903701061317947
[2025-08-09 22:01:53.922]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:53.926]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 22:01:53.933]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-09 22:01:53.938]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:53.944]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 22:01:53.952]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 22:01:53.957]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:53.962]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:53.971]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:53.976]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 22:01:53.983]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:53.988]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 22:01:53.993]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 22:01:53.999]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 22:01:54.003]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.007]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 22:01:54.013]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.019]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:54.023]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 22:01:54.028]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.034]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.039]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 22:01:54.043]  	[DEBUG]		[ResourceManager]	Unregistered resource: NotesManagementViewModel_4150710_638903701061746124
[2025-08-09 22:01:54.050]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:54.055]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 22:01:54.060]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.067]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.073]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 22:01:54.078]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.086]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.091]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.096]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.103]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.108]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.115]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 22:01:54.120]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 22:01:54.126]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.132]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.137]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.143]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_39958021_638903701060771417
[2025-08-09 22:01:54.149]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 22:01:54.155]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 22:01:54.160]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.167]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.172]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.179]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_27303998_638903701058931890
[2025-08-09 22:01:54.186]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 22:01:54.191]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 22:01:54.196]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.202]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.206]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.212]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.218]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:54.222]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.227]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.233]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:54.243]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.269]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:54.275]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 22:01:54.280]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.286]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.291]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 22:01:54.295]  	[DEBUG]		[ResourceManager]	Unregistered resource: NPersonalViewModel_17946858_638903701058743100
[2025-08-09 22:01:54.304]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:54.310]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 22:01:54.316]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.321]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.326]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 22:01:54.333]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.338]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:54.344]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 22:01:54.350]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 22:01:54.355]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 22:01:54.361]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.368]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 22:01:54.373]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 22:01:54.379]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 22:01:54.388]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 22:01:54.396]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 22:01:54.421]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 22:01:54.440]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:54.450]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 22:01:54.460]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.470]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.477]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 22:01:54.484]  	[DEBUG]		[ResourceManager]	Unregistered resource: NewClientViewModel_26809401_638903701060601581
[2025-08-09 22:01:54.490]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 22:01:54.497]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 22:01:54.506]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-09 22:01:54.519]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 22:01:54.529]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 22:01:54.554]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 10 tracked, 16 disposed, 1 cleanups
[2025-08-09 22:01:54.572]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-09 22:01:54.601]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 22:01:54.637]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-09 22:01:54.673]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 22:01:54.712]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-09 22:01:54.729]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 22:01:54.742]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-09 22:01:54.753]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 22:01:54.760]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-09 22:01:54.768]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-09 22:01:54.773]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-09 22:01:54.779]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-09 22:01:54.789]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-09 22:01:54.794]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-09 22:01:54.802]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 22:01:54.808]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-09 22:01:54.817]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-09 22:01:54.824]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-09 22:01:54.829]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-09 22:01:54.836]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 22:01:54 ===
