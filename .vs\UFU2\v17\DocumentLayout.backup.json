{"Version": 1, "WorkspaceRootPath": "E:\\UserFiles\\Projects\\UFU2\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|e:\\userfiles\\projects\\ufu2\\viewmodels\\activitymanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|solutionrelative:viewmodels\\activitymanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|e:\\userfiles\\projects\\ufu2\\views\\newclient\\nactivitydetailview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|solutionrelative:views\\newclient\\nactivitydetailview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|e:\\userfiles\\projects\\ufu2\\views\\dialogs\\duplicateclientdetectiondialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|solutionrelative:views\\dialogs\\duplicateclientdetectiondialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|e:\\userfiles\\projects\\ufu2\\views\\newclient\\npersonalview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|solutionrelative:views\\newclient\\npersonalview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|e:\\userfiles\\projects\\ufu2\\resources\\styles\\inputboxstyles.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|solutionrelative:resources\\styles\\inputboxstyles.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "DuplicateClientDetectionDialog.xaml", "DocumentMoniker": "E:\\UserFiles\\Projects\\UFU2\\Views\\Dialogs\\DuplicateClientDetectionDialog.xaml", "RelativeDocumentMoniker": "Views\\Dialogs\\DuplicateClientDetectionDialog.xaml", "ToolTip": "E:\\UserFiles\\Projects\\UFU2\\Views\\Dialogs\\DuplicateClientDetectionDialog.xaml", "RelativeToolTip": "Views\\Dialogs\\DuplicateClientDetectionDialog.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-10T01:22:39.323Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "NActivityDetailView.xaml", "DocumentMoniker": "E:\\UserFiles\\Projects\\UFU2\\Views\\NewClient\\NActivityDetailView.xaml", "RelativeDocumentMoniker": "Views\\NewClient\\NActivityDetailView.xaml", "ToolTip": "E:\\UserFiles\\Projects\\UFU2\\Views\\NewClient\\NActivityDetailView.xaml", "RelativeToolTip": "Views\\NewClient\\NActivityDetailView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-10T01:02:52.959Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "NPersonalView.xaml", "DocumentMoniker": "E:\\UserFiles\\Projects\\UFU2\\Views\\NewClient\\NPersonalView.xaml", "RelativeDocumentMoniker": "Views\\NewClient\\NPersonalView.xaml", "ToolTip": "E:\\UserFiles\\Projects\\UFU2\\Views\\NewClient\\NPersonalView.xaml", "RelativeToolTip": "Views\\NewClient\\NPersonalView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-10T00:56:54.24Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ActivityManagementViewModel.cs", "DocumentMoniker": "E:\\UserFiles\\Projects\\UFU2\\ViewModels\\ActivityManagementViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\ActivityManagementViewModel.cs", "ToolTip": "E:\\UserFiles\\Projects\\UFU2\\ViewModels\\ActivityManagementViewModel.cs", "RelativeToolTip": "ViewModels\\ActivityManagementViewModel.cs", "ViewState": "AgIAAKkCAAAAAAAAAAAnwLgCAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T00:16:02.459Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "InputBoxStyles.xaml", "DocumentMoniker": "E:\\UserFiles\\Projects\\UFU2\\Resources\\Styles\\InputBoxStyles.xaml", "RelativeDocumentMoniker": "Resources\\Styles\\InputBoxStyles.xaml", "ToolTip": "E:\\UserFiles\\Projects\\UFU2\\Resources\\Styles\\InputBoxStyles.xaml", "RelativeToolTip": "Resources\\Styles\\InputBoxStyles.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-10T00:12:07.57Z", "EditorCaption": ""}]}]}]}