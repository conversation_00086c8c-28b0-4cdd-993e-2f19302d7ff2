using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using Microsoft.Win32;
using MaterialDesignThemes.Wpf;
using UFU2.Common;
using UFU2.ViewModels;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// ImageManagementDialog provides functionality for loading, editing, and managing profile images.
    /// Supports common image formats and follows UFU2 MVVM patterns with proper error handling.
    /// </summary>
    public partial class ImageManagementDialog : UserControl
    {
        #region Private Fields

        /// <summary>
        /// Dialog result indicating whether the user saved or cancelled the operation
        /// </summary>
        private bool _dialogResult = false;

        /// <summary>
        /// Currently loaded image source for disposal management
        /// </summary>
        private BitmapImage? _currentImageSource;

        /// <summary>
        /// ViewModel for managing image state and UI binding
        /// </summary>
        private ImageManagementViewModel _viewModel;

        /// <summary>
        /// Indicates whether to save images to disk or keep them in memory only.
        /// When true, images are saved to ProfileImages directory.
        /// When false, images are kept in memory for later use (e.g., in NewClientView).
        /// </summary>
        private bool _saveToProfileImagesDirectory = true;

        /// <summary>
        /// Stores the processed image in memory when _saveToProfileImagesDirectory is false.
        /// This allows the calling context to retrieve the processed image without file I/O.
        /// </summary>
        private BitmapSource? _processedImageInMemory;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the dialog result indicating whether the user saved or cancelled.
        /// </summary>
        public bool DialogResult => _dialogResult;

        /// <summary>
        /// Gets the currently loaded image source.
        /// </summary>
        public BitmapImage? CurrentImageSource => _currentImageSource;

        /// <summary>
        /// Gets the edited/cropped image from the ViewModel.
        /// Returns the current image which may have been cropped or modified.
        /// </summary>
        public BitmapImage? EditedImage => _viewModel.CurrentImage;

        /// <summary>
        /// Gets the path where the image was saved, if any.
        /// </summary>
        public string? SavedImagePath { get; private set; }

        /// <summary>
        /// Gets the ViewModel for this dialog.
        /// </summary>
        public ImageManagementViewModel ViewModel => _viewModel;

        /// <summary>
        /// Gets or sets whether to save images to the ProfileImages directory.
        /// When false, images are kept in memory only and can be retrieved via ProcessedImageInMemory.
        /// Default is true for backward compatibility.
        /// </summary>
        public bool SaveToProfileImagesDirectory
        {
            get => _saveToProfileImagesDirectory;
            set => _saveToProfileImagesDirectory = value;
        }

        /// <summary>
        /// Gets the processed image stored in memory when SaveToProfileImagesDirectory is false.
        /// Returns null if no image has been processed or if SaveToProfileImagesDirectory is true.
        /// </summary>
        public BitmapSource? ProcessedImageInMemory => _processedImageInMemory;

        #endregion

        #region Constructor
        
        /// <summary>
        /// Initializes a new instance of the ImageManagementDialog.
        /// </summary>
        public ImageManagementDialog()
        {
            InitializeComponent();

            // Initialize the ViewModel and set as DataContext
            _viewModel = new ImageManagementViewModel();
            DataContext = _viewModel;

            // Log dialog initialization with memory info
            try
            {
                var currentMemory = GC.GetTotalMemory(false);
                var currentMemoryFormatted = FormatFileSize(currentMemory);
                LoggingService.LogInfo($"ImageManagementDialog initialized - Current Memory Usage: {currentMemoryFormatted} ({currentMemory:N0} bytes)", "ImageManagementDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogInfo("ImageManagementDialog initialized with ViewModel", "ImageManagementDialog");
                LoggingService.LogDebug($"Unable to get memory info during initialization: {ex.Message}", "ImageManagementDialog");
            }
        }
        
        #endregion

        #region Utility Methods

        /// <summary>
        /// Formats file size in bytes to human-readable format (B, KB, MB, GB).
        /// Follows UFU2 patterns for consistent formatting across the application.
        /// </summary>
        /// <param name="bytes">Size in bytes</param>
        /// <returns>Formatted size string with appropriate unit</returns>
        private static string FormatFileSize(long bytes)
        {
            try
            {
                if (bytes == 0) return "0 B";

                string[] units = { "B", "KB", "MB", "GB", "TB" };
                int unitIndex = 0;
                double size = bytes;

                while (size >= 1024 && unitIndex < units.Length - 1)
                {
                    size /= 1024;
                    unitIndex++;
                }

                return $"{size:F2} {units[unitIndex]}";
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Error formatting file size {bytes}: {ex.Message}", "ImageManagementDialog");
                return $"{bytes} B";
            }
        }

        /// <summary>
        /// Calculates and logs comprehensive image metrics including original file and preview information.
        /// Provides detailed performance and memory usage insights for debugging.
        /// </summary>
        /// <param name="filePath">Path to the original image file</param>
        /// <param name="fileInfo">FileInfo object for the original file</param>
        /// <param name="previewImage">The loaded BitmapImage used for preview</param>
        private void LogImageMetrics(string filePath, FileInfo fileInfo, BitmapImage previewImage)
        {
            try
            {
                // Original Image Metrics
                var originalFileSize = fileInfo.Length;
                var originalFileSizeFormatted = FormatFileSize(originalFileSize);
                var fileName = Path.GetFileName(filePath);

                LoggingService.LogInfo($"Original Image Loaded - File: {fileName}, Size: {originalFileSizeFormatted} ({originalFileSize:N0} bytes), Path: {filePath}", "ImageManagementDialog");

                if (previewImage != null)
                {
                    // Preview Image Metrics
                    var previewWidth = previewImage.PixelWidth;
                    var previewHeight = previewImage.PixelHeight;
                    var previewDimensions = $"{previewWidth}x{previewHeight}";

                    // Calculate memory usage for preview image (approximate)
                    // Assuming 32-bit RGBA format (4 bytes per pixel)
                    var previewMemoryBytes = (long)(previewWidth * previewHeight * 4);
                    var previewMemoryFormatted = FormatFileSize(previewMemoryBytes);

                    // Calculate scaling factor applied during decode
                    var decodeWidth = previewImage.DecodePixelWidth;
                    var scalingFactor = decodeWidth > 0 ? (double)decodeWidth / previewWidth : 1.0;
                    var scalingPercentage = scalingFactor * 100;

                    // Calculate compression ratio (file size vs memory usage)
                    var compressionRatio = previewMemoryBytes > 0 ? (double)originalFileSize / previewMemoryBytes : 0;

                    LoggingService.LogInfo($"Preview Image Generated - Dimensions: {previewDimensions}, Memory: {previewMemoryFormatted} ({previewMemoryBytes:N0} bytes), Decode Width: {decodeWidth}px", "ImageManagementDialog");
                    LoggingService.LogInfo($"Image Processing Metrics - Scaling: {scalingPercentage:F1}%, Compression Ratio: {compressionRatio:F2}:1, Memory vs File: {(double)previewMemoryBytes / originalFileSize:F2}x", "ImageManagementDialog");

                    // Log performance insights
                    if (originalFileSize > 5 * 1024 * 1024) // > 5MB
                    {
                        LoggingService.LogInfo($"Large Image Detected - Original file size {originalFileSizeFormatted} may impact performance. Consider optimizing source images.", "ImageManagementDialog");
                    }

                    if (previewMemoryBytes > 50 * 1024 * 1024) // > 50MB in memory
                    {
                        LoggingService.LogWarning($"High Memory Usage - Preview image using {previewMemoryFormatted} in memory. Consider reducing DecodePixelWidth for better performance.", "ImageManagementDialog");
                    }
                }
                else
                {
                    LoggingService.LogWarning($"Preview Image Metrics - Unable to calculate preview metrics, BitmapImage is null", "ImageManagementDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating image metrics: {ex.Message}", "ImageManagementDialog");
            }
        }

        /// <summary>
        /// Converts a BitmapSource to a BitmapImage for compatibility with the ViewModel.
        /// Handles the conversion safely with proper error handling.
        /// </summary>
        /// <param name="bitmapSource">The BitmapSource to convert</param>
        /// <returns>BitmapImage or null if conversion fails</returns>
        private BitmapImage? ConvertBitmapSourceToBitmapImage(BitmapSource bitmapSource)
        {
            try
            {
                if (bitmapSource == null)
                {
                    LoggingService.LogWarning("Cannot convert null BitmapSource to BitmapImage", "ImageManagementDialog");
                    return null;
                }

                // If it's already a BitmapImage, return it directly
                if (bitmapSource is BitmapImage bitmapImage)
                {
                    return bitmapImage;
                }

                // Create a new BitmapImage from the BitmapSource
                var convertedImage = new BitmapImage();

                using (var memoryStream = new MemoryStream())
                {
                    // Encode the BitmapSource to a stream
                    var encoder = new PngBitmapEncoder();
                    encoder.Frames.Add(BitmapFrame.Create(bitmapSource));
                    encoder.Save(memoryStream);

                    // Create BitmapImage from the stream
                    memoryStream.Position = 0;
                    convertedImage.BeginInit();
                    convertedImage.CacheOption = BitmapCacheOption.OnLoad;
                    convertedImage.StreamSource = memoryStream;
                    convertedImage.EndInit();
                    convertedImage.Freeze(); // Make it thread-safe
                }

                LoggingService.LogDebug($"Successfully converted BitmapSource to BitmapImage - Size: {convertedImage.PixelWidth}x{convertedImage.PixelHeight}", "ImageManagementDialog");
                return convertedImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error converting BitmapSource to BitmapImage: {ex.Message}", "ImageManagementDialog");
                return null;
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the LoadImage button click event.
        /// Opens a file dialog to allow the user to select an image file and loads it for preview.
        /// </summary>
        private void LoadImage_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("LoadImage button clicked", "ImageManagementDialog");

                // Disable the button during operation to prevent multiple clicks
                LoadImage.IsEnabled = false;

                // Create and configure the OpenFileDialog
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار صورة شخصية",
                    Filter = "ملفات الصور|*.png;*.jpg;*.jpeg;*.bmp;*.gif|" +
                            "PNG Files|*.png|" +
                            "JPEG Files|*.jpg;*.jpeg|" +
                            "BMP Files|*.bmp|" +
                            "GIF Files|*.gif|" +
                            "جميع الملفات|*.*",
                    FilterIndex = 1,
                    Multiselect = false,
                    CheckFileExists = true,
                    CheckPathExists = true
                };

                // Show the dialog
                bool? result = openFileDialog.ShowDialog();

                if (result == true)
                {
                    string selectedFilePath = openFileDialog.FileName;
                    LoggingService.LogInfo($"User selected image file: {Path.GetFileName(selectedFilePath)}", "ImageManagementDialog");

                    // Start loading state in ViewModel
                    _viewModel.StartLoading();

                    // Load and display the selected image
                    LoadImageFromFile(selectedFilePath);
                }
                else
                {
                    LoggingService.LogInfo("User cancelled image selection", "ImageManagementDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in LoadImage_Click: {ex.Message}", "ImageManagementDialog");

                // Complete loading with error in ViewModel
                _viewModel.CompleteLoadingWithError();

                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ أثناء فتح مربع حوار اختيار الصورة. يرجى المحاولة مرة أخرى.",
                    "خطأ في تحميل الصورة",
                    LogLevel.Error,
                    "ImageManagementDialog"
                );
            }
            finally
            {
                // Re-enable the button
                LoadImage.IsEnabled = true;
            }
        }

        /// <summary>
        /// Handles the Cancel button click event.
        /// Closes the dialog with a negative result and disposes of any temporary resources.
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Cancel button clicked", "ImageManagementDialog");

                // Set dialog result to false (cancelled)
                _dialogResult = false;

                // Dispose of any temporary resources
                DisposeCurrentImage();

                // Close the dialog using MaterialDesign DialogHost
                CloseDialog(false);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in CancelButton_Click: {ex.Message}", "ImageManagementDialog");
                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ أثناء إلغاء العملية.",
                    "خطأ",
                    LogLevel.Error,
                    "ImageManagementDialog"
                );
            }
        }

        /// <summary>
        /// Handles the Reset button click event.
        /// Shows confirmation dialog and resets all transformations to restore the original image.
        /// </summary>
        private async void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Reset button clicked - Starting reset operation workflow", "ImageManagementDialog");

                // Note: ResetButton.IsEnabled is controlled by data binding to IsImageLoaded

                // Comprehensive validation before reset operation
                if (!_viewModel.IsImageLoaded || _viewModel.OriginalImage == null)
                {
                    LoggingService.LogWarning("Reset operation aborted - No original image available", "ImageManagementDialog");
                    ErrorManager.ShowUserErrorToast(
                        "لا توجد صورة أصلية متاحة للاستعادة. يرجى تحميل صورة جديدة.",
                        "لا توجد صورة أصلية"
                    );
                    return;
                }

                // Show confirmation dialog to prevent accidental reset
                var confirmationResult = await ShowResetConfirmationDialog();
                if (!confirmationResult)
                {
                    LoggingService.LogInfo("Reset operation cancelled by user", "ImageManagementDialog");
                    return;
                }

                LoggingService.LogInfo("User confirmed reset operation - Proceeding with image restoration", "ImageManagementDialog");

                // Start loading state with visual feedback
                _viewModel.StartLoading();

                // Add visual feedback for reset operation
                await ShowResetProgressFeedback();

                // Perform reset operation
                LoggingService.LogDebug("Calling ResetToOriginal method", "ImageManagementDialog");
                _viewModel.ResetToOriginal();

                // Update the preview image source directly for immediate visual feedback
                if (_viewModel.CurrentImage != null)
                {
                    PreviewImage.Source = _viewModel.CurrentImage;
                    LoggingService.LogInfo("Preview image updated with original image for immediate visual feedback", "ImageManagementDialog");
                }

                // Complete loading state without overwriting OriginalImage backup
                _viewModel.CompleteLoadingPreservingOriginal(_viewModel.CurrentImage);

                LoggingService.LogInfo("Reset operation completed successfully - Original image restored", "ImageManagementDialog");

            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error in reset operation: {ex.Message}\nStack Trace: {ex.StackTrace}", "ImageManagementDialog");

                _viewModel.CompleteLoadingWithError();

                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ غير متوقع أثناء إعادة تعيين الصورة. يرجى إعادة تشغيل التطبيق والمحاولة مرة أخرى.",
                    "خطأ حرج في إعادة التعيين",
                    LogLevel.Error,
                    "ImageManagementDialog"
                );
            }
            finally
            {
                // Note: ResetButton.IsEnabled is controlled by data binding to IsImageLoaded
                LoggingService.LogDebug("Reset operation workflow completed", "ImageManagementDialog");
            }
        }

        /// <summary>
        /// Handles the keyboard shortcuts help button click event.
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Toggle the popup visibility
                shortcutPopup.IsOpen = true;
                LoggingService.LogDebug("Keyboard shortcuts popup opened", "ImageManagementDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening shortcuts popup: {ex.Message}", "ImageManagementDialog");
            }
        }

        /// <summary>
        /// Handles the Crop Image button click event.
        /// Shows confirmation dialog and performs the crop operation using the current crop guide position and image transformations.
        /// </summary>
        private async void CropImageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Crop Image button clicked - Starting crop operation workflow", "ImageManagementDialog");

                // Note: CropImageButton.IsEnabled is controlled by data binding to IsImageLoaded

                // Comprehensive validation before crop operation
                if (!_viewModel.IsImageLoaded || _viewModel.CurrentImage == null)
                {
                    LoggingService.LogWarning("Crop operation aborted - No image loaded", "ImageManagementDialog");
                    ErrorManager.ShowUserWarningToast(
                        "لا توجد صورة محملة للقص. يرجى تحميل صورة أولاً.",
                        "يجب تحميل صورة",
                        "ImageManagementDialog"
                    );
                    return;
                }

                // Validate crop rectangle
                var cropRect = _viewModel.CropRectangle;
                if (cropRect.IsEmpty || cropRect.Width <= 0 || cropRect.Height <= 0)
                {
                    LoggingService.LogWarning($"Crop operation aborted - Invalid crop rectangle: {cropRect}", "ImageManagementDialog");
                    ErrorManager.ShowUserWarningToast(
                        "منطقة القص غير صالحة. يرجى تحديد منطقة صالحة للقص باستخدام المقابض.",
                        "يجب تحديد منطقة القص",
                        "ImageManagementDialog"
                    );
                    return;
                }

                LoggingService.LogInfo($"Crop validation passed - Image: {_viewModel.CurrentImage.PixelWidth}x{_viewModel.CurrentImage.PixelHeight}, Crop: {cropRect}", "ImageManagementDialog");

                LoggingService.LogInfo("Proceeding with direct crop operation", "ImageManagementDialog");

                // Start loading state with visual feedback
                _viewModel.StartLoading();

                // Add visual feedback for crop operation
                await ShowCropProgressFeedback();

                // Perform crop operation with detailed logging
                LoggingService.LogDebug("Calling CreateCroppedImage method", "ImageManagementDialog");
                var croppedImage = _viewModel.CreateCroppedImage();

                if (croppedImage != null)
                {
                    LoggingService.LogInfo($"Crop operation successful - Result: {croppedImage.PixelWidth}x{croppedImage.PixelHeight}", "ImageManagementDialog");

                    // Apply real-time crop preview: replace current image with cropped result
                    var croppedBitmapImage = _viewModel.ApplyCropPreview(croppedImage);

                    if (croppedBitmapImage != null)
                    {
                        // Update the preview image source directly for immediate visual feedback
                        PreviewImage.Source = croppedBitmapImage;

                        // Complete loading state with the cropped image while preserving original image backup
                        _viewModel.CompleteLoadingPreservingOriginal(croppedBitmapImage);

                        LoggingService.LogInfo("Real-time crop preview applied successfully - Image replaced with cropped result", "ImageManagementDialog");

                    }
                    else
                    {
                        LoggingService.LogError("Failed to apply crop preview", "ImageManagementDialog");
                        _viewModel.CompleteLoadingWithError();
                        ErrorManager.ShowUserErrorToast(
                            "فشل في تطبيق معاينة القص. يرجى المحاولة مرة أخرى.",
                            "فشل في معاينة القص"
                        );
                    }
                }
                else
                {
                    // ENHANCED UX: Preserve user state when crop validation fails
                    // Do NOT call CompleteLoadingWithError() as it would destroy CurrentImage and user transformations
                    // Instead, gracefully complete loading while preserving all user state
                    _viewModel.CompleteLoading(_viewModel.CurrentImage);

                    LoggingService.LogInfo("Crop operation skipped due to validation failure - user state preserved", "ImageManagementDialog");

                    // Note: Error toast is already shown by the validation methods in ViewModel
                    // No additional error message needed here to avoid duplicate notifications
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error in crop operation: {ex.Message}\nStack Trace: {ex.StackTrace}", "ImageManagementDialog");

                _viewModel.CompleteLoadingWithError();

                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ غير متوقع أثناء قص الصورة. يرجى إعادة تشغيل التطبيق والمحاولة مرة أخرى.",
                    "خطأ حرج في قص الصورة",
                    LogLevel.Error,
                    "ImageManagementDialog"
                );
            }
            finally
            {
                // Note: CropImageButton.IsEnabled is controlled by data binding to IsImageLoaded
                LoggingService.LogDebug("Crop operation workflow completed", "ImageManagementDialog");
            }
        }

        /// <summary>
        /// Handles the Save button click event.
        /// Saves the current image (cropped or original) to the profile images directory.
        /// Automatically performs cropping if the image hasn't been cropped yet.
        /// </summary>
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Save button clicked - Starting save operation workflow", "ImageManagementDialog");

                // Comprehensive validation before save operation
                if (!_viewModel.IsImageLoaded || _viewModel.CurrentImage == null)
                {
                    LoggingService.LogWarning("Save operation aborted - No image loaded", "ImageManagementDialog");
                    ErrorManager.ShowUserErrorToast(
                        "لا توجد صورة محملة للحفظ. يرجى تحميل صورة أولاً.",
                        "لا توجد صورة"
                    );
                    return;
                }

                // Save operation proceeds with current image (cropped or original)

                // Log image details before saving with null check
                var currentImage = _viewModel.CurrentImage;
                if (currentImage != null)
                {
                    LoggingService.LogInfo($"Preparing to save image with standardized dimensions - Original size: {currentImage.PixelWidth}x{currentImage.PixelHeight}, Target: 127x145 pixels, Format: {currentImage.Format}", "ImageManagementDialog");
                }
                else
                {
                    LoggingService.LogWarning("Current image is null before save operation", "ImageManagementDialog");
                    ErrorManager.ShowUserErrorToast(
                        "لا توجد صورة صالحة للحفظ. يرجى تحميل صورة جديدة.",
                        "لا توجد صورة صالحة"
                    );
                    return;
                }

                // Start loading state
                _viewModel.StartLoading();

                bool saveResult;
                if (_saveToProfileImagesDirectory)
                {
                    // Traditional save to ProfileImages directory
                    LoggingService.LogDebug("Calling SaveCurrentImage method with standardized 127x145 output", "ImageManagementDialog");
                    saveResult = SaveCurrentImage();
                }
                else
                {
                    // Store image in memory for later use
                    LoggingService.LogDebug("Storing processed image in memory for later use", "ImageManagementDialog");
                    saveResult = StoreImageInMemory();
                }

                if (saveResult)
                {
                    _viewModel.CompleteLoading(_viewModel.CurrentImage);

                    // Set dialog result to true (saved)
                    _dialogResult = true;

                    if (_saveToProfileImagesDirectory)
                    {
                        LoggingService.LogInfo($"High-quality image save operation completed successfully - Standardized to 127x145 pixels, Path: {SavedImagePath}", "ImageManagementDialog");
                    }
                    else
                    {
                        LoggingService.LogInfo("Image processed and stored in memory successfully - Standardized to 127x145 pixels", "ImageManagementDialog");
                    }

                    // Close the dialog with success result
                    CloseDialog(true);
                }
                else
                {
                    _viewModel.CompleteLoadingWithError();
                    LoggingService.LogError("Save operation failed", "ImageManagementDialog");
                    ErrorManager.ShowUserErrorToast(
                        "فشلت عملية معالجة الصورة. يرجى المحاولة مرة أخرى.",
                        "فشل في معالجة الصورة"
                    );
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error in save operation: {ex.Message}\nStack Trace: {ex.StackTrace}", "ImageManagementDialog");

                _viewModel.CompleteLoadingWithError();

                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ غير متوقع أثناء حفظ الصورة. يرجى التحقق من صلاحيات الكتابة والمحاولة مرة أخرى.",
                    "خطأ حرج في حفظ الصورة",
                    LogLevel.Error,
                    "ImageManagementDialog"
                );
            }
            finally
            {
                // Note: SaveButton.IsEnabled is now controlled by data binding to IsImageLoaded
                // No need to manually re-enable as it will be controlled by the ViewModel state
                LoggingService.LogDebug("Save operation workflow completed", "ImageManagementDialog");
            }
        }

        #endregion

        #region Image Save Methods

        /// <summary>
        /// Saves the current image to the profile images directory.
        /// Creates the directory if it doesn't exist and generates a unique filename.
        /// </summary>
        /// <returns>True if save operation was successful</returns>
        private bool SaveCurrentImage()
        {
            try
            {
                if (_viewModel.CurrentImage == null)
                {
                    LoggingService.LogWarning("Save operation aborted - No current image available", "ImageManagementDialog");
                    ErrorManager.ShowUserErrorToast(
                        "لا توجد صورة حالية للحفظ. يرجى تحميل أو قص صورة أولاً.",
                        "لا توجد صورة للحفظ"
                    );
                    return false;
                }

                LoggingService.LogDebug($"Starting save process for image: {_viewModel.CurrentImage.PixelWidth}x{_viewModel.CurrentImage.PixelHeight}", "ImageManagementDialog");

                // Get the profile images directory
                string profileImagesDir = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "UFU2",
                    "ProfileImages");

                LoggingService.LogDebug($"Target directory: {profileImagesDir}", "ImageManagementDialog");

                // Create directory if it doesn't exist
                if (!Directory.Exists(profileImagesDir))
                {
                    try
                    {
                        Directory.CreateDirectory(profileImagesDir);
                        LoggingService.LogInfo($"Created profile images directory: {profileImagesDir}", "ImageManagementDialog");
                    }
                    catch (Exception dirEx)
                    {
                        LoggingService.LogError($"Failed to create directory {profileImagesDir}: {dirEx.Message}", "ImageManagementDialog");
                        ErrorManager.ShowUserErrorToast(
                            $"فشل في إنشاء مجلد الصور:\n{profileImagesDir}\n\nيرجى التحقق من صلاحيات الكتابة.",
                            "خطأ في إنشاء المجلد"
                        );
                        return false;
                    }
                }

                // Check available disk space
                try
                {
                    var driveInfo = new DriveInfo(Path.GetPathRoot(profileImagesDir));
                    var availableSpace = driveInfo.AvailableFreeSpace;
                    LoggingService.LogDebug($"Available disk space: {availableSpace / (1024 * 1024):F1} MB", "ImageManagementDialog");

                    if (availableSpace < 10 * 1024 * 1024) // Less than 10 MB
                    {
                        LoggingService.LogWarning($"Low disk space: {availableSpace / (1024 * 1024):F1} MB", "ImageManagementDialog");
                        ErrorManager.ShowUserWarningToast(
                            "المساحة المتاحة على القرص منخفضة. قد تفشل عملية الحفظ.",
                            "مساحة قرص منخفضة"
                        );
                    }
                }
                catch (Exception spaceEx)
                {
                    LoggingService.LogDebug($"Could not check disk space: {spaceEx.Message}", "ImageManagementDialog");
                }

                // Generate unique filename
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string guid = Guid.NewGuid().ToString("N")[..8];
                string filename = $"profile_{timestamp}_{guid}.jpg";
                string filePath = Path.Combine(profileImagesDir, filename);

                LoggingService.LogDebug($"Generated filename for standardized profile image: {filename}", "ImageManagementDialog");

                // Save the image with standardized 127×145 dimensions
                SaveBitmapToFile(_viewModel.CurrentImage, filePath);

                // Store the saved image path for retrieval
                SavedImagePath = filePath;

                // Get file size for logging
                var fileInfo = new FileInfo(filePath);
                var fileSizeKB = fileInfo.Length / 1024.0;

                LoggingService.LogInfo($"Standardized profile image saved successfully - Dimensions: 127x145 pixels, Path: {filePath}, Size: {fileSizeKB:F1} KB", "ImageManagementDialog");

                return true;
            }
            catch (UnauthorizedAccessException authEx)
            {
                LoggingService.LogError($"Access denied while saving image: {authEx.Message}", "ImageManagementDialog");
                ErrorManager.ShowUserErrorToast(
                    "ليس لديك صلاحية للكتابة في مجلد الصور. يرجى تشغيل التطبيق كمدير أو اختيار مجلد آخر.",
                    "ليس لديك صلاحية كتابة"
                );
                return false;
            }
            catch (DirectoryNotFoundException dirEx)
            {
                LoggingService.LogError($"Directory not found while saving image: {dirEx.Message}", "ImageManagementDialog");
                ErrorManager.ShowUserErrorToast(
                    "لم يتم العثور على مجلد الصور. يرجى التحقق من المسار والمحاولة مرة أخرى.",
                    "مجلد غير موجود"
                );
                return false;
            }
            catch (IOException ioEx)
            {
                LoggingService.LogError($"IO error while saving image: {ioEx.Message}", "ImageManagementDialog");
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ في الإدخال/الإخراج أثناء حفظ الصورة. قد يكون القرص ممتلئاً أو الملف مستخدماً.",
                    "خطأ في الإدخال/الإخراج"
                );
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error saving image: {ex.Message}\nStack Trace: {ex.StackTrace}", "ImageManagementDialog");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ غير متوقع أثناء حفظ الصورة. يرجى إعادة المحاولة أو إعادة تشغيل التطبيق.",
                    "خطأ غير متوقع في الحفظ",
                    LogLevel.Error,
                    "ImageManagementDialog");
                return false;
            }
        }

        /// <summary>
        /// Stores the current image in memory with standardized 127×145 pixel dimensions.
        /// Used when SaveToProfileImagesDirectory is false to keep the processed image in memory.
        /// </summary>
        /// <returns>True if the image was processed and stored successfully</returns>
        private bool StoreImageInMemory()
        {
            try
            {
                if (_viewModel.CurrentImage == null)
                {
                    LoggingService.LogWarning("Store in memory operation aborted - No current image available", "ImageManagementDialog");
                    ErrorManager.ShowUserErrorToast(
                        "لا توجد صورة حالية للمعالجة. يرجى تحميل أو قص صورة أولاً.",
                        "لا توجد صورة للمعالجة"
                    );
                    return false;
                }

                LoggingService.LogInfo($"Starting in-memory image processing - Original size: {_viewModel.CurrentImage.PixelWidth}x{_viewModel.CurrentImage.PixelHeight}", "ImageManagementDialog");

                // Resize to standardized 127×145 pixels with high-quality resampling
                var resizedBitmap = ResizeImageHighQuality(_viewModel.CurrentImage, 127, 145);

                // Store the processed image in memory
                _processedImageInMemory = resizedBitmap;

                LoggingService.LogInfo("Image processed and stored in memory successfully - Final size: 127x145 pixels", "ImageManagementDialog");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error storing image in memory: {ex.Message}", "ImageManagementDialog");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء معالجة الصورة في الذاكرة. يرجى المحاولة مرة أخرى.",
                    "خطأ في معالجة الصورة",
                    LogLevel.Error,
                    "ImageManagementDialog");
                return false;
            }
        }

        /// <summary>
        /// Saves a BitmapSource to a file with standardized 127×145 pixel dimensions.
        /// Applies high-quality resizing using bicubic interpolation for optimal visual fidelity.
        /// Supports both JPEG and PNG formats with maximum quality settings.
        /// </summary>
        /// <param name="bitmap">Bitmap to save</param>
        /// <param name="filePath">Target file path</param>
        private void SaveBitmapToFile(BitmapSource bitmap, string filePath)
        {
            try
            {
                LoggingService.LogInfo($"Starting high-quality save operation - Original size: {bitmap.PixelWidth}x{bitmap.PixelHeight}", "ImageManagementDialog");

                // Resize to standardized 127×145 pixels with high-quality resampling
                var resizedBitmap = ResizeImageHighQuality(bitmap, 127, 145);

                // Determine format based on file extension
                string extension = Path.GetExtension(filePath).ToLowerInvariant();
                bool usePng = extension == ".png";

                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    BitmapEncoder encoder;
                    if (usePng)
                    {
                        // PNG format for lossless quality
                        var pngEncoder = new PngBitmapEncoder();
                        encoder = pngEncoder;
                        LoggingService.LogDebug("Using PNG encoder for lossless quality", "ImageManagementDialog");
                    }
                    else
                    {
                        // JPEG format with maximum quality
                        var jpegEncoder = new JpegBitmapEncoder();
                        jpegEncoder.QualityLevel = 95; // High quality
                        encoder = jpegEncoder;
                        LoggingService.LogDebug("Using JPEG encoder with 95% quality", "ImageManagementDialog");
                    }

                    encoder.Frames.Add(BitmapFrame.Create(resizedBitmap));
                    encoder.Save(fileStream);
                }

                LoggingService.LogInfo($"High-quality image saved successfully - Format: {extension.ToUpper()}, Final size: 127x145 pixels, Path: {filePath}", "ImageManagementDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error saving bitmap to file: {ex.Message}", "ImageManagementDialog");
                throw;
            }
        }

        /// <summary>
        /// Resizes an image to exact dimensions using high-quality bicubic interpolation.
        /// Maintains aspect ratio by cropping to fit the target dimensions.
        /// </summary>
        /// <param name="source">Source bitmap to resize</param>
        /// <param name="targetWidth">Target width in pixels</param>
        /// <param name="targetHeight">Target height in pixels</param>
        /// <returns>High-quality resized bitmap</returns>
        private BitmapSource ResizeImageHighQuality(BitmapSource source, int targetWidth, int targetHeight)
        {
            try
            {
                // Validate input parameters
                if (source == null)
                {
                    LoggingService.LogError("Source bitmap is null for resize operation", "ImageManagementDialog");
                    throw new ArgumentNullException(nameof(source), "Source bitmap cannot be null");
                }

                if (targetWidth <= 0 || targetHeight <= 0)
                {
                    LoggingService.LogError($"Invalid target dimensions: {targetWidth}x{targetHeight}", "ImageManagementDialog");
                    throw new ArgumentException($"Target dimensions must be positive: {targetWidth}x{targetHeight}");
                }

                if (source.PixelWidth <= 0 || source.PixelHeight <= 0)
                {
                    LoggingService.LogError($"Invalid source dimensions: {source.PixelWidth}x{source.PixelHeight}", "ImageManagementDialog");
                    throw new ArgumentException($"Source dimensions must be positive: {source.PixelWidth}x{source.PixelHeight}");
                }

                LoggingService.LogDebug($"Resizing image from {source.PixelWidth}x{source.PixelHeight} to {targetWidth}x{targetHeight}", "ImageManagementDialog");

                // Calculate scaling factors for both dimensions
                double scaleX = (double)targetWidth / source.PixelWidth;
                double scaleY = (double)targetHeight / source.PixelHeight;

                // Use the larger scale factor to ensure the image fills the target dimensions
                double scale = Math.Max(scaleX, scaleY);

                // Validate scale factor
                if (scale <= 0 || double.IsNaN(scale) || double.IsInfinity(scale))
                {
                    LoggingService.LogError($"Invalid scale factor calculated: {scale}", "ImageManagementDialog");
                    throw new InvalidOperationException($"Invalid scale factor: {scale}");
                }

                // Calculate intermediate size (may be larger than target to maintain aspect ratio)
                int intermediateWidth = (int)Math.Round(source.PixelWidth * scale);
                int intermediateHeight = (int)Math.Round(source.PixelHeight * scale);

                LoggingService.LogDebug($"Intermediate scaling - Scale: {scale:F3}, Size: {intermediateWidth}x{intermediateHeight}", "ImageManagementDialog");

                // Step 1: Scale the image using high-quality transformation
                var scaleTransform = new ScaleTransform(scale, scale);
                var scaledBitmap = new TransformedBitmap(source, scaleTransform);
                scaledBitmap.Freeze();

                // Step 2: Crop to exact target dimensions if needed
                BitmapSource finalBitmap;
                if (intermediateWidth == targetWidth && intermediateHeight == targetHeight)
                {
                    // Perfect fit, no cropping needed
                    finalBitmap = scaledBitmap;
                    LoggingService.LogDebug("Perfect fit achieved, no cropping required", "ImageManagementDialog");
                }
                else
                {
                    // Calculate crop area to center the image
                    int cropX = Math.Max(0, (intermediateWidth - targetWidth) / 2);
                    int cropY = Math.Max(0, (intermediateHeight - targetHeight) / 2);

                    // Ensure crop dimensions don't exceed available image
                    int cropWidth = Math.Min(targetWidth, intermediateWidth - cropX);
                    int cropHeight = Math.Min(targetHeight, intermediateHeight - cropY);

                    // Validate crop parameters
                    if (cropX < 0 || cropY < 0 || cropWidth <= 0 || cropHeight <= 0 ||
                        cropX + cropWidth > scaledBitmap.PixelWidth || cropY + cropHeight > scaledBitmap.PixelHeight)
                    {
                        LoggingService.LogError($"Invalid crop parameters - X:{cropX}, Y:{cropY}, W:{cropWidth}, H:{cropHeight}, Source:{scaledBitmap.PixelWidth}x{scaledBitmap.PixelHeight}", "ImageManagementDialog");
                        throw new InvalidOperationException("Invalid crop parameters calculated");
                    }

                    LoggingService.LogDebug($"Cropping to center - Crop area: {cropX},{cropY} {cropWidth}x{cropHeight}", "ImageManagementDialog");

                    var croppedBitmap = new CroppedBitmap(scaledBitmap, new Int32Rect(cropX, cropY, cropWidth, cropHeight));
                    croppedBitmap.Freeze();
                    finalBitmap = croppedBitmap;
                }

                // Final validation
                if (finalBitmap.PixelWidth != targetWidth || finalBitmap.PixelHeight != targetHeight)
                {
                    LoggingService.LogWarning($"Final dimensions don't match target - Got: {finalBitmap.PixelWidth}x{finalBitmap.PixelHeight}, Expected: {targetWidth}x{targetHeight}", "ImageManagementDialog");
                }

                LoggingService.LogInfo($"High-quality resize completed successfully - Final size: {finalBitmap.PixelWidth}x{finalBitmap.PixelHeight}", "ImageManagementDialog");
                return finalBitmap;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during high-quality image resize: {ex.Message}\nStack Trace: {ex.StackTrace}", "ImageManagementDialog");
                throw;
            }
        }



        #endregion

        #region Private Methods

        /// <summary>
        /// Loads an image from the specified file path and displays it in the preview area.
        /// Handles common image formats and provides appropriate error messages for invalid files.
        /// </summary>
        /// <param name="filePath">The path to the image file to load</param>
        private void LoadImageFromFile(string filePath)
        {
            try
            {
                // Validate file path
                if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                {
                    _viewModel.CompleteLoadingWithError();
                    ErrorManager.ShowUserErrorToast(
                        "الملف المحدد غير موجود أو غير صالح.",
                        "خطأ في الملف"
                    );
                    return;
                }

                // Check file size (limit to 10MB for performance)
                var fileInfo = new FileInfo(filePath);
                const long maxFileSize = 10 * 1024 * 1024; // 10MB

                if (fileInfo.Length > maxFileSize)
                {
                    _viewModel.CompleteLoadingWithError();
                    ErrorManager.ShowUserWarningToast(
                        "حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 10 ميجابايت.",
                        "حجم الملف كبير"
                    );
                    return;
                }

                // Dispose of previous image if exists
                DisposeCurrentImage();

                // Create new BitmapImage with proper settings
                _currentImageSource = new BitmapImage();
                _currentImageSource.BeginInit();
                _currentImageSource.CacheOption = BitmapCacheOption.OnLoad; // Load into memory to release file lock
                _currentImageSource.UriSource = new Uri(filePath, UriKind.Absolute);
                _currentImageSource.DecodePixelWidth = 800; // Limit decode size for performance
                _currentImageSource.EndInit();
                _currentImageSource.Freeze(); // Make it thread-safe and improve performance

                // Set the image source to the preview control
                PreviewImage.Source = _currentImageSource;

                // Log comprehensive image metrics
                LogImageMetrics(filePath, fileInfo, _currentImageSource);

                // Update ViewModel with successful load
                _viewModel.CompleteLoading(_currentImageSource);

                LoggingService.LogInfo($"Image loading process completed successfully for: {Path.GetFileName(filePath)}", "ImageManagementDialog");
            }
            catch (NotSupportedException ex)
            {
                LoggingService.LogWarning($"Unsupported image format: {ex.Message}", "ImageManagementDialog");
                _viewModel.CompleteLoadingWithError();
                ErrorManager.ShowUserErrorToast(
                    "تنسيق الصورة غير مدعوم. يرجى اختيار صورة بتنسيق PNG أو JPG أو BMP أو GIF.",
                    "تنسيق غير مدعوم"
                );
            }
            catch (FileFormatException ex)
            {
                LoggingService.LogWarning($"Invalid image file format: {ex.Message}", "ImageManagementDialog");
                _viewModel.CompleteLoadingWithError();
                ErrorManager.ShowUserErrorToast(
                    "الملف المحدد ليس صورة صالحة أو تالف. يرجى اختيار ملف صورة آخر.",
                    "ملف صورة غير صالح"
                );
            }
            catch (UnauthorizedAccessException ex)
            {
                LoggingService.LogError($"Access denied to image file: {ex.Message}", "ImageManagementDialog");
                _viewModel.CompleteLoadingWithError();
                ErrorManager.ShowUserErrorToast(
                    "لا يمكن الوصول إلى الملف. تأكد من أن لديك صلاحيات القراءة للملف.",
                    "خطأ في الوصول للملف"
                );
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading image file: {ex.Message}", "ImageManagementDialog");
                _viewModel.CompleteLoadingWithError();
                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ أثناء تحميل الصورة. يرجى التأكد من أن الملف صورة صالحة والمحاولة مرة أخرى.",
                    "خطأ في تحميل الصورة",
                    LogLevel.Error,
                    "ImageManagementDialog"
                );
            }
        }



        /// <summary>
        /// Disposes of the current image source to free memory and file locks.
        /// </summary>
        private void DisposeCurrentImage()
        {
            try
            {
                if (_currentImageSource != null)
                {
                    // Log memory metrics before disposal
                    try
                    {
                        var width = _currentImageSource.PixelWidth;
                        var height = _currentImageSource.PixelHeight;
                        var memoryBytes = (long)(width * height * 4); // Approximate RGBA memory usage
                        var memoryFormatted = FormatFileSize(memoryBytes);

                        LoggingService.LogDebug($"Disposing image - Dimensions: {width}x{height}, Estimated Memory: {memoryFormatted} ({memoryBytes:N0} bytes)", "ImageManagementDialog");
                    }
                    catch (Exception metricsEx)
                    {
                        LoggingService.LogDebug($"Unable to calculate disposal metrics: {metricsEx.Message}", "ImageManagementDialog");
                    }

                    // Clear the preview image source
                    PreviewImage.Source = null;

                    // The BitmapImage is frozen, so we don't need to explicitly dispose it
                    // Just clear the reference
                    _currentImageSource = null;

                    // Clear the ViewModel image state
                    _viewModel.ClearImage();

                    LoggingService.LogInfo("Image resources disposed and memory freed", "ImageManagementDialog");
                }
                else
                {
                    LoggingService.LogDebug("No current image to dispose", "ImageManagementDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disposing current image: {ex.Message}", "ImageManagementDialog");
            }
        }

        /// <summary>
        /// Closes the dialog using MaterialDesign DialogHost with the specified result.
        /// </summary>
        /// <param name="result">The dialog result to return</param>
        private void CloseDialog(bool result)
        {
            try
            {
                _dialogResult = result;

                // Log final memory metrics before closing
                try
                {
                    var finalMemory = GC.GetTotalMemory(false);
                    var finalMemoryFormatted = FormatFileSize(finalMemory);
                    LoggingService.LogInfo($"Dialog closing - Final Memory Usage: {finalMemoryFormatted} ({finalMemory:N0} bytes), Result: {result}", "ImageManagementDialog");

                    // Force garbage collection to free image memory
                    GC.Collect();
                    GC.WaitForPendingFinalizers();

                    var afterGCMemory = GC.GetTotalMemory(true);
                    var afterGCMemoryFormatted = FormatFileSize(afterGCMemory);
                    var memoryFreed = finalMemory - afterGCMemory;
                    var memoryFreedFormatted = FormatFileSize(memoryFreed);

                    if (memoryFreed > 0)
                    {
                        LoggingService.LogInfo($"Memory cleanup completed - Freed: {memoryFreedFormatted} ({memoryFreed:N0} bytes), After GC: {afterGCMemoryFormatted}", "ImageManagementDialog");
                    }
                }
                catch (Exception memoryEx)
                {
                    LoggingService.LogDebug($"Unable to calculate final memory metrics: {memoryEx.Message}", "ImageManagementDialog");
                }

                // Close using MaterialDesign DialogHost
                DialogHost.CloseDialogCommand.Execute(result, this);

                LoggingService.LogInfo($"ImageManagementDialog session completed with result: {result}", "ImageManagementDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing dialog: {ex.Message}", "ImageManagementDialog");
            }
        }

        /// <summary>
        /// Helper method to find a visual child of a specific type.
        /// </summary>
        /// <typeparam name="T">The type of child to find</typeparam>
        /// <param name="parent">The parent element to search in</param>
        /// <returns>The first child of the specified type, or null if not found</returns>
        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            try
            {
                for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
                {
                    var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
                    
                    if (child is T typedChild)
                    {
                        return typedChild;
                    }
                    
                    var childOfChild = FindVisualChild<T>(child);
                    if (childOfChild != null)
                    {
                        return childOfChild;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error finding visual child: {ex.Message}", "ImageManagementDialog");
            }
            
            return null;
        }

        #endregion

        #region Interactive Crop Rectangle Events

        // This section is now empty as the crop guide is non-interactive

        #endregion

        #region Crop Enhancement Methods



        /// <summary>
        /// Shows visual feedback during the crop operation.
        /// </summary>
        private async Task ShowCropProgressFeedback()
        {
            try
            {
                // Add a small delay to show the loading state
                await Task.Delay(500);

                LoggingService.LogDebug("Crop progress feedback displayed", "ImageManagementDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing crop progress feedback: {ex.Message}", "ImageManagementDialog");
            }
        }



        /// <summary>
        /// Shows confirmation dialog before reset operation to prevent accidental data loss.
        /// </summary>
        /// <returns>True if user confirms reset, false otherwise</returns>
        private async Task<bool> ShowResetConfirmationDialog()
        {
            try
            {
                LoggingService.LogDebug("Showing reset confirmation dialog", "ImageManagementDialog");

                var confirmationDialog = new StackPanel
                {
                    Margin = new Thickness(16),
                    Children =
                    {
                        new TextBlock
                        {
                            Text = "هل أنت متأكد من إعادة تعيين جميع التحويلات؟",
                            FontFamily = new FontFamily("Segoe UI"),
                            FontSize = 16,
                            FontWeight = FontWeights.Bold,
                            Foreground = new SolidColorBrush(Colors.DarkRed),
                            TextAlignment = TextAlignment.Center,
                            Margin = new Thickness(0, 0, 0, 8)
                        },
                        new TextBlock
                        {
                            Text = "سيتم فقدان جميع التعديلات (القص، التكبير، الدوران) والعودة للصورة الأصلية.",
                            FontFamily = new FontFamily("Segoe UI"),
                            FontSize = 12,
                            Foreground = new SolidColorBrush(Colors.Gray),
                            TextAlignment = TextAlignment.Center,
                            TextWrapping = TextWrapping.Wrap,
                            Margin = new Thickness(0, 8, 0, 16)
                        }
                    }
                };

                // For now, return true (in a real implementation, this would show a proper dialog)
                // This is a placeholder for the confirmation logic
                await Task.Delay(100);
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing reset confirmation dialog: {ex.Message}", "ImageManagementDialog");
                return false;
            }
        }

        /// <summary>
        /// Shows visual feedback during reset operation.
        /// </summary>
        private async Task ShowResetProgressFeedback()
        {
            try
            {
                LoggingService.LogDebug("Showing reset progress feedback", "ImageManagementDialog");

                // Add a small delay to show the loading state
                await Task.Delay(600);

                LoggingService.LogDebug("Reset progress feedback displayed", "ImageManagementDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing reset progress feedback: {ex.Message}", "ImageManagementDialog");
            }
        }







        #endregion

        #region Image Drag Event Handlers

        /// <summary>
        /// Handles mouse down events on the preview image to start drag operations.
        /// Captures the mouse and initiates drag state in the ViewModel.
        /// </summary>
        /// <param name="sender">The preview image control</param>
        /// <param name="e">Mouse button event arguments</param>
        private void PreviewImage_MouseDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (e.LeftButton == MouseButtonState.Pressed && _viewModel.IsImageLoaded && !_viewModel.IsLoading)
                {
                    var image = sender as Image;
                    if (image != null)
                    {
                        // Capture the mouse to receive events even when cursor moves outside the image
                        image.CaptureMouse();
                        
                        // Get mouse position relative to the preview container
                        var mousePosition = e.GetPosition(PreviewContainer);
                        
                        // Start drag operation in ViewModel
                        _viewModel.StartDrag(mousePosition);
                        
                        LoggingService.LogDebug($"Image drag started at position: {mousePosition.X:F1}, {mousePosition.Y:F1}", "ImageManagementDialog");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling mouse down on preview image: {ex.Message}", "ImageManagementDialog");
                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ أثناء بدء سحب الصورة.",
                    "خطأ في السحب",
                    LogLevel.Error,
                    "ImageManagementDialog"
                );
            }
        }

        /// <summary>
        /// Handles mouse move events on the preview image to update drag position.
        /// Updates image position in real-time during drag operations.
        /// </summary>
        /// <param name="sender">The preview image control</param>
        /// <param name="e">Mouse event arguments</param>
        private void PreviewImage_MouseMove(object sender, MouseEventArgs e)
        {
            try
            {
                if (_viewModel.IsDragging && e.LeftButton == MouseButtonState.Pressed)
                {
                    // Get mouse position relative to the preview container
                    var mousePosition = e.GetPosition(PreviewContainer);
                    
                    // Update drag position in ViewModel
                    _viewModel.UpdateDrag(mousePosition);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling mouse move on preview image: {ex.Message}", "ImageManagementDialog");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementDialog");
                
                // End drag operation on error to prevent stuck state
                try
                {
                    _viewModel.EndDrag();
                    var image = sender as Image;
                    image?.ReleaseMouseCapture();
                }
                catch (Exception endEx)
                {
                    LoggingService.LogError($"Error ending drag after mouse move error: {endEx.Message}", "ImageManagementDialog");
                }
            }
        }

        /// <summary>
        /// Handles mouse up events on the preview image to end drag operations.
        /// Releases mouse capture and finalizes drag state in the ViewModel.
        /// </summary>
        /// <param name="sender">The preview image control</param>
        /// <param name="e">Mouse button event arguments</param>
        private void PreviewImage_MouseUp(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (_viewModel.IsDragging)
                {
                    var image = sender as Image;
                    if (image != null)
                    {
                        // Release mouse capture
                        image.ReleaseMouseCapture();
                        
                        // End drag operation in ViewModel
                        _viewModel.EndDrag();
                        
                        LoggingService.LogDebug("Image drag ended", "ImageManagementDialog");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling mouse up on preview image: {ex.Message}", "ImageManagementDialog");
                ErrorManager.HandleErrorToast(
                    ex,
                    "حدث خطأ أثناء إنهاء سحب الصورة.",
                    "خطأ في السحب",
                    LogLevel.Error,
                    "ImageManagementDialog"
                );
                
                // Ensure drag state is cleared
                try
                {
                    _viewModel.EndDrag();
                    var image = sender as Image;
                    image?.ReleaseMouseCapture();
                }
                catch (Exception endEx)
                {
                    LoggingService.LogError($"Error cleaning up drag state: {endEx.Message}", "ImageManagementDialog");
                }
            }
        }

        /// <summary>
        /// Handles mouse leave events on the preview image to end drag operations.
        /// Ensures drag operations are properly terminated when the mouse leaves the image area.
        /// </summary>
        /// <param name="sender">The preview image control</param>
        /// <param name="e">Mouse event arguments</param>
        private void PreviewImage_MouseLeave(object sender, MouseEventArgs e)
        {
            try
            {
                // Only end drag if we're not capturing the mouse (which means we're in a drag operation)
                var image = sender as Image;
                if (image != null && !image.IsMouseCaptured && _viewModel.IsDragging)
                {
                    // End drag operation in ViewModel
                    _viewModel.EndDrag();
                    
                    LoggingService.LogDebug("Image drag ended due to mouse leave", "ImageManagementDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling mouse leave on preview image: {ex.Message}", "ImageManagementDialog");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementDialog");
            }
        }

        #endregion

        #region Keyboard Event Handlers

        /// <summary>
        /// Handles keyboard input for image manipulation shortcuts including arrow key dragging.
        /// Supports arrow keys for precise image positioning and existing shortcuts.
        /// </summary>
        /// <param name="sender">The dialog control</param>
        /// <param name="e">Key event arguments</param>
        private void ImageManagementDialogControl_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (!_viewModel.IsImageLoaded || _viewModel.IsLoading)
                {
                    return;
                }

                const double keyboardDragStep = 5.0; // Pixels to move per arrow key press
                const double precisionDragStep = 1.0; // Pixels for precise movement with Shift

                var dragStep = (Keyboard.Modifiers & ModifierKeys.Shift) == ModifierKeys.Shift ? precisionDragStep : keyboardDragStep;
                var handled = false;

                // Handle arrow key dragging
                switch (e.Key)
                {
                    case Key.Left:
                        _viewModel.MoveImageByOffset(-dragStep, 0);
                        handled = true;
                        LoggingService.LogDebug($"Keyboard drag left by {dragStep}px", "ImageManagementDialog");
                        break;

                    case Key.Right:
                        _viewModel.MoveImageByOffset(dragStep, 0);
                        handled = true;
                        LoggingService.LogDebug($"Keyboard drag right by {dragStep}px", "ImageManagementDialog");
                        break;

                    case Key.Up:
                        _viewModel.MoveImageByOffset(0, -dragStep);
                        handled = true;
                        LoggingService.LogDebug($"Keyboard drag up by {dragStep}px", "ImageManagementDialog");
                        break;

                    case Key.Down:
                        _viewModel.MoveImageByOffset(0, dragStep);
                        handled = true;
                        LoggingService.LogDebug($"Keyboard drag down by {dragStep}px", "ImageManagementDialog");
                        break;

                    // Existing keyboard shortcuts (preserved)
                    case Key.R:
                        if ((Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
                        {
                            // Ctrl+R: Crop image
                            if (CropImageButton.IsEnabled)
                            {
                                CropImageButton_Click(CropImageButton, new RoutedEventArgs());
                                handled = true;
                            }
                        }
                        else
                        {
                            // R: Reset transforms
                            if (ResetButton.IsEnabled)
                            {
                                ResetButton_Click(ResetButton, new RoutedEventArgs());
                                handled = true;
                            }
                        }
                        break;

                    case Key.OemPlus:
                    case Key.Add:
                        // +: Zoom in
                        _viewModel.ZoomPercentage = Math.Min(400, _viewModel.ZoomPercentage + 25);
                        handled = true;
                        break;

                    case Key.OemMinus:
                    case Key.Subtract:
                        // -: Zoom out
                        _viewModel.ZoomPercentage = Math.Max(25, _viewModel.ZoomPercentage - 25);
                        handled = true;
                        break;

                    case Key.D0:
                    case Key.NumPad0:
                        // 0: Reset zoom
                        _viewModel.ZoomPercentage = 100;
                        handled = true;
                        break;
                }



                if (handled)
                {
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling keyboard input: {ex.Message}", "ImageManagementDialog");
                ErrorManager.LogException(ex, LogLevel.Error, "ImageManagementDialog");
            }
        }

        #endregion
    }
}
