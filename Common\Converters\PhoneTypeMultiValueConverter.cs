using System;
using System.Globalization;
using System.Windows.Data;
using MaterialDesignThemes.Wpf;
using UFU2.Models;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Multi-value converter for phone type data that handles bidirectional conversions between
    /// Arabic display text, English database values, and Material Design icons.
    /// 
    /// This converter supports the following conversion scenarios:
    /// - Arabic Type → English Type (for database storage)
    /// - Arabic Type → Icon (for UI display)
    /// - English Type → Arabic Type (for UI ComboBox display from database)
    /// - English Type → Icon (for UI display)
    /// - Icon → English Type (for reverse lookup)
    /// - Icon → Arabic Type (for reverse lookup)
    /// 
    /// Usage in XAML:
    /// - Parameter "ArabicName": Convert to/from Arabic display text
    /// - Parameter "EnglishName": Convert to/from English database value
    /// - Parameter "Icon": Convert to/from Material Design PackIconKind
    /// - No parameter: Default behavior (PhoneType enum conversion)
    /// </summary>
    public class PhoneTypeMultiValueConverter : IValueConverter
    {
        #region IValueConverter Implementation

        /// <summary>
        /// Converts a phone type value to the target representation based on the parameter.
        /// </summary>
        /// <param name="value">The source value (PhoneType, string, or PackIconKind)</param>
        /// <param name="targetType">The target type for conversion</param>
        /// <param name="parameter">Conversion parameter ("ArabicName", "EnglishName", "Icon", or null)</param>
        /// <param name="culture">Culture information (not used)</param>
        /// <returns>Converted value based on parameter, or original value if conversion fails</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var conversionType = parameter?.ToString()?.ToLowerInvariant() ?? string.Empty;

                return conversionType switch
                {
                    "arabicname" => ConvertToArabicName(value),
                    "englishname" => ConvertToEnglishName(value),
                    "icon" => ConvertToIcon(value),
                    _ => ConvertToPhoneType(value)
                };
            }
            catch (Exception)
            {
                // Return appropriate fallback based on target type and parameter
                return GetFallbackValue(targetType, parameter?.ToString());
            }
        }

        /// <summary>
        /// Converts a value back from the target representation to the source type.
        /// </summary>
        /// <param name="value">The target value to convert back</param>
        /// <param name="targetType">The target type (not used in reverse conversion)</param>
        /// <param name="parameter">Conversion parameter indicating the source type</param>
        /// <param name="culture">Culture information (not used)</param>
        /// <returns>Converted source value, or fallback if conversion fails</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var conversionType = parameter?.ToString()?.ToLowerInvariant() ?? string.Empty;

                return conversionType switch
                {
                    "arabicname" => ConvertFromArabicName(value),
                    "englishname" => ConvertFromEnglishName(value),
                    "icon" => ConvertFromIcon(value),
                    _ => ConvertToPhoneType(value)
                };
            }
            catch (Exception)
            {
                // Return default phone type as fallback
                return PhoneType.Mobile;
            }
        }

        #endregion

        #region Private Conversion Methods

        /// <summary>
        /// Converts any supported input to Arabic display name.
        /// </summary>
        /// <param name="value">Input value (PhoneType, string, or PackIconKind)</param>
        /// <returns>Arabic display name</returns>
        private static object ConvertToArabicName(object value)
        {
            return value switch
            {
                PhoneType phoneType => PhoneTypeIconMapping.GetArabicName(phoneType),
                string stringValue when Enum.TryParse<PhoneType>(stringValue, true, out var parsedType) => 
                    PhoneTypeIconMapping.GetArabicName(parsedType),
                string stringValue => PhoneTypeIconMapping.GetArabicFromIcon(
                    Enum.TryParse<PackIconKind>(stringValue, true, out var iconKind) ? iconKind : PackIconKind.Cellphone),
                PackIconKind iconKind => PhoneTypeIconMapping.GetArabicFromIcon(iconKind),
                _ => PhoneTypeIconMapping.GetArabicName(PhoneType.Mobile)
            };
        }

        /// <summary>
        /// Converts any supported input to English name.
        /// </summary>
        /// <param name="value">Input value (PhoneType, string, or PackIconKind)</param>
        /// <returns>English name</returns>
        private static object ConvertToEnglishName(object value)
        {
            return value switch
            {
                PhoneType phoneType => PhoneTypeIconMapping.GetEnglishName(phoneType),
                string stringValue when Enum.TryParse<PhoneType>(stringValue, true, out var parsedType) => 
                    PhoneTypeIconMapping.GetEnglishName(parsedType),
                string stringValue => PhoneTypeIconMapping.GetEnglishFromIcon(
                    Enum.TryParse<PackIconKind>(stringValue, true, out var iconKind) ? iconKind : PackIconKind.Cellphone),
                PackIconKind iconKind => PhoneTypeIconMapping.GetEnglishFromIcon(iconKind),
                _ => PhoneTypeIconMapping.GetEnglishName(PhoneType.Mobile)
            };
        }

        /// <summary>
        /// Converts any supported input to Material Design icon kind.
        /// </summary>
        /// <param name="value">Input value (PhoneType, string, or PackIconKind)</param>
        /// <returns>PackIconKind</returns>
        private static object ConvertToIcon(object value)
        {
            return value switch
            {
                PhoneType phoneType => PhoneTypeIconMapping.GetIconKind(phoneType),
                string stringValue when Enum.TryParse<PhoneType>(stringValue, true, out var parsedType) => 
                    PhoneTypeIconMapping.GetIconKind(parsedType),
                string stringValue => PhoneTypeIconMapping.GetIconFromArabic(stringValue),
                PackIconKind iconKind => iconKind,
                _ => PhoneTypeIconMapping.GetIconKind(PhoneType.Mobile)
            };
        }

        /// <summary>
        /// Converts any supported input to PhoneType enum.
        /// </summary>
        /// <param name="value">Input value (PhoneType, string, or PackIconKind)</param>
        /// <returns>PhoneType enum</returns>
        private static object ConvertToPhoneType(object value)
        {
            return value switch
            {
                PhoneType phoneType => phoneType,
                string stringValue when Enum.TryParse<PhoneType>(stringValue, true, out var parsedType) => parsedType,
                string stringValue => PhoneTypeIconMapping.GetPhoneTypeFromArabic(stringValue),
                PackIconKind iconKind => PhoneTypeIconMapping.GetPhoneTypeFromIcon(iconKind),
                _ => PhoneType.Mobile
            };
        }

        /// <summary>
        /// Converts from Arabic name back to the appropriate type.
        /// </summary>
        /// <param name="value">Arabic name string</param>
        /// <returns>PhoneType enum</returns>
        private static object ConvertFromArabicName(object value)
        {
            if (value is string arabicName)
            {
                return PhoneTypeIconMapping.GetPhoneTypeFromArabic(arabicName);
            }
            return PhoneType.Mobile;
        }

        /// <summary>
        /// Converts from English name back to the appropriate type.
        /// </summary>
        /// <param name="value">English name string</param>
        /// <returns>PhoneType enum</returns>
        private static object ConvertFromEnglishName(object value)
        {
            if (value is string englishName)
            {
                return PhoneTypeIconMapping.GetPhoneTypeFromEnglish(englishName);
            }
            return PhoneType.Mobile;
        }

        /// <summary>
        /// Converts from icon kind back to the appropriate type.
        /// </summary>
        /// <param name="value">PackIconKind</param>
        /// <returns>PhoneType enum</returns>
        private static object ConvertFromIcon(object value)
        {
            if (value is PackIconKind iconKind)
            {
                return PhoneTypeIconMapping.GetPhoneTypeFromIcon(iconKind);
            }
            return PhoneType.Mobile;
        }

        /// <summary>
        /// Gets an appropriate fallback value based on the target type and parameter.
        /// </summary>
        /// <param name="targetType">The target type for conversion</param>
        /// <param name="parameter">The conversion parameter</param>
        /// <returns>Appropriate fallback value</returns>
        private static object GetFallbackValue(Type targetType, string? parameter)
        {
            var conversionType = parameter?.ToLowerInvariant() ?? string.Empty;

            return conversionType switch
            {
                "arabicname" => PhoneTypeIconMapping.GetArabicName(PhoneType.Mobile),
                "englishname" => PhoneTypeIconMapping.GetEnglishName(PhoneType.Mobile),
                "icon" => PhoneTypeIconMapping.GetIconKind(PhoneType.Mobile),
                _ when targetType == typeof(PackIconKind) => PackIconKind.Cellphone,
                _ when targetType == typeof(string) => PhoneTypeIconMapping.GetArabicName(PhoneType.Mobile),
                _ => PhoneType.Mobile
            };
        }

        #endregion
    }
}
