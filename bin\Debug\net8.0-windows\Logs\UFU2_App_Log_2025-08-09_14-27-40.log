=== UFU2 Application Session Started at 2025-08-09 14:27:40 ===
[2025-08-09 14:27:40.235]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 14:27:40.243]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 14:27:40.249]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 14:27:40.254]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 14:27:40.299]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 14:27:40.304]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 14:27:40.311]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 14:27:40.320]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 14:27:40.326]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 14:27:40.332]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 14:27:40.337]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 14:27:40.342]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 14:27:40.347]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 14:27:40.352]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 14:27:40.358]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 14:27:40.365]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 14:27:40.371]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 14:27:40.377]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 14:27:40.397]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 14:27:40.407]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 14:27:40.417]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 14:27:40.433]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 14:27:40.446]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 14:27:40.473]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 14:27:40.483]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 14:27:40.501]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 14:27:40.509]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 14:27:40.518]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 14:27:40.530]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 14:27:40.545]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 14:27:40.577]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 14:27:40.584]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 14:27:40.594]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 14:27:40.600]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 14:27:40.644]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 14:27:40.670]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 14:27:40.678]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 14:27:40.689]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 14:27:40.732]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 117.22MB working set
[2025-08-09 14:27:40.777]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 14:27:40.784]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 14:27:40.795]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 14:27:40.801]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 14:27:40.810]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 14:27:40.824]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 14:27:40.970]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 14:27:41.069]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 14:27:41.206]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 14:27:41.581]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 14:27:41.591]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 14:27:41.597]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 14:27:41.603]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 14:27:41.616]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_51489795_638903428616142177 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 14:27:41.622]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 14:27:41.629]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:27:41.635]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 14:27:41.656]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 14:27:41.666]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 14:27:41.674]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 14:27:41.681]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 14:27:41.688]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 14:27:41.717]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 14:27:41.741]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 14:27:41.767]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 14:27:41.781]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 14:27:41.789]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 14:27:41.797]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 14:27:41.806]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 14:27:41.812]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 14:27:41.821]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 14:27:41.827]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 14:27:41.834]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 14:27:41.843]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 14:27:41.851]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 14:27:41.857]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 14:27:41.864]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 14:27:41.873]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 14:27:41.879]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 14:27:41.888]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 14:27:41.895]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 14:27:41.903]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 14:27:41.909]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 14:27:41.915]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 14:27:41.922]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 14:27:41.929]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 14:27:41.936]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 14:27:41.944]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 14:27:41.950]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 14:27:41.957]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 14:27:41.964]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 14:27:41.971]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 14:27:41.981]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 14:27:41.995]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 14:27:42.001]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 14:27:42.015]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 14:27:42.024]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 14:27:42.031]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 14:27:42.037]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 14:27:42.043]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 14:27:42.049]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 14:27:42.056]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 14:27:42.062]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 14:27:42.068]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 14:27:42.074]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 14:27:42.081]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 14:27:42.087]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 14:27:42.093]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 14:27:42.101]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 14:27:42.108]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 14:27:42.114]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 14:27:42.128]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 14:27:42.133]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 14:27:42.140]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 14:27:42.148]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 14:27:42.160]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 14:27:42.167]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 14:27:42.173]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 14:27:42.182]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 14:27:42.188]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 1365ms
[2025-08-09 14:27:42.194]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 14:27:42.204]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 14:27:42.241]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 14:27:42.251]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 14:27:42.261]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 14:27:42.270]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 14:27:42.276]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 14:27:42.282]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 14:27:42.289]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:27:42.296]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 14:27:42.304]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:27:42.316]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 14:27:42.323]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:27:42.332]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 14:27:42.344]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 14:27:42.352]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:27:42.414]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:27:42.414]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:27:42.421]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:27:42.425]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:27:42.432]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:27:42.440]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-09 14:27:42.444]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:27:42.450]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:27:42.414]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:27:42.458]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:27:42.468]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:27:42.474]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:27:42.480]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:27:42.486]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:27:42.495]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:27:42.501]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:27:42.508]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:27:42.514]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:27:42.520]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:27:42.537]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:27:42.543]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:27:42.553]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:27:42.559]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:42.566]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 14:27:42.602]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 14:27:42.609]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 14:27:42.615]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 14:27:42.621]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 14:27:42.628]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 14:27:42.634]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 14:27:42.642]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 14:27:42.654]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 14:27:42.665]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 14:27:42.688]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 14:27:42.696]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 14:27:42.706]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 14:27:42.713]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 14:27:42.719]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 14:27:42.726]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 14:27:42.732]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 14:27:42.739]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 14:27:42.745]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 14:27:42.752]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 14:27:42.761]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 14:27:42.770]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:27:42.777]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:27:42.784]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 14:27:42.800]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 14:27:42.840]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 14:27:42.854]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 14:27:42.895]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:27:42.905]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:27:42.912]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:42.919]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 14:27:42.925]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:27:42.932]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:42.940]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:27:42.946]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:27:42.953]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:27:42.959]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:27:42.965]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:27:42.973]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:27:42.979]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:27:42.988]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:27:42.995]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:27:43.002]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:27:43.010]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:27:43.017]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:43.025]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 14:27:43.033]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 14:27:43.040]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 14:27:43.046]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 14:27:43.055]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 14:27:43.061]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 14:27:43.067]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 14:27:43.074]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 14:27:43.081]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 14:27:43.087]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 14:27:43.093]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 14:27:43.099]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:27:43.109]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:27:43.118]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 14:27:43.130]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 14:27:43.138]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 14:27:43.144]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 14:27:43.151]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:27:43.157]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:27:43.164]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:43.171]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 14:27:43.180]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:27:43.187]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:43.193]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:27:43.200]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:27:43.207]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:27:43.213]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:27:43.220]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:27:43.227]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:27:43.234]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:27:43.241]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:27:43.247]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:27:43.257]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:27:43.264]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:27:43.270]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:43.277]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 14:27:43.284]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 14:27:43.291]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 14:27:43.298]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 14:27:43.311]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 14:27:43.317]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 14:27:43.343]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 14:27:43.350]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 14:27:43.384]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 14:27:43.403]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:27:43.412]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:27:43.422]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 14:27:43.430]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 14:27:43.438]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 14:27:43.445]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 14:27:43.475]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:27:43.483]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:27:43.491]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:43.501]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:27:43.511]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:43.518]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 14:27:43.526]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 14:27:43.533]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 14:27:43.547]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 14:27:43.558]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 14:27:43.565]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 14:27:43.573]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 14:27:43.581]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 14:27:43.589]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 14:27:43.597]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 14:27:43.606]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 14:27:43.613]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 14:27:43.621]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 14:27:43.628]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 14:27:43.636]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 14:27:43.657]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 14:27:43.667]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 14:27:43.677]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 14:27:43.690]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 14:27:43.710]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 14:27:43.721]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 14:27:43.733]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:27:43.743]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:27:43.750]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 14:27:43.759]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 14:27:43.777]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 14:27:43.786]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 14:27:43.795]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:27:43.828]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 14:27:43.841]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 14:27:43.871]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 14:27:43.898]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 14:27:43.922]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 14:27:43.955]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 14:27:43.994]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 14:27:44.038]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 14:27:44.049]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 14:27:44.055]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 14:27:44.063]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 14:27:44.069]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 14:27:44.077]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 14:27:44.084]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 14:27:44.091]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 14:27:44.098]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 14:27:44.105]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 14:27:44.128]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 14:27:44.140]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 14:27:44.147]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 14:27:44.157]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 14:27:44.166]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 14:27:44.174]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:44.182]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 14:27:44.189]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 14:27:44.196]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 14:27:44.203]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 14:27:44.214]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 14:27:44.222]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 14:27:44.229]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 14:27:44.238]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 14:27:44.248]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 14:27:44.258]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 14:27:44.266]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 14:27:44.273]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 14:27:44.280]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 14:27:44.289]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:44.298]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 14:27:44.305]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:44.314]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:44.323]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:44.329]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 14:27:44.336]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:44.345]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:44.351]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 14:27:44.370]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 14:27:44.465]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 14:27:44.477]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:44.512]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 14:27:44.519]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 14:27:44.527]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:44.546]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 14:27:44.552]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 14:27:44.560]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 14:27:44.567]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 14:27:44.576]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 14:27:44.591]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 14:27:44.939]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:45.065]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:27:45.097]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 14:27:45.108]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 14:27:45.135]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 14:27:45.147]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 14:27:45.153]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 14:27:45.177]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.232]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 14:27:45.247]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:27:45.262]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-09 14:27:45.265]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 14:27:45.273]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.279]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 14:27:45.283]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 14:27:45.291]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 14:27:45.294]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 127ms
[2025-08-09 14:27:45.298]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:45.310]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 2 clients, 0 activities
[2025-08-09 14:27:45.328]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-09 14:27:45.339]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 14:27:45.346]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 14:27:45.354]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 14:27:45.360]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 14:27:45.368]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 14:27:45.373]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 14:27:45.379]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 14:27:45.385]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 14:27:45.391]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 14:27:45.397]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 14:27:45.407]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 14:27:45.414]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 14:27:45.421]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 14:27:45.429]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 14:27:45.436]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 14:27:45.442]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 14:27:45.451]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 14:27:45.461]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 14:27:45.468]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-09 14:27:45.495]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.509]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 14:27:45.515]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.521]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.527]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 14:27:45.534]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.543]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.553]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 14:27:45.559]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.566]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.573]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 14:27:45.580]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.590]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.598]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 14:27:45.605]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.611]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.618]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 14:27:45.624]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.630]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 1
[2025-08-09 14:27:45.638]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 177ms
[2025-08-09 14:27:45.646]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 14:27:45.672]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 14:27:45.678]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 34ms
[2025-08-09 14:27:45.686]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 14:27:45.694]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 14:27:45.700]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 16ms
[2025-08-09 14:27:45.710]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 14:27:45.718]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:45.725]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 14:27:45.733]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:45.741]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 14:27:45.747]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:45.759]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 14:27:45.769]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:45.775]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 14:27:45.782]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 14:27:45.789]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 80ms
[2025-08-09 14:27:45.796]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 14:27:45.804]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.818]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 14:27:45.824]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.832]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.840]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 14:27:45.847]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.854]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.860]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 14:27:45.866]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.875]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.881]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 14:27:45.887]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.894]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.900]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 14:27:45.907]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.913]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.919]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 14:27:45.928]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.934]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.941]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 14:27:45.947]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.953]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.960]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 14:27:45.972]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:45.981]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:45.991]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 14:27:46.006]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:46.014]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:46.022]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 14:27:46.028]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:46.035]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 14:27:46.049]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 254ms
[2025-08-09 14:27:46.061]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 14:27:46.069]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 14:27:46.075]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 14:27:46.082]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 14:27:46.089]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 14:27:46.096]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 14:27:46.102]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 14:27:46.111]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 14:27:46.119]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 14:27:46.127]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 14:27:46.136]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 14:27:46.155]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 14:27:46.164]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 14:27:46.175]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 14:27:46.201]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 14:27:46.208]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 14:27:46.214]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 14:27:46.366]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 14:27:46.372]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 14:27:46.379]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 14:27:46.387]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 14:27:46.399]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:27:46.410]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:46.420]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:27:46.426]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:46.435]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:27:46.442]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:46.555]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 14:27:46.562]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 14:27:47.325]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 14:27:47.338]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:47.344]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:47.354]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:47.362]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:47.375]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:47.381]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:48.223]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 14:27:52.054]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.062]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:52.069]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.078]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:52.084]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.094]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:52.127]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.134]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:52.143]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.150]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:52.158]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-09 14:27:52.165]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.172]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-09 14:27:52.180]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-09 14:27:52.186]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-09 14:27:52.194]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 14:27:52.203]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_28167350_638903428722035681 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-09 14:27:52.210]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-09 14:27:52.223]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:27:52.234]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 14:27:52.242]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 14:27:52.250]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-09 14:27:52.270]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 14:27:52.278]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 14:27:52.286]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 14:27:52.377]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 14:27:52.411]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 14:27:52.659]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.668]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:52.675]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.682]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:52.689]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:52.696]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:53.240]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 987.8668ms
[2025-08-09 14:27:53.247]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:53.373]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:53.379]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:53.387]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:53.395]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:53.401]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:53.408]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:53.450]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-09 14:27:53.457]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 14:27:53.466]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 14:27:53.483]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 14:27:53.490]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 14:27:53.497]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 14:27:53.504]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 14:27:53.510]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 14:27:53.518]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:27:53.526]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:27:53.536]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_28167350_638903428722035681
[2025-08-09 14:27:53.544]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 14:27:53.550]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 14:27:53.557]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 14:27:53.563]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 14:27:53.569]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:27:53.577]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:27:53.583]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 14:27:53.611]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-09 14:27:53.617]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-09 14:27:53.624]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 14:27:53.630]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-09 14:27:53.637]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 14:27:53.643]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 14:27:53.650]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 14:27:53.656]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 14:27:53.662]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 14:27:53.670]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 14:27:53.677]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 14:27:53.684]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 14:27:53.693]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_51489795_638903428616142177
[2025-08-09 14:27:53.700]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 14:27:53.707]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 14:27:53.714]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 14:27:53.741]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 14:27:53.748]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 14:27:53.755]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 14:27:53.762]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-09 14:27:53.768]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-09 14:27:53.776]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-09 14:27:53.783]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-09 14:27:53.790]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 14:27:53.799]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 14:27:53.812]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-09 14:27:53.819]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 14:27:53.829]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 14:27:53.839]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 14:27:53.900]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 14:27:53.981]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-09 14:27:54.011]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 14:27:54.022]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-09 14:27:54.028]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-09 14:27:54.036]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-09 14:27:54.044]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 14:27:54.050]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 14:27:54.060]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 14:27:54.066]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 14:27:54.074]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 14:27:54.080]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-09 14:27:54.089]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 14:27:54.095]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-09 14:27:54.103]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 14:27:54.110]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-09 14:27:54.119]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-09 14:27:54.127]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-09 14:27:54.134]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-09 14:27:54.142]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-09 14:27:54.149]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-09 14:27:54.157]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-09 14:27:54.165]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 0.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-09 14:27:54.173]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-09 14:27:54.181]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-09 14:27:54.188]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-09 14:27:54.196]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 50.0%
[2025-08-09 14:27:54.207]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-09 14:27:54.215]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 14:27:54.222]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 14:27:54.229]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 14:27:54.236]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-09 14:27:54.242]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-09 14:27:54.249]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-09 14:27:54.255]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-09 14:27:54.262]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-09 14:27:54.269]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-09 14:27:54.276]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-09 14:27:54.282]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-09 14:27:54.288]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 14:27:54.296]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 14:27:54.331]  	[INFO]		[ResourceManager]	Generated memory leak report: 0 alive resources, 0 dead resources
[2025-08-09 14:27:54.352]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 1
[2025-08-09 14:27:54.359]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 1 potential leaks detected
[2025-08-09 14:27:54.365]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-09 14:27:54.372]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-09 14:27:54.380]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 14:27:54.388]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 14:27:54.399]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-09 14:27:54.409]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-09 14:27:54.419]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 14:27:54.427]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 14:27:54.433]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 2 tracked, 2 disposed, 1 cleanups
[2025-08-09 14:27:54.441]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-09 14:27:54.447]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 14:27:54.454]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-09 14:27:54.461]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 14:27:54.468]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-09 14:27:54.475]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 14:27:54.482]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-09 14:27:54.522]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 14:27:54.531]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-09 14:27:54.541]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-09 14:27:54.548]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-09 14:27:54.556]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-09 14:27:54.570]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-09 14:27:54.577]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-09 14:27:54.583]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 14:27:54.590]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-09 14:27:54.598]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-09 14:27:54.605]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-09 14:27:54.617]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-09 14:27:54.630]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 14:27:54 ===
