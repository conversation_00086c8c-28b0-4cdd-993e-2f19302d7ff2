using System;
using System.Globalization;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using UFU2.Common;

namespace UFU2.Views.UserControls
{
    /// <summary>
    /// Reusable UserControl for window control buttons (minimize, maximize/restore, close).
    /// Provides MaterialDesign theming, Arabic RTL layout support, proper Windows standard behavior,
    /// and comprehensive accessibility features including screen reader support and keyboard navigation.
    /// Follows UFU2's MVVM architecture with DynamicResource bindings for theme integration.
    /// </summary>
    public partial class WindowControlsUserControl : UserControl, IDisposable
    {
        #region Constructors

        /// <summary>
        /// Initializes a new instance of the WindowControlsUserControl.
        /// </summary>
        public WindowControlsUserControl()
        {
            InitializeComponent();
            LoggingService.LogDebug("WindowControlsUserControl initialized", "WindowControlsUserControl");
            
            // Set initial flow direction based on current culture
            UpdateFlowDirection();
            
            // Initialize keyboard support
            InitializeKeyboardSupport();
        }

        #endregion

        #region Dependency Properties

        /// <summary>
        /// Command to execute when Minimize button is clicked.
        /// </summary>
        public static readonly DependencyProperty MinimizeCommandProperty =
            DependencyProperty.Register(nameof(MinimizeCommand), typeof(ICommand), typeof(WindowControlsUserControl), new PropertyMetadata(null));

        public ICommand MinimizeCommand
        {
            get => (ICommand)GetValue(MinimizeCommandProperty);
            set => SetValue(MinimizeCommandProperty, value);
        }

        /// <summary>
        /// Command to execute when Maximize/Restore button is clicked.
        /// </summary>
        public static readonly DependencyProperty MaximizeRestoreCommandProperty =
            DependencyProperty.Register(nameof(MaximizeRestoreCommand), typeof(ICommand), typeof(WindowControlsUserControl), new PropertyMetadata(null));

        public ICommand MaximizeRestoreCommand
        {
            get => (ICommand)GetValue(MaximizeRestoreCommandProperty);
            set => SetValue(MaximizeRestoreCommandProperty, value);
        }

        /// <summary>
        /// Command to execute when Close button is clicked.
        /// </summary>
        public static readonly DependencyProperty CloseCommandProperty =
            DependencyProperty.Register(nameof(CloseCommand), typeof(ICommand), typeof(WindowControlsUserControl), new PropertyMetadata(null));

        public ICommand CloseCommand
        {
            get => (ICommand)GetValue(CloseCommandProperty);
            set => SetValue(CloseCommandProperty, value);
        }

        /// <summary>
        /// Icon kind for the maximize/restore button based on current window state.
        /// </summary>
        public static readonly DependencyProperty MaximizeRestoreIconKindProperty =
            DependencyProperty.Register(nameof(MaximizeRestoreIconKind), typeof(PackIconKind), typeof(WindowControlsUserControl), 
                new PropertyMetadata(PackIconKind.SquareRoundedOutline));

        public PackIconKind MaximizeRestoreIconKind
        {
            get => (PackIconKind)GetValue(MaximizeRestoreIconKindProperty);
            set => SetValue(MaximizeRestoreIconKindProperty, value);
        }

        /// <summary>
        /// Tooltip text for the maximize/restore button based on current window state.
        /// </summary>
        public static readonly DependencyProperty MaximizeRestoreTooltipProperty =
            DependencyProperty.Register(nameof(MaximizeRestoreTooltip), typeof(string), typeof(WindowControlsUserControl), 
                new PropertyMetadata("تكبير"));

        public string MaximizeRestoreTooltip
        {
            get => (string)GetValue(MaximizeRestoreTooltipProperty);
            set => SetValue(MaximizeRestoreTooltipProperty, value);
        }

        /// <summary>
        /// Flow direction for the window controls panel to support RTL layouts.
        /// </summary>
        public static readonly DependencyProperty WindowControlsFlowDirectionProperty =
            DependencyProperty.Register(nameof(WindowControlsFlowDirection), typeof(FlowDirection), typeof(WindowControlsUserControl), 
                new PropertyMetadata(FlowDirection.LeftToRight));

        public FlowDirection WindowControlsFlowDirection
        {
            get => (FlowDirection)GetValue(WindowControlsFlowDirectionProperty);
            set => SetValue(WindowControlsFlowDirectionProperty, value);
        }

        /// <summary>
        /// Horizontal alignment for the window controls panel to support RTL layouts.
        /// </summary>
        public static readonly DependencyProperty WindowControlsAlignmentProperty =
            DependencyProperty.Register(nameof(WindowControlsAlignment), typeof(HorizontalAlignment), typeof(WindowControlsUserControl), 
                new PropertyMetadata(HorizontalAlignment.Right));

        public HorizontalAlignment WindowControlsAlignment
        {
            get => (HorizontalAlignment)GetValue(WindowControlsAlignmentProperty);
            set => SetValue(WindowControlsAlignmentProperty, value);
        }

        /// <summary>
        /// Indicates whether the window is currently maximized.
        /// </summary>
        public static readonly DependencyProperty IsMaximizedProperty =
            DependencyProperty.Register(nameof(IsMaximized), typeof(bool), typeof(WindowControlsUserControl), 
                new PropertyMetadata(false, OnIsMaximizedChanged));

        public bool IsMaximized
        {
            get => (bool)GetValue(IsMaximizedProperty);
            set => SetValue(IsMaximizedProperty, value);
        }

        private static void OnIsMaximizedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is WindowControlsUserControl control)
            {
                control.UpdateMaximizeRestoreButton((bool)e.NewValue);
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the Minimize button is clicked (for backward compatibility).
        /// </summary>
        public event RoutedEventHandler MinimizeClick;

        /// <summary>
        /// Event raised when the Maximize/Restore button is clicked (for backward compatibility).
        /// </summary>
        public event RoutedEventHandler MaximizeRestoreClick;

        /// <summary>
        /// Event raised when the Close button is clicked (for backward compatibility).
        /// </summary>
        public event RoutedEventHandler CloseClick;

        #endregion

        #region Keyboard Support

        /// <summary>
        /// Initializes keyboard support for window controls
        /// </summary>
        private void InitializeKeyboardSupport()
        {
            try
            {
                LoggingService.LogDebug("Initializing keyboard support for window controls", "WindowControlsUserControl");

                // Make the control focusable for keyboard navigation
                this.Focusable = true;
                this.IsTabStop = false; // The individual buttons should be tab stops, not the container

                // Set up keyboard event handlers
                this.KeyDown += OnKeyDown;
                this.PreviewKeyDown += OnPreviewKeyDown;

                // Ensure buttons are properly configured for keyboard access
                ConfigureButtonKeyboardAccess();

                LoggingService.LogDebug("Keyboard support initialized successfully", "WindowControlsUserControl");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing keyboard support: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Configures keyboard access for window control buttons
        /// </summary>
        private void ConfigureButtonKeyboardAccess()
        {
            try
            {
                // Ensure buttons are focusable and have proper tab order
                MinimizeButton.IsTabStop = true;
                MinimizeButton.TabIndex = 1;
                
                MaximizeRestoreButton.IsTabStop = true;
                MaximizeRestoreButton.TabIndex = 2;
                
                CloseButton.IsTabStop = true;
                CloseButton.TabIndex = 3;

                // Add individual button keyboard handlers for additional functionality
                MinimizeButton.KeyDown += OnButtonKeyDown;
                MaximizeRestoreButton.KeyDown += OnButtonKeyDown;
                CloseButton.KeyDown += OnButtonKeyDown;

                LoggingService.LogDebug("Button keyboard access configured", "WindowControlsUserControl");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error configuring button keyboard access: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Handles preview key down events for system keyboard shortcuts
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Key event arguments</param>
        private void OnPreviewKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // Handle system keyboard shortcuts
                if (e.Key == Key.F4 && (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    // Alt+F4 - Close window
                    LoggingService.LogDebug("Alt+F4 detected, executing close command", "WindowControlsUserControl");
                    if (CloseCommand?.CanExecute(null) == true)
                    {
                        CloseCommand.Execute(null);
                        e.Handled = true;
                    }
                }
                else if (e.Key == Key.F9 && (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    // Alt+F9 - Minimize window (custom shortcut)
                    LoggingService.LogDebug("Alt+F9 detected, executing minimize command", "WindowControlsUserControl");
                    if (MinimizeCommand?.CanExecute(null) == true)
                    {
                        MinimizeCommand.Execute(null);
                        e.Handled = true;
                    }
                }
                else if (e.Key == Key.F10 && (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    // Alt+F10 - Maximize/Restore window (custom shortcut)
                    LoggingService.LogDebug("Alt+F10 detected, executing maximize/restore command", "WindowControlsUserControl");
                    if (MaximizeRestoreCommand?.CanExecute(null) == true)
                    {
                        MaximizeRestoreCommand.Execute(null);
                        e.Handled = true;
                    }
                }
                else if (e.Key == Key.Space && (Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    // Alt+Space - Show system menu (handled by Windows, but we log it)
                    LoggingService.LogDebug("Alt+Space detected, system menu should appear", "WindowControlsUserControl");
                    // Don't handle this - let Windows handle the system menu
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling preview key down: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Handles key down events for general keyboard navigation
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Key event arguments</param>
        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // Handle arrow key navigation between buttons
                switch (e.Key)
                {
                    case Key.Left:
                        NavigateToButton(GetPreviousButton());
                        e.Handled = true;
                        break;
                    case Key.Right:
                        NavigateToButton(GetNextButton());
                        e.Handled = true;
                        break;
                    case Key.Home:
                        NavigateToButton(MinimizeButton);
                        e.Handled = true;
                        break;
                    case Key.End:
                        NavigateToButton(CloseButton);
                        e.Handled = true;
                        break;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling key down: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Handles key down events for individual buttons
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Key event arguments</param>
        private void OnButtonKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (sender is Button button)
                {
                    // Handle Enter and Space keys to activate button
                    if (e.Key == Key.Enter || e.Key == Key.Space)
                    {
                        LoggingService.LogDebug($"Enter/Space pressed on {button.Name}", "WindowControlsUserControl");
                        button.Command?.Execute(button.CommandParameter);
                        e.Handled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling button key down: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Gets the next button in the navigation order
        /// </summary>
        /// <returns>The next button to focus</returns>
        private Button GetNextButton()
        {
            try
            {
                var focusedElement = Keyboard.FocusedElement as Button;
                
                if (focusedElement == MinimizeButton)
                    return MaximizeRestoreButton;
                else if (focusedElement == MaximizeRestoreButton)
                    return CloseButton;
                else
                    return MinimizeButton; // Wrap around or default
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting next button: {ex.Message}", "WindowControlsUserControl");
                return MinimizeButton; // Fallback
            }
        }

        /// <summary>
        /// Gets the previous button in the navigation order
        /// </summary>
        /// <returns>The previous button to focus</returns>
        private Button GetPreviousButton()
        {
            try
            {
                var focusedElement = Keyboard.FocusedElement as Button;
                
                if (focusedElement == CloseButton)
                    return MaximizeRestoreButton;
                else if (focusedElement == MaximizeRestoreButton)
                    return MinimizeButton;
                else
                    return CloseButton; // Wrap around or default
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting previous button: {ex.Message}", "WindowControlsUserControl");
                return CloseButton; // Fallback
            }
        }

        /// <summary>
        /// Navigates focus to the specified button
        /// </summary>
        /// <param name="button">The button to focus</param>
        private void NavigateToButton(Button button)
        {
            try
            {
                if (button != null && button.IsEnabled && button.IsVisible)
                {
                    button.Focus();
                    LoggingService.LogDebug($"Focus moved to {button.Name}", "WindowControlsUserControl");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error navigating to button: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Updates accessibility properties based on current state
        /// </summary>
        private void UpdateAccessibilityProperties()
        {
            try
            {
                // Update AutomationProperties based on current window state
                var isMaximized = IsMaximized;
                
                // Update maximize/restore button accessibility properties
                var maximizeRestoreText = isMaximized ? "استعادة النافذة" : "تكبير النافذة";
                var maximizeRestoreHelp = isMaximized ? 
                    "اضغط لاستعادة النافذة إلى حجمها الطبيعي" : 
                    "اضغط لتكبير النافذة لملء الشاشة";

                AutomationProperties.SetName(MaximizeRestoreButton, maximizeRestoreText);
                AutomationProperties.SetHelpText(MaximizeRestoreButton, maximizeRestoreHelp);

                LoggingService.LogDebug($"Accessibility properties updated for maximized state: {isMaximized}", "WindowControlsUserControl");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating accessibility properties: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Ensures high contrast theme compatibility
        /// </summary>
        private void EnsureHighContrastCompatibility()
        {
            try
            {
                // Check if high contrast mode is enabled
                var isHighContrast = SystemParameters.HighContrast;
                
                if (isHighContrast)
                {
                    LoggingService.LogDebug("High contrast mode detected, applying compatible styling", "WindowControlsUserControl");
                    
                    // Apply high contrast compatible styling
                    ApplyHighContrastStyling();
                }
                else
                {
                    LoggingService.LogDebug("Normal contrast mode, using standard styling", "WindowControlsUserControl");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error ensuring high contrast compatibility: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Applies high contrast compatible styling to window controls
        /// </summary>
        private void ApplyHighContrastStyling()
        {
            try
            {
                // Use system colors for high contrast mode
                var systemButtonFace = SystemColors.ControlBrush;
                var systemButtonText = SystemColors.ControlTextBrush;
                var systemHighlight = SystemColors.HighlightBrush;

                // Apply high contrast colors to buttons
                MinimizeButton.Background = systemButtonFace;
                MinimizeButton.Foreground = systemButtonText;
                
                MaximizeRestoreButton.Background = systemButtonFace;
                MaximizeRestoreButton.Foreground = systemButtonText;
                
                CloseButton.Background = systemButtonFace;
                CloseButton.Foreground = systemButtonText;

                LoggingService.LogDebug("High contrast styling applied to window controls", "WindowControlsUserControl");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying high contrast styling: {ex.Message}", "WindowControlsUserControl");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Updates the maximize/restore button icon and tooltip based on window state.
        /// </summary>
        /// <param name="isMaximized">True if window is maximized, false otherwise.</param>
        public void UpdateMaximizeRestoreButton(bool isMaximized)
        {
            try
            {
                if (isMaximized)
                {
                    MaximizeRestoreIconKind = PackIconKind.CheckboxMultipleBlankOutline;
                    MaximizeRestoreTooltip = "استعادة";
                }
                else
                {
                    MaximizeRestoreIconKind = PackIconKind.SquareRoundedOutline;
                    MaximizeRestoreTooltip = "تكبير";
                }

                // Update accessibility properties
                UpdateAccessibilityProperties();

                LoggingService.LogDebug($"Updated maximize/restore button for maximized state: {isMaximized}", "WindowControlsUserControl");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating maximize/restore button: {ex.Message}", "WindowControlsUserControl");
            }
        }

        /// <summary>
        /// Updates the flow direction based on current culture for RTL support.
        /// </summary>
        public void UpdateFlowDirection()
        {
            try
            {
                var currentCulture = CultureInfo.CurrentUICulture;
                var isRtl = currentCulture.TextInfo.IsRightToLeft;
                
                LoggingService.LogDebug($"Updating flow direction for culture: {currentCulture.Name}, IsRTL: {isRtl}", "WindowControlsUserControl");
                
                // In RTL layouts, window controls should be on the left side
                WindowControlsFlowDirection = isRtl ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
                
                // Update the panel alignment for RTL
                if (isRtl)
                {
                    WindowControlsAlignment = HorizontalAlignment.Left;
                }
                else
                {
                    WindowControlsAlignment = HorizontalAlignment.Right;
                }

                LoggingService.LogDebug($"Updated flow direction for RTL: {isRtl}, FlowDirection: {WindowControlsFlowDirection}, Alignment: {WindowControlsAlignment}", "WindowControlsUserControl");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating flow direction: {ex.Message}", "WindowControlsUserControl");
                
                // Fallback to LTR layout
                WindowControlsFlowDirection = FlowDirection.LeftToRight;
                WindowControlsAlignment = HorizontalAlignment.Right;
            }
        }

        /// <summary>
        /// Sets the window state and updates the UI accordingly.
        /// </summary>
        /// <param name="windowState">The current window state.</param>
        public void SetWindowState(WindowState windowState)
        {
            try
            {
                IsMaximized = windowState == WindowState.Maximized;
                LoggingService.LogDebug($"Window state set to: {windowState}", "WindowControlsUserControl");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting window state: {ex.Message}", "WindowControlsUserControl");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// Called when the template is applied to set up initial state.
        /// </summary>
        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();

            try
            {
                // Ensure proper initial state
                UpdateFlowDirection();
                UpdateMaximizeRestoreButton(IsMaximized);
                
                // Initialize accessibility features
                UpdateAccessibilityProperties();
                EnsureHighContrastCompatibility();
                
                // Subscribe to system parameter changes for high contrast mode
                SystemParameters.StaticPropertyChanged += OnSystemParametersChanged;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying template: {ex.Message}", "WindowControlsUserControl");
            }
        }

        #endregion

        #region System Events

        /// <summary>
        /// Handles system parameter changes (e.g., high contrast mode)
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Property changed event arguments</param>
        private void OnSystemParametersChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            try
            {
                if (e.PropertyName == nameof(SystemParameters.HighContrast))
                {
                    LoggingService.LogDebug($"High contrast mode changed to: {SystemParameters.HighContrast}", "WindowControlsUserControl");
                    
                    // Update styling for high contrast mode
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        EnsureHighContrastCompatibility();
                    }));
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling system parameters change: {ex.Message}", "WindowControlsUserControl");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Indicates whether the object has been disposed
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// Disposes the WindowControlsUserControl and cleans up resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        LoggingService.LogDebug("Disposing WindowControlsUserControl", "WindowControlsUserControl");

                        // Unsubscribe from system parameter changes
                        SystemParameters.StaticPropertyChanged -= OnSystemParametersChanged;

                        // Remove event handlers
                        this.KeyDown -= OnKeyDown;
                        this.PreviewKeyDown -= OnPreviewKeyDown;

                        if (MinimizeButton != null)
                            MinimizeButton.KeyDown -= OnButtonKeyDown;
                        if (MaximizeRestoreButton != null)
                            MaximizeRestoreButton.KeyDown -= OnButtonKeyDown;
                        if (CloseButton != null)
                            CloseButton.KeyDown -= OnButtonKeyDown;

                        LoggingService.LogDebug("WindowControlsUserControl disposed successfully", "WindowControlsUserControl");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error disposing WindowControlsUserControl: {ex.Message}", "WindowControlsUserControl");
                    }
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// Finalizer
        /// </summary>
        ~WindowControlsUserControl()
        {
            Dispose(false);
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Handles culture changes to update RTL layout.
        /// </summary>
        private void OnCultureChanged()
        {
            UpdateFlowDirection();
        }

        #endregion
    }

    /// <summary>
    /// Converter to convert boolean IsMaximized to appropriate PackIconKind
    /// </summary>
    public class BoolToMaximizeRestoreIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isMaximized)
            {
                // When maximized, show restore icon; when normal/minimized, show maximize icon
                return isMaximized ? PackIconKind.CheckboxMultipleBlankOutline : PackIconKind.CheckboxBlankOutline;
            }
            // Default to maximize icon for normal state
            return PackIconKind.CheckboxBlankOutline;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter to convert boolean IsMaximized to appropriate Arabic tooltip
    /// </summary>
    public class BoolToMaximizeRestoreTooltipConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isMaximized)
            {
                return isMaximized ? "استعادة" : "تكبير";
            }
            return "تكبير";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}