using System;
using System.Collections.Generic;
using System.Linq;

namespace UFU2.Models
{
    /// <summary>
    /// Data transfer object for activity creation operations.
    /// Contains all necessary information to create a new activity with related entities.
    /// Supports all activity types: MainCommercial, SecondaryCommercial, Craft, and Professional.
    /// </summary>
    public class ActivityCreationData
    {
        /// <summary>
        /// Gets or sets the type of activity (optional).
        /// Valid values: MainCommercial, SecondaryCommercial, Craft, Professional.
        /// </summary>
        public string? ActivityType { get; set; }

        /// <summary>
        /// Gets or sets the current status of the activity (optional).
        /// Values depend on activity type (Active, Inactive, Pending, etc.).
        /// </summary>
        public string? ActivityStatus { get; set; }

        /// <summary>
        /// Gets or sets the activity start date in DD/MM/YYYY format (optional).
        /// Supports special "x" placeholder dates (xx/xx/YYYY) for unknown values.
        /// </summary>
        public string? ActivityStartDate { get; set; }

        /// <summary>
        /// Gets or sets the commercial register number (optional).
        /// Required for commercial activities, optional for others.
        /// </summary>
        public string? CommercialRegister { get; set; }

        /// <summary>
        /// Gets or sets the physical location where the activity is conducted (optional).
        /// Full address including street, city, and region information.
        /// </summary>
        public string? ActivityLocation { get; set; }

        /// <summary>
        /// Gets or sets the NIF (Numéro d'Identification Fiscale) number (optional).
        /// Tax identification number for fiscal purposes.
        /// </summary>
        public string? NifNumber { get; set; }

        /// <summary>
        /// Gets or sets the NIS (Numéro d'Identification Statistique) number (optional).
        /// Statistical identification number for government reporting.
        /// </summary>
        public string? NisNumber { get; set; }

        /// <summary>
        /// Gets or sets the ART (Article) number (optional).
        /// Article number for specific activity classification.
        /// </summary>
        public string? ArtNumber { get; set; }

        /// <summary>
        /// Gets or sets the CPI Daira (Municipal Tax Office) information (optional).
        /// Municipal tax office responsible for local taxation.
        /// </summary>
        public string? CpiDaira { get; set; }

        /// <summary>
        /// Gets or sets the CPI Wilaya (Provincial Tax Office) information (optional).
        /// Provincial tax office responsible for regional taxation.
        /// </summary>
        public string? CpiWilaya { get; set; }

        /// <summary>
        /// Gets or sets the activity update date in DD/MM/YYYY format (optional).
        /// Used to track when activity status was last modified.
        /// </summary>
        public string? ActivityUpdateDate { get; set; }

        /// <summary>
        /// Gets or sets the activity update note (optional).
        /// Contains additional information about status changes or modifications.
        /// Supports Arabic RTL text input.
        /// </summary>
        public string? ActivityUpdateNote { get; set; }

        /// <summary>
        /// Gets or sets the list of activity codes for commercial activities (optional).
        /// Used for MainCommercial and SecondaryCommercial activity types.
        /// References the Algerian business activity code system.
        /// </summary>
        public List<int>? ActivityCodes { get; set; }

        /// <summary>
        /// Gets or sets the activity code for the primary activity entry (optional).
        /// For commercial activities, this may be a single 6-digit code when not using multiple selection.
        /// For craft activities, prefer CraftCode; this is maintained for compatibility.
        /// </summary>
        public string? ActivityCode { get; set; }

        /// <summary>
        /// Gets or sets the activity description for non-commercial activities (optional).
        /// Used for Craft and Professional activity types.
        /// Free text description of the business activity.
        /// </summary>
        public string? ActivityDescription { get; set; }

        /// <summary>
        /// Gets or sets the craft code for craft activities (optional).
        /// Used only for Craft activity type in XX-XX-XXX format.
        /// References CraftTypeBase.Code for validation.
        /// Enforces one-to-one relationship: each client can have only one craft code.
        /// </summary>
        public string? CraftCode { get; set; }

        /// <summary>
        /// Gets or sets the file check states (optional).
        /// Dictionary mapping file check types to their completion status.
        /// Valid keys: CAS, NIF, NIS, RC (commercial), ART (craft), AGR (professional), DEX.
        /// </summary>
        public Dictionary<string, bool>? FileCheckStates { get; set; }

        /// <summary>
        /// Gets or sets the G12 check payment years (optional).
        /// List of years for which G12 payments have been made or are required.
        /// </summary>
        public List<int>? G12CheckYears { get; set; }

        /// <summary>
        /// Gets or sets the BIS check payment years (optional).
        /// List of years for which BIS payments have been made or are required.
        /// </summary>
        public List<int>? BisCheckYears { get; set; }

        /// <summary>
        /// Gets or sets the list of notes associated with the activity (optional).
        /// Can include multiple notes with different flag types and priorities.
        /// </summary>
        public List<NoteCreationData>? Notes { get; set; }

        /// <summary>
        /// Initializes a new instance of the ActivityCreationData class.
        /// </summary>
        public ActivityCreationData()
        {
            ActivityCodes = new List<int>();
            FileCheckStates = new Dictionary<string, bool>();
            G12CheckYears = new List<int>();
            BisCheckYears = new List<int>();
            Notes = new List<NoteCreationData>();
        }

        /// <summary>
        /// Validates the activity creation data based on activity type requirements.
        /// </summary>
        /// <returns>True if the data is valid for activity creation</returns>
        public bool IsValid()
        {
            // Activity type specific validation
            if (!string.IsNullOrWhiteSpace(ActivityType))
            {
                switch (ActivityType)
                {
                    case "MainCommercial":
                    case "SecondaryCommercial":
                        // Commercial activities should have activity codes
                        if (ActivityCodes == null || ActivityCodes.Count == 0)
                        {
                            // Allow empty codes for flexible data entry
                        }
                        break;

                    case "Craft":
                        // Craft activities should have craft code
                        if (string.IsNullOrWhiteSpace(CraftCode))
                        {
                            // Allow empty craft code for flexible data entry
                        }
                        // Validate craft code format if provided
                        if (!string.IsNullOrWhiteSpace(CraftCode) && !IsValidCraftCodeFormat(CraftCode))
                        {
                            return false;
                        }
                        break;

                    case "Professional":
                        // Professional activities should have description
                        if (string.IsNullOrWhiteSpace(ActivityDescription))
                        {
                            // Allow empty description for flexible data entry
                        }
                        break;
                }
            }

            // Validate file check states if provided
            if (FileCheckStates != null)
            {
                var validFileCheckTypes = new[] { "CAS", "NIF", "NIS", "RC", "ART", "AGR", "DEX" };
                foreach (var fileCheck in FileCheckStates.Keys)
                {
                    if (!validFileCheckTypes.Contains(fileCheck))
                        return false;
                }

                // Validate activity type-specific file checks
                if (!string.IsNullOrWhiteSpace(ActivityType))
                {
                    var requiredTypes = GetRequiredFileCheckTypes(ActivityType);
                    // Don't enforce required types for flexible data entry
                }
            }

            // Validate notes if provided
            if (Notes != null)
            {
                foreach (var note in Notes)
                {
                    if (!note.IsValid())
                        return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Gets the required file check types for the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>List of required file check types</returns>
        public static List<string> GetRequiredFileCheckTypes(string activityType)
        {
            var commonTypes = new List<string> { "CAS", "NIF", "NIS", "DEX" };

            return activityType switch
            {
                "MainCommercial" or "SecondaryCommercial" => commonTypes.Concat(new[] { "RC" }).ToList(),
                "Craft" => commonTypes.Concat(new[] { "ART" }).ToList(),
                "Professional" => commonTypes.Concat(new[] { "AGR" }).ToList(),
                _ => commonTypes
            };
        }

        /// <summary>
        /// Gets the display name for an activity type in Arabic.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>Arabic display name</returns>
        public static string GetActivityTypeDisplayName(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "تجاري رئيسي",
                "SecondaryCommercial" => "تجاري ثانوي",
                "Craft" => "حرفي",
                "Professional" => "مهني",
                _ => activityType ?? "غير محدد"
            };
        }

        /// <summary>
        /// Validates craft code format (XX-XX-XXX).
        /// </summary>
        /// <param name="craftCode">The craft code to validate</param>
        /// <returns>True if the format is valid</returns>
        private static bool IsValidCraftCodeFormat(string craftCode)
        {
            if (string.IsNullOrWhiteSpace(craftCode))
                return false;

            // Check format: XX-XX-XXX (2 digits, dash, 2 digits, dash, 3 digits)
            if (craftCode.Length != 9)
                return false;

            if (craftCode[2] != '-' || craftCode[5] != '-')
                return false;

            // Check if all other characters are digits
            for (int i = 0; i < craftCode.Length; i++)
            {
                if (i == 2 || i == 5) continue; // Skip dashes
                if (!char.IsDigit(craftCode[i]))
                    return false;
            }

            return true;
        }
    }

    /// <summary>
    /// Data transfer object for complete activity data retrieval.
    /// Contains all activity information including related entities.
    /// </summary>
    public class ActivityData
    {
        /// <summary>
        /// Gets or sets the unique activity identifier.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the client UID this activity belongs to.
        /// </summary>
        public string ClientUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the type of activity.
        /// </summary>
        public string? ActivityType { get; set; }

        /// <summary>
        /// Gets or sets the current status of the activity.
        /// </summary>
        public string? ActivityStatus { get; set; }

        /// <summary>
        /// Gets or sets the activity start date.
        /// </summary>
        public string? ActivityStartDate { get; set; }

        /// <summary>
        /// Gets or sets the commercial register number.
        /// </summary>
        public string? CommercialRegister { get; set; }

        /// <summary>
        /// Gets or sets the activity location.
        /// </summary>
        public string? ActivityLocation { get; set; }

        /// <summary>
        /// Gets or sets the NIF number.
        /// </summary>
        public string? NifNumber { get; set; }

        /// <summary>
        /// Gets or sets the NIS number.
        /// </summary>
        public string? NisNumber { get; set; }

        /// <summary>
        /// Gets or sets the ART number.
        /// </summary>
        public string? ArtNumber { get; set; }

        /// <summary>
        /// Gets or sets the CPI Daira information.
        /// </summary>
        public string? CpiDaira { get; set; }

        /// <summary>
        /// Gets or sets the CPI Wilaya information.
        /// </summary>
        public string? CpiWilaya { get; set; }

        /// <summary>
        /// Gets or sets the activity update date.
        /// </summary>
        public string? ActivityUpdateDate { get; set; }

        /// <summary>
        /// Gets or sets the activity update note.
        /// </summary>
        public string? ActivityUpdateNote { get; set; }

        /// <summary>
        /// Gets or sets the creation timestamp.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the last update timestamp.
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Gets or sets the list of activity codes.
        /// </summary>
        public List<int> ActivityCodes { get; set; } = new();

        /// <summary>
        /// Gets or sets the activity description.
        /// </summary>
        public string? ActivityDescription { get; set; }

        /// <summary>
        /// Gets or sets the craft code for craft activities.
        /// </summary>
        public string? CraftCode { get; set; }

        /// <summary>
        /// Gets or sets the file check states.
        /// </summary>
        public Dictionary<string, bool> FileCheckStates { get; set; } = new();

        /// <summary>
        /// Gets or sets the G12 check years.
        /// </summary>
        public List<int> G12CheckYears { get; set; } = new();

        /// <summary>
        /// Gets or sets the BIS check years.
        /// </summary>
        public List<int> BisCheckYears { get; set; } = new();

        /// <summary>
        /// Gets or sets the list of notes.
        /// </summary>
        public List<NoteData> Notes { get; set; } = new();
    }
}