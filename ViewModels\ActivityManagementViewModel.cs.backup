using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Input;
using System.Windows.Threading;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Common.Converters;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for managing client activities.
    /// Handles the collection of activities (Commercial, Craft, Professional), including adding, editing, and removing activities.
    /// Extracted from NewClientViewModel to improve maintainability and separation of concerns.
    /// </summary>
    public class ActivityManagementViewModel : BaseViewModel
    {
        #region Private Fields

        private string _selectedActivityType = "MainCommercial";
        private bool _activityIdFormatEnabled = false;

        // Tab-specific activity models for data persistence
        private ActivityModel _mainCommercialActivity = new ActivityModel();
        private ActivityModel _secondaryCommercialActivity = new ActivityModel();
        private ActivityModel _craftActivity = new ActivityModel();
        private ActivityModel _professionalActivity = new ActivityModel();

        // Tab-specific file check state models for data persistence
        private FileCheckStatesModel _mainCommercialFileCheckStates = new FileCheckStatesModel();
        private FileCheckStatesModel _secondaryCommercialFileCheckStates = new FileCheckStatesModel();
        private FileCheckStatesModel _craftFileCheckStates = new FileCheckStatesModel();
        private FileCheckStatesModel _professionalFileCheckStates = new FileCheckStatesModel();

        // Multiple activities collections for each tab
        private List<ActivityTypeBaseModel> _mainCommercialActivities = new List<ActivityTypeBaseModel>();
        private List<ActivityTypeBaseModel> _secondaryCommercialActivities = new List<ActivityTypeBaseModel>();

        // CPI Location collections and selected values
        private List<CpiWilaya> _cpiWilayas = new List<CpiWilaya>();
        private ObservableCollection<CpiDaira> _cpiDairas = new ObservableCollection<CpiDaira>();
        private CpiWilaya? _selectedCpiWilaya;
        private CpiDaira? _selectedCpiDaira;
        private bool _isDairasUpdateInProgress = false;

        // Tab-specific payment years collections for data persistence
        private List<int> _mainCommercialG12SelectedYears = new List<int>();
        private List<int> _mainCommercialBISSelectedYears = new List<int>();
        private List<int> _secondaryCommercialG12SelectedYears = new List<int>();
        private List<int> _secondaryCommercialBISSelectedYears = new List<int>();
        private List<int> _craftG12SelectedYears = new List<int>();
        private List<int> _craftBISSelectedYears = new List<int>();
        private List<int> _professionalG12SelectedYears = new List<int>();
        private List<int> _professionalBISSelectedYears = new List<int>();

        // Tab-specific display text for payment years
        private string _mainCommercialG12DisplayText = "لم يتم التحديد بعد";
        private string _mainCommercialBISDisplayText = "لم يتم التحديد بعد";
        private string _secondaryCommercialG12DisplayText = "لم يتم التحديد بعد";
        private string _secondaryCommercialBISDisplayText = "لم يتم التحديد بعد";
        private string _craftG12DisplayText = "لم يتم التحديد بعد";
        private string _craftBISDisplayText = "لم يتم التحديد بعد";
        private string _professionalG12DisplayText = "لم يتم التحديد بعد";
        private string _professionalBISDisplayText = "لم يتم التحديد بعد";

        // Services
        private readonly ActivityTypeBaseService? _activityTypeService;
        private readonly CraftTypeBaseService? _craftTypeService;
        private readonly CpiLocationService? _cpiLocationService;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the currently selected activity type.
        /// Valid values: "MainCommercial", "SecondaryCommercial", "Craft", "Professional"
        /// Data persistence: Switching between tabs preserves previously entered data.
        /// </summary>
        public string SelectedActivityType
        {
            get => _selectedActivityType;
            set
            {
                if (SetProperty(ref _selectedActivityType, value))
                {
                    // Ensure the current activity has a default status if it's empty
                    EnsureDefaultActivityStatus(value);

                    // Notify that all tab-specific properties have changed to switch to the appropriate tab's data
                    OnPropertyChanged(nameof(CurrentActivity));
                    OnPropertyChanged(nameof(CurrentFileCheckStates));
                    OnPropertyChanged(nameof(G12SelectedYears));
                    OnPropertyChanged(nameof(BISSelectedYears));
                    OnPropertyChanged(nameof(G12DisplayText));
                    OnPropertyChanged(nameof(BISDisplayText));

                    // Synchronize CPI location selections with the current activity
                    SynchronizeCpiLocationSelections();

                    LoggingService.LogInfo($"Activity type switched to: {value}", "ActivityManagementViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the alternate Activity UID format is enabled.
        /// When true, Activity UIDs will include an 's' suffix (e.g., A01_Act1s instead of A01_Act1).
        /// This setting affects UID generation during activity creation workflow.
        /// </summary>
        public bool ActivityIdFormatEnabled
        {
            get => _activityIdFormatEnabled;
            set
            {
                if (SetProperty(ref _activityIdFormatEnabled, value))
                {
                    LoggingService.LogDebug($"Activity ID format toggled: {(value ? "Alternate format (with 's' suffix)" : "Standard format")}", "ActivityManagementViewModel");
                }
            }
        }

        /// <summary>
        /// Gets the current activity model containing all activity-related data for the selected tab.
        /// This model is bound to the NActivityDetailView form fields.
        /// Returns the appropriate ActivityModel instance based on SelectedActivityType.
        /// Data persists when switching between tabs.
        /// </summary>
        public ActivityModel CurrentActivity
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialActivity,
                    "SecondaryCommercial" => _secondaryCommercialActivity,
                    "Craft" => _craftActivity,
                    "Professional" => _professionalActivity,
                    _ => _mainCommercialActivity // Default fallback
                };
            }
        }

        /// <summary>
        /// Gets the current multiple activities collection for the selected tab.
        /// Only MainCommercial and SecondaryCommercial tabs support multiple activities.
        /// </summary>
        public List<ActivityTypeBaseModel> CurrentMultipleActivities
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialActivities,
                    "SecondaryCommercial" => _secondaryCommercialActivities,
                    _ => new List<ActivityTypeBaseModel>() // Empty list for unsupported tabs
                };
            }
        }

        /// <summary>
        /// Gets the current file check states model containing all file check completion status for the selected tab.
        /// This model is bound to the NFileCheckView checkbox controls.
        /// Returns the appropriate FileCheckStatesModel instance based on SelectedActivityType.
        /// File check states persist independently when switching between tabs.
        /// </summary>
        public FileCheckStatesModel CurrentFileCheckStates
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialFileCheckStates,
                    "SecondaryCommercial" => _secondaryCommercialFileCheckStates,
                    "Craft" => _craftFileCheckStates,
                    "Professional" => _professionalFileCheckStates,
                    _ => _mainCommercialFileCheckStates // Default fallback
                };
            }
        }

        /// <summary>
        /// Gets the collection of CPI Wilayas (administrative provinces) for ComboBox binding.
        /// Data is loaded from the APP_Database.db CpiWilayas table via CpiLocationService.
        /// </summary>
        public List<CpiWilaya> CpiWilayas => _cpiWilayas;

        /// <summary>
        /// Gets the collection of CPI Dairas (administrative districts) for ComboBox binding.
        /// This collection is filtered based on the selected wilaya for cascading selection.
        /// Uses ObservableCollection for proper change notifications to the UI.
        /// </summary>
        public ObservableCollection<CpiDaira> CpiDairas => _cpiDairas;

        /// <summary>
        /// Gets or sets the selected CPI Wilaya for the current activity.
        /// When changed, triggers filtering of the CpiDairas collection.
        /// </summary>
        public CpiWilaya? SelectedCpiWilaya
        {
            get => _selectedCpiWilaya;
            set
            {
                if (SetProperty(ref _selectedCpiWilaya, value))
                {
                    // Update the CurrentActivity.CpiWilaya property with the NameAr value
                    CurrentActivity.CpiWilaya = value?.NameAr ?? string.Empty;

                    // Trigger cascading update of dairas
                    _ = UpdateCpiDairasForSelectedWilayaAsync();
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected CPI Daira for the current activity.
        /// Updates the CurrentActivity.CpiDaira property when changed.
        /// Validates that the selected daira belongs to the selected wilaya.
        /// </summary>
        public CpiDaira? SelectedCpiDaira
        {
            get => _selectedCpiDaira;
            set
            {
                if (SetProperty(ref _selectedCpiDaira, value))
                {
                    // Validate that the selected daira belongs to the selected wilaya
                    if (value != null && _selectedCpiWilaya != null && value.WilayaCode != _selectedCpiWilaya.Code)
                    {
                        LoggingService.LogWarning($"Selected daira {value.NameAr} does not belong to selected wilaya {_selectedCpiWilaya.NameAr}", "ActivityManagementViewModel");
                        // Clear the invalid selection
                        _selectedCpiDaira = null;
                        CurrentActivity.CpiDaira = string.Empty;
                        OnPropertyChanged(nameof(SelectedCpiDaira));
                        return;
                    }

                    // Update the CurrentActivity.CpiDaira property with the NameAr value
                    CurrentActivity.CpiDaira = value?.NameAr ?? string.Empty;
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected G12 payment years for the current activity tab.
        /// Returns the appropriate tab-specific collection based on SelectedActivityType.
        /// Data persists independently when switching between tabs.
        /// </summary>
        public List<int> G12SelectedYears
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialG12SelectedYears,
                    "SecondaryCommercial" => _secondaryCommercialG12SelectedYears,
                    "Craft" => _craftG12SelectedYears,
                    "Professional" => _professionalG12SelectedYears,
                    _ => _mainCommercialG12SelectedYears // Default fallback
                };
            }
            set
            {
                var currentList = SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialG12SelectedYears,
                    "SecondaryCommercial" => _secondaryCommercialG12SelectedYears,
                    "Craft" => _craftG12SelectedYears,
                    "Professional" => _professionalG12SelectedYears,
                    _ => _mainCommercialG12SelectedYears
                };

                // Update the appropriate tab-specific collection
                currentList.Clear();
                if (value != null)
                {
                    currentList.AddRange(value);
                }

                // Update the corresponding tab-specific collection reference
                switch (SelectedActivityType)
                {
                    case "MainCommercial":
                        _mainCommercialG12SelectedYears = currentList;
                        break;
                    case "SecondaryCommercial":
                        _secondaryCommercialG12SelectedYears = currentList;
                        break;
                    case "Craft":
                        _craftG12SelectedYears = currentList;
                        break;
                    case "Professional":
                        _professionalG12SelectedYears = currentList;
                        break;
                }

                UpdateG12DisplayText();
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets the selected BIS payment years for the current activity tab.
        /// Returns the appropriate tab-specific collection based on SelectedActivityType.
        /// Data persists independently when switching between tabs.
        /// </summary>
        public List<int> BISSelectedYears
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialBISSelectedYears,
                    "SecondaryCommercial" => _secondaryCommercialBISSelectedYears,
                    "Craft" => _craftBISSelectedYears,
                    "Professional" => _professionalBISSelectedYears,
                    _ => _mainCommercialBISSelectedYears // Default fallback
                };
            }
            set
            {
                var currentList = SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialBISSelectedYears,
                    "SecondaryCommercial" => _secondaryCommercialBISSelectedYears,
                    "Craft" => _craftBISSelectedYears,
                    "Professional" => _professionalBISSelectedYears,
                    _ => _mainCommercialBISSelectedYears
                };

                // Update the appropriate tab-specific collection
                currentList.Clear();
                if (value != null)
                {
                    currentList.AddRange(value);
                }

                // Update the corresponding tab-specific collection reference
                switch (SelectedActivityType)
                {
                    case "MainCommercial":
                        _mainCommercialBISSelectedYears = currentList;
                        break;
                    case "SecondaryCommercial":
                        _secondaryCommercialBISSelectedYears = currentList;
                        break;
                    case "Craft":
                        _craftBISSelectedYears = currentList;
                        break;
                    case "Professional":
                        _professionalBISSelectedYears = currentList;
                        break;
                }

                UpdateBISDisplayText();
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets the display text for G12 payment years for the current activity tab.
        /// Returns the appropriate tab-specific display text based on SelectedActivityType.
        /// </summary>
        public string G12DisplayText
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialG12DisplayText,
                    "SecondaryCommercial" => _secondaryCommercialG12DisplayText,
                    "Craft" => _craftG12DisplayText,
                    "Professional" => _professionalG12DisplayText,
                    _ => _mainCommercialG12DisplayText // Default fallback
                };
            }
        }

        /// <summary>
        /// Gets or sets the display text for BIS payment years for the current activity tab.
        /// Returns the appropriate tab-specific display text based on SelectedActivityType.
        /// </summary>
        public string BISDisplayText
        {
            get
            {
                return SelectedActivityType switch
                {
                    "MainCommercial" => _mainCommercialBISDisplayText,
                    "SecondaryCommercial" => _secondaryCommercialBISDisplayText,
                    "Craft" => _craftBISDisplayText,
                    "Professional" => _professionalBISDisplayText,
                    _ => _mainCommercialBISDisplayText // Default fallback
                };
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to add a new activity to the current tab.
        /// </summary>
        public ICommand AddActivityCommand { get; }

        /// <summary>
        /// Command to edit the status of the current activity.
        /// </summary>
        public ICommand EditStatusCommand { get; }

        /// <summary>
        /// Command to select G12 payment years.
        /// </summary>
        public ICommand SelectG12YearsCommand { get; }

        /// <summary>
        /// Command to select BIS payment years.
        /// </summary>
        public ICommand SelectBISYearsCommand { get; }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the edit status command is executed.
        /// The view should handle this event to open the ActivityStatusUpdateDialog.
        /// </summary>
        public event Action? EditStatusRequested;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ActivityManagementViewModel class.
        /// </summary>
        public ActivityManagementViewModel()
        {
            // Initialize services
            _activityTypeService = ServiceLocator.GetService<ActivityTypeBaseService>();
            _craftTypeService = ServiceLocator.GetService<CraftTypeBaseService>();
            _cpiLocationService = ServiceLocator.GetService<CpiLocationService>();

            // Initialize commands
            AddActivityCommand = new RelayCommand(
                execute: AddActivity,
                commandName: "AddActivity"
            );

            EditStatusCommand = new RelayCommand(
                execute: EditStatus,
                commandName: "EditStatus"
            );

            SelectG12YearsCommand = new RelayCommand(
                execute: SelectG12Years,
                commandName: "SelectG12Years"
            );

            SelectBISYearsCommand = new RelayCommand(
                execute: SelectBISYears,
                commandName: "SelectBISYears"
            );

            // Initialize default activity status for all activity types
            InitializeDefaultActivityStatus();

            // Load CPI location data
            _ = LoadCpiLocationDataAsync();

            // Set up ActivityCode change handlers for automatic description population
            SetupActivityCodeChangeHandlers();

            // Ensure initial synchronization after a short delay to allow data loading
            Dispatcher.BeginInvoke(new Action(() =>
            {
                SynchronizeCpiLocationSelections();
            }), System.Windows.Threading.DispatcherPriority.ApplicationIdle);

            LoggingService.LogDebug("ActivityManagementViewModel initialized", "ActivityManagementViewModel");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Forces synchronization of CPI location selections with the current activity.
        /// This can be called from views when needed to ensure UI consistency.
        /// </summary>
        public void ForceSynchronizeCpiLocationSelections()
        {
            SynchronizeCpiLocationSelections();
        }

        /// <summary>
        /// Validates all activity data.
        /// </summary>
        /// <returns>True if all activity data is valid, false otherwise</returns>
        public bool IsValid()
        {
            try
            {
                // Validate current activity
                var currentActivity = CurrentActivity;
                if (currentActivity == null)
                {
                    LoggingService.LogDebug("Activity validation failed: Current activity is null", "ActivityManagementViewModel");
                    return false;
                }

                // Add specific validation logic based on activity type
                // This can be expanded based on business rules

                LoggingService.LogDebug("Activity validation passed", "ActivityManagementViewModel");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating activity data: {ex.Message}", "ActivityManagementViewModel");
                return false;
            }
        }

        /// <summary>
        /// Clears all activity data.
        /// </summary>
        public void Clear()
        {
            try
            {
                // Clear all activity models
                _mainCommercialActivity = new ActivityModel();
                _secondaryCommercialActivity = new ActivityModel();
                _craftActivity = new ActivityModel();
                _professionalActivity = new ActivityModel();

                // Clear file check states
                _mainCommercialFileCheckStates = new FileCheckStatesModel();
                _secondaryCommercialFileCheckStates = new FileCheckStatesModel();
                _craftFileCheckStates = new FileCheckStatesModel();
                _professionalFileCheckStates = new FileCheckStatesModel();

                // Clear multiple activities
                _mainCommercialActivities.Clear();
                _secondaryCommercialActivities.Clear();

                // Clear payment years
                _mainCommercialG12SelectedYears.Clear();
                _mainCommercialBISSelectedYears.Clear();
                _secondaryCommercialG12SelectedYears.Clear();
                _secondaryCommercialBISSelectedYears.Clear();
                _craftG12SelectedYears.Clear();
                _craftBISSelectedYears.Clear();
                _professionalG12SelectedYears.Clear();
                _professionalBISSelectedYears.Clear();

                // Reset display texts
                _mainCommercialG12DisplayText = "لم يتم التحديد بعد";
                _mainCommercialBISDisplayText = "لم يتم التحديد بعد";
                _secondaryCommercialG12DisplayText = "لم يتم التحديد بعد";
                _secondaryCommercialBISDisplayText = "لم يتم التحديد بعد";
                _craftG12DisplayText = "لم يتم التحديد بعد";
                _craftBISDisplayText = "لم يتم التحديد بعد";
                _professionalG12DisplayText = "لم يتم التحديد بعد";
                _professionalBISDisplayText = "لم يتم التحديد بعد";

                // Clear CPI selections
                SelectedCpiWilaya = null;
                SelectedCpiDaira = null;

                // Initialize default activity status for all activity types
                InitializeDefaultActivityStatus();

                // Reset to default activity type
                SelectedActivityType = "MainCommercial";

                // Notify all properties changed
                OnPropertyChanged(nameof(CurrentActivity));
                OnPropertyChanged(nameof(CurrentFileCheckStates));
                OnPropertyChanged(nameof(G12SelectedYears));
                OnPropertyChanged(nameof(BISSelectedYears));
                OnPropertyChanged(nameof(G12DisplayText));
                OnPropertyChanged(nameof(BISDisplayText));

                LoggingService.LogDebug("Activity management data cleared", "ActivityManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing activity data: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Ensures the current activity has a default status if it's empty.
        /// </summary>
        /// <param name="activityType">The activity type to ensure default status for</param>
        private void EnsureDefaultActivityStatus(string activityType)
        {
            try
            {
                var activity = activityType switch
                {
                    "MainCommercial" => _mainCommercialActivity,
                    "SecondaryCommercial" => _secondaryCommercialActivity,
                    "Craft" => _craftActivity,
                    "Professional" => _professionalActivity,
                    _ => _mainCommercialActivity
                };

                if (string.IsNullOrWhiteSpace(activity.ActivityStatus))
                {
                    activity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus(activityType);
                    LoggingService.LogDebug($"Set default activity status '{activity.ActivityStatus}' for {activityType}", "ActivityManagementViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error ensuring default activity status: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Initializes default ActivityStatus values for all activity types.
        /// Sets the appropriate default status for each activity type using the converter's GetDefaultStatus method.
        /// This ensures ComboBoxes have proper default selections when the form loads or is cleared.
        /// </summary>
        private void InitializeDefaultActivityStatus()
        {
            try
            {
                _mainCommercialActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus("MainCommercial");
                _secondaryCommercialActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus("SecondaryCommercial");
                _craftActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus("Craft");
                _professionalActivity.ActivityStatus = ActivityStatusItemsComboBoxConverter.GetDefaultStatus("Professional");

                LoggingService.LogDebug("Initialized default activity status for all activity types", "ActivityManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing default activity status: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Synchronizes CPI location selections with the current activity.
        /// </summary>
        private void SynchronizeCpiLocationSelections()
        {
            try
            {
                var currentActivity = CurrentActivity;
                if (currentActivity == null) return;

                // Temporarily disable property change notifications to prevent recursion
                var suppressNotifications = true;

                // Find and set the selected wilaya based on the current activity
                if (!string.IsNullOrWhiteSpace(currentActivity.CpiWilaya))
                {
                    var wilaya = _cpiWilayas.FirstOrDefault(w => w.NameAr == currentActivity.CpiWilaya);
                    if (wilaya != null && wilaya != _selectedCpiWilaya)
                    {
                        // Set the field directly to avoid triggering property setter logic
                        _selectedCpiWilaya = wilaya;
                        OnPropertyChanged(nameof(SelectedCpiWilaya));

                        // Update dairas for the selected wilaya
                        _ = UpdateCpiDairasForSelectedWilayaAsync();
                    }
                }
                else
                {
                    // Clear wilaya selection if current activity has no wilaya
                    if (_selectedCpiWilaya != null)
                    {
                        _selectedCpiWilaya = null;
                        OnPropertyChanged(nameof(SelectedCpiWilaya));
                        _cpiDairas.Clear();
                    }
                }

                // Find and set the selected daira based on the current activity
                if (!string.IsNullOrWhiteSpace(currentActivity.CpiDaira))
                {
                    var daira = _cpiDairas.FirstOrDefault(d => d.NameAr == currentActivity.CpiDaira);
                    if (daira != null && daira != _selectedCpiDaira)
                    {
                        // Set the field directly to avoid triggering property setter logic
                        _selectedCpiDaira = daira;
                        OnPropertyChanged(nameof(SelectedCpiDaira));
                    }
                }
                else
                {
                    // Clear daira selection if current activity has no daira
                    if (_selectedCpiDaira != null)
                    {
                        _selectedCpiDaira = null;
                        OnPropertyChanged(nameof(SelectedCpiDaira));
                    }
                }

                LoggingService.LogDebug($"CPI location selections synchronized for {SelectedActivityType}: Wilaya={currentActivity.CpiWilaya}, Daira={currentActivity.CpiDaira}", "ActivityManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error synchronizing CPI location selections: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Loads CPI location data from the service.
        /// </summary>
        private async Task LoadCpiLocationDataAsync()
        {
            try
            {
                if (_cpiLocationService == null)
                {
                    LoggingService.LogWarning("CpiLocationService is not available", "ActivityManagementViewModel");
                    return;
                }

                var wilayas = await _cpiLocationService.GetWilayasAsync();
                _cpiWilayas.Clear();
                _cpiWilayas.AddRange(wilayas);

                LoggingService.LogInfo($"Loaded {wilayas.Count} CPI Wilayas", "ActivityManagementViewModel");

                // Synchronize CPI location selections after data is loaded
                SynchronizeCpiLocationSelections();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading CPI location data: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Updates the CPI Dairas collection based on the selected wilaya.
        /// </summary>
        private async Task UpdateCpiDairasForSelectedWilayaAsync()
        {
            if (_isDairasUpdateInProgress || _selectedCpiWilaya == null || _cpiLocationService == null)
                return;

            try
            {
                _isDairasUpdateInProgress = true;

                var dairas = await _cpiLocationService.GetDairasByWilayaAsync(_selectedCpiWilaya.Code);
                
                _cpiDairas.Clear();
                foreach (var daira in dairas)
                {
                    _cpiDairas.Add(daira);
                }

                LoggingService.LogInfo($"Updated CPI Dairas for wilaya {_selectedCpiWilaya.NameAr}: {dairas.Count} dairas", "ActivityManagementViewModel");

                // Synchronize daira selection after dairas are updated
                var currentActivity = CurrentActivity;
                if (currentActivity != null && !string.IsNullOrWhiteSpace(currentActivity.CpiDaira))
                {
                    var daira = _cpiDairas.FirstOrDefault(d => d.NameAr == currentActivity.CpiDaira);
                    if (daira != null && daira != _selectedCpiDaira)
                    {
                        _selectedCpiDaira = daira;
                        OnPropertyChanged(nameof(SelectedCpiDaira));
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating CPI Dairas: {ex.Message}", "ActivityManagementViewModel");
            }
            finally
            {
                _isDairasUpdateInProgress = false;
            }
        }

        /// <summary>
        /// Updates the G12 display text based on selected years.
        /// </summary>
        private void UpdateG12DisplayText()
        {
            try
            {
                var selectedYears = G12SelectedYears;
                var displayText = selectedYears?.Count > 0 
                    ? string.Join(", ", selectedYears.OrderBy(y => y))
                    : "لم يتم التحديد بعد";

                switch (SelectedActivityType)
                {
                    case "MainCommercial":
                        _mainCommercialG12DisplayText = displayText;
                        break;
                    case "SecondaryCommercial":
                        _secondaryCommercialG12DisplayText = displayText;
                        break;
                    case "Craft":
                        _craftG12DisplayText = displayText;
                        break;
                    case "Professional":
                        _professionalG12DisplayText = displayText;
                        break;
                }

                OnPropertyChanged(nameof(G12DisplayText));
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating G12 display text: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Updates the BIS display text based on selected years.
        /// </summary>
        private void UpdateBISDisplayText()
        {
            try
            {
                var selectedYears = BISSelectedYears;
                var displayText = selectedYears?.Count > 0 
                    ? string.Join(", ", selectedYears.OrderBy(y => y))
                    : "لم يتم التحديد بعد";

                switch (SelectedActivityType)
                {
                    case "MainCommercial":
                        _mainCommercialBISDisplayText = displayText;
                        break;
                    case "SecondaryCommercial":
                        _secondaryCommercialBISDisplayText = displayText;
                        break;
                    case "Craft":
                        _craftBISDisplayText = displayText;
                        break;
                    case "Professional":
                        _professionalBISDisplayText = displayText;
                        break;
                }

                OnPropertyChanged(nameof(BISDisplayText));
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating BIS display text: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Adds a new activity to the current tab.
        /// </summary>
        private void AddActivity()
        {
            try
            {
                LoggingService.LogInfo("Add activity dialog requested", "ActivityManagementViewModel");
                
                // TODO: Implement add activity dialog opening
                // This should integrate with the existing AddActivityDialogViewModel
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding activity: {ex.Message}", "ActivityManagementViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء إضافة النشاط", 
                    "خطأ في إضافة النشاط", 
                    LogLevel.Error, 
                    "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Edits the status of the current activity.
        /// </summary>
        private void EditStatus()
        {
            try
            {
                LoggingService.LogInfo("Edit status requested", "ActivityManagementViewModel");
                EditStatusRequested?.Invoke();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error editing activity status: {ex.Message}", "ActivityManagementViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء تعديل حالة النشاط", 
                    "خطأ في تعديل الحالة", 
                    LogLevel.Error, 
                    "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Selects G12 payment years.
        /// </summary>
        private void SelectG12Years()
        {
            try
            {
                LoggingService.LogInfo("G12 years selection dialog requested", "ActivityManagementViewModel");
                
                // TODO: Implement G12 years selection dialog opening
                // This should integrate with the existing payment years selection dialog
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error selecting G12 years: {ex.Message}", "ActivityManagementViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء اختيار سنوات G12", 
                    "خطأ في اختيار السنوات", 
                    LogLevel.Error, 
                    "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Selects BIS payment years.
        /// </summary>
        private void SelectBISYears()
        {
            try
            {
                LoggingService.LogInfo("BIS years selection dialog requested", "ActivityManagementViewModel");
                
                // TODO: Implement BIS years selection dialog opening
                // This should integrate with the existing payment years selection dialog
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error selecting BIS years: {ex.Message}", "ActivityManagementViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء اختيار سنوات BIS", 
                    "خطأ في اختيار السنوات", 
                    LogLevel.Error, 
                    "ActivityManagementViewModel");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the ViewModel resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from activity code change handlers
                _mainCommercialActivity.PropertyChanged -= OnActivityCodeChanged;
                _secondaryCommercialActivity.PropertyChanged -= OnActivityCodeChanged;
                _craftActivity.PropertyChanged -= OnActivityCodeChanged;

                // Clear collections
                _mainCommercialActivities?.Clear();
                _secondaryCommercialActivities?.Clear();
                _cpiWilayas?.Clear();
                _cpiDairas?.Clear();

                LoggingService.LogDebug("ActivityManagementViewModel disposed", "ActivityManagementViewModel");
            }

            base.Dispose(disposing);
        }

        #endregion

        #region Activity Code Change Handling

        /// <summary>
        /// Sets up PropertyChanged event handlers for ActivityCode changes in MainCommercial, SecondaryCommercial, and Craft tabs.
        /// Enables automatic ActivityDescription population from ActivityTypeBase and CraftTypeBase databases when valid codes are entered.
        /// </summary>
        private void SetupActivityCodeChangeHandlers()
        {
            // Set up PropertyChanged handlers for MainCommercial, SecondaryCommercial, and Craft activities
            _mainCommercialActivity.PropertyChanged += OnActivityCodeChanged;
            _secondaryCommercialActivity.PropertyChanged += OnActivityCodeChanged;
            _craftActivity.PropertyChanged += OnActivityCodeChanged;
        }

        /// <summary>
        /// Handles PropertyChanged events for activity models to detect ActivityCode changes.
        /// Automatically populates ActivityDescription when a valid code is entered.
        /// Supports commercial codes (6 digits) and craft codes (XX-XX-XXX format).
        /// </summary>
        /// <param name="sender">The activity model that raised the event</param>
        /// <param name="e">Property change event arguments</param>
        private async void OnActivityCodeChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // Only handle ActivityCode property changes
            if (e.PropertyName != nameof(ActivityModel.ActivityCode) || sender is not ActivityModel activityModel)
                return;

            // Only process for MainCommercial, SecondaryCommercial, and Craft tabs
            var currentActivityType = SelectedActivityType;
            if (currentActivityType != "MainCommercial" && currentActivityType != "SecondaryCommercial" && currentActivityType != "Craft")
                return;

            // Only process if this is the current activity model
            if (activityModel != CurrentActivity)
                return;

            var activityCode = activityModel.ActivityCode?.Trim();

            // Handle different activity types
            if (currentActivityType == "Craft")
            {
                await HandleCraftCodeChanged(activityCode, activityModel);
            }
            else
            {
                await HandleCommercialCodeChanged(activityCode, activityModel);
            }
        }

        /// <summary>
        /// Handles commercial activity code changes (6-digit codes).
        /// </summary>
        /// <param name="activityCode">The activity code</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task HandleCommercialCodeChanged(string? activityCode, ActivityModel activityModel)
        {
            // Check if we have exactly 6 digits
            if (string.IsNullOrEmpty(activityCode) || activityCode.Length != 6 || !activityCode.All(char.IsDigit))
            {
                // Clear description if code is invalid or incomplete
                if (!string.IsNullOrEmpty(activityModel.ActivityDescription))
                {
                    activityModel.ActivityDescription = string.Empty;
                }
                return;
            }

            // Perform database lookup
            await LookupActivityDescriptionAsync(activityCode, activityModel);
        }

        /// <summary>
        /// Handles craft activity code changes (XX-XX-XXX format).
        /// </summary>
        /// <param name="craftCode">The craft code</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task HandleCraftCodeChanged(string? craftCode, ActivityModel activityModel)
        {
            // Check if we have valid craft code format (XX-XX-XXX)
            if (!IsValidCraftCodeFormat(craftCode))
            {
                // Clear description if code is invalid or incomplete
                if (!string.IsNullOrEmpty(activityModel.ActivityDescription))
                {
                    activityModel.ActivityDescription = string.Empty;
                }
                return;
            }

            // Perform craft database lookup
            await LookupCraftDescriptionAsync(craftCode, activityModel);
        }

        /// <summary>
        /// Validates craft code format (XX-XX-XXX).
        /// </summary>
        /// <param name="code">The code to validate</param>
        /// <returns>True if valid craft code format</returns>
        private static bool IsValidCraftCodeFormat(string? code)
        {
            if (string.IsNullOrEmpty(code)) return false;
            return CraftCodeFormatter.IsValidCraftCodeFormat(code);
        }

        /// <summary>
        /// Performs asynchronous lookup of activity description from ActivityTypeBase database.
        /// Updates the ActivityDescription property if a matching code is found.
        /// </summary>
        /// <param name="activityCode">The 6-digit activity code to lookup</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task LookupActivityDescriptionAsync(string activityCode, ActivityModel activityModel)
        {
            if (_activityTypeService == null)
            {
                LoggingService.LogWarning("ActivityTypeBaseService not available for code lookup", "ActivityManagementViewModel");
                return;
            }

            try
            {
                var activityType = await _activityTypeService.GetByCodeAsync(activityCode);

                if (activityType != null && !string.IsNullOrEmpty(activityType.Description))
                {
                    // Update the description
                    activityModel.ActivityDescription = activityType.Description;

                    // Also add to multiple activities collection if not already present
                    UpdateMultipleActivitiesFromSingleActivity(activityType);

                    LoggingService.LogDebug($"Activity description found for code {activityCode}: {activityType.Description}", "ActivityManagementViewModel");
                }
                else
                {
                    // Clear description if no match found
                    activityModel.ActivityDescription = string.Empty;
                    LoggingService.LogDebug($"No activity description found for code {activityCode}", "ActivityManagementViewModel");

                    // TODO: Open AddActivityDialog to allow user to add the missing activity
                    // This would need to be implemented as an event or callback to the parent ViewModel
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error looking up activity code {activityCode}: {ex.Message}", "ActivityManagementViewModel");
                // Don't clear the description on error, let user keep what they have
            }
        }

        /// <summary>
        /// Performs asynchronous lookup of craft description from CraftTypeBase database.
        /// Updates the ActivityDescription property if a matching craft code is found.
        /// </summary>
        /// <param name="craftCode">The craft code in XX-XX-XXX format to lookup</param>
        /// <param name="activityModel">The activity model to update</param>
        private async Task LookupCraftDescriptionAsync(string craftCode, ActivityModel activityModel)
        {
            if (_craftTypeService == null)
            {
                LoggingService.LogWarning("CraftTypeBaseService not available for craft code lookup", "ActivityManagementViewModel");
                return;
            }

            try
            {
                var craftType = await _craftTypeService.GetByCodeAsync(craftCode);

                if (craftType != null && !string.IsNullOrEmpty(craftType.Description))
                {
                    // Update the description
                    activityModel.ActivityDescription = craftType.Description;

                    LoggingService.LogDebug($"Craft description found for code {craftCode}: {craftType.Description}", "ActivityManagementViewModel");
                }
                else
                {
                    // Clear description if no match found
                    activityModel.ActivityDescription = string.Empty;
                    LoggingService.LogDebug($"No craft description found for code {craftCode}", "ActivityManagementViewModel");

                    // TODO: Open AddCraftTypeDialog to allow user to add the missing craft
                    // This would need to be implemented as an event or callback to the parent ViewModel
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error looking up craft code {craftCode}: {ex.Message}", "ActivityManagementViewModel");
                // Don't clear the description on error, let user keep what they have
            }
        }

        /// <summary>
        /// Updates the multiple activities collection when a single activity is found.
        /// </summary>
        /// <param name="activityType">The activity type to add</param>
        private void UpdateMultipleActivitiesFromSingleActivity(ActivityTypeBaseModel activityType)
        {
            var currentActivities = CurrentMultipleActivities;

            // Check if already exists
            if (!currentActivities.Any(a => a.Code == activityType.Code))
            {
                currentActivities.Add(activityType);
                LoggingService.LogDebug($"Added activity to multiple activities collection: {activityType.Code}", "ActivityManagementViewModel");
            }
        }

        #endregion

        #region Dialog Management Methods

        /// <summary>
        /// Opens the multiple activities management dialog.
        /// </summary>
        public async Task ManageMultipleActivitiesAsync()
        {
            try
            {
                LoggingService.LogInfo("Opening multiple activities management dialog", "ActivityManagementViewModel");

                // Create and show the dialog
                var dialog = new Views.Dialogs.MultipleActivitiesDialog(CurrentMultipleActivities);
                var result = await MaterialDesignThemes.Wpf.DialogHost.Show(dialog, "NewClientDialogHost");

                if (result is bool dialogResult && dialogResult && dialog.DialogResult)
                {
                    // Update the multiple activities collection with the results
                    var updatedActivities = dialog.GetAddedActivities();

                    // Clear current collection and add updated activities
                    CurrentMultipleActivities.Clear();
                    foreach (var activity in updatedActivities)
                    {
                        CurrentMultipleActivities.Add(activity);
                    }

                    LoggingService.LogInfo($"Multiple activities updated: {updatedActivities.Count} activities", "ActivityManagementViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening multiple activities dialog: {ex.Message}", "ActivityManagementViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح إدارة الأنشطة المتعددة",
                    "خطأ في إدارة الأنشطة",
                    LogLevel.Error,
                    "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Shows craft information dialog for the current craft activity.
        /// </summary>
        public async Task ShowCraftInformationAsync()
        {
            try
            {
                if (SelectedActivityType != "Craft")
                {
                    LoggingService.LogWarning("Craft information requested for non-craft activity type", "ActivityManagementViewModel");
                    return;
                }

                var craftCode = CurrentActivity.ActivityCode?.Trim();
                if (string.IsNullOrEmpty(craftCode))
                {
                    ErrorManager.ShowUserWarningToast(
                        "يرجى إدخال رمز الحرفة أولاً",
                        "رمز الحرفة مطلوب",
                        "ActivityManagementViewModel");
                    return;
                }

                LoggingService.LogInfo($"Opening craft information dialog for code: {craftCode}", "ActivityManagementViewModel");

                // Lookup craft type and show the craft information dialog
                var craftType = await _craftTypeService?.GetByCodeAsync(craftCode);
                if (craftType == null)
                {
                    ErrorManager.ShowUserErrorToast(
                        "لم يتم العثور على رمز الحرفة في قاعدة البيانات",
                        "رمز غير موجود",
                        "ActivityManagementViewModel");
                    return;
                }

                var dialog = new Views.Dialogs.CraftInformationDialog(craftType);
                await MaterialDesignThemes.Wpf.DialogHost.Show(dialog, "NewClientDialogHost");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing craft information: {ex.Message}", "ActivityManagementViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء عرض معلومات الحرفة",
                    "خطأ في عرض المعلومات",
                    LogLevel.Error,
                    "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Opens the craft types search dialog.
        /// </summary>
        public async Task SearchCraftTypesAsync()
        {
            try
            {
                if (SelectedActivityType != "Craft")
                {
                    LoggingService.LogWarning("Craft search requested for non-craft activity type", "ActivityManagementViewModel");
                    return;
                }

                LoggingService.LogInfo("Opening craft types search dialog", "ActivityManagementViewModel");

                // Create and show the craft search dialog
                var dialog = new Views.Dialogs.CraftSearchDialog();
                var result = await MaterialDesignThemes.Wpf.DialogHost.Show(dialog, "NewClientDialogHost");

                if (result is CraftTypeBaseModel selectedCraft)
                {
                    // Update the current craft activity with the selected craft
                    CurrentActivity.ActivityCode = selectedCraft.Code;
                    CurrentActivity.ActivityDescription = selectedCraft.Description;

                    LoggingService.LogInfo($"Craft selected: {selectedCraft.Code} - {selectedCraft.Description}", "ActivityManagementViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening craft search dialog: {ex.Message}", "ActivityManagementViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح البحث عن الحرف",
                    "خطأ في البحث",
                    LogLevel.Error,
                    "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Auto-populates payment years based on activity start date.
        /// </summary>
        /// <param name="activityStartDate">The activity start date</param>
        public void AutoPopulatePaymentYears(string activityStartDate)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(activityStartDate))
                {
                    LoggingService.LogDebug("Empty activity start date provided for auto-population", "ActivityManagementViewModel");
                    return;
                }

                // Calculate payment years range based on the start date
                var paymentYears = PaymentYearRangeCalculator.CalculatePaymentYears(activityStartDate);

                if (paymentYears != null && paymentYears.Count > 0)
                {
                    // Set the calculated years for both G12 and BIS
                    G12SelectedYears = new List<int>(paymentYears);
                    BISSelectedYears = new List<int>(paymentYears);

                    // Update display texts
                    UpdateG12DisplayText();
                    UpdateBISDisplayText();

                    LoggingService.LogInfo($"Auto-populated payment years for date {activityStartDate}: {paymentYears.Count} years", "ActivityManagementViewModel");
                }
                else
                {
                    LoggingService.LogDebug($"No payment years calculated for date: {activityStartDate}", "ActivityManagementViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error auto-populating payment years: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        /// <summary>
        /// Clears payment years for the current activity.
        /// </summary>
        public void ClearPaymentYears()
        {
            try
            {
                // Clear payment years for the current activity
                G12SelectedYears = new List<int>();
                BISSelectedYears = new List<int>();

                // Update display texts
                UpdateG12DisplayText();
                UpdateBISDisplayText();

                LoggingService.LogDebug("Payment years cleared for current activity", "ActivityManagementViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing payment years: {ex.Message}", "ActivityManagementViewModel");
            }
        }

        #endregion
    }
}