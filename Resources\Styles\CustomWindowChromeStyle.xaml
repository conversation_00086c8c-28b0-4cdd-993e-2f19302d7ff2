<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:behaviors="clr-namespace:UFU2.Common.Behaviors;assembly=UFU2"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls;assembly=UFU2">

    <!--
        ========================================
        CUSTOM WINDOW CHROME STYLES
        ========================================
        
        This ResourceDictionary defines styles and templates for custom window chrome
        with full RTL support for Arabic interface and MaterialDesign theme integration.
        
        Features:
        - Custom title bar with proper RTL layout support
        - Window control buttons with Arabic tooltips
        - Theme-aware colors using DynamicResource bindings
        - Proper text alignment for RTL languages
        - Icon positioning adjustments for RTL layouts
    -->

    <!--  Converters  -->
    <userControls:BoolToMaximizeRestoreIconConverter x:Key="BoolToMaximizeRestoreIconConverter" />
    <userControls:BoolToMaximizeRestoreTooltipConverter x:Key="BoolToMaximizeRestoreTooltipConverter" />

    <!--  Custom Window Chrome Style  -->
    <Style x:Key="CustomWindowChromeStyle" TargetType="{x:Type Window}">
        <Setter Property="WindowChrome.WindowChrome">
            <Setter.Value>
                <WindowChrome
                    CaptionHeight="32"
                    CornerRadius="7"
                    GlassFrameThickness="0"
                    NonClientFrameEdges="None"
                    ResizeBorderThickness="8"
                    UseAeroCaptionButtons="False" />
            </Setter.Value>
        </Setter>
        <Setter Property="Background" Value="{DynamicResource TitleBarBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource WindowBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Window}">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <!--  Title Bar Row  -->
                                <RowDefinition Height="36" />
                                <!--  Content Row  -->
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <!--  Title Bar  -->
                            <materialDesign:Card Grid.Row="0" Style="{StaticResource BarCardStyle}">
                                <Grid
                                    x:Name="TitleBar"
                                    behaviors:TitleBarBehavior.EnableDoubleClickMaximize="True"
                                    behaviors:TitleBarBehavior.EnableDrag="True"
                                    AutomationProperties.HelpText="اسحب لتحريك النافذة، انقر مرتين لتكبير أو استعادة النافذة"
                                    AutomationProperties.Name="شريط عنوان النافذة"
                                    Background="{DynamicResource TitleBarBackground}">
                                    <Grid.ColumnDefinitions>
                                        <!--  Icon Column  -->
                                        <ColumnDefinition Width="Auto" />
                                        <!--  Title Column  -->
                                        <ColumnDefinition Width="*" />
                                        <!--  Window Controls Column  -->
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!--  Window Icon  -->
                                    <Image
                                        x:Name="WindowIcon"
                                        Grid.Column="0"
                                        Width="16"
                                        Height="16"
                                        Margin="7,0,7,0"
                                        VerticalAlignment="Center"
                                        AutomationProperties.HelpText="أيقونة تطبيق UFU2"
                                        AutomationProperties.Name="أيقونة التطبيق"
                                        Source="{Binding Icon, RelativeSource={RelativeSource TemplatedParent}}"
                                        WindowChrome.IsHitTestVisibleInChrome="False" />

                                    <!--  Window Title  -->
                                    <TextBlock
                                        x:Name="WindowTitle"
                                        Grid.Column="1"
                                        Margin="7,0,7,0"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        AutomationProperties.HelpText="عنوان النافذة الحالية"
                                        AutomationProperties.Name="{Binding Title, RelativeSource={RelativeSource TemplatedParent}}"
                                        FontSize="14"
                                        FontWeight="Normal"
                                        Foreground="{DynamicResource TitleBarForeground}"
                                        Text="{Binding Title, RelativeSource={RelativeSource TemplatedParent}}"
                                        TextAlignment="Center"
                                        TextTrimming="CharacterEllipsis"
                                        WindowChrome.IsHitTestVisibleInChrome="False" />

                                    <!--  Window Controls  -->
                                    <StackPanel
                                        x:Name="WindowControls"
                                        Grid.Column="2"
                                        Margin="7,0,7,0"
                                        HorizontalAlignment="Stretch"
                                        VerticalAlignment="Stretch"
                                        AutomationProperties.HelpText="أزرار تصغير وتكبير وإغلاق النافذة"
                                        AutomationProperties.Name="أزرار التحكم في النافذة"
                                        Orientation="Horizontal"
                                        WindowChrome.IsHitTestVisibleInChrome="True">

                                        <!--  Minimize Button  -->
                                        <Button
                                            x:Name="MinimizeButton"
                                            AutomationProperties.AcceleratorKey="Alt+F9"
                                            AutomationProperties.HelpText="اضغط لتصغير النافذة إلى شريط المهام"
                                            AutomationProperties.Name="تصغير النافذة"
                                            Command="{Binding MinimizeCommand}"
                                            Style="{StaticResource MinmizeButtonStyle}"
                                            TabIndex="1"
                                            ToolTip="تصغير" />

                                        <!--  Maximize/Restore Button  -->
                                        <Button
                                            x:Name="MaximizeRestoreButton"
                                            AutomationProperties.AcceleratorKey="Alt+F10"
                                            AutomationProperties.HelpText="اضغط لتكبير النافذة أو استعادة حجمها الطبيعي"
                                            AutomationProperties.Name="{Binding IsMaximized, Converter={StaticResource BoolToMaximizeRestoreTooltipConverter}}"
                                            Command="{Binding MaximizeRestoreCommand}"
                                            Style="{StaticResource MaximizeButtonStyle}"
                                            TabIndex="2" />

                                        <!--  Close Button  -->
                                        <Button
                                            x:Name="CloseButton"
                                            AutomationProperties.AcceleratorKey="Alt+F4"
                                            AutomationProperties.HelpText="اضغط لإغلاق النافذة والخروج من التطبيق"
                                            AutomationProperties.Name="إغلاق النافذة"
                                            Command="{Binding CloseCommand}"
                                            Style="{StaticResource CloseButtonStyle}"
                                            TabIndex="3"
                                            ToolTip="إغلاق" />
                                    </StackPanel>
                                </Grid>
                            </materialDesign:Card>

                            <!--  Window Content  -->
                            <ContentPresenter
                                x:Name="WindowContent"
                                Grid.Row="1"
                                AutomationProperties.HelpText="المنطقة الرئيسية لمحتوى التطبيق"
                                AutomationProperties.Name="محتوى النافذة الرئيسي"
                                Content="{TemplateBinding Content}"
                                ContentTemplate="{TemplateBinding ContentTemplate}" />
                        </Grid>
                    </Border>

                    <!--  Template Triggers for RTL Layout Adjustments  -->
                    <ControlTemplate.Triggers>
                        <!--  RTL Layout Trigger  -->
                        <DataTrigger Binding="{Binding TitleBarFlowDirection}" Value="RightToLeft">
                            <!--  Adjust icon positioning for RTL  -->
                            <Setter TargetName="WindowIcon" Property="Margin" Value="8,0,8,0" />
                            <!--  Adjust title positioning for RTL  -->
                            <Setter TargetName="WindowTitle" Property="Margin" Value="8,0,8,0" />
                        </DataTrigger>

                        <!--  Window State Triggers  -->
                        <Trigger Property="WindowState" Value="Maximized">
                            <Setter Property="BorderThickness" Value="0" />
                            <Setter TargetName="TitleBar" Property="Margin" Value="0" />
                            <!--  Remove corner radius when maximized to ensure proper boundary fitting  -->
                            <Setter Property="WindowChrome.WindowChrome">
                                <Setter.Value>
                                    <WindowChrome
                                        CaptionHeight="32"
                                        CornerRadius="0"
                                        GlassFrameThickness="0"
                                        NonClientFrameEdges="None"
                                        ResizeBorderThickness="8"
                                        UseAeroCaptionButtons="False" />
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <Trigger Property="WindowState" Value="Normal">
                            <Setter Property="BorderThickness" Value="1" />
                            <Setter TargetName="TitleBar" Property="Margin" Value="0" />
                            <!--  Restore corner radius for normal state  -->
                            <Setter Property="WindowChrome.WindowChrome">
                                <Setter.Value>
                                    <WindowChrome
                                        CaptionHeight="32"
                                        CornerRadius="7"
                                        GlassFrameThickness="0"
                                        NonClientFrameEdges="None"
                                        ResizeBorderThickness="8"
                                        UseAeroCaptionButtons="False" />
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <!--  High Contrast Mode Triggers  -->
                        <DataTrigger Binding="{Binding IsHighContrastMode}" Value="True">
                            <!--  Use system colors for high contrast mode  -->
                            <Setter TargetName="TitleBar" Property="Background" Value="{x:Static SystemColors.ActiveCaptionBrush}" />
                            <Setter TargetName="WindowTitle" Property="Foreground" Value="{x:Static SystemColors.ActiveCaptionTextBrush}" />
                            <Setter Property="BorderBrush" Value="{x:Static SystemColors.WindowFrameBrush}" />
                            <Setter Property="BorderThickness" Value="2" />

                            <!--  High contrast button styling  -->
                            <Setter TargetName="MinimizeButton" Property="Background" Value="{x:Static SystemColors.ControlBrush}" />
                            <Setter TargetName="MinimizeButton" Property="Foreground" Value="{x:Static SystemColors.ControlTextBrush}" />
                            <Setter TargetName="MinimizeButton" Property="BorderBrush" Value="{x:Static SystemColors.ControlTextBrush}" />
                            <Setter TargetName="MinimizeButton" Property="BorderThickness" Value="1" />

                            <Setter TargetName="MaximizeRestoreButton" Property="Background" Value="{x:Static SystemColors.ControlBrush}" />
                            <Setter TargetName="MaximizeRestoreButton" Property="Foreground" Value="{x:Static SystemColors.ControlTextBrush}" />
                            <Setter TargetName="MaximizeRestoreButton" Property="BorderBrush" Value="{x:Static SystemColors.ControlTextBrush}" />
                            <Setter TargetName="MaximizeRestoreButton" Property="BorderThickness" Value="1" />

                            <Setter TargetName="CloseButton" Property="Background" Value="{x:Static SystemColors.ControlBrush}" />
                            <Setter TargetName="CloseButton" Property="Foreground" Value="{x:Static SystemColors.ControlTextBrush}" />
                            <Setter TargetName="CloseButton" Property="BorderBrush" Value="{x:Static SystemColors.ControlTextBrush}" />
                            <Setter TargetName="CloseButton" Property="BorderThickness" Value="1" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Title Bar Grid Style for Standalone Use  -->
    <Style x:Key="TitleBarGridStyle" TargetType="{x:Type Grid}">
        <Setter Property="Background" Value="{DynamicResource TitleBarBackground}" />
        <Setter Property="Height" Value="32" />
        <Setter Property="FlowDirection" Value="{Binding TitleBarFlowDirection, RelativeSource={RelativeSource AncestorType=Window}}" />
    </Style>

    <!--  Window Title TextBlock Style  -->
    <Style x:Key="WindowTitleTextStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="FontWeight" Value="Normal" />
        <Setter Property="Foreground" Value="{DynamicResource TitleBarForeground}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="TextTrimming" Value="CharacterEllipsis" />
        <Setter Property="WindowChrome.IsHitTestVisibleInChrome" Value="False" />
        <Style.Triggers>
            <!--  RTL Text Alignment Trigger  -->
            <DataTrigger Binding="{Binding TitleBarFlowDirection, RelativeSource={RelativeSource AncestorType=Window}}" Value="RightToLeft">
                <Setter Property="TextAlignment" Value="Right" />
                <Setter Property="HorizontalAlignment" Value="Right" />
            </DataTrigger>
            <DataTrigger Binding="{Binding TitleBarFlowDirection, RelativeSource={RelativeSource AncestorType=Window}}" Value="LeftToRight">
                <Setter Property="TextAlignment" Value="Left" />
                <Setter Property="HorizontalAlignment" Value="Left" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!--  Window Icon Image Style  -->
    <Style x:Key="WindowIconImageStyle" TargetType="{x:Type Image}">
        <Setter Property="Width" Value="16" />
        <Setter Property="Height" Value="16" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="WindowChrome.IsHitTestVisibleInChrome" Value="False" />
        <Style.Triggers>
            <!--  RTL Icon Positioning  -->
            <DataTrigger Binding="{Binding TitleBarFlowDirection, RelativeSource={RelativeSource AncestorType=Window}}" Value="RightToLeft">
                <Setter Property="Margin" Value="8,0,8,0" />
            </DataTrigger>
            <DataTrigger Binding="{Binding TitleBarFlowDirection, RelativeSource={RelativeSource AncestorType=Window}}" Value="LeftToRight">
                <Setter Property="Margin" Value="8,0,8,0" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>