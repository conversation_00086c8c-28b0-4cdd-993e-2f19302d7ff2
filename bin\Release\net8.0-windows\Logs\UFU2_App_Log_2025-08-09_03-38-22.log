=== UFU2 Application Session Started at 2025-08-09 03:38:22 ===
[2025-08-09 03:38:22.480]  	[INFO]		[LoggingService]	Log level set to Info
[2025-08-09 03:38:22.596]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 03:38:22.690]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 03:38:22.752]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 03:38:23.378]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 03:38:23.392]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 03:38:23.402]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 03:38:23.453]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 03:38:23.467]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 03:38:23.509]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 03:38:23.550]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 03:38:23.588]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 03:38:23.607]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 03:38:24.136]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 03:38:24.141]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 03:38:24.156]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 03:38:24.171]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 03:38:24.184]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 03:38:24.194]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 03:38:24.199]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 03:38:24.242]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 03:38:24.319]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 03:38:24.374]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 03:38:24.391]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 03:38:24.388]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 03:38:24.427]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 03:38:24.438]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 03:38:24.441]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 03:38:24.483]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 03:38:24.508]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 03:38:24.588]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 03:38:24.621]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 03:38:24.661]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 03:38:24.692]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 03:38:24.724]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 03:38:24.743]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 03:38:24.760]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 03:38:24.780]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 03:38:24.791]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 03:38:24.825]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 03:38:24.844]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 03:38:24.872]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 03:38:24.934]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 03:38:24.969]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 03:38:24.995]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 03:38:25.006]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 03:38:25.032]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 03:38:25.066]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 03:38:25.112]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 03:38:25.184]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 03:38:25.271]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 03:38:25.299]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 03:38:25.372]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 03:38:25.407]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 03:38:25.771]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 03:38:25.793]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 03:38:25.830]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 03:38:25.858]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 03:38:25.869]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 03:38:25.898]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 03:38:25.906]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 03:38:25.942]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 03:38:25.980]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 03:38:26.006]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 03:38:26.025]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 03:38:26.117]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-09 03:38:26.155]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 88ms
[2025-08-09 03:38:26.201]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 03:38:26.225]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 32ms
[2025-08-09 03:38:26.265]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 03:38:26.309]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 46ms
[2025-08-09 03:38:26.363]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 11ms
[2025-08-09 03:38:26.410]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 03:38:26.475]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 76ms
[2025-08-09 03:38:26.536]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 03:38:26.600]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 03:38:26.627]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 03:38:26.647]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 03:38:26.679]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 03:38:26.736]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 03:38:26.762]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 03:38:26.788]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 03:38:26.812]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 03:38:26.828]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 03:38:26.862]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 03:38:26.899]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 03:38:26.921]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 03:38:26.944]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 03:38:26.963]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 03:38:27.198]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 03:38:27.243]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 03:38:28.779]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 03:38:41.327]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 03:38:41.406]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 03:38:41.504]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 03:38:41.631]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 03:38:41.745]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 03:38:41.962]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 03:38:43.366]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 03:38:43.456]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 03:38:43.568]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 03:38:43.727]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 03:38:43.807]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 03:38:43.921]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 03:38:43.999]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 03:38:44.158]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 03:38:44.284]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 03:38:44.337]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 03:38:44.408]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 03:38:44.492]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 03:38:44.576]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 03:38:44.621]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 03:38:44.726]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 03:38:44.763]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 03:38:44.838]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 03:38:44.903]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 03:38:45.022]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 03:38:45.342]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 03:38:45.491]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 03:38:45.565]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 03:38:45.731]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 03:38:45.841]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 03:38:46.063]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-09 03:38:46.222]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 0.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-09 03:38:46.474]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-09 03:38:46.637]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 50.0%
[2025-08-09 03:38:46.740]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 03:38:46.875]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 03:38:47.341]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 03:38:47.536]  	[INFO]		[ResourceManager]	Generated memory leak report: 0 alive resources, 0 dead resources
[2025-08-09 03:38:47.714]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 1
[2025-08-09 03:38:47.788]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 1 potential leaks detected
[2025-08-09 03:38:47.892]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-09 03:38:47.989]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 03:38:48.111]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 03:38:48.176]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-09 03:38:48.269]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 03:38:48.369]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 03:38:48.507]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 2 tracked, 2 disposed, 1 cleanups
[2025-08-09 03:38:48.710]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 03:38:48.852]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 03:38:49.014]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 03:38:49.152]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 03:38:49.237]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-09 03:38:49.283]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 03:38:49 ===
