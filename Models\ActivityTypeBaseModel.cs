using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace UFU2.Models
{
    /// <summary>
    /// Model class representing an activity type from the ActivityTypeBase database table.
    /// Contains activity code and description data with validation support.
    /// </summary>
    public class ActivityTypeBaseModel : INotifyPropertyChanged, IDataErrorInfo
    {
        private string _code;
        private string _description;

        /// <summary>
        /// Gets or sets the activity code (primary identifier).
        /// </summary>
        [Required(ErrorMessage = "كود النشاط مطلوب")]
        [StringLength(10, ErrorMessage = "كود النشاط يجب أن يكون أقل من 10 أحرف")]
        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged(nameof(Code));
                }
            }
        }

        /// <summary>
        /// Gets or sets the activity description.
        /// </summary>
        [Required(ErrorMessage = "وصف النشاط مطلوب")]
        [StringLength(500, ErrorMessage = "وصف النشاط يجب أن يكون أقل من 500 حرف")]
        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged(nameof(Description));
                }
            }
        }

        /// <summary>
        /// Default constructor for ActivityTypeBaseModel.
        /// </summary>
        public ActivityTypeBaseModel()
        {
        }

        /// <summary>
        /// Constructor with parameters for ActivityTypeBaseModel.
        /// </summary>
        /// <param name="code">Activity code</param>
        /// <param name="description">Activity description</param>
        public ActivityTypeBaseModel(string code, string description)
        {
            Code = code;
            Description = description;
        }

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event raised when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event.
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region IDataErrorInfo Implementation

        /// <summary>
        /// Gets an error message indicating what is wrong with this object.
        /// </summary>
        public string Error
        {
            get
            {
                var codeError = this[nameof(Code)];
                var descriptionError = this[nameof(Description)];

                if (!string.IsNullOrEmpty(codeError))
                    return codeError;
                if (!string.IsNullOrEmpty(descriptionError))
                    return descriptionError;

                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the error message for the property with the given name.
        /// </summary>
        /// <param name="columnName">Property name</param>
        /// <returns>Error message or empty string if no error</returns>
        public string this[string columnName]
        {
            get
            {
                switch (columnName)
                {
                    case nameof(Code):
                        if (string.IsNullOrWhiteSpace(Code))
                            return "كود النشاط مطلوب";
                        if (Code?.Length > 10)
                            return "كود النشاط يجب أن يكون أقل من 10 أحرف";
                        break;

                    case nameof(Description):
                        if (string.IsNullOrWhiteSpace(Description))
                            return "وصف النشاط مطلوب";
                        if (Description?.Length > 500)
                            return "وصف النشاط يجب أن يكون أقل من 500 حرف";
                        break;
                }

                return string.Empty;
            }
        }

        #endregion

        #region Object Overrides

        /// <summary>
        /// Returns a string representation of the ActivityTypeBaseModel.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"{Code} - {Description}";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object.
        /// </summary>
        /// <param name="obj">Object to compare</param>
        /// <returns>True if objects are equal, false otherwise</returns>
        public override bool Equals(object obj)
        {
            if (obj is ActivityTypeBaseModel other)
            {
                return string.Equals(Code, other.Code, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the current object.
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Code?.GetHashCode() ?? 0;
        }

        #endregion

        /// <summary>
        /// Validates the model and returns true if all properties are valid.
        /// </summary>
        /// <returns>True if model is valid, false otherwise</returns>
        public bool IsValid()
        {
            return string.IsNullOrEmpty(Error);
        }

        /// <summary>
        /// Creates a copy of the current ActivityTypeBaseModel.
        /// </summary>
        /// <returns>New instance with copied values</returns>
        public ActivityTypeBaseModel Clone()
        {
            return new ActivityTypeBaseModel(Code, Description);
        }
    }
}
