using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Threading;
using UFU2.Common;
using UFU2.Views.UserControls;

namespace UFU2.Services
{
    /// <summary>
    /// Static service for managing toast notifications in UFU2 application.
    /// Supports both in-app notifications and desktop notifications with proper cleanup and lifecycle management.
    /// </summary>
    public static class ToastService
    {
        #region Private Fields

        // Desktop-only toast management
        private static readonly List<ToastNotification> _activeToasts = new List<ToastNotification>();
        private static ToastPosition _defaultPosition = ToastPosition.BottomRight;
        private static int _maxToasts = 5; // Maximum number of toasts to show at once

        // Desktop toast management
        private static Window? _toastWindow;
        private static Panel? _toastContainer;
        private static bool _isShuttingDown = false;
        private static bool _isInitialized = false;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets whether the ToastService has been initialized
        /// </summary>
        public static bool IsInitialized => _isInitialized;

        /// <summary>
        /// Gets the number of currently active toasts
        /// </summary>
        public static int ActiveToastCount => _activeToasts.Count;

        /// <summary>
        /// Gets or sets the maximum number of toasts to display at once
        /// </summary>
        public static int MaxToasts
        {
            get => _maxToasts;
            set => _maxToasts = Math.Max(1, Math.Min(value, 10)); // Limit between 1 and 10
        }

        /// <summary>
        /// Gets or sets the default position for toast notifications
        /// </summary>
        public static ToastPosition DefaultPosition
        {
            get => _defaultPosition;
            set => _defaultPosition = value;
        }

        #endregion

        #region Initialization Methods

        /// <summary>
        /// Initialize the ToastService for desktop-only notifications
        /// </summary>
        /// <param name="position">The default position for toast notifications (optional, defaults to BottomRight)</param>
        public static void Initialize(ToastPosition position = ToastPosition.BottomRight)
        {
            try
            {
                if (_isInitialized)
                {
                    LoggingService.LogWarning("ToastService already initialized", "ToastService");
                    return;
                }

                _defaultPosition = position;
                _isInitialized = true;

                // Register for application exit to ensure cleanup
                if (Application.Current != null)
                {
                    Application.Current.Exit += OnApplicationExit;
                }

                // Subscribe to theme changes for desktop toast theme updates
                try
                {
                    ThemeService.ThemeChanged += OnThemeChanged;
                    LoggingService.LogDebug("Subscribed to theme change events", "ToastService");
                }
                catch (Exception ex)
                {
                    LoggingService.LogWarning($"Failed to subscribe to theme changes: {ex.Message}", "ToastService");
                }

                LoggingService.LogDebug($"ToastService initialized in desktop-only mode with position: {position}", "ToastService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to initialize ToastService: {ex.Message}", "ToastService");
                throw;
            }
        }

        /// <summary>
        /// Initialize the desktop toast notifier for notifications outside the app
        /// </summary>
        public static void InitializeDesktopNotifier()
        {
            if (_isShuttingDown || _toastWindow != null)
            {
                return;
            }

            try
            {
                // Create a new window for toast notifications
                _toastWindow = new Window
                {
                    Title = "UFU2 Toast Notifications",
                    Width = 400,
                    MinWidth = 380,
                    MaxWidth = 450,
                    ShowInTaskbar = false,
                    WindowStyle = WindowStyle.None,
                    ResizeMode = ResizeMode.NoResize,
                    Background = Brushes.Transparent,
                    AllowsTransparency = true,
                    Topmost = true,
                    SizeToContent = SizeToContent.WidthAndHeight,
                    // Set initial position off-screen to avoid flicker
                    Left = -1000,
                    Top = -1000
                };

                // Apply current application theme resources to the toast window
                ApplyThemeToDesktopWindow();

                // Create a container for toast notifications
                _toastContainer = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    VerticalAlignment = VerticalAlignment.Bottom
                };
                _toastWindow.Content = _toastContainer;

                // Show the window (initially off-screen)
                _toastWindow.Show();

                // Hide the window from Alt+Tab
                HideFromAltTab(_toastWindow);

                // Ensure the window stays at topmost z-order above all applications
                EnsureTopmostZOrder(_toastWindow);

                // Register for window closing to ensure proper cleanup
                _toastWindow.Closed += OnToastWindowClosed;

                LoggingService.LogDebug("Desktop toast notifier initialized with theme support", "ToastService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to initialize desktop notifier: {ex.Message}", "ToastService");
            }
        }

        /// <summary>
        /// Applies the current application theme resources to the desktop toast window
        /// </summary>
        private static void ApplyThemeToDesktopWindow()
        {
            if (_toastWindow == null || Application.Current == null)
                return;

            try
            {
                // Copy theme resources from the main application to the toast window
                foreach (var dictionary in Application.Current.Resources.MergedDictionaries)
                {
                    _toastWindow.Resources.MergedDictionaries.Add(dictionary);
                }

                // Copy direct resources
                foreach (var key in Application.Current.Resources.Keys)
                {
                    _toastWindow.Resources[key] = Application.Current.Resources[key];
                }

                LoggingService.LogDebug("Theme resources applied to desktop toast window", "ToastService");
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to apply theme to desktop toast window: {ex.Message}", "ToastService");
            }
        }

        #endregion

        #region Toast Display Methods

        /// <summary>
        /// Show a toast notification (desktop-only mode)
        /// </summary>
        /// <param name="title">The title of the notification</param>
        /// <param name="message">The message content of the notification</param>
        /// <param name="type">The type of notification</param>
        /// <param name="duration">How long the notification should stay visible (in milliseconds)</param>
        /// <param name="position">The position of the notification (ignored in desktop-only mode)</param>
        /// <param name="detailMessage">Optional detailed message to show when clicked</param>
        /// <param name="useDesktopNotifier">Force using desktop notifier (always true in desktop-only mode)</param>
        public static void ShowToast(string title, string message, ToastType type, int duration = 3000,
            ToastPosition? position = null, string? detailMessage = null, bool useDesktopNotifier = true)
        {
            if (_isShuttingDown)
            {
                return;
            }

            try
            {
                // Always use desktop notifications in desktop-only mode
                ShowDesktopToast(title, message, type, detailMessage, duration);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show desktop toast: {ex.Message}", "ToastService");
            }
        }

        /// <summary>
        /// Show a success toast notification
        /// </summary>
        public static void Success(string title, string message, int duration = 3000, 
            ToastPosition? position = null, string? detailMessage = null)
        {
            ShowToast(title, message, ToastType.Success, duration, position, detailMessage);
        }

        /// <summary>
        /// Show an info toast notification
        /// </summary>
        public static void Info(string title, string message, int duration = 3000, 
            ToastPosition? position = null, string? detailMessage = null)
        {
            ShowToast(title, message, ToastType.Info, duration, position, detailMessage);
        }

        /// <summary>
        /// Show a warning toast notification
        /// </summary>
        public static void Warning(string title, string message, int duration = 5000, 
            ToastPosition? position = null, string? detailMessage = null)
        {
            ShowToast(title, message, ToastType.Warning, duration, position, detailMessage);
        }

        /// <summary>
        /// Show an error toast notification
        /// </summary>
        public static void Error(string title, string message, int duration = 5000, 
            ToastPosition? position = null, string? detailMessage = null)
        {
            ShowToast(title, message, ToastType.Error, duration, position, detailMessage);
        }

        #endregion

        #region Private Implementation Methods

        // In-app toast functionality removed in desktop-only mode

        /// <summary>
        /// Shows a desktop toast notification
        /// </summary>
        private static void ShowDesktopToast(string title, string message, ToastType type,
            string? detailMessage = null, int duration = 5000)
        {
            if (_isShuttingDown)
            {
                return;
            }

            EnsureDesktopNotifierInitialized();

            if (_toastContainer == null)
            {
                LoggingService.LogWarning("Desktop toast container not available", "ToastService");
                return;
            }

            // Log toast display for deduplication tracking
            LoggingService.LogDebug($"Displaying {type} toast: {title} - {message}", "ToastService");

            var toast = new ToastNotification(title, message, type, duration, ToastPosition.BottomRight,
                (t) => _toastContainer.Children.Remove(t), detailMessage);

            toast.DetailButtonClicked += OnDetailButtonClicked;

            _toastContainer.Children.Add(toast);

            // Adjust the window size to fit the content
            if (_toastWindow != null)
            {
                _toastWindow.SizeToContent = SizeToContent.WidthAndHeight;

                // Ensure the window remains at topmost z-order when displaying new toasts
                EnsureTopmostZOrder(_toastWindow);

                // Reposition the window after layout is updated
                _toastWindow.Dispatcher.BeginInvoke(
                    new Action(PositionDesktopWindow),
                    DispatcherPriority.Loaded
                );
            }

            LoggingService.LogInfo($"Desktop toast displayed: {type} - {title}", "ToastService");
        }

        /// <summary>
        /// Ensures the desktop toast notifier is initialized
        /// </summary>
        private static void EnsureDesktopNotifierInitialized()
        {
            if (_isShuttingDown)
            {
                return;
            }

            if (_toastWindow == null || !_toastWindow.IsVisible)
            {
                InitializeDesktopNotifier();
            }
        }

        /// <summary>
        /// Positions the desktop toast window
        /// </summary>
        private static void PositionDesktopWindow()
        {
            if (_toastWindow == null || !_toastWindow.IsVisible)
                return;

            try
            {
                // Get the working area of the primary screen
                var workArea = SystemParameters.WorkArea;

                // Position the window in the bottom right corner
                _toastWindow.Left = workArea.Right - _toastWindow.ActualWidth - 20;
                _toastWindow.Top = workArea.Bottom - _toastWindow.ActualHeight - 20;
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to position desktop toast window: {ex.Message}", "ToastService");
            }
        }

        // Toast rearrangement not needed in desktop-only mode

        /// <summary>
        /// Hides a window from Alt+Tab
        /// </summary>
        private static void HideFromAltTab(Window window)
        {
            try
            {
                var helper = new WindowInteropHelper(window);
                var hwnd = helper.Handle;
                var extendedStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
                SetWindowLong(hwnd, GWL_EXSTYLE, extendedStyle | WS_EX_TOOLWINDOW);
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to hide window from Alt+Tab: {ex.Message}", "ToastService");
            }
        }

        /// <summary>
        /// Ensures the toast window stays at the topmost z-order above all desktop applications
        /// </summary>
        private static void EnsureTopmostZOrder(Window window)
        {
            try
            {
                var helper = new WindowInteropHelper(window);
                var hwnd = helper.Handle;

                if (hwnd != IntPtr.Zero)
                {
                    // Set the window to topmost z-order using Windows API
                    bool success = SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);

                    if (success)
                    {
                        LoggingService.LogDebug("Toast window z-order set to topmost successfully", "ToastService");
                    }
                    else
                    {
                        LoggingService.LogWarning("Failed to set toast window to topmost z-order", "ToastService");
                    }

                    // Also set the WS_EX_TOPMOST extended style for additional assurance
                    var extendedStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
                    SetWindowLong(hwnd, GWL_EXSTYLE, extendedStyle | WS_EX_TOPMOST);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Failed to ensure topmost z-order for toast window: {ex.Message}", "ToastService");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the toast closed event (desktop-only mode)
        /// </summary>
        private static void OnToastClosed(ToastNotification toast)
        {
            try
            {
                // In desktop-only mode, toasts are managed by the desktop container
                toast.Dispose();
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Error handling toast close: {ex.Message}", "ToastService");
            }
        }

        /// <summary>
        /// Handles the detail button clicked event
        /// </summary>
        private static void OnDetailButtonClicked(object sender, ToastDetailEventArgs e)
        {
            try
            {
                // Show detailed message using ErrorManager
                ErrorManager.ShowUserInfo(e.DetailMessage, "تفاصيل الإشعار", "ToastService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing toast details: {ex.Message}", "ToastService");
            }
        }

        /// <summary>
        /// Handles application exit event
        /// </summary>
        private static void OnApplicationExit(object? sender, ExitEventArgs e)
        {
            CloseAll();
        }

        /// <summary>
        /// Handles theme change events to update desktop toast appearance
        /// </summary>
        private static void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug($"Theme changed to {e.NewTheme}, refreshing desktop toast theme", "ToastService");
                RefreshDesktopTheme();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling theme change: {ex.Message}", "ToastService");
            }
        }

        /// <summary>
        /// Handles toast window closed event
        /// </summary>
        private static void OnToastWindowClosed(object? sender, EventArgs e)
        {
            LoggingService.LogDebug("Toast window closed", "ToastService");
            if (_toastContainer != null)
            {
                _toastContainer.Children.Clear();
            }
            _toastContainer = null;
            _toastWindow = null;
        }

        #endregion

        #region Cleanup Methods

        /// <summary>
        /// Close all active toast notifications (desktop-only mode)
        /// </summary>
        public static void CloseAll()
        {
            _isShuttingDown = true;

            LoggingService.LogInfo("Closing all desktop toast notifications", "ToastService");

            try
            {
                // Close desktop toasts
                if (_toastWindow != null)
                {
                    if (_toastContainer != null)
                    {
                        var toastsToClose = new List<UIElement>();
                        foreach (UIElement element in _toastContainer.Children)
                        {
                            if (element is ToastNotification)
                            {
                                toastsToClose.Add(element);
                            }
                        }

                        foreach (var element in toastsToClose)
                        {
                            if (element is ToastNotification toast)
                            {
                                try
                                {
                                    toast.CloseToast();
                                }
                                catch (Exception ex)
                                {
                                    LoggingService.LogWarning($"Error closing desktop toast: {ex.Message}", "ToastService");
                                }
                            }
                        }

                        _toastContainer.Children.Clear();
                    }

                    try
                    {
                        if (_toastWindow.Dispatcher.CheckAccess())
                        {
                            _toastWindow.Hide();
                            _toastWindow.Close();
                        }
                        else
                        {
                            _toastWindow.Dispatcher.Invoke(() =>
                            {
                                _toastWindow.Hide();
                                _toastWindow.Close();
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogWarning($"Error closing toast window: {ex.Message}", "ToastService");
                    }
                    finally
                    {
                        _toastWindow = null;
                        _toastContainer = null;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogWarning($"Error closing desktop toasts: {ex.Message}", "ToastService");
            }

            LoggingService.LogInfo("All toast notifications closed", "ToastService");
        }

        #endregion

        #region Theme Support

        /// <summary>
        /// Refreshes the desktop toast window theme when the application theme changes
        /// </summary>
        public static void RefreshDesktopTheme()
        {
            if (_toastWindow == null)
                return;

            try
            {
                LoggingService.LogDebug("Refreshing desktop toast window theme", "ToastService");

                // Clear existing resources
                _toastWindow.Resources.MergedDictionaries.Clear();
                _toastWindow.Resources.Clear();

                // Reapply current theme resources
                ApplyThemeToDesktopWindow();

                // Force refresh of all toast notifications in the window
                if (_toastContainer != null)
                {
                    foreach (UIElement element in _toastContainer.Children)
                    {
                        if (element is ToastNotification toast)
                        {
                            // Force the toast to refresh its visual state
                            toast.InvalidateVisual();
                            toast.UpdateLayout();
                        }
                    }
                }

                LoggingService.LogDebug("Desktop toast window theme refreshed successfully", "ToastService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to refresh desktop toast theme: {ex.Message}", "ToastService");
            }
        }

        #endregion

        #region Windows API

        private const int GWL_EXSTYLE = -20;
        private const int WS_EX_TOOLWINDOW = 0x00000080;
        private const int WS_EX_TOPMOST = 0x00000008;

        // SetWindowPos constants for topmost behavior
        private static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);
        private static readonly IntPtr HWND_NOTOPMOST = new IntPtr(-2);
        private const uint SWP_NOMOVE = 0x0002;
        private const uint SWP_NOSIZE = 0x0001;
        private const uint SWP_SHOWWINDOW = 0x0040;

        [DllImport("user32.dll")]
        private static extern int GetWindowLong(IntPtr hwnd, int index);

        [DllImport("user32.dll")]
        private static extern int SetWindowLong(IntPtr hwnd, int index, int newStyle);

        [DllImport("user32.dll")]
        private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

        #endregion
    }
}
