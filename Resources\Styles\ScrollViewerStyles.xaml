﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">


    <!--
        ========================================
        SCROLL COMPONENT STYLES
        ========================================
    -->

    <!--  Base ScrollViewer Style  -->
    <Style x:Key="BaseScrollViewerStyle" TargetType="ScrollViewer">
        <Setter Property="CanContentScroll" Value="False" />
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />

        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid x:Name="Grid">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  Content Area  -->
                        <ScrollContentPresenter
                            x:Name="PART_ScrollContentPresenter"
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="{TemplateBinding Padding}"
                            CanContentScroll="{TemplateBinding CanContentScroll}"
                            CanHorizontallyScroll="False"
                            CanVerticallyScroll="False"
                            Content="{TemplateBinding Content}"
                            ContentTemplate="{TemplateBinding ContentTemplate}" />

                        <!--  Vertical ScrollBar  -->
                        <ScrollBar
                            x:Name="PART_VerticalScrollBar"
                            Grid.Row="0"
                            Grid.Column="1"
                            AutomationProperties.AutomationId="VerticalScrollBar"
                            Cursor="Arrow"
                            Maximum="{TemplateBinding ScrollableHeight}"
                            Minimum="0"
                            Orientation="Vertical"
                            Style="{DynamicResource UFU2VerticalScrollBarStyle}"
                            ViewportSize="{TemplateBinding ViewportHeight}"
                            Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                            Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />

                        <!--  Horizontal ScrollBar  -->
                        <ScrollBar
                            x:Name="PART_HorizontalScrollBar"
                            Grid.Row="1"
                            Grid.Column="0"
                            AutomationProperties.AutomationId="HorizontalScrollBar"
                            Cursor="Arrow"
                            Maximum="{TemplateBinding ScrollableWidth}"
                            Minimum="0"
                            Orientation="Horizontal"
                            Style="{DynamicResource UFU2HorizontalScrollBarStyle}"
                            ViewportSize="{TemplateBinding ViewportWidth}"
                            Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                            Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Content ScrollViewer Style (for main content areas)  -->
    <Style
        x:Key="ContentScrollViewerStyle"
        BasedOn="{StaticResource BaseScrollViewerStyle}"
        TargetType="ScrollViewer">
        <Setter Property="HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="CanContentScroll" Value="False" />
    </Style>

    <!--  List ScrollViewer Style (for lists with max height)  -->
    <Style
        x:Key="ListScrollViewerStyle"
        BasedOn="{StaticResource BaseScrollViewerStyle}"
        TargetType="ScrollViewer">
        <Setter Property="HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="CanContentScroll" Value="True" />
    </Style>

    <!--  Dialog ScrollViewer Style (for dialog content)  -->
    <Style
        x:Key="DialogScrollViewerStyle"
        BasedOn="{StaticResource BaseScrollViewerStyle}"
        TargetType="ScrollViewer">
        <Setter Property="HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="CanContentScroll" Value="False" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="MaxHeight" Value="400" />
    </Style>

    <!--  Vertical ScrollBar Style  -->
    <Style x:Key="UFU2VerticalScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="False" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Width" Value="9" />
        <Setter Property="MinWidth" Value="9" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid x:Name="GridRoot" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="0.00001*" />
                        </Grid.RowDefinitions>

                        <!--  Track  -->
                        <Track
                            x:Name="PART_Track"
                            Grid.Row="0"
                            Focusable="False"
                            IsDirectionReversed="True">
                            <Track.DecreaseRepeatButton>
                                <RepeatButton
                                    x:Name="PageUp"
                                    Command="ScrollBar.PageUpCommand"
                                    Focusable="False"
                                    Opacity="0" />
                            </Track.DecreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb x:Name="Thumb" Style="{DynamicResource UFU2ScrollBarThumbStyle}" />
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton
                                    x:Name="PageDown"
                                    Command="ScrollBar.PageDownCommand"
                                    Focusable="False"
                                    Opacity="0" />
                            </Track.IncreaseRepeatButton>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Horizontal ScrollBar Style  -->
    <Style x:Key="UFU2HorizontalScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="False" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Height" Value="12" />
        <Setter Property="MinHeight" Value="12" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid x:Name="GridRoot" Background="Transparent">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="0.00001*" />
                        </Grid.ColumnDefinitions>

                        <!--  Track  -->
                        <Track
                            x:Name="PART_Track"
                            Grid.Column="0"
                            Focusable="False"
                            IsDirectionReversed="False">
                            <Track.DecreaseRepeatButton>
                                <RepeatButton
                                    x:Name="PageLeft"
                                    Command="ScrollBar.PageLeftCommand"
                                    Focusable="False"
                                    Opacity="0" />
                            </Track.DecreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb x:Name="Thumb" Style="{DynamicResource UFU2ScrollBarThumbStyle}" />
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton
                                    x:Name="PageRight"
                                    Command="ScrollBar.PageRightCommand"
                                    Focusable="False"
                                    Opacity="0" />
                            </Track.IncreaseRepeatButton>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  ScrollBar Thumb Style  -->
    <Style x:Key="UFU2ScrollBarThumbStyle" TargetType="Thumb">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Border
                        x:Name="ThumbBorder"
                        Background="{DynamicResource ScrollThumbBackground}"
                        CornerRadius="6"
                        Opacity="0.6" />
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ThumbBorder" Property="Background" Value="{DynamicResource ScrollThumbHoverBackground}" />
                            <Setter TargetName="ThumbBorder" Property="Opacity" Value="0.8" />
                        </Trigger>
                        <Trigger Property="IsDragging" Value="True">
                            <Setter TargetName="ThumbBorder" Property="Background" Value="{DynamicResource ScrollThumbPressedBackground}" />
                            <Setter TargetName="ThumbBorder" Property="Opacity" Value="1.0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
