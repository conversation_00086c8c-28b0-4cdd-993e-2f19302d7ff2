using System;
using System.Globalization;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that determines whether activity description fields should be read-only based on the selected activity type.
    /// Some activity types may have predefined descriptions or require specific input restrictions.
    /// Returns true if the field should be read-only, false if it should be editable.
    /// </summary>
    public class ActivityDescriptionReadOnlyConverter : IValueConverter
    {
        /// <summary>
        /// Converts the selected activity type to a boolean indicating whether the description field should be read-only.
        /// </summary>
        /// <param name="value">The selected activity type (MainCommercial, SecondaryCommercial, Craft, Professional)</param>
        /// <param name="targetType">The target type (should be bool)</param>
        /// <param name="parameter">Optional parameter for field-specific behavior (not used currently)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>True if the field should be read-only, false if editable</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var activityType = value?.ToString() ?? "MainCommercial";
                return GetReadOnlyStatus(activityType);
            }
            catch (Exception)
            {
                // Default to editable if any error occurs
                return false;
            }
        }

        /// <summary>
        /// Not implemented for this converter as it's one-way only.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ActivityDescriptionReadOnlyConverter is a one-way converter.");
        }

        /// <summary>
        /// Determines the read-only status based on activity type.
        /// Commercial activities use activity codes, so description should be read-only.
        /// Non-commercial activities use descriptions, so they should be editable.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>True if the field should be read-only, false if editable</returns>
        private static bool GetReadOnlyStatus(string activityType)
        {
            return activityType switch
            {
                // Commercial activities use activity codes - description should be read-only
                "MainCommercial" => true,
                "SecondaryCommercial" => true,

                // Craft activities use craft codes - description should be read-only
                "Craft" => true,

                // Professional activities use descriptions - should be editable
                "Professional" => false,

                // Default to read-only for unknown types (safer approach)
                _ => true
            };
        }

        /// <summary>
        /// Alternative method to get read-only status with more granular control.
        /// This can be extended in the future to handle specific field types or business rules.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <param name="fieldContext">The context or field name for more specific rules</param>
        /// <returns>True if the field should be read-only, false if editable</returns>
        public static bool GetReadOnlyStatus(string activityType, string fieldContext = "")
        {
            // Use the same logic as the main method for consistency
            return GetReadOnlyStatus(activityType);
        }

        /// <summary>
        /// Gets user-friendly explanation for why a field might be read-only.
        /// This can be used for tooltips or help text.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Arabic explanation text for read-only status</returns>
        public static string GetReadOnlyExplanation(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "وصف النشاط التجاري الرئيسي يتم تحديده تلقائياً من رمز النشاط",
                "SecondaryCommercial" => "وصف النشاط التجاري الثانوي يتم تحديده تلقائياً من رمز النشاط",
                "Craft" => "يمكن تعديل وصف النشاط الحرفي",
                "Professional" => "يمكن تعديل وصف النشاط المهني",
                _ => "وصف النشاط يتم تحديده تلقائياً"
            };
        }
    }
}
