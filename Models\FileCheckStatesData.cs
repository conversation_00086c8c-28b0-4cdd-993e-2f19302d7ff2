using System;

namespace UFU2.Models
{
    /// <summary>
    /// Data transfer object representing file check completion states for an activity.
    /// Provides strongly typed properties for common checks used in the UI.
    /// Can be converted to and from the dictionary representation used by services.
    /// </summary>
    public class FileCheckStatesData
    {
        /// <summary>
        /// Gets or sets whether the Commercial Register (RC) check is completed.
        /// </summary>
        public bool CommercialRegisterCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Tax Certificate (NIF) check is completed.
        /// </summary>
        public bool TaxCertificateCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Social Security (CAS) check is completed.
        /// </summary>
        public bool SocialSecurityCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Municipality check is completed.
        /// </summary>
        public bool MunicipalityCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Chamber of Commerce check is completed.
        /// </summary>
        public bool ChamberCommerceCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Insurance check is completed.
        /// </summary>
        public bool InsuranceCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Bank Statement check is completed.
        /// </summary>
        public bool BankStatementCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Rental Contract check is completed.
        /// </summary>
        public bool RentalContractCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Utility Bill check is completed.
        /// </summary>
        public bool UtilityBillCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Identity Document check is completed.
        /// </summary>
        public bool IdentityDocumentCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Photo check is completed.
        /// </summary>
        public bool PhotoCheck { get; set; }

        /// <summary>
        /// Gets or sets whether other documents check is completed.
        /// </summary>
        public bool OtherDocumentsCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Craft Card (ART) check is completed.
        /// Required for craft activities.
        /// </summary>
        public bool CraftCardCheck { get; set; }

        /// <summary>
        /// Gets or sets whether the Professional License (AGR) check is completed.
        /// Required for professional activities.
        /// </summary>
        public bool ProfessionalLicenseCheck { get; set; }

        /// <summary>
        /// Converts this FileCheckStatesData to a Dictionary&lt;string, bool&gt; for compatibility with services.
        /// Maps the strongly-typed properties to the dictionary keys expected by the database layer.
        /// This method is deprecated - use ToDictionary(string activityType) instead for proper activity type validation.
        /// </summary>
        /// <returns>Dictionary with file check type keys and completion status values</returns>
        [Obsolete("Use ToDictionary(string activityType) instead for proper activity type validation")]
        public Dictionary<string, bool> ToDictionary()
        {
            return new Dictionary<string, bool>
            {
                ["RC"] = CommercialRegisterCheck,
                ["NIF"] = TaxCertificateCheck,
                ["CAS"] = SocialSecurityCheck,
                ["DEX"] = MunicipalityCheck,
                ["NIS"] = InsuranceCheck,
                // Note: Some properties map to the same underlying check types
                // This is intentional for UI flexibility while maintaining data consistency
            };
        }

        /// <summary>
        /// Converts this FileCheckStatesData to a Dictionary&lt;string, bool&gt; for compatibility with services.
        /// Maps the strongly-typed properties to the dictionary keys expected by the database layer.
        /// Only includes file check types that are valid for the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type to determine valid file check types</param>
        /// <returns>Dictionary with file check type keys and completion status values for the activity type</returns>
        public Dictionary<string, bool> ToDictionary(string activityType)
        {
            var result = new Dictionary<string, bool>();

            // Get valid file check types for this activity type
            var validTypes = FileCheckTypeRules.GetValidFileCheckTypes(activityType);

            // Map only the valid file check types for this activity
            foreach (var fileCheckType in validTypes)
            {
                bool isChecked = fileCheckType switch
                {
                    "CAS" => SocialSecurityCheck,
                    "NIF" => TaxCertificateCheck,
                    "NIS" => InsuranceCheck,
                    "RC" => CommercialRegisterCheck,
                    "ART" => CraftCardCheck,
                    "AGR" => ProfessionalLicenseCheck,
                    "DEX" => MunicipalityCheck,
                    _ => false
                };

                result[fileCheckType] = isChecked;
            }

            return result;
        }

        /// <summary>
        /// Creates a FileCheckStatesData from a Dictionary&lt;string, bool&gt;.
        /// </summary>
        /// <param name="dictionary">Dictionary with file check states</param>
        /// <returns>FileCheckStatesData instance</returns>
        public static FileCheckStatesData FromDictionary(Dictionary<string, bool>? dictionary)
        {
            if (dictionary == null)
                return new FileCheckStatesData();

            return new FileCheckStatesData
            {
                CommercialRegisterCheck = dictionary.GetValueOrDefault("RC", false),
                TaxCertificateCheck = dictionary.GetValueOrDefault("NIF", false),
                SocialSecurityCheck = dictionary.GetValueOrDefault("CAS", false),
                MunicipalityCheck = dictionary.GetValueOrDefault("DEX", false),
                InsuranceCheck = dictionary.GetValueOrDefault("NIS", false),
                CraftCardCheck = dictionary.GetValueOrDefault("ART", false),
                ProfessionalLicenseCheck = dictionary.GetValueOrDefault("AGR", false),
                // Set other properties based on available data or defaults
                ChamberCommerceCheck = dictionary.GetValueOrDefault("RC", false), // Maps to RC
                BankStatementCheck = dictionary.GetValueOrDefault("DEX", false), // Maps to DEX
                RentalContractCheck = dictionary.GetValueOrDefault("DEX", false), // Maps to DEX
                UtilityBillCheck = dictionary.GetValueOrDefault("DEX", false), // Maps to DEX
                IdentityDocumentCheck = dictionary.GetValueOrDefault("CAS", false), // Maps to CAS
                PhotoCheck = dictionary.GetValueOrDefault("DEX", false), // Maps to DEX
                OtherDocumentsCheck = dictionary.GetValueOrDefault("DEX", false) // Maps to DEX
            };
        }
    }
}

