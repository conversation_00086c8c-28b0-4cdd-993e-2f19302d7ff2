using System;
using System.Windows;
using System.Windows.Input;
using UFU2.Common;
using UFU2.Common.Utilities;

namespace UFU2.Common.Behaviors
{
    /// <summary>
    /// Attached behavior for implementing title bar interactions including drag functionality,
    /// double-click to maximize/restore, and proper Windows native behavior support.
    /// Supports Arabic RTL layout and integrates with UFU2's error handling and logging.
    /// </summary>
    public static class TitleBarBehavior
    {
        #region Attached Properties

        /// <summary>
        /// Attached property to enable title bar drag functionality on an element
        /// </summary>
        public static readonly DependencyProperty EnableDragProperty =
            DependencyProperty.RegisterAttached(
                "EnableDrag",
                typeof(bool),
                typeof(TitleBarBehavior),
                new PropertyMetadata(false, OnEnableDragChanged));

        /// <summary>
        /// Attached property to enable double-click maximize/restore functionality on an element
        /// </summary>
        public static readonly DependencyProperty EnableDoubleClickMaximizeProperty =
            DependencyProperty.RegisterAttached(
                "EnableDoubleClickMaximize",
                typeof(bool),
                typeof(TitleBarBehavior),
                new PropertyMetadata(false, OnEnableDoubleClickMaximizeChanged));

        /// <summary>
        /// Attached property to store the target window for the behavior
        /// </summary>
        public static readonly DependencyProperty TargetWindowProperty =
            DependencyProperty.RegisterAttached(
                "TargetWindow",
                typeof(Window),
                typeof(TitleBarBehavior),
                new PropertyMetadata(null));

        #endregion

        #region Property Accessors

        /// <summary>
        /// Gets the EnableDrag property value
        /// </summary>
        /// <param name="obj">The dependency object</param>
        /// <returns>True if drag is enabled, false otherwise</returns>
        public static bool GetEnableDrag(DependencyObject obj)
        {
            return (bool)obj.GetValue(EnableDragProperty);
        }

        /// <summary>
        /// Sets the EnableDrag property value
        /// </summary>
        /// <param name="obj">The dependency object</param>
        /// <param name="value">True to enable drag, false to disable</param>
        public static void SetEnableDrag(DependencyObject obj, bool value)
        {
            obj.SetValue(EnableDragProperty, value);
        }

        /// <summary>
        /// Gets the EnableDoubleClickMaximize property value
        /// </summary>
        /// <param name="obj">The dependency object</param>
        /// <returns>True if double-click maximize is enabled, false otherwise</returns>
        public static bool GetEnableDoubleClickMaximize(DependencyObject obj)
        {
            return (bool)obj.GetValue(EnableDoubleClickMaximizeProperty);
        }

        /// <summary>
        /// Sets the EnableDoubleClickMaximize property value
        /// </summary>
        /// <param name="obj">The dependency object</param>
        /// <param name="value">True to enable double-click maximize, false to disable</param>
        public static void SetEnableDoubleClickMaximize(DependencyObject obj, bool value)
        {
            obj.SetValue(EnableDoubleClickMaximizeProperty, value);
        }

        /// <summary>
        /// Gets the TargetWindow property value
        /// </summary>
        /// <param name="obj">The dependency object</param>
        /// <returns>The target window</returns>
        public static Window GetTargetWindow(DependencyObject obj)
        {
            return (Window)obj.GetValue(TargetWindowProperty);
        }

        /// <summary>
        /// Sets the TargetWindow property value
        /// </summary>
        /// <param name="obj">The dependency object</param>
        /// <param name="value">The target window</param>
        public static void SetTargetWindow(DependencyObject obj, Window value)
        {
            obj.SetValue(TargetWindowProperty, value);
        }

        #endregion

        #region Property Changed Handlers

        /// <summary>
        /// Handles changes to the EnableDrag property
        /// </summary>
        /// <param name="d">The dependency object</param>
        /// <param name="e">Property changed event arguments</param>
        private static void OnEnableDragChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            try
            {
                if (d is FrameworkElement element)
                {
                    UpdateEventHandlers(element);
                    LoggingService.LogDebug($"Title bar drag {((bool)e.NewValue ? "enabled" : "disabled")} for {element.GetType().Name}", "TitleBarBehavior");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling EnableDrag property change: {ex.Message}", "TitleBarBehavior");
            }
        }

        /// <summary>
        /// Handles changes to the EnableDoubleClickMaximize property
        /// </summary>
        /// <param name="d">The dependency object</param>
        /// <param name="e">Property changed event arguments</param>
        private static void OnEnableDoubleClickMaximizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            try
            {
                if (d is FrameworkElement element)
                {
                    UpdateEventHandlers(element);
                    LoggingService.LogDebug($"Title bar double-click maximize {((bool)e.NewValue ? "enabled" : "disabled")} for {element.GetType().Name}", "TitleBarBehavior");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling EnableDoubleClickMaximize property change: {ex.Message}", "TitleBarBehavior");
            }
        }

        /// <summary>
        /// Updates event handlers based on current property values
        /// </summary>
        /// <param name="element">The element to update handlers for</param>
        private static void UpdateEventHandlers(FrameworkElement element)
        {
            try
            {
                var enableDrag = GetEnableDrag(element);
                var enableDoubleClick = GetEnableDoubleClickMaximize(element);

                // Remove all existing handlers first
                element.MouseLeftButtonDown -= OnMouseLeftButtonDownCombined;
                element.MouseMove -= OnMouseMove;
                element.MouseLeftButtonUp -= OnMouseLeftButtonUp;

                // Add handlers based on enabled features
                if (enableDrag || enableDoubleClick)
                {
                    element.MouseLeftButtonDown += OnMouseLeftButtonDownCombined;
                    
                    // Only add move and up handlers if drag is enabled
                    if (enableDrag)
                    {
                        element.MouseMove += OnMouseMove;
                        element.MouseLeftButtonUp += OnMouseLeftButtonUp;
                    }
                }

                LoggingService.LogDebug($"Event handlers updated - Drag: {enableDrag}, DoubleClick: {enableDoubleClick}", "TitleBarBehavior");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating event handlers: {ex.Message}", "TitleBarBehavior");
            }
        }

        #endregion

        #region Private Fields

        // Fields for double-click detection
        private static DateTime _lastClickTime = DateTime.MinValue;
        private static Point _lastClickPosition;
        private static readonly TimeSpan DoubleClickTime = TimeSpan.FromMilliseconds(500); // Standard double-click time
        private static readonly double DoubleClickDistance = 4.0; // Maximum distance between clicks

        #endregion

        #region Event Handlers

        /// <summary>
        /// Combined handler for mouse left button down events that handles both drag and double-click functionality
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Mouse button event arguments</param>
        private static void OnMouseLeftButtonDownCombined(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is FrameworkElement element)
                {
                    var window = GetTargetWindow(element) ?? Window.GetWindow(element);
                    if (window == null)
                    {
                        LoggingService.LogWarning("Cannot handle mouse event: no target window found", "TitleBarBehavior");
                        return;
                    }

                    var enableDrag = GetEnableDrag(element);
                    var enableDoubleClick = GetEnableDoubleClickMaximize(element);

                    // Only handle left button clicks
                    if (e.ChangedButton != MouseButton.Left)
                        return;

                    var currentTime = DateTime.Now;
                    var currentPosition = e.GetPosition(element);

                    // Handle double-click detection if enabled
                    if (enableDoubleClick)
                    {
                        var timeDiff = currentTime - _lastClickTime;
                        var positionDiff = Math.Sqrt(Math.Pow(currentPosition.X - _lastClickPosition.X, 2) + 
                                                    Math.Pow(currentPosition.Y - _lastClickPosition.Y, 2));

                        if (timeDiff <= DoubleClickTime && positionDiff <= DoubleClickDistance)
                        {
                            // This is a double-click - handle maximize/restore
                            LoggingService.LogDebug($"Title bar double-click detected on window: {window.Title}", "TitleBarBehavior");

                            var success = WindowStateManager.HandleDoubleClickTitleBar(window);
                            
                            if (success)
                            {
                                LoggingService.LogDebug($"Double-click maximize/restore completed successfully for window: {window.Title}", "TitleBarBehavior");
                            }
                            else
                            {
                                LoggingService.LogWarning($"Double-click maximize/restore failed for window: {window.Title}", "TitleBarBehavior");
                            }

                            // Reset click tracking and don't process as drag
                            _lastClickTime = DateTime.MinValue;
                            _lastClickPosition = new Point();
                            return;
                        }
                    }

                    // Handle drag if enabled and this is a single click
                    if (enableDrag && e.ClickCount == 1)
                    {
                        try
                        {
                            LoggingService.LogDebug($"Title bar drag initiated for window: {window.Title}", "TitleBarBehavior");
                            
                            // Use WPF's built-in DragMove method which properly handles:
                            // - Native Windows drag behavior
                            // - Aero Snap functionality
                            // - Multi-monitor support
                            // - Screen boundary constraints
                            window.DragMove();
                            
                            LoggingService.LogDebug("Title bar drag completed successfully", "TitleBarBehavior");
                        }
                        catch (InvalidOperationException)
                        {
                            // DragMove can only be called during a mouse left button down event
                            // This is expected behavior if called at wrong time
                            LoggingService.LogDebug("DragMove called at invalid time - this is normal", "TitleBarBehavior");
                        }
                    }

                    // Update click tracking for double-click detection
                    if (enableDoubleClick)
                    {
                        _lastClickTime = currentTime;
                        _lastClickPosition = currentPosition;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling combined mouse left button down: {ex.Message}", "TitleBarBehavior");
                
                // Show Arabic error message to user
                ErrorManager.ShowUserWarningToast(
                    "فشل في تنفيذ عملية شريط العنوان",
                    "خطأ في إدارة النافذة",
                    "TitleBarBehavior"
                );
            }
        }

        /// <summary>
        /// Handles mouse move events - not needed for DragMove implementation
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Mouse event arguments</param>
        private static void OnMouseMove(object sender, MouseEventArgs e)
        {
            // No implementation needed - DragMove handles all mouse movement during drag
            // This method is kept for consistency with the attached property pattern
        }

        /// <summary>
        /// Handles mouse left button up events - not needed for DragMove implementation
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Mouse button event arguments</param>
        private static void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            // No implementation needed - DragMove handles mouse button release
            // This method is kept for consistency with the attached property pattern
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Enables title bar drag functionality for the specified element
        /// </summary>
        /// <param name="element">The element to enable drag for</param>
        /// <param name="targetWindow">The target window (optional, will auto-detect if null)</param>
        public static void EnableDrag(FrameworkElement element, Window targetWindow = null)
        {
            try
            {
                if (element == null)
                {
                    LoggingService.LogWarning("Cannot enable drag for null element", "TitleBarBehavior");
                    return;
                }

                if (targetWindow != null)
                {
                    SetTargetWindow(element, targetWindow);
                }

                SetEnableDrag(element, true);
                
                LoggingService.LogDebug($"Title bar drag enabled for {element.GetType().Name}", "TitleBarBehavior");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error enabling drag: {ex.Message}", "TitleBarBehavior");
            }
        }

        /// <summary>
        /// Enables double-click maximize functionality for the specified element
        /// </summary>
        /// <param name="element">The element to enable double-click maximize for</param>
        /// <param name="targetWindow">The target window (optional, will auto-detect if null)</param>
        public static void EnableDoubleClickMaximize(FrameworkElement element, Window targetWindow = null)
        {
            try
            {
                if (element == null)
                {
                    LoggingService.LogWarning("Cannot enable double-click maximize for null element", "TitleBarBehavior");
                    return;
                }

                if (targetWindow != null)
                {
                    SetTargetWindow(element, targetWindow);
                }

                SetEnableDoubleClickMaximize(element, true);
                
                LoggingService.LogDebug($"Title bar double-click maximize enabled for {element.GetType().Name}", "TitleBarBehavior");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error enabling double-click maximize: {ex.Message}", "TitleBarBehavior");
            }
        }

        /// <summary>
        /// Disables all title bar behaviors for the specified element
        /// </summary>
        /// <param name="element">The element to disable behaviors for</param>
        public static void DisableAll(FrameworkElement element)
        {
            try
            {
                if (element == null)
                {
                    LoggingService.LogWarning("Cannot disable behaviors for null element", "TitleBarBehavior");
                    return;
                }

                SetEnableDrag(element, false);
                SetEnableDoubleClickMaximize(element, false);
                SetTargetWindow(element, null);
                
                LoggingService.LogDebug($"All title bar behaviors disabled for {element.GetType().Name}", "TitleBarBehavior");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disabling behaviors: {ex.Message}", "TitleBarBehavior");
            }
        }

        #endregion
    }
}