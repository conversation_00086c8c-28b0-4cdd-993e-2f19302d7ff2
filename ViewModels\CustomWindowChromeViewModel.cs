using System.Collections.Generic;
using System.Globalization;
using System.Windows;
using System.Windows.Media;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for custom window chrome functionality.
    /// Manages window state, theme integration, and window control commands.
    /// Supports Arabic RTL layout and MaterialDesign theme integration.
    /// </summary>
    public class CustomWindowChromeViewModel : BaseViewModel
    {
        #region Private Fields

        private WindowState _currentWindowState = WindowState.Normal;
        private bool _isMaximized = false;
        private string _windowTitle = string.Empty;
        private ImageSource? _windowIcon;
        private Brush _titleBarBackground = Brushes.Transparent;
        private Brush _titleBarForeground = Brushes.Black;
        private Brush _windowBorderBrush = Brushes.Gray;
        private Window? _targetWindow;
        private FlowDirection _titleBarFlowDirection = FlowDirection.LeftToRight;
        private HorizontalAlignment _titleAlignment = HorizontalAlignment.Left;
        private HorizontalAlignment _windowControlsAlignment = HorizontalAlignment.Right;
        private bool _isHighContrastMode = false;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the current window state
        /// </summary>
        public WindowState CurrentWindowState
        {
            get => _currentWindowState;
            set
            {
                if (SetProperty(ref _currentWindowState, value))
                {
                    // Update IsMaximized when window state changes
                    IsMaximized = value == WindowState.Maximized;
                    LoggingService.LogDebug($"Window state changed to: {value}", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets whether the window is maximized
        /// </summary>
        public bool IsMaximized
        {
            get => _isMaximized;
            set
            {
                if (SetProperty(ref _isMaximized, value))
                {
                    LoggingService.LogDebug($"IsMaximized changed to: {value}", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets the window title
        /// </summary>
        public string WindowTitle
        {
            get => _windowTitle;
            set
            {
                if (SetProperty(ref _windowTitle, value))
                {
                    LoggingService.LogDebug($"Window title changed to: {value}", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets the window icon
        /// </summary>
        public ImageSource? WindowIcon
        {
            get => _windowIcon;
            set
            {
                if (SetProperty(ref _windowIcon, value))
                {
                    LoggingService.LogDebug("Window icon changed", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets the title bar background brush (bound to DynamicResource)
        /// </summary>
        public Brush TitleBarBackground
        {
            get => _titleBarBackground;
            set
            {
                if (SetProperty(ref _titleBarBackground, value))
                {
                    LoggingService.LogDebug("Title bar background changed", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets the title bar foreground brush (bound to DynamicResource)
        /// </summary>
        public Brush TitleBarForeground
        {
            get => _titleBarForeground;
            set
            {
                if (SetProperty(ref _titleBarForeground, value))
                {
                    LoggingService.LogDebug("Title bar foreground changed", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets the window border brush (bound to DynamicResource)
        /// </summary>
        public Brush WindowBorderBrush
        {
            get => _windowBorderBrush;
            set
            {
                if (SetProperty(ref _windowBorderBrush, value))
                {
                    LoggingService.LogDebug("Window border brush changed", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets the flow direction for the title bar to support RTL layouts
        /// </summary>
        public FlowDirection TitleBarFlowDirection
        {
            get => _titleBarFlowDirection;
            set
            {
                if (SetProperty(ref _titleBarFlowDirection, value))
                {
                    LoggingService.LogDebug($"Title bar flow direction changed to: {value}", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets the horizontal alignment for the window title text
        /// </summary>
        public HorizontalAlignment TitleAlignment
        {
            get => _titleAlignment;
            set
            {
                if (SetProperty(ref _titleAlignment, value))
                {
                    LoggingService.LogDebug($"Title alignment changed to: {value}", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets the horizontal alignment for the window controls
        /// </summary>
        public HorizontalAlignment WindowControlsAlignment
        {
            get => _windowControlsAlignment;
            set
            {
                if (SetProperty(ref _windowControlsAlignment, value))
                {
                    LoggingService.LogDebug($"Window controls alignment changed to: {value}", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets or sets whether the system is in high contrast mode
        /// </summary>
        public bool IsHighContrastMode
        {
            get => _isHighContrastMode;
            set
            {
                if (SetProperty(ref _isHighContrastMode, value))
                {
                    LoggingService.LogDebug($"High contrast mode changed to: {value}", "CustomWindowChromeViewModel");
                }
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to minimize the window
        /// </summary>
        public RelayCommand MinimizeCommand { get; private set; }

        /// <summary>
        /// Command to maximize or restore the window
        /// </summary>
        public RelayCommand MaximizeRestoreCommand { get; private set; }

        /// <summary>
        /// Command to close the window
        /// </summary>
        public RelayCommand CloseCommand { get; private set; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the CustomWindowChromeViewModel
        /// </summary>
        /// <param name="targetWindow">The window this ViewModel is associated with (optional)</param>
        public CustomWindowChromeViewModel(Window? targetWindow = null)
        {
            try
            {
                LoggingService.LogDebug("Initializing CustomWindowChromeViewModel", "CustomWindowChromeViewModel");

                // Store reference to target window
                _targetWindow = targetWindow;

                // Initialize properties
                InitializeProperties();

                // Initialize commands
                InitializeCommands();

                // Subscribe to theme changes
                SubscribeToThemeChanges();

                // Apply current theme
                ApplyCurrentTheme();

                // Initialize RTL support
                UpdateRtlLayout();

                // Subscribe to system parameter changes for high contrast monitoring
                SystemParameters.StaticPropertyChanged += OnSystemParametersChanged;

                LoggingService.LogDebug("CustomWindowChromeViewModel initialized successfully", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing CustomWindowChromeViewModel: {ex.Message}", "CustomWindowChromeViewModel");
                throw;
            }
        }

        #endregion

        #region Initialization Methods

        /// <summary>
        /// Initializes default property values
        /// </summary>
        private void InitializeProperties()
        {
            try
            {
                // Set default window properties
                CurrentWindowState = WindowState.Normal;
                IsMaximized = false;
                WindowTitle = "UFU2"; // Default application title

                // Initialize theme properties with default values
                // These will be updated when theme is applied
                TitleBarBackground = Brushes.Transparent;
                TitleBarForeground = Brushes.Black;
                WindowBorderBrush = Brushes.Gray;

                // Initialize RTL properties with default values
                TitleBarFlowDirection = FlowDirection.LeftToRight;
                TitleAlignment = HorizontalAlignment.Left;
                WindowControlsAlignment = HorizontalAlignment.Right;

                // Initialize high contrast mode detection
                IsHighContrastMode = SystemParameters.HighContrast;

                LoggingService.LogDebug("Properties initialized with default values", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing properties: {ex.Message}", "CustomWindowChromeViewModel");
                throw;
            }
        }

        /// <summary>
        /// Initializes window control commands using RelayCommand pattern
        /// </summary>
        private void InitializeCommands()
        {
            try
            {
                // Initialize MinimizeCommand
                MinimizeCommand = new RelayCommand(
                    execute: ExecuteMinimizeCommand,
                    canExecute: CanExecuteMinimizeCommand,
                    commandName: "MinimizeCommand"
                );

                // Initialize MaximizeRestoreCommand
                MaximizeRestoreCommand = new RelayCommand(
                    execute: ExecuteMaximizeRestoreCommand,
                    canExecute: CanExecuteMaximizeRestoreCommand,
                    commandName: "MaximizeRestoreCommand"
                );

                // Initialize CloseCommand
                CloseCommand = new RelayCommand(
                    execute: ExecuteCloseCommand,
                    canExecute: CanExecuteCloseCommand,
                    commandName: "CloseCommand"
                );

                LoggingService.LogDebug("Window control commands initialized successfully", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing commands: {ex.Message}", "CustomWindowChromeViewModel");
                throw;
            }
        }

        /// <summary>
        /// Subscribes to theme change events from ThemeManager
        /// </summary>
        private void SubscribeToThemeChanges()
        {
            try
            {
                // Subscribe to theme changed events
                ThemeManager.ThemeChanged += OnThemeChanged;
                LoggingService.LogDebug("Subscribed to theme change events", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error subscribing to theme changes: {ex.Message}", "CustomWindowChromeViewModel");
                throw;
            }
        }

        /// <summary>
        /// Applies the current theme to the window chrome properties
        /// </summary>
        private void ApplyCurrentTheme()
        {
            try
            {
                var currentTheme = ThemeManager.CurrentTheme;
                UpdateThemeProperties(currentTheme);
                LoggingService.LogDebug($"Applied current theme: {currentTheme}", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying current theme: {ex.Message}", "CustomWindowChromeViewModel");
                // Continue with default theme properties
            }
        }

        #endregion

        #region Theme Management

        /// <summary>
        /// Handles theme change events from ThemeManager
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Theme change event arguments</param>
        private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug($"Theme changed from {e.PreviousTheme} to {e.NewTheme}", "CustomWindowChromeViewModel");
                
                // Update theme properties on UI thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    UpdateThemeProperties(e.NewTheme);
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling theme change: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Updates theme-related properties based on the specified theme
        /// </summary>
        /// <param name="theme">The theme to apply</param>
        private void UpdateThemeProperties(ApplicationTheme theme)
        {
            try
            {
                // Get theme-appropriate brushes from application resources
                // These will be bound to DynamicResource in XAML
                var titleBarBackgroundKey = theme == ApplicationTheme.Dark 
                    ? "DarkTheme_TitleBarBackground" 
                    : "LightTheme_TitleBarBackground";
                
                var titleBarForegroundKey = theme == ApplicationTheme.Dark 
                    ? "DarkTheme_TitleBarForeground" 
                    : "LightTheme_TitleBarForeground";
                
                var windowBorderKey = theme == ApplicationTheme.Dark 
                    ? "DarkTheme_WindowBorder" 
                    : "LightTheme_WindowBorder";

                // Try to get brushes from application resources
                var titleBarBackground = TryGetBrushResource(titleBarBackgroundKey);
                var titleBarForeground = TryGetBrushResource(titleBarForegroundKey);
                var windowBorder = TryGetBrushResource(windowBorderKey);

                // Update properties if resources were found
                if (titleBarBackground != null)
                    TitleBarBackground = titleBarBackground;
                
                if (titleBarForeground != null)
                    TitleBarForeground = titleBarForeground;
                
                if (windowBorder != null)
                    WindowBorderBrush = windowBorder;

                LoggingService.LogDebug($"Theme properties updated for {theme} theme", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating theme properties: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Attempts to get a brush resource from application resources
        /// </summary>
        /// <param name="resourceKey">The resource key to retrieve</param>
        /// <returns>The brush resource or null if not found</returns>
        private Brush? TryGetBrushResource(string resourceKey)
        {
            try
            {
                var resource = Application.Current?.Resources[resourceKey];
                if (resource is Brush brush)
                {
                    return brush;
                }
                
                LoggingService.LogWarning($"Brush resource '{resourceKey}' not found or invalid type", "CustomWindowChromeViewModel");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting brush resource '{resourceKey}': {ex.Message}", "CustomWindowChromeViewModel");
                return null;
            }
        }

        #endregion

        #region System Parameter Monitoring

        /// <summary>
        /// Handles system parameter changes (e.g., high contrast mode)
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Property changed event arguments</param>
        private void OnSystemParametersChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            try
            {
                if (e.PropertyName == nameof(SystemParameters.HighContrast))
                {
                    var newHighContrastMode = SystemParameters.HighContrast;
                    LoggingService.LogDebug($"High contrast mode changed to: {newHighContrastMode}", "CustomWindowChromeViewModel");
                    
                    // Update the property on the UI thread
                    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        IsHighContrastMode = newHighContrastMode;
                    }));
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling system parameters change: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        #endregion

        #region Command Execution Methods

        /// <summary>
        /// Executes the minimize command
        /// </summary>
        private void ExecuteMinimizeCommand()
        {
            try
            {
                LoggingService.LogDebug("Executing MinimizeCommand", "CustomWindowChromeViewModel");

                var window = GetTargetWindow();
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot minimize: target window is null", "CustomWindowChromeViewModel");
                    ErrorManager.ShowUserWarningToast(
                        "لا يمكن تصغير النافذة: النافذة المستهدفة غير متاحة",
                        "خطأ في النافذة المستهدفة",
                        "CustomWindowChromeViewModel"
                    );
                    return;
                }

                // Log current window state before operation
                LoggingService.LogDebug($"Current window state before minimize: {window.WindowState} (Title: {window.Title})", "CustomWindowChromeViewModel");

                var success = WindowStateManager.MinimizeWindow(window);
                if (success)
                {
                    // Update ViewModel state to reflect the change
                    try
                    {
                        UpdateWindowState(WindowState.Minimized);
                        LoggingService.LogDebug($"MinimizeCommand executed successfully for window: {window.Title}", "CustomWindowChromeViewModel");
                    }
                    catch (Exception updateEx)
                    {
                        LoggingService.LogWarning($"Window minimized but failed to update ViewModel state: {updateEx.Message}", "CustomWindowChromeViewModel");
                        // Continue - the window was minimized successfully even if ViewModel update failed
                    }
                }
                else
                {
                    LoggingService.LogWarning($"MinimizeCommand execution failed for window: {window.Title} (ResizeMode: {window.ResizeMode})", "CustomWindowChromeViewModel");
                    
                    // Show Arabic error message to user with more specific information
                    ErrorManager.ShowUserWarningToast(
                        "فشل في تصغير النافذة. قد تكون النافذة لا تدعم التصغير",
                        "خطأ في تصغير النافذة",
                        "CustomWindowChromeViewModel"
                    );
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error executing MinimizeCommand: {ex.Message}", "CustomWindowChromeViewModel");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "CustomWindowChromeViewModel");
                
                // Show Arabic error message to user
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ خطير أثناء تصغير النافذة",
                    "خطأ خطير في النظام",
                    LogLevel.Error,
                    "CustomWindowChromeViewModel"
                );
            }
        }

        /// <summary>
        /// Determines if the minimize command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanExecuteMinimizeCommand()
        {
            try
            {
                var window = GetTargetWindow();
                if (window == null)
                {
                    LoggingService.LogDebug("CanExecuteMinimizeCommand: false - target window is null", "CustomWindowChromeViewModel");
                    return false;
                }

                // Check if window allows minimizing
                var canMinimize = window.ResizeMode != ResizeMode.NoResize;
                LoggingService.LogDebug($"CanExecuteMinimizeCommand: {canMinimize} - ResizeMode: {window.ResizeMode}", "CustomWindowChromeViewModel");
                
                return canMinimize;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking CanExecuteMinimizeCommand: {ex.Message}", "CustomWindowChromeViewModel");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "CustomWindowChromeViewModel");
                
                // Return false on error to prevent command execution
                return false;
            }
        }

        /// <summary>
        /// Executes the maximize/restore command
        /// </summary>
        private void ExecuteMaximizeRestoreCommand()
        {
            try
            {
                LoggingService.LogDebug("Executing MaximizeRestoreCommand", "CustomWindowChromeViewModel");

                var window = GetTargetWindow();
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot maximize/restore: target window is null", "CustomWindowChromeViewModel");
                    ErrorManager.ShowUserWarningToast(
                        "لا يمكن تكبير/استعادة النافذة: النافذة المستهدفة غير متاحة",
                        "خطأ في النافذة المستهدفة",
                        "CustomWindowChromeViewModel"
                    );
                    return;
                }

                // Log current window state and intended operation
                var currentState = window.WindowState;
                var targetState = currentState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
                var operation = currentState == WindowState.Maximized ? "restore" : "maximize";
                
                LoggingService.LogDebug($"Current window state: {currentState}, Target operation: {operation} to {targetState} (Title: {window.Title})", "CustomWindowChromeViewModel");

                var success = WindowStateManager.MaximizeRestoreWindow(window);
                if (success)
                {
                    // Update ViewModel state to reflect the change
                    try
                    {
                        UpdateWindowState(window.WindowState);
                        LoggingService.LogDebug($"MaximizeRestoreCommand executed successfully: {operation} operation completed for window: {window.Title} (Final state: {window.WindowState})", "CustomWindowChromeViewModel");
                    }
                    catch (Exception updateEx)
                    {
                        LoggingService.LogWarning($"Window {operation} succeeded but failed to update ViewModel state: {updateEx.Message}", "CustomWindowChromeViewModel");
                        // Continue - the window operation was successful even if ViewModel update failed
                    }
                }
                else
                {
                    LoggingService.LogWarning($"MaximizeRestoreCommand execution failed: {operation} operation failed for window: {window.Title} (ResizeMode: {window.ResizeMode})", "CustomWindowChromeViewModel");
                    
                    // Show Arabic error message to user with more specific information
                    var arabicOperation = operation == "maximize" ? "تكبير" : "استعادة";
                    ErrorManager.ShowUserWarningToast(
                        $"فشل في {arabicOperation} النافذة. قد تكون النافذة لا تدعم هذه العملية",
                        $"خطأ في {arabicOperation} النافذة",
                        "CustomWindowChromeViewModel"
                    );
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error executing MaximizeRestoreCommand: {ex.Message}", "CustomWindowChromeViewModel");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "CustomWindowChromeViewModel");
                
                // Show Arabic error message to user
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ خطير أثناء تكبير/استعادة النافذة",
                    "خطأ خطير في النظام",
                    LogLevel.Error,
                    "CustomWindowChromeViewModel"
                );
            }
        }

        /// <summary>
        /// Determines if the maximize/restore command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanExecuteMaximizeRestoreCommand()
        {
            try
            {
                var window = GetTargetWindow();
                if (window == null)
                {
                    LoggingService.LogDebug("CanExecuteMaximizeRestoreCommand: false - target window is null", "CustomWindowChromeViewModel");
                    return false;
                }

                // Check if window allows maximizing/restoring
                var canMaximizeRestore = window.ResizeMode != ResizeMode.NoResize && window.ResizeMode != ResizeMode.CanMinimize;
                LoggingService.LogDebug($"CanExecuteMaximizeRestoreCommand: {canMaximizeRestore} - ResizeMode: {window.ResizeMode}", "CustomWindowChromeViewModel");
                
                return canMaximizeRestore;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking CanExecuteMaximizeRestoreCommand: {ex.Message}", "CustomWindowChromeViewModel");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "CustomWindowChromeViewModel");
                
                // Return false on error to prevent command execution
                return false;
            }
        }

        /// <summary>
        /// Executes the close command
        /// </summary>
        private void ExecuteCloseCommand()
        {
            try
            {
                LoggingService.LogDebug("Executing CloseCommand", "CustomWindowChromeViewModel");

                var window = GetTargetWindow();
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot close: target window is null", "CustomWindowChromeViewModel");
                    ErrorManager.ShowUserWarningToast(
                        "لا يمكن إغلاق النافذة: النافذة المستهدفة غير متاحة",
                        "خطأ في النافذة المستهدفة",
                        "CustomWindowChromeViewModel"
                    );
                    return;
                }

                LoggingService.LogDebug($"Attempting to close window: {window.Title} (Type: {window.GetType().Name})", "CustomWindowChromeViewModel");

                // For main application window, we might want to show confirmation
                // The window's Closing event handler can handle this logic
                var success = WindowStateManager.CloseWindow(window, forceClose: false);
                if (success)
                {
                    LoggingService.LogDebug($"CloseCommand executed successfully for window: {window.Title}", "CustomWindowChromeViewModel");
                }
                else
                {
                    LoggingService.LogWarning($"CloseCommand execution failed for window: {window.Title} - operation may have been cancelled", "CustomWindowChromeViewModel");
                    
                    // Note: Don't show error message if close was cancelled by user
                    // The WindowStateManager will have already logged the cancellation
                    LoggingService.LogDebug("Close operation may have been cancelled by user or application logic", "CustomWindowChromeViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error executing CloseCommand: {ex.Message}", "CustomWindowChromeViewModel");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "CustomWindowChromeViewModel");
                
                // Show Arabic error message to user
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ خطير أثناء إغلاق النافذة",
                    "خطأ خطير في النظام",
                    LogLevel.Error,
                    "CustomWindowChromeViewModel"
                );
            }
        }

        /// <summary>
        /// Determines if the close command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanExecuteCloseCommand()
        {
            try
            {
                var window = GetTargetWindow();
                var canClose = window != null;
                
                LoggingService.LogDebug($"CanExecuteCloseCommand: {canClose} - window available: {window != null}", "CustomWindowChromeViewModel");
                
                // Close command should always be available if we have a valid window
                return canClose;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking CanExecuteCloseCommand: {ex.Message}", "CustomWindowChromeViewModel");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "CustomWindowChromeViewModel");
                
                // Return false on error to prevent command execution
                return false;
            }
        }

        /// <summary>
        /// Gets the target window for command execution
        /// </summary>
        /// <returns>The target window or null if not available</returns>
        private Window? GetTargetWindow()
        {
            try
            {
                // If we have a stored reference, validate and use it
                if (_targetWindow != null)
                {
                    try
                    {
                        // Validate that the window is still valid and accessible
                        var isValid = _targetWindow.IsLoaded;
                        LoggingService.LogDebug($"Using stored target window: {_targetWindow.Title} (IsLoaded: {isValid})", "CustomWindowChromeViewModel");
                        return _targetWindow;
                    }
                    catch (Exception windowEx)
                    {
                        LoggingService.LogWarning($"Stored target window is no longer valid: {windowEx.Message}", "CustomWindowChromeViewModel");
                        _targetWindow = null; // Clear invalid reference
                    }
                }

                // Otherwise, try to get the active window
                try
                {
                    var mainWindow = Application.Current?.MainWindow;
                    if (mainWindow != null)
                    {
                        LoggingService.LogDebug($"Using Application.Current.MainWindow: {mainWindow.Title}", "CustomWindowChromeViewModel");
                        return mainWindow;
                    }
                    else
                    {
                        LoggingService.LogWarning("Application.Current.MainWindow is null", "CustomWindowChromeViewModel");
                    }
                }
                catch (Exception appEx)
                {
                    LoggingService.LogWarning($"Error accessing Application.Current.MainWindow: {appEx.Message}", "CustomWindowChromeViewModel");
                }

                // Try to get any active window as last resort
                try
                {
                    var activeWindows = Application.Current?.Windows;
                    if (activeWindows != null && activeWindows.Count > 0)
                    {
                        foreach (Window window in activeWindows)
                        {
                            if (window.IsLoaded && window.IsVisible)
                            {
                                LoggingService.LogDebug($"Using first available active window: {window.Title}", "CustomWindowChromeViewModel");
                                return window;
                            }
                        }
                    }
                }
                catch (Exception activeEx)
                {
                    LoggingService.LogWarning($"Error searching for active windows: {activeEx.Message}", "CustomWindowChromeViewModel");
                }

                LoggingService.LogWarning("No valid target window found", "CustomWindowChromeViewModel");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error getting target window: {ex.Message}", "CustomWindowChromeViewModel");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "CustomWindowChromeViewModel");
                return null;
            }
        }

        #endregion

        #region Error Handling and Recovery

        /// <summary>
        /// Handles window chrome related errors with appropriate user feedback and recovery attempts
        /// </summary>
        /// <param name="ex">The exception that occurred</param>
        /// <param name="operation">The operation that failed</param>
        /// <param name="severity">The severity level of the error</param>
        private void HandleWindowChromeError(Exception ex, string operation, LogLevel severity = LogLevel.Error)
        {
            try
            {
                LoggingService.LogError($"Window chrome error during {operation}: {ex.Message}", "CustomWindowChromeViewModel");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "CustomWindowChromeViewModel");

                // Get Arabic error messages based on operation
                var (arabicMessage, arabicTitle) = GetArabicErrorMessage(operation);

                // Show appropriate user notification based on severity
                switch (severity)
                {
                    case LogLevel.Error:
                        ErrorManager.HandleErrorToast(ex, arabicMessage, arabicTitle, LogLevel.Error, "CustomWindowChromeViewModel");
                        break;
                    case LogLevel.Warning:
                        ErrorManager.HandleWarningToast(ex, arabicMessage, arabicTitle, "CustomWindowChromeViewModel");
                        break;
                    default:
                        ErrorManager.ShowUserInfoToast(arabicMessage, arabicTitle, "CustomWindowChromeViewModel");
                        break;
                }

                // Attempt recovery based on operation type
                AttemptErrorRecovery(operation, ex);
            }
            catch (Exception handlingEx)
            {
                LoggingService.LogError($"Error handling window chrome error: {handlingEx.Message}", "CustomWindowChromeViewModel");
                
                // Fallback error handling
                try
                {
                    ErrorManager.ShowUserErrorToast(
                        "حدث خطأ خطير في إدارة أخطاء إطار النافذة",
                        "خطأ خطير في النظام",
                        "CustomWindowChromeViewModel"
                    );
                }
                catch
                {
                    // Last resort - log to console
                    LoggingService.LogError("Critical: Failed to handle window chrome error and show user notification", "CustomWindowChromeViewModel");
                }
            }
        }

        /// <summary>
        /// Gets appropriate Arabic error messages for different window chrome operations
        /// </summary>
        /// <param name="operation">The operation that failed</param>
        /// <returns>Tuple containing Arabic message and title</returns>
        private (string message, string title) GetArabicErrorMessage(string operation)
        {
            return operation.ToLower() switch
            {
                "minimize" => ("فشل في تصغير النافذة. قد تكون النافذة لا تدعم التصغير", "خطأ في تصغير النافذة"),
                "maximize" => ("فشل في تكبير النافذة. قد تكون النافذة لا تدعم التكبير", "خطأ في تكبير النافذة"),
                "restore" => ("فشل في استعادة النافذة إلى الحجم العادي", "خطأ في استعادة النافذة"),
                "close" => ("فشل في إغلاق النافذة. قد تكون هناك عمليات قيد التنفيذ", "خطأ في إغلاق النافذة"),
                "theme_update" => ("فشل في تحديث ألوان إطار النافذة", "خطأ في تحديث الألوان"),
                "initialization" => ("فشل في تهيئة إطار النافذة المخصص", "خطأ في التهيئة"),
                "state_sync" => ("فشل في مزامنة حالة النافذة مع واجهة المستخدم", "خطأ في المزامنة"),
                _ => ("حدث خطأ في إدارة إطار النافذة", "خطأ في إطار النافذة")
            };
        }

        /// <summary>
        /// Attempts to recover from window chrome errors
        /// </summary>
        /// <param name="operation">The operation that failed</param>
        /// <param name="originalException">The original exception</param>
        private void AttemptErrorRecovery(string operation, Exception originalException)
        {
            try
            {
                LoggingService.LogDebug($"Attempting recovery from {operation} error", "CustomWindowChromeViewModel");

                switch (operation.ToLower())
                {
                    case "minimize":
                    case "maximize":
                    case "restore":
                        // For window state operations, try to refresh the ViewModel state
                        RefreshWindowState();
                        break;

                    case "theme_update":
                        // For theme errors, try to reapply the current theme
                        ReapplyCurrentTheme();
                        break;

                    case "state_sync":
                        // For synchronization errors, force a state refresh
                        ForceStateRefresh();
                        break;

                    default:
                        LoggingService.LogDebug($"No specific recovery action for operation: {operation}", "CustomWindowChromeViewModel");
                        break;
                }

                LoggingService.LogDebug($"Recovery attempt completed for {operation} error", "CustomWindowChromeViewModel");
            }
            catch (Exception recoveryEx)
            {
                LoggingService.LogError($"Error during recovery attempt for {operation}: {recoveryEx.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Refreshes the window state by querying the actual window
        /// </summary>
        private void RefreshWindowState()
        {
            try
            {
                var window = GetTargetWindow();
                if (window != null)
                {
                    LoggingService.LogDebug($"Refreshing window state from actual window: {window.WindowState}", "CustomWindowChromeViewModel");
                    UpdateWindowState(window.WindowState);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error refreshing window state: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Reapplies the current theme to recover from theme-related errors
        /// </summary>
        private void ReapplyCurrentTheme()
        {
            try
            {
                LoggingService.LogDebug("Reapplying current theme for error recovery", "CustomWindowChromeViewModel");
                var currentTheme = ThemeManager.CurrentTheme;
                UpdateThemeProperties(currentTheme);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error reapplying theme: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Forces a complete state refresh for synchronization error recovery
        /// </summary>
        private void ForceStateRefresh()
        {
            try
            {
                LoggingService.LogDebug("Forcing complete state refresh", "CustomWindowChromeViewModel");
                
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        RefreshWindowState();
                        ReapplyCurrentTheme();
                        UpdateRtlLayout();
                    }
                    catch (Exception refreshEx)
                    {
                        LoggingService.LogError($"Error during forced state refresh: {refreshEx.Message}", "CustomWindowChromeViewModel");
                    }
                }));
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error forcing state refresh: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        #endregion

        #region RTL Layout Management

        /// <summary>
        /// Updates the RTL layout properties based on current culture
        /// </summary>
        private void UpdateRtlLayout()
        {
            try
            {
                var currentCulture = CultureInfo.CurrentUICulture;
                var isRtl = currentCulture.TextInfo.IsRightToLeft;

                LoggingService.LogDebug($"Updating RTL layout for culture: {currentCulture.Name}, IsRTL: {isRtl}", "CustomWindowChromeViewModel");

                // Update flow direction for title bar
                TitleBarFlowDirection = isRtl ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;

                // Update title alignment for RTL
                if (isRtl)
                {
                    // In RTL, title should be aligned to the right
                    TitleAlignment = HorizontalAlignment.Right;
                    // Window controls should be on the left side in RTL
                    WindowControlsAlignment = HorizontalAlignment.Left;
                }
                else
                {
                    // In LTR, title should be aligned to the left
                    TitleAlignment = HorizontalAlignment.Left;
                    // Window controls should be on the right side in LTR
                    WindowControlsAlignment = HorizontalAlignment.Right;
                }

                LoggingService.LogDebug($"RTL layout updated - FlowDirection: {TitleBarFlowDirection}, TitleAlignment: {TitleAlignment}, ControlsAlignment: {WindowControlsAlignment}", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating RTL layout: {ex.Message}", "CustomWindowChromeViewModel");
                
                // Fallback to LTR layout
                TitleBarFlowDirection = FlowDirection.LeftToRight;
                TitleAlignment = HorizontalAlignment.Left;
                WindowControlsAlignment = HorizontalAlignment.Right;
            }
        }

        /// <summary>
        /// Updates RTL layout when culture changes
        /// </summary>
        public void RefreshRtlLayout()
        {
            try
            {
                UpdateRtlLayout();
                LoggingService.LogDebug("RTL layout refreshed", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error refreshing RTL layout: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Updates the window state and related properties
        /// </summary>
        /// <param name="newState">The new window state</param>
        public void UpdateWindowState(WindowState newState)
        {
            try
            {
                CurrentWindowState = newState;
                
                // Refresh command states when window state changes
                RefreshCommandStates();
                
                LoggingService.LogDebug($"Window state updated to: {newState}", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating window state: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Sets the target window for this ViewModel
        /// </summary>
        /// <param name="window">The target window</param>
        public void SetTargetWindow(Window window)
        {
            try
            {
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot set null target window", "CustomWindowChromeViewModel");
                    return;
                }

                _targetWindow = window;
                
                // Update window-related properties
                UpdateWindowTitle(window.Title);
                UpdateWindowState(window.WindowState);
                
                // Refresh command states
                RefreshCommandStates();
                
                LoggingService.LogDebug($"Target window set: {window.Title}", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting target window: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Refreshes the CanExecute state of all commands
        /// </summary>
        public void RefreshCommandStates()
        {
            try
            {
                MinimizeCommand?.RaiseCanExecuteChanged();
                MaximizeRestoreCommand?.RaiseCanExecuteChanged();
                CloseCommand?.RaiseCanExecuteChanged();
                
                LoggingService.LogDebug("Command states refreshed", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error refreshing command states: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Updates the window title
        /// </summary>
        /// <param name="title">The new window title</param>
        public void UpdateWindowTitle(string title)
        {
            try
            {
                WindowTitle = title ?? string.Empty;
                LoggingService.LogDebug($"Window title updated to: {title}", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating window title: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        /// <summary>
        /// Updates the window icon
        /// </summary>
        /// <param name="icon">The new window icon</param>
        public void UpdateWindowIcon(ImageSource? icon)
        {
            try
            {
                WindowIcon = icon;
                LoggingService.LogDebug("Window icon updated", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating window icon: {ex.Message}", "CustomWindowChromeViewModel");
            }
        }

        #endregion


        #region Cleanup

        /// <summary>
        /// Disposes the ViewModel and unsubscribes from events
        /// </summary>
        protected override void OnDispose()
        {
            try
            {
                // Unsubscribe from theme changes
                ThemeManager.ThemeChanged -= OnThemeChanged;
                
                // Unsubscribe from system parameter changes
                SystemParameters.StaticPropertyChanged -= OnSystemParametersChanged;
                
                LoggingService.LogDebug("CustomWindowChromeViewModel disposed", "CustomWindowChromeViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disposing CustomWindowChromeViewModel: {ex.Message}", "CustomWindowChromeViewModel");
            }
            finally
            {
                base.OnDispose();
            }
        }

        #endregion
    }
    
}