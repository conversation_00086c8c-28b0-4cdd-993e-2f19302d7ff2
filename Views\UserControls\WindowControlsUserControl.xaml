<UserControl
    x:Class="UFU2.Views.UserControls.WindowControlsUserControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:UFU2.Views.UserControls"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="32"
    d:DesignWidth="138"
    mc:Ignorable="d">

    <UserControl.Resources>
        <!--  Converters  -->
        <local:BoolToMaximizeRestoreIconConverter x:Key="BoolToMaximizeRestoreIconConverter" />
        <local:BoolToMaximizeRestoreTooltipConverter x:Key="BoolToMaximizeRestoreTooltipConverter" />

        <!--  Window Control Button Style  -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="Width" Value="46" />
            <Setter Property="Height" Value="32" />
            <Setter Property="Padding" Value="0" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            x:Name="border"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter
                                x:Name="contentPresenter"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                Content="{TemplateBinding Content}"
                                ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                Focusable="False"
                                RecognizesAccessKey="True" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource SurfaceBrightBrush}" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource InverseSurfaceBrush}" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  Close Button Style (Red hover)  -->
        <Style
            x:Key="CloseButtonStyle"
            BasedOn="{StaticResource WindowControlButtonStyle}"
            TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            x:Name="border"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter
                                x:Name="contentPresenter"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                Content="{TemplateBinding Content}"
                                ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                Focusable="False"
                                RecognizesAccessKey="True" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource ErrorBrush}" />
                                <Setter Property="Foreground" Value="White" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{DynamicResource ErrorBrush}" />
                                <Setter Property="Foreground" Value="White" />
                                <Setter Property="Opacity" Value="0.8" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <!--  Window Controls Container  -->
    <StackPanel
        x:Name="WindowControlsPanel"
        HorizontalAlignment="{Binding WindowControlsAlignment}"
        VerticalAlignment="Top"
        FlowDirection="{Binding TitleBarFlowDirection}"
        Orientation="Horizontal">

        <!--  Minimize Button  -->
        <Button
            x:Name="MinimizeButton"
            AutomationProperties.AcceleratorKey="Alt+F9"
            AutomationProperties.HelpText="اضغط لتصغير النافذة إلى شريط المهام"
            AutomationProperties.Name="تصغير النافذة"
            Command="{Binding MinimizeCommand}"
            Style="{StaticResource WindowControlButtonStyle}"
            TabIndex="1"
            ToolTip="تصغير">
            <materialDesign:PackIcon
                Width="16"
                Height="16"
                AutomationProperties.Name="أيقونة التصغير"
                Kind="WindowMinimize" />
        </Button>

        <!--  Maximize/Restore Button  -->
        <Button
            x:Name="MaximizeRestoreButton"
            AutomationProperties.AcceleratorKey="Alt+F10"
            AutomationProperties.HelpText="اضغط لتكبير النافذة أو استعادة حجمها الطبيعي"
            AutomationProperties.Name="{Binding IsMaximized, Converter={StaticResource BoolToMaximizeRestoreTooltipConverter}}"
            Command="{Binding MaximizeRestoreCommand}"
            Style="{StaticResource WindowControlButtonStyle}"
            TabIndex="2">
            <Button.ToolTip>
                <TextBlock Text="{Binding IsMaximized, Converter={StaticResource BoolToMaximizeRestoreTooltipConverter}}" />
            </Button.ToolTip>
            <materialDesign:PackIcon
                x:Name="MaximizeRestoreIcon"
                Width="16"
                Height="16"
                AutomationProperties.Name="{Binding IsMaximized, Converter={StaticResource BoolToMaximizeRestoreTooltipConverter}}"
                Kind="{Binding IsMaximized, Converter={StaticResource BoolToMaximizeRestoreIconConverter}}" />
        </Button>

        <!--  Close Button  -->
        <Button
            x:Name="CloseButton"
            AutomationProperties.AcceleratorKey="Alt+F4"
            AutomationProperties.HelpText="اضغط لإغلاق النافذة والخروج من التطبيق"
            AutomationProperties.Name="إغلاق النافذة"
            Command="{Binding CloseCommand}"
            Style="{StaticResource CloseButtonStyle}"
            TabIndex="3"
            ToolTip="إغلاق">
            <materialDesign:PackIcon
                Width="16"
                Height="16"
                AutomationProperties.Name="أيقونة الإغلاق"
                Kind="WindowClose" />
        </Button>
    </StackPanel>
</UserControl>