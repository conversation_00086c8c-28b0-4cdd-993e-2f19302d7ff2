using System;
using System.Collections.Generic;
using System.Globalization;
using System.Windows.Data;

namespace UFU2.Common.Converters
{
    /// <summary>
    /// Converter that provides activity-specific status options for ComboBox controls.
    /// Returns different status lists based on the selected activity type.
    /// Each activity type may have different valid status values and Arabic labels.
    /// </summary>
    public class ActivityStatusItemsComboBoxConverter : IValueConverter
    {
        /// <summary>
        /// Converts the selected activity type to a list of appropriate status options.
        /// </summary>
        /// <param name="value">The selected activity type (MainCommercial, SecondaryCommercial, Craft, Professional)</param>
        /// <param name="targetType">The target type (should be IEnumerable)</param>
        /// <param name="parameter">Optional parameter for customization (not used currently)</param>
        /// <param name="culture">The culture info (not used)</param>
        /// <returns>List of status options appropriate for the activity type</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var activityType = value?.ToString() ?? "MainCommercial";
                return GetStatusItems(activityType);
            }
            catch (Exception)
            {
                // Return default status items if any error occurs
                return GetDefaultStatusItems();
            }
        }

        /// <summary>
        /// Not implemented for this converter as it's one-way only.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ActivityStatusItemsComboBoxConverter is a one-way converter.");
        }

        /// <summary>
        /// Gets the appropriate status items based on activity type.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>List of status options for the activity type</returns>
        private static List<string> GetStatusItems(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => GetMainCommercialStatusItems(),
                "SecondaryCommercial" => GetSecondaryCommercialStatusItems(),
                "Craft" => GetCraftStatusItems(),
                "Professional" => GetProfessionalStatusItems(),
                _ => GetDefaultStatusItems()
            };
        }

        /// <summary>
        /// Gets status items for main commercial activities.
        /// </summary>
        /// <returns>List of status options for main commercial activities</returns>
        private static List<string> GetMainCommercialStatusItems()
        {
            return new List<string>
            {
                "قيد",                    // Active
                "معدل",               // Edited
                "شطب",                   // Suspended
            };
        }

        /// <summary>
        /// Gets status items for secondary commercial activities.
        /// </summary>
        /// <returns>List of status options for secondary commercial activities</returns>
        private static List<string> GetSecondaryCommercialStatusItems()
        {
            return new List<string>
            {
                "قيد",                    // Active
                "معدل",               // Edited
                "شطب",                   // Suspended
            };
        }

        /// <summary>
        /// Gets status items for craft activities.
        /// </summary>
        /// <returns>List of status options for craft activities</returns>
        private static List<string> GetCraftStatusItems()
        {
            return new List<string>
            {
                "نشط",                    // Active
                "معدل",                   // Edited
                "غير نشط",               // Inactive
                
            };
        }

        /// <summary>
        /// Gets status items for professional activities.
        /// </summary>
        /// <returns>List of status options for professional activities</returns>
        private static List<string> GetProfessionalStatusItems()
        {
            return new List<string>
            {
                "نشط",                    // Active
                "معدل",                   // Edited
                "غير نشط",               // Inactive
            };
        }

        /// <summary>
        /// Gets default status items for unknown activity types.
        /// </summary>
        /// <returns>List of default status options</returns>
        private static List<string> GetDefaultStatusItems()
        {
            return new List<string>
            {
                "قيد",                    // Active
                "معدل",               // Edited
                "شطب",                   // Suspended
            };
        }

        /// <summary>
        /// Gets the default status value for a given activity type.
        /// This can be used to set initial ComboBox selection.
        /// </summary>
        /// <param name="activityType">The selected activity type</param>
        /// <returns>Default status value for the activity type</returns>
        public static string GetDefaultStatus(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => "قيد",
                "SecondaryCommercial" => "قيد",
                "Craft" => "نشط",
                "Professional" => "نشط",
                _ => "قيد"
            };
        }

        /// <summary>
        /// Validates if a status value is valid for the given activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="status">The status value to validate</param>
        /// <returns>True if the status is valid for the activity type, false otherwise</returns>
        public static bool IsValidStatus(string activityType, string status)
        {
            if (string.IsNullOrWhiteSpace(status))
                return false;

            var validStatuses = GetStatusItems(activityType);
            return validStatuses.Contains(status);
        }
    }
}
