// BACKUP: Original DatabaseEntities.cs created on 2025-07-30 before PascalCase schema fix
using System;

namespace UFU2.Models
{
    /// <summary>
    /// Database entity model for the Clients table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class ClientEntity
    {
        /// <summary>
        /// Gets or sets the unique client identifier.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the French name of the client (required).
        /// Maps to 'NameFr' column.
        /// </summary>
        public string NameFr { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Arabic name of the client (optional).
        /// Maps to 'NameAr' column.
        /// </summary>
        public string? NameAr { get; set; }

        /// <summary>
        /// Gets or sets the birth date in DD/MM/YYYY format (optional).
        /// Maps to 'BirthDate' column.
        /// </summary>
        public string? BirthDate { get; set; }

        /// <summary>
        /// Gets or sets the birth place (optional).
        /// Maps to 'BirthPlace' column.
        /// </summary>
        public string? BirthPlace { get; set; }

        /// <summary>
        /// Gets or sets the gender (optional).
        /// Maps to 'Gender' column. 0 = Male, 1 = Female, null = Not specified.
        /// </summary>
        public int? Gender { get; set; }

        /// <summary>
        /// Gets or sets the address (optional).
        /// Maps to 'Address' column.
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// Gets or sets the national ID number (optional).
        /// Maps to 'NationalId' column.
        /// </summary>
        public string? NationalId { get; set; }

        /// <summary>
        /// Gets or sets the creation timestamp.
        /// Maps to 'CreatedAt' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string CreatedAt { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the last update timestamp.
        /// Maps to 'UpdatedAt' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string UpdatedAt { get; set; } = string.Empty;
    }

    /// <summary>
    /// Database entity model for the Activities table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class ActivityEntity
    {
        /// <summary>
        /// Gets or sets the unique activity identifier.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the client UID this activity belongs to.
        /// Maps to 'ClientUid' column.
        /// </summary>
        public string ClientUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the type of activity (optional).
        /// Maps to 'ActivityType' column.
        /// </summary>
        public string? ActivityType { get; set; }

        /// <summary>
        /// Gets or sets the current status of the activity (optional).
        /// Maps to 'ActivityStatus' column.
        /// </summary>
        public string? ActivityStatus { get; set; }

        /// <summary>
        /// Gets or sets the activity start date (optional).
        /// Maps to 'ActivityStartDate' column.
        /// </summary>
        public string? ActivityStartDate { get; set; }

        /// <summary>
        /// Gets or sets the commercial register number (optional).
        /// Maps to 'CommercialRegister' column.
        /// </summary>
        public string? CommercialRegister { get; set; }

        /// <summary>
        /// Gets or sets the activity location (optional).
        /// Maps to 'ActivityLocation' column.
        /// </summary>
        public string? ActivityLocation { get; set; }

        /// <summary>
        /// Gets or sets the NIF number (optional).
        /// Maps to 'NifNumber' column.
        /// </summary>
        public string? NifNumber { get; set; }

        /// <summary>
        /// Gets or sets the NIS number (optional).
        /// Maps to 'NisNumber' column.
        /// </summary>
        public string? NisNumber { get; set; }

        /// <summary>
        /// Gets or sets the ART number (optional).
        /// Maps to 'ArtNumber' column.
        /// </summary>
        public string? ArtNumber { get; set; }

        /// <summary>
        /// Gets or sets the CPI Daira information (optional).
        /// Maps to 'CpiDaira' column.
        /// </summary>
        public string? CpiDaira { get; set; }

        /// <summary>
        /// Gets or sets the CPI Wilaya information (optional).
        /// Maps to 'CpiWilaya' column.
        /// </summary>
        public string? CpiWilaya { get; set; }

        /// <summary>
        /// Gets or sets the activity update date (optional).
        /// Maps to 'ActivityUpdateDate' column.
        /// </summary>
        public string? ActivityUpdateDate { get; set; }

        /// <summary>
        /// Gets or sets the activity update note (optional).
        /// Maps to 'ActivityUpdateNote' column.
        /// </summary>
        public string? ActivityUpdateNote { get; set; }

        /// <summary>
        /// Gets or sets the creation timestamp.
        /// Maps to 'CreatedAt' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string CreatedAt { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the last update timestamp.
        /// Maps to 'UpdatedAt' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string UpdatedAt { get; set; } = string.Empty;
    }

    /// <summary>
    /// Database entity model for the PhoneNumbers table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class PhoneNumberEntity
    {
        /// <summary>
        /// Gets or sets the unique phone number identifier.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the client UID this phone number belongs to.
        /// Maps to 'ClientUid' column.
        /// </summary>
        public string ClientUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the phone number.
        /// Maps to 'PhoneNumber' column.
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Gets or sets the phone type.
        /// Maps to 'PhoneType' column. 0 = Mobile, 1 = Home, 2 = Work, 3 = Other.
        /// </summary>
        public int? PhoneType { get; set; }

        /// <summary>
        /// Gets or sets whether this is the primary phone number.
        /// Maps to 'IsPrimary' column. 1 = Primary, 0 = Not primary.
        /// </summary>
        public int? IsPrimary { get; set; }
    }

    /// <summary>
    /// Database entity model for the FileCheckStates table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class FileCheckStateEntity
    {
        /// <summary>
        /// Gets or sets the unique identifier for this file check state.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the activity UID this file check belongs to.
        /// Maps to 'ActivityUid' column.
        /// </summary>
        public string ActivityUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the file check type.
        /// Maps to 'FileCheckType' column.
        /// Valid values: CAS, NIF, NIS, RC, ART, AGR, DEX.
        /// </summary>
        public string FileCheckType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the file check is completed.
        /// Maps to 'IsChecked' column. 1 = Checked, 0 = Not checked.
        /// </summary>
        public int IsChecked { get; set; } = 0;

        /// <summary>
        /// Gets or sets the date when the file check was completed (optional).
        /// Maps to 'CheckedDate' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string? CheckedDate { get; set; }
    }

    /// <summary>
    /// Database entity model for the G12Check table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class G12CheckEntity
    {
        /// <summary>
        /// Gets or sets the unique identifier for this G12 check.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the activity UID this G12 check belongs to.
        /// Maps to 'ActivityUid' column.
        /// </summary>
        public string ActivityUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the payment year.
        /// Maps to 'Year' column.
        /// </summary>
        public int Year { get; set; }
    }

    /// <summary>
    /// Database entity model for the BisCheck table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class BisCheckEntity
    {
        /// <summary>
        /// Gets or sets the unique identifier for this BIS check.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the activity UID this BIS check belongs to.
        /// Maps to 'ActivityUid' column.
        /// </summary>
        public string ActivityUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the payment year.
        /// Maps to 'Year' column.
        /// </summary>
        public int Year { get; set; }
    }

    /// <summary>
    /// Database entity model for the Notes table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class NoteEntity
    {
        /// <summary>
        /// Gets or sets the unique identifier for this note.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the activity UID this note belongs to.
        /// Maps to 'ActivityUid' column.
        /// </summary>
        public string ActivityUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the note content.
        /// Maps to 'Content' column.
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// Gets or sets the priority level.
        /// Maps to 'Priority' column. 0 = Normal, 1 = Medium, 2 = High.
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// Gets or sets the creation timestamp.
        /// Maps to 'CreatedAt' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string CreatedAt { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the last update timestamp.
        /// Maps to 'UpdatedAt' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string UpdatedAt { get; set; } = string.Empty;
    }

    /// <summary>
    /// Database entity model for the CommercialActivityCodes table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class CommercialActivityCodeEntity
    {
        /// <summary>
        /// Gets or sets the unique identifier for this commercial activity code.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the activity UID this code belongs to.
        /// Maps to 'ActivityUid' column.
        /// </summary>
        public string ActivityUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the activity code.
        /// Maps to 'ActivityCode' column.
        /// </summary>
        public int ActivityCode { get; set; }
    }

    /// <summary>
    /// Database entity model for the ProfessionNames table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// </summary>
    public class ProfessionNameEntity
    {
        /// <summary>
        /// Gets or sets the activity UID this description belongs to.
        /// Maps to 'ActivityUid' column.
        /// </summary>
        public string ActivityUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the activity description.
        /// Maps to 'ActivityDescription' column.
        /// </summary>
        public string? ActivityDescription { get; set; }
    }

    /// <summary>
    /// Database entity model for the CraftActivityCodes table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations.
    /// Enforces one-to-one relationship between activities and craft codes.
    /// </summary>
    public class CraftActivityCodeEntity
    {
        /// <summary>
        /// Gets or sets the unique identifier for this craft activity code.
        /// Maps to 'Uid' column.
        /// </summary>
        public string Uid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the activity UID this craft code belongs to.
        /// Maps to 'ActivityUid' column.
        /// Enforces one-to-one relationship: each activity can have only one craft code.
        /// </summary>
        public string ActivityUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the craft code in XX-XX-XXX format.
        /// Maps to 'CraftCode' column.
        /// References CraftTypeBase.Code for validation.
        /// </summary>
        public string CraftCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Database entity model for the UidSequences table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations and UID generation management.
    /// </summary>
    public class UidSequenceEntity
    {
        /// <summary>
        /// Gets or sets the entity type for which the sequence is used.
        /// Maps to 'EntityType' column.
        /// Valid values: Client, Activity, Note, CommercialActivityCode, FileCheckState, G12Check, BisCheck.
        /// </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the prefix used for UID generation.
        /// Maps to 'Prefix' column.
        /// For clients, this is the first letter of the name.
        /// </summary>
        public string Prefix { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the last sequence number used.
        /// Maps to 'LastSequence' column.
        /// </summary>
        public int LastSequence { get; set; } = 0;

        /// <summary>
        /// Gets or sets the last update timestamp.
        /// Maps to 'UpdatedAt' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string UpdatedAt { get; set; } = string.Empty;
    }

    /// <summary>
    /// Database entity model for the SchemaVersion table.
    /// Maps directly to the database schema with PascalCase column names.
    /// Used for Dapper ORM operations and database migration tracking.
    /// </summary>
    public class SchemaVersionEntity
    {
        /// <summary>
        /// Gets or sets the schema version number.
        /// Maps to 'Version' column.
        /// </summary>
        public int Version { get; set; }

        /// <summary>
        /// Gets or sets the timestamp when the version was applied.
        /// Maps to 'AppliedAt' column. Format: "dd/MM/yyyy HH:mm:ss".
        /// </summary>
        public string AppliedAt { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the schema version.
        /// Maps to 'Description' column.
        /// </summary>
        public string? Description { get; set; }
    }
}