using System;
using System.IO;
using System.Threading.Tasks;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// Client folder management service for UFU2 client management system.
    /// Handles creation and management of client folder structures in the UFU2 application data directory.
    /// Follows UFU2 architectural patterns with comprehensive error handling and Arabic user messages.
    /// </summary>
    public class ClientFolderManagementService : IDisposable
    {
        #region Private Fields

        private bool _disposed = false;
        private readonly string _baseClientsDirectory;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ClientFolderManagementService.
        /// Sets up the base clients directory path in the UFU2 application data folder.
        /// </summary>
        public ClientFolderManagementService()
        {
            try
            {
                // Set base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                _baseClientsDirectory = Path.Combine(appDataPath, "UFU2", "Clients");

                LoggingService.LogDebug($"ClientFolderManagementService initialized with base directory: {_baseClientsDirectory}", "ClientFolderManagementService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing ClientFolderManagementService: {ex.Message}", "ClientFolderManagementService");
                throw;
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Creates only a new activity folder for an existing client.
        /// This method is used when adding activities to existing clients (e.g., from duplicate detection workflow).
        /// It assumes the client folder already exists and only creates the activity folder and its subfolders.
        /// </summary>
        /// <param name="clientUID">The existing client UID (e.g., "D01")</param>
        /// <param name="clientNameFr">The client's French name for folder path resolution</param>
        /// <param name="activityUID">The new activity UID (e.g., "D01_Act2")</param>
        /// <returns>True if activity folder was created successfully, false otherwise</returns>
        public async Task<bool> CreateActivityFolderForExistingClientAsync(string clientUID, string clientNameFr, string activityUID)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogError("Client UID is required for activity folder creation", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast(
                    "معرف العميل مطلوب لإنشاء مجلد النشاط",
                    "خطأ في البيانات"
                );
                return false;
            }

            if (string.IsNullOrWhiteSpace(activityUID))
            {
                LoggingService.LogError("Activity UID is required for activity folder creation", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast(
                    "معرف النشاط مطلوب لإنشاء مجلد النشاط",
                    "خطأ في البيانات"
                );
                return false;
            }

            try
            {
                LoggingService.LogInfo($"Creating activity folder for existing client {clientUID} - Activity: {activityUID}", "ClientFolderManagementService");

                // Ensure base directory exists
                await EnsureBaseDirectoryExistsAsync();

                // Get the existing client folder path
                string clientFolderName = GetClientFolderName(clientUID, clientNameFr);
                string clientFolderPath = Path.Combine(_baseClientsDirectory, clientFolderName);

                // Verify client folder exists
                if (!Directory.Exists(clientFolderPath))
                {
                    LoggingService.LogError($"Client folder does not exist: {clientFolderPath}", "ClientFolderManagementService");
                    ErrorManager.ShowUserErrorToast(
                        $"مجلد العميل غير موجود: {clientUID}\n\nيرجى التحقق من وجود مجلد العميل والمحاولة مرة أخرى.",
                        "مجلد العميل غير موجود"
                    );
                    return false;
                }

                // Create activity folder within existing client folder
                string activityFolderPath = await CreateActivityFolderAsync(clientFolderPath, activityUID);
                if (string.IsNullOrEmpty(activityFolderPath))
                {
                    return false; // Error already logged and shown to user
                }

                // Create document category subfolders
                bool subfoldersCreated = await CreateDocumentSubfoldersAsync(activityFolderPath);
                if (!subfoldersCreated)
                {
                    return false; // Error already logged and shown to user
                }

                LoggingService.LogInfo($"Successfully created activity folder {activityUID} for existing client {clientUID}", "ClientFolderManagementService");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error creating activity folder for existing client {clientUID}: {ex.Message}", "ClientFolderManagementService");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ غير متوقع أثناء إنشاء مجلد النشاط. يرجى المحاولة مرة أخرى.",
                    "خطأ في إنشاء مجلد النشاط",
                    LogLevel.Error,
                    "ClientFolderManagementService");
                return false;
            }
        }

        /// <summary>
        /// Creates the complete folder structure for a new client including client folder, activity folder, and document subfolders.
        /// Ensures base directory exists and handles all folder creation operations with proper error handling.
        /// </summary>
        /// <param name="clientUID">The client UID (e.g., "J01")</param>
        /// <param name="clientNameFr">The client's French name for folder naming</param>
        /// <param name="activityUID">The activity UID for the default activity folder (e.g., "J01_Act1")</param>
        /// <returns>True if folder structure was created successfully, false otherwise</returns>
        public async Task<bool> CreateClientFolderStructureAsync(string clientUID, string clientNameFr, string activityUID)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogError("Client UID is required for folder creation", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast("معرف العميل مطلوب لإنشاء المجلدات", "خطأ في البيانات");
                return false;
            }

            if (string.IsNullOrWhiteSpace(clientNameFr))
            {
                LoggingService.LogError("Client NameFr is required for folder creation", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast("اسم العميل الفرنسي مطلوب لإنشاء المجلدات", "خطأ في البيانات");
                return false;
            }

            if (string.IsNullOrWhiteSpace(activityUID))
            {
                LoggingService.LogError("Activity UID is required for folder creation", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast("معرف النشاط مطلوب لإنشاء المجلدات", "خطأ في البيانات");
                return false;
            }

            try
            {
                LoggingService.LogInfo($"Starting folder structure creation for client {clientUID} - {clientNameFr}", "ClientFolderManagementService");

                // Ensure base directory exists
                await EnsureBaseDirectoryExistsAsync();

                // Create client folder
                string clientFolderPath = await CreateClientFolderAsync(clientUID, clientNameFr);
                if (string.IsNullOrEmpty(clientFolderPath))
                {
                    return false; // Error already logged and shown to user
                }

                // Create activity folder
                string activityFolderPath = await CreateActivityFolderAsync(clientFolderPath, activityUID);
                if (string.IsNullOrEmpty(activityFolderPath))
                {
                    return false; // Error already logged and shown to user
                }

                // Create document category subfolders
                bool subfoldersCreated = await CreateDocumentSubfoldersAsync(activityFolderPath);
                if (!subfoldersCreated)
                {
                    return false; // Error already logged and shown to user
                }

                LoggingService.LogInfo($"Successfully created complete folder structure for client {clientUID}", "ClientFolderManagementService");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error creating folder structure for client {clientUID}: {ex.Message}", "ClientFolderManagementService");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ غير متوقع أثناء إنشاء مجلدات العميل. يرجى المحاولة مرة أخرى.",
                    "خطأ في إنشاء المجلدات",
                    LogLevel.Error,
                    "ClientFolderManagementService");
                return false;
            }
        }

        /// <summary>
        /// Checks if a client folder already exists for the given client UID and name.
        /// </summary>
        /// <param name="clientUID">The client UID</param>
        /// <param name="clientNameFr">The client's French name</param>
        /// <returns>True if the folder exists, false otherwise</returns>
        public async Task<bool> ClientFolderExistsAsync(string clientUID, string clientNameFr)
        {
            try
            {
                await Task.Run(() =>
                {
                    string clientFolderName = GetClientFolderName(clientUID, clientNameFr);
                    string clientFolderPath = Path.Combine(_baseClientsDirectory, clientFolderName);
                    return Directory.Exists(clientFolderPath);
                });

                string clientFolderName = GetClientFolderName(clientUID, clientNameFr);
                string clientFolderPath = Path.Combine(_baseClientsDirectory, clientFolderName);
                bool exists = Directory.Exists(clientFolderPath);

                LoggingService.LogDebug($"Client folder existence check for {clientUID}: {exists}", "ClientFolderManagementService");
                return exists;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking client folder existence for {clientUID}: {ex.Message}", "ClientFolderManagementService");
                return false;
            }
        }

        /// <summary>
        /// Copies a profile image to the client's folder with the proper naming convention.
        /// The image is saved as: {ClientUID} - {NameFr}.{extension}
        /// </summary>
        /// <param name="clientUID">The client UID</param>
        /// <param name="clientNameFr">The client's French name</param>
        /// <param name="profileImage">The profile image to copy</param>
        /// <param name="imageExtension">The image file extension (e.g., "jpg", "png")</param>
        /// <returns>True if the image was copied successfully, false otherwise</returns>
        public async Task<bool> CopyProfileImageToClientFolderAsync(string clientUID, string clientNameFr,
            System.Windows.Media.Imaging.BitmapSource profileImage, string imageExtension)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                LoggingService.LogError("Client UID is required for profile image copying", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast("معرف العميل مطلوب لنسخ الصورة الشخصية", "خطأ في البيانات");
                return false;
            }

            if (string.IsNullOrWhiteSpace(clientNameFr))
            {
                LoggingService.LogError("Client NameFr is required for profile image copying", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast("اسم العميل الفرنسي مطلوب لنسخ الصورة الشخصية", "خطأ في البيانات");
                return false;
            }

            if (profileImage == null)
            {
                LoggingService.LogError("Profile image is required for copying", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast("الصورة الشخصية مطلوبة للنسخ", "خطأ في البيانات");
                return false;
            }

            try
            {
                LoggingService.LogInfo($"Starting profile image copy for client {clientUID} - {clientNameFr}", "ClientFolderManagementService");

                // Get client folder path
                string clientFolderName = GetClientFolderName(clientUID, clientNameFr);
                string clientFolderPath = Path.Combine(_baseClientsDirectory, clientFolderName);

                // Verify client folder exists
                if (!Directory.Exists(clientFolderPath))
                {
                    LoggingService.LogError($"Client folder does not exist: {clientFolderPath}", "ClientFolderManagementService");
                    ErrorManager.ShowUserErrorToast(
                        $"مجلد العميل غير موجود. يرجى التأكد من إنشاء مجلد العميل أولاً.",
                        "مجلد العميل غير موجود"
                    );
                    return false;
                }

                // Generate image filename
                string sanitizedName = SanitizeFileName(clientNameFr);
                string imageFileName = $"{clientUID} - {sanitizedName}.{imageExtension.ToLowerInvariant()}";
                string imageFilePath = Path.Combine(clientFolderPath, imageFileName);

                // Copy image to client folder
                bool copyResult = await CopyImageToFileAsync(profileImage, imageFilePath, imageExtension);

                if (copyResult)
                {
                    LoggingService.LogInfo($"Profile image copied successfully to: {imageFilePath}", "ClientFolderManagementService");
                    return true;
                }
                else
                {
                    LoggingService.LogError($"Failed to copy profile image to: {imageFilePath}", "ClientFolderManagementService");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error copying profile image for client {clientUID}: {ex.Message}", "ClientFolderManagementService");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ غير متوقع أثناء نسخ الصورة الشخصية. يرجى المحاولة مرة أخرى.",
                    "خطأ في نسخ الصورة",
                    LogLevel.Error,
                    "ClientFolderManagementService");
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Ensures the base clients directory exists, creating it if necessary.
        /// </summary>
        private async Task EnsureBaseDirectoryExistsAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    if (!Directory.Exists(_baseClientsDirectory))
                    {
                        Directory.CreateDirectory(_baseClientsDirectory);
                        LoggingService.LogInfo($"Created base clients directory: {_baseClientsDirectory}", "ClientFolderManagementService");
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to create base clients directory {_baseClientsDirectory}: {ex.Message}", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast(
                    $"فشل في إنشاء المجلد الأساسي للعملاء:\n{_baseClientsDirectory}\n\nيرجى التحقق من صلاحيات الكتابة.",
                    "خطأ في إنشاء المجلد"
                );
                throw;
            }
        }

        /// <summary>
        /// Creates the client folder with the naming pattern: {ClientUID} - {NameFr}
        /// </summary>
        /// <param name="clientUID">The client UID</param>
        /// <param name="clientNameFr">The client's French name</param>
        /// <returns>The full path to the created client folder, or empty string if creation failed</returns>
        private async Task<string> CreateClientFolderAsync(string clientUID, string clientNameFr)
        {
            try
            {
                string clientFolderName = GetClientFolderName(clientUID, clientNameFr);
                string clientFolderPath = Path.Combine(_baseClientsDirectory, clientFolderName);

                return await Task.Run(() =>
                {
                    // Check if folder already exists
                    if (Directory.Exists(clientFolderPath))
                    {
                        LoggingService.LogInfo($"Client folder already exists: {clientFolderPath}", "ClientFolderManagementService");
                        return clientFolderPath;
                    }

                    // Create the client folder
                    Directory.CreateDirectory(clientFolderPath);
                    LoggingService.LogInfo($"Created client folder: {clientFolderPath}", "ClientFolderManagementService");
                    return clientFolderPath;
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to create client folder for {clientUID}: {ex.Message}", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast(
                    $"فشل في إنشاء مجلد العميل {clientUID}.\n\nيرجى التحقق من صلاحيات الكتابة والمحاولة مرة أخرى.",
                    "خطأ في إنشاء مجلد العميل"
                );
                return string.Empty;
            }
        }

        /// <summary>
        /// Creates the activity folder within the client folder.
        /// </summary>
        /// <param name="clientFolderPath">The path to the client folder</param>
        /// <param name="activityUID">The activity UID</param>
        /// <returns>The full path to the created activity folder, or empty string if creation failed</returns>
        private async Task<string> CreateActivityFolderAsync(string clientFolderPath, string activityUID)
        {
            try
            {
                string activityFolderPath = Path.Combine(clientFolderPath, activityUID);

                return await Task.Run(() =>
                {
                    // Check if folder already exists
                    if (Directory.Exists(activityFolderPath))
                    {
                        LoggingService.LogInfo($"Activity folder already exists: {activityFolderPath}", "ClientFolderManagementService");
                        return activityFolderPath;
                    }

                    // Create the activity folder
                    Directory.CreateDirectory(activityFolderPath);
                    LoggingService.LogInfo($"Created activity folder: {activityFolderPath}", "ClientFolderManagementService");
                    return activityFolderPath;
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to create activity folder {activityUID}: {ex.Message}", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast(
                    $"فشل في إنشاء مجلد النشاط {activityUID}.\n\nيرجى التحقق من صلاحيات الكتابة والمحاولة مرة أخرى.",
                    "خطأ في إنشاء مجلد النشاط"
                );
                return string.Empty;
            }
        }

        /// <summary>
        /// Creates the four required document category subfolders within the activity folder.
        /// </summary>
        /// <param name="activityFolderPath">The path to the activity folder</param>
        /// <returns>True if all subfolders were created successfully, false otherwise</returns>
        private async Task<bool> CreateDocumentSubfoldersAsync(string activityFolderPath)
        {
            string[] subfolders = { "Documents", "G12", "Bis", "Casnos" };

            try
            {
                return await Task.Run(() =>
                {
                    foreach (string subfolder in subfolders)
                    {
                        string subfolderPath = Path.Combine(activityFolderPath, subfolder);

                        try
                        {
                            // Check if subfolder already exists
                            if (Directory.Exists(subfolderPath))
                            {
                                LoggingService.LogDebug($"Document subfolder already exists: {subfolderPath}", "ClientFolderManagementService");
                                continue;
                            }

                            // Create the subfolder
                            Directory.CreateDirectory(subfolderPath);
                            LoggingService.LogDebug($"Created document subfolder: {subfolderPath}", "ClientFolderManagementService");
                        }
                        catch (Exception subEx)
                        {
                            LoggingService.LogError($"Failed to create document subfolder {subfolder}: {subEx.Message}", "ClientFolderManagementService");
                            ErrorManager.ShowUserErrorToast(
                                $"فشل في إنشاء مجلد الوثائق '{subfolder}'.\n\nيرجى التحقق من صلاحيات الكتابة والمحاولة مرة أخرى.",
                                "خطأ في إنشاء مجلد الوثائق"
                            );
                            return false;
                        }
                    }

                    LoggingService.LogInfo($"Successfully created all document subfolders in: {activityFolderPath}", "ClientFolderManagementService");
                    return true;
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error creating document subfolders: {ex.Message}", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ غير متوقع أثناء إنشاء مجلدات الوثائق. يرجى المحاولة مرة أخرى.",
                    "خطأ في إنشاء مجلدات الوثائق"
                );
                return false;
            }
        }

        /// <summary>
        /// Generates the client folder name using the pattern: {ClientUID} - {NameFr}
        /// </summary>
        /// <param name="clientUID">The client UID</param>
        /// <param name="clientNameFr">The client's French name</param>
        /// <returns>The formatted folder name</returns>
        private static string GetClientFolderName(string clientUID, string clientNameFr)
        {
            // Sanitize the name to remove invalid file system characters
            string sanitizedName = SanitizeFileName(clientNameFr);
            return $"{clientUID} - {sanitizedName}";
        }

        /// <summary>
        /// Sanitizes a file name by removing or replacing invalid characters.
        /// </summary>
        /// <param name="fileName">The file name to sanitize</param>
        /// <returns>The sanitized file name</returns>
        private static string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return "Unknown";

            // Get invalid characters for file names
            char[] invalidChars = Path.GetInvalidFileNameChars();

            // Replace invalid characters with underscores
            foreach (char invalidChar in invalidChars)
            {
                fileName = fileName.Replace(invalidChar, '_');
            }

            // Trim whitespace and limit length
            fileName = fileName.Trim();
            if (fileName.Length > 100) // Reasonable limit for folder names
            {
                fileName = fileName.Substring(0, 100).Trim();
            }

            return string.IsNullOrWhiteSpace(fileName) ? "Unknown" : fileName;
        }

        /// <summary>
        /// Copies a BitmapSource to a file with proper encoding.
        /// Supports both JPEG and PNG formats based on the extension.
        /// </summary>
        /// <param name="image">The image to copy</param>
        /// <param name="filePath">The target file path</param>
        /// <param name="extension">The file extension (determines encoder)</param>
        /// <returns>True if the image was copied successfully</returns>
        private async Task<bool> CopyImageToFileAsync(System.Windows.Media.Imaging.BitmapSource image, string filePath, string extension)
        {
            try
            {
                return await Task.Run(() =>
                {
                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        System.Windows.Media.Imaging.BitmapEncoder encoder;

                        // Choose encoder based on extension
                        string ext = extension.ToLowerInvariant();
                        if (ext == "png")
                        {
                            encoder = new System.Windows.Media.Imaging.PngBitmapEncoder();
                            LoggingService.LogDebug("Using PNG encoder for profile image", "ClientFolderManagementService");
                        }
                        else
                        {
                            // Default to JPEG with high quality
                            var jpegEncoder = new System.Windows.Media.Imaging.JpegBitmapEncoder();
                            jpegEncoder.QualityLevel = 95;
                            encoder = jpegEncoder;
                            LoggingService.LogDebug("Using JPEG encoder with 95% quality for profile image", "ClientFolderManagementService");
                        }

                        encoder.Frames.Add(System.Windows.Media.Imaging.BitmapFrame.Create(image));
                        encoder.Save(fileStream);
                    }

                    // Verify file was created and get size
                    var fileInfo = new FileInfo(filePath);
                    if (fileInfo.Exists)
                    {
                        var fileSizeKB = fileInfo.Length / 1024.0;
                        LoggingService.LogInfo($"Profile image saved successfully - Size: {fileSizeKB:F1} KB, Path: {filePath}", "ClientFolderManagementService");
                        return true;
                    }
                    else
                    {
                        LoggingService.LogError($"Profile image file was not created: {filePath}", "ClientFolderManagementService");
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error copying image to file {filePath}: {ex.Message}", "ClientFolderManagementService");
                ErrorManager.ShowUserErrorToast(
                    $"فشل في حفظ الصورة الشخصية في:\n{filePath}\n\nيرجى التحقق من صلاحيات الكتابة والمساحة المتاحة.",
                    "خطأ في حفظ الصورة"
                );
                return false;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the service resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources if any
                    LoggingService.LogDebug("ClientFolderManagementService disposed", "ClientFolderManagementService");
                }

                _disposed = true;
            }
        }

        #endregion
    }
}
