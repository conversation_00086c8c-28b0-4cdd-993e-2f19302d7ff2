=== UFU2 Application Session Started at 2025-08-10 00:52:16 ===
[2025-08-10 00:52:16.351]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-10 00:52:16.354]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-10 00:52:16.357]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-10 00:52:16.361]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-10 00:52:16.369]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-10 00:52:16.372]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-10 00:52:16.376]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-10 00:52:16.380]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-10 00:52:16.384]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-10 00:52:16.386]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-10 00:52:16.389]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-10 00:52:16.392]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-10 00:52:16.395]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-10 00:52:16.397]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-10 00:52:16.400]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-10 00:52:16.402]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-10 00:52:16.405]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-10 00:52:16.408]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-10 00:52:16.416]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-10 00:52:16.419]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-10 00:52:16.422]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-10 00:52:16.426]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-10 00:52:16.428]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-10 00:52:16.432]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-10 00:52:16.435]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-10 00:52:16.439]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-10 00:52:16.442]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-10 00:52:16.445]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-10 00:52:16.448]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-10 00:52:16.452]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-10 00:52:16.455]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-10 00:52:16.459]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-10 00:52:16.463]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-10 00:52:16.466]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-10 00:52:16.470]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-10 00:52:16.473]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-10 00:52:16.477]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-10 00:52:16.480]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-10 00:52:16.494]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 104.12MB working set
[2025-08-10 00:52:16.498]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-10 00:52:16.501]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-10 00:52:16.504]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-10 00:52:16.507]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-10 00:52:16.511]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-10 00:52:16.517]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-10 00:52:16.540]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-10 00:52:16.545]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-10 00:52:16.548]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-10 00:52:16.779]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-10 00:52:16.782]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-10 00:52:16.785]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-10 00:52:16.788]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-10 00:52:16.794]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638903803367935653 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-10 00:52:16.797]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-10 00:52:16.801]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 00:52:16.804]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-10 00:52:16.814]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-10 00:52:16.820]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-10 00:52:16.823]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-10 00:52:16.827]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-10 00:52:16.831]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-10 00:52:16.835]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-10 00:52:16.838]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-10 00:52:16.840]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-10 00:52:16.844]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-10 00:52:16.848]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-10 00:52:16.852]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-10 00:52:16.855]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-10 00:52:16.859]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-10 00:52:16.862]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-10 00:52:16.865]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-10 00:52:16.868]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-10 00:52:16.872]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-10 00:52:16.877]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-10 00:52:16.880]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-10 00:52:16.883]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-10 00:52:16.886]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-10 00:52:16.889]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-10 00:52:16.893]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-10 00:52:16.897]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-10 00:52:16.900]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-10 00:52:16.903]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-10 00:52:16.906]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-10 00:52:16.911]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-10 00:52:16.914]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-10 00:52:16.917]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-10 00:52:16.921]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-10 00:52:16.924]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-10 00:52:16.928]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-10 00:52:16.932]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-10 00:52:16.935]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-10 00:52:16.938]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-10 00:52:16.944]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-10 00:52:16.947]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-10 00:52:16.951]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-10 00:52:16.955]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-10 00:52:16.959]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-10 00:52:16.962]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-10 00:52:16.965]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-10 00:52:16.968]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-10 00:52:16.971]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-10 00:52:16.975]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-10 00:52:16.978]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-10 00:52:16.981]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-10 00:52:16.985]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-10 00:52:16.989]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-10 00:52:16.993]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-10 00:52:16.997]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-10 00:52:17.000]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-10 00:52:17.004]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-10 00:52:17.011]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-10 00:52:17.014]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-10 00:52:17.018]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-10 00:52:17.022]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-10 00:52:17.030]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-10 00:52:17.034]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-10 00:52:17.040]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-10 00:52:17.046]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-10 00:52:17.050]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 533ms
[2025-08-10 00:52:17.053]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-10 00:52:17.060]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-10 00:52:17.074]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-10 00:52:17.079]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-10 00:52:17.083]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-10 00:52:17.087]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-10 00:52:17.092]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-10 00:52:17.096]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-10 00:52:17.100]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:52:17.104]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-10 00:52:17.110]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:52:17.115]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-10 00:52:17.121]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-10 00:52:17.124]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-10 00:52:17.128]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-10 00:52:17.134]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:52:17.163]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:52:17.165]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:52:17.168]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:52:17.168]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:52:17.166]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:52:17.172]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:52:17.186]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:52:17.179]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:52:17.183]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-10 00:52:17.176]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-10 00:52:17.198]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:52:17.201]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-10 00:52:17.205]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:52:17.212]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:52:17.216]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:52:17.219]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:52:17.223]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:52:17.228]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:52:17.231]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:52:17.240]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-10 00:52:17.244]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 00:52:17.249]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 00:52:17.255]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.UFU2_Schema.sql
[2025-08-10 00:52:17.260]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.UFU2_Schema.sql (19126 characters)
[2025-08-10 00:52:17.263]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 00:52:17.268]  	[DEBUG]		[DatabaseMigrationService]	Executing 55 SQL statements
[2025-08-10 00:52:17.273]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/55 statements
[2025-08-10 00:52:17.278]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/55 statements
[2025-08-10 00:52:17.282]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/55 statements
[2025-08-10 00:52:17.286]  	[DEBUG]		[DatabaseMigrationService]	Executed 40/55 statements
[2025-08-10 00:52:17.292]  	[DEBUG]		[DatabaseMigrationService]	Executed 50/55 statements
[2025-08-10 00:52:17.317]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-10 00:52:17.339]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (55 statements executed)
[2025-08-10 00:52:17.344]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 00:52:17.350]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:52:17.354]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:17.358]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 00:52:17.363]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 00:52:17.367]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 00:52:17.370]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 00:52:17.374]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 00:52:17.378]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 00:52:17.382]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 00:52:17.385]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 00:52:17.389]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 00:52:17.394]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 00:52:17.397]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 00:52:17.402]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 00:52:17.406]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 00:52:17.410]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 00:52:17.414]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 00:52:17.418]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 00:52:17.421]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 00:52:17.425]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 00:52:17.429]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 00:52:17.433]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 00:52:17.436]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 00:52:17.444]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:52:17.448]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:52:17.453]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 00:52:17.457]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 00:52:17.462]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 00:52:17.465]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 00:52:17.469]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:52:17.474]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:52:17.479]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:17.482]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-10 00:52:17.486]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:52:17.490]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:17.494]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:52:17.498]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:52:17.501]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:52:17.505]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:52:17.509]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:52:17.512]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:52:17.516]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:52:17.519]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:52:17.525]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-10 00:52:17.529]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 00:52:17.533]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 00:52:17.536]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.APP_Schema.sql
[2025-08-10 00:52:17.540]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.APP_Schema.sql (9406 characters)
[2025-08-10 00:52:17.545]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 00:52:17.549]  	[DEBUG]		[DatabaseMigrationService]	Executing 30 SQL statements
[2025-08-10 00:52:17.553]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/30 statements
[2025-08-10 00:52:17.560]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/30 statements
[2025-08-10 00:52:17.565]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/30 statements
[2025-08-10 00:52:17.570]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-10 00:52:17.585]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (30 statements executed)
[2025-08-10 00:52:17.589]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 00:52:17.593]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:52:17.597]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:17.600]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-10 00:52:17.605]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-10 00:52:17.609]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-10 00:52:17.613]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-10 00:52:17.617]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-10 00:52:17.621]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-10 00:52:17.625]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-10 00:52:17.629]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-10 00:52:17.633]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-10 00:52:17.637]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-10 00:52:17.641]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-10 00:52:17.645]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:52:17.649]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:52:17.652]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-10 00:52:17.657]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-10 00:52:17.661]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-10 00:52:17.665]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-10 00:52:17.669]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:52:17.673]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:52:17.679]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:17.684]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-10 00:52:17.689]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-10 00:52:17.696]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:17.702]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-10 00:52:17.711]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-10 00:52:17.717]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-10 00:52:17.724]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-10 00:52:17.734]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-10 00:52:17.742]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-10 00:52:17.748]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-10 00:52:17.752]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-10 00:52:17.757]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-10 00:52:17.763]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-10 00:52:17.767]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-10 00:52:17.771]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.Archive_Schema.sql
[2025-08-10 00:52:17.776]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.Archive_Schema.sql (7159 characters)
[2025-08-10 00:52:17.780]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-10 00:52:17.784]  	[DEBUG]		[DatabaseMigrationService]	Executing 16 SQL statements
[2025-08-10 00:52:17.789]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/16 statements
[2025-08-10 00:52:17.795]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-10 00:52:17.810]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (16 statements executed)
[2025-08-10 00:52:17.814]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-10 00:52:17.818]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:52:17.822]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:17.827]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-10 00:52:17.831]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-10 00:52:17.835]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-10 00:52:17.839]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-10 00:52:17.844]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-10 00:52:17.848]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-10 00:52:17.853]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-10 00:52:17.857]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-10 00:52:17.861]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-10 00:52:17.865]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:52:17.869]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:52:17.873]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-10 00:52:17.879]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-10 00:52:17.883]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-10 00:52:17.887]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-10 00:52:17.891]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:52:17.895]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-10 00:52:17.899]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:17.902]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-10 00:52:17.906]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:17.911]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-10 00:52:17.915]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-10 00:52:17.919]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-10 00:52:17.923]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-10 00:52:17.928]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-10 00:52:17.931]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-10 00:52:17.935]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-10 00:52:17.939]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-10 00:52:17.944]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-10 00:52:17.948]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-10 00:52:17.953]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-10 00:52:17.957]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-10 00:52:17.962]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-10 00:52:17.966]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-10 00:52:17.971]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-10 00:52:17.976]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-10 00:52:17.980]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-10 00:52:17.985]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-10 00:52:17.989]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-10 00:52:17.994]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-10 00:52:17.998]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-10 00:52:18.002]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-10 00:52:18.006]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-10 00:52:18.011]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-10 00:52:18.015]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-10 00:52:18.019]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-10 00:52:18.023]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-10 00:52:18.027]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-10 00:52:18.032]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-10 00:52:18.035]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-10 00:52:18.039]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-10 00:52:18.044]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-10 00:52:18.049]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-10 00:52:18.053]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-10 00:52:18.057]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-10 00:52:18.061]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-10 00:52:18.066]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-10 00:52:18.070]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-10 00:52:18.074]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-10 00:52:18.078]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-10 00:52:18.083]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-10 00:52:18.087]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-10 00:52:18.091]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-10 00:52:18.096]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-10 00:52:18.100]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-10 00:52:18.104]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-10 00:52:18.110]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-10 00:52:18.114]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-10 00:52:18.118]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-10 00:52:18.123]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-10 00:52:18.128]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:18.133]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-10 00:52:18.137]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-10 00:52:18.142]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-10 00:52:18.146]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-10 00:52:18.151]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-10 00:52:18.154]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-10 00:52:18.159]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-10 00:52:18.163]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-10 00:52:18.167]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-10 00:52:18.171]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-10 00:52:18.176]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-10 00:52:18.180]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-10 00:52:18.184]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-10 00:52:18.189]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:18.195]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-10 00:52:18.199]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:18.204]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:18.209]  	[INFO]		[ServiceLocator]	Seeding activity type data from embedded resource
[2025-08-10 00:52:18.214]  	[DEBUG]		[ActivityTypeBaseService]	Starting import from embedded JSON resource
[2025-08-10 00:52:18.303]  	[INFO]		[ActivityTypeBaseService]	Parsed 1028 activity types from JSON
[2025-08-10 00:52:18.307]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:18.322]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 1/11 (100 records)
[2025-08-10 00:52:18.337]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 2/11 (100 records)
[2025-08-10 00:52:18.353]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 3/11 (100 records)
[2025-08-10 00:52:18.368]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 4/11 (100 records)
[2025-08-10 00:52:18.383]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 5/11 (100 records)
[2025-08-10 00:52:18.399]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 6/11 (100 records)
[2025-08-10 00:52:18.414]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 7/11 (100 records)
[2025-08-10 00:52:18.432]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 8/11 (100 records)
[2025-08-10 00:52:18.447]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 9/11 (100 records)
[2025-08-10 00:52:18.464]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 10/11 (100 records)
[2025-08-10 00:52:18.474]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 11/11 (28 records)
[2025-08-10 00:52:18.478]  	[INFO]		[ActivityTypeBaseService]	Successfully imported 1028 activity types
[2025-08-10 00:52:18.483]  	[INFO]		[ActivityTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-10 00:52:18.492]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-10 00:52:18.645]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 00:52:18.702]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:52:18.706]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-10 00:52:18.712]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-10 00:52:18.754]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-10 00:52:18.764]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:52:18.778]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-10 00:52:18.789]  	[INFO]		[ServiceLocator]	Successfully imported 1028 activity types
[2025-08-10 00:52:18.802]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:18.810]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-10 00:52:18.816]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:18.824]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:18.830]  	[INFO]		[ServiceLocator]	Seeding craft type data from embedded resource
[2025-08-10 00:52:18.842]  	[DEBUG]		[CraftTypeBaseService]	Starting import from embedded JSON resource
[2025-08-10 00:52:18.879]  	[INFO]		[CraftTypeBaseService]	Found 337 craft types to import
[2025-08-10 00:52:18.883]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:18.952]  	[INFO]		[CraftTypeBaseService]	Successfully imported 337 craft types
[2025-08-10 00:52:18.956]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:18.960]  	[INFO]		[CraftTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-10 00:52:18.965]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-10 00:52:18.974]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-10 00:52:18.981]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:52:18.985]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-10 00:52:18.989]  	[INFO]		[ServiceLocator]	Successfully imported 337 craft types
[2025-08-10 00:52:18.994]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-10 00:52:18.999]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-10 00:52:19.032]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-10 00:52:19.038]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:19.062]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 00:52:19.067]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-10 00:52:19.072]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:19.087]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 00:52:19.093]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-10 00:52:19.097]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-10 00:52:19.101]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-10 00:52:19.105]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 00:52:19.109]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-10 00:52:19.123]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-10 00:52:19.131]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-10 00:52:19.135]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-10 00:52:19.139]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-10 00:52:19.145]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-10 00:52:19.151]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:19.157]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-10 00:52:19.167]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.00 MB MB size
[2025-08-10 00:52:19.171]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-10 00:52:19.176]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-10 00:52:19.182]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-10 00:52:19.186]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-10 00:52:19.190]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-10 00:52:19.194]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-10 00:52:19.198]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-10 00:52:19.202]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-10 00:52:19.206]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-10 00:52:19.211]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-10 00:52:19.215]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-10 00:52:19.219]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-10 00:52:19.223]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-10 00:52:19.228]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-10 00:52:19.232]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-10 00:52:19.236]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-10 00:52:19.242]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-10 00:52:19.248]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-10 00:52:19.254]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.279]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-10 00:52:19.283]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.300]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.309]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-10 00:52:19.313]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.317]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.322]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-10 00:52:19.327]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.331]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.335]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-10 00:52:19.339]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.344]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.348]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-10 00:52:19.352]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.356]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.361]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-10 00:52:19.365]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.369]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.373]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-10 00:52:19.378]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.382]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 0
[2025-08-10 00:52:19.387]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 138ms
[2025-08-10 00:52:19.392]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-10 00:52:19.403]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-10 00:52:19.408]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 17ms
[2025-08-10 00:52:19.413]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-10 00:52:19.419]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-10 00:52:19.423]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 10ms
[2025-08-10 00:52:19.428]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-10 00:52:19.434]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:19.439]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-10 00:52:19.445]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:19.450]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-10 00:52:19.455]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:19.460]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-10 00:52:19.465]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-10 00:52:19.469]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-10 00:52:19.473]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-10 00:52:19.478]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 50ms
[2025-08-10 00:52:19.482]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-10 00:52:19.488]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.494]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-10 00:52:19.498]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.503]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.508]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-10 00:52:19.512]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.517]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.521]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-10 00:52:19.526]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.530]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.534]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-10 00:52:19.538]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.542]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.547]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-10 00:52:19.551]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.555]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.560]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-10 00:52:19.564]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.568]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.572]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-10 00:52:19.577]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.582]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.588]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-10 00:52:19.592]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.597]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.601]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-10 00:52:19.605]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.610]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-10 00:52:19.614]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-10 00:52:19.618]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-10 00:52:19.622]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-10 00:52:19.626]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 145ms
[2025-08-10 00:52:19.631]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-10 00:52:19.635]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-10 00:52:19.639]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-10 00:52:19.643]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-10 00:52:19.647]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-10 00:52:19.651]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-10 00:52:19.655]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-10 00:52:19.659]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-10 00:52:19.663]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-10 00:52:19.667]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-10 00:52:19.671]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-10 00:52:19.676]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-10 00:52:19.680]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-10 00:52:19.683]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-10 00:52:19.687]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-10 00:52:19.692]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-10 00:52:19.696]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-10 00:52:19.702]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 909%
[2025-08-10 00:52:19.706]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 1,818%
[2025-08-10 00:52:19.711]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 2,727%
[2025-08-10 00:52:19.715]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 3,636%
[2025-08-10 00:52:19.719]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 4,545%
[2025-08-10 00:52:19.723]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 5,455%
[2025-08-10 00:52:19.727]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 6,364%
[2025-08-10 00:52:19.731]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 7,273%
[2025-08-10 00:52:19.735]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 8,182%
[2025-08-10 00:52:19.739]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 9,091%
[2025-08-10 00:52:19.744]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 10,000%
[2025-08-10 00:52:19.748]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 0%
[2025-08-10 00:52:19.752]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 00:52:19.756]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 00:52:19.760]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 00:52:19.764]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-10 00:52:19.768]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 00:52:19.773]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 00:52:19.778]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-10 00:52:19.782]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 00:52:19.844]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 00:52:19.849]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-10 00:52:19.855]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 00:52:19.864]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 00:52:19.876]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 00:52:19.894]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-10 00:52:19.912]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 00:52:19.928]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 00:52:19.936]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-10 00:52:19.940]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 00:52:19.946]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 00:52:19.952]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-10 00:52:19.958]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 00:52:19.963]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 00:52:19.968]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 00:52:19.972]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-10 00:52:19.977]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 00:52:19.981]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 00:52:19.985]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-10 00:52:19.989]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 00:52:19.994]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 00:52:19.998]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 00:52:20.002]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-10 00:52:20.006]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 00:52:20.011]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 00:52:20.015]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-10 00:52:20.019]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 00:52:20.023]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 00:52:20.027]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-10 00:52:20.027]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-10 00:52:20.032]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-10 00:52:20.035]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 00:52:20.038]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-10 00:52:20.043]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 00:52:20.053]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 00:52:20.057]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-10 00:52:20.062]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 00:52:20.065]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 00:52:20.069]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-10 00:52:20.073]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 00:52:20.078]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 00:52:20.081]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-10 00:52:20.086]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 00:52:20.089]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 00:52:20.094]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 00:52:20.098]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-10 00:52:20.101]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 00:52:20.106]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 00:52:20.107]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-10 00:52:20.110]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-10 00:52:20.114]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-10 00:52:20.117]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 00:52:20.121]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 14ms
[2025-08-10 00:52:20.125]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 00:52:20.132]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-10 00:52:20.136]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 00:52:20.141]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 00:52:20.145]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 00:52:20.149]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-10 00:52:20.152]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 00:52:20.156]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 00:52:20.161]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-10 00:52:20.164]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 00:52:20.168]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 00:52:20.172]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 00:52:20.176]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-10 00:52:20.180]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 00:52:20.184]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 00:52:20.187]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-10 00:52:20.191]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 00:52:20.195]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 00:52:20.199]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-10 00:52:20.203]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 00:52:20.206]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 00:52:20.211]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 00:52:20.215]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-10 00:52:20.219]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 00:52:20.222]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 00:52:20.227]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-10 00:52:20.230]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 00:52:20.234]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 00:52:20.238]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-10 00:52:20.242]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 00:52:20.246]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 00:52:20.250]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 00:52:20.254]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-10 00:52:20.258]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 00:52:20.262]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 00:52:20.266]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-10 00:52:20.270]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 00:52:20.274]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 00:52:20.279]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 00:52:20.283]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-10 00:52:20.287]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 00:52:20.291]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 00:52:20.295]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-10 00:52:20.299]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 00:52:20.303]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 00:52:20.307]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-10 00:52:20.312]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 00:52:20.316]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 00:52:20.320]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 00:52:20.324]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-10 00:52:20.329]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 00:52:20.333]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 00:52:20.337]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-10 00:52:20.340]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 00:52:20.346]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 00:52:20.350]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-10 00:52:20.354]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 00:52:20.359]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 00:52:20.363]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 00:52:20.367]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-10 00:52:20.370]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 00:52:20.375]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 00:52:20.379]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-10 00:52:20.383]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 00:52:20.387]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 00:52:20.391]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 00:52:20.396]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-10 00:52:20.399]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 00:52:20.403]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 00:52:20.408]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-10 00:52:20.412]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 00:52:20.416]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 00:52:20.421]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-10 00:52:20.426]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 00:52:20.430]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 00:52:20.434]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 00:52:20.437]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-10 00:52:20.442]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 00:52:20.446]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 00:52:20.450]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-10 00:52:20.454]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 00:52:20.458]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 00:52:20.462]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-10 00:52:20.466]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 00:52:20.470]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 00:52:20.474]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 00:52:20.479]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-10 00:52:20.483]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 00:52:20.487]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 00:52:20.491]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-10 00:52:20.496]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 00:52:20.500]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 00:52:20.504]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-10 00:52:20.508]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 00:52:20.512]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 00:52:20.516]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 00:52:20.521]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-10 00:52:20.526]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 00:52:20.531]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 00:52:20.535]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-10 00:52:20.539]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 00:52:20.544]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 00:52:20.548]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 00:52:20.553]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-10 00:52:20.558]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 00:52:20.566]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 00:52:20.571]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-10 00:52:20.576]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 00:52:20.579]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 00:52:20.584]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-10 00:52:20.588]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 00:52:20.593]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 00:52:20.597]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 00:52:20.601]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-10 00:52:20.605]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 00:52:20.610]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 00:52:20.615]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-10 00:52:20.619]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 00:52:20.623]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 00:52:20.628]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-10 00:52:20.632]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 00:52:20.637]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 00:52:20.641]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 00:52:20.649]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-10 00:52:20.653]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 00:52:20.657]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 00:52:20.662]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-10 00:52:20.666]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 00:52:20.670]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 00:52:20.673]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 00:52:20.679]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-10 00:52:20.683]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 00:52:20.687]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 00:52:20.690]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-10 00:52:20.696]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 00:52:20.699]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 00:52:20.703]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-10 00:52:20.707]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 00:52:20.712]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 00:52:20.716]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 00:52:20.719]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-10 00:52:20.723]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 00:52:20.728]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 00:52:20.732]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-10 00:52:20.736]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 00:52:20.740]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 00:52:20.745]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-10 00:52:20.749]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 00:52:20.752]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 00:52:20.756]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 00:52:20.761]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-10 00:52:20.764]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 00:52:20.768]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 00:52:20.773]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-10 00:52:20.777]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 00:52:20.781]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 00:52:20.785]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-10 00:52:20.789]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 00:52:20.794]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 00:52:20.797]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 00:52:20.801]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-10 00:52:20.805]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 00:52:20.810]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 00:52:20.813]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-10 00:52:20.817]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 00:52:20.821]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 00:52:20.826]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 00:52:20.830]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-10 00:52:20.834]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 00:52:20.838]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 00:52:20.843]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-10 00:52:20.847]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 00:52:20.850]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 00:52:20.856]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-10 00:52:20.861]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 00:52:20.864]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 00:52:20.868]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 00:52:20.872]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-10 00:52:20.877]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 00:52:20.881]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 00:52:20.885]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-10 00:52:20.889]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 00:52:20.893]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 00:52:20.897]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-10 00:52:20.901]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 00:52:20.904]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 00:52:20.909]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 00:52:20.913]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-10 00:52:20.917]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 00:52:20.921]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 00:52:20.926]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-10 00:52:20.930]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 00:52:20.933]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 00:52:20.937]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 00:52:20.941]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-10 00:52:20.947]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 00:52:20.953]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 00:52:20.962]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-10 00:52:20.974]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 00:52:20.985]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 00:52:21.007]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-10 00:52:21.025]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 00:52:21.030]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 00:52:21.035]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 00:52:21.039]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-10 00:52:21.044]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 00:52:21.047]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 00:52:21.051]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-10 00:52:21.054]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 00:52:21.059]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 00:52:21.063]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-10 00:52:21.066]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 00:52:21.070]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 00:52:21.074]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 00:52:21.078]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-10 00:52:21.081]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 00:52:21.085]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 00:52:21.089]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-10 00:52:21.093]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 00:52:21.096]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 00:52:21.100]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 00:52:21.104]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-10 00:52:21.107]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 00:52:21.111]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 00:52:21.115]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-10 00:52:21.118]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 00:52:21.122]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 00:52:21.126]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-10 00:52:21.130]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 00:52:21.133]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 00:52:21.138]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 00:52:21.142]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-10 00:52:21.146]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 00:52:21.150]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 00:52:21.156]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-10 00:52:21.161]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 00:52:21.165]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 00:52:21.170]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-10 00:52:21.174]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 00:52:21.179]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 00:52:21.184]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 00:52:21.188]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-10 00:52:21.194]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 00:52:21.200]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 00:52:21.204]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-10 00:52:21.210]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 00:52:21.215]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 00:52:21.218]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-10 00:52:21.222]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 00:52:21.227]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 00:52:21.231]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 00:52:21.235]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-10 00:52:21.239]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 00:52:21.243]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 00:52:21.247]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-10 00:52:21.251]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 00:52:21.255]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 00:52:21.259]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 00:52:21.263]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-10 00:52:21.267]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 00:52:21.271]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 00:52:21.276]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-10 00:52:21.280]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 00:52:21.284]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 00:52:21.288]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-10 00:52:21.293]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 00:52:21.296]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 00:52:21.300]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 00:52:21.304]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-10 00:52:21.308]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 00:52:21.313]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 00:52:21.316]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-10 00:52:21.320]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 00:52:21.326]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 00:52:21.330]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-10 00:52:21.335]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 00:52:21.340]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 00:52:21.346]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 00:52:21.351]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-10 00:52:21.358]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-10 00:52:21.364]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-10 00:52:21.477]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-10 00:52:21.481]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-10 00:52:21.489]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-10 00:52:21.496]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-10 00:52:21.505]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:52:21.513]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:21.519]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:52:21.526]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:21.534]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-10 00:52:21.538]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:52:21.612]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-10 00:52:21.617]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-10 00:52:21.780]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-10 00:52:21.868]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-10 00:52:21.881]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:21.886]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:21.894]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:21.901]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:21.907]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:21.917]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:52:21.960]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-10 00:52:22.116]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-10 00:52:23.086]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 00:52:23.101]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:23.104]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:23.110]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:23.113]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:23.117]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:23.121]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:52:23.361]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 30.0% increase in response time
[2025-08-10 00:52:23.863]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 80.0% increase in response time
[2025-08-10 00:52:23.880]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 5234.134ms
[2025-08-10 00:52:23.885]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-10 00:52:31.895]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:52:32.401]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:52:32.905]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:52:38.337]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 00:52:38.365]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:38.372]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:38.381]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:38.386]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:38.390]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:38.395]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:52:38.899]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 562.0942ms
[2025-08-10 00:52:38.903]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-10 00:52:39.260]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 00:52:39.295]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:39.299]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:39.302]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:39.306]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:39.311]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:39.315]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:52:39.371]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 00:52:39.424]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:39.434]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:39.440]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:39.449]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:39.472]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:39.489]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:52:40.381]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.386]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:40.389]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.394]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:40.398]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.402]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:52:40.447]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.451]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:40.455]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.460]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:40.465]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing MinimizeCommand
[2025-08-10 00:52:40.469]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.472]  	[DEBUG]		[CustomWindowChromeViewModel]	Current window state before minimize: Normal (Title: UFU Client Management)
[2025-08-10 00:52:40.478]  	[DEBUG]		[WindowStateManager]	Minimizing window: UFU Client Management (Current state: Normal)
[2025-08-10 00:52:40.490]  	[DEBUG]		[WindowChromeService]	Window state changed to: Minimized for window: UFU Client Management
[2025-08-10 00:52:40.499]  	[DEBUG]		[MainWindow]	MainWindow state changed to: Minimized, synchronizing ViewModel
[2025-08-10 00:52:40.503]  	[DEBUG]		[CustomWindowChromeViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-10 00:52:40.511]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 1 High priority property notifications
[2025-08-10 00:52:40.516]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 00:52:40.530]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state changed to: Minimized
[2025-08-10 00:52:40.537]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-10 00:52:40.545]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Minimized
[2025-08-10 00:52:40.550]  	[DEBUG]		[MainWindow]	ViewModel synchronized with window state: Minimized
[2025-08-10 00:52:40.567]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-10 00:52:40.614]  	[DEBUG]		[WindowStateManager]	Window minimized successfully: UFU Client Management (Normal -> Minimized)
[2025-08-10 00:52:40.622]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-10 00:52:40.632]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Minimized
[2025-08-10 00:52:40.637]  	[DEBUG]		[CustomWindowChromeViewModel]	MinimizeCommand executed successfully for window: UFU Client Management
[2025-08-10 00:52:40.743]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.748]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:40.752]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.757]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:52:40.766]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:52:40.771]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:52:40.990]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 487.1667ms
[2025-08-10 00:52:40.997]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-10 00:52:41.925]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: CurrentWindowState(1)
[2025-08-10 00:52:54.362]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:52:54.865]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:53:19.239]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 239.0MB, System: 30.0%, Pressure: Normal
[2025-08-10 00:53:25.387]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:53:25.881]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:53:55.940]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:53:56.448]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-10 00:54:16.492]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 239.03MB working set
[2025-08-10 00:54:19.225]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 239.0MB, System: 30.0%, Pressure: Normal
[2025-08-10 00:54:19.226]  	[DEBUG]		[CacheMonitoringService]	Performing cache monitoring
[2025-08-10 00:54:19.240]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-10 00:54:19.250]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CpiLocationService: Needs Attention
[2025-08-10 00:54:19.257]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for ValidationService: Hit ratio: 0.0%
[2025-08-10 00:54:19.268]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ValidationService: Low hit ratio: 0.0%
[2025-08-10 00:54:19.279]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ActivityTypeBaseService: Error: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-10 00:54:19.283]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for CraftTypeBaseService: Hit ratio: 0.0%
[2025-08-10 00:54:19.287]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CraftTypeBaseService: Poor Performance
[2025-08-10 00:54:19.291]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for FileCheckBusinessRuleService: Hit ratio: 0.0%
[2025-08-10 00:54:19.294]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for FileCheckBusinessRuleService: Low hit ratio: 0.0%
[2025-08-10 00:54:19.300]  	[DEBUG]		[CacheMonitoringService]	No valid health info with positive hit ratio found
[2025-08-10 00:54:19.304]  	[INFO]		[CacheMonitoringService]	Cache monitoring summary - Services: 5, Unhealthy: 5, Total memory: 0.0MB, Avg hit ratio: 0.0%
[2025-08-10 00:54:19.308]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for Global: Average hit ratio (0.0%) below threshold (40.0%)
[2025-08-10 00:54:54.151]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-10 00:54:54.162]  	[DEBUG]		[WindowChromeService]	Window state changed to: Normal for window: UFU Client Management
[2025-08-10 00:54:54.167]  	[DEBUG]		[WindowChromeService]	Window restored to normal, border adjustments handled by template
[2025-08-10 00:54:54.173]  	[DEBUG]		[MainWindow]	MainWindow state changed to: Normal, synchronizing ViewModel
[2025-08-10 00:54:54.176]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state changed to: Normal
[2025-08-10 00:54:54.180]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-10 00:54:54.184]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-10 00:54:54.188]  	[DEBUG]		[MainWindow]	ViewModel synchronized with window state: Normal
[2025-08-10 00:54:54.217]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:54.222]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:54:54.226]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:54.229]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:54:54.234]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:54.238]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:54:54.248]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 1 Normal priority property notifications
[2025-08-10 00:54:55.032]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.037]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:54:55.041]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.045]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:54:55.048]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.052]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:54:55.099]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 922.7951ms
[2025-08-10 00:54:55.104]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-10 00:54:55.122]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.126]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:54:55.131]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.134]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:54:55.139]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-10 00:54:55.143]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.147]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-10 00:54:55.152]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-10 00:54:55.156]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-10 00:54:55.160]  	[INFO]		[MainWindow]	Application closing
[2025-08-10 00:54:55.165]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_19562430_638903804951650719 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-10 00:54:55.169]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-10 00:54:55.173]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-10 00:54:55.177]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 00:54:55.181]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-10 00:54:55.185]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-10 00:54:55.196]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-10 00:54:55.201]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-10 00:54:55.205]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-10 00:54:55.261]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-10 00:54:55.283]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-10 00:54:55.303]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.308]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:54:55.312]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.316]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:54:55.320]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:55.324]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:54:56.171]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 985.6909ms
[2025-08-10 00:54:56.176]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-10 00:54:56.472]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:56.477]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:54:56.481]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:56.485]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-10 00:54:56.490]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-10 00:54:56.493]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-10 00:54:56.534]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-10 00:54:56.539]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-10 00:54:56.544]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-10 00:54:56.551]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-10 00:54:56.555]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:54:56.559]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-10 00:54:56.563]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-10 00:54:56.566]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:54:56.571]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:54:56.575]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:54:56.578]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_19562430_638903804951650719
[2025-08-10 00:54:56.582]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:54:56.586]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-10 00:54:56.590]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-10 00:54:56.594]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:54:56.598]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:54:56.602]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-10 00:54:56.605]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-10 00:54:56.616]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-10 00:54:56.621]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-10 00:54:56.625]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-10 00:54:56.629]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-10 00:54:56.633]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 00:54:56.637]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-10 00:54:56.641]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:54:56.644]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-10 00:54:56.648]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-10 00:54:56.652]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:54:56.656]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:54:56.659]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:54:56.663]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_28090709_638903803367935653
[2025-08-10 00:54:56.667]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-10 00:54:56.671]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-10 00:54:56.675]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-10 00:54:56.680]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-10 00:54:56.684]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:54:56.688]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-10 00:54:56.692]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-10 00:54:56.696]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-10 00:54:56.701]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-10 00:54:56.704]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-10 00:54:56.709]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-10 00:54:56.713]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-10 00:54:56.722]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-10 00:54:56.726]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-10 00:54:56.730]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-10 00:54:56.734]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-10 00:54:56.783]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-10 00:54:56.798]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-10 00:54:56.806]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-10 00:54:56.811]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-10 00:54:56.815]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-10 00:54:56.823]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-10 00:54:56.828]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:54:56.832]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:54:56.839]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:54:56.843]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:54:56.847]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-10 00:54:56.855]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-10 00:54:56.859]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-10 00:54:56.863]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-10 00:54:56.868]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-10 00:54:56.874]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-10 00:54:56.879]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-10 00:54:56.883]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-10 00:54:56.889]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-10 00:54:56.894]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-10 00:54:56.898]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-10 00:54:56.902]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-10 00:54:56.908]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 0.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-10 00:54:56.912]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-10 00:54:56.916]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-10 00:54:56.921]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-10 00:54:56.925]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 50.0%
[2025-08-10 00:54:56.929]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-10 00:54:56.934]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-10 00:54:56.938]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-10 00:54:56.943]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-10 00:54:56.947]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-10 00:54:56.951]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-10 00:54:56.956]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-10 00:54:56.959]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-10 00:54:56.963]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-10 00:54:56.967]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-10 00:54:56.971]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-10 00:54:56.975]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-10 00:54:56.979]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-10 00:54:56.985]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-10 00:54:57.004]  	[INFO]		[ResourceManager]	Generated memory leak report: 0 alive resources, 0 dead resources
[2025-08-10 00:54:57.016]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 2
[2025-08-10 00:54:57.021]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 2 potential leaks detected
[2025-08-10 00:54:57.026]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 2 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-10 00:54:57.030]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-10 00:54:57.034]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-10 00:54:57.040]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-10 00:54:57.045]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-10 00:54:57.049]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-10 00:54:57.054]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-10 00:54:57.059]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-10 00:54:57.063]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 2 tracked, 2 disposed, 1 cleanups
[2025-08-10 00:54:57.068]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-10 00:54:57.081]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-10 00:54:57.091]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-10 00:54:57.098]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-10 00:54:57.111]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-10 00:54:57.124]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-10 00:54:57.144]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-10 00:54:57.149]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-10 00:54:57.156]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-10 00:54:57.161]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-10 00:54:57.165]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-10 00:54:57.168]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-10 00:54:57.176]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-10 00:54:57.180]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-10 00:54:57.184]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-10 00:54:57.188]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-10 00:54:57.192]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-10 00:54:57.196]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-10 00:54:57.200]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-10 00:54:57.204]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-10 00:54:57 ===
