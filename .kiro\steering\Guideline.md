---
inclusion: always
---

# UFU2 Development Guidelines (SPARC Methodology)

This document provides a structured process for developers working on the UFU2 application, based on the SPARC (Specification, Pseudocode, Architecture, Refinement, Completion) methodology. Following these guidelines ensures high-quality, maintainable code that integrates smoothly with the existing codebase.

## 1. Pre-Task Analysis (Always Apply)

Before adding new code or creating new files:

1.  **Analyze Existing Codebase:**
    *   Use `codebase-retrieval` to understand relevant existing implementations.
    *   Search for similar functionality, duplicate classes, or conflicting implementations.
2.  **Identify Obsolete/Duplicate Code:**
    *   Look for old files, unused methods, or deprecated implementations that should be removed or refactored.
3.  **Check for Naming Conflicts:**
    *   Ensure new identifiers don't conflict with existing ones.
4.  **Remove Before Adding:**
    *   Delete or refactor conflicting/obsolete code **BEFORE** implementing new functionality.
5.  **Verify Integration:**
    *   Ensure your new code integrates cleanly with the existing architecture without breaking existing functionality.
6.  **Avoid Generic Naming:**
    *   Do not use generic prefixes like "Optimized", "Performance", "Enhanced". Use descriptive, domain-specific names.
7.  **Update Codebase Analysis:**
    *   If your changes significantly impact the application's architecture, core services, data models, or major workflows, **you MUST update `Documentation/UFU2-Codebase-Analysis.md`** to accurately reflect these changes. This ensures the documentation remains a reliable source for understanding the system.

## 2. SPARC Development Workflow

Follow these steps for every development task:

### ⚡️ Specification (Specification Writer)

*   Clearly define the objective, scope, functional requirements, edge cases, and constraints.
*   Translate requirements into modular components.
*   **Never** hard-code environment variables or secrets.
*   Ensure specifications are clear and concise.

### 📋 Pseudocode (Specification Writer / Auto-Coder)

*   Create high-level pseudocode logic for the task.
*   Include Test-Driven Development (TDD) anchors by outlining expected test scenarios.

### 🏗️ Architecture (Architect)

*   Design the solution's structure, defining responsibilities for services, components, and ViewModels.
*   Ensure the design is modular, scalable, and secure.
*   Create necessary architecture diagrams or data flows if complex.
*   Align the design with existing UFU2 patterns (MVVM, ServiceLocator).

### 🧠 Auto-Coding (Auto-Coder)

*   Write clean, efficient, modular code based on the specification and architecture.
*   Use `str-replace-editor` for all code modifications.
*   Adhere strictly to the `CodeQuality.md` standards.
*   **Break large components into smaller, focused files:**
    *   **Small/Very Focused Classes:** 50-150 lines (e.g., simple Models, small Helper classes, EventArgs).
    *   **Standard Classes:** 150-500 lines (e.g., moderately complex ViewModels, Services, Converters). This is the target range.
    *   **Larger Complex Classes:** 500-1000 lines (e.g., complex ViewModels handling significant logic for a specific view, or large Service classes). Classes approaching or exceeding 1000 lines **must** be refactored.
*   Use configuration abstractions; **never** hardcode environment values.
*   Prioritize readability and maintainability.

### 🧪 Refinement - Testing (Tester - TDD)

*   Implement Test-Driven Development: Write failing tests first.
*   Write only enough code to pass the tests.
*   Refactor code for clarity and efficiency after tests pass.
*   Ensure good test coverage for new logic.
*   Use `str-replace-editor` for test code.
*   Use `launch-process` to run tests and verify results.

### 🪲 Refinement - Debugging (Debugger)

*   Troubleshoot any bugs or integration issues that arise.
*   Use logs, diagnostics, and stack traces to isolate problems.
*   Make modular fixes.
*   Use `diagnostics` to identify issues.
*   Use `str-replace-editor` to implement fixes.
*   Use `launch-process` to verify fixes.

### 🛡️ Refinement - Security Review (Security Reviewer)

*   Perform static analysis for potential security issues (e.g., secrets, hardcoded values).
*   Ensure the code follows secure coding practices.
*   Flag any violations of `CodeQuality.md` standards.
*   Use `codebase-retrieval` to scan for issues.
*   Use `str-replace-editor` to implement security fixes.

### 🧹 Optimization (Optimizer)

*   Refactor and improve performance or clarity if needed.
*   Ensure all files adhere to the line count guidelines:
    *   **Target:** Standard Classes (150-500 lines).
    *   **Refactor:** Larger Complex Classes (>500 lines) towards the target. Flag classes >1000 lines.
*   Move any inline configurations to appropriate config files.
*   Use `codebase-retrieval` to understand the code being optimized.
*   Use `str-replace-editor` to implement changes.
*   Use `launch-process` to verify optimizations (e.g., tests still pass).

## 3. Completion & Integration (System Integrator / Deployment Monitor)

*   Integrate the new components into the system.
*   Ensure consistency and cohesion with existing modules.
*   Verify interface compatibility and shared module usage.
*   Use `str-replace-editor` for integration changes.
*   Use `launch-process` to run comprehensive tests.
*   **Document the changes:** If significant, update `Documentation/UFU2-Codebase-Analysis.md`.
*   Post-launch, monitor for performance, logs, and user feedback (Deployment Monitor role).

## Core Best Practices Enforcement

*   **File Size:** Adhere to the line count guidelines:
    *   **Target:** 150-500 lines for most classes.
    *   **Maximum:** Aim for <1000 lines. Refactor larger classes.
*   **Hardcoding:** Never hard-code environment variables, paths, or secrets. Use configuration.
*   **Modularity:** Write modular, testable outputs. Avoid monolithic structures.
*   **Tool Usage:** Leverage SPARC tools (`codebase-retrieval`, `str-replace-editor`, `diagnostics`, `launch-process`) as appropriate for each role and task.

The goal is to improve the application continuously, not worsen it. Always clean up and analyze the codebase as part of your implementation process, guided by the SPARC methodology.