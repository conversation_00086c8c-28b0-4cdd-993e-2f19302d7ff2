using System;
using System.Windows;
using System.Windows.Media;
using System.Windows.Shell;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services.Interfaces;
using UFU2.ViewModels;

namespace UFU2.Services
{
    /// <summary>
    /// Service implementation for managing custom window chrome functionality
    /// Integrates with ThemeManager for dynamic theme updates and provides error handling
    /// </summary>
    public class WindowChromeService : IWindowChromeService
    {
        #region Private Fields

        private WindowChromeConfiguration _configuration;
        private WindowChromeTheme _currentTheme;
        private bool _isThemeSubscribed = false;
        private readonly object _serviceLock = new object();

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the WindowChromeService
        /// </summary>
        public WindowChromeService()
        {
            try
            {
                LoggingService.LogDebug("Initializing WindowChromeService", "WindowChromeService");
                
                // Initialize with default configuration
                _configuration = new WindowChromeConfiguration();
                _currentTheme = new WindowChromeTheme();
                
                // Subscribe to theme changes
                SubscribeToThemeChanges();
                
                LoggingService.LogDebug("WindowChromeService initialized successfully", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing WindowChromeService: {ex.Message}", "WindowChromeService");
                ErrorManager.HandleErrorToast(ex, 
                    "فشل في تهيئة خدمة إطار النافذة المخصص", 
                    "خطأ في التهيئة", 
                    LogLevel.Error, 
                    "WindowChromeService");
                throw;
            }
        }

        #endregion

        #region IWindowChromeService Implementation

        /// <summary>
        /// Applies custom chrome to the specified window
        /// </summary>
        /// <param name="window">The window to apply chrome to</param>
        public void ApplyCustomChrome(Window window)
        {
            if (window == null)
            {
                LoggingService.LogWarning("Cannot apply chrome to null window", "WindowChromeService");
                ErrorManager.ShowUserWarningToast(
                    "لا يمكن تطبيق إطار النافذة المخصص على نافذة فارغة",
                    "خطأ في المعاملات",
                    "WindowChromeService"
                );
                return;
            }

            try
            {
                LoggingService.LogDebug($"Applying custom chrome to window: {window.GetType().Name} (Title: {window.Title})", "WindowChromeService");

                lock (_serviceLock)
                {
                    // Validate window state before applying chrome
                    if (!ValidateWindowForChrome(window))
                    {
                        LoggingService.LogWarning($"Window validation failed for chrome application: {window.GetType().Name}", "WindowChromeService");
                        ApplyFallbackChrome(window);
                        return;
                    }

                    // Create and configure WindowChrome
                    var windowChrome = CreateWindowChrome();
                    if (windowChrome == null)
                    {
                        LoggingService.LogError("Failed to create WindowChrome instance", "WindowChromeService");
                        ApplyFallbackChrome(window);
                        return;
                    }
                    
                    // Apply the chrome to the window with error handling
                    try
                    {
                        WindowChrome.SetWindowChrome(window, windowChrome);
                        LoggingService.LogDebug("WindowChrome applied to window successfully", "WindowChromeService");
                    }
                    catch (Exception chromeEx)
                    {
                        LoggingService.LogError($"Failed to set WindowChrome: {chromeEx.Message}", "WindowChromeService");
                        ApplyFallbackChrome(window);
                        return;
                    }
                    
                    // Update the window's theme to match current application theme
                    try
                    {
                        UpdateWindowTheme(window);
                        LoggingService.LogDebug("Window theme updated successfully", "WindowChromeService");
                    }
                    catch (Exception themeEx)
                    {
                        LoggingService.LogWarning($"Failed to update window theme, continuing with default: {themeEx.Message}", "WindowChromeService");
                        // Continue - theme update failure is not critical
                    }

                    // Validate that chrome was applied correctly
                    if (!ValidateChromeApplication(window))
                    {
                        LoggingService.LogWarning("Chrome application validation failed", "WindowChromeService");
                        ApplyFallbackChrome(window);
                        return;
                    }
                }

                LoggingService.LogDebug($"Custom chrome applied successfully to {window.GetType().Name}", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error applying custom chrome to window: {ex.Message}", "WindowChromeService");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "WindowChromeService");
                
                ErrorManager.HandleErrorToast(ex, 
                    "فشل في تطبيق إطار النافذة المخصص. سيتم استخدام الإطار الافتراضي.", 
                    "خطأ في إطار النافذة", 
                    LogLevel.Error, 
                    "WindowChromeService");
                
                // Apply fallback chrome as last resort
                ApplyFallbackChrome(window);
            }
        }

        /// <summary>
        /// Updates the chrome theme based on the current application theme
        /// </summary>
        /// <param name="theme">The theme to apply</param>
        public void UpdateTheme(ApplicationTheme theme)
        {
            try
            {
                LoggingService.LogDebug($"Updating window chrome theme to: {theme}", "WindowChromeService");

                lock (_serviceLock)
                {
                    // Store previous theme for rollback if needed
                    var previousTheme = _currentTheme;
                    
                    try
                    {
                        // Update the current theme based on the application theme
                        UpdateCurrentTheme(theme);
                        
                        // Validate that theme was applied correctly
                        if (_currentTheme == null)
                        {
                            LoggingService.LogError("Theme update resulted in null theme", "WindowChromeService");
                            _currentTheme = previousTheme ?? new WindowChromeTheme();
                            
                            ErrorManager.ShowUserWarningToast(
                                "فشل في تحديث ألوان إطار النافذة. تم الاحتفاظ بالألوان السابقة",
                                "تحذير تحديث الألوان",
                                "WindowChromeService"
                            );
                            return;
                        }

                        // Log successful theme properties
                        LoggingService.LogDebug($"Theme updated - TitleBarBackground: {_currentTheme.TitleBarBackground?.ToString() ?? "null"}", "WindowChromeService");
                        LoggingService.LogDebug($"Theme updated - TitleBarForeground: {_currentTheme.TitleBarForeground?.ToString() ?? "null"}", "WindowChromeService");
                        LoggingService.LogDebug($"Theme updated - WindowBorderBrush: {_currentTheme.WindowBorderBrush?.ToString() ?? "null"}", "WindowChromeService");
                    }
                    catch (Exception themeEx)
                    {
                        LoggingService.LogError($"Error during theme update: {themeEx.Message}", "WindowChromeService");
                        
                        // Rollback to previous theme
                        if (previousTheme != null)
                        {
                            _currentTheme = previousTheme;
                            LoggingService.LogDebug("Rolled back to previous theme due to update failure", "WindowChromeService");
                        }
                        
                        throw; // Re-throw to be handled by outer catch
                    }
                }

                LoggingService.LogDebug($"Window chrome theme updated successfully to {theme}", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error updating window chrome theme: {ex.Message}", "WindowChromeService");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "WindowChromeService");
                
                ErrorManager.HandleErrorToast(ex, 
                    "فشل في تحديث ألوان إطار النافذة. قد تظهر الألوان بشكل غير صحيح", 
                    "خطأ في تحديث الألوان", 
                    LogLevel.Warning, 
                    "WindowChromeService");
                
                // Ensure we have a valid theme even if update failed
                if (_currentTheme == null)
                {
                    _currentTheme = new WindowChromeTheme();
                    LoggingService.LogDebug("Applied default theme as fallback", "WindowChromeService");
                }
            }
        }

        /// <summary>
        /// Configures window chrome with the specified ViewModel
        /// </summary>
        /// <param name="window">The target window</param>
        /// <param name="viewModel">The ViewModel containing chrome configuration</param>
        public void ConfigureWindowChrome(Window window, object viewModel)
        {
            if (window == null)
            {
                LoggingService.LogWarning("Cannot configure chrome for null window", "WindowChromeService");
                ErrorManager.ShowUserWarningToast(
                    "لا يمكن تكوين إطار النافذة لنافذة فارغة",
                    "خطأ في المعاملات",
                    "WindowChromeService"
                );
                return;
            }

            if (viewModel == null)
            {
                LoggingService.LogWarning("Cannot configure chrome with null ViewModel", "WindowChromeService");
                ErrorManager.ShowUserWarningToast(
                    "لا يمكن تكوين إطار النافذة بدون نموذج عرض",
                    "خطأ في المعاملات",
                    "WindowChromeService"
                );
                return;
            }

            try
            {
                LoggingService.LogDebug($"Configuring window chrome for {window.GetType().Name} with ViewModel {viewModel.GetType().Name}", "WindowChromeService");

                // Validate ViewModel type
                if (!(viewModel is CustomWindowChromeViewModel))
                {
                    LoggingService.LogWarning($"ViewModel is not of expected type CustomWindowChromeViewModel: {viewModel.GetType().Name}", "WindowChromeService");
                    // Continue anyway - might be a derived type
                }

                // Set the ViewModel as the window's DataContext if not already set
                try
                {
                    if (window.DataContext == null)
                    {
                        window.DataContext = viewModel;
                        LoggingService.LogDebug("ViewModel set as window DataContext", "WindowChromeService");
                    }
                    else if (window.DataContext != viewModel)
                    {
                        LoggingService.LogWarning("Window already has a different DataContext, chrome bindings may not work correctly", "WindowChromeService");
                    }
                }
                catch (Exception dataContextEx)
                {
                    LoggingService.LogError($"Failed to set DataContext: {dataContextEx.Message}", "WindowChromeService");
                    // Continue - this is not critical for chrome application
                }

                // Apply custom chrome
                try
                {
                    ApplyCustomChrome(window);
                    LoggingService.LogDebug("Custom chrome applied during configuration", "WindowChromeService");
                }
                catch (Exception chromeEx)
                {
                    LoggingService.LogError($"Failed to apply chrome during configuration: {chromeEx.Message}", "WindowChromeService");
                    // Error already handled in ApplyCustomChrome
                }

                // Ensure native Windows behaviors work correctly
                try
                {
                    EnsureNativeWindowBehaviors(window);
                    LoggingService.LogDebug("Native window behaviors ensured", "WindowChromeService");
                }
                catch (Exception behaviorEx)
                {
                    LoggingService.LogWarning($"Failed to ensure native behaviors: {behaviorEx.Message}", "WindowChromeService");
                    // Continue - this is not critical
                }

                // Validate final configuration
                if (!ValidateWindowConfiguration(window, viewModel))
                {
                    LoggingService.LogWarning("Window chrome configuration validation failed", "WindowChromeService");
                    ErrorManager.ShowUserWarningToast(
                        "تم تكوين إطار النافذة ولكن قد تكون هناك مشاكل في الوظائف",
                        "تحذير تكوين النافذة",
                        "WindowChromeService"
                    );
                }

                LoggingService.LogDebug("Window chrome configuration completed successfully", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error configuring window chrome: {ex.Message}", "WindowChromeService");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "WindowChromeService");
                
                ErrorManager.HandleErrorToast(ex, 
                    "فشل في تكوين إطار النافذة. قد لا تعمل بعض وظائف النافذة بشكل صحيح", 
                    "خطأ في تكوين النافذة", 
                    LogLevel.Error, 
                    "WindowChromeService");
            }
        }

        /// <summary>
        /// Validates the final window configuration after chrome setup
        /// </summary>
        /// <param name="window">The configured window</param>
        /// <param name="viewModel">The associated ViewModel</param>
        /// <returns>True if configuration is valid</returns>
        private bool ValidateWindowConfiguration(Window window, object viewModel)
        {
            try
            {
                LoggingService.LogDebug("Validating window configuration", "WindowChromeService");

                // Check if chrome was applied
                var chrome = WindowChrome.GetWindowChrome(window);
                if (chrome == null)
                {
                    LoggingService.LogWarning("No WindowChrome found on configured window", "WindowChromeService");
                    return false;
                }

                // Check if DataContext is set correctly
                if (window.DataContext != viewModel)
                {
                    LoggingService.LogWarning("Window DataContext does not match provided ViewModel", "WindowChromeService");
                    return false;
                }

                LoggingService.LogDebug("Window configuration validation passed", "WindowChromeService");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating window configuration: {ex.Message}", "WindowChromeService");
                return false;
            }
        }

        /// <summary>
        /// Creates a new WindowChrome instance with default configuration optimized for native behaviors
        /// </summary>
        /// <returns>Configured WindowChrome instance</returns>
        public WindowChrome CreateWindowChrome()
        {
            try
            {
                LoggingService.LogDebug("Creating WindowChrome instance", "WindowChromeService");

                // Validate configuration before creating chrome
                if (_configuration == null)
                {
                    LoggingService.LogWarning("Configuration is null, using default configuration", "WindowChromeService");
                    _configuration = new WindowChromeConfiguration();
                }

                // Validate configuration values
                if (!ValidateConfiguration(_configuration))
                {
                    LoggingService.LogWarning("Configuration validation failed, using safe defaults", "WindowChromeService");
                    _configuration = new WindowChromeConfiguration();
                }

                var windowChrome = new WindowChrome
                {
                    CaptionHeight = _configuration.CaptionHeight,
                    ResizeBorderThickness = new Thickness(_configuration.ResizeBorderThickness),
                    GlassFrameThickness = new Thickness(_configuration.GlassFrameThickness),
                    UseAeroCaptionButtons = _configuration.UseAeroCaptionButtons,
                    CornerRadius = _configuration.CornerRadius,
                    // Ensure NonClientFrameEdges is set to None for full custom chrome control
                    NonClientFrameEdges = NonClientFrameEdges.None
                };

                // Ensure proper configuration for native Windows behaviors
                try
                {
                    ValidateWindowChromeConfiguration(windowChrome);
                    LoggingService.LogDebug("WindowChrome configuration validated successfully", "WindowChromeService");
                }
                catch (Exception validationEx)
                {
                    LoggingService.LogWarning($"WindowChrome validation failed: {validationEx.Message}", "WindowChromeService");
                    // Continue with the chrome as-is, validation adjustments are not critical
                }

                LoggingService.LogDebug($"WindowChrome instance created successfully - CaptionHeight: {windowChrome.CaptionHeight}, ResizeBorder: {windowChrome.ResizeBorderThickness}", "WindowChromeService");
                return windowChrome;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error creating WindowChrome: {ex.Message}", "WindowChromeService");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "WindowChromeService");
                
                // Return a basic WindowChrome as fallback
                try
                {
                    var fallbackChrome = CreateFallbackWindowChrome();
                    LoggingService.LogDebug("Created fallback WindowChrome due to creation error", "WindowChromeService");
                    return fallbackChrome;
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Critical: Failed to create fallback WindowChrome: {fallbackEx.Message}", "WindowChromeService");
                    
                    ErrorManager.ShowUserErrorToast(
                        "فشل في إنشاء إطار النافذة المخصص والبديل. قد تحتاج لإعادة تشغيل التطبيق",
                        "خطأ خطير في إطار النافذة",
                        "WindowChromeService"
                    );
                    
                    // Return null to indicate complete failure
                    return null;
                }
            }
        }

        /// <summary>
        /// Validates the WindowChromeConfiguration values
        /// </summary>
        /// <param name="config">The configuration to validate</param>
        /// <returns>True if configuration is valid</returns>
        private bool ValidateConfiguration(WindowChromeConfiguration config)
        {
            try
            {
                if (config.CaptionHeight < 16 || config.CaptionHeight > 64)
                {
                    LoggingService.LogWarning($"Invalid caption height: {config.CaptionHeight}", "WindowChromeService");
                    return false;
                }

                if (config.ResizeBorderThickness < 0 || config.ResizeBorderThickness > 32)
                {
                    LoggingService.LogWarning($"Invalid resize border thickness: {config.ResizeBorderThickness}", "WindowChromeService");
                    return false;
                }

                if (config.GlassFrameThickness < 0)
                {
                    LoggingService.LogWarning($"Invalid glass frame thickness: {config.GlassFrameThickness}", "WindowChromeService");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating configuration: {ex.Message}", "WindowChromeService");
                return false;
            }
        }

        /// <summary>
        /// Creates a fallback WindowChrome with safe default values optimized for native behaviors
        /// </summary>
        /// <returns>Fallback WindowChrome instance</returns>
        private WindowChrome CreateFallbackWindowChrome()
        {
            try
            {
                LoggingService.LogDebug("Creating fallback WindowChrome with safe defaults", "WindowChromeService");

                var fallbackChrome = new WindowChrome
                {
                    CaptionHeight = 32,
                    ResizeBorderThickness = new Thickness(8),
                    GlassFrameThickness = new Thickness(0),
                    UseAeroCaptionButtons = false,
                    CornerRadius = new CornerRadius(0),
                    NonClientFrameEdges = NonClientFrameEdges.None
                };

                LoggingService.LogDebug("Fallback WindowChrome created successfully", "WindowChromeService");
                return fallbackChrome;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical: Failed to create fallback WindowChrome: {ex.Message}", "WindowChromeService");
                
                // This should never happen, but if it does, we have a serious problem
                ErrorManager.ShowUserErrorToast(
                    "فشل في إنشاء إطار النافذة البديل. هناك مشكلة خطيرة في النظام",
                    "خطأ خطير جداً",
                    "WindowChromeService"
                );
                
                throw new InvalidOperationException("Failed to create fallback WindowChrome", ex);
            }
        }

        /// <summary>
        /// Validates and optimizes WindowChrome configuration for native Windows behavior
        /// </summary>
        /// <param name="windowChrome">The WindowChrome to validate</param>
        private void ValidateWindowChromeConfiguration(WindowChrome windowChrome)
        {
            try
            {
                // Ensure resize border thickness is adequate for edge resizing
                // Minimum 4px for comfortable resizing, 8px is optimal
                var minResizeBorder = 4.0;
                var currentThickness = windowChrome.ResizeBorderThickness;
                
                if (currentThickness.Left < minResizeBorder || currentThickness.Right < minResizeBorder ||
                    currentThickness.Top < minResizeBorder || currentThickness.Bottom < minResizeBorder)
                {
                    LoggingService.LogWarning($"Resize border thickness too small: {currentThickness}, adjusting to minimum", "WindowChromeService");
                    windowChrome.ResizeBorderThickness = new Thickness(Math.Max(currentThickness.Left, minResizeBorder),
                                                                      Math.Max(currentThickness.Top, minResizeBorder),
                                                                      Math.Max(currentThickness.Right, minResizeBorder),
                                                                      Math.Max(currentThickness.Bottom, minResizeBorder));
                }

                // Ensure caption height is reasonable (minimum 24px, maximum 48px)
                if (windowChrome.CaptionHeight < 24)
                {
                    LoggingService.LogWarning($"Caption height too small: {windowChrome.CaptionHeight}, adjusting to 24px", "WindowChromeService");
                    windowChrome.CaptionHeight = 24;
                }
                else if (windowChrome.CaptionHeight > 48)
                {
                    LoggingService.LogWarning($"Caption height too large: {windowChrome.CaptionHeight}, adjusting to 48px", "WindowChromeService");
                    windowChrome.CaptionHeight = 48;
                }

                // Ensure GlassFrameThickness is 0 for custom chrome (non-zero values can interfere with custom styling)
                if (windowChrome.GlassFrameThickness.Left != 0 || windowChrome.GlassFrameThickness.Right != 0 ||
                    windowChrome.GlassFrameThickness.Top != 0 || windowChrome.GlassFrameThickness.Bottom != 0)
                {
                    LoggingService.LogDebug("Setting GlassFrameThickness to 0 for custom chrome compatibility", "WindowChromeService");
                    windowChrome.GlassFrameThickness = new Thickness(0);
                }

                // Ensure UseAeroCaptionButtons is false for custom chrome
                if (windowChrome.UseAeroCaptionButtons)
                {
                    LoggingService.LogDebug("Disabling UseAeroCaptionButtons for custom chrome", "WindowChromeService");
                    windowChrome.UseAeroCaptionButtons = false;
                }

                LoggingService.LogDebug("WindowChrome configuration validated and optimized", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating WindowChrome configuration: {ex.Message}", "WindowChromeService");
            }
        }

        /// <summary>
        /// Gets the current window chrome configuration
        /// </summary>
        /// <returns>Current WindowChromeConfiguration</returns>
        public WindowChromeConfiguration GetConfiguration()
        {
            lock (_serviceLock)
            {
                return _configuration;
            }
        }

        /// <summary>
        /// Gets the current window chrome theme
        /// </summary>
        /// <returns>Current WindowChromeTheme</returns>
        public WindowChromeTheme GetTheme()
        {
            lock (_serviceLock)
            {
                return _currentTheme;
            }
        }

        #endregion

        #region Validation and Fallback Methods

        /// <summary>
        /// Validates that a window is suitable for custom chrome application
        /// </summary>
        /// <param name="window">The window to validate</param>
        /// <returns>True if the window is valid for chrome application</returns>
        private bool ValidateWindowForChrome(Window window)
        {
            try
            {
                LoggingService.LogDebug($"Validating window for chrome application: {window.GetType().Name}", "WindowChromeService");

                // Check if window is already closed or closing
                if (window.IsLoaded == false)
                {
                    LoggingService.LogWarning("Window is not loaded, cannot apply chrome", "WindowChromeService");
                    return false;
                }

                // Check if window has valid dimensions
                if (window.ActualWidth <= 0 || window.ActualHeight <= 0)
                {
                    LoggingService.LogDebug("Window has invalid dimensions, will retry after layout", "WindowChromeService");
                    // This is not necessarily an error - window might not be laid out yet
                }

                // Check if window allows custom chrome (some system dialogs might not)
                if (window.WindowStyle == WindowStyle.ToolWindow)
                {
                    LoggingService.LogDebug("ToolWindow style detected, custom chrome may not be appropriate", "WindowChromeService");
                    return false;
                }

                LoggingService.LogDebug("Window validation passed", "WindowChromeService");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating window for chrome: {ex.Message}", "WindowChromeService");
                return false;
            }
        }

        /// <summary>
        /// Validates that chrome was applied correctly to the window
        /// </summary>
        /// <param name="window">The window to validate</param>
        /// <returns>True if chrome application was successful</returns>
        private bool ValidateChromeApplication(Window window)
        {
            try
            {
                LoggingService.LogDebug("Validating chrome application", "WindowChromeService");

                // Check if WindowChrome was actually applied
                var appliedChrome = WindowChrome.GetWindowChrome(window);
                if (appliedChrome == null)
                {
                    LoggingService.LogError("WindowChrome was not applied to window", "WindowChromeService");
                    return false;
                }

                // Validate chrome configuration
                if (appliedChrome.CaptionHeight <= 0)
                {
                    LoggingService.LogWarning($"Invalid caption height: {appliedChrome.CaptionHeight}", "WindowChromeService");
                    return false;
                }

                if (appliedChrome.ResizeBorderThickness.Left < 0 || appliedChrome.ResizeBorderThickness.Top < 0)
                {
                    LoggingService.LogWarning($"Invalid resize border thickness: {appliedChrome.ResizeBorderThickness}", "WindowChromeService");
                    return false;
                }

                LoggingService.LogDebug("Chrome application validation passed", "WindowChromeService");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating chrome application: {ex.Message}", "WindowChromeService");
                return false;
            }
        }

        /// <summary>
        /// Applies fallback chrome behavior when custom chrome fails
        /// </summary>
        /// <param name="window">The window to apply fallback chrome to</param>
        private void ApplyFallbackChrome(Window window)
        {
            try
            {
                LoggingService.LogDebug($"Applying fallback chrome to window: {window.GetType().Name}", "WindowChromeService");

                // Remove any existing custom chrome
                try
                {
                    WindowChrome.SetWindowChrome(window, null);
                    LoggingService.LogDebug("Existing WindowChrome removed", "WindowChromeService");
                }
                catch (Exception removeEx)
                {
                    LoggingService.LogWarning($"Failed to remove existing chrome: {removeEx.Message}", "WindowChromeService");
                }

                // Apply minimal chrome for basic functionality
                try
                {
                    var fallbackChrome = CreateFallbackWindowChrome();
                    WindowChrome.SetWindowChrome(window, fallbackChrome);
                    LoggingService.LogDebug("Fallback chrome applied successfully", "WindowChromeService");
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Failed to apply fallback chrome: {fallbackEx.Message}", "WindowChromeService");
                    
                    // Last resort: ensure window uses default system chrome
                    try
                    {
                        WindowChrome.SetWindowChrome(window, null);
                        LoggingService.LogDebug("Reverted to default system chrome", "WindowChromeService");
                        
                        ErrorManager.ShowUserWarningToast(
                            "تم الرجوع إلى إطار النافذة الافتراضي بسبب مشكلة تقنية",
                            "تحذير إطار النافذة",
                            "WindowChromeService"
                        );
                    }
                    catch (Exception systemEx)
                    {
                        LoggingService.LogError($"Critical: Failed to revert to system chrome: {systemEx.Message}", "WindowChromeService");
                        
                        ErrorManager.ShowUserErrorToast(
                            "حدث خطأ خطير في إطار النافذة. قد تحتاج لإعادة تشغيل التطبيق",
                            "خطأ خطير",
                            "WindowChromeService"
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error in fallback chrome application: {ex.Message}", "WindowChromeService");
                
                ErrorManager.ShowUserErrorToast(
                    "فشل في تطبيق إطار النافذة البديل. قد يؤثر هذا على وظائف النافذة",
                    "خطأ خطير في إطار النافذة",
                    "WindowChromeService"
                );
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Subscribes to ThemeManager theme change events
        /// </summary>
        private void SubscribeToThemeChanges()
        {
            try
            {
                if (!_isThemeSubscribed)
                {
                    ThemeManager.ThemeChanged += OnThemeChanged;
                    _isThemeSubscribed = true;
                    
                    // Initialize with current theme
                    UpdateTheme(ThemeManager.CurrentTheme);
                    
                    LoggingService.LogDebug("Subscribed to ThemeManager events", "WindowChromeService");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error subscribing to theme changes: {ex.Message}", "WindowChromeService");
            }
        }

        /// <summary>
        /// Handles theme change events from ThemeManager
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Theme change event arguments</param>
        private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
        {
            try
            {
                LoggingService.LogDebug($"Theme change event received: {e.PreviousTheme} -> {e.NewTheme}", "WindowChromeService");
                UpdateTheme(e.NewTheme);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling theme change event: {ex.Message}", "WindowChromeService");
            }
        }

        /// <summary>
        /// Updates the current theme based on the application theme
        /// </summary>
        /// <param name="theme">The application theme to apply</param>
        private void UpdateCurrentTheme(ApplicationTheme theme)
        {
            try
            {
                // Get theme colors from ThemeManager
                var titleBarBackground = GetThemeResource(theme, "TitleBarBackground") ?? GetDefaultTitleBarBackground(theme);
                var titleBarForeground = GetThemeResource(theme, "TitleBarForeground") ?? GetDefaultTitleBarForeground(theme);
                var windowBorderBrush = GetThemeResource(theme, "WindowBorderBrush") ?? GetDefaultWindowBorder(theme);
                var buttonHoverBackground = GetThemeResource(theme, "ButtonHoverBackground") ?? GetDefaultButtonHover(theme);
                var buttonPressedBackground = GetThemeResource(theme, "ButtonPressedBackground") ?? GetDefaultButtonPressed(theme);

                // Update current theme
                _currentTheme = new WindowChromeTheme
                {
                    TitleBarBackground = titleBarBackground,
                    TitleBarForeground = titleBarForeground,
                    WindowBorderBrush = windowBorderBrush,
                    ButtonHoverBackground = buttonHoverBackground,
                    ButtonPressedBackground = buttonPressedBackground,
                    TitleBarOpacity = 1.0
                };

                LoggingService.LogDebug($"Current theme updated for {theme}", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating current theme: {ex.Message}", "WindowChromeService");
                
                // Use default theme as fallback
                _currentTheme = new WindowChromeTheme();
            }
        }

        /// <summary>
        /// Updates the theme for a specific window
        /// </summary>
        /// <param name="window">The window to update</param>
        private void UpdateWindowTheme(Window window)
        {
            try
            {
                // This method can be extended to apply theme-specific resources to the window
                // For now, the theme colors will be applied through DynamicResource bindings in XAML
                LoggingService.LogDebug($"Window theme updated for {window.GetType().Name}", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating window theme: {ex.Message}", "WindowChromeService");
            }
        }

        /// <summary>
        /// Gets a theme resource from the current application resources
        /// </summary>
        /// <param name="theme">The theme to get the resource for</param>
        /// <param name="resourceKey">The resource key</param>
        /// <returns>The brush resource or null if not found</returns>
        private Brush? GetThemeResource(ApplicationTheme theme, string resourceKey)
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources != null && app.Resources.Contains(resourceKey))
                {
                    var resource = app.Resources[resourceKey];
                    if (resource is Brush brush)
                    {
                        return brush;
                    }
                    else if (resource is Color color)
                    {
                        return new SolidColorBrush(color);
                    }
                }

                // Try to get from ThemeManager
                var themeColor = ThemeManager.GetThemeColor(resourceKey);
                if (themeColor.HasValue)
                {
                    return new SolidColorBrush(themeColor.Value);
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting theme resource '{resourceKey}': {ex.Message}", "WindowChromeService");
                return null;
            }
        }

        /// <summary>
        /// Gets the default title bar background for the specified theme
        /// </summary>
        /// <param name="theme">The application theme</param>
        /// <returns>Default title bar background brush</returns>
        private Brush GetDefaultTitleBarBackground(ApplicationTheme theme)
        {
            return theme == ApplicationTheme.Dark 
                ? new SolidColorBrush(Color.FromRgb(45, 45, 48))  // Dark gray
                : new SolidColorBrush(Color.FromRgb(240, 240, 240)); // Light gray
        }

        /// <summary>
        /// Gets the default title bar foreground for the specified theme
        /// </summary>
        /// <param name="theme">The application theme</param>
        /// <returns>Default title bar foreground brush</returns>
        private Brush GetDefaultTitleBarForeground(ApplicationTheme theme)
        {
            return theme == ApplicationTheme.Dark 
                ? Brushes.White 
                : Brushes.Black;
        }

        /// <summary>
        /// Gets the default window border for the specified theme
        /// </summary>
        /// <param name="theme">The application theme</param>
        /// <returns>Default window border brush</returns>
        private Brush GetDefaultWindowBorder(ApplicationTheme theme)
        {
            return theme == ApplicationTheme.Dark 
                ? new SolidColorBrush(Color.FromRgb(60, 60, 60))  // Dark border
                : new SolidColorBrush(Color.FromRgb(200, 200, 200)); // Light border
        }

        /// <summary>
        /// Gets the default button hover background for the specified theme
        /// </summary>
        /// <param name="theme">The application theme</param>
        /// <returns>Default button hover background brush</returns>
        private Brush GetDefaultButtonHover(ApplicationTheme theme)
        {
            return theme == ApplicationTheme.Dark 
                ? new SolidColorBrush(Color.FromRgb(70, 70, 70))  // Dark hover
                : new SolidColorBrush(Color.FromRgb(220, 220, 220)); // Light hover
        }

        /// <summary>
        /// Gets the default button pressed background for the specified theme
        /// </summary>
        /// <param name="theme">The application theme</param>
        /// <returns>Default button pressed background brush</returns>
        private Brush GetDefaultButtonPressed(ApplicationTheme theme)
        {
            return theme == ApplicationTheme.Dark 
                ? new SolidColorBrush(Color.FromRgb(90, 90, 90))  // Dark pressed
                : new SolidColorBrush(Color.FromRgb(180, 180, 180)); // Light pressed
        }

        #endregion

        #region Aero Snap and Native Behavior Support

        /// <summary>
        /// Validates that Windows Aero Snap functionality works correctly with the custom chrome
        /// </summary>
        /// <param name="window">The window to validate</param>
        /// <returns>True if Aero Snap is working correctly, false otherwise</returns>
        public bool ValidateAeroSnapFunctionality(Window window)
        {
            try
            {
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot validate Aero Snap for null window", "WindowChromeService");
                    return false;
                }

                LoggingService.LogDebug($"Validating Aero Snap functionality for window: {window.Title}", "WindowChromeService");

                // Check if WindowChrome is applied
                var windowChrome = WindowChrome.GetWindowChrome(window);
                if (windowChrome == null)
                {
                    LoggingService.LogWarning("No WindowChrome found on window, Aero Snap may not work correctly", "WindowChromeService");
                    return false;
                }

                // Validate resize border thickness for proper edge detection
                var resizeBorder = windowChrome.ResizeBorderThickness;
                var hasAdequateResizeBorder = resizeBorder.Left >= 4 && resizeBorder.Right >= 4 &&
                                            resizeBorder.Top >= 4 && resizeBorder.Bottom >= 4;

                if (!hasAdequateResizeBorder)
                {
                    LoggingService.LogWarning($"Resize border thickness may be too small for proper Aero Snap: {resizeBorder}", "WindowChromeService");
                    return false;
                }

                // Check window resize mode
                if (window.ResizeMode == ResizeMode.NoResize || window.ResizeMode == ResizeMode.CanMinimize)
                {
                    LoggingService.LogDebug($"Window ResizeMode ({window.ResizeMode}) does not support Aero Snap", "WindowChromeService");
                    return false; // This is expected behavior, not an error
                }

                // Validate that the window can be moved and resized
                var canResize = window.ResizeMode == ResizeMode.CanResize || window.ResizeMode == ResizeMode.CanResizeWithGrip;
                if (!canResize)
                {
                    LoggingService.LogWarning($"Window ResizeMode ({window.ResizeMode}) may not support full Aero Snap functionality", "WindowChromeService");
                }

                LoggingService.LogDebug("Aero Snap functionality validation completed successfully", "WindowChromeService");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating Aero Snap functionality: {ex.Message}", "WindowChromeService");
                return false;
            }
        }

        /// <summary>
        /// Ensures that native Windows behaviors (resize, move, snap) work correctly with custom chrome
        /// </summary>
        /// <param name="window">The window to configure</param>
        public void EnsureNativeWindowBehaviors(Window window)
        {
            try
            {
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot ensure native behaviors for null window", "WindowChromeService");
                    return;
                }

                LoggingService.LogDebug($"Ensuring native window behaviors for: {window.Title}", "WindowChromeService");

                // Validate and apply WindowChrome if not already applied
                var existingChrome = WindowChrome.GetWindowChrome(window);
                if (existingChrome == null)
                {
                    LoggingService.LogDebug("Applying WindowChrome for native behavior support", "WindowChromeService");
                    ApplyCustomChrome(window);
                    existingChrome = WindowChrome.GetWindowChrome(window);
                }

                // Ensure proper WindowChrome configuration for native behaviors
                if (existingChrome != null)
                {
                    EnsureOptimalChromeConfiguration(existingChrome, window);
                }

                // Validate Aero Snap functionality
                var aeroSnapWorking = ValidateAeroSnapFunctionality(window);
                if (!aeroSnapWorking)
                {
                    LoggingService.LogWarning("Aero Snap functionality validation failed", "WindowChromeService");
                }

                // Ensure proper window state handling
                window.StateChanged += OnWindowStateChanged;

                // Validate window resizing from all edges and corners
                ValidateWindowResizing(window);

                LoggingService.LogDebug("Native window behaviors configuration completed", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error ensuring native window behaviors: {ex.Message}", "WindowChromeService");
            }
        }

        /// <summary>
        /// Ensures optimal WindowChrome configuration for native Windows behaviors
        /// </summary>
        /// <param name="windowChrome">The WindowChrome to configure</param>
        /// <param name="window">The target window</param>
        private void EnsureOptimalChromeConfiguration(WindowChrome windowChrome, Window window)
        {
            try
            {
                LoggingService.LogDebug("Ensuring optimal WindowChrome configuration for native behaviors", "WindowChromeService");

                // Ensure adequate resize border thickness for edge/corner resizing
                // 8px is optimal for comfortable resizing while maintaining visual appeal
                var optimalResizeBorder = 8.0;
                if (windowChrome.ResizeBorderThickness.Left < optimalResizeBorder ||
                    windowChrome.ResizeBorderThickness.Right < optimalResizeBorder ||
                    windowChrome.ResizeBorderThickness.Top < optimalResizeBorder ||
                    windowChrome.ResizeBorderThickness.Bottom < optimalResizeBorder)
                {
                    LoggingService.LogDebug($"Adjusting resize border thickness to optimal value: {optimalResizeBorder}px", "WindowChromeService");
                    windowChrome.ResizeBorderThickness = new Thickness(optimalResizeBorder);
                }

                // Ensure caption height is adequate for title bar interactions
                var optimalCaptionHeight = 32.0;
                if (windowChrome.CaptionHeight < optimalCaptionHeight)
                {
                    LoggingService.LogDebug($"Adjusting caption height to optimal value: {optimalCaptionHeight}px", "WindowChromeService");
                    windowChrome.CaptionHeight = optimalCaptionHeight;
                }

                // Ensure GlassFrameThickness is 0 for proper custom chrome behavior
                if (windowChrome.GlassFrameThickness.Left != 0 || windowChrome.GlassFrameThickness.Right != 0 ||
                    windowChrome.GlassFrameThickness.Top != 0 || windowChrome.GlassFrameThickness.Bottom != 0)
                {
                    LoggingService.LogDebug("Setting GlassFrameThickness to 0 for optimal custom chrome", "WindowChromeService");
                    windowChrome.GlassFrameThickness = new Thickness(0);
                }

                // Ensure UseAeroCaptionButtons is false for custom chrome
                if (windowChrome.UseAeroCaptionButtons)
                {
                    LoggingService.LogDebug("Disabling UseAeroCaptionButtons for custom chrome", "WindowChromeService");
                    windowChrome.UseAeroCaptionButtons = false;
                }

                // Ensure NonClientFrameEdges is set to None for full custom chrome control
                if (windowChrome.NonClientFrameEdges != NonClientFrameEdges.None)
                {
                    LoggingService.LogDebug("Setting NonClientFrameEdges to None for full custom chrome control", "WindowChromeService");
                    windowChrome.NonClientFrameEdges = NonClientFrameEdges.None;
                }

                LoggingService.LogDebug("WindowChrome configuration optimized for native behaviors", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error ensuring optimal chrome configuration: {ex.Message}", "WindowChromeService");
            }
        }

        /// <summary>
        /// Validates that window resizing works correctly from all edges and corners
        /// </summary>
        /// <param name="window">The window to validate</param>
        private void ValidateWindowResizing(Window window)
        {
            try
            {
                LoggingService.LogDebug($"Validating window resizing functionality for: {window.Title}", "WindowChromeService");

                // Check if window allows resizing
                if (window.ResizeMode == ResizeMode.NoResize)
                {
                    LoggingService.LogDebug("Window ResizeMode is NoResize - resizing validation skipped", "WindowChromeService");
                    return;
                }

                if (window.ResizeMode == ResizeMode.CanMinimize)
                {
                    LoggingService.LogDebug("Window ResizeMode is CanMinimize - limited resizing expected", "WindowChromeService");
                    return;
                }

                // Validate WindowChrome resize border configuration
                var windowChrome = WindowChrome.GetWindowChrome(window);
                if (windowChrome == null)
                {
                    LoggingService.LogWarning("No WindowChrome found - resizing may not work properly", "WindowChromeService");
                    return;
                }

                var resizeBorder = windowChrome.ResizeBorderThickness;
                var minResizeBorder = 4.0; // Minimum for comfortable resizing

                if (resizeBorder.Left < minResizeBorder || resizeBorder.Right < minResizeBorder ||
                    resizeBorder.Top < minResizeBorder || resizeBorder.Bottom < minResizeBorder)
                {
                    LoggingService.LogWarning($"Resize border thickness may be too small for comfortable resizing: {resizeBorder}", "WindowChromeService");
                }
                else
                {
                    LoggingService.LogDebug($"Resize border thickness is adequate: {resizeBorder}", "WindowChromeService");
                }

                // Check window minimum and maximum size constraints
                if (window.MinWidth > 0 && window.MinHeight > 0)
                {
                    LoggingService.LogDebug($"Window has minimum size constraints: {window.MinWidth}x{window.MinHeight}", "WindowChromeService");
                }

                if (window.MaxWidth < double.PositiveInfinity && window.MaxHeight < double.PositiveInfinity)
                {
                    LoggingService.LogDebug($"Window has maximum size constraints: {window.MaxWidth}x{window.MaxHeight}", "WindowChromeService");
                }

                LoggingService.LogDebug("Window resizing validation completed successfully", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating window resizing: {ex.Message}", "WindowChromeService");
            }
        }

        /// <summary>
        /// Handles window state changes to ensure proper behavior with custom chrome
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Event arguments</param>
        private void OnWindowStateChanged(object sender, EventArgs e)
        {
            try
            {
                if (sender is Window window)
                {
                    LoggingService.LogDebug($"Window state changed to: {window.WindowState} for window: {window.Title}", "WindowChromeService");

                    // Handle maximized state border adjustments
                    if (window.WindowState == WindowState.Maximized)
                    {
                        // When maximized, we might want to adjust border thickness to 0
                        // This is handled in the XAML template triggers
                        LoggingService.LogDebug("Window maximized, border adjustments handled by template", "WindowChromeService");
                    }
                    else if (window.WindowState == WindowState.Normal)
                    {
                        // When restored, ensure proper border thickness
                        LoggingService.LogDebug("Window restored to normal, border adjustments handled by template", "WindowChromeService");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling window state change: {ex.Message}", "WindowChromeService");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the service and unsubscribes from events
        /// </summary>
        public void Dispose()
        {
            try
            {
                if (_isThemeSubscribed)
                {
                    ThemeManager.ThemeChanged -= OnThemeChanged;
                    _isThemeSubscribed = false;
                }

                LoggingService.LogDebug("WindowChromeService disposed", "WindowChromeService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disposing WindowChromeService: {ex.Message}", "WindowChromeService");
            }
        }

        #endregion
    }
}