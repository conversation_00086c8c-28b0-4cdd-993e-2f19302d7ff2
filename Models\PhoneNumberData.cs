using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace UFU2.Models
{
    /// <summary>
    /// Data transfer object for phone number operations.
    /// Contains phone number information with type classification and primary phone designation.
    /// Supports validation and formatting according to UFU2 standards.
    /// </summary>
    public class PhoneNumberData
    {
        /// <summary>
        /// Gets or sets the client UID this phone number belongs to.
        /// </summary>
        public string ClientUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the phone number.
        /// Should be formatted according to UFU2 standards (XXXX-XX-XX-XX or XXX-XX-XX-XX).
        /// </summary>
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the type of phone number.
        /// Valid values: Mobile, Home, Work, Fax.
        /// </summary>
        public string PhoneType { get; set; } = "Mobile";

        /// <summary>
        /// Gets or sets whether this is the primary phone number for the client.
        /// Only one phone number per client can be marked as primary.
        /// </summary>
        public bool IsPrimary { get; set; } = false;

        /// <summary>
        /// Gets or sets the creation timestamp.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the last update timestamp.
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Gets the Arabic display name for the phone type.
        /// </summary>
        public string PhoneTypeDisplayName => GetPhoneTypeDisplayName(PhoneType);

        /// <summary>
        /// Initializes a new instance of the PhoneNumberData class.
        /// </summary>
        public PhoneNumberData()
        {
            CreatedAt = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// Initializes a new instance of the PhoneNumberData class with specified values.
        /// </summary>
        /// <param name="phoneNumber">The phone number</param>
        /// <param name="phoneType">The phone type</param>
        /// <param name="isPrimary">Whether this is the primary phone</param>
        public PhoneNumberData(string phoneNumber, string phoneType, bool isPrimary = false)
        {
            PhoneNumber = phoneNumber ?? string.Empty;
            PhoneType = phoneType ?? "Mobile";
            IsPrimary = isPrimary;
            CreatedAt = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// Validates the phone number data.
        /// </summary>
        /// <returns>True if the phone number data is valid</returns>
        public bool IsValid()
        {
            // Check if phone number is not empty
            if (string.IsNullOrWhiteSpace(PhoneNumber))
                return false;

            // Check if phone type is valid
            var validTypes = new[] { "Mobile", "Home", "Work", "Fax" };
            if (!validTypes.Contains(PhoneType))
                return false;

            // Validate phone number format
            return IsValidPhoneNumberFormat(PhoneNumber);
        }

        /// <summary>
        /// Validates the phone number format.
        /// Accepts Algerian phone number formats with or without formatting.
        /// </summary>
        /// <param name="phoneNumber">The phone number to validate</param>
        /// <returns>True if the format is valid</returns>
        private bool IsValidPhoneNumberFormat(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Remove all non-digit characters for validation
            var digitsOnly = Regex.Replace(phoneNumber, @"\D", "");

            // Algerian phone numbers are typically 9 or 10 digits
            // Mobile: 9 digits (05XXXXXXXX, 06XXXXXXXX, 07XXXXXXXX)
            // Fixed: 9 digits (021XXXXXX, 031XXXXXX, etc.)
            // International format: 10 digits with country code (213XXXXXXXXX)
            return digitsOnly.Length >= 9 && digitsOnly.Length <= 13;
        }

        /// <summary>
        /// Formats the phone number for display.
        /// Applies standard formatting if the number is valid.
        /// </summary>
        /// <returns>Formatted phone number</returns>
        public string GetFormattedPhoneNumber()
        {
            if (string.IsNullOrWhiteSpace(PhoneNumber))
                return PhoneNumber;

            var digitsOnly = Regex.Replace(PhoneNumber, @"\D", "");

            // Format based on length
            return digitsOnly.Length switch
            {
                9 => FormatNineDigitNumber(digitsOnly),
                10 => FormatTenDigitNumber(digitsOnly),
                _ => PhoneNumber // Return original if can't format
            };
        }

        /// <summary>
        /// Formats a 9-digit phone number (typical Algerian format).
        /// </summary>
        /// <param name="digits">The 9 digits</param>
        /// <returns>Formatted phone number</returns>
        private string FormatNineDigitNumber(string digits)
        {
            // Format as XXX-XX-XX-XX
            if (digits.Length == 9)
            {
                return $"{digits.Substring(0, 3)}-{digits.Substring(3, 2)}-{digits.Substring(5, 2)}-{digits.Substring(7, 2)}";
            }
            return digits;
        }

        /// <summary>
        /// Formats a 10-digit phone number (with area code or country code).
        /// </summary>
        /// <param name="digits">The 10 digits</param>
        /// <returns>Formatted phone number</returns>
        private string FormatTenDigitNumber(string digits)
        {
            // Format as XXXX-XX-XX-XX
            if (digits.Length == 10)
            {
                return $"{digits.Substring(0, 4)}-{digits.Substring(4, 2)}-{digits.Substring(6, 2)}-{digits.Substring(8, 2)}";
            }
            return digits;
        }

        /// <summary>
        /// Gets the Arabic display name for a phone type.
        /// </summary>
        /// <param name="phoneType">The phone type</param>
        /// <returns>Arabic display name</returns>
        public static string GetPhoneTypeDisplayName(string phoneType)
        {
            return phoneType switch
            {
                "Mobile" => "هاتف محمول",
                "Home" => "هاتف المنزل",
                "Work" => "هاتف العمل",
                "Fax" => "فاكس",
                _ => "هاتف محمول"
            };
        }

        /// <summary>
        /// Gets all available phone types with their Arabic display names.
        /// </summary>
        /// <returns>Dictionary of phone types and their display names</returns>
        public static Dictionary<string, string> GetAllPhoneTypes()
        {
            return new Dictionary<string, string>
            {
                { "Mobile", GetPhoneTypeDisplayName("Mobile") },
                { "Home", GetPhoneTypeDisplayName("Home") },
                { "Work", GetPhoneTypeDisplayName("Work") },
                { "Fax", GetPhoneTypeDisplayName("Fax") }
            };
        }

        /// <summary>
        /// Creates a copy of the phone number data.
        /// </summary>
        /// <returns>A new PhoneNumberData instance with the same values</returns>
        public PhoneNumberData Clone()
        {
            return new PhoneNumberData
            {
                ClientUid = this.ClientUid,
                PhoneNumber = this.PhoneNumber,
                PhoneType = this.PhoneType,
                IsPrimary = this.IsPrimary,
                CreatedAt = this.CreatedAt,
                UpdatedAt = this.UpdatedAt
            };
        }

        /// <summary>
        /// Returns a string representation of the phone number data.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            var primaryIndicator = IsPrimary ? " (أساسي)" : "";
            return $"{GetFormattedPhoneNumber()} - {PhoneTypeDisplayName}{primaryIndicator}";
        }
    }
}