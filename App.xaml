﻿<Application
    x:Class="UFU2.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:local="clr-namespace:UFU2"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--  MaterialDesign theme - will be managed dynamically by ThemeManager  -->
                <materialDesign:BundledTheme
                    x:Name="MaterialDesignBundledTheme"
                    BaseTheme="Dark"
                    PrimaryColor="DeepPurple"
                    SecondaryColor="Purple" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign3.Defaults.xaml" />

                <ResourceDictionary Source="/Resources/Themes/DarkTheme.xaml" />
                <ResourceDictionary Source="/Resources/Tokens/SpacingTokens.xaml" />
                <ResourceDictionary Source="/Resources/Styles/TypographyStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ButtonsStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/InputBoxStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/CardsStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/SwitchButtonStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ScrollViewerStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/SliderStyles.xaml" />

            </ResourceDictionary.MergedDictionaries>

            <Style TargetType="Window">
                <Setter Property="FontFamily" Value="Calibri" />
                <Setter Property="FontSize" Value="12" />
            </Style>

            <!--  Global TextElement style for consistent Calibri font across all text elements  -->
            <Style TargetType="TextElement">
                <Setter Property="FontFamily" Value="Calibri" />
                <Setter Property="FontSize" Value="12" />
            </Style>

            <!--  Activity Management Converters  -->
            <converters:ActivityHintTextConverter x:Key="ActivityHintTextConverter" />
            <converters:ActivityDescriptionReadOnlyConverter x:Key="ActivityDescriptionReadOnlyConverter" />
            <converters:ActivityStatusItemsComboBoxConverter x:Key="ActivityStatusItemsComboBoxConverter" />
            <converters:ActivityCodeVisibilityConverter x:Key="ActivityCodeVisibilityConverter" />
            <converters:ActivityCodeMaxLengthConverter x:Key="ActivityCodeMaxLengthConverter" />
            <converters:ActivityStatusVisibilityConverter x:Key="ActivityStatusVisibilityConverter" />
            <converters:MultipleActivitiesVisibilityConverter x:Key="MultipleActivitiesVisibilityConverter" />
            <converters:CraftActivityButtonVisibilityConverter x:Key="CraftActivityButtonVisibilityConverter" />

            <!--  File Check Management Converters  -->
            <converters:ActivityFileCheckVisibilityConverter x:Key="ActivityFileCheckVisibilityConverter" />

            <!--  Phone Type Management Converters  -->
            <converters:PhoneTypeMultiValueConverter x:Key="PhoneTypeMultiValueConverter" />

            <!--  Notes Management Converters  -->
            <converters:PriorityToThemeColorConverter x:Key="PriorityToThemeColorConverter" />

            <!--  General UI Converters  -->
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />


        </ResourceDictionary>
    </Application.Resources>
</Application>
