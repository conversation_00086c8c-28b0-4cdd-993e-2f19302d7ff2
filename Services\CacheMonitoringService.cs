using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UFU2.Common;
using UFU2.Services.Interfaces;

namespace UFU2.Services
{
    /// <summary>
    /// Comprehensive cache monitoring and cleanup service for UFU2 application.
    /// Provides cache size monitoring, automatic cleanup, health monitoring, and statistics collection.
    /// Implements Day 3 Task 3.2 requirements for cache monitoring and cleanup.
    /// </summary>
    public class CacheMonitoringService : IDisposable
    {
        #region Private Fields

        private readonly CacheCoordinatorService _cacheCoordinator;
        private readonly Timer _monitoringTimer;
        private readonly Timer _cleanupTimer;
        private readonly Timer _statisticsCollectionTimer;
        private readonly ConcurrentDictionary<string, CacheMetrics> _cacheMetrics;
        private readonly ConcurrentQueue<CacheEvent> _cacheEvents;
        private readonly SemaphoreSlim _monitoringLock;
        private bool _disposed = false;

        // Configuration
        private readonly TimeSpan _monitoringInterval = TimeSpan.FromMinutes(2);
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(15);
        private readonly TimeSpan _statisticsInterval = TimeSpan.FromMinutes(5);
        private readonly int _maxCacheEvents = 1000;
        private readonly double _memoryThresholdMB = 100.0; // 100MB threshold
        private readonly double _lowHitRatioThreshold = 0.4; // 40% hit ratio threshold

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the CacheMonitoringService.
        /// </summary>
        /// <param name="cacheCoordinator">The cache coordinator service</param>
        public CacheMonitoringService(CacheCoordinatorService cacheCoordinator)
        {
            _cacheCoordinator = cacheCoordinator ?? throw new ArgumentNullException(nameof(cacheCoordinator));
            _cacheMetrics = new ConcurrentDictionary<string, CacheMetrics>();
            _cacheEvents = new ConcurrentQueue<CacheEvent>();
            _monitoringLock = new SemaphoreSlim(1, 1);

            // Start monitoring timer
            _monitoringTimer = new Timer(PerformMonitoring, null, _monitoringInterval, _monitoringInterval);

            // Start cleanup timer
            _cleanupTimer = new Timer(PerformAutomaticCleanup, null, _cleanupInterval, _cleanupInterval);

            // Start statistics collection timer
            _statisticsCollectionTimer = new Timer(CollectStatistics, null, _statisticsInterval, _statisticsInterval);

            LoggingService.LogInfo("CacheMonitoringService initialized with monitoring, cleanup, and statistics collection", "CacheMonitoringService");
        }

        #endregion

        #region Cache Monitoring

        /// <summary>
        /// Performs comprehensive cache monitoring (timer callback).
        /// </summary>
        /// <param name="state">Timer state (not used)</param>
        private void PerformMonitoring(object? state)
        {
            try
            {
                LoggingService.LogDebug("Performing cache monitoring", "CacheMonitoringService");

                var healthInfo = _cacheCoordinator.GetOverallCacheHealth();
                var currentTime = DateTime.UtcNow;

                if (healthInfo == null)
                {
                    LoggingService.LogWarning("GetOverallCacheHealth returned null", "CacheMonitoringService");
                    return;
                }

                foreach (var kvp in healthInfo)
                {
                    var serviceName = kvp.Key;
                    var health = kvp.Value;

                    // Skip null or invalid entries
                    if (string.IsNullOrEmpty(serviceName))
                    {
                        LoggingService.LogWarning("Encountered null or empty service name in health info", "CacheMonitoringService");
                        continue;
                    }

                    if (health == null)
                    {
                        LoggingService.LogWarning($"Health info is null for service {serviceName}", "CacheMonitoringService");
                        continue;
                    }

                    try
                    {
                        // Update metrics
                        var metrics = _cacheMetrics.GetOrAdd(serviceName, _ => new CacheMetrics(serviceName));
                        if (metrics != null)
                        {
                            metrics.UpdateMetrics(health, currentTime);
                        }

                        // Check for issues and log events
                        CheckCacheHealth(serviceName, health, metrics);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error processing health info for service {serviceName}: {ex.Message}", "CacheMonitoringService");
                    }
                }

                // Log overall monitoring summary
                var totalMemoryMB = healthInfo.Values.Sum(h => h?.MemoryUsageBytes ?? 0) / 1024.0 / 1024.0;
                var avgHitRatio = CalculateSafeAverageHitRatio(healthInfo.Values);
                var unhealthyCount = healthInfo.Values.Count(h => h != null && !h.IsHealthy);

                LoggingService.LogInfo($"Cache monitoring summary - Services: {healthInfo.Count}, Unhealthy: {unhealthyCount}, Total memory: {totalMemoryMB:F1}MB, Avg hit ratio: {avgHitRatio:P1}", "CacheMonitoringService");

                // Trigger alerts if needed
                if (totalMemoryMB > _memoryThresholdMB)
                {
                    LogCacheEvent(CacheEventType.HighMemoryUsage, "Global", $"Total cache memory usage ({totalMemoryMB:F1}MB) exceeds threshold ({_memoryThresholdMB:F1}MB)");
                }

                if (avgHitRatio < _lowHitRatioThreshold)
                {
                    LogCacheEvent(CacheEventType.LowHitRatio, "Global", $"Average hit ratio ({avgHitRatio:P1}) below threshold ({_lowHitRatioThreshold:P1})");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cache monitoring: {ex.Message}", "CacheMonitoringService");
            }
        }

        /// <summary>
        /// Checks the health of a specific cache service and logs events.
        /// </summary>
        /// <param name="serviceName">Name of the service</param>
        /// <param name="health">Cache health information</param>
        /// <param name="metrics">Cache metrics for the service</param>
        private void CheckCacheHealth(string serviceName, CacheHealthInfo health, CacheMetrics metrics)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrEmpty(serviceName))
                {
                    LoggingService.LogWarning("Service name is null or empty in CheckCacheHealth", "CacheMonitoringService");
                    return;
                }

                if (health == null)
                {
                    LoggingService.LogWarning($"CacheHealthInfo is null for service {serviceName}", "CacheMonitoringService");
                    return;
                }

                if (metrics == null)
                {
                    LoggingService.LogWarning($"CacheMetrics is null for service {serviceName}", "CacheMonitoringService");
                    return;
                }

                // Check memory usage
                var memoryMB = health.MemoryUsageBytes / 1024.0 / 1024.0;
                if (memoryMB > _memoryThresholdMB / 4) // 25MB per service threshold
                {
                    LogCacheEvent(CacheEventType.HighMemoryUsage, serviceName, $"Memory usage: {memoryMB:F1}MB");
                }

                // Check hit ratio
                if (health.HitRatio < _lowHitRatioThreshold && health.ItemCount > 0)
                {
                    LogCacheEvent(CacheEventType.LowHitRatio, serviceName, $"Hit ratio: {health.HitRatio:P1}");
                }

                // Check if cache is unhealthy
                if (!health.IsHealthy)
                {
                    var healthStatus = string.IsNullOrEmpty(health.HealthStatus) ? "Unknown health issue" : health.HealthStatus;
                    LogCacheEvent(CacheEventType.UnhealthyCache, serviceName, healthStatus);
                }

                // Check for rapid growth in cache size
                if (metrics.PreviousItemCount > 0)
                {
                    var growthRate = (double)(health.ItemCount - metrics.PreviousItemCount) / metrics.PreviousItemCount;
                    if (growthRate > 0.5) // 50% growth
                    {
                        LogCacheEvent(CacheEventType.RapidGrowth, serviceName, $"Cache size grew by {growthRate:P1} (from {metrics.PreviousItemCount} to {health.ItemCount} items)");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking cache health for service {serviceName}: {ex.Message}", "CacheMonitoringService");
            }
        }

        /// <summary>
        /// Logs a cache event for monitoring and alerting.
        /// </summary>
        /// <param name="eventType">Type of cache event</param>
        /// <param name="serviceName">Name of the affected service</param>
        /// <param name="details">Event details</param>
        private void LogCacheEvent(CacheEventType eventType, string serviceName, string details)
        {
            var cacheEvent = new CacheEvent
            {
                EventType = eventType,
                ServiceName = serviceName,
                Details = details,
                Timestamp = DateTime.UtcNow
            };

            _cacheEvents.Enqueue(cacheEvent);

            // Trim events if queue gets too large
            while (_cacheEvents.Count > _maxCacheEvents)
            {
                _cacheEvents.TryDequeue(out _);
            }

            // Log based on severity
            var logLevel = eventType switch
            {
                CacheEventType.HighMemoryUsage => "Warning",
                CacheEventType.LowHitRatio => "Warning",
                CacheEventType.UnhealthyCache => "Error",
                CacheEventType.RapidGrowth => "Info",
                CacheEventType.CleanupPerformed => "Info",
                _ => "Info"
            };

            LoggingService.LogInfo($"Cache event [{eventType}] for {serviceName}: {details}", "CacheMonitoringService");
        }

        /// <summary>
        /// Safely calculates the average hit ratio from a collection of CacheHealthInfo objects.
        /// Handles null values and empty collections to prevent NullReferenceException.
        /// </summary>
        /// <param name="healthInfoCollection">Collection of CacheHealthInfo objects</param>
        /// <returns>Average hit ratio, or 0.0 if no valid data is available</returns>
        private static double CalculateSafeAverageHitRatio(IEnumerable<CacheHealthInfo> healthInfoCollection)
        {
            try
            {
                if (healthInfoCollection == null)
                {
                    LoggingService.LogWarning("HealthInfo collection is null when calculating average hit ratio", "CacheMonitoringService");
                    return 0.0;
                }

                var validHealthInfos = healthInfoCollection
                    .Where(h => h != null && h.HitRatio > 0)
                    .ToList();

                if (!validHealthInfos.Any())
                {
                    LoggingService.LogDebug("No valid health info with positive hit ratio found", "CacheMonitoringService");
                    return 0.0;
                }

                return validHealthInfos.Average(h => h.HitRatio);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating safe average hit ratio: {ex.Message}", "CacheMonitoringService");
                return 0.0;
            }
        }

        #endregion

        #region Automatic Cleanup

        /// <summary>
        /// Performs automatic cache cleanup based on monitoring data (timer callback).
        /// </summary>
        /// <param name="state">Timer state (not used)</param>
        private void PerformAutomaticCleanup(object? state)
        {
            try
            {
                LoggingService.LogDebug("Performing automatic cache cleanup", "CacheMonitoringService");

                var healthInfo = _cacheCoordinator.GetOverallCacheHealth();
                var servicesToCleanup = new List<string>();

                foreach (var kvp in healthInfo)
                {
                    var serviceName = kvp.Key;
                    var health = kvp.Value;

                    // Determine if cleanup is needed
                    var needsCleanup = ShouldPerformCleanup(serviceName, health);
                    if (needsCleanup)
                    {
                        servicesToCleanup.Add(serviceName);
                    }
                }

                if (servicesToCleanup.Any())
                {
                    LoggingService.LogInfo($"Performing cleanup for {servicesToCleanup.Count} services: {string.Join(", ", servicesToCleanup)}", "CacheMonitoringService");

                    // Perform cleanup
                    Task.Run(async () =>
                    {
                        try
                        {
                            await PerformCleanupForServices(servicesToCleanup);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error during automatic cleanup: {ex.Message}", "CacheMonitoringService");
                        }
                    });
                }
                else
                {
                    LoggingService.LogDebug("No services require cleanup at this time", "CacheMonitoringService");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during automatic cleanup: {ex.Message}", "CacheMonitoringService");
            }
        }

        /// <summary>
        /// Determines if a service should have its cache cleaned up.
        /// </summary>
        /// <param name="serviceName">Name of the service</param>
        /// <param name="health">Cache health information</param>
        /// <returns>True if cleanup should be performed</returns>
        private bool ShouldPerformCleanup(string serviceName, CacheHealthInfo health)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrEmpty(serviceName))
                {
                    LoggingService.LogWarning("Service name is null or empty in ShouldPerformCleanup", "CacheMonitoringService");
                    return false;
                }

                if (health == null)
                {
                    LoggingService.LogWarning($"CacheHealthInfo is null for service {serviceName} in ShouldPerformCleanup", "CacheMonitoringService");
                    return false;
                }

                // Cleanup if memory usage is high
                var memoryMB = health.MemoryUsageBytes / 1024.0 / 1024.0;
                if (memoryMB > _memoryThresholdMB / 4) // 25MB per service
                {
                    return true;
                }

                // Cleanup if hit ratio is very low
                if (health.HitRatio < 0.2 && health.ItemCount > 10) // Less than 20% hit ratio with some items
                {
                    return true;
                }

                // Cleanup if cache is unhealthy
                if (!health.IsHealthy && health.ItemCount > 0)
                {
                    return true;
                }

                // Check metrics for additional cleanup criteria
                if (_cacheMetrics.TryGetValue(serviceName, out var metrics) && metrics != null)
                {
                    // Cleanup if cache hasn't been accessed recently
                    var timeSinceLastAccess = DateTime.UtcNow - metrics.LastAccessTime;
                    if (timeSinceLastAccess > TimeSpan.FromHours(2) && health.ItemCount > 0)
                    {
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error determining cleanup for service {serviceName}: {ex.Message}", "CacheMonitoringService");
                return false; // Conservative approach - don't cleanup if there's an error
            }
        }

        /// <summary>
        /// Performs cleanup for specific services.
        /// </summary>
        /// <param name="serviceNames">List of service names to clean up</param>
        private async Task PerformCleanupForServices(List<string> serviceNames)
        {
            foreach (var serviceName in serviceNames)
            {
                try
                {
                    // Create invalidation context for cleanup
                    var invalidationContext = new CacheInvalidationContext
                    {
                        DataType = "Cleanup",
                        InvalidationType = CacheInvalidationType.Full,
                        AdditionalContext = new Dictionary<string, object>
                        {
                            ["Reason"] = "Automatic cleanup",
                            ["ServiceName"] = serviceName
                        }
                    };

                    // Perform coordinated invalidation (which includes cleanup)
                    await _cacheCoordinator.CoordinateInvalidationAsync(invalidationContext);

                    LogCacheEvent(CacheEventType.CleanupPerformed, serviceName, "Automatic cleanup completed");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error cleaning up cache for service {serviceName}: {ex.Message}", "CacheMonitoringService");
                }
            }
        }

        #endregion

        #region Statistics Collection

        /// <summary>
        /// Collects cache statistics for analysis and reporting (timer callback).
        /// </summary>
        /// <param name="state">Timer state (not used)</param>
        private void CollectStatistics(object? state)
        {
            try
            {
                LoggingService.LogDebug("Collecting cache statistics", "CacheMonitoringService");

                var allStats = _cacheCoordinator.GetAllCacheStatistics();
                var healthInfo = _cacheCoordinator.GetOverallCacheHealth();

                foreach (var kvp in allStats)
                {
                    var serviceName = kvp.Key;
                    var stats = kvp.Value;

                    if (_cacheMetrics.TryGetValue(serviceName, out var metrics))
                    {
                        metrics.UpdateStatistics(stats);
                    }
                }

                // Log aggregated statistics
                LogAggregatedStatistics(allStats, healthInfo);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error collecting cache statistics: {ex.Message}", "CacheMonitoringService");
            }
        }

        /// <summary>
        /// Logs aggregated cache statistics for monitoring.
        /// </summary>
        /// <param name="allStats">All cache statistics</param>
        /// <param name="healthInfo">All cache health information</param>
        private void LogAggregatedStatistics(Dictionary<string, Dictionary<string, object>> allStats, Dictionary<string, CacheHealthInfo> healthInfo)
        {
            try
            {
                var totalServices = allStats.Count;
                var totalMemoryMB = healthInfo.Values.Sum(h => h?.MemoryUsageBytes ?? 0) / 1024.0 / 1024.0;
                var totalItems = healthInfo.Values.Sum(h => h?.ItemCount ?? 0);
                var avgHitRatio = CalculateSafeAverageHitRatio(healthInfo.Values);
                var healthyServices = healthInfo.Values.Count(h => h != null && h.IsHealthy);

                LoggingService.LogInfo($"Cache statistics - Services: {totalServices} ({healthyServices} healthy), Items: {totalItems}, Memory: {totalMemoryMB:F1}MB, Avg hit ratio: {avgHitRatio:P1}", "CacheMonitoringService");

                // Log per-service statistics
                foreach (var serviceName in allStats.Keys)
                {
                    if (healthInfo.TryGetValue(serviceName, out var health))
                    {
                        var memoryMB = health.MemoryUsageBytes / 1024.0 / 1024.0;
                        LoggingService.LogDebug($"Service {serviceName}: {health.ItemCount} items, {memoryMB:F1}MB, {health.HitRatio:P1} hit ratio, {(health.IsHealthy ? "healthy" : "unhealthy")}", "CacheMonitoringService");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error logging aggregated statistics: {ex.Message}", "CacheMonitoringService");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets comprehensive cache monitoring report.
        /// </summary>
        /// <returns>Cache monitoring report</returns>
        public CacheMonitoringReport GetMonitoringReport()
        {
            try
            {
                var healthInfo = _cacheCoordinator.GetOverallCacheHealth();
                var allStats = _cacheCoordinator.GetAllCacheStatistics();

                var report = new CacheMonitoringReport
                {
                    GeneratedAt = DateTime.UtcNow,
                    TotalServices = healthInfo.Count,
                    HealthyServices = healthInfo.Values.Count(h => h != null && h.IsHealthy),
                    TotalMemoryUsageBytes = healthInfo.Values.Sum(h => h?.MemoryUsageBytes ?? 0),
                    TotalItems = healthInfo.Values.Sum(h => h?.ItemCount ?? 0),
                    AverageHitRatio = CalculateSafeAverageHitRatio(healthInfo.Values),
                    ServiceMetrics = _cacheMetrics.Values.ToList(),
                    RecentEvents = _cacheEvents.TakeLast(50).ToList()
                };

                return report;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating monitoring report: {ex.Message}", "CacheMonitoringService");
                return new CacheMonitoringReport
                {
                    GeneratedAt = DateTime.UtcNow,
                    TotalServices = 0,
                    HealthyServices = 0,
                    TotalMemoryUsageBytes = 0,
                    TotalItems = 0,
                    AverageHitRatio = 0.0,
                    ServiceMetrics = new List<CacheMetrics>(),
                    RecentEvents = new List<CacheEvent>()
                };
            }
        }

        /// <summary>
        /// Forces immediate cache monitoring and cleanup.
        /// </summary>
        public async Task ForceMonitoringAndCleanupAsync()
        {
            await _monitoringLock.WaitAsync();
            try
            {
                LoggingService.LogInfo("Forcing immediate cache monitoring and cleanup", "CacheMonitoringService");

                // Perform monitoring
                PerformMonitoring(null);

                // Perform cleanup
                PerformAutomaticCleanup(null);

                // Collect statistics
                CollectStatistics(null);
            }
            finally
            {
                _monitoringLock.Release();
            }
        }

        /// <summary>
        /// Gets recent cache events for analysis.
        /// </summary>
        /// <param name="count">Number of recent events to retrieve</param>
        /// <returns>List of recent cache events</returns>
        public List<CacheEvent> GetRecentEvents(int count = 100)
        {
            return _cacheEvents.TakeLast(Math.Min(count, _maxCacheEvents)).ToList();
        }

        /// <summary>
        /// Gets cache metrics for a specific service.
        /// </summary>
        /// <param name="serviceName">Name of the service</param>
        /// <returns>Cache metrics for the service, or null if not found</returns>
        public CacheMetrics? GetServiceMetrics(string serviceName)
        {
            return _cacheMetrics.TryGetValue(serviceName, out var metrics) ? metrics : null;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the cache monitoring service and its resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;

                try
                {
                    _monitoringTimer?.Dispose();
                    _cleanupTimer?.Dispose();
                    _statisticsCollectionTimer?.Dispose();
                    _monitoringLock?.Dispose();

                    LoggingService.LogInfo("CacheMonitoringService disposed", "CacheMonitoringService");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error disposing CacheMonitoringService: {ex.Message}", "CacheMonitoringService");
                }
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Cache metrics for a specific service.
    /// </summary>
    public class CacheMetrics
    {
        public string ServiceName { get; set; }
        public DateTime LastUpdateTime { get; set; }
        public DateTime LastAccessTime { get; set; }
        public int CurrentItemCount { get; set; }
        public int PreviousItemCount { get; set; }
        public long CurrentMemoryUsageBytes { get; set; }
        public double CurrentHitRatio { get; set; }
        public bool IsCurrentlyHealthy { get; set; }
        public int TotalMonitoringCycles { get; set; }
        public int UnhealthyCycles { get; set; }
        public double AverageHitRatio { get; set; }
        public long PeakMemoryUsageBytes { get; set; }
        public int PeakItemCount { get; set; }

        public CacheMetrics(string serviceName)
        {
            ServiceName = serviceName;
            LastUpdateTime = DateTime.UtcNow;
            LastAccessTime = DateTime.UtcNow;
        }

        public void UpdateMetrics(CacheHealthInfo health, DateTime updateTime)
        {
            PreviousItemCount = CurrentItemCount;
            CurrentItemCount = health.ItemCount;
            CurrentMemoryUsageBytes = health.MemoryUsageBytes;
            CurrentHitRatio = health.HitRatio;
            IsCurrentlyHealthy = health.IsHealthy;
            LastUpdateTime = updateTime;

            if (health.ItemCount > 0)
            {
                LastAccessTime = updateTime;
            }

            TotalMonitoringCycles++;
            if (!health.IsHealthy)
            {
                UnhealthyCycles++;
            }

            // Update peaks
            if (health.MemoryUsageBytes > PeakMemoryUsageBytes)
            {
                PeakMemoryUsageBytes = health.MemoryUsageBytes;
            }

            if (health.ItemCount > PeakItemCount)
            {
                PeakItemCount = health.ItemCount;
            }

            // Update average hit ratio
            if (TotalMonitoringCycles == 1)
            {
                AverageHitRatio = health.HitRatio;
            }
            else
            {
                AverageHitRatio = ((AverageHitRatio * (TotalMonitoringCycles - 1)) + health.HitRatio) / TotalMonitoringCycles;
            }
        }

        public void UpdateStatistics(Dictionary<string, object> stats)
        {
            // Additional statistics can be updated here if needed
        }
    }

    /// <summary>
    /// Cache event for monitoring and alerting.
    /// </summary>
    public class CacheEvent
    {
        public CacheEventType EventType { get; set; }
        public string ServiceName { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Types of cache events.
    /// </summary>
    public enum CacheEventType
    {
        HighMemoryUsage,
        LowHitRatio,
        UnhealthyCache,
        RapidGrowth,
        CleanupPerformed
    }

    /// <summary>
    /// Comprehensive cache monitoring report.
    /// </summary>
    public class CacheMonitoringReport
    {
        public DateTime GeneratedAt { get; set; }
        public int TotalServices { get; set; }
        public int HealthyServices { get; set; }
        public long TotalMemoryUsageBytes { get; set; }
        public int TotalItems { get; set; }
        public double AverageHitRatio { get; set; }
        public List<CacheMetrics> ServiceMetrics { get; set; } = new List<CacheMetrics>();
        public List<CacheEvent> RecentEvents { get; set; } = new List<CacheEvent>();
    }

    #endregion
}