=== UFU2 Application Session Started at 2025-08-09 21:15:48 ===
[2025-08-09 21:15:48.495]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 21:15:48.502]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 21:15:48.508]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 21:15:48.511]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 21:15:48.522]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 21:15:48.528]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 21:15:48.533]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 21:15:48.539]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 21:15:48.545]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 21:15:48.549]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 21:15:48.555]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 21:15:48.559]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 21:15:48.566]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 21:15:48.569]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 21:15:48.574]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 21:15:48.578]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 21:15:48.582]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 21:15:48.585]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 21:15:48.594]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 21:15:48.599]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 21:15:48.603]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 21:15:48.607]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 21:15:48.610]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 21:15:48.618]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 21:15:48.622]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 21:15:48.626]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 21:15:48.630]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 21:15:48.635]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 21:15:48.639]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 21:15:48.643]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 21:15:48.647]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 21:15:48.652]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 21:15:48.656]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 21:15:48.659]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 21:15:48.662]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 21:15:48.666]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 21:15:48.670]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 21:15:48.673]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 21:15:48.687]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 104.43MB working set
[2025-08-09 21:15:48.692]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 21:15:48.696]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 21:15:48.699]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 21:15:48.703]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 21:15:48.707]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 21:15:48.714]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 21:15:48.739]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 21:15:48.743]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 21:15:48.746]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 21:15:48.992]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 21:15:48.996]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 21:15:48.999]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 21:15:49.003]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 21:15:49.009]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_28090709_638903673490084654 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 21:15:49.013]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 21:15:49.017]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:15:49.020]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 21:15:49.031]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 21:15:49.037]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 21:15:49.041]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 21:15:49.045]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 21:15:49.049]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 21:15:49.052]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 21:15:49.056]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 21:15:49.059]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 21:15:49.062]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 21:15:49.066]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 21:15:49.071]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 21:15:49.075]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 21:15:49.078]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 21:15:49.081]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 21:15:49.085]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 21:15:49.089]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 21:15:49.093]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 21:15:49.097]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 21:15:49.101]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 21:15:49.104]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 21:15:49.108]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 21:15:49.111]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 21:15:49.115]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 21:15:49.119]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 21:15:49.124]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 21:15:49.127]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 21:15:49.131]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 21:15:49.135]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 21:15:49.139]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 21:15:49.143]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 21:15:49.147]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 21:15:49.151]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 21:15:49.155]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 21:15:49.159]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 21:15:49.164]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 21:15:49.167]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 21:15:49.172]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 21:15:49.176]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 21:15:49.180]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 21:15:49.185]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 21:15:49.189]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 21:15:49.192]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 21:15:49.196]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 21:15:49.200]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 21:15:49.204]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 21:15:49.207]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 21:15:49.211]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 21:15:49.214]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 21:15:49.218]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 21:15:49.222]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 21:15:49.226]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 21:15:49.230]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 21:15:49.234]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 21:15:49.238]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 21:15:49.243]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 21:15:49.247]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 21:15:49.251]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 21:15:49.255]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 21:15:49.263]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 21:15:49.267]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 21:15:49.329]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 21:15:49.338]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 624ms
[2025-08-09 21:15:49.342]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 21:15:49.348]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 21:15:49.365]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 21:15:49.369]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 21:15:49.374]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 21:15:49.379]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 21:15:49.383]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 21:15:49.387]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 21:15:49.392]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 21:15:49.395]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 21:15:49.399]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 21:15:49.405]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 21:15:49.410]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 21:15:49.416]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 21:15:49.420]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 21:15:49.425]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 21:15:49.471]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 21:15:49.475]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 21:15:49.480]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 21:15:49.472]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 21:15:49.488]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 21:15:49.492]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 21:15:49.478]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 21:15:49.500]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 21:15:49.503]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 21:15:49.473]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 21:15:49.511]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-09 21:15:49.515]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 21:15:49.519]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 21:15:49.523]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 21:15:49.526]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 21:15:49.530]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 21:15:49.534]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 21:15:49.537]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 21:15:49.541]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 21:15:49.557]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-09 21:15:49.560]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-09 21:15:49.570]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-09 21:15:49.583]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.UFU2_Schema.sql
[2025-08-09 21:15:49.609]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.UFU2_Schema.sql (19126 characters)
[2025-08-09 21:15:49.629]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-09 21:15:49.634]  	[DEBUG]		[DatabaseMigrationService]	Executing 55 SQL statements
[2025-08-09 21:15:49.644]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/55 statements
[2025-08-09 21:15:49.650]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/55 statements
[2025-08-09 21:15:49.661]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/55 statements
[2025-08-09 21:15:49.666]  	[DEBUG]		[DatabaseMigrationService]	Executed 40/55 statements
[2025-08-09 21:15:49.672]  	[DEBUG]		[DatabaseMigrationService]	Executed 50/55 statements
[2025-08-09 21:15:49.701]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-09 21:15:49.720]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (55 statements executed)
[2025-08-09 21:15:49.724]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-09 21:15:49.730]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 21:15:49.734]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:49.740]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 21:15:49.744]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 21:15:49.748]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 21:15:49.752]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 21:15:49.756]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 21:15:49.760]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 21:15:49.764]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 21:15:49.768]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 21:15:49.772]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 21:15:49.775]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 21:15:49.780]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 21:15:49.784]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 21:15:49.789]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 21:15:49.793]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 21:15:49.797]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 21:15:49.801]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 21:15:49.805]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 21:15:49.808]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 21:15:49.812]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 21:15:49.816]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 21:15:49.820]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 21:15:49.827]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 21:15:49.831]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 21:15:49.836]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 21:15:49.841]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 21:15:49.845]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 21:15:49.848]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 21:15:49.852]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 21:15:49.858]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 21:15:49.862]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:49.866]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 21:15:49.869]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 21:15:49.873]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:49.877]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 21:15:49.880]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 21:15:49.884]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 21:15:49.888]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 21:15:49.892]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 21:15:49.895]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 21:15:49.899]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 21:15:49.903]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 21:15:49.908]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-09 21:15:49.912]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-09 21:15:49.916]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-09 21:15:49.920]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.APP_Schema.sql
[2025-08-09 21:15:49.924]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.APP_Schema.sql (9406 characters)
[2025-08-09 21:15:49.928]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-09 21:15:49.932]  	[DEBUG]		[DatabaseMigrationService]	Executing 30 SQL statements
[2025-08-09 21:15:49.936]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/30 statements
[2025-08-09 21:15:49.943]  	[DEBUG]		[DatabaseMigrationService]	Executed 20/30 statements
[2025-08-09 21:15:49.948]  	[DEBUG]		[DatabaseMigrationService]	Executed 30/30 statements
[2025-08-09 21:15:49.953]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-09 21:15:49.968]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (30 statements executed)
[2025-08-09 21:15:49.973]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-09 21:15:49.977]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 21:15:49.981]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:49.985]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 21:15:49.990]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 21:15:49.994]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 21:15:49.998]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 21:15:50.002]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 21:15:50.007]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 21:15:50.011]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 21:15:50.016]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 21:15:50.020]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 21:15:50.024]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 21:15:50.028]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 21:15:50.033]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 21:15:50.037]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 21:15:50.041]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 21:15:50.045]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 21:15:50.049]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 21:15:50.053]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 21:15:50.057]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 21:15:50.061]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 21:15:50.065]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:50.069]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 21:15:50.073]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 21:15:50.077]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:50.081]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 21:15:50.085]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 21:15:50.089]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 21:15:50.093]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 21:15:50.096]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 21:15:50.100]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 21:15:50.104]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 21:15:50.108]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 21:15:50.114]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 0, target version: 5
[2025-08-09 21:15:50.118]  	[DEBUG]		[DatabaseMigrationService]	New database detected, creating initial schema
[2025-08-09 21:15:50.121]  	[DEBUG]		[DatabaseMigrationService]	Starting initial schema creation
[2025-08-09 21:15:50.125]  	[DEBUG]		[DatabaseMigrationService]	Reading schema from embedded resource: UFU2.Database.Archive_Schema.sql
[2025-08-09 21:15:50.129]  	[DEBUG]		[DatabaseMigrationService]	Successfully read embedded resource: UFU2.Database.Archive_Schema.sql (7159 characters)
[2025-08-09 21:15:50.133]  	[DEBUG]		[DatabaseMigrationService]	Schema SQL loaded from embedded resource successfully
[2025-08-09 21:15:50.137]  	[DEBUG]		[DatabaseMigrationService]	Executing 16 SQL statements
[2025-08-09 21:15:50.143]  	[DEBUG]		[DatabaseMigrationService]	Executed 10/16 statements
[2025-08-09 21:15:50.148]  	[DEBUG]		[DatabaseMigrationService]	Updated schema version to 5: Initial UFU2 database schema
[2025-08-09 21:15:50.165]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema applied successfully (16 statements executed)
[2025-08-09 21:15:50.169]  	[DEBUG]		[DatabaseMigrationService]	Initial database schema created successfully
[2025-08-09 21:15:50.173]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 21:15:50.178]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:50.182]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 21:15:50.187]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 21:15:50.192]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 21:15:50.197]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 21:15:50.211]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 21:15:50.216]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 21:15:50.222]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 21:15:50.227]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 21:15:50.232]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 21:15:50.236]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 21:15:50.242]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 21:15:50.247]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 21:15:50.252]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 21:15:50.257]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 21:15:50.262]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 21:15:50.266]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 21:15:50.270]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 21:15:50.274]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:50.278]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 21:15:50.282]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:50.286]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 21:15:50.291]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 21:15:50.295]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 21:15:50.299]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 21:15:50.303]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 21:15:50.308]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 21:15:50.313]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 21:15:50.317]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 21:15:50.321]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 21:15:50.326]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 21:15:50.330]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 21:15:50.335]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 21:15:50.340]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 21:15:50.344]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 21:15:50.349]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 21:15:50.353]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 21:15:50.358]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 21:15:50.362]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 21:15:50.366]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 21:15:50.371]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 21:15:50.375]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 21:15:50.379]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 21:15:50.383]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 21:15:50.387]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 21:15:50.391]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 21:15:50.396]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 21:15:50.400]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 21:15:50.404]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 21:15:50.408]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 21:15:50.413]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 21:15:50.417]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 21:15:50.422]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 21:15:50.428]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 21:15:50.432]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 21:15:50.438]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 21:15:50.443]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 21:15:50.448]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 21:15:50.453]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 21:15:50.458]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 21:15:50.463]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 21:15:50.468]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 21:15:50.474]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 21:15:50.479]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 21:15:50.492]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 21:15:50.505]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 21:15:50.516]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 21:15:50.530]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 21:15:50.536]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 21:15:50.542]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 21:15:50.548]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 21:15:50.553]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:50.560]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 21:15:50.567]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 21:15:50.573]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 21:15:50.581]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 21:15:50.586]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 21:15:50.591]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 21:15:50.596]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 21:15:50.601]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 21:15:50.606]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 21:15:50.619]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 21:15:50.632]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 21:15:50.644]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 21:15:50.650]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 21:15:50.658]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:50.664]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 21:15:50.674]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:50.819]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:50.877]  	[INFO]		[ServiceLocator]	Seeding activity type data from embedded resource
[2025-08-09 21:15:50.902]  	[DEBUG]		[ActivityTypeBaseService]	Starting import from embedded JSON resource
[2025-08-09 21:15:51.148]  	[INFO]		[ActivityTypeBaseService]	Parsed 1028 activity types from JSON
[2025-08-09 21:15:51.157]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:51.178]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 1/11 (100 records)
[2025-08-09 21:15:51.194]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 2/11 (100 records)
[2025-08-09 21:15:51.209]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 3/11 (100 records)
[2025-08-09 21:15:51.226]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 4/11 (100 records)
[2025-08-09 21:15:51.257]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 5/11 (100 records)
[2025-08-09 21:15:51.281]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 6/11 (100 records)
[2025-08-09 21:15:51.302]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 7/11 (100 records)
[2025-08-09 21:15:51.332]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 8/11 (100 records)
[2025-08-09 21:15:51.357]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 9/11 (100 records)
[2025-08-09 21:15:51.384]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 10/11 (100 records)
[2025-08-09 21:15:51.426]  	[DEBUG]		[ActivityTypeBaseService]	Imported batch 11/11 (28 records)
[2025-08-09 21:15:51.435]  	[INFO]		[ActivityTypeBaseService]	Successfully imported 1028 activity types
[2025-08-09 21:15:51.443]  	[INFO]		[ActivityTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-09 21:15:51.456]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 21:15:51.741]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:15:51.818]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 21:15:51.823]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 21:15:51.829]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 1028 نوع نشاط بنجاح
[2025-08-09 21:15:51.890]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-09 21:15:51.906]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 21:15:51.914]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-09 21:15:51.923]  	[INFO]		[ServiceLocator]	Successfully imported 1028 activity types
[2025-08-09 21:15:51.943]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:51.950]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 21:15:51.957]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:51.964]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:51.972]  	[INFO]		[ServiceLocator]	Seeding craft type data from embedded resource
[2025-08-09 21:15:51.979]  	[DEBUG]		[CraftTypeBaseService]	Starting import from embedded JSON resource
[2025-08-09 21:15:52.034]  	[INFO]		[CraftTypeBaseService]	Found 337 craft types to import
[2025-08-09 21:15:52.039]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:52.110]  	[INFO]		[CraftTypeBaseService]	Successfully imported 337 craft types
[2025-08-09 21:15:52.115]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:52.120]  	[INFO]		[CraftTypeBaseService]	Displaying user success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-09 21:15:52.126]  	[DEBUG]		[ToastService]	Displaying Success toast: تم الاستيراد - تم استيراد 337 نوع حرفة بنجاح
[2025-08-09 21:15:52.140]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم الاستيراد
[2025-08-09 21:15:52.147]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 21:15:52.152]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم الاستيراد
[2025-08-09 21:15:52.160]  	[INFO]		[ServiceLocator]	Successfully imported 337 craft types
[2025-08-09 21:15:52.164]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 21:15:52.171]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 21:15:52.201]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 21:15:52.208]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:52.234]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 21:15:52.239]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 21:15:52.245]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:52.254]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 21:15:52.259]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 21:15:52.264]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 21:15:52.262]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 21:15:52.273]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 21:15:52.277]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 21:15:52.281]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 21:15:52.287]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 21:15:52.292]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 21:15:52.303]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:52.307]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 21:15:52.316]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 21:15:52.326]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 21:15:52.331]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 21:15:52.334]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-09 21:15:52.340]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:52.337]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 21:15:52.349]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:52.345]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 21:15:52.357]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-09 21:15:52.360]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 58ms
[2025-08-09 21:15:52.374]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.00 MB MB size
[2025-08-09 21:15:52.379]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 21:15:52.384]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 21:15:52.390]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 21:15:52.396]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 21:15:52.401]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 21:15:52.406]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 21:15:52.411]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 21:15:52.415]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 21:15:52.420]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 21:15:52.425]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 21:15:52.430]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 21:15:52.433]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 21:15:52.437]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 21:15:52.442]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 21:15:52.446]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 21:15:52.450]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 21:15:52.458]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 21:15:52.464]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 21:15:52.468]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-09 21:15:52.504]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:52.513]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 21:15:52.518]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:52.523]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:52.527]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 21:15:52.531]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:52.536]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:52.541]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 21:15:52.547]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:52.552]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:52.558]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 21:15:52.563]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:52.569]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:52.574]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 21:15:52.579]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:52.607]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:52.723]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 21:15:52.737]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:52.768]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 1
[2025-08-09 21:15:52.835]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 371ms
[2025-08-09 21:15:52.842]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 21:15:52.860]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 21:15:52.867]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 27ms
[2025-08-09 21:15:52.874]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 21:15:52.886]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 21:15:52.893]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 21ms
[2025-08-09 21:15:52.903]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 21:15:52.911]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:52.920]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 21:15:52.930]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:52.949]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 21:15:52.968]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:53.001]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 21:15:53.055]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 21:15:53.097]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 21:15:53.120]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 21:15:53.126]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 224ms
[2025-08-09 21:15:53.132]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 21:15:53.141]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.153]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 21:15:53.160]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.167]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.175]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 21:15:53.180]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.186]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.195]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 21:15:53.206]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.233]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.239]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 21:15:53.244]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.249]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.254]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 21:15:53.259]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.264]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.269]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 21:15:53.274]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.278]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.283]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 21:15:53.289]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.294]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.299]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 21:15:53.304]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.308]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.313]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 21:15:53.318]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.322]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:15:53.327]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 21:15:53.331]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:15:53.336]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 21:15:53.340]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 209ms
[2025-08-09 21:15:53.345]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 21:15:53.350]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 21:15:53.354]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 21:15:53.359]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 21:15:53.364]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 21:15:53.369]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 21:15:53.373]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 21:15:53.377]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 21:15:53.382]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 21:15:53.386]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 21:15:53.390]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 21:15:53.395]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 21:15:53.399]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 21:15:53.403]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 21:15:53.408]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 21:15:53.413]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 21:15:53.418]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 21:15:53.424]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 909%
[2025-08-09 21:15:53.429]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 1,818%
[2025-08-09 21:15:53.433]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 2,727%
[2025-08-09 21:15:53.438]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 3,636%
[2025-08-09 21:15:53.442]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 4,545%
[2025-08-09 21:15:53.446]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 5,455%
[2025-08-09 21:15:53.450]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 6,364%
[2025-08-09 21:15:53.454]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 7,273%
[2025-08-09 21:15:53.460]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 8,182%
[2025-08-09 21:15:53.464]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 9,091%
[2025-08-09 21:15:53.468]  	[DEBUG]		[ServiceLocator]	Activity type import progress: 10,000%
[2025-08-09 21:15:53.472]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 0%
[2025-08-09 21:15:53.477]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-09 21:15:53.481]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-09 21:15:53.485]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-09 21:15:53.489]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 1%
[2025-08-09 21:15:53.494]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-09 21:15:53.498]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-09 21:15:53.502]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 2%
[2025-08-09 21:15:53.506]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-09 21:15:53.510]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-09 21:15:53.515]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 3%
[2025-08-09 21:15:53.519]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-09 21:15:53.524]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-09 21:15:53.528]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-09 21:15:53.532]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 4%
[2025-08-09 21:15:53.537]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-09 21:15:53.542]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-09 21:15:53.546]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 5%
[2025-08-09 21:15:53.550]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-09 21:15:53.554]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-09 21:15:53.559]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 6%
[2025-08-09 21:15:53.562]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-09 21:15:53.566]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-09 21:15:53.571]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-09 21:15:53.575]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 7%
[2025-08-09 21:15:53.579]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-09 21:15:53.585]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-09 21:15:53.589]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 8%
[2025-08-09 21:15:53.594]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-09 21:15:53.598]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-09 21:15:53.602]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-09 21:15:53.607]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 9%
[2025-08-09 21:15:53.611]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-09 21:15:53.616]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-09 21:15:53.621]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 10%
[2025-08-09 21:15:53.625]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-09 21:15:53.629]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-09 21:15:53.634]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 11%
[2025-08-09 21:15:53.639]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-09 21:15:53.644]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-09 21:15:53.648]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-09 21:15:53.653]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 12%
[2025-08-09 21:15:53.658]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-09 21:15:53.662]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-09 21:15:53.666]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 13%
[2025-08-09 21:15:53.671]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-09 21:15:53.676]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-09 21:15:53.680]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 14%
[2025-08-09 21:15:53.684]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-09 21:15:53.689]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-09 21:15:53.693]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-09 21:15:53.698]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 15%
[2025-08-09 21:15:53.702]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-09 21:15:53.707]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-09 21:15:53.712]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 16%
[2025-08-09 21:15:53.717]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-09 21:15:53.722]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-09 21:15:53.727]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 17%
[2025-08-09 21:15:53.736]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-09 21:15:53.741]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-09 21:15:53.747]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-09 21:15:53.751]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 18%
[2025-08-09 21:15:53.756]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-09 21:15:53.761]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-09 21:15:53.766]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 19%
[2025-08-09 21:15:53.771]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-09 21:15:53.777]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-09 21:15:53.781]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-09 21:15:53.787]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 20%
[2025-08-09 21:15:53.792]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-09 21:15:53.797]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-09 21:15:53.801]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 21%
[2025-08-09 21:15:53.815]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-09 21:15:53.820]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-09 21:15:53.825]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 22%
[2025-08-09 21:15:53.829]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-09 21:15:53.833]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-09 21:15:53.837]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-09 21:15:53.843]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 23%
[2025-08-09 21:15:53.847]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-09 21:15:53.852]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-09 21:15:53.857]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 24%
[2025-08-09 21:15:53.862]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-09 21:15:53.866]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-09 21:15:53.871]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 25%
[2025-08-09 21:15:53.876]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-09 21:15:53.880]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-09 21:15:53.885]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-09 21:15:53.889]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 26%
[2025-08-09 21:15:53.893]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-09 21:15:53.898]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-09 21:15:53.902]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 27%
[2025-08-09 21:15:53.907]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-09 21:15:53.912]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-09 21:15:53.916]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-09 21:15:53.921]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 28%
[2025-08-09 21:15:53.925]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-09 21:15:53.929]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-09 21:15:53.933]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 29%
[2025-08-09 21:15:53.940]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-09 21:15:53.944]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-09 21:15:53.948]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 30%
[2025-08-09 21:15:53.952]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-09 21:15:53.957]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-09 21:15:53.960]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-09 21:15:53.964]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 31%
[2025-08-09 21:15:53.969]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-09 21:15:53.973]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-09 21:15:53.977]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 32%
[2025-08-09 21:15:53.982]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-09 21:15:53.991]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-09 21:15:53.995]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 33%
[2025-08-09 21:15:53.999]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-09 21:15:54.004]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-09 21:15:54.009]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-09 21:15:54.014]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 34%
[2025-08-09 21:15:54.017]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-09 21:15:54.022]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-09 21:15:54.026]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 35%
[2025-08-09 21:15:54.031]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-09 21:15:54.035]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-09 21:15:54.041]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-09 21:15:54.045]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 36%
[2025-08-09 21:15:54.050]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-09 21:15:54.057]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-09 21:15:54.062]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 37%
[2025-08-09 21:15:54.075]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-09 21:15:54.084]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-09 21:15:54.094]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 38%
[2025-08-09 21:15:54.101]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-09 21:15:54.107]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-09 21:15:54.115]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-09 21:15:54.124]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 39%
[2025-08-09 21:15:54.135]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-09 21:15:54.141]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-09 21:15:54.147]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 40%
[2025-08-09 21:15:54.153]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-09 21:15:54.159]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-09 21:15:54.170]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 41%
[2025-08-09 21:15:54.175]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-09 21:15:54.180]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-09 21:15:54.185]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-09 21:15:54.190]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 42%
[2025-08-09 21:15:54.195]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-09 21:15:54.200]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-09 21:15:54.205]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 43%
[2025-08-09 21:15:54.210]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-09 21:15:54.214]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-09 21:15:54.219]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 44%
[2025-08-09 21:15:54.224]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-09 21:15:54.228]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-09 21:15:54.232]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-09 21:15:54.236]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 45%
[2025-08-09 21:15:54.241]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-09 21:15:54.246]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-09 21:15:54.252]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 46%
[2025-08-09 21:15:54.257]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-09 21:15:54.263]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-09 21:15:54.268]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-09 21:15:54.274]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 47%
[2025-08-09 21:15:54.279]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-09 21:15:54.284]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-09 21:15:54.294]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 48%
[2025-08-09 21:15:54.299]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-09 21:15:54.304]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-09 21:15:54.309]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 49%
[2025-08-09 21:15:54.314]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-09 21:15:54.318]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-09 21:15:54.323]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-09 21:15:54.328]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 50%
[2025-08-09 21:15:54.332]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-09 21:15:54.336]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-09 21:15:54.341]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 51%
[2025-08-09 21:15:54.346]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-09 21:15:54.350]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-09 21:15:54.356]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 52%
[2025-08-09 21:15:54.360]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-09 21:15:54.365]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-09 21:15:54.370]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-09 21:15:54.377]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 53%
[2025-08-09 21:15:54.382]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-09 21:15:54.386]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-09 21:15:54.392]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 54%
[2025-08-09 21:15:54.396]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-09 21:15:54.401]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-09 21:15:54.405]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-09 21:15:54.410]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 55%
[2025-08-09 21:15:54.414]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-09 21:15:54.419]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-09 21:15:54.425]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 56%
[2025-08-09 21:15:54.430]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-09 21:15:54.435]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-09 21:15:54.440]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 57%
[2025-08-09 21:15:54.445]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-09 21:15:54.449]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-09 21:15:54.454]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-09 21:15:54.460]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 58%
[2025-08-09 21:15:54.465]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-09 21:15:54.477]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-09 21:15:54.481]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 59%
[2025-08-09 21:15:54.486]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-09 21:15:54.493]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-09 21:15:54.498]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 60%
[2025-08-09 21:15:54.502]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-09 21:15:54.513]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-09 21:15:54.518]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-09 21:15:54.523]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 61%
[2025-08-09 21:15:54.528]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-09 21:15:54.601]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-09 21:15:54.654]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 62%
[2025-08-09 21:15:54.693]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-09 21:15:54.723]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-09 21:15:54.730]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 63%
[2025-08-09 21:15:54.737]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-09 21:15:54.764]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-09 21:15:54.782]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-09 21:15:54.790]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 64%
[2025-08-09 21:15:54.796]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-09 21:15:54.802]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-09 21:15:54.807]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 65%
[2025-08-09 21:15:54.812]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-09 21:15:54.817]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-09 21:15:54.822]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-09 21:15:54.827]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 66%
[2025-08-09 21:15:54.831]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-09 21:15:54.835]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-09 21:15:54.840]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 67%
[2025-08-09 21:15:54.844]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-09 21:15:54.848]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-09 21:15:54.851]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 68%
[2025-08-09 21:15:54.855]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-09 21:15:54.859]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-09 21:15:54.863]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-09 21:15:54.867]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 69%
[2025-08-09 21:15:54.870]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-09 21:15:54.874]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-09 21:15:54.878]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 70%
[2025-08-09 21:15:54.881]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-09 21:15:54.885]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-09 21:15:54.889]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 71%
[2025-08-09 21:15:54.893]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-09 21:15:54.897]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-09 21:15:54.900]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-09 21:15:54.904]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 72%
[2025-08-09 21:15:54.908]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-09 21:15:54.912]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-09 21:15:54.916]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 73%
[2025-08-09 21:15:54.920]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-09 21:15:54.924]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-09 21:15:54.928]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-09 21:15:54.932]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 74%
[2025-08-09 21:15:54.938]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-09 21:15:54.944]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-09 21:15:54.948]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 75%
[2025-08-09 21:15:54.952]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-09 21:15:54.956]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-09 21:15:54.960]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 76%
[2025-08-09 21:15:54.964]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-09 21:15:54.968]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-09 21:15:54.972]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-09 21:15:54.976]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 77%
[2025-08-09 21:15:54.980]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-09 21:15:54.984]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-09 21:15:54.987]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 78%
[2025-08-09 21:15:54.992]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-09 21:15:54.996]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-09 21:15:55.000]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 79%
[2025-08-09 21:15:55.003]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-09 21:15:55.007]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-09 21:15:55.011]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-09 21:15:55.016]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 80%
[2025-08-09 21:15:55.020]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-09 21:15:55.024]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-09 21:15:55.028]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 81%
[2025-08-09 21:15:55.032]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-09 21:15:55.036]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-09 21:15:55.042]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-09 21:15:55.046]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 82%
[2025-08-09 21:15:55.050]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-09 21:15:55.054]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-09 21:15:55.059]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 83%
[2025-08-09 21:15:55.063]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-09 21:15:55.068]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-09 21:15:55.072]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 84%
[2025-08-09 21:15:55.077]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-09 21:15:55.081]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-09 21:15:55.086]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-09 21:15:55.090]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 85%
[2025-08-09 21:15:55.095]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-09 21:15:55.099]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-09 21:15:55.104]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 86%
[2025-08-09 21:15:55.109]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-09 21:15:55.114]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-09 21:15:55.118]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 87%
[2025-08-09 21:15:55.123]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-09 21:15:55.128]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-09 21:15:55.132]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-09 21:15:55.136]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 88%
[2025-08-09 21:15:55.142]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-09 21:15:55.148]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-09 21:15:55.152]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 89%
[2025-08-09 21:15:55.157]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-09 21:15:55.161]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-09 21:15:55.166]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 90%
[2025-08-09 21:15:55.170]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-09 21:15:55.175]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-09 21:15:55.179]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-09 21:15:55.183]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 91%
[2025-08-09 21:15:55.188]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-09 21:15:55.192]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-09 21:15:55.196]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 92%
[2025-08-09 21:15:55.201]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-09 21:15:55.205]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-09 21:15:55.209]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-09 21:15:55.213]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 93%
[2025-08-09 21:15:55.218]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-09 21:15:55.224]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-09 21:15:55.229]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 94%
[2025-08-09 21:15:55.233]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-09 21:15:55.239]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-09 21:15:55.243]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 95%
[2025-08-09 21:15:55.247]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-09 21:15:55.251]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-09 21:15:55.255]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-09 21:15:55.259]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 96%
[2025-08-09 21:15:55.263]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-09 21:15:55.267]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-09 21:15:55.272]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 97%
[2025-08-09 21:15:55.276]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-09 21:15:55.281]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-09 21:15:55.286]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 98%
[2025-08-09 21:15:55.290]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-09 21:15:55.294]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-09 21:15:55.297]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-09 21:15:55.301]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 99%
[2025-08-09 21:15:55.305]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-09 21:15:55.309]  	[DEBUG]		[ServiceLocator]	Craft type import progress: 100%
[2025-08-09 21:15:55.314]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 21:15:55.433]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 21:15:55.437]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 21:15:55.442]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 21:15:55.446]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 21:15:55.454]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 21:15:55.460]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:15:55.466]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 21:15:55.471]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:15:55.478]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 21:15:55.483]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:15:55.624]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 21:15:55.629]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 21:15:55.711]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-09 21:15:55.817]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم الاستيراد
[2025-08-09 21:15:55.821]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 21:15:55.862]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 21:15:55.868]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:15:55.873]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:15:55.879]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:15:55.882]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:15:55.886]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:15:55.890]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:16:18.442]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:18.598]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:18.790]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:18.884]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:18.961]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:19.145]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:16:19.382]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 21:16:19.451]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-09 21:16:19.603]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-09 21:16:19.795]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 21:16:19.857]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_1003178_638903673798576359 (BaseViewModel) for NPersonalViewModel
[2025-08-09 21:16:20.066]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-09 21:16:20.236]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:16:20.392]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_9028608_638903673803927347 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 21:16:20.526]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 21:16:20.697]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:16:21.085]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 21:16:21.223]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_14148614_638903673812234420 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 21:16:21.426]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 21:16:21.542]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:16:21.705]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 21:16:21.839]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 21:16:21.957]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-09 21:16:22.175]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 21:16:22.327]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_52610097_638903673823275573 (BaseViewModel) for NewClientViewModel
[2025-08-09 21:16:22.427]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-09 21:16:22.555]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:16:22.646]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_3728830_638903673826468680 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 21:16:22.767]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 21:16:22.885]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:16:22.980]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 21:16:23.057]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_33559471_638903673830574253 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 21:16:23.144]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 21:16:23.294]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:16:23.428]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 21:16:23.539]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 21:16:23.621]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_33599791_638903673836216158 (BaseViewModel) for ActivityManagementViewModel
[2025-08-09 21:16:23.737]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-09 21:16:23.812]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:16:23.966]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 21:16:24.159]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-09 21:16:24.229]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 21:16:24.309]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-09 21:16:24.361]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_33962670_638903673843612252 (BaseViewModel) for NotesManagementViewModel
[2025-08-09 21:16:24.430]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-09 21:16:24.523]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:16:24.592]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 21:16:24.653]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-09 21:16:24.729]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-09 21:16:24.833]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 21:16:24.927]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-09 21:16:24.963]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 21:16:25.032]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 21:16:25.092]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 21:16:25.171]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 21:16:25.239]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-09 21:16:25.265]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-09 21:16:25.324]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-09 21:16:25.369]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 2, misses: 1)
[2025-08-09 21:16:25.373]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 21:16:25.402]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-09 21:16:25.456]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-09 21:16:25.508]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-09 21:16:25.547]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 5943ms (Success: True)
[2025-08-09 21:16:25.635]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 266ms
[2025-08-09 21:16:25.735]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 366ms
[2025-08-09 21:16:25.778]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 6328ms (Success: True)
[2025-08-09 21:16:25.917]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x560 (Height-based width calculation)
[2025-08-09 21:16:26.022]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-09 21:16:26.148]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:16:26.698]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-09 21:16:26.754]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 21:16:26.930]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbersCollection(1)
[2025-08-09 21:16:27.005]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:27.070]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:27.128]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:27.203]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:27.273]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:27.354]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:16:27.534]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:27.656]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:27.808]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:27.950]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:28.130]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:28.335]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:16:28.918]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:29.127]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:29.369]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:29.567]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:29.635]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:29.711]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:16:29.974]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:30.101]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:30.207]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:30.295]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:30.370]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:30.462]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:16:30.533]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:30.584]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:30.671]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:30.783]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:16:30.854]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:16:30.945]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:16:52.447]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 265.5MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:17:15.275]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:15.282]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:15.286]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:15.290]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:15.294]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:15.297]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:15.366]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:15.371]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:15.377]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:15.389]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:15.394]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:15.399]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:16.495]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:16.499]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:16.503]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:16.507]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:16.512]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:16.516]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:16.600]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:16.607]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:16.729]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:16.733]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:16.737]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:16.741]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:16.745]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:16.750]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:16.973]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:16.977]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:17.086]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.090]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:17.094]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.098]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:17.101]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.105]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:17.284]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:17.293]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:17.298]  	[INFO]		[NActivityDetailView]	Activity code not found: 1071
[2025-08-09 21:17:17.303]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 1071
[2025-08-09 21:17:17.334]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:17.340]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_61306625_638903674373401591 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:17.344]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:17.347]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:17.352]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:17.356]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 1071
[2025-08-09 21:17:17.360]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 1071
[2025-08-09 21:17:17.487]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:17.617]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.645]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:17.658]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.666]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:17.672]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.677]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:17.816]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.854]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:17.870]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.884]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:17.904]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:17.926]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:18.366]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1013.3562ms
[2025-08-09 21:17:18.374]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:19.379]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2026.8699ms
[2025-08-09 21:17:19.383]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:19.532]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:19.536]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:19.540]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:19.543]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:19.547]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:19.551]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:19.581]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:19.585]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 1071
[2025-08-09 21:17:19.691]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:19.696]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:19.701]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:19.706]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:19.710]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:19.714]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:20.732]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:20.737]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:20.741]  	[INFO]		[NActivityDetailView]	Activity code not found: 10710
[2025-08-09 21:17:20.749]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 10710
[2025-08-09 21:17:20.758]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:20.763]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_58888592_638903674407634604 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:20.767]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:20.771]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:20.776]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:20.780]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 10710
[2025-08-09 21:17:20.784]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 10710
[2025-08-09 21:17:20.914]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:21.020]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:21.025]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:21.029]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:21.033]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:21.039]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:21.043]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:21.081]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:21.088]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:21.092]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:21.095]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:21.099]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:21.103]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:21.793]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1017.3167ms
[2025-08-09 21:17:21.797]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:22.349]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:22.645]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:22.660]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:22.671]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:22.684]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:22.689]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:22.694]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:22.726]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:22.732]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 10710
[2025-08-09 21:17:22.850]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2074.1156ms
[2025-08-09 21:17:22.854]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:23.036]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:23.040]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:23.043]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:23.047]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:23.050]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:23.055]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:24.021]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:24.029]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:24.037]  	[DEBUG]		[NActivityDetailView]	Activity description updated for code 107107: صناعة تغذية الأطفال
[2025-08-09 21:17:24.155]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:24.169]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:24.178]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:24.196]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:24.218]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:24.225]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:25.788]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:26.265]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.270]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:26.274]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.278]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:26.283]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.287]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:26.436]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.442]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:26.446]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.449]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:26.454]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.458]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:26.827]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.833]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:26.838]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.842]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:26.846]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:26.850]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:27.062]  	[INFO]		[NActivityDetailView]	Opening ActivityStatusUpdateDialog - Context: StatusChange, Status: معدل
[2025-08-09 21:17:27.072]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:27.078]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityStatusUpdateDialogViewModel_36494097_638903674470782404 (BaseViewModel) for ActivityStatusUpdateDialogViewModel
[2025-08-09 21:17:27.081]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	BaseViewModel memory management initialized for ActivityStatusUpdateDialogViewModel
[2025-08-09 21:17:27.085]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:27.089]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized
[2025-08-09 21:17:27.093]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with ValidationService integration
[2025-08-09 21:17:27.097]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with existing data - Status: معدل, Type: MainCommercial
[2025-08-09 21:17:27.101]  	[DEBUG]		[ActivityStatusUpdateDialog]	ActivityStatusUpdateDialog initialized successfully
[2025-08-09 21:17:27.264]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 21:17:27.338]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:27.342]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:27.345]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:27.349]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:27.355]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:27.358]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:27.568]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:27.572]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:27.577]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:27.581]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:27.585]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:27.590]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:28.109]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 1012.2371ms
[2025-08-09 21:17:28.118]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:28.189]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.196]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:28.199]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.204]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:28.209]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.212]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:28.263]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:28.268]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	Activity update dialog cancelled
[2025-08-09 21:17:28.273]  	[DEBUG]		[ActivityStatusUpdateDialog]	Activity status update cancelled, keeping original values
[2025-08-09 21:17:28.278]  	[INFO]		[NActivityDetailView]	Activity status update cancelled, default values applied
[2025-08-09 21:17:28.361]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.365]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:28.369]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.373]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:28.377]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.381]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:28.873]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.877]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:28.881]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.884]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:28.888]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.892]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:28.950]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for SecondaryCommercial
[2025-08-09 21:17:28.956]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: SecondaryCommercial
[2025-08-09 21:17:28.960]  	[INFO]		[NewClientViewModel]	Switched to activity tab: SecondaryCommercial
[2025-08-09 21:17:28.984]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 7, Batched: 7, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1)
[2025-08-09 21:17:28.989]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:28.993]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:28.997]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:29.001]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:29.005]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:29.010]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:29.029]  	[DEBUG]		[ActivityManagementViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 21:17:29.064]  	[DEBUG]		[NewClientViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 21:17:29.155]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2057.9206ms
[2025-08-09 21:17:29.160]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:29.204]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: SecondaryCommercial
[2025-08-09 21:17:29.353]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 9, Time since interaction: 323.2558ms
[2025-08-09 21:17:29.357]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:29.377]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 421.8548ms
[2025-08-09 21:17:29.382]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:29.468]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:29.472]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:29.478]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:29.482]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:29.488]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:29.493]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:29.545]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.550]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.555]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.558]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.563]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.567]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.571]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.579]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.584]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.588]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.592]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.596]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.600]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.604]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.608]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.612]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.616]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.621]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.625]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.629]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.634]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.638]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.643]  	[DEBUG]		[NActivityDetailView]	Skipping dialog opening - programmatic ComboBox change detected
[2025-08-09 21:17:29.648]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.652]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.657]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for Craft
[2025-08-09 21:17:29.662]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.671]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.675]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.679]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.683]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.688]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.692]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.696]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.699]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.704]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.708]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.712]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.716]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.752]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.777]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.799]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.819]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.837]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.857]  	[DEBUG]		[ActivityManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.880]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:17:29.886]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.891]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.906]  	[DEBUG]		[ActivityManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:17:29.930]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:17:29.935]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: Craft
[2025-08-09 21:17:29.940]  	[INFO]		[NewClientViewModel]	Switched to activity tab: Craft
[2025-08-09 21:17:29.959]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: Craft
[2025-08-09 21:17:29.972]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:29.976]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:29.980]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:29.985]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:29.990]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:29.994]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:30.539]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:30.544]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:30.548]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:30.551]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:30.555]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:30.559]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:30.635]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:30.640]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:30.644]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:30.648]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:30.651]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:30.656]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:31.444]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:31.448]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:31.452]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:31.455]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:31.459]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:31.463]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:31.604]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:31.608]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:31.737]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:31.741]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:31.745]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:31.748]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:31.752]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:31.756]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:32.095]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: UpdateDate(1), ActivityStatus(1), ActivityType(1)
[2025-08-09 21:17:32.429]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:32.433]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:32.438]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-0
[2025-08-09 21:17:32.442]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-0
[2025-08-09 21:17:32.450]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:32.456]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_46099929_638903674524567986 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:32.461]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:32.465]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:32.469]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:32.473]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-0
[2025-08-09 21:17:32.477]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-0
[2025-08-09 21:17:32.497]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:32.501]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:32.505]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-0
[2025-08-09 21:17:32.509]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-0
[2025-08-09 21:17:32.520]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:32.524]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_53664804_638903674525245401 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:32.528]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:32.531]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:32.535]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:32.539]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-0
[2025-08-09 21:17:32.542]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-0
[2025-08-09 21:17:32.548]  	[ERROR]		[NActivityDetailView]	Error showing AddActivityDialog: DialogHost is already open.
[2025-08-09 21:17:32.669]  	[ERROR]		[NActivityDetailView]	Exception in LogException: DialogHost is already open.
[2025-08-09 21:17:32.674]  	[ERROR]		[NActivityDetailView]	Context - Source: NActivityDetailView, Operation: LogException, Timestamp: 2025-08-09 21:17:32.552, ElapsedMs: 0, UserAgent: XLABZ
[2025-08-09 21:17:32.678]  	[ERROR]		[NActivityDetailView]	Stack trace:    at MaterialDesignThemes.Wpf.DialogHost.ShowInternal(Object content, DialogOpenedEventHandler openedEventHandler, DialogClosingEventHandler closingEventHandler, DialogClosedEventHandler closedEventHandler)
   at MaterialDesignThemes.Wpf.DialogHost.Show(Object content, Object dialogIdentifier, DialogOpenedEventHandler openedEventHandler, DialogClosingEventHandler closingEventHandler, DialogClosedEventHandler closedEventHandler)
   at UFU2.Views.NewClient.NActivityDetailView.ShowAddActivityDialogAsync(String activityCode) in E:\UserFiles\Projects\UFU2\Views\NewClient\NActivityDetailView.xaml.cs:line 576
[2025-08-09 21:17:32.684]  	[DEBUG]		[ErrorDeduplicationManager]	No operation ID provided, allowing toast from NActivityDetailView
[2025-08-09 21:17:32.690]  	[INFO]		[NActivityDetailView]	Displaying user error toast: خطأ في النافذة - حدث خطأ أثناء فتح نافذة إضافة النشاط
[2025-08-09 21:17:32.694]  	[DEBUG]		[ToastService]	Displaying Error toast: خطأ في النافذة - حدث خطأ أثناء فتح نافذة إضافة النشاط
[2025-08-09 21:17:32.707]  	[INFO]		[ToastNotification]	Toast notification created: Error - خطأ في النافذة
[2025-08-09 21:17:32.738]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 21:17:32.745]  	[INFO]		[ToastService]	Desktop toast displayed: Error - خطأ في النافذة
[2025-08-09 21:17:32.751]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:32.756]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:33.039]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 18, Time since interaction: 3159.3436ms
[2025-08-09 21:17:33.043]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:33.047]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 14, Time since interaction: 3190.3488ms
[2025-08-09 21:17:33.051]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:33.067]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:33.073]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:33.077]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:33.082]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:33.088]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:33.092]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:33.119]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:33.125]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:33.129]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:33.134]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:33.139]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:33.144]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:33.458]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 989.12ms
[2025-08-09 21:17:33.462]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:33.520]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 984.6708ms
[2025-08-09 21:17:33.524]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:33.566]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 18, Batched: 18, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: G12DisplayText(4), BISDisplayText(4), SelectedActivityType(2), CurrentActivity(2), CurrentFileCheckStates(2)
[2025-08-09 21:17:33.972]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 7, Batched: 7, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1)
[2025-08-09 21:17:34.543]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2007.9409ms
[2025-08-09 21:17:34.547]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:35.468]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2999.6809ms
[2025-08-09 21:17:35.472]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:35.623]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:35.628]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:35.633]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:35.637]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:35.641]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:35.645]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:35.682]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:35.687]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 01-0
[2025-08-09 21:17:35.779]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:35.785]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:35.791]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:35.796]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:35.801]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:35.805]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:36.153]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:17:36.666]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:17:37.457]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:37.518]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:37.705]  	[INFO]		[ToastNotification]	Toast notification closed: Error - خطأ في النافذة
[2025-08-09 21:17:37.973]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:37.978]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:37.982]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-01
[2025-08-09 21:17:37.986]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-01
[2025-08-09 21:17:37.993]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:37.998]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_25478763_638903674579986573 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:38.002]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:38.006]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:38.010]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:38.014]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-01
[2025-08-09 21:17:38.018]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-01
[2025-08-09 21:17:38.159]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:38.292]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:38.299]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:38.306]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:38.312]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:38.317]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:38.322]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:38.394]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:38.400]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:38.406]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:38.410]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:38.415]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:38.420]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:39.004]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 994.1018ms
[2025-08-09 21:17:39.009]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:39.412]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:39.417]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:39.422]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:39.426]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:39.429]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:39.433]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:39.498]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:39.503]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 01-01
[2025-08-09 21:17:39.638]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:39.642]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:39.646]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:39.650]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:39.654]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:39.658]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:40.061]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2051.3813ms
[2025-08-09 21:17:40.065]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:42.941]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:42.945]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:42.949]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-01-0
[2025-08-09 21:17:42.954]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-01-0
[2025-08-09 21:17:42.961]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:42.966]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_4263198_638903674629662305 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:42.970]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:42.974]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:42.978]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:42.982]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-01-0
[2025-08-09 21:17:42.986]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-01-0
[2025-08-09 21:17:43.001]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:43.006]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:43.009]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-01-0
[2025-08-09 21:17:43.013]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-01-0
[2025-08-09 21:17:43.020]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:43.025]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_51393992_638903674630254531 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:43.029]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:43.033]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:43.037]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:43.041]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-01-0
[2025-08-09 21:17:43.045]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-01-0
[2025-08-09 21:17:43.048]  	[ERROR]		[NActivityDetailView]	Error showing AddActivityDialog: DialogHost is already open.
[2025-08-09 21:17:43.052]  	[ERROR]		[NActivityDetailView]	Exception in LogException: DialogHost is already open.
[2025-08-09 21:17:43.057]  	[ERROR]		[NActivityDetailView]	Context - Source: NActivityDetailView, Operation: LogException, Timestamp: 2025-08-09 21:17:43.052, ElapsedMs: 0, UserAgent: XLABZ
[2025-08-09 21:17:43.060]  	[ERROR]		[NActivityDetailView]	Stack trace:    at MaterialDesignThemes.Wpf.DialogHost.ShowInternal(Object content, DialogOpenedEventHandler openedEventHandler, DialogClosingEventHandler closingEventHandler, DialogClosedEventHandler closedEventHandler)
   at MaterialDesignThemes.Wpf.DialogHost.Show(Object content, Object dialogIdentifier, DialogOpenedEventHandler openedEventHandler, DialogClosingEventHandler closingEventHandler, DialogClosedEventHandler closedEventHandler)
   at UFU2.Views.NewClient.NActivityDetailView.ShowAddActivityDialogAsync(String activityCode) in E:\UserFiles\Projects\UFU2\Views\NewClient\NActivityDetailView.xaml.cs:line 576
[2025-08-09 21:17:43.064]  	[DEBUG]		[ErrorDeduplicationManager]	No operation ID provided, allowing toast from NActivityDetailView
[2025-08-09 21:17:43.067]  	[INFO]		[NActivityDetailView]	Displaying user error toast: خطأ في النافذة - حدث خطأ أثناء فتح نافذة إضافة النشاط
[2025-08-09 21:17:43.071]  	[DEBUG]		[ToastService]	Displaying Error toast: خطأ في النافذة - حدث خطأ أثناء فتح نافذة إضافة النشاط
[2025-08-09 21:17:43.082]  	[INFO]		[ToastNotification]	Toast notification created: Error - خطأ في النافذة
[2025-08-09 21:17:43.093]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 21:17:43.099]  	[INFO]		[ToastService]	Desktop toast displayed: Error - خطأ في النافذة
[2025-08-09 21:17:43.106]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:43.111]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:43.349]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:43.365]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:43.373]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:43.377]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:43.382]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:43.386]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:43.390]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:43.421]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:43.427]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:43.431]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:43.435]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:43.440]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:43.444]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:43.971]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 992.8975ms
[2025-08-09 21:17:43.975]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:44.037]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 999.8186ms
[2025-08-09 21:17:44.041]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:44.422]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:44.431]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:44.438]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:44.442]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:44.447]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:44.456]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:44.506]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:44.512]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 01-01-0
[2025-08-09 21:17:44.605]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:44.609]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:44.613]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:44.617]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:44.623]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:44.627]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:45.269]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:45.273]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:45.277]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-01-00
[2025-08-09 21:17:45.281]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-01-00
[2025-08-09 21:17:45.288]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:45.293]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_14468758_638903674652938714 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:45.298]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:45.303]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:45.308]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:45.313]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-01-00
[2025-08-09 21:17:45.317]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-01-00
[2025-08-09 21:17:45.458]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:45.564]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:45.587]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:45.592]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:45.598]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:45.603]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:45.607]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:45.654]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:45.659]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:45.663]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:45.667]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:45.671]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:45.676]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:45.971]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2992.4614ms
[2025-08-09 21:17:45.975]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:46.065]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 3027.6389ms
[2025-08-09 21:17:46.071]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:46.321]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1013.29ms
[2025-08-09 21:17:46.326]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:46.331]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:46.336]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:46.340]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:46.343]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:46.348]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:46.352]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:46.382]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:46.388]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 01-01-00
[2025-08-09 21:17:46.587]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:46.591]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:46.595]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:46.598]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:46.602]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:46.606]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:47.036]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:47.040]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:47.044]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-01-000
[2025-08-09 21:17:47.048]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-01-000
[2025-08-09 21:17:47.055]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:47.060]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_53718883_638903674670602254 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:47.064]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:47.067]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:47.071]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:47.075]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-01-000
[2025-08-09 21:17:47.078]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-01-000
[2025-08-09 21:17:47.190]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:47.292]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:47.296]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:47.310]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:47.314]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:47.319]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:47.323]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:47.332]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2023.6342ms
[2025-08-09 21:17:47.336]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:47.369]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:47.375]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:47.381]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:47.386]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:47.391]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:47.395]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:47.971]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:48.037]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:48.070]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 998.69ms
[2025-08-09 21:17:48.081]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:48.104]  	[INFO]		[ToastNotification]	Toast notification closed: Error - خطأ في النافذة
[2025-08-09 21:17:48.180]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:48.185]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:48.190]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:48.193]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:48.197]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:48.201]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:48.226]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:48.232]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 01-01-000
[2025-08-09 21:17:48.431]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:48.435]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:48.439]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:48.442]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:48.446]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:48.450]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:48.681]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 288.12MB working set
[2025-08-09 21:17:49.006]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:49.010]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:49.013]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-01-00
[2025-08-09 21:17:49.017]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-01-00
[2025-08-09 21:17:49.024]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:49.028]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_33421169_638903674690288190 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:49.032]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:49.036]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:49.040]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:49.043]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-01-00
[2025-08-09 21:17:49.047]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-01-00
[2025-08-09 21:17:49.175]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:49.238]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2166.8502ms
[2025-08-09 21:17:49.242]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:49.269]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:49.273]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:49.277]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:49.281]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:49.284]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:49.288]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:49.317]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:49.324]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:49.328]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:49.332]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:49.336]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:49.339]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:50.037]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 997.1354ms
[2025-08-09 21:17:50.041]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:50.088]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.093]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:50.100]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.104]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:50.108]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.112]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:50.169]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:50.173]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 01-01-00
[2025-08-09 21:17:50.248]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.274]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:50.294]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.312]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:50.320]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.326]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:50.364]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:50.492]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:50.496]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:50.501]  	[INFO]		[NActivityDetailView]	Activity code not found: 01-01-001
[2025-08-09 21:17:50.505]  	[INFO]		[NActivityDetailView]	Showing AddActivityDialog for unknown code: 01-01-001
[2025-08-09 21:17:50.512]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:50.519]  	[DEBUG]		[ResourceManager]	Registered resource: AddActivityDialogViewModel_29441793_638903674705195895 (BaseViewModel) for AddActivityDialogViewModel
[2025-08-09 21:17:50.523]  	[DEBUG]		[AddActivityDialogViewModel]	BaseViewModel memory management initialized for AddActivityDialogViewModel
[2025-08-09 21:17:50.527]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:50.531]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized
[2025-08-09 21:17:50.536]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel initialized for code: 01-01-001
[2025-08-09 21:17:50.543]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog initialized for code: 01-01-001
[2025-08-09 21:17:50.658]  	[DEBUG]		[AddActivityDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:17:50.789]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.806]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:50.818]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.860]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:50.892]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:50.913]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:51.003]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:51.026]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:51.032]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:51.038]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:51.044]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:51.053]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:51.071]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2031.0393ms
[2025-08-09 21:17:51.078]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:51.518]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 986.8272ms
[2025-08-09 21:17:51.530]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:17:52.064]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:52.173]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:52.182]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:52.189]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:52.194]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:52.198]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:52.203]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:52.247]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:17:52.251]  	[DEBUG]		[AddActivityDialog]	AddActivityDialog cancelled for code: 01-01-001
[2025-08-09 21:17:52.360]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:52.365]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:52.370]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:52.374]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:52.379]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:52.383]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:52.431]  	[DEBUG]		[CacheMonitoringService]	Performing cache monitoring
[2025-08-09 21:17:52.441]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:17:52.445]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 287.9MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:17:52.449]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CpiLocationService: Needs Attention
[2025-08-09 21:17:52.455]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ActivityTypeBaseService: Error: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:17:52.459]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for CraftTypeBaseService: Hit ratio: 0.0%
[2025-08-09 21:17:52.463]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CraftTypeBaseService: Poor Performance
[2025-08-09 21:17:52.467]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for FileCheckBusinessRuleService: Hit ratio: 0.0%
[2025-08-09 21:17:52.471]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for FileCheckBusinessRuleService: Low hit ratio: 0.0%
[2025-08-09 21:17:52.476]  	[INFO]		[CacheMonitoringService]	Cache monitoring summary - Services: 5, Unhealthy: 4, Total memory: 0.0MB, Avg hit ratio: 49.6%
[2025-08-09 21:17:52.718]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2186.46ms
[2025-08-09 21:17:52.722]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:17:53.650]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:53.654]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:53.658]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:53.662]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:53.665]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:53.669]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:53.754]  	[INFO]		[NewClientViewModel]	Craft information dialog requested
[2025-08-09 21:17:53.759]  	[INFO]		[NewClientViewModel]	Displaying user error toast: معلومات الحرفة - لا يوجد رمز حرفة محدد
[2025-08-09 21:17:53.762]  	[DEBUG]		[ToastService]	Displaying Error toast: معلومات الحرفة - لا يوجد رمز حرفة محدد
[2025-08-09 21:17:53.773]  	[INFO]		[ToastNotification]	Toast notification created: Error - معلومات الحرفة
[2025-08-09 21:17:53.786]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 21:17:53.791]  	[INFO]		[ToastService]	Desktop toast displayed: Error - معلومات الحرفة
[2025-08-09 21:17:53.839]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:53.844]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:53.847]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:53.851]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:53.856]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:53.859]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:54.068]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:55.413]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:55.418]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:55.423]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:55.427]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:55.433]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:55.438]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:55.498]  	[INFO]		[NewClientViewModel]	Craft types search dialog requested
[2025-08-09 21:17:55.519]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 1, misses: 1)
[2025-08-09 21:17:55.524]  	[DEBUG]		[CraftSearchDialog]	Loaded 20 initial craft types
[2025-08-09 21:17:55.737]  	[DEBUG]		[AddActivityDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ActivityCode(1)
[2025-08-09 21:17:55.881]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:55.892]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:55.900]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:55.906]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:55.913]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:55.917]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:55.973]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:55.979]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:55.984]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:55.989]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:55.995]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:56.000]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:58.109]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:58.114]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:58.119]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:58.123]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:58.126]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:58.130]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:58.166]  	[DEBUG]		[CraftSearchDialog]	Craft search dialog cancelled
[2025-08-09 21:17:58.209]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:58.213]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:58.218]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:58.222]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:58.226]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:58.230]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:58.784]  	[INFO]		[ToastNotification]	Toast notification closed: Error - معلومات الحرفة
[2025-08-09 21:17:59.289]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.295]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:59.300]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.305]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:59.309]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.313]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:59.390]  	[INFO]		[ActivityManagementViewModel]	Activity type switched to: MainCommercial
[2025-08-09 21:17:59.395]  	[INFO]		[NewClientViewModel]	Switched to activity tab: MainCommercial
[2025-08-09 21:17:59.403]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.408]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:59.413]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.419]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:59.463]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.483]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:17:59.508]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 21:17:59.531]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 21:17:59.549]  	[DEBUG]		[NActivityDetailView]	Activity description updated for code 107107: صناعة تغذية الأطفال
[2025-08-09 21:17:59.582]  	[INFO]		[NActivityDetailView]	Opening ActivityStatusUpdateDialog - Context: StatusChange, Status: معدل
[2025-08-09 21:17:59.592]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:17:59.598]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityStatusUpdateDialogViewModel_19214507_638903674795989693 (BaseViewModel) for ActivityStatusUpdateDialogViewModel
[2025-08-09 21:17:59.651]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	BaseViewModel memory management initialized for ActivityStatusUpdateDialogViewModel
[2025-08-09 21:17:59.662]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:17:59.671]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized
[2025-08-09 21:17:59.674]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with ValidationService integration
[2025-08-09 21:17:59.678]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel initialized with existing data - Status: معدل, Type: MainCommercial
[2025-08-09 21:17:59.683]  	[DEBUG]		[ActivityStatusUpdateDialog]	ActivityStatusUpdateDialog initialized successfully
[2025-08-09 21:17:59.706]  	[DEBUG]		[ActivityManagementViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 21:17:59.711]  	[DEBUG]		[NewClientViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 21:17:59.715]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Processed 4 Normal priority property notifications
[2025-08-09 21:17:59.908]  	[DEBUG]		[NFileCheckView]	Optimized visibility update for activity type: MainCommercial
[2025-08-09 21:17:59.939]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.957]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:59.963]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.969]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:17:59.976]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:17:59.986]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:00.019]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.059]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:00.068]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.073]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:00.077]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.082]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:00.134]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 9, Time since interaction: 428.0213ms
[2025-08-09 21:18:00.139]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:00.237]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 846.5615ms
[2025-08-09 21:18:00.241]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:00.671]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 5, Time since interaction: 992.2457ms
[2025-08-09 21:18:00.675]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:00.802]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.807]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:00.812]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.817]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:00.821]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.826]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:00.867]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:18:00.872]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	Activity update dialog cancelled
[2025-08-09 21:18:00.875]  	[DEBUG]		[ActivityStatusUpdateDialog]	Activity status update cancelled, keeping original values
[2025-08-09 21:18:00.880]  	[INFO]		[NActivityDetailView]	Activity status update cancelled - Context: StatusChange, keeping existing values
[2025-08-09 21:18:00.925]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.929]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:00.933]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.938]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:00.944]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:00.949]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:01.685]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 5, Time since interaction: 2006.4597ms
[2025-08-09 21:18:01.689]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:18:01.892]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:01.896]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:01.900]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:01.904]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:01.908]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:01.911]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:02.000]  	[INFO]		[NewClientViewModel]	Multiple activities management requested
[2025-08-09 21:18:02.028]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 21:18:02.033]  	[DEBUG]		[ResourceManager]	Registered resource: MultipleActivitiesDialogViewModel_50369505_638903674820336268 (BaseViewModel) for MultipleActivitiesDialogViewModel
[2025-08-09 21:18:02.037]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	BaseViewModel memory management initialized for MultipleActivitiesDialogViewModel
[2025-08-09 21:18:02.041]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:18:02.045]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel initialized
[2025-08-09 21:18:02.050]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel initialized with IDisposable support
[2025-08-09 21:18:02.056]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Loaded 0 existing activities
[2025-08-09 21:18:02.202]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:18:02.278]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 9, Time since interaction: 2571.7145ms
[2025-08-09 21:18:02.283]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:18:02.291]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 7, Time since interaction: 2900.7434ms
[2025-08-09 21:18:02.298]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:18:02.312]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:02.330]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:02.337]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:02.352]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:02.357]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:02.363]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:02.386]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:02.405]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:02.412]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:02.420]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:02.427]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:02.439]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:03.046]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 989.6526ms
[2025-08-09 21:18:03.078]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:03.320]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:03.330]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:03.335]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:03.339]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:03.343]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:03.346]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:03.374]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 21:18:03.379]  	[INFO]		[MultipleActivitiesDialog]	Multiple activities dialog cancelled
[2025-08-09 21:18:03.437]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:03.441]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:03.446]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:03.450]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:03.456]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:03.460]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:03.858]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 9, Batched: 9, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: G12DisplayText(2), BISDisplayText(2), SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1)
[2025-08-09 21:18:03.983]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 7, Batched: 7, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedActivityType(1), CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1)
[2025-08-09 21:18:04.109]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2053.2092ms
[2025-08-09 21:18:04.114]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 21:18:04.664]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	PropertyChanged Performance - Total: 5, Batched: 5, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: UpdateDate(2), UpdateNote(1), ActivityStatus(1), ActivityType(1)
[2025-08-09 21:18:07.036]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: HasActivities(1)
[2025-08-09 21:18:11.755]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.763]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.767]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.772]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.776]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.781]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.785]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.789]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.792]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.796]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.800]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.804]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.808]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.811]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.815]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.819]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.823]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.827]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.830]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.834]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.838]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.842]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.845]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:11.882]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.890]  	[DEBUG]		[NPersonalViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.894]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.898]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.904]  	[DEBUG]		[NewClientViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.908]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.913]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.917]  	[DEBUG]		[ActivityManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.924]  	[DEBUG]		[NotesManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.930]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.937]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.969]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.974]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.980]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.986]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.990]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.995]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:11.999]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:12.004]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:12.008]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:12.012]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:12.016]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:12.021]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:18:12.051]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 5, Time since interaction: 35.2021ms
[2025-08-09 21:18:12.057]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.068]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 64.076ms
[2025-08-09 21:18:12.073]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.077]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 82.0669ms
[2025-08-09 21:18:12.081]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.085]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 86.5442ms
[2025-08-09 21:18:12.089]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.093]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 81.0126ms
[2025-08-09 21:18:12.097]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.101]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 80.108ms
[2025-08-09 21:18:12.105]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.109]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:12.113]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:12.117]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:12.121]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:12.124]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:12.128]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:12.307]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 321.0171ms
[2025-08-09 21:18:12.311]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.324]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 9, Time since interaction: 419.7144ms
[2025-08-09 21:18:12.328]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.332]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 423.9389ms
[2025-08-09 21:18:12.336]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.341]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 428.4421ms
[2025-08-09 21:18:12.345]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.349]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 432.0564ms
[2025-08-09 21:18:12.353]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.358]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 433.9969ms
[2025-08-09 21:18:12.362]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.366]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 435.6504ms
[2025-08-09 21:18:12.370]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.374]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 437.2347ms
[2025-08-09 21:18:12.378]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.383]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 413.9575ms
[2025-08-09 21:18:12.387]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.391]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 509.0379ms
[2025-08-09 21:18:12.395]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.400]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 510.0123ms
[2025-08-09 21:18:12.404]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.408]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 514.2597ms
[2025-08-09 21:18:12.412]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.416]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 517.4536ms
[2025-08-09 21:18:12.420]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.424]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 450.1543ms
[2025-08-09 21:18:12.429]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.433]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 452.8603ms
[2025-08-09 21:18:12.437]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.441]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 451.0923ms
[2025-08-09 21:18:12.446]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:12.450]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 441.6814ms
[2025-08-09 21:18:12.454]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 21:18:13.754]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.759]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.769]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.773]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.776]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.780]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.785]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.789]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.792]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.796]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.799]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.803]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.807]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.810]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.814]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.829]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.832]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.836]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.840]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.843]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.847]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.852]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.855]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:18:13.879]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:13.884]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:13.890]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:13.895]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:18:13.898]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:18:13.911]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:18:14.063]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 5, Time since interaction: 2047.3778ms
[2025-08-09 21:18:14.068]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.073]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2068.6835ms
[2025-08-09 21:18:14.077]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.083]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2088.001ms
[2025-08-09 21:18:14.087]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.094]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2095.2295ms
[2025-08-09 21:18:14.107]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.119]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2106.7947ms
[2025-08-09 21:18:14.130]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.146]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2125.4388ms
[2025-08-09 21:18:14.161]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.318]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2332.3ms
[2025-08-09 21:18:14.326]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.331]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 9, Time since interaction: 2426.8782ms
[2025-08-09 21:18:14.338]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.342]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2433.6318ms
[2025-08-09 21:18:14.357]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.362]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2449.2806ms
[2025-08-09 21:18:14.366]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.371]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 7, Time since interaction: 2454.1842ms
[2025-08-09 21:18:14.375]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.379]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2455.0709ms
[2025-08-09 21:18:14.383]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.387]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2457.477ms
[2025-08-09 21:18:14.391]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.395]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2458.0609ms
[2025-08-09 21:18:14.399]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.405]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 3, Time since interaction: 2435.829ms
[2025-08-09 21:18:14.408]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.412]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 3, Time since interaction: 2530.1355ms
[2025-08-09 21:18:14.416]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.424]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2534.3351ms
[2025-08-09 21:18:14.428]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.433]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2538.8195ms
[2025-08-09 21:18:14.438]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.442]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2543.8669ms
[2025-08-09 21:18:14.446]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.450]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2476.1607ms
[2025-08-09 21:18:14.455]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.462]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2482.2918ms
[2025-08-09 21:18:14.468]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.473]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2482.9543ms
[2025-08-09 21:18:14.478]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:14.482]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2474.1722ms
[2025-08-09 21:18:14.487]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 21:18:48.679]  	[DEBUG]		[WeakEventManager]	Automatic weak event cleanup completed
[2025-08-09 21:18:52.447]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 294.0MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:19:02.793]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:19:03.294]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:19:48.691]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 294.50MB working set
[2025-08-09 21:19:52.444]  	[DEBUG]		[CacheMonitoringService]	Performing cache monitoring
[2025-08-09 21:19:52.448]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 294.5MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:19:52.449]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:19:52.457]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CpiLocationService: Needs Attention
[2025-08-09 21:19:52.460]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ActivityTypeBaseService: Error: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:19:52.464]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for CraftTypeBaseService: Hit ratio: 9.1%
[2025-08-09 21:19:52.468]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CraftTypeBaseService: Poor Performance
[2025-08-09 21:19:52.472]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for FileCheckBusinessRuleService: Hit ratio: 0.0%
[2025-08-09 21:19:52.475]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for FileCheckBusinessRuleService: Low hit ratio: 0.0%
[2025-08-09 21:19:52.479]  	[INFO]		[CacheMonitoringService]	Cache monitoring summary - Services: 5, Unhealthy: 4, Total memory: 0.0MB, Avg hit ratio: 36.9%
[2025-08-09 21:19:52.483]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for Global: Average hit ratio (36.9%) below threshold (40.0%)
[2025-08-09 21:20:48.649]  	[INFO]		[ViewLoadingMonitoringService]	View Loading Performance Report - Views: 2, Avg Load: 6135.5ms, Fast Ratio: 0.0%, Background Ratio: 0.0%, Score: 0.0
[2025-08-09 21:20:48.665]  	[DEBUG]		[ResourceManager]	Automatic cleanup completed
[2025-08-09 21:20:48.701]  	[INFO]		[ResourceManager]	Generated memory leak report: 23 alive resources, 0 dead resources
[2025-08-09 21:20:48.713]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 3
[2025-08-09 21:20:48.718]  	[WARN]		[MemoryLeakDetectionService]	Critical memory leaks detected: 3 issues found
[2025-08-09 21:20:49.376]  	[DEBUG]		[DatabaseService]	Pool Utilization - Current: 2/10, Health: Healthy, Hit Ratio: 100.00%, Reuse Ratio: 0.00%, Total Requests: 37
[2025-08-09 21:20:49.376]  	[DEBUG]		[DatabaseService]	Pool Utilization - Current: 3/10, Health: Healthy, Hit Ratio: 0.00%, Reuse Ratio: 0.00%, Total Requests: 1
[2025-08-09 21:20:49.390]  	[DEBUG]		[DatabaseService]	Pool Utilization - Current: 2/10, Health: Healthy, Hit Ratio: 100.00%, Reuse Ratio: 0.00%, Total Requests: 1
[2025-08-09 21:20:52.400]  	[DEBUG]		[CacheCoordinatorService]	Performing cache health monitoring
[2025-08-09 21:20:52.406]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:20:52.415]  	[WARN]		[CacheCoordinatorService]	Found 4 unhealthy cache services: CpiLocationService, ActivityTypeBaseService, CraftTypeBaseService, FileCheckBusinessRuleService
[2025-08-09 21:20:52.423]  	[INFO]		[CacheCoordinatorService]	Cache health summary - Services: 5, Avg hit ratio: 36.9%, Total items: 52, Memory: 0.0MB
[2025-08-09 21:20:52.423]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 4 services
[2025-08-09 21:20:52.434]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 21:20:52.439]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 3, misses: 1)
[2025-08-09 21:20:52.444]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached search results for: تجاري (Cache hits: 1, misses: 6)
[2025-08-09 21:20:52.460]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached search results for: حرفي (Cache hits: 2, misses: 6)
[2025-08-09 21:20:52.485]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached search results for: مهني (Cache hits: 3, misses: 6)
[2025-08-09 21:20:52.492]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached search results for: Commercial (Cache hits: 4, misses: 6)
[2025-08-09 21:20:52.507]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached search results for: Craft (Cache hits: 5, misses: 6)
[2025-08-09 21:20:52.519]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached search results for: Professional (Cache hits: 6, misses: 6)
[2025-08-09 21:20:52.524]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 6, Data cache hits: 3
[2025-08-09 21:20:52.467]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 295.1MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:20:52.454]  	[DEBUG]		[CacheMonitoringService]	Collecting cache statistics
[2025-08-09 21:20:52.530]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 94ms
[2025-08-09 21:20:52.542]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 21:20:52.548]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 8, Cache misses: 8
[2025-08-09 21:20:52.557]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 15ms
[2025-08-09 21:20:52.565]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 21:20:52.571]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-09 21:20:52.577]  	[DEBUG]		[CpiLocationService]	Retrieved dairas for wilaya 16 from cache
[2025-08-09 21:20:52.539]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:20:52.587]  	[DEBUG]		[CpiLocationService]	Retrieved dairas for wilaya 31 from cache
[2025-08-09 21:20:52.602]  	[DEBUG]		[CpiLocationService]	Retrieved dairas for wilaya 25 from cache
[2025-08-09 21:20:52.607]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 21:20:52.614]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 49ms
[2025-08-09 21:20:52.627]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 21:20:52.640]  	[DEBUG]		[CraftTypeBaseService]	Returning cached craft types (Cache hits: 2, misses: 1)
[2025-08-09 21:20:52.656]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'تجاري' (Cache hits: 1, misses: 9)
[2025-08-09 21:20:52.664]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'حرفي' (Cache hits: 2, misses: 9)
[2025-08-09 21:20:52.671]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'مهني' (Cache hits: 3, misses: 9)
[2025-08-09 21:20:52.675]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'صناعة' (Cache hits: 4, misses: 9)
[2025-08-09 21:20:52.682]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'تقليدي' (Cache hits: 5, misses: 9)
[2025-08-09 21:20:52.686]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'Commercial' (Cache hits: 6, misses: 9)
[2025-08-09 21:20:52.594]  	[INFO]		[CacheMonitoringService]	Cache statistics - Services: 5 (1 healthy), Items: 60, Memory: 0.0MB, Avg hit ratio: 40.2%
[2025-08-09 21:20:52.695]  	[DEBUG]		[CacheMonitoringService]	Service CpiLocationService: 0 items, 0.0MB, 16.7% hit ratio, unhealthy
[2025-08-09 21:20:52.702]  	[DEBUG]		[CacheMonitoringService]	Service ValidationService: 33 items, 0.0MB, 84.8% hit ratio, healthy
[2025-08-09 21:20:52.706]  	[DEBUG]		[CacheMonitoringService]	Service ActivityTypeBaseService: 0 items, 0.0MB, 0.0% hit ratio, unhealthy
[2025-08-09 21:20:52.710]  	[DEBUG]		[CacheMonitoringService]	Service CraftTypeBaseService: 11 items, 0.0MB, 9.1% hit ratio, unhealthy
[2025-08-09 21:20:52.717]  	[DEBUG]		[CacheMonitoringService]	Service FileCheckBusinessRuleService: 16 items, 0.0MB, 50.0% hit ratio, unhealthy
[2025-08-09 21:20:52.690]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'Craft' (Cache hits: 7, misses: 9)
[2025-08-09 21:20:52.726]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'Professional' (Cache hits: 8, misses: 9)
[2025-08-09 21:20:52.733]  	[DEBUG]		[CraftTypeBaseService]	Returning cached search results for 'Traditional' (Cache hits: 9, misses: 9)
[2025-08-09 21:20:52.741]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 21:20:52.748]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 122ms
[2025-08-09 21:21:48.221]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:21:48.678]  	[DEBUG]		[WeakEventManager]	Automatic weak event cleanup completed
[2025-08-09 21:21:48.682]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 295.13MB working set
[2025-08-09 21:21:48.725]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:21:52.434]  	[DEBUG]		[CacheMonitoringService]	Performing cache monitoring
[2025-08-09 21:21:52.438]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:21:52.442]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CpiLocationService: Needs Attention
[2025-08-09 21:21:52.447]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ActivityTypeBaseService: Error: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:21:52.452]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CraftTypeBaseService: Poor Performance
[2025-08-09 21:21:52.453]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 295.2MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:21:52.456]  	[INFO]		[CacheMonitoringService]	Cache event [RapidGrowth] for CraftTypeBaseService: Cache size grew by 90.9% (from 11 to 21 items)
[2025-08-09 21:21:52.465]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for FileCheckBusinessRuleService: Low hit ratio: 50.0%
[2025-08-09 21:21:52.469]  	[INFO]		[CacheMonitoringService]	Cache event [RapidGrowth] for FileCheckBusinessRuleService: Cache size grew by 100.0% (from 8 to 16 items)
[2025-08-09 21:21:52.473]  	[INFO]		[CacheMonitoringService]	Cache monitoring summary - Services: 5, Unhealthy: 4, Total memory: 0.0MB, Avg hit ratio: 56.5%
[2025-08-09 21:22:52.451]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 294.9MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:23:48.681]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 294.84MB working set
[2025-08-09 21:23:52.440]  	[DEBUG]		[CacheMonitoringService]	Performing cache monitoring
[2025-08-09 21:23:52.445]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:23:52.449]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CpiLocationService: Needs Attention
[2025-08-09 21:23:52.452]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ActivityTypeBaseService: Error: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:23:52.457]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CraftTypeBaseService: Poor Performance
[2025-08-09 21:23:52.459]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 294.9MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:23:52.461]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for FileCheckBusinessRuleService: Low hit ratio: 50.0%
[2025-08-09 21:23:52.468]  	[INFO]		[CacheMonitoringService]	Cache monitoring summary - Services: 5, Unhealthy: 4, Total memory: 0.0MB, Avg hit ratio: 56.5%
[2025-08-09 21:24:13.982]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:24:14.494]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:24:48.678]  	[DEBUG]		[WeakEventManager]	Automatic weak event cleanup completed
[2025-08-09 21:24:52.454]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 294.8MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:25:27.057]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:25:27.553]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:25:28.063]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 21:25:48.649]  	[INFO]		[ViewLoadingMonitoringService]	View Loading Performance Report - Views: 2, Avg Load: 6135.5ms, Fast Ratio: 0.0%, Background Ratio: 0.0%, Score: 0.0
[2025-08-09 21:25:48.661]  	[INFO]		[PerformanceDashboardService]	Performance Dashboard Report:

=== UFU2 Performance Optimization Report ===
Generated: 2025-08-09 20:25:48 UTC

PHASE 2C PERFORMANCE TARGETS:
✓ Target: 60-70% improvement in dialog initialization
✓ Current Achievement: 0.0%
✓ Target Met: NO

DIALOG PERFORMANCE:
• Baseline: 250ms
• Current: 6135.5ms
• Improvement: 0.0%
• Fast Load Ratio: 0.0%
• Background Load Ratio: 0.0%

MEMORY OPTIMIZATION:
• Current Usage: 294.0MB
• Memory Efficiency: 26.5%
• Views Tracked: 2
• Memory Freed: 0MB

BACKGROUND PROCESSING:
• Active Tasks: 0
• Completed Tasks: 3
• Average Processing: 256ms
• Queue Length: 0

OVERALL PERFORMANCE SCORE: 38.8/100

PHASE 2C TARGETS STATUS:
• Dialog Initialization (≤150ms): ✗ NOT MET
• Background Processing (≤500ms): ✓ MET
• Memory Optimization (≤350MB): ✓ MET
• UI Responsiveness (Good+): ✓ MET

=== End Report ===

[2025-08-09 21:25:48.665]  	[DEBUG]		[ResourceManager]	Automatic cleanup completed
[2025-08-09 21:25:48.683]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 295.45MB working set
[2025-08-09 21:25:48.687]  	[INFO]		[ResourceManager]	Generated memory leak report: 23 alive resources, 0 dead resources
[2025-08-09 21:25:48.692]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 3
[2025-08-09 21:25:48.696]  	[WARN]		[MemoryLeakDetectionService]	Critical memory leaks detected: 3 issues found
[2025-08-09 21:25:49.362]  	[DEBUG]		[DatabaseService]	Pool Utilization - Current: 3/10, Health: Healthy, Hit Ratio: 0.00%, Reuse Ratio: 0.00%, Total Requests: 1
[2025-08-09 21:25:49.376]  	[DEBUG]		[DatabaseService]	Pool Utilization - Current: 2/10, Health: Healthy, Hit Ratio: 100.00%, Reuse Ratio: 0.00%, Total Requests: 37
[2025-08-09 21:25:49.383]  	[DEBUG]		[DatabaseService]	Pool Utilization - Current: 2/10, Health: Healthy, Hit Ratio: 100.00%, Reuse Ratio: 0.00%, Total Requests: 1
[2025-08-09 21:25:50.498]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.507]  	[DEBUG]		[NPersonalViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.513]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.521]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.525]  	[DEBUG]		[NewClientViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.528]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.533]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.537]  	[DEBUG]		[ActivityManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.540]  	[DEBUG]		[NotesManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.544]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.548]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.552]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.556]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.559]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.563]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.567]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.571]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.574]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.578]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.582]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.585]  	[DEBUG]		[AddActivityDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.589]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.593]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 21:25:50.606]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:50.635]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:25:50.673]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:50.691]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:25:50.718]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-09 21:25:50.735]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:50.750]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-09 21:25:50.762]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-09 21:25:50.767]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-09 21:25:50.781]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 21:25:50.799]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_1694433_638903679507998882 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-09 21:25:50.805]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-09 21:25:50.809]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 21:25:50.813]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 21:25:50.817]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 21:25:50.823]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-09 21:25:50.833]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 21:25:50.837]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 21:25:50.841]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 21:25:50.884]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 21:25:50.903]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 21:25:50.926]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 5, Time since interaction: 337.3777ms
[2025-08-09 21:25:50.930]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:50.939]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 360.8235ms
[2025-08-09 21:25:50.945]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:50.953]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 382.5261ms
[2025-08-09 21:25:50.958]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.003]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 428.9144ms
[2025-08-09 21:25:51.007]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.020]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 435.1993ms
[2025-08-09 21:25:51.024]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.034]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 441.0281ms
[2025-08-09 21:25:51.038]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.043]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 479.5883ms
[2025-08-09 21:25:51.047]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.051]  	[DEBUG]		[NewClientViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 9, Time since interaction: 525.8158ms
[2025-08-09 21:25:51.056]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.060]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 531.4161ms
[2025-08-09 21:25:51.064]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.070]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 537.8692ms
[2025-08-09 21:25:51.075]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.087]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 7, Time since interaction: 549.7865ms
[2025-08-09 21:25:51.091]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.103]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 562.874ms
[2025-08-09 21:25:51.107]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.120]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 576.0201ms
[2025-08-09 21:25:51.124]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.137]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 588.7794ms
[2025-08-09 21:25:51.140]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.153]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 601.2526ms
[2025-08-09 21:25:51.157]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.170]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 672.3656ms
[2025-08-09 21:25:51.174]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.187]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 679.4837ms
[2025-08-09 21:25:51.191]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.204]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 690.7182ms
[2025-08-09 21:25:51.208]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.220]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 699.3998ms
[2025-08-09 21:25:51.225]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.230]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 674.3143ms
[2025-08-09 21:25:51.236]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.254]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 694.4518ms
[2025-08-09 21:25:51.259]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.269]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 701.867ms
[2025-08-09 21:25:51.275]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.285]  	[DEBUG]		[AddActivityDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 703.3569ms
[2025-08-09 21:25:51.290]  	[DEBUG]		[AddActivityDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 21:25:51.320]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:51.324]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:25:51.327]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:51.331]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:25:51.335]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:51.340]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:25:51.655]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:51.675]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:25:51.679]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:51.690]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 21:25:51.694]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 21:25:51.698]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 21:25:51.728]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-09 21:25:51.733]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 21:25:51.739]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 21:25:51.746]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 21:25:51.751]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:51.756]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 21:25:51.761]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 21:25:51.768]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:51.772]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 21:25:51.777]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 21:25:51.781]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_1694433_638903679507998882
[2025-08-09 21:25:51.786]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:51.790]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 21:25:51.793]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 21:25:51.797]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:51.801]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 21:25:51.805]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 21:25:51.808]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 21:25:51.820]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-09 21:25:51.824]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-09 21:25:51.828]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 21:25:51.832]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-09 21:25:51.836]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 21:25:51.840]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 21:25:51.844]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:51.847]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 21:25:51.851]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 21:25:51.855]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:51.859]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 21:25:51.863]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 21:25:51.866]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_28090709_638903673490084654
[2025-08-09 21:25:51.870]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:51.874]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 21:25:51.877]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 21:25:51.882]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:51.886]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 21:25:51.892]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 21:25:51.897]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-09 21:25:51.901]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-09 21:25:51.906]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-09 21:25:51.910]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-09 21:25:51.915]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 21:25:51.920]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 21:25:51.929]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-09 21:25:51.935]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 21:25:51.940]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 21:25:51.944]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 21:25:51.960]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:51.996]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.005]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.011]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.016]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.023]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.028]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.035]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.046]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.051]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.057]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.061]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.066]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.072]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.077]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.082]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.088]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.093]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.098]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.104]  	[DEBUG]		[AddActivityDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.109]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.113]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 21:25:52.125]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 21:25:52.130]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-09 21:25:52.136]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 21:25:52.140]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-09 21:25:52.144]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-09 21:25:52.149]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-09 21:25:52.171]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 21:25:52.177]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 21:25:52.331]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 21:25:52.411]  	[DEBUG]		[CacheCoordinatorService]	Performing cache health monitoring
[2025-08-09 21:25:52.452]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:25:52.459]  	[WARN]		[CacheCoordinatorService]	Found 4 unhealthy cache services: CpiLocationService, ActivityTypeBaseService, CraftTypeBaseService, FileCheckBusinessRuleService
[2025-08-09 21:25:52.463]  	[INFO]		[CacheCoordinatorService]	Cache health summary - Services: 5, Avg hit ratio: 56.5%, Total items: 70, Memory: 0.0MB
[2025-08-09 21:25:52.455]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 297.4MB, System: 30.0%, Pressure: Normal
[2025-08-09 21:25:52.424]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 21:25:52.435]  	[DEBUG]		[CacheMonitoringService]	Collecting cache statistics
[2025-08-09 21:25:52.480]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:25:52.484]  	[INFO]		[CacheMonitoringService]	Cache statistics - Services: 5 (1 healthy), Items: 70, Memory: 0.0MB, Avg hit ratio: 56.5%
[2025-08-09 21:25:52.488]  	[DEBUG]		[CacheMonitoringService]	Service CpiLocationService: 0 items, 0.0MB, 38.9% hit ratio, unhealthy
[2025-08-09 21:25:52.492]  	[DEBUG]		[CacheMonitoringService]	Service ValidationService: 33 items, 0.0MB, 84.8% hit ratio, healthy
[2025-08-09 21:25:52.476]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 21:25:52.500]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-09 21:25:52.463]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 4 services
[2025-08-09 21:25:52.496]  	[DEBUG]		[CacheMonitoringService]	Service ActivityTypeBaseService: 0 items, 0.0MB, 0.0% hit ratio, unhealthy
[2025-08-09 21:25:52.512]  	[DEBUG]		[CacheMonitoringService]	Service CraftTypeBaseService: 21 items, 0.0MB, 52.4% hit ratio, unhealthy
[2025-08-09 21:25:52.504]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 21:25:52.520]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-09 21:25:52.436]  	[DEBUG]		[CacheMonitoringService]	Performing cache monitoring
[2025-08-09 21:25:52.529]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:25:52.508]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 21:25:52.525]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 21:25:52.541]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-09 21:25:52.533]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CpiLocationService: Needs Attention
[2025-08-09 21:25:52.549]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ActivityTypeBaseService: Error: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 21:25:52.553]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CraftTypeBaseService: Poor Performance
[2025-08-09 21:25:52.545]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-09 21:25:52.537]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 4, misses: 1)
[2025-08-09 21:25:52.516]  	[DEBUG]		[CacheMonitoringService]	Service FileCheckBusinessRuleService: 16 items, 0.0MB, 50.0% hit ratio, unhealthy
[2025-08-09 21:25:52.557]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for FileCheckBusinessRuleService: Low hit ratio: 50.0%
[2025-08-09 21:25:52.561]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-09 21:25:52.576]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-09 21:25:52.572]  	[INFO]		[CacheMonitoringService]	Cache monitoring summary - Services: 5, Unhealthy: 4, Total memory: 0.0MB, Avg hit ratio: 56.5%
[2025-08-09 21:25:52.580]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 50.0%, Total lookups: 16
[2025-08-09 21:25:52.588]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-09 21:25:52.592]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-09 21:25:52.596]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 66.7%, Daira hit ratio: 50.0%, Search hit ratio: 0.0%
[2025-08-09 21:25:52.600]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-09 21:25:52.604]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 50.0%, Data hit ratio: 66.7%
[2025-08-09 21:25:52.608]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-09 21:25:52.605]  	[ERROR]		[ActivityTypeBaseService]	Exception in LogException: Cannot access a disposed object.
Object name: 'DatabaseService'.
[2025-08-09 21:25:52.616]  	[ERROR]		[ActivityTypeBaseService]	Context - Source: ActivityTypeBaseService, Operation: LogException, Timestamp: 2025-08-09 21:25:52.565, ElapsedMs: 0, UserAgent: XLABZ
[2025-08-09 21:25:52.620]  	[ERROR]		[ActivityTypeBaseService]	Stack trace:    at UFU2.Services.DatabaseService.GetPooledConnectionAsync() in E:\UserFiles\Projects\UFU2\Services\DatabaseService.cs:line 202
   at UFU2.Services.ActivityTypeBaseService.SearchByDescriptionAsync(String searchTerm, Int32 limit) in E:\UserFiles\Projects\UFU2\Services\ActivityTypeBaseService.cs:line 429
[2025-08-09 21:25:52.623]  	[DEBUG]		[ErrorDeduplicationManager]	No operation ID provided, allowing toast from ActivityTypeBaseService
[2025-08-09 21:25:52.628]  	[INFO]		[ActivityTypeBaseService]	Displaying user error toast: خطأ في قاعدة البيانات - فشل في البحث عن أنواع الأنشطة
[2025-08-09 21:25:52.632]  	[WARN]		[ActivityTypeBaseService]	Failed to warmup cache for search term 'تجاري': Cannot access a disposed object.
Object name: 'DatabaseService'.
[2025-08-09 21:25:52.612]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 46.2%, Data hit ratio: 80.0%
[2025-08-09 21:25:52.643]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-09 21:25:52.639]  	[WARN]		[ActivityTypeBaseService]	Failed to warmup cache for search term 'حرفي': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.651]  	[WARN]		[ActivityTypeBaseService]	Failed to warmup cache for search term 'مهني': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.655]  	[WARN]		[ActivityTypeBaseService]	Failed to warmup cache for search term 'Commercial': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.648]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 21:25:52.663]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 21:25:52.667]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 21:25:52.671]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-09 21:25:52.675]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-09 21:25:52.659]  	[WARN]		[ActivityTypeBaseService]	Failed to warmup cache for search term 'Craft': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.679]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-09 21:25:52.683]  	[WARN]		[ActivityTypeBaseService]	Failed to warmup cache for search term 'Professional': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.691]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 6, Data cache hits: 4
[2025-08-09 21:25:52.694]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 186ms
[2025-08-09 21:25:52.698]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 21:25:52.687]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-09 21:25:52.702]  	[WARN]		[FileCheckBusinessRuleService]	Failed to warmup cache for activity type 'MainCommercial': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.706]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-09 21:25:52.710]  	[WARN]		[FileCheckBusinessRuleService]	Failed to warmup cache for activity type 'SecondaryCommercial': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.718]  	[WARN]		[FileCheckBusinessRuleService]	Failed to warmup cache for activity type 'Craft': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.722]  	[WARN]		[FileCheckBusinessRuleService]	Failed to warmup cache for activity type 'Professional': Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.726]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 8, Cache misses: 8
[2025-08-09 21:25:52.730]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 31ms
[2025-08-09 21:25:52.733]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 21:25:52.714]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-09 21:25:52.738]  	[ERROR]		[CpiLocationService]	Error during cache warmup: Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.742]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-09 21:25:52.749]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-09 21:25:52.753]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 21:25:52.745]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 12ms
[2025-08-09 21:25:52.757]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 21:25:52.760]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 21:25:52.769]  	[ERROR]		[CraftTypeBaseService]	Error during cache warmup: Cannot access a disposed object.
Object name: 'Microsoft.Extensions.Caching.Memory.MemoryCache'.
[2025-08-09 21:25:52.773]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 13ms
[2025-08-09 21:25:52.770]  	[INFO]		[ResourceManager]	Generated memory leak report: 22 alive resources, 0 dead resources
[2025-08-09 21:25:52.782]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 3
[2025-08-09 21:25:52.785]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 3 potential leaks detected
[2025-08-09 21:25:52.778]  	[ERROR]		[CacheCoordinatorService]	Error during health-triggered cache warmup: Cannot access a disposed object.
Object name: 'System.Threading.SemaphoreSlim'.
[2025-08-09 21:25:52.789]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 6 snapshots, 0 leaks detected, 2 alerts generated
[2025-08-09 21:25:52.796]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-09 21:25:52.801]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 21:25:52.805]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 21:25:52.808]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 4 cleanups
[2025-08-09 21:25:52.812]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-09 21:25:52.817]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 21:25:52.821]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 21:25:52.825]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.829]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.832]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.836]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.840]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.844]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.848]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.852]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.856]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.859]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.864]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.867]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.871]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.875]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.879]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.882]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.886]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.890]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.894]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.898]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.901]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.905]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.909]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.912]  	[DEBUG]		[AddActivityDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:52.916]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:52.920]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:52.924]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:52.927]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:52.931]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.935]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_46099929_638903674524567986
[2025-08-09 21:25:52.938]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:52.942]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:52.946]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:52.950]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:52.954]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.958]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_4263198_638903674629662305
[2025-08-09 21:25:52.962]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:52.966]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:52.969]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:52.973]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:52.977]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:52.980]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_29441793_638903674705195895
[2025-08-09 21:25:52.984]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:52.989]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:52.994]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:52.998]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.002]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.006]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_53664804_638903674525245401
[2025-08-09 21:25:53.010]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:53.014]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:53.017]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.022]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.025]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.029]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_51393992_638903674630254531
[2025-08-09 21:25:53.033]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:53.037]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:53.042]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.045]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.049]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.054]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_58888592_638903674407634604
[2025-08-09 21:25:53.058]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:53.062]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:53.066]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.070]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.074]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.078]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_25478763_638903674579986573
[2025-08-09 21:25:53.082]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:53.086]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:53.089]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.093]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.098]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.102]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_53718883_638903674670602254
[2025-08-09 21:25:53.106]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:53.109]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:53.113]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.117]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.120]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.125]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_14468758_638903674652938714
[2025-08-09 21:25:53.128]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:53.132]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:53.136]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.140]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.144]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.148]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_33421169_638903674690288190
[2025-08-09 21:25:53.152]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:53.156]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:53.160]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.163]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.167]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.170]  	[DEBUG]		[ResourceManager]	Unregistered resource: AddActivityDialogViewModel_61306625_638903674373401591
[2025-08-09 21:25:53.175]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: AddActivityDialogViewModel (11 resources, 0 event subscriptions)
[2025-08-09 21:25:53.178]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: AddActivityDialogViewModel (0 handlers)
[2025-08-09 21:25:53.182]  	[DEBUG]		[AddActivityDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.186]  	[DEBUG]		[AddActivityDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.189]  	[INFO]		[AddActivityDialogViewModel]	AddActivityDialogViewModel disposed
[2025-08-09 21:25:53.194]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 21:25:53.198]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.202]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 21:25:53.206]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.210]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:53.214]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 21:25:53.218]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.223]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.227]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 21:25:53.231]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityManagementViewModel_33599791_638903673836216158
[2025-08-09 21:25:53.236]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:53.240]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 21:25:53.244]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.248]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.253]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 21:25:53.258]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.262]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.267]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.271]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.275]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.280]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.284]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.288]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 21:25:53.293]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.297]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.301]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.305]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_14148614_638903673812234420
[2025-08-09 21:25:53.310]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.314]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 21:25:53.318]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.322]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.326]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.331]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_33559471_638903673830574253
[2025-08-09 21:25:53.335]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.339]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 21:25:53.343]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.347]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.351]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.356]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Starting disposal of ActivityStatusUpdateDialogViewModel resources
[2025-08-09 21:25:53.360]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Dialog event handlers cleared successfully
[2025-08-09 21:25:53.364]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposal completed successfully
[2025-08-09 21:25:53.368]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.372]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Starting disposal of ActivityStatusUpdateDialogViewModel resources
[2025-08-09 21:25:53.377]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Dialog event handlers cleared successfully
[2025-08-09 21:25:53.380]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposal completed successfully
[2025-08-09 21:25:53.384]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.388]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Starting disposal of ActivityStatusUpdateDialogViewModel resources
[2025-08-09 21:25:53.392]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Dialog event handlers cleared successfully
[2025-08-09 21:25:53.396]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposal completed successfully
[2025-08-09 21:25:53.400]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.404]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.408]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-09 21:25:53.412]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.415]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.419]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-09 21:25:53.423]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityStatusUpdateDialogViewModel_19214507_638903674795989693
[2025-08-09 21:25:53.427]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.431]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-09 21:25:53.434]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.438]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.442]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-09 21:25:53.445]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityStatusUpdateDialogViewModel_36494097_638903674470782404
[2025-08-09 21:25:53.449]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityStatusUpdateDialogViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.453]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityStatusUpdateDialogViewModel (0 handlers)
[2025-08-09 21:25:53.457]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.461]  	[DEBUG]		[ActivityStatusUpdateDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.465]  	[INFO]		[ActivityStatusUpdateDialogViewModel]	ActivityStatusUpdateDialogViewModel disposed
[2025-08-09 21:25:53.471]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.477]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.483]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.495]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.502]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.508]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.513]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.520]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 21:25:53.532]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.538]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.544]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.554]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_9028608_638903673803927347
[2025-08-09 21:25:53.560]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.564]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 21:25:53.569]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.573]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.578]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.582]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_3728830_638903673826468680
[2025-08-09 21:25:53.586]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (2 resources, 0 event subscriptions)
[2025-08-09 21:25:53.590]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 21:25:53.595]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.600]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.604]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.608]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.612]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.617]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.621]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.625]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.630]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:53.634]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 21:25:53.638]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.643]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.647]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 21:25:53.651]  	[DEBUG]		[ResourceManager]	Unregistered resource: NPersonalViewModel_1003178_638903673798576359
[2025-08-09 21:25:53.656]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:53.660]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 21:25:53.664]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.669]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.673]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 21:25:53.678]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.682]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.687]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 21:25:53.693]  	[DEBUG]		[NotesManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:25:53.697]  	[DEBUG]		[NotesManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:25:53.702]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.706]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 21:25:53.711]  	[DEBUG]		[NotesManagementViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 21:25:53.715]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.720]  	[DEBUG]		[NotesManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 21:25:53.724]  	[DEBUG]		[NotesManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 21:25:53.728]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.732]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 21:25:53.736]  	[DEBUG]		[NotesManagementViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 21:25:53.740]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.743]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:53.747]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 21:25:53.750]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.754]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.758]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 21:25:53.761]  	[DEBUG]		[ResourceManager]	Unregistered resource: NotesManagementViewModel_33962670_638903673843612252
[2025-08-09 21:25:53.766]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:53.829]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 21:25:53.838]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.847]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.856]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 21:25:53.871]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 21:25:53.876]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.881]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 21:25:53.887]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 21:25:53.893]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 21:25:53.900]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 21:25:53.905]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 21:25:53.912]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 21:25:53.917]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:53.923]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 21:25:53.927]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.931]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.934]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 21:25:53.938]  	[DEBUG]		[ResourceManager]	Unregistered resource: NewClientViewModel_52610097_638903673823275573
[2025-08-09 21:25:53.943]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:53.950]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 21:25:53.957]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-09 21:25:53.962]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:53.966]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 21:25:53.990]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Unsubscribed from AddedActivities.CollectionChanged event
[2025-08-09 21:25:53.995]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared AddedActivities collection
[2025-08-09 21:25:54.000]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared SearchResults collection
[2025-08-09 21:25:54.005]  	[DEBUG]		[ActivityTypeBaseService]	All caches cleared and recreated
[2025-08-09 21:25:54.009]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared ActivityTypeBaseService caches
[2025-08-09 21:25:54.013]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel disposed successfully
[2025-08-09 21:25:54.018]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:25:54.022]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:54.026]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared AddedActivities collection
[2025-08-09 21:25:54.030]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared SearchResults collection
[2025-08-09 21:25:54.034]  	[DEBUG]		[ActivityTypeBaseService]	All caches cleared and recreated
[2025-08-09 21:25:54.038]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Cleared ActivityTypeBaseService caches
[2025-08-09 21:25:54.042]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel disposed successfully
[2025-08-09 21:25:54.045]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 21:25:54.050]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	All batched notifications flushed
[2025-08-09 21:25:54.056]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: MultipleActivitiesDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:54.060]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: MultipleActivitiesDialogViewModel (0 handlers)
[2025-08-09 21:25:54.064]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:54.067]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:54.071]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel disposed
[2025-08-09 21:25:54.075]  	[DEBUG]		[ResourceManager]	Unregistered resource: MultipleActivitiesDialogViewModel_50369505_638903674820336268
[2025-08-09 21:25:54.079]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: MultipleActivitiesDialogViewModel (1 resources, 0 event subscriptions)
[2025-08-09 21:25:54.083]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: MultipleActivitiesDialogViewModel (0 handlers)
[2025-08-09 21:25:54.087]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Memory management cleanup completed
[2025-08-09 21:25:54.090]  	[DEBUG]		[MultipleActivitiesDialogViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 21:25:54.094]  	[INFO]		[MultipleActivitiesDialogViewModel]	MultipleActivitiesDialogViewModel disposed
[2025-08-09 21:25:54.098]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 24 tracked, 31 disposed, 3 cleanups
[2025-08-09 21:25:54.102]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-09 21:25:54.106]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 21:25:54.110]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-09 21:25:54.114]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 21:25:54.118]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-09 21:25:54.122]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 21:25:54.126]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-09 21:25:54.130]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 21:25:54.134]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-09 21:25:54.138]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-09 21:25:54.142]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-09 21:25:54.146]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-09 21:25:54.152]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-09 21:25:54.156]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-09 21:25:54.160]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 21:25:54.164]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-09 21:25:54.168]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 84.8%, Total validations: 33
[2025-08-09 21:25:54.172]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-09 21:25:54.176]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-09 21:25:54.179]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 21:25:54 ===
