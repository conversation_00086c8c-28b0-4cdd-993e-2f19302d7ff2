using System.Windows;
using System.Windows.Controls;
using UFU2.ViewModels;
using UFU2.Common;
using UFU2.Models;
using UFU2.Windows;
using MaterialDesignThemes.Wpf;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for NoteListDialog.xaml
    /// A UserControl that provides a dialog for managing multiple notes.
    /// Integrates with MaterialDesign DialogHost for consistent modal behavior
    /// and follows UFU2 design patterns with Arabic RTL layout support.
    /// </summary>
    public partial class NoteListDialog : UserControl
    {
        #region Private Fields
        private NoteListDialogViewModel _viewModel;
        private bool _dialogResult = false;
        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the NoteListDialog class.
        /// </summary>
        public NoteListDialog()
        {
            InitializeComponent();
            InitializeViewModel(new NoteListDialogViewModel());
        }

        /// <summary>
        /// Initializes a new instance of the NoteListDialog class with existing notes.
        /// </summary>
        /// <param name="existingNotes">Existing notes collection to manage</param>
        public NoteListDialog(NotesCollectionModel existingNotes)
        {
            InitializeComponent();
            InitializeViewModel(new NoteListDialogViewModel(existingNotes));
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets the dialog result indicating whether the user saved or cancelled.
        /// </summary>
        public bool DialogResult => _dialogResult;

        /// <summary>
        /// Gets the notes collection from the dialog.
        /// </summary>
        public NotesCollectionModel Notes => _viewModel?.Notes ?? new NotesCollectionModel();

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes the ViewModel and sets up event handlers.
        /// </summary>
        /// <param name="viewModel">The ViewModel to initialize</param>
        private void InitializeViewModel(NoteListDialogViewModel viewModel)
        {
            _viewModel = viewModel;
            DataContext = _viewModel;

            // Subscribe to ViewModel events
            _viewModel.AddNoteRequested += OnAddNoteRequested;
            _viewModel.EditNoteRequested += OnEditNoteRequested;
            _viewModel.DeleteNoteRequested += OnDeleteNoteRequested;
            _viewModel.CloseRequested += OnCloseRequested;

            LoggingService.LogDebug($"NoteListDialog initialized with {_viewModel.NotesCount} notes", "NoteListDialog");
        }

        /// <summary>
        /// Handles the add note request from the ViewModel.
        /// </summary>
        private async void OnAddNoteRequested(object? sender, EventArgs e)
        {
            try
            {
                LoggingService.LogDebug("Add note requested", "NoteListDialog");

                // Create the add note dialog
                var addNoteDialog = new AddNotesDialog();

                // Show the dialog using the nested DialogHost with unique identifier
                var result = await DialogHost.Show(addNoteDialog, "NoteListDialogHost");

                // Handle dialog result
                if (result is bool dialogResult && dialogResult && addNoteDialog.DialogResult)
                {
                    var noteData = addNoteDialog.NoteData;
                    if (noteData != null)
                    {
                        _viewModel.AddNote(noteData);
                        LoggingService.LogInfo($"Note added successfully. Total notes: {_viewModel.NotesCount}", "NoteListDialog");
                    }
                }
                else
                {
                    LoggingService.LogInfo("Add note cancelled", "NoteListDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding note: {ex.Message}", "NoteListDialog");
            }
        }

        /// <summary>
        /// Handles the edit note request from the ViewModel.
        /// </summary>
        private async void OnEditNoteRequested(object? sender, NoteModel note)
        {
            try
            {
                if (note == null)
                {
                    LoggingService.LogWarning("Edit note requested with null note", "NoteListDialog");
                    return;
                }

                LoggingService.LogDebug($"Edit note requested for note ID: {note.Id}", "NoteListDialog");

                // Create the edit note dialog
                var editNoteDialog = new AddNotesDialog(note);

                // Show the dialog using the nested DialogHost with unique identifier
                var result = await DialogHost.Show(editNoteDialog, "NoteListDialogHost");

                // Handle dialog result
                if (result is bool dialogResult && dialogResult && editNoteDialog.DialogResult)
                {
                    var updatedNoteData = editNoteDialog.NoteData;
                    if (updatedNoteData != null)
                    {
                        _viewModel.UpdateNote(updatedNoteData);
                        LoggingService.LogDebug($"Note updated successfully. ID: {updatedNoteData.Id}", "NoteListDialog");
                    }
                }
                else
                {
                    LoggingService.LogDebug("Edit note cancelled", "NoteListDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error editing note: {ex.Message}", "NoteListDialog");
            }
        }

        /// <summary>
        /// Handles the delete note request from the ViewModel.
        /// </summary>
        private async void OnDeleteNoteRequested(object? sender, NoteModel note)
        {
            try
            {
                if (note == null)
                {
                    LoggingService.LogWarning("Delete note requested with null note", "NoteListDialog");
                    return;
                }

                LoggingService.LogDebug($"Delete note requested for note ID: {note.Id}", "NoteListDialog");

                // Show confirmation dialog using UFU2 pattern
                bool confirmResult = ConfirmationWindow.ShowCustomDialog(
                    "تأكيد الحذف",
                    "هل أنت متأكد من حذف هذه الملاحظة؟",
                    "حذف",
                    "إلغاء",
                    null, // owner window
                    MaterialDesignThemes.Wpf.PackIconKind.Delete,
                    false // not topmost
                );

                if (confirmResult)
                {
                    _viewModel.RemoveNote(note);
                    LoggingService.LogInfo($"Note deleted successfully. Total notes: {_viewModel.NotesCount}", "NoteListDialog");
                    ErrorManager.ShowUserSuccessToast("تم حذف الملاحظة بنجاح", "تم الحذف", "NoteListDialog");
                }
                else
                {
                    LoggingService.LogInfo("Delete note cancelled", "NoteListDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error deleting note: {ex.Message}", "NoteListDialog");
            }
        }

        /// <summary>
        /// Handles the close request from the ViewModel.
        /// </summary>
        private void OnCloseRequested(object? sender, EventArgs e)
        {
            try
            {
                _dialogResult = true; // Notes dialog always returns true when closed normally
                LoggingService.LogInfo("Notes dialog closed", "NoteListDialog");
                CloseDialog(true);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing dialog: {ex.Message}", "NoteListDialog");
            }
        }

        /// <summary>
        /// Closes the dialog with the specified result.
        /// </summary>
        /// <param name="result">The dialog result</param>
        private void CloseDialog(bool result)
        {
            try
            {
                // Close the dialog using MaterialDesign DialogHost
                DialogHost.CloseDialogCommand.Execute(result, this);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing dialog: {ex.Message}", "NoteListDialog");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the current notes collection for external access.
        /// </summary>
        /// <returns>The current notes collection</returns>
        public NotesCollectionModel GetNotes()
        {
            return _viewModel?.Notes ?? new NotesCollectionModel();
        }

        /// <summary>
        /// Sets the notes collection for the dialog.
        /// </summary>
        /// <param name="notes">The notes collection to set</param>
        public void SetNotes(NotesCollectionModel notes)
        {
            try
            {
                if (notes != null)
                {
                    // Create a new ViewModel with the provided notes
                    var newViewModel = new NoteListDialogViewModel(notes);
                    InitializeViewModel(newViewModel);
                    LoggingService.LogInfo($"Notes collection set with {notes.Count} notes", "NoteListDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting notes collection: {ex.Message}", "NoteListDialog");
            }
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// Cleans up event subscriptions when the control is unloaded.
        /// </summary>
        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.AddNoteRequested -= OnAddNoteRequested;
                    _viewModel.EditNoteRequested -= OnEditNoteRequested;
                    _viewModel.DeleteNoteRequested -= OnDeleteNoteRequested;
                    _viewModel.CloseRequested -= OnCloseRequested;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cleanup: {ex.Message}", "NoteListDialog");
            }
        }

        #endregion
    }
}
