<UserControl
    x:Class="UFU2.Views.Dialogs.ImageManagementDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:UFU2.Controls"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    x:Name="ImageManagementDialogControl"
    Width="600"
    Height="500"
    AutomationProperties.HelpText="Image management dialog for profile image editing"
    AutomationProperties.ItemType="Dialog"
    AutomationProperties.Name="Image Management Dialog"
    FlowDirection="LeftToRight"
    Focusable="True"
    IsTabStop="True"
    KeyDown="ImageManagementDialogControl_KeyDown"
    SnapsToDevicePixels="True"
    UseLayoutRounding="True"
    mc:Ignorable="d">



    <UserControl.Resources>
        <ResourceDictionary>
            <!--  Boolean to Visibility Converters  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />

            <!--  Inverse Boolean Converter for button enabling  -->
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <!--  DialogHost for confirmation dialogs  -->
    <materialDesign:DialogHost x:Name="ImageManagementDialogHost" Identifier="ImageManagementDialogHost">
        <!--  Main Dialog Container with MaterialDesign Card styling  -->
        <materialDesign:Card Padding="0" Style="{StaticResource DialogBaseCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <!--  Header  -->
                    <RowDefinition Height="Auto" />
                    <!--  Content Area  -->
                    <RowDefinition Height="*" />
                    <!--  Status Panel  -->
                    <RowDefinition Height="Auto" />
                    <!--  Footer Buttons  -->
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Header Section  -->
                <materialDesign:Card
                    Grid.Row="0"
                    Padding="0"
                    materialDesign:ElevationAssist.Elevation="Dp8"
                    Style="{StaticResource HeaderCardStyle}">

                    <!--  Title  -->
                    <TextBlock
                        Margin="12,0"
                        HorizontalAlignment="Center"
                        Style="{StaticResource HeadlineStyle}"
                        Text="إدارة الصورة الشخصية" />
                </materialDesign:Card>

                <!--  Main Content Area  -->
                <Grid Grid.Row="1" Margin="5,12,5,12">
                    <Grid.ColumnDefinitions>
                        <!--  Controls Panel  -->
                        <ColumnDefinition Width="45" />
                        <!--  Image Preview Area  -->
                        <ColumnDefinition Width="3*" />
                    </Grid.ColumnDefinitions>

                    <!--  Controls Panel  -->
                    <StackPanel Grid.Column="0" Orientation="Vertical">
                        <!--  Load Image Button  -->
                        <Button
                            x:Name="LoadImage"
                            Width="32"
                            Height="32"
                            Margin="6,3"
                            AutomationProperties.HelpText="Select image from files"
                            AutomationProperties.Name="Load image button"
                            Click="LoadImage_Click"
                            IsTabStop="True"
                            Style="{StaticResource ContainerButtonStyle}"
                            TabIndex="5"
                            ToolTip="اختيار صورة من الملفات">
                            <materialDesign:PackIcon
                                Width="20"
                                Height="20"
                                Kind="Plus" />
                        </Button>

                        <!--  Reset Button  -->
                        <Button
                            x:Name="ResetButton"
                            Width="32"
                            Height="32"
                            Margin="6,3"
                            AutomationProperties.HelpText="Reset all transformations"
                            AutomationProperties.Name="Reset button"
                            Click="ResetButton_Click"
                            IsEnabled="{Binding CanReset}"
                            IsTabStop="True"
                            Style="{StaticResource ContainerButtonStyle}"
                            TabIndex="6"
                            ToolTip="إعادة تعيين جميع التحويلات (R)">
                            <materialDesign:PackIcon
                                Width="20"
                                Height="20"
                                Kind="Autorenew" />
                        </Button>

                        <!--  Crop Image Button  -->
                        <Button
                            x:Name="CropImageButton"
                            Width="32"
                            Height="32"
                            Margin="6,3"
                            AutomationProperties.HelpText="Crop the image using current selection"
                            AutomationProperties.Name="Crop image button"
                            Click="CropImageButton_Click"
                            IsEnabled="{Binding CanCrop}"
                            IsTabStop="True"
                            Style="{StaticResource ContainerButtonStyle}"
                            TabIndex="7"
                            ToolTip="قص الصورة (Ctrl+R)">
                            <materialDesign:PackIcon
                                Width="20"
                                Height="20"
                                Kind="Crop" />
                        </Button>

                        <!--  Keyboard Shortcuts Help  -->
                        <Grid>
                            <!--  Button to trigger the popup  -->
                            <Button
                                Width="32"
                                Height="32"
                                Margin="6,3"
                                AutomationProperties.HelpText="Show keyboard shortcuts help"
                                AutomationProperties.Name="Keyboard shortcuts button"
                                Click="Button_Click"
                                IsTabStop="True"
                                Style="{DynamicResource ContainerButtonStyle}"
                                TabIndex="8"
                                ToolTip="عرض اختصارات لوحة المفاتيح">
                                <materialDesign:PackIcon
                                    Width="20"
                                    Height="20"
                                    Kind="KeyboardVariant" />
                            </Button>

                            <!--  Popup containing the shortcuts  -->
                            <Popup
                                x:Name="shortcutPopup"
                                AllowsTransparency="True"
                                Placement="Bottom"
                                PlacementTarget="{Binding ElementName=Button}"
                                StaysOpen="False">
                                <materialDesign:Card
                                    Padding="12"
                                    materialDesign:ElevationAssist.Elevation="Dp8"
                                    Style="{StaticResource ContentCardStyle}">
                                    <StackPanel>
                                        <TextBlock
                                            Margin="0,2,0,2"
                                            FontFamily="{DynamicResource MaterialDesignFont}"
                                            FontSize="{DynamicResource LabelMediumFontSize}"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            Text="R: إعادة تعيين" />
                                        <TextBlock
                                            Margin="0,2,0,2"
                                            FontFamily="{DynamicResource MaterialDesignFont}"
                                            FontSize="{DynamicResource LabelMediumFontSize}"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            Text="+/-: تكبير/تصغير" />
                                        <TextBlock
                                            Margin="0,2,0,2"
                                            FontFamily="{DynamicResource MaterialDesignFont}"
                                            FontSize="{DynamicResource LabelMediumFontSize}"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            Text="0: إعادة تعيين التكبير" />
                                        <TextBlock
                                            Margin="0,2,0,2"
                                            FontFamily="{DynamicResource MaterialDesignFont}"
                                            FontSize="{DynamicResource LabelMediumFontSize}"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            Text="الأسهم: سحب الصورة" />
                                        <TextBlock
                                            Margin="0,2,0,2"
                                            FontFamily="{DynamicResource MaterialDesignFont}"
                                            FontSize="{DynamicResource LabelMediumFontSize}"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            Text="Shift+أسهم: سحب دقيق" />
                                        <TextBlock
                                            Margin="0,2,0,2"
                                            FontFamily="{DynamicResource MaterialDesignFont}"
                                            FontSize="{DynamicResource LabelMediumFontSize}"
                                            Foreground="{DynamicResource MaterialDesignBody}"
                                            Text="Ctrl+R: قص الصورة" />
                                    </StackPanel>
                                </materialDesign:Card>
                            </Popup>
                        </Grid>

                    </StackPanel>

                    <!--  Image Preview Section  -->
                    <Grid Grid.Column="1">
                        <Grid.RowDefinitions>
                            <!--  Image Area/Rotation Controls  -->
                            <RowDefinition Height="*" />
                            <!--  Zoom Controls  -->
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <!--  Image Area  -->
                                <ColumnDefinition Width="*" />
                                <!--  Rotation Controls  -->
                                <ColumnDefinition Width="33" />
                            </Grid.ColumnDefinitions>

                            <materialDesign:Card
                                Grid.Column="0"
                                Padding="0"
                                Style="{StaticResource ContentCardStyle}">

                                <!--  Image Preview Section  -->
                                <Grid>
                                    <!--  Preview Container - Viewbox with precise 500x320 clipping  -->
                                    <Viewbox
                                        x:Name="PreviewContainer"
                                        Stretch="Uniform"
                                        StretchDirection="Both">

                                        <Viewbox.Clip>
                                            <RectangleGeometry Rect="0,0,500,320" />
                                        </Viewbox.Clip>

                                        <Grid Width="500" Height="320">
                                            <!--  Image Display with Transformations  -->
                                            <Image
                                                x:Name="PreviewImage"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Cursor="{Binding DragCursor}"
                                                MouseDown="PreviewImage_MouseDown"
                                                MouseLeave="PreviewImage_MouseLeave"
                                                MouseMove="PreviewImage_MouseMove"
                                                MouseUp="PreviewImage_MouseUp"
                                                RenderOptions.BitmapScalingMode="HighQuality"
                                                RenderOptions.EdgeMode="Aliased"
                                                Stretch="Uniform"
                                                StretchDirection="Both">

                                                <Image.RenderTransform>
                                                    <TransformGroup>
                                                        <ScaleTransform x:Name="ImageScaleTransform" ScaleX="{Binding ZoomScale}" ScaleY="{Binding ZoomScale}" />
                                                        <ScaleTransform x:Name="PostCropVisualScaleTransform" ScaleX="{Binding PostCropVisualScale}" ScaleY="{Binding PostCropVisualScale}" />
                                                        <RotateTransform x:Name="ImageRotateTransform" Angle="{Binding RotationAngle}" />
                                                        <TranslateTransform x:Name="ImageTranslateTransform" X="{Binding ImageOffsetX}" Y="{Binding ImageOffsetY}" />
                                                    </TransformGroup>
                                                </Image.RenderTransform>

                                                <Image.RenderTransformOrigin>0.5,0.5</Image.RenderTransformOrigin>
                                            </Image>

                                            <!--  Fixed Crop Rectangle - Non-interactive crop area overlay  -->
                                            <controls:InteractiveCropRectangle
                                                x:Name="InteractiveCropGuide"
                                                Width="500"
                                                Height="320"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                CropRect="{Binding CropRectangle, Mode=OneWay}"
                                                IsInteractive="False"
                                                MinimumSize="50,50"
                                                Visibility="{Binding CropGuideVisibility}" />


                                        </Grid>

                                    </Viewbox>

                                    <!--  No Image Placeholder  -->
                                    <StackPanel
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Opacity="0.6"
                                        Visibility="{Binding NoImagePlaceholderVisibility}">

                                        <materialDesign:PackIcon
                                            Width="64"
                                            Height="64"
                                            HorizontalAlignment="Center"
                                            AutomationProperties.Name="No image placeholder icon"
                                            Foreground="{DynamicResource MaterialDesignBodyLight}"
                                            Kind="ImageOutline" />

                                        <TextBlock
                                            Margin="0,16,0,0"
                                            HorizontalAlignment="Center"
                                            AutomationProperties.Name="No image placeholder text"
                                            FontFamily="{DynamicResource MaterialDesignFont}"
                                            FontSize="{DynamicResource BodyMediumFontSize}"
                                            Foreground="{DynamicResource MaterialDesignBodyLight}"
                                            Text="اختر صورة للبدء"
                                            TextAlignment="Center" />
                                    </StackPanel>

                                    <!--  Loading Indicator  -->
                                    <Grid
                                        Background="{DynamicResource MaterialDesignPaper}"
                                        Opacity="0.9"
                                        Visibility="{Binding LoadingIndicatorVisibility}">

                                        <ProgressBar
                                            Width="60"
                                            Height="60"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            AutomationProperties.LiveSetting="Polite"
                                            AutomationProperties.Name="Loading indicator"
                                            Foreground="{DynamicResource PrimaryHueMidBrush}"
                                            IsIndeterminate="True"
                                            Style="{StaticResource MaterialDesignCircularProgressBar}" />
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>
                            <!--  Zoom Controls  -->
                            <StackPanel
                                Grid.Column="1"
                                Width="30"
                                Margin="3,0,0,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center">

                                <TextBlock
                                    HorizontalAlignment="Left"
                                    AutomationProperties.LiveSetting="Polite"
                                    AutomationProperties.Name="Zoom percentage display"
                                    Style="{StaticResource CaptionTextStyle}"
                                    Text="{Binding ZoomPercentageText}" />

                                <!--  Zoom In Button  -->
                                <Button
                                    x:Name="ZoomInButton"
                                    Width="28"
                                    Height="28"
                                    Padding="0"
                                    AutomationProperties.Name="Zoom in"
                                    Command="{Binding ZoomInCommand}"
                                    Style="{StaticResource IconButtonStyle}"
                                    ToolTip="تكبير (+10%)">
                                    <materialDesign:PackIcon
                                        Width="18"
                                        Height="18"
                                        Kind="ZoomIn" />
                                </Button>

                                <Slider
                                    x:Name="ZoomSlider"
                                    Height="250"
                                    Panel.ZIndex="100"
                                    materialDesign:SliderAssist.FocusSliderOnClick="False"
                                    materialDesign:SliderAssist.OnlyShowFocusVisualWhileDragging="True"
                                    AutomationProperties.HelpText="Adjust image zoom from 25% to 400% (integer percentages only)"
                                    AutomationProperties.Name="Zoom percentage slider"
                                    IsSnapToTickEnabled="False"
                                    IsTabStop="True"
                                    Maximum="400"
                                    Minimum="25"
                                    Orientation="Vertical"
                                    TabIndex="1"
                                    TickFrequency="25"
                                    TickPlacement="TopLeft"
                                    Value="{Binding ZoomPercentage, Mode=TwoWay}" />

                                <!--  Zoom Out Button  -->
                                <Button
                                    x:Name="ZoomOutButton"
                                    Width="28"
                                    Height="28"
                                    Padding="0"
                                    AutomationProperties.Name="Zoom out"
                                    Command="{Binding ZoomOutCommand}"
                                    Style="{StaticResource IconButtonStyle}"
                                    ToolTip="تصغير (-10%)">
                                    <materialDesign:PackIcon
                                        Width="18"
                                        Height="18"
                                        Kind="ZoomOut" />
                                </Button>
                            </StackPanel>

                        </Grid>
                        <!--  Bidirectional Rotation Controls  -->
                        <StackPanel
                            Grid.Row="1"
                            Margin="0,6,0,0"
                            Orientation="Horizontal">

                            <!--  Clockwise Rotation Button  -->
                            <Button
                                x:Name="RotateClockwiseButton"
                                Width="28"
                                Height="28"
                                Padding="0"
                                AutomationProperties.Name="Rotate clockwise"
                                Command="{Binding RotateClockwiseCommand}"
                                Style="{StaticResource IconButtonStyle}"
                                ToolTip="دوران في اتجاه عقارب الساعة (+15°)">
                                <materialDesign:PackIcon
                                    Width="18"
                                    Height="18"
                                    Kind="RotateRight" />
                            </Button>

                            <!--  Bidirectional Rotation Slider  -->
                            <Slider
                                x:Name="RotationSlider"
                                Width="450"
                                Margin="0"
                                Panel.ZIndex="100"
                                materialDesign:SliderAssist.FocusSliderOnClick="False"
                                materialDesign:SliderAssist.OnlyShowFocusVisualWhileDragging="True"
                                AutomationProperties.HelpText="Adjust image rotation from -180 to +180 degrees (bidirectional rotation support)"
                                AutomationProperties.Name="Bidirectional rotation angle slider"
                                IsSnapToTickEnabled="False"
                                IsTabStop="True"
                                Maximum="180"
                                Minimum="-180"
                                Orientation="Horizontal"
                                TabIndex="2"
                                TickFrequency="15"
                                TickPlacement="TopLeft"
                                Value="{Binding RotationAngle, Mode=TwoWay}" />

                            <!--  Counterclockwise Rotation Button  -->
                            <Button
                                x:Name="RotateCounterclockwiseButton"
                                Width="28"
                                Height="28"
                                Padding="0"
                                AutomationProperties.Name="Rotate counterclockwise"
                                Command="{Binding RotateCounterclockwiseCommand}"
                                Style="{StaticResource IconButtonStyle}"
                                ToolTip="دوران عكس اتجاه عقارب الساعة (-15°)">
                                <materialDesign:PackIcon
                                    Width="18"
                                    Height="18"
                                    Kind="RotateLeft" />
                            </Button>

                            <TextBlock
                                Width="36"
                                Margin="3,0,12,0"
                                VerticalAlignment="Center"
                                AutomationProperties.LiveSetting="Polite"
                                AutomationProperties.Name="Zoom percentage display"
                                Style="{StaticResource CaptionTextStyle}"
                                Text="{Binding RotationAngleText}" />
                        </StackPanel>
                    </Grid>
                </Grid>

                <!--  Action buttons section  -->
                <userControls:SaveCancelButtonsControl
                    Grid.Row="3"
                    CancelClick="CancelButton_Click"
                    CancelTooltip="إلغاء التغييرات"
                    IsSaveEnabled="{Binding CanSave}"
                    SaveClick="SaveButton_Click"
                    SaveTooltip="حفظ الصورة الشخصية" />

            </Grid>
        </materialDesign:Card>
    </materialDesign:DialogHost>
</UserControl>
