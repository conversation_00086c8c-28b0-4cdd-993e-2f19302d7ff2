{"enabled": false, "name": "File Size Monitor", "description": "Monitors C# files to ensure they stay within recommended size limits: 50-150 lines for simple classes, 150-500 lines for standard classes, 500-1000 lines for complex classes, and flags files over 1000 lines for mandatory refactoring", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.cs"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified C# file and check its line count against the UFU2 file size guidelines:\n\n- Small/Very Focused Classes: 50-150 lines (simple Models, Helper classes, EventArgs)\n- Standard Classes: 150-500 lines (moderately complex ViewModels, Services, Converters) - TARGET RANGE\n- Larger Complex Classes: 500-1000 lines (complex ViewModels, large Services) - should be scrutinized\n- Classes > 1000 lines: MUST BE REFACTORED - hard limit\n\nIf the file exceeds guidelines:\n1. Identify the current line count and category\n2. Suggest specific refactoring strategies (extract methods, split into multiple classes, move logic to services)\n3. Recommend which parts could be extracted into separate files\n4. Provide concrete next steps for refactoring\n\nIf refactoring is required based on the analysis:\n1. Rename the original file to `<OriginalFileName>.backup`.\n2. Delete the original file (`<OriginalFileName>`).\n3. Create the new, refactored version with the exact same original file name (`<OriginalFileName>`).\n\nFocus on maintaining the MVVM architecture, service patterns, and code quality standards outlined in the UFU2 guidelines."}}