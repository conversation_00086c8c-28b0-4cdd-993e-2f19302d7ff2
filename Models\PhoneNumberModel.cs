using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace UFU2.Models
{
    /// <summary>
    /// Model class representing a phone number with its type for client registration.
    /// Implements INotifyPropertyChanged for proper WPF data binding support.
    /// Supports multiple phone types with Arabic display names.
    /// 
    /// This model follows UFU2 architectural patterns with:
    /// - Property change notification for real-time UI updates
    /// - Phone number validation and formatting support
    /// - Arabic RTL layout compatibility
    /// - MaterialDesign form field integration
    /// </summary>
    public class PhoneNumberModel : INotifyPropertyChanged
    {
        #region Private Fields

        private string _phoneNumber = string.Empty;
        private PhoneType _phoneType = PhoneType.Mobile;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the phone number.
        /// Should be formatted according to UFU2 standards (XXXX-XX-XX-XX or XXX-XX-XX-XX).
        /// </summary>
        public string PhoneNumber
        {
            get => _phoneNumber;
            set => SetProperty(ref _phoneNumber, value);
        }

        /// <summary>
        /// Gets or sets the type of phone number (Mobile, Home, Work, Fax).
        /// </summary>
        public PhoneType PhoneType
        {
            get => _phoneType;
            set => SetProperty(ref _phoneType, value);
        }

        /// <summary>
        /// Gets or sets whether this is the primary phone number.
        /// Only one phone number per client can be marked as primary.
        /// </summary>
        public bool IsPrimary { get; set; } = false;

        /// <summary>
        /// Gets the Arabic display name for the phone type.
        /// </summary>
        public string PhoneTypeDisplayName => GetPhoneTypeDisplayName(PhoneType);

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the PhoneNumberModel class with default values.
        /// </summary>
        public PhoneNumberModel()
        {
        }

        /// <summary>
        /// Initializes a new instance of the PhoneNumberModel class with specified values.
        /// </summary>
        /// <param name="phoneNumber">The phone number</param>
        /// <param name="phoneType">The phone type</param>
        public PhoneNumberModel(string phoneNumber, PhoneType phoneType)
        {
            _phoneNumber = phoneNumber ?? string.Empty;
            _phoneType = phoneType;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Resets the phone number model to default values.
        /// </summary>
        public void Reset()
        {
            PhoneNumber = string.Empty;
            PhoneType = PhoneType.Mobile;
        }

        /// <summary>
        /// Creates a copy of the current PhoneNumberModel instance.
        /// </summary>
        /// <returns>A new PhoneNumberModel with the same property values</returns>
        public PhoneNumberModel Clone()
        {
            return new PhoneNumberModel(PhoneNumber, PhoneType);
        }

        /// <summary>
        /// Validates the phone number model.
        /// </summary>
        /// <returns>True if the model is valid, false otherwise</returns>
        public bool IsValid()
        {
            // Check if phone number is not empty and contains only digits and formatting characters
            if (string.IsNullOrWhiteSpace(PhoneNumber))
                return false;

            // Remove formatting characters and check if we have 9 or 10 digits
            var digitsOnly = System.Text.RegularExpressions.Regex.Replace(PhoneNumber, @"\D", "");
            return digitsOnly.Length == 9 || digitsOnly.Length == 10;
        }

        /// <summary>
        /// Gets the Arabic display name for a phone type.
        /// </summary>
        /// <param name="phoneType">The phone type</param>
        /// <returns>Arabic display name</returns>
        public static string GetPhoneTypeDisplayName(PhoneType phoneType)
        {
            return phoneType switch
            {
                PhoneType.Mobile => "هاتف محمول",
                PhoneType.Home => "هاتف المنزل",
                PhoneType.Work => "هاتف العمل",
                PhoneType.Fax => "فاكس",
                _ => "هاتف محمول"
            };
        }

        /// <summary>
        /// Gets all available phone types with their Arabic display names.
        /// </summary>
        /// <returns>Dictionary of phone types and their display names</returns>
        public static Dictionary<PhoneType, string> GetAllPhoneTypes()
        {
            return new Dictionary<PhoneType, string>
            {
                { PhoneType.Mobile, GetPhoneTypeDisplayName(PhoneType.Mobile) },
                { PhoneType.Home, GetPhoneTypeDisplayName(PhoneType.Home) },
                { PhoneType.Work, GetPhoneTypeDisplayName(PhoneType.Work) },
                { PhoneType.Fax, GetPhoneTypeDisplayName(PhoneType.Fax) }
            };
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Occurs when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Sets the property and raises PropertyChanged event if the value has changed.
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">The new value</param>
        /// <param name="propertyName">Name of the property (automatically provided)</param>
        /// <returns>True if the property was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            
            // Also notify PhoneTypeDisplayName when PhoneType changes
            if (propertyName == nameof(PhoneType))
            {
                OnPropertyChanged(nameof(PhoneTypeDisplayName));
            }
            
            return true;
        }

        /// <summary>
        /// Raises the PropertyChanged event.
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// Enumeration of phone number types.
    /// </summary>
    public enum PhoneType
    {
        /// <summary>
        /// Mobile phone number
        /// </summary>
        Mobile,

        /// <summary>
        /// Home phone number
        /// </summary>
        Home,

        /// <summary>
        /// Work phone number
        /// </summary>
        Work,

        /// <summary>
        /// Fax number
        /// </summary>
        Fax
    }
}
