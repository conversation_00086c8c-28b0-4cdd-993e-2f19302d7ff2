<UserControl
    x:Class="UFU2.Views.Dialogs.AddActivityDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    d:DesignHeight="250"
    d:DesignWidth="300"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <materialDesign:Card
        Width="300"
        Height="250"
        Padding="0"
        materialDesign:ElevationAssist.Elevation="Dp8"
        Style="{StaticResource DialogBaseCardStyle}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  Header  -->
            <materialDesign:Card
                Grid.Row="0"
                Margin="0"
                Style="{DynamicResource HeaderCardStyle}">
                <TextBlock
                    HorizontalAlignment="Center"
                    Style="{StaticResource HeadlineStyle}"
                    Text="إضافة نشاط جديد" />
            </materialDesign:Card>

            <!--  Message with Activity Code  -->
            <TextBlock
                Grid.Row="1"
                Margin="12"
                HorizontalAlignment="Center"
                Style="{StaticResource BodyTextStyle}"
                TextWrapping="Wrap">
                <Run Text="نشاط برمز " />
                <Run FontWeight="Bold" Text="{Binding ActivityCode, Mode=OneWay}" />
                <Run Text=" غير موجود. هل تريد اضافته؟" />
            </TextBlock>

            <!--  Activity Description Input  -->
            <TextBox
                Grid.Row="2"
                Margin="12,4,12,24"
                materialDesign:HintAssist.Hint="وصف النشاط"
                materialDesign:TextFieldAssist.TextBoxViewMargin="12,0"
                materialDesign:ValidationAssist.UsePopup="True"
                MaxLength="500"
                Style="{StaticResource BaseOutlinedTextBoxStyle}"
                Text="{Binding ActivityDescription, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                TextWrapping="Wrap">
                <TextBox.Resources>
                    <Style TargetType="{x:Type materialDesign:SmartHint}">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                    </Style>
                </TextBox.Resources>
            </TextBox>

            <!--  Action Buttons  -->
            <userControls:SaveCancelButtonsControl
                Grid.Row="3"
                CancelClick="CancelButton_Click"
                CancelTooltip="إلغاء إضافة نشاط جديد وإغلاق النافذة"
                IsSaveEnabled="{Binding CanSave}"
                SaveClick="SaveButton_Click"
                SaveTooltip="حفظ نشاط جديد في قاعدة البايانات" />
        </Grid>
    </materialDesign:Card>
</UserControl>
