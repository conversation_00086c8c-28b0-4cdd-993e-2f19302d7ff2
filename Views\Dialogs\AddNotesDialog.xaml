<UserControl
    x:Class="UFU2.Views.Dialogs.AddNotesDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:UFU2.Models"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    d:DesignHeight="350"
    d:DesignWidth="300"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  Built-in converters  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <!--  Main dialog card  -->
    <materialDesign:Card
        x:Name="MainCard"
        Width="300"
        Height="350"
        Padding="0"
        Style="{StaticResource DialogBaseCardStyle}">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  Header section  -->
            <materialDesign:Card
                Grid.Row="0"
                Margin="0"
                Style="{DynamicResource HeaderCardStyle}">
                <TextBlock
                    HorizontalAlignment="Center"
                    Style="{StaticResource HeadlineStyle}"
                    Text="{Binding DialogHeader}" />
            </materialDesign:Card>

            <!--  Note content input  -->
            <TextBox
                x:Name="NoteContentTextBox"
                Grid.Row="1"
                Height="207"
                Margin="12"
                materialDesign:HintAssist.Hint="اكتب ملاحظتك هنا..."
                materialDesign:HintAssist.HintHorizontalAlignment="Center"
                materialDesign:TextFieldAssist.HasClearButton="True"
                materialDesign:TextFieldAssist.TextBoxViewMargin="12,0"
                materialDesign:TextFieldAssist.TextBoxViewVerticalAlignment="Top"
                materialDesign:TextFieldAssist.UnderlineBrush="{DynamicResource SurfaceBrush}"
                AcceptsTab="True"
                BorderThickness="0"
                CaretBrush="{DynamicResource TextFillColorPrimaryBrush}"
                FontFamily="{DynamicResource PrimaryFontFamily}"
                FontSize="{StaticResource BodyLargeFontSize}"
                FontWeight="{DynamicResource FontWeightBlack}"
                Text="{Binding NoteContent, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                TextWrapping="Wrap"
                ToolTip="أدخل محتوى الملاحظة">
                <TextBox.Resources>
                    <Style TargetType="{x:Type materialDesign:SmartHint}">
                        <Setter Property="HorizontalAlignment" Value="Center" />
                    </Style>

                </TextBox.Resources>
            </TextBox>

            <!--  Flag selection section  -->
            <materialDesign:PopupBox
                x:Name="FlagSelectionPopupBox"
                Margin="12,-220"
                Padding="0"
                HorizontalAlignment="Right"
                VerticalAlignment="Bottom"
                PlacementMode="LeftAndAlignMiddles"
                PopupHorizontalOffset="-12"
                PopupVerticalOffset="-1"
                StaysOpen="False"
                ToolTip="تحديد الأهمية">
                <materialDesign:PopupBox.ToggleContent>
                    <materialDesign:PackIcon Foreground="{Binding SelectedPriority, Converter={StaticResource PriorityToThemeColorConverter}}" Kind="Flag" />
                </materialDesign:PopupBox.ToggleContent>
                <StackPanel
                    Margin="0"
                    FlowDirection="LeftToRight"
                    Orientation="Horizontal">
                    <Button
                        x:Name="GreenFlagButton"
                        Margin="-5"
                        Padding="-5"
                        Click="GreenFlagButton_Click"
                        Style="{StaticResource IconButtonStyle}"
                        ToolTip="عادي">
                        <materialDesign:PackIcon
                            Width="16"
                            Height="16"
                            Foreground="{DynamicResource NormaleFlagPopup}"
                            Kind="Flag" />
                    </Button>
                    <Button
                        x:Name="OrangeFlagButton"
                        Margin="-5"
                        Padding="-5"
                        Click="OrangeFlagButton_Click"
                        Style="{StaticResource IconButtonStyle}"
                        ToolTip="هام">
                        <materialDesign:PackIcon
                            Width="16"
                            Height="16"
                            Foreground="{DynamicResource ImportantFlagPopup}"
                            Kind="Flag" />
                    </Button>
                    <Button
                        x:Name="RedFlagButton"
                        Margin="-5"
                        Padding="-5"
                        Click="RedFlagButton_Click"
                        Style="{StaticResource IconButtonStyle}"
                        ToolTip="هام جدا">
                        <materialDesign:PackIcon
                            Width="16"
                            Height="16"
                            Foreground="{DynamicResource VeryImportantFlagPopup}"
                            Kind="Flag" />
                    </Button>
                </StackPanel>
            </materialDesign:PopupBox>

            <!--  Action buttons section  -->
            <userControls:SaveCancelButtonsControl
                Grid.Row="2"
                CancelCommand="{Binding CancelCommand}"
                CancelTooltip="إلغاء التغييرات"
                SaveCommand="{Binding SaveCommand}"
                SaveTooltip="حفظ الملاحظة" />

        </Grid>
    </materialDesign:Card>
</UserControl>
