using System.Windows.Media;

namespace UFU2.Models
{
    /// <summary>
    /// Theme configuration model for custom window chrome styling
    /// </summary>
    public class WindowChromeTheme
    {
        /// <summary>
        /// Background brush for the title bar
        /// </summary>
        public Brush TitleBarBackground { get; set; } = Brushes.White;

        /// <summary>
        /// Foreground brush for title bar text and icons
        /// </summary>
        public Brush TitleBarForeground { get; set; } = Brushes.Black;

        /// <summary>
        /// Brush for the window border
        /// </summary>
        public Brush WindowBorderBrush { get; set; } = Brushes.Gray;

        /// <summary>
        /// Background brush for button hover state
        /// </summary>
        public Brush ButtonHoverBackground { get; set; } = Brushes.LightGray;

        /// <summary>
        /// Background brush for button pressed state
        /// </summary>
        public Brush ButtonPressedBackground { get; set; } = Brushes.DarkGray;

        /// <summary>
        /// Opacity level for the title bar (0.0 to 1.0)
        /// </summary>
        public double TitleBarOpacity { get; set; } = 1.0;
    }
}