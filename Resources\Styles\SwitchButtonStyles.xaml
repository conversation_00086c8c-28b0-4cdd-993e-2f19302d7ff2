﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">


    <!--
        ========================================
        TOGGLE SWITCH STYLES
        ========================================
    -->

    <!--  ToggleSwitch Style  -->
    <Style x:Key="ToggleSwitchStyle" TargetType="ToggleButton">
        <Setter Property="Width" Value="36" />
        <Setter Property="Height" Value="27" />
        <Setter Property="FlowDirection" Value="LeftToRight" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Grid>
                        <!--  Track Background  -->
                        <Border
                            x:Name="TrackBorder"
                            Width="36"
                            Height="12"
                            Background="{DynamicResource TrackOffBackground}"
                            CornerRadius="6" />

                        <!--  Thumb with PackIcon Content  -->
                        <Border
                            x:Name="ThumbBorder"
                            Width="18"
                            Height="18"
                            HorizontalAlignment="Left"
                            Background="{DynamicResource ThumbOffBackground}"
                            BorderBrush="{DynamicResource ThumbOffForeground}"
                            BorderThickness="0"
                            CornerRadius="9">
                            <Border.RenderTransform>
                                <TranslateTransform x:Name="ThumbTransform" X="0" />
                            </Border.RenderTransform>

                            <!--  PackIcon Content  -->
                            <materialDesign:PackIcon
                                x:Name="ThumbIcon"
                                Width="16"
                                Height="16"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource ThumbOffForeground}"
                                Kind="Minus" />
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!--  Checked State (ON)  -->
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ThumbBorder" Property="Background" Value="{DynamicResource ThumbOnBackground}" />
                            <Setter TargetName="ThumbIcon" Property="Kind" Value="Check" />
                            <Setter TargetName="ThumbIcon" Property="Foreground" Value="{DynamicResource ThumbOnForeground}" />
                            <Setter TargetName="TrackBorder" Property="Background" Value="{DynamicResource TrackOnBackground}" />
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation
                                            Storyboard.TargetName="ThumbTransform"
                                            Storyboard.TargetProperty="X"
                                            From="0"
                                            To="21"
                                            Duration="0:0:0.2" />
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation
                                            Storyboard.TargetName="ThumbTransform"
                                            Storyboard.TargetProperty="X"
                                            From="21"
                                            To="0"
                                            Duration="0:0:0.2" />
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>

                        <!--  Disabled State  -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="TrackBorder" Property="Background" Value="{DynamicResource DisabledTextBrush}" />
                            <Setter TargetName="ThumbBorder" Property="Background" Value="{DynamicResource TertiaryTextBrush}" />
                            <Setter TargetName="ThumbIcon" Property="Foreground" Value="{DynamicResource DisabledTextBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Activity Tab Radio Button Style  -->
    <Style x:Key="TabRadioButtonStyle" TargetType="RadioButton">
        <Setter Property="Foreground" Value="{DynamicResource RadioForegroundColor}" />
        <Setter Property="FontSize" Value="{DynamicResource LabelLargeFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBold}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <Border
                        x:Name="Border"
                        Padding="16,8"
                        Background="{DynamicResource RadioBackgroundColor}"
                        BorderBrush="{DynamicResource RadioBorderBrush}"
                        BorderThickness="0,0,0,2"
                        CornerRadius="4,4,0,0">
                        <ContentPresenter
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsChecked" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource RadioHover}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource RadioBorderHover}" />
                        </MultiTrigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource RadioBackgroundChecked}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource RadioBorderChecked}" />
                            <Setter TargetName="Border" Property="BorderThickness" Value="0,0,0,3" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Margin" Value="2,0" />
        <Setter Property="Cursor" Value="Hand" />
    </Style>

    <!--  Duplicate Radio Button Style  -->
    <Style x:Key="DuplicateRadioButtonStyle" TargetType="RadioButton">
        <Setter Property="Foreground" Value="{DynamicResource RadioForegroundColor}" />
        <Setter Property="FontSize" Value="{DynamicResource LabelLargeFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBold}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <Border
                        x:Name="Border"
                        Margin="0,3"
                        Padding="12,3"
                        Background="{DynamicResource RadioBackgroundColor}"
                        BorderBrush="{DynamicResource RadioBorderBrush}"
                        BorderThickness="0,0,0,1"
                        CornerRadius="4,4,0,0">
                        <ContentPresenter
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsChecked" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource RadioHover}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource RadioBorderHover}" />
                        </MultiTrigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource RadioBackgroundChecked}" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="{DynamicResource RadioBorderChecked}" />
                            <Setter TargetName="Border" Property="BorderThickness" Value="0,0,0,1.5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Margin" Value="2,0" />
        <Setter Property="Cursor" Value="Hand" />
    </Style>

    <!--
        ========================================
        FILE CHECK COMPONENT STYLES
        ========================================
    -->

    <!--  Base File Check Chip Style  -->
    <Style x:Key="FileCheckChipBaseStyle" TargetType="CheckBox">
        <Setter Property="Foreground" Value="{DynamicResource ChipForegroundBase}" />
        <Setter Property="FontSize" Value="{DynamicResource LabelMediumFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBold}" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Border.CornerRadius" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <Border
                        x:Name="ChipBorder"
                        Padding="12,6"
                        Background="{DynamicResource ChipBackgroundBase}"
                        BorderBrush="{DynamicResource ChipBorderBase}"
                        BorderThickness="1"
                        CornerRadius="{TemplateBinding Border.CornerRadius}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  Icon that shows when checked  -->
                            <materialDesign:PackIcon
                                x:Name="CheckIcon"
                                Grid.Column="0"
                                Width="15"
                                Height="15"
                                Margin="0"
                                HorizontalAlignment="Right"
                                Foreground="{TemplateBinding Foreground}"
                                Kind="Check"
                                Visibility="Collapsed" />

                            <!--  Content  -->
                            <ContentPresenter
                                Grid.Column="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding Content}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ChipBorder" Property="Background" Value="{DynamicResource ChipHoverBase}" />
                            <Setter TargetName="ChipBorder" Property="BorderBrush" Value="{DynamicResource ChipBorderHover}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ChipBorder" Property="Background" Value="{DynamicResource ChipBackgroundSelected}" />
                            <Setter TargetName="ChipBorder" Property="BorderBrush" Value="{DynamicResource ChipBorderSelected}" />
                            <Setter Property="Foreground" Value="{DynamicResource ChipForegroundBase}" />
                            <Setter TargetName="CheckIcon" Property="Visibility" Value="Visible" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  First File Check Chip Style (Left-most with rounded left corners)  -->
    <Style
        x:Key="FileCheckChipFirstStyle"
        BasedOn="{StaticResource FileCheckChipBaseStyle}"
        TargetType="CheckBox">
        <Setter Property="Border.CornerRadius" Value="9,0,0,9" />
    </Style>

    <!--  Middle File Check Chip Style (No rounded corners)  -->
    <Style
        x:Key="FileCheckChipMiddleStyle"
        BasedOn="{StaticResource FileCheckChipBaseStyle}"
        TargetType="CheckBox">
        <!--  Inherits CornerRadius="0" from base style, no override needed  -->
    </Style>

    <!--  Last File Check Chip Style (Right-most with rounded right corners)  -->
    <Style
        x:Key="FileCheckChipLastStyle"
        BasedOn="{StaticResource FileCheckChipBaseStyle}"
        TargetType="CheckBox">
        <Setter Property="Border.CornerRadius" Value="0,9,9,0" />
    </Style>

    <!--  Default File Check Chip Style (for backward compatibility)  -->
    <Style
        x:Key="FileCheckChipStyle"
        BasedOn="{StaticResource FileCheckChipFirstStyle}"
        TargetType="CheckBox" />

    <!--  Payment Year Chip Style  -->
    <Style x:Key="PaymentYearChipStyle" TargetType="CheckBox">
        <Setter Property="Foreground" Value="{DynamicResource YearBorderNotPaid}" />
        <Setter Property="FontSize" Value="{DynamicResource LabelMediumFontSize}" />
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}" />
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBold}" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="MinWidth" Value="54" />
        <Setter Property="Height" Value="27" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <Border
                        x:Name="ChipBorder"
                        Padding="8,0"
                        Background="{DynamicResource YearBackgroundPaid}"
                        BorderBrush="{DynamicResource YearBorderPaid}"
                        BorderThickness="1"
                        CornerRadius="7">
                        <!--  Changed CornerRadius to 7  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  Icon  -->
                            <materialDesign:PackIcon
                                x:Name="ChipIcon"
                                Grid.Column="0"
                                Width="16"
                                Height="16"
                                Margin="0"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource YearBorderPaid}"
                                Kind="Check" />

                            <!--  Content  -->
                            <ContentPresenter
                                x:Name="YearsText"
                                Grid.Column="1"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding Content}"
                                TextElement.Foreground="{DynamicResource YearBorderPaid}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ChipBorder" Property="Background" Value="{DynamicResource YearBackgroundNotPaid}" />
                            <Setter TargetName="ChipBorder" Property="BorderBrush" Value="{DynamicResource YearBorderNotPaid}" />
                            <Setter TargetName="YearsText" Property="TextElement.Foreground" Value="{DynamicResource YearBorderNotPaid}" />
                            <Setter TargetName="ChipIcon" Property="Kind" Value="Close" />
                            <Setter TargetName="ChipIcon" Property="Foreground" Value="{DynamicResource YearBorderNotPaid}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <ColorAnimation
                                            Storyboard.TargetName="ChipBorder"
                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                            To="#4DF90000"
                                            Duration="0:0:0.2" />
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <ColorAnimation
                                            Storyboard.TargetName="ChipBorder"
                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                            To="#4D07873B"
                                            Duration="0:0:0.2" />
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
