<UserControl
    x:Class="UFU2.Views.Dialogs.AddCraftTypeDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    Width="450"
    MaxHeight="360"
    FlowDirection="RightToLeft"
    Loaded="AddCraftTypeDialog_Loaded"
    Unloaded="AddCraftTypeDialog_Unloaded">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  Boolean to Visibility Converter  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!--  Inverse Boolean to Visibility Converter  -->
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <materialDesign:Card
        x:Name="MainCard"
        Width="450"
        Height="360"
        Padding="0"
        Style="{StaticResource DialogBaseCardStyle}">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  Header  -->
            <materialDesign:Card
                Grid.Row="0"
                Margin="0"
                Style="{DynamicResource HeaderCardStyle}">
                <TextBlock
                    HorizontalAlignment="Center"
                    Style="{StaticResource HeadlineStyle}"
                    Text="إضافة نوع حرفة جديد" />

            </materialDesign:Card>

            <!--  Message Section  -->
            <StackPanel
                Grid.Row="1"
                Margin="12,3,12,9"
                Orientation="Horizontal">
                <TextBlock Style="{StaticResource BodyTextStyle}" Text="إضافة نشاط برمز" />
                <TextBox
                    x:Name="CodeTextBox"
                    Width="81"
                    Margin="3,0"
                    materialDesign:TextFieldAssist.CharacterCounterVisibility="Hidden"
                    FontFamily="Consolas"
                    MaxLength="9"
                    Style="{StaticResource UnderlineTextBoxStyle}"
                    Text="{Binding CraftType.Code, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                    TextAlignment="Center"
                    TextChanged="CodeTextBox_TextChanged"
                    ToolTip="رمز الحرفة (تنسيق: XX-XX-XXX)" />
                <TextBlock Style="{StaticResource BodyTextStyle}" Text="إلى قاعدة البيانات." />
            </StackPanel>

            <!--  Input Fields  -->
            <ScrollViewer
                Grid.Row="2"
                Margin="24,8,24,0"
                VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!--  Description Field (Required)  -->
                    <materialDesign:Card
                        Margin="0,0,0,12"
                        Padding="0"
                        Style="{StaticResource ContentCardStyle}">
                        <TextBox
                            x:Name="DescriptionTextBox"
                            Margin="12,12,12,16"
                            materialDesign:HintAssist.Hint="وصف الحرفة *"
                            materialDesign:HintAssist.IsFloating="True"
                            materialDesign:TextFieldAssist.CharacterCounterVisibility="Visible"
                            MaxLength="1000"
                            Style="{StaticResource MultiLineTextBoxStyle}"
                            Text="{Binding CraftType.Description, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                            TextWrapping="Wrap"
                            ToolTip="وصف الحرفة (مطلوب - 3 أحرف على الأقل)" />
                    </materialDesign:Card>

                    <!--  Content Field (Optional, Multiline)  -->
                    <materialDesign:Card
                        Margin="0,0,0,12"
                        Padding="0,5"
                        Style="{StaticResource ContentCardStyle}">
                        <TextBox
                            x:Name="ContentTextBox"
                            MaxHeight="54"
                            Margin="12,12,12,16"
                            materialDesign:HintAssist.Hint="محتوى الحرفة"
                            materialDesign:HintAssist.IsFloating="True"
                            materialDesign:TextFieldAssist.CharacterCounterVisibility="Visible"
                            AcceptsReturn="True"
                            GotFocus="ContentTextBox_GotFocus"
                            MaxLength="2000"
                            MinLines="3"
                            PreviewKeyDown="ContentTextBox_PreviewKeyDown"
                            Style="{StaticResource MultiLineTextBoxStyle}"
                            Text="{Binding CraftType.Content, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                            TextChanged="ContentTextBox_TextChanged"
                            TextWrapping="Wrap"
                            ToolTip="تفاصيل محتوى الحرفة والأنشطة المتعلقة بها (اختياري)"
                            VerticalScrollBarVisibility="Auto" />
                    </materialDesign:Card>

                    <!--  Secondary Field (Optional, Multiline)  -->
                    <materialDesign:Card
                        Margin="0,0,0,12"
                        Padding="0"
                        Style="{StaticResource ContentCardStyle}">

                        <TextBox
                            x:Name="SecondaryTextBox"
                            MaxHeight="54"
                            Margin="12,12,12,16"
                            materialDesign:HintAssist.Hint="معلومات ثانوية"
                            materialDesign:HintAssist.IsFloating="True"
                            materialDesign:TextFieldAssist.CharacterCounterVisibility="Visible"
                            AcceptsReturn="True"
                            GotFocus="SecondaryTextBox_GotFocus"
                            MaxLength="1000"
                            MinLines="3"
                            PreviewKeyDown="SecondaryTextBox_PreviewKeyDown"
                            Style="{StaticResource MultiLineTextBoxStyle}"
                            Text="{Binding CraftType.Secondary, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                            TextChanged="SecondaryTextBox_TextChanged"
                            TextWrapping="Wrap"
                            ToolTip="معلومات إضافية أو ثانوية عن الحرفة (اختياري)"
                            VerticalScrollBarVisibility="Auto" />
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>

            <!--  Footer Buttons  -->
            <StackPanel
                Grid.Row="3"
                Margin="24,0,24,12"
                HorizontalAlignment="Right"
                Orientation="Horizontal">

                <!--  Cancel Button  -->
                <Button
                    x:Name="CancelButton"
                    Margin="0,0,8,0"
                    Command="{Binding CancelCommand}"
                    Content="إلغاء"
                    IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanToVisibilityConverter}}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="إلغاء العملية بدون حفظ" />

                <!--  Save Button  -->
                <Button
                    x:Name="SaveButton"
                    Command="{Binding SaveCommand}"
                    Content="حفظ"
                    IsEnabled="{Binding CanSave}"
                    Style="{StaticResource PrimaryButtonStyle}"
                    ToolTip="حفظ نوع الحرفة الجديد"
                    Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</UserControl>
