using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using UFU2.Common;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.ViewModels;

namespace UFU2.Views.Dialogs
{
    /// <summary>
    /// Dialog for adding missing craft types to the CraftTypeBase database.
    /// Provides a user-friendly interface for creating new craft type entries when they don't exist during lookup operations.
    /// </summary>
    public partial class AddCraftTypeDialog : UserControl
    {
        #region Private Fields

        private readonly AddCraftTypeDialogViewModel _viewModel;
        private CraftTypeBaseModel? _result;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the AddCraftTypeDialog class.
        /// </summary>
        /// <param name="craftCode">The craft code that was not found</param>
        public AddCraftTypeDialog(string craftCode)
        {
            InitializeComponent();

            // Initialize ViewModel
            _viewModel = new AddCraftTypeDialogViewModel(craftCode);
            DataContext = _viewModel;

            // Subscribe to ViewModel events
            _viewModel.DialogResultRequested += ViewModel_DialogResultRequested;

            LoggingService.LogDebug($"AddCraftTypeDialog initialized for code: {craftCode}", "AddCraftTypeDialog");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the result of the dialog operation.
        /// </summary>
        /// <returns>The created CraftTypeBaseModel if successful, null if cancelled</returns>
        public CraftTypeBaseModel? GetResult()
        {
            return _result;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Handles the DialogResultRequested event from the ViewModel.
        /// Closes the dialog with the appropriate result.
        /// </summary>
        /// <param name="success">True if the operation was successful, false if cancelled</param>
        /// <param name="craftType">The created craft type or null if cancelled</param>
        private void ViewModel_DialogResultRequested(bool success, CraftTypeBaseModel? craftType)
        {
            try
            {
                _result = success ? craftType : null;

                LoggingService.LogDebug($"AddCraftTypeDialog closing with result: {success}", "AddCraftTypeDialog");

                // Close dialog with the result
                DialogHost.CloseDialogCommand.Execute(success ? craftType : false, this);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing AddCraftTypeDialog: {ex.Message}", "AddCraftTypeDialog");
                
                // Fallback: try to close with false result
                try
                {
                    DialogHost.CloseDialogCommand.Execute(false, this);
                }
                catch (Exception fallbackEx)
                {
                    LoggingService.LogError($"Error in fallback dialog close: {fallbackEx.Message}", "AddCraftTypeDialog");
                }
            }
        }

        #endregion

        #region Private Methods



        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the TextChanged event for the CodeTextBox to provide real-time formatting.
        /// </summary>
        private void CodeTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is not TextBox textBox)
                return;

            try
            {
                // Store current cursor position
                int cursorPosition = textBox.SelectionStart;
                string originalText = textBox.Text;

                // Use shared utility for formatting
                string formattedText = CraftCodeFormatter.ProcessCraftCodeInput(originalText);

                // Only update if the text actually changed
                if (formattedText != originalText)
                {
                    // Update the text
                    textBox.Text = formattedText;

                    // Adjust cursor position using shared utility
                    int newCursorPosition = CraftCodeFormatter.CalculateNewCursorPosition(originalText, formattedText, cursorPosition);
                    textBox.SelectionStart = Math.Min(newCursorPosition, formattedText.Length);
                }

                // Update the ViewModel property directly to trigger validation
                if (_viewModel != null && _viewModel.CraftType != null)
                {
                    _viewModel.CraftType.Code = formattedText;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error formatting craft code: {ex.Message}", "AddCraftTypeDialog");
            }
        }

        /// <summary>
        /// Handles the PreviewKeyDown event for the ContentTextBox to provide auto-formatting with bullet points.
        /// </summary>
        private void ContentTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                if (e.Key == Key.Enter)
                {
                    HandleMultilineAutoFormatting(textBox, e);
                }
                else if (e.Key == Key.Back)
                {
                    HandleBulletRemoval(textBox, e);
                }
            }
        }

        /// <summary>
        /// Handles the PreviewKeyDown event for the SecondaryTextBox to provide auto-formatting with bullet points.
        /// </summary>
        private void SecondaryTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                if (e.Key == Key.Enter)
                {
                    HandleMultilineAutoFormatting(textBox, e);
                }
                else if (e.Key == Key.Back)
                {
                    HandleBulletRemoval(textBox, e);
                }
            }
        }

        /// <summary>
        /// Handles the GotFocus event for the ContentTextBox to add initial bullet formatting.
        /// </summary>
        private void ContentTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                HandleInitialBulletFormatting(textBox);
            }
        }

        /// <summary>
        /// Handles the TextChanged event for the ContentTextBox to add initial bullet formatting.
        /// </summary>
        private void ContentTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                HandleInitialBulletFormatting(textBox);
            }
        }

        /// <summary>
        /// Handles the GotFocus event for the SecondaryTextBox to add initial bullet formatting.
        /// </summary>
        private void SecondaryTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                HandleInitialBulletFormatting(textBox);
            }
        }

        /// <summary>
        /// Handles the TextChanged event for the SecondaryTextBox to add initial bullet formatting.
        /// </summary>
        private void SecondaryTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                HandleInitialBulletFormatting(textBox);
            }
        }

        /// <summary>
        /// Handles initial bullet formatting for empty multiline TextBoxes.
        /// Adds "- " prefix when user starts typing in an empty field.
        /// </summary>
        /// <param name="textBox">The TextBox to format</param>
        private void HandleInitialBulletFormatting(TextBox textBox)
        {
            try
            {
                string currentText = textBox.Text ?? string.Empty;

                // Only apply initial formatting if:
                // 1. Text is not empty (user has started typing)
                // 2. Text doesn't already start with "- "
                // 3. Text is not just whitespace
                if (!string.IsNullOrWhiteSpace(currentText) &&
                    !currentText.StartsWith("- ") &&
                    currentText.Trim().Length > 0)
                {
                    // Store current cursor position
                    int cursorPosition = textBox.SelectionStart;

                    // Add bullet prefix
                    string formattedText = "- " + currentText;

                    // Update text
                    textBox.Text = formattedText;

                    // Adjust cursor position (add 2 for "- ")
                    int newCursorPosition = cursorPosition + 2;
                    textBox.SelectionStart = Math.Min(newCursorPosition, formattedText.Length);
                    textBox.SelectionLength = 0;

                    LoggingService.LogDebug($"Initial bullet formatting applied to TextBox. Original length: {currentText.Length}, new length: {formattedText.Length}", "AddCraftTypeDialog");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in initial bullet formatting: {ex.Message}", "AddCraftTypeDialog");
                // Don't throw - let the user continue typing normally
            }
        }

        /// <summary>
        /// Handles auto-formatting for multiline TextBoxes by inserting "- " at the beginning of new lines.
        /// </summary>
        /// <param name="textBox">The TextBox that triggered the event</param>
        /// <param name="e">The KeyEventArgs for the Enter key press</param>
        private void HandleMultilineAutoFormatting(TextBox textBox, KeyEventArgs e)
        {
            try
            {
                // Get current cursor position before handling the event
                int cursorPosition = textBox.SelectionStart;
                string currentText = textBox.Text ?? string.Empty;

                LoggingService.LogDebug($"Auto-formatting triggered at cursor position: {cursorPosition}, text length: {currentText.Length}", "AddCraftTypeDialog");

                // Mark the event as handled to prevent default Enter behavior
                e.Handled = true;

                // Create the text to insert (newline + bullet)
                string bulletText = Environment.NewLine + "- ";

                // Insert the formatted text at cursor position
                string beforeCursor = currentText.Substring(0, cursorPosition);
                string afterCursor = currentText.Substring(cursorPosition);
                string newText = beforeCursor + bulletText + afterCursor;

                // Update the text directly
                textBox.Text = newText;

                // Set cursor position after the inserted bullet
                int newCursorPosition = cursorPosition + bulletText.Length;
                textBox.SelectionStart = newCursorPosition;
                textBox.SelectionLength = 0;

                // Ensure the TextBox has focus
                textBox.Focus();

                LoggingService.LogDebug($"Auto-formatting completed. New cursor position: {newCursorPosition}, new text length: {newText.Length}", "AddCraftTypeDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in multiline auto-formatting: {ex.Message}", "AddCraftTypeDialog");
                // If there's an error, allow default Enter behavior
                e.Handled = false;
            }
        }

        /// <summary>
        /// Handles bullet removal when Backspace is pressed at the beginning of a bulleted line.
        /// Removes the "- " prefix when cursor is positioned immediately after it.
        /// </summary>
        /// <param name="textBox">The TextBox that triggered the event</param>
        /// <param name="e">The KeyEventArgs for the Backspace key press</param>
        private void HandleBulletRemoval(TextBox textBox, KeyEventArgs e)
        {
            try
            {
                string currentText = textBox.Text ?? string.Empty;
                int cursorPosition = textBox.SelectionStart;

                LoggingService.LogDebug($"Backspace pressed at cursor position: {cursorPosition}, text length: {currentText.Length}", "AddCraftTypeDialog");

                // Check if we should remove bullet formatting
                if (ShouldRemoveBullet(currentText, cursorPosition))
                {
                    // Mark event as handled to prevent default Backspace behavior
                    e.Handled = true;

                    // Remove the "- " prefix
                    string newText = RemoveBulletPrefix(currentText, cursorPosition);

                    // Update the text
                    textBox.Text = newText;

                    // Position cursor at the beginning of the line where bullet was removed
                    int newCursorPosition = GetCursorPositionAfterBulletRemoval(currentText, cursorPosition);
                    textBox.SelectionStart = newCursorPosition;
                    textBox.SelectionLength = 0;

                    // Ensure the TextBox has focus
                    textBox.Focus();

                    LoggingService.LogDebug($"Bullet removed. New cursor position: {newCursorPosition}, new text length: {newText.Length}", "AddCraftTypeDialog");
                }
                // If conditions not met, let default Backspace behavior occur (e.Handled remains false)
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in bullet removal: {ex.Message}", "AddCraftTypeDialog");
                // Don't mark as handled if there's an error, let default behavior occur
            }
        }

        /// <summary>
        /// Determines if bullet formatting should be removed based on cursor position and text content.
        /// </summary>
        /// <param name="text">Current text content</param>
        /// <param name="cursorPosition">Current cursor position</param>
        /// <returns>True if bullet should be removed</returns>
        private bool ShouldRemoveBullet(string text, int cursorPosition)
        {
            if (string.IsNullOrEmpty(text) || cursorPosition < 2)
                return false;

            // Check if cursor is immediately after "- " at the beginning of a line
            if (cursorPosition == 2 && text.StartsWith("- "))
            {
                return true;
            }

            // Check if cursor is immediately after "- " at the beginning of any line (after newline)
            if (cursorPosition >= 2)
            {
                // Look for pattern: \n- (newline followed by dash-space)
                int lineStartIndex = text.LastIndexOf('\n', cursorPosition - 1);
                if (lineStartIndex >= 0)
                {
                    int bulletStartIndex = lineStartIndex + 1;
                    if (bulletStartIndex + 2 == cursorPosition &&
                        bulletStartIndex + 1 < text.Length &&
                        text.Substring(bulletStartIndex, 2) == "- ")
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Removes the bullet prefix from the current line.
        /// </summary>
        /// <param name="text">Current text content</param>
        /// <param name="cursorPosition">Current cursor position</param>
        /// <returns>Text with bullet prefix removed</returns>
        private string RemoveBulletPrefix(string text, int cursorPosition)
        {
            if (cursorPosition == 2 && text.StartsWith("- "))
            {
                // Remove bullet from beginning of text
                return text.Substring(2);
            }

            // Find the line start and remove bullet from that line
            int lineStartIndex = text.LastIndexOf('\n', cursorPosition - 1);
            if (lineStartIndex >= 0)
            {
                int bulletStartIndex = lineStartIndex + 1;
                if (bulletStartIndex + 2 <= text.Length &&
                    text.Substring(bulletStartIndex, 2) == "- ")
                {
                    // Remove the "- " from this line
                    return text.Remove(bulletStartIndex, 2);
                }
            }

            return text; // Fallback: return original text if no bullet found
        }

        /// <summary>
        /// Calculates the cursor position after bullet removal.
        /// </summary>
        /// <param name="originalText">Original text before bullet removal</param>
        /// <param name="originalCursorPosition">Original cursor position</param>
        /// <returns>New cursor position</returns>
        private int GetCursorPositionAfterBulletRemoval(string originalText, int originalCursorPosition)
        {
            if (originalCursorPosition == 2 && originalText.StartsWith("- "))
            {
                // Cursor was at beginning after bullet, move to start of text
                return 0;
            }

            // For bullets in middle of text, move cursor back by 2 positions (length of "- ")
            return Math.Max(0, originalCursorPosition - 2);
        }

        /// <summary>
        /// Handles the Loaded event to set focus on the description field.
        /// </summary>
        private void AddCraftTypeDialog_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Set focus to the description field since code is read-only
                var descriptionTextBox = FindName("DescriptionTextBox") as TextBox;
                descriptionTextBox?.Focus();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting focus in AddCraftTypeDialog: {ex.Message}", "AddCraftTypeDialog");
            }
        }

        /// <summary>
        /// Handles the Unloaded event to clean up resources.
        /// </summary>
        private void AddCraftTypeDialog_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Unsubscribe from ViewModel events
                if (_viewModel != null)
                {
                    _viewModel.DialogResultRequested -= ViewModel_DialogResultRequested;
                }

                LoggingService.LogDebug("AddCraftTypeDialog unloaded and cleaned up", "AddCraftTypeDialog");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during AddCraftTypeDialog cleanup: {ex.Message}", "AddCraftTypeDialog");
            }
        }

        #endregion

        #region Static Factory Methods

        /// <summary>
        /// Creates and shows the AddCraftTypeDialog for the specified craft code.
        /// </summary>
        /// <param name="craftCode">The craft code that was not found</param>
        /// <param name="dialogHostIdentifier">The DialogHost identifier to use</param>
        /// <returns>The created CraftTypeBaseModel if successful, null if cancelled</returns>
        public static async Task<CraftTypeBaseModel?> ShowDialogAsync(string craftCode, string dialogHostIdentifier = "NewClientDialogHost")
        {
            try
            {
                LoggingService.LogInfo($"Showing AddCraftTypeDialog for code: {craftCode}", "AddCraftTypeDialog");

                var dialog = new AddCraftTypeDialog(craftCode);
                var result = await DialogHost.Show(dialog, dialogHostIdentifier);

                if (result is CraftTypeBaseModel craftType)
                {
                    LoggingService.LogInfo($"AddCraftTypeDialog completed successfully: {craftType.Code} - {craftType.Description}", "AddCraftTypeDialog");
                    return craftType;
                }
                else
                {
                    LoggingService.LogDebug("AddCraftTypeDialog was cancelled", "AddCraftTypeDialog");
                    return null;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing AddCraftTypeDialog: {ex.Message}", "AddCraftTypeDialog");
                ErrorManager.ShowUserErrorToast(
                    "حدث خطأ أثناء فتح نافذة إضافة الحرفة",
                    "خطأ في النظام",
                    "AddCraftTypeDialog");
                return null;
            }
        }

        #endregion
    }
}
