using System.Windows;
using UFU2.Common;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Static utility class for managing window state transitions and native window behaviors.
    /// Provides methods for minimizing, maximizing, restoring, and closing windows with proper error handling.
    /// Supports native Windows animations and behaviors while maintaining consistency with UFU2 architecture.
    /// </summary>
    public static class WindowStateManager
    {
        #region Window State Management

        /// <summary>
        /// Minimizes the specified window with native Windows animation
        /// </summary>
        /// <param name="window">The window to minimize</param>
        /// <returns>True if the operation was successful, false otherwise</returns>
        public static bool MinimizeWindow(Window window)
        {
            try
            {
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot minimize null window", "WindowStateManager");
                    ErrorManager.ShowUserWarningToast(
                        "لا يمكن تصغير نافذة فارغة",
                        "خطأ في المعاملات",
                        "WindowStateManager"
                    );
                    return false;
                }

                // Validate that minimizing is allowed
                if (!IsValidStateTransition(window, WindowState.Minimized))
                {
                    LoggingService.LogWarning($"Minimize operation not allowed for window: {window.Title} (ResizeMode: {window.ResizeMode})", "WindowStateManager");
                    ErrorManager.ShowUserWarningToast(
                        "لا يمكن تصغير هذه النافذة بسبب إعداداتها",
                        "عملية غير مسموحة",
                        "WindowStateManager"
                    );
                    return false;
                }

                if (window.WindowState == WindowState.Minimized)
                {
                    LoggingService.LogDebug($"Window is already minimized: {window.Title}", "WindowStateManager");
                    return true;
                }

                LoggingService.LogDebug($"Minimizing window: {window.Title} (Current state: {window.WindowState})", "WindowStateManager");
                
                var previousState = window.WindowState;
                window.WindowState = WindowState.Minimized;
                
                // Verify the state change was successful
                if (window.WindowState != WindowState.Minimized)
                {
                    LoggingService.LogError($"Failed to minimize window - state is still: {window.WindowState}", "WindowStateManager");
                    ErrorManager.ShowUserErrorToast(
                        "فشل في تصغير النافذة. قد تكون هناك مشكلة في النظام",
                        "خطأ في تصغير النافذة",
                        "WindowStateManager"
                    );
                    return false;
                }
                
                LoggingService.LogDebug($"Window minimized successfully: {window.Title} ({previousState} -> {window.WindowState})", "WindowStateManager");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error minimizing window: {ex.Message}", "WindowStateManager");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "WindowStateManager");
                
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تصغير النافذة",
                    "خطأ في إدارة النافذة",
                    LogLevel.Error,
                    "WindowStateManager"
                );
                return false;
            }
        }

        /// <summary>
        /// Toggles the window between maximized and normal states with native Windows animation
        /// </summary>
        /// <param name="window">The window to maximize or restore</param>
        /// <returns>True if the operation was successful, false otherwise</returns>
        public static bool MaximizeRestoreWindow(Window window)
        {
            try
            {
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot maximize/restore null window", "WindowStateManager");
                    ErrorManager.ShowUserWarningToast(
                        "لا يمكن تكبير/استعادة نافذة فارغة",
                        "خطأ في المعاملات",
                        "WindowStateManager"
                    );
                    return false;
                }

                var previousState = window.WindowState;
                var targetState = window.WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
                
                // Validate that the state transition is allowed
                if (!IsValidStateTransition(window, targetState))
                {
                    LoggingService.LogWarning($"State transition not allowed for window: {window.Title} (ResizeMode: {window.ResizeMode}, Target: {targetState})", "WindowStateManager");
                    ErrorManager.ShowUserWarningToast(
                        "لا يمكن تكبير/استعادة هذه النافذة بسبب إعداداتها",
                        "عملية غير مسموحة",
                        "WindowStateManager"
                    );
                    return false;
                }
                
                if (window.WindowState == WindowState.Maximized)
                {
                    LoggingService.LogDebug($"Restoring window from maximized: {window.Title}", "WindowStateManager");
                    window.WindowState = WindowState.Normal;
                    
                    // Verify the state change was successful
                    if (window.WindowState != WindowState.Normal)
                    {
                        LoggingService.LogError($"Failed to restore window - state is still: {window.WindowState}", "WindowStateManager");
                        ErrorManager.ShowUserErrorToast(
                            "فشل في استعادة النافذة إلى الحجم العادي",
                            "خطأ في استعادة النافذة",
                            "WindowStateManager"
                        );
                        return false;
                    }
                    
                    LoggingService.LogDebug($"Window restored to normal state: {window.Title} ({previousState} -> {window.WindowState})", "WindowStateManager");
                }
                else
                {
                    LoggingService.LogDebug($"Maximizing window: {window.Title} (Current state: {window.WindowState})", "WindowStateManager");
                    window.WindowState = WindowState.Maximized;
                    
                    // Verify the state change was successful
                    if (window.WindowState != WindowState.Maximized)
                    {
                        LoggingService.LogError($"Failed to maximize window - state is still: {window.WindowState}", "WindowStateManager");
                        ErrorManager.ShowUserErrorToast(
                            "فشل في تكبير النافذة إلى الحد الأقصى",
                            "خطأ في تكبير النافذة",
                            "WindowStateManager"
                        );
                        return false;
                    }
                    
                    LoggingService.LogDebug($"Window maximized: {window.Title} ({previousState} -> {window.WindowState})", "WindowStateManager");
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error toggling window state: {ex.Message}", "WindowStateManager");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "WindowStateManager");
                
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تكبير/استعادة النافذة",
                    "خطأ في إدارة النافذة",
                    LogLevel.Error,
                    "WindowStateManager"
                );
                return false;
            }
        }

        /// <summary>
        /// Closes the specified window with proper cleanup and optional confirmation
        /// </summary>
        /// <param name="window">The window to close</param>
        /// <param name="forceClose">If true, closes without confirmation dialogs</param>
        /// <returns>True if the window was closed, false if cancelled or error occurred</returns>
        public static bool CloseWindow(Window window, bool forceClose = false)
        {
            try
            {
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot close null window", "WindowStateManager");
                    ErrorManager.ShowUserWarningToast(
                        "لا يمكن إغلاق نافذة فارغة",
                        "خطأ في المعاملات",
                        "WindowStateManager"
                    );
                    return false;
                }

                LoggingService.LogDebug($"Attempting to close window: {window.Title} (ForceClose: {forceClose})", "WindowStateManager");

                // Check if window is already closed or closing
                try
                {
                    if (!window.IsLoaded)
                    {
                        LoggingService.LogDebug($"Window is not loaded, may already be closed: {window.Title}", "WindowStateManager");
                        return true;
                    }
                }
                catch (Exception checkEx)
                {
                    LoggingService.LogWarning($"Error checking window state before close: {checkEx.Message}", "WindowStateManager");
                    // Continue with close attempt
                }

                // Track if close was cancelled
                bool closeCancelled = false;
                
                // Subscribe to closing event to detect cancellation
                System.ComponentModel.CancelEventHandler closingHandler = (sender, e) =>
                {
                    if (e.Cancel)
                    {
                        closeCancelled = true;
                        LoggingService.LogInfo($"Window close was cancelled: {window.Title}", "WindowStateManager");
                    }
                };

                try
                {
                    window.Closing += closingHandler;

                    if (!forceClose)
                    {
                        // Allow the window's closing event to handle confirmation if needed
                        // The window can cancel the close operation by setting e.Cancel = true in the Closing event
                        LoggingService.LogDebug("Initiating normal window close", "WindowStateManager");
                        window.Close();
                    }
                    else
                    {
                        // Force close without confirmation
                        LoggingService.LogDebug("Initiating forced window close", "WindowStateManager");
                        window.Close();
                    }

                    // Check if close was successful
                    if (closeCancelled)
                    {
                        LoggingService.LogInfo($"Window close was cancelled by user or application: {window.Title}", "WindowStateManager");
                        return false;
                    }

                    LoggingService.LogInfo($"Window close initiated successfully: {window.Title}", "WindowStateManager");
                    return true;
                }
                finally
                {
                    // Clean up event handler
                    try
                    {
                        window.Closing -= closingHandler;
                    }
                    catch (Exception cleanupEx)
                    {
                        LoggingService.LogWarning($"Error cleaning up closing event handler: {cleanupEx.Message}", "WindowStateManager");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Critical error closing window: {ex.Message}", "WindowStateManager");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "WindowStateManager");
                
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء إغلاق النافذة",
                    "خطأ في إغلاق النافذة",
                    LogLevel.Error,
                    "WindowStateManager"
                );
                return false;
            }
        }

        #endregion

        #region Title Bar Interaction

        /// <summary>
        /// Handles double-click on title bar to toggle window state between maximized and normal
        /// </summary>
        /// <param name="window">The window to toggle</param>
        /// <returns>True if the operation was successful, false otherwise</returns>
        public static bool HandleDoubleClickTitleBar(Window window)
        {
            try
            {
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot handle double-click for null window", "WindowStateManager");
                    return false;
                }

                // Check if the window allows resizing (some dialogs might not)
                if (window.ResizeMode == ResizeMode.NoResize || window.ResizeMode == ResizeMode.CanMinimize)
                {
                    LoggingService.LogDebug($"Window resize mode ({window.ResizeMode}) does not allow maximize/restore", "WindowStateManager");
                    return false;
                }

                LoggingService.LogDebug($"Handling title bar double-click for window: {window.Title}", "WindowStateManager");
                return MaximizeRestoreWindow(window);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling title bar double-click: {ex.Message}", "WindowStateManager");
                return false;
            }
        }

        #endregion

        #region Window State Validation

        /// <summary>
        /// Validates if a window state transition is valid for the given window
        /// </summary>
        /// <param name="window">The window to validate</param>
        /// <param name="targetState">The target window state</param>
        /// <returns>True if the transition is valid, false otherwise</returns>
        public static bool IsValidStateTransition(Window window, WindowState targetState)
        {
            try
            {
                if (window == null)
                {
                    LoggingService.LogWarning("Cannot validate state transition for null window", "WindowStateManager");
                    return false;
                }

                // Check if the target state is allowed based on window's ResizeMode
                switch (targetState)
                {
                    case WindowState.Maximized:
                        if (window.ResizeMode == ResizeMode.NoResize || window.ResizeMode == ResizeMode.CanMinimize)
                        {
                            LoggingService.LogDebug($"Maximize not allowed for ResizeMode: {window.ResizeMode}", "WindowStateManager");
                            return false;
                        }
                        break;

                    case WindowState.Minimized:
                        if (window.ResizeMode == ResizeMode.NoResize)
                        {
                            LoggingService.LogDebug($"Minimize not allowed for ResizeMode: {window.ResizeMode}", "WindowStateManager");
                            return false;
                        }
                        break;

                    case WindowState.Normal:
                        // Normal state is always allowed
                        break;

                    default:
                        LoggingService.LogWarning($"Unknown window state: {targetState}", "WindowStateManager");
                        return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating state transition: {ex.Message}", "WindowStateManager");
                return false;
            }
        }

        #endregion
    }
}