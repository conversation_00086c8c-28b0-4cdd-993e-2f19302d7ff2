// BACKUP: Original UIDGenerationService.cs created on 2025-07-29 before nested transaction refactoring
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;
using System.Globalization;
using System.Text.RegularExpressions;

namespace UFU2.Services
{
    /// <summary>
    /// UID generation service for UFU2 client and activity management.
    /// Generates unique identifiers following business rules with database sequence management.
    /// Provides transaction safety for concurrent operations and integrates with UFU2 error handling.
    /// </summary>
    public class UIDGenerationService : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private bool _disposed = false;

        // In-memory sequence cache to avoid database reads during transaction
        // Key format: "{EntityType}:{Prefix}" -> Current sequence number
        private readonly Dictionary<string, int> _sequenceCache = new Dictionary<string, int>();

        /// <summary>
        /// Initializes a new instance of the UIDGenerationService.
        /// </summary>
        /// <param name="databaseService">The database service for connection management</param>
        public UIDGenerationService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            LoggingService.LogDebug("UIDGenerationService initialized", "UIDGenerationService");
        }

        /// <summary>
        /// Generates a unique Client UID following the pattern {FirstLetter}{SequentialNumber:D2}.
        /// Example: "A01", "B02", "Z99", "A100"
        /// </summary>
        /// <param name="nameFr">The French name of the client (required)</param>
        /// <param name="operationId">Optional operation ID for error deduplication tracking</param>
        /// <returns>Generated Client UID</returns>
        /// <exception cref="ArgumentException">Thrown when nameFr is null or empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when UID generation fails</exception>
        public async Task<string> GenerateClientUIDAsync(string nameFr, string? operationId = null)
        {
            // Delegate to the connection-based overload with new connection
            using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
            var connection = pooledConnection.Connection;
            using var transaction = connection.BeginTransaction();

            try
            {
                var result = await GenerateClientUIDAsync(connection, transaction, nameFr, operationId);
                transaction.Commit();
                return result;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// Generates a unique Client UID within an existing database transaction.
        /// This method eliminates nested transactions and supports single-transaction operations.
        /// Example: "A01", "B02", "Z99", "A100"
        /// </summary>
        /// <param name="connection">Active database connection</param>
        /// <param name="transaction">Active database transaction</param>
        /// <param name="nameFr">The French name of the client (required)</param>
        /// <param name="operationId">Optional operation ID for error deduplication tracking</param>
        /// <returns>Generated Client UID</returns>
        /// <exception cref="ArgumentException">Thrown when nameFr is null or empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when UID generation fails</exception>
        public async Task<string> GenerateClientUIDAsync(SqliteConnection connection, SqliteTransaction transaction,
            string nameFr, string? operationId = null)
        {
            if (string.IsNullOrWhiteSpace(nameFr))
            {
                var errorMessage = "اسم العميل مطلوب لتوليد المعرف الفريد";
                LoggingService.LogError("Client name is required for UID generation", "UIDGenerationService");
                throw new ArgumentException(errorMessage, nameof(nameFr));
            }

            if (connection == null)
            {
                throw new ArgumentNullException(nameof(connection));
            }

            if (transaction == null)
            {
                throw new ArgumentNullException(nameof(transaction));
            }

            try
            {
                // Extract first letter and convert to uppercase
                string firstLetter = ExtractFirstLetter(nameFr);
                LoggingService.LogDebug($"Extracted first letter '{firstLetter}' from name '{nameFr}' (using existing transaction)", "UIDGenerationService");

                // Get next sequence number for this prefix using in-memory cache
                int sequenceNumber = await GetNextSequenceWithCacheAsync(connection, transaction, "Client", firstLetter);

                // Generate UID with zero-padded sequence (minimum 2 digits)
                string clientUID = $"{firstLetter}{sequenceNumber:D2}";

                // Verify uniqueness (additional safety check)
                await EnsureClientUIDUniquenessAsync(connection, transaction, clientUID);

                LoggingService.LogDebug($"Generated Client UID: {clientUID} for name: {nameFr} (within existing transaction)", "UIDGenerationService");
                return clientUID;
            }
            catch (Exception ex) when (!(ex is ArgumentException))
            {
                var errorMessage = "فشل في توليد معرف العميل الفريد";
                LoggingService.LogError($"Client UID generation failed within existing transaction: {ex.Message}", "UIDGenerationService");
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في توليد المعرف", LogLevel.Error, "UIDGenerationService", operationId);
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        /// <summary>
        /// Generates a unique Activity UID following the pattern {ClientUID}_Act{ActivitySequence}.
        /// Example: "A01_Act1", "B02_Act2", "Z99_Act10"
        /// When useAlternateFormat is true, appends 's' suffix: "A01_Act1s", "B02_Act2s"
        /// </summary>
        /// <param name="clientUID">The Client UID (required)</param>
        /// <param name="useAlternateFormat">When true, appends 's' suffix to the generated UID</param>
        /// <returns>Generated Activity UID</returns>
        /// <exception cref="ArgumentException">Thrown when clientUID is null, empty, or invalid format</exception>
        /// <exception cref="InvalidOperationException">Thrown when UID generation fails</exception>
        public async Task<string> GenerateActivityUIDAsync(string clientUID, bool useAlternateFormat = false)
        {
            // Delegate to the connection-based overload with new connection
            using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
            var connection = pooledConnection.Connection;
            using var transaction = connection.BeginTransaction();

            try
            {
                var result = await GenerateActivityUIDAsync(connection, transaction, clientUID, useAlternateFormat);
                transaction.Commit();
                return result;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// Generates a unique Activity UID within an existing database transaction.
        /// This method eliminates nested transactions and supports single-transaction operations.
        /// Example: "A01_Act1", "B02_Act2", "Z99_Act10"
        /// When useAlternateFormat is true, appends 's' suffix: "A01_Act1s", "B02_Act2s"
        /// </summary>
        /// <param name="connection">Active database connection</param>
        /// <param name="transaction">Active database transaction</param>
        /// <param name="clientUID">The Client UID (required)</param>
        /// <param name="useAlternateFormat">When true, appends 's' suffix to the generated UID</param>
        /// <returns>Generated Activity UID</returns>
        /// <exception cref="ArgumentException">Thrown when clientUID is null, empty, or invalid format</exception>
        /// <exception cref="InvalidOperationException">Thrown when UID generation fails</exception>
        public async Task<string> GenerateActivityUIDAsync(SqliteConnection connection, SqliteTransaction transaction, string clientUID, bool useAlternateFormat = false)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                var errorMessage = "معرف العميل مطلوب لتوليد معرف النشاط";
                LoggingService.LogError("Client UID is required for Activity UID generation", "UIDGenerationService");
                throw new ArgumentException(errorMessage, nameof(clientUID));
            }

            // Validate Client UID format
            if (!IsValidClientUIDFormat(clientUID))
            {
                var errorMessage = $"تنسيق معرف العميل غير صحيح: {clientUID}";
                LoggingService.LogError($"Invalid Client UID format: {clientUID}", "UIDGenerationService");
                throw new ArgumentException(errorMessage, nameof(clientUID));
            }

            if (connection == null)
            {
                throw new ArgumentNullException(nameof(connection));
            }

            if (transaction == null)
            {
                throw new ArgumentNullException(nameof(transaction));
            }

            try
            {
                // Verify client exists within existing transaction
                await EnsureClientExistsAsync(connection, transaction, clientUID);

                // Get next unified sequence number for this client's activities (prevents conflicts between formats)
                int activitySequence = await GetNextUnifiedActivitySequenceAsync(connection, transaction, clientUID);

                // Generate Activity UID with optional alternate format
                string activityUID = useAlternateFormat
                    ? $"{clientUID}_Act{activitySequence}s"
                    : $"{clientUID}_Act{activitySequence}";

                // Verify uniqueness (additional safety check)
                await EnsureActivityUIDUniquenessAsync(connection, transaction, activityUID);

                LoggingService.LogDebug($"Generated Activity UID: {activityUID} for Client: {clientUID} (format: {(useAlternateFormat ? "alternate with 's' suffix" : "standard")}) (within existing transaction)", "UIDGenerationService");
                return activityUID;
            }
            catch (Exception ex) when (!(ex is ArgumentException))
            {
                var errorMessage = "فشل في توليد معرف النشاط الفريد";
                LoggingService.LogError($"Activity UID generation failed within existing transaction: {ex.Message}", "UIDGenerationService");
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في توليد المعرف", LogLevel.Error, "UIDGenerationService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        /// <summary>
        /// Generates a unique Phone Number UID following the pattern {ClientUID}_Phone_{8-char-guid}.
        /// Example: "A01_Phone_a1b2c3d4", "B02_Phone_f5e6d7c8"
        /// This method provides consistency with other UID patterns while ensuring uniqueness.
        /// </summary>
        /// <param name="clientUID">The Client UID (required)</param>
        /// <returns>Generated Phone Number UID</returns>
        /// <exception cref="ArgumentException">Thrown when clientUID is null, empty, or invalid format</exception>
        public string GeneratePhoneNumberUID(string clientUID)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
            {
                var errorMessage = "معرف العميل مطلوب لتوليد معرف رقم الهاتف";
                LoggingService.LogError("Client UID is required for Phone Number UID generation", "UIDGenerationService");
                throw new ArgumentException(errorMessage, nameof(clientUID));
            }

            try
            {
                // Generate 8-character GUID suffix
                string guidSuffix = Guid.NewGuid().ToString("N")[..8];

                // Generate Phone Number UID with the new pattern
                string phoneUID = $"{clientUID}_Phone_{guidSuffix}";

                LoggingService.LogDebug($"Generated Phone Number UID: {phoneUID} for Client: {clientUID}", "UIDGenerationService");
                return phoneUID;
            }
            catch (Exception ex)
            {
                var errorMessage = "فشل في توليد معرف رقم الهاتف الفريد";
                LoggingService.LogError($"Phone Number UID generation failed: {ex.Message}", "UIDGenerationService");
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في توليد المعرف", LogLevel.Error, "UIDGenerationService");
                throw new InvalidOperationException(errorMessage, ex);
            }
        }

        /// <summary>
        /// Gets the next sequence number with in-memory caching to avoid database reads during transaction.
        /// This method optimizes performance by caching sequence values and deferring database updates.
        /// </summary>
        /// <param name="connection">Active database connection</param>
        /// <param name="transaction">Active database transaction</param>
        /// <param name="entityType">Entity type (Client, Activity, etc.)</param>
        /// <param name="prefix">Prefix for the sequence</param>
        /// <returns>Next sequence number</returns>
        private async Task<int> GetNextSequenceWithCacheAsync(SqliteConnection connection, SqliteTransaction transaction,
            string entityType, string prefix)
        {
            string cacheKey = $"{entityType}:{prefix}";

            // Check if we have this sequence cached
            if (_sequenceCache.TryGetValue(cacheKey, out int cachedSequence))
            {
                // Increment cached value and return
                _sequenceCache[cacheKey] = cachedSequence + 1;
                LoggingService.LogDebug($"Using cached sequence {cachedSequence + 1} for {entityType}:{prefix}", "UIDGenerationService");
                return cachedSequence + 1;
            }

            // Not cached, get from database and cache it
            int dbSequence = await GetNextSequenceAsync(connection, transaction, entityType, prefix);
            _sequenceCache[cacheKey] = dbSequence;

            LoggingService.LogDebug($"Cached sequence {dbSequence} for {entityType}:{prefix}", "UIDGenerationService");
            return dbSequence;
        }

        /// <summary>
        /// Clears the in-memory sequence cache. Should be called after transaction commit/rollback.
        /// </summary>
        public void ClearSequenceCache()
        {
            _sequenceCache.Clear();
            LoggingService.LogDebug("Sequence cache cleared", "UIDGenerationService");
        }

        /// <summary>
        /// Extracts the first letter from a name, handling Arabic and French text.
        /// </summary>
        /// <param name="name">The name to extract from</param>
        /// <returns>First letter in uppercase</returns>
        private string ExtractFirstLetter(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentException("Name cannot be null or empty", nameof(name));
            }

            // Trim and get first character
            string trimmedName = name.Trim();
            char firstChar = trimmedName[0];

            // Convert to uppercase using invariant culture for consistency
            string firstLetter = char.ToUpper(firstChar, CultureInfo.InvariantCulture).ToString();

            // For Arabic characters, we might want to transliterate or use a mapping
            // For now, we'll use the character as-is if it's a letter, otherwise default to 'X'
            if (!char.IsLetter(firstChar))
            {
                LoggingService.LogWarning($"First character '{firstChar}' is not a letter, using 'X' as default", "UIDGenerationService");
                firstLetter = "X";
            }

            return firstLetter;
        }

        /// <summary>
        /// Gets the next sequence number for the specified entity type and prefix.
        /// Thread-safe operation with database-level locking.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        /// <param name="entityType">Type of entity (Client, Activity)</param>
        /// <param name="prefix">Prefix for the sequence</param>
        /// <returns>Next sequence number</returns>
        private async Task<int> GetNextSequenceAsync(SqliteConnection connection, SqliteTransaction transaction, 
            string entityType, string prefix)
        {
            try
            {
                // First, try to get existing sequence (using PascalCase table and column names)
                const string getSequenceSql = @"
                    SELECT LastSequence FROM UidSequences
                    WHERE EntityType = @EntityType AND Prefix = @Prefix";

                var currentSequence = await connection.ExecuteScalarAsync<int?>(
                    getSequenceSql, 
                    new { EntityType = entityType, Prefix = prefix }, 
                    transaction);

                int nextSequence;

                if (currentSequence.HasValue)
                {
                    // Increment existing sequence
                    nextSequence = currentSequence.Value + 1;
                    
                    const string updateSequenceSql = @"
                        UPDATE UidSequences
                        SET LastSequence = @NextSequence, UpdatedAt = strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')
                        WHERE EntityType = @EntityType AND Prefix = @Prefix";

                    await connection.ExecuteAsync(updateSequenceSql, 
                        new { NextSequence = nextSequence, EntityType = entityType, Prefix = prefix }, 
                        transaction);
                }
                else
                {
                    // Create new sequence starting at 1
                    nextSequence = 1;
                    
                    const string insertSequenceSql = @"
                        INSERT INTO UidSequences (EntityType, Prefix, LastSequence, UpdatedAt)
                        VALUES (@EntityType, @Prefix, @NextSequence, strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime'))";

                    await connection.ExecuteAsync(insertSequenceSql, 
                        new { EntityType = entityType, Prefix = prefix, NextSequence = nextSequence }, 
                        transaction);
                }

                LoggingService.LogDebug($"Generated sequence {nextSequence} for {entityType} with prefix '{prefix}'", "UIDGenerationService");
                return nextSequence;
            }
            catch (SqliteException ex) when (ex.SqliteErrorCode == 1) // SQLITE_ERROR - no such table
            {
                string errorMessage = "جدول تسلسل المعرفات غير موجود في قاعدة البيانات";
                LoggingService.LogError($"UidSequences table missing: {ex.Message}", "UIDGenerationService");
                ErrorManager.HandleErrorToast(ex, errorMessage, "خطأ في قاعدة البيانات", LogLevel.Error, "UIDGenerationService");
                throw new InvalidOperationException(errorMessage, ex);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to get next sequence for {entityType} with prefix '{prefix}': {ex.Message}", "UIDGenerationService");
                throw;
            }
        }

        /// <summary>
        /// Gets the next unified activity sequence number for a client, ensuring no conflicts between formats.
        /// This method checks existing Activity UIDs (both standard and alternate formats) and returns
        /// the next available sequence number to prevent logical conflicts.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        /// <param name="clientUID">Client UID to get sequence for</param>
        /// <returns>Next available sequence number</returns>
        private async Task<int> GetNextUnifiedActivitySequenceAsync(SqliteConnection connection, SqliteTransaction transaction, string clientUID)
        {
            try
            {
                // Check cache first for performance
                string cacheKey = $"UnifiedActivity:{clientUID}";
                if (_sequenceCache.TryGetValue(cacheKey, out int cachedSequence))
                {
                    _sequenceCache[cacheKey] = cachedSequence + 1;
                    LoggingService.LogDebug($"Using cached unified sequence {cachedSequence + 1} for client {clientUID}", "UIDGenerationService");
                    return cachedSequence + 1;
                }

                // Query existing Activity UIDs for this client to find the highest sequence number
                const string getExistingSequencesSql = @"
                    SELECT Uid FROM Activities
                    WHERE ClientUid = @ClientUID
                    AND (Uid GLOB @StandardPattern OR Uid GLOB @AlternatePattern)";

                var standardPattern = $"{clientUID}_Act[0-9]*";
                var alternatePattern = $"{clientUID}_Act[0-9]*s";

                var existingUIDs = await connection.QueryAsync<string>(
                    getExistingSequencesSql,
                    new { ClientUID = clientUID, StandardPattern = standardPattern, AlternatePattern = alternatePattern },
                    transaction);

                // Extract sequence numbers from existing UIDs
                int maxSequence = 0;
                foreach (var uid in existingUIDs)
                {
                    // Extract sequence number from UID patterns like "A01_Act5" or "A01_Act5s"
                    var match = Regex.Match(uid, @"_Act(\d+)s?$");
                    if (match.Success && int.TryParse(match.Groups[1].Value, out int sequence))
                    {
                        maxSequence = Math.Max(maxSequence, sequence);
                    }
                }

                int nextSequence = maxSequence + 1;

                // Cache the result for subsequent calls within the same transaction
                _sequenceCache[cacheKey] = nextSequence;

                LoggingService.LogDebug($"Generated unified sequence {nextSequence} for client {clientUID} (max existing: {maxSequence})", "UIDGenerationService");
                return nextSequence;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to get unified activity sequence for client {clientUID}: {ex.Message}", "UIDGenerationService");
                throw;
            }
        }

        /// <summary>
        /// Validates Client UID format using regex pattern.
        /// Expected format: {Letter}{Number} (e.g., A01, B123, Z99)
        /// </summary>
        /// <param name="clientUID">Client UID to validate</param>
        /// <returns>True if format is valid</returns>
        private bool IsValidClientUIDFormat(string clientUID)
        {
            if (string.IsNullOrWhiteSpace(clientUID))
                return false;

            // Pattern: One letter followed by one or more digits
            var pattern = @"^[A-Za-z]\d+$";
            return Regex.IsMatch(clientUID, pattern);
        }

        /// <summary>
        /// Ensures the generated Client UID is unique in the database.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        /// <param name="clientUID">Client UID to check</param>
        private async Task EnsureClientUIDUniquenessAsync(SqliteConnection connection, SqliteTransaction transaction, string clientUID)
        {
            const string checkUniquenessSql = "SELECT COUNT(*) FROM clients WHERE uid = @ClientUID";
            
            int existingCount = await connection.ExecuteScalarAsync<int>(
                checkUniquenessSql, 
                new { ClientUID = clientUID }, 
                transaction);

            if (existingCount > 0)
            {
                var errorMessage = $"معرف العميل {clientUID} موجود بالفعل";
                LoggingService.LogError($"Client UID {clientUID} already exists", "UIDGenerationService");
                throw new InvalidOperationException(errorMessage);
            }
        }

        /// <summary>
        /// Ensures the generated Activity UID is unique in the database and checks for sequence conflicts across formats.
        /// This prevents both exact duplicates and logical conflicts between standard and alternate formats.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        /// <param name="activityUID">Activity UID to check</param>
        private async Task EnsureActivityUIDUniquenessAsync(SqliteConnection connection, SqliteTransaction transaction, string activityUID)
        {
            // Check for exact duplicate
            const string checkUniquenessSql = "SELECT COUNT(*) FROM activities WHERE uid = @ActivityUID";

            int existingCount = await connection.ExecuteScalarAsync<int>(
                checkUniquenessSql,
                new { ActivityUID = activityUID },
                transaction);

            if (existingCount > 0)
            {
                var errorMessage = $"معرف النشاط {activityUID} موجود بالفعل";
                LoggingService.LogError($"Activity UID {activityUID} already exists", "UIDGenerationService");
                throw new InvalidOperationException(errorMessage);
            }

            // Check for sequence conflicts across formats (e.g., if A01_Act1 exists, prevent A01_Act1s)
            var match = Regex.Match(activityUID, @"^(.+_Act\d+)(s?)$");
            if (match.Success)
            {
                string baseUID = match.Groups[1].Value; // e.g., "A01_Act1"
                string suffix = match.Groups[2].Value;   // "s" or empty

                // Check for the opposite format
                string conflictUID = suffix == "s" ? baseUID : baseUID + "s";

                int conflictCount = await connection.ExecuteScalarAsync<int>(
                    checkUniquenessSql,
                    new { ActivityUID = conflictUID },
                    transaction);

                if (conflictCount > 0)
                {
                    var errorMessage = $"تعارض في تسلسل معرف النشاط: {activityUID} يتعارض مع {conflictUID} الموجود";
                    LoggingService.LogError($"Activity UID sequence conflict: {activityUID} conflicts with existing {conflictUID}", "UIDGenerationService");
                    throw new InvalidOperationException(errorMessage);
                }
            }
        }

        /// <summary>
        /// Ensures the specified client exists in the database.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        /// <param name="clientUID">Client UID to verify</param>
        private async Task EnsureClientExistsAsync(SqliteConnection connection, SqliteTransaction transaction, string clientUID)
        {
            const string checkClientSql = "SELECT COUNT(*) FROM clients WHERE uid = @ClientUID";
            
            int clientCount = await connection.ExecuteScalarAsync<int>(
                checkClientSql, 
                new { ClientUID = clientUID }, 
                transaction);

            if (clientCount == 0)
            {
                var errorMessage = $"العميل بالمعرف {clientUID} غير موجود";
                LoggingService.LogError($"Client with UID {clientUID} does not exist", "UIDGenerationService");
                throw new InvalidOperationException(errorMessage);
            }
        }

        /// <summary>
        /// Validates a Client UID format and checks if it exists in the database.
        /// </summary>
        /// <param name="clientUID">Client UID to validate</param>
        /// <returns>True if valid and exists</returns>
        public async Task<bool> ValidateClientUIDAsync(string clientUID)
        {
            if (!IsValidClientUIDFormat(clientUID))
            {
                return false;
            }

            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                const string checkClientSql = "SELECT COUNT(*) FROM clients WHERE uid = @ClientUID";
                int clientCount = await connection.ExecuteScalarAsync<int>(checkClientSql, new { ClientUID = clientUID });

                return clientCount > 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating Client UID {clientUID}: {ex.Message}", "UIDGenerationService");
                return false;
            }
        }

        /// <summary>
        /// Gets statistics about UID generation for monitoring and debugging.
        /// </summary>
        /// <returns>UID generation statistics</returns>
        public async Task<UIDGenerationStats> GetUIDGenerationStatsAsync()
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var stats = new UIDGenerationStats();

                // Get sequence statistics (using PascalCase table and column names)
                const string sequenceStatsSql = @"
                    SELECT EntityType, COUNT(*) as sequence_count, MAX(LastSequence) as max_sequence
                    FROM UidSequences
                    GROUP BY EntityType";

                var sequenceStats = await connection.QueryAsync(sequenceStatsSql);

                foreach (var stat in sequenceStats)
                {
                    if (stat.EntityType == "Client")
                    {
                        stats.ClientSequenceCount = stat.sequence_count;
                        stats.MaxClientSequence = stat.max_sequence;
                    }
                    else if (stat.EntityType == "Activity")
                    {
                        stats.ActivitySequenceCount = stat.sequence_count;
                        stats.MaxActivitySequence = stat.max_sequence;
                    }
                }

                // Get actual entity counts for verification
                stats.TotalClientsGenerated = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM clients");
                stats.TotalActivitiesGenerated = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM activities");

                LoggingService.LogDebug($"UID Generation Stats: {stats.TotalClientsGenerated} clients, {stats.TotalActivitiesGenerated} activities", "UIDGenerationService");
                return stats;
            }
            catch (Exception ex)
            {
                ErrorManager.HandleErrorToast(ex, "فشل في الحصول على إحصائيات توليد المعرفات", "خطأ في الإحصائيات",
                                       LogLevel.Error, "UIDGenerationService");
                throw;
            }
        }

        /// <summary>
        /// Disposes of the UIDGenerationService resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources if any
                    LoggingService.LogDebug("UIDGenerationService disposed", "UIDGenerationService");
                }
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Statistics for UID generation monitoring and debugging.
    /// </summary>
    public class UIDGenerationStats
    {
        public int ClientSequenceCount { get; set; }
        public int ActivitySequenceCount { get; set; }
        public int MaxClientSequence { get; set; }
        public int MaxActivitySequence { get; set; }
        public int TotalClientsGenerated { get; set; }
        public int TotalActivitiesGenerated { get; set; }
        
        public override string ToString()
        {
            return $"Clients: {TotalClientsGenerated} (Max Seq: {MaxClientSequence}), " +
                   $"Activities: {TotalActivitiesGenerated} (Max Seq: {MaxActivitySequence})";
        }
    }
}
