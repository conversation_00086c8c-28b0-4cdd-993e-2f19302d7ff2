using System;
using System.Windows;
using System.Windows.Media;
using UFU2.Common;

namespace UFU2.Common.Utilities
{
    /// <summary>
    /// Helper class for coordinate transformations and image cropping calculations.
    /// Provides advanced coordinate mapping, boundary validation, and transformation utilities
    /// for the ImageManagementDialog cropping functionality.
    /// Follows UFU2 patterns with comprehensive error handling and logging.
    /// </summary>
    public static class CoordinateTransformHelper
    {
        #region Constants

        /// <summary>
        /// Tolerance for floating-point comparisons to handle precision issues
        /// </summary>
        private const double FloatingPointTolerance = 1e-10;

        /// <summary>
        /// Minimum valid dimension for rectangles and images
        /// </summary>
        private const double MinimumDimension = 1.0;

        #endregion

        #region Validation Methods

        /// <summary>
        /// Validates image parameters for coordinate transformation operations.
        /// Ensures all parameters are within valid ranges and not NaN/Infinity.
        /// </summary>
        /// <param name="imageWidth">Width of the source image</param>
        /// <param name="imageHeight">Height of the source image</param>
        /// <param name="displayWidth">Width of the display container</param>
        /// <param name="displayHeight">Height of the display container</param>
        /// <param name="zoomScale">Zoom scale factor</param>
        /// <param name="rotationAngle">Rotation angle in degrees</param>
        /// <returns>True if all parameters are valid</returns>
        public static bool ValidateImageParameters(double imageWidth, double imageHeight, 
            double displayWidth, double displayHeight, double zoomScale, double rotationAngle)
        {
            try
            {
                // Check for NaN or Infinity values
                if (double.IsNaN(imageWidth) || double.IsInfinity(imageWidth) ||
                    double.IsNaN(imageHeight) || double.IsInfinity(imageHeight) ||
                    double.IsNaN(displayWidth) || double.IsInfinity(displayWidth) ||
                    double.IsNaN(displayHeight) || double.IsInfinity(displayHeight) ||
                    double.IsNaN(zoomScale) || double.IsInfinity(zoomScale) ||
                    double.IsNaN(rotationAngle) || double.IsInfinity(rotationAngle))
                {
                    LoggingService.LogWarning("Invalid parameters detected: NaN or Infinity values", "CoordinateTransformHelper");
                    return false;
                }

                // Check for positive dimensions
                if (imageWidth <= 0 || imageHeight <= 0 || displayWidth <= 0 || displayHeight <= 0)
                {
                    LoggingService.LogWarning($"Invalid dimensions: Image({imageWidth}x{imageHeight}), Display({displayWidth}x{displayHeight})", "CoordinateTransformHelper");
                    return false;
                }

                // Check for valid zoom scale
                if (zoomScale <= 0)
                {
                    LoggingService.LogWarning($"Invalid zoom scale: {zoomScale}", "CoordinateTransformHelper");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating image parameters: {ex.Message}", "CoordinateTransformHelper");
                return false;
            }
        }

        /// <summary>
        /// Validates that a rectangle has positive dimensions and is not empty.
        /// </summary>
        /// <param name="rect">Rectangle to validate</param>
        /// <returns>True if the rectangle is valid</returns>
        public static bool IsValidRect(Rect rect)
        {
            try
            {
                return !rect.IsEmpty && 
                       rect.Width >= MinimumDimension && 
                       rect.Height >= MinimumDimension &&
                       !double.IsNaN(rect.X) && !double.IsInfinity(rect.X) &&
                       !double.IsNaN(rect.Y) && !double.IsInfinity(rect.Y) &&
                       !double.IsNaN(rect.Width) && !double.IsInfinity(rect.Width) &&
                       !double.IsNaN(rect.Height) && !double.IsInfinity(rect.Height);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating rectangle: {ex.Message}", "CoordinateTransformHelper");
                return false;
            }
        }

        /// <summary>
        /// Validates that a point is within the specified bounds with floating-point tolerance.
        /// </summary>
        /// <param name="point">Point to validate</param>
        /// <param name="bounds">Boundary rectangle</param>
        /// <returns>True if the point is within bounds</returns>
        public static bool IsSafePoint(Point point, Rect bounds)
        {
            try
            {
                return point.X >= bounds.Left - FloatingPointTolerance &&
                       point.X <= bounds.Right + FloatingPointTolerance &&
                       point.Y >= bounds.Top - FloatingPointTolerance &&
                       point.Y <= bounds.Bottom + FloatingPointTolerance;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating safe point: {ex.Message}", "CoordinateTransformHelper");
                return false;
            }
        }

        #endregion

        #region Coordinate Transformation

        /// <summary>
        /// Creates a bidirectional transformation matrix for converting between UI and image coordinates.
        /// Handles zoom, rotation, and scaling transformations.
        /// </summary>
        /// <param name="imageSize">Size of the source image</param>
        /// <param name="displaySize">Size of the display container</param>
        /// <param name="zoomScale">Zoom scale factor</param>
        /// <param name="rotationAngle">Rotation angle in degrees</param>
        /// <returns>Tuple containing forward and inverse transformation matrices</returns>
        public static (Matrix Forward, Matrix Inverse) CreateBidirectionalTransform(
            Size imageSize, Size displaySize, double zoomScale, double rotationAngle)
        {
            try
            {
                LoggingService.LogDebug($"Creating bidirectional transform - ImageSize: {imageSize.Width},{imageSize.Height}, " +
                    $"DisplaySize: {displaySize.Width},{displaySize.Height}, Zoom: {zoomScale}, Rotation: {rotationAngle}", 
                    "CoordinateTransformHelper");

                // Calculate scale to fit image in display
                double scaleX = displaySize.Width / imageSize.Width;
                double scaleY = displaySize.Height / imageSize.Height;
                double uniformScale = Math.Min(scaleX, scaleY) * zoomScale;

                // Create forward transformation matrix
                var forwardMatrix = Matrix.Identity;
                forwardMatrix.Scale(uniformScale, uniformScale);
                
                if (Math.Abs(rotationAngle) > FloatingPointTolerance)
                {
                    forwardMatrix.Rotate(rotationAngle);
                }

                // Create inverse transformation matrix
                var inverseMatrix = forwardMatrix;
                inverseMatrix.Invert();

                LoggingService.LogDebug($"Transform matrices created - Forward: {forwardMatrix}, Inverse: {inverseMatrix}", 
                    "CoordinateTransformHelper");

                return (forwardMatrix, inverseMatrix);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating bidirectional transform: {ex.Message}", "CoordinateTransformHelper");
                return (Matrix.Identity, Matrix.Identity);
            }
        }

        /// <summary>
        /// Transforms UI coordinates to image pixel coordinates using the provided transformation matrix.
        /// </summary>
        /// <param name="uiRect">Rectangle in UI coordinates</param>
        /// <param name="inverseMatrix">Inverse transformation matrix</param>
        /// <returns>Rectangle in image pixel coordinates</returns>
        public static Rect TransformUIToImageCoordinates(Rect uiRect, Matrix inverseMatrix)
        {
            try
            {
                var topLeft = inverseMatrix.Transform(new Point(uiRect.Left, uiRect.Top));
                var bottomRight = inverseMatrix.Transform(new Point(uiRect.Right, uiRect.Bottom));

                return new Rect(topLeft, bottomRight);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error transforming UI to image coordinates: {ex.Message}", "CoordinateTransformHelper");
                return Rect.Empty;
            }
        }

        #endregion

        #region Boundary Constraint Methods

        /// <summary>
        /// Constrains a point to be within the specified rectangle bounds.
        /// Uses ImageCropper's GetSafePoint algorithm for robust boundary handling.
        /// </summary>
        /// <param name="point">Point to constrain</param>
        /// <param name="bounds">Boundary rectangle</param>
        /// <returns>Point constrained within bounds</returns>
        public static Point GetSafePoint(Point point, Rect bounds)
        {
            try
            {
                double safeX = Math.Max(bounds.Left, Math.Min(bounds.Right, point.X));
                double safeY = Math.Max(bounds.Top, Math.Min(bounds.Bottom, point.Y));
                
                return new Point(safeX, safeY);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting safe point: {ex.Message}", "CoordinateTransformHelper");
                return new Point(bounds.Left, bounds.Top);
            }
        }

        /// <summary>
        /// Creates a rectangle with uniform aspect ratio that fits within the specified bounds.
        /// Preserves the target aspect ratio while ensuring the rectangle stays within bounds.
        /// </summary>
        /// <param name="targetAspectRatio">Desired aspect ratio (width/height)</param>
        /// <param name="bounds">Boundary rectangle</param>
        /// <returns>Rectangle with uniform aspect ratio within bounds</returns>
        public static Rect GetUniformRect(double targetAspectRatio, Rect bounds)
        {
            try
            {
                double boundsAspectRatio = bounds.Width / bounds.Height;
                
                double width, height;
                if (targetAspectRatio > boundsAspectRatio)
                {
                    // Constrained by width
                    width = bounds.Width;
                    height = width / targetAspectRatio;
                }
                else
                {
                    // Constrained by height
                    height = bounds.Height;
                    width = height * targetAspectRatio;
                }

                // Center the rectangle within bounds
                double x = bounds.Left + (bounds.Width - width) / 2;
                double y = bounds.Top + (bounds.Height - height) / 2;

                var result = new Rect(x, y, width, height);
                
                LoggingService.LogDebug($"Created uniform rect with aspect ratio {targetAspectRatio}: {result}", 
                    "CoordinateTransformHelper");
                
                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating uniform rectangle: {ex.Message}", "CoordinateTransformHelper");
                return bounds;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Normalizes an angle to the -180 to +180 degree range for bidirectional rotation support.
        /// </summary>
        /// <param name="angle">Angle in degrees</param>
        /// <returns>Normalized angle between -180 and +180 degrees</returns>
        public static double NormalizeAngle(double angle)
        {
            try
            {
                if (double.IsNaN(angle) || double.IsInfinity(angle))
                {
                    return 0.0;
                }

                // Normalize to -180 to +180 range for bidirectional rotation
                while (angle > 180.0)
                {
                    angle -= 360.0;
                }
                while (angle < -180.0)
                {
                    angle += 360.0;
                }

                return angle;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error normalizing angle: {ex.Message}", "CoordinateTransformHelper");
                return 0.0;
            }
        }

        /// <summary>
        /// Normalizes an angle to the 0-360 degree range (legacy method for backward compatibility).
        /// </summary>
        /// <param name="angle">Angle in degrees</param>
        /// <returns>Normalized angle between 0 and 360 degrees</returns>
        public static double NormalizeAngleTo360(double angle)
        {
            try
            {
                if (double.IsNaN(angle) || double.IsInfinity(angle))
                {
                    return 0.0;
                }

                angle = angle % 360.0;
                if (angle < 0)
                {
                    angle += 360.0;
                }

                return angle;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error normalizing angle to 360: {ex.Message}", "CoordinateTransformHelper");
                return 0.0;
            }
        }

        /// <summary>
        /// Calculates the optimal render size for an image with the given transformations.
        /// </summary>
        /// <param name="originalSize">Original image size</param>
        /// <param name="rotationAngle">Rotation angle in degrees</param>
        /// <param name="zoomScale">Zoom scale factor</param>
        /// <returns>Optimal render size</returns>
        public static Size CalculateOptimalRenderSize(Size originalSize, double rotationAngle, double zoomScale)
        {
            try
            {
                // For rotated images, calculate the bounding box
                double radians = rotationAngle * Math.PI / 180.0;
                double cos = Math.Abs(Math.Cos(radians));
                double sin = Math.Abs(Math.Sin(radians));

                double rotatedWidth = originalSize.Width * cos + originalSize.Height * sin;
                double rotatedHeight = originalSize.Width * sin + originalSize.Height * cos;

                return new Size(rotatedWidth * zoomScale, rotatedHeight * zoomScale);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error calculating optimal render size: {ex.Message}", "CoordinateTransformHelper");
                return originalSize;
            }
        }

        #endregion
    }
}
