using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Caching.Memory;
using UFU2.Common;
using UFU2.Common.Utilities;

namespace UFU2.Services
{
    /// <summary>
    /// Full-Text Search service using SQLite FTS5 for high-performance text search
    /// with Arabic text support, ranking capabilities, and advanced query features.
    /// Provides significant performance improvements over LIKE-based searches.
    /// </summary>
    public class FtsSearchService : IDisposable
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;
        private IMemoryCache _ftsCache;
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);
        private int _cacheHits = 0;
        private int _cacheMisses = 0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the FtsSearchService class.
        /// </summary>
        /// <param name="databaseService">Database service instance</param>
        public FtsSearchService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));

            _ftsCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 100,
                CompactionPercentage = 0.25
            });

            LoggingService.LogInfo("FtsSearchService initialized with SQLite FTS5 support", "FtsSearchService");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initializes FTS5 virtual tables for ActivityTypeBase and CraftTypeBase.
        /// Should be called during application startup.
        /// </summary>
        public async Task InitializeFtsTablesAsync()
        {
            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Create FTS5 virtual table for ActivityTypeBase
                const string createActivityFtsSql = @"
                    CREATE VIRTUAL TABLE IF NOT EXISTS ActivityTypeBaseFts 
                    USING fts5(Code, Description, content='ActivityTypeBase', content_rowid='rowid');";

                await connection.ExecuteAsync(createActivityFtsSql).ConfigureAwait(false);

                // Create FTS5 virtual table for CraftTypeBase
                const string createCraftFtsSql = @"
                    CREATE VIRTUAL TABLE IF NOT EXISTS CraftTypeBaseFts 
                    USING fts5(Code, Description, Content, Secondary, content='CraftTypeBase', content_rowid='rowid');";

                await connection.ExecuteAsync(createCraftFtsSql).ConfigureAwait(false);

                // Populate FTS tables with existing data
                await PopulateFtsTablesAsync(connection).ConfigureAwait(false);

                LoggingService.LogInfo("FTS5 virtual tables initialized successfully", "FtsSearchService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing FTS5 tables: {ex.Message}", "FtsSearchService");
                throw;
            }
        }

        /// <summary>
        /// Searches ActivityTypeBase using FTS5 with ranking and Arabic text support.
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="limit">Maximum number of results</param>
        /// <returns>Ranked search results</returns>
        public async Task<List<FtsSearchResult>> SearchActivityTypesAsync(string searchTerm, int limit = 50)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<FtsSearchResult>();
            }

            string cacheKey = $"fts_activity_{searchTerm.ToLowerInvariant()}_{limit}";

            // Check cache first
            if (_ftsCache.TryGetValue(cacheKey, out List<FtsSearchResult> cachedResults))
            {
                _cacheHits++;
                // Only log cache performance periodically
                if (_cacheHits % 20 == 0)
                {
                    LoggingService.LogDebug($"FTS cache performance: {_cacheHits} hits, {_cacheMisses} misses", "FtsSearchService");
                }
                return cachedResults;
            }

            _cacheMisses++;

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Prepare FTS5 query with normalized search term
                string normalizedSearchTerm = TextNormalizationHelper.NormalizeForSearch(searchTerm);
                string ftsQuery = PrepareFtsQuery(normalizedSearchTerm);

                const string searchSql = @"
                    SELECT a.Code, a.Description, 
                           fts.rank as FtsRank,
                           highlight(ActivityTypeBaseFts, 1, '<mark>', '</mark>') as HighlightedDescription
                    FROM ActivityTypeBase a
                    JOIN ActivityTypeBaseFts fts ON a.rowid = fts.rowid
                    WHERE ActivityTypeBaseFts MATCH @FtsQuery
                    ORDER BY fts.rank
                    LIMIT @Limit";

                var results = await connection.QueryAsync<FtsSearchResult>(
                    searchSql,
                    new { FtsQuery = ftsQuery, Limit = limit }
                ).ConfigureAwait(false);

                var resultList = results.ToList();

                // Cache the results
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = _cacheExpiration,
                    Size = 1
                };
                _ftsCache.Set(cacheKey, resultList, cacheOptions);

                // Only log search completion for significant searches
                if (resultList.Count > 10 || searchTerm.Length > 10)
                {
                    LoggingService.LogDebug($"FTS activity search completed for '{searchTerm}': {resultList.Count} results", "FtsSearchService");
                }

                return resultList;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in FTS activity search for '{searchTerm}': {ex.Message}", "FtsSearchService");
                return new List<FtsSearchResult>();
            }
        }

        /// <summary>
        /// Searches CraftTypeBase using FTS5 with ranking and Arabic text support.
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="limit">Maximum number of results</param>
        /// <returns>Ranked search results</returns>
        public async Task<List<FtsSearchResult>> SearchCraftTypesAsync(string searchTerm, int limit = 50)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<FtsSearchResult>();
            }

            string cacheKey = $"fts_craft_{searchTerm.ToLowerInvariant()}_{limit}";

            // Check cache first
            if (_ftsCache.TryGetValue(cacheKey, out List<FtsSearchResult> cachedResults))
            {
                _cacheHits++;
                // Cache performance already logged in activity search method
                return cachedResults;
            }

            _cacheMisses++;

            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Prepare FTS5 query with normalized search term
                string normalizedSearchTerm = TextNormalizationHelper.NormalizeForSearch(searchTerm);
                string ftsQuery = PrepareFtsQuery(normalizedSearchTerm);

                const string searchSql = @"
                    SELECT c.Code, c.Description, c.Content, c.Secondary,
                           fts.rank as FtsRank,
                           highlight(CraftTypeBaseFts, 1, '<mark>', '</mark>') as HighlightedDescription
                    FROM CraftTypeBase c
                    JOIN CraftTypeBaseFts fts ON c.rowid = fts.rowid
                    WHERE CraftTypeBaseFts MATCH @FtsQuery
                    ORDER BY fts.rank
                    LIMIT @Limit";

                var results = await connection.QueryAsync<FtsSearchResult>(
                    searchSql,
                    new { FtsQuery = ftsQuery, Limit = limit }
                ).ConfigureAwait(false);

                var resultList = results.ToList();

                // Cache the results
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = _cacheExpiration,
                    Size = 1
                };
                _ftsCache.Set(cacheKey, resultList, cacheOptions);

                // Only log search completion for significant searches
                if (resultList.Count > 10 || searchTerm.Length > 10)
                {
                    LoggingService.LogDebug($"FTS craft search completed for '{searchTerm}': {resultList.Count} results", "FtsSearchService");
                }

                return resultList;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in FTS craft search for '{searchTerm}': {ex.Message}", "FtsSearchService");
                return new List<FtsSearchResult>();
            }
        }

        /// <summary>
        /// Rebuilds FTS5 indexes for optimal performance.
        /// Should be called periodically or after bulk data changes.
        /// </summary>
        public async Task RebuildFtsIndexesAsync()
        {
            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                // Rebuild ActivityTypeBase FTS index
                await connection.ExecuteAsync("INSERT INTO ActivityTypeBaseFts(ActivityTypeBaseFts) VALUES('rebuild')").ConfigureAwait(false);

                // Rebuild CraftTypeBase FTS index
                await connection.ExecuteAsync("INSERT INTO CraftTypeBaseFts(CraftTypeBaseFts) VALUES('rebuild')").ConfigureAwait(false);

                // Clear cache after rebuild
                _ftsCache?.Dispose();
                _ftsCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 100,
                    CompactionPercentage = 0.25
                });

                LoggingService.LogInfo("FTS5 indexes rebuilt successfully", "FtsSearchService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error rebuilding FTS5 indexes: {ex.Message}", "FtsSearchService");
                throw;
            }
        }

        /// <summary>
        /// Updates FTS5 tables when data changes.
        /// Should be called after INSERT, UPDATE, or DELETE operations.
        /// </summary>
        public async Task UpdateFtsTablesAsync()
        {
            try
            {
                using var pooledConnection = await _databaseService.GetPooledConnectionAsync().ConfigureAwait(false);
                var connection = pooledConnection.Connection;

                await PopulateFtsTablesAsync(connection).ConfigureAwait(false);

                // Clear relevant cache entries
                _ftsCache?.Dispose();
                _ftsCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 100,
                    CompactionPercentage = 0.25
                });

                LoggingService.LogDebug("FTS5 tables updated successfully", "FtsSearchService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating FTS5 tables: {ex.Message}", "FtsSearchService");
            }
        }

        /// <summary>
        /// Clears the FTS search cache.
        /// </summary>
        public void ClearCache()
        {
            try
            {
                _ftsCache?.Dispose();
                _ftsCache = new MemoryCache(new MemoryCacheOptions
                {
                    SizeLimit = 100,
                    CompactionPercentage = 0.25
                });

                LoggingService.LogInfo($"FTS search cache cleared. Final stats - Hits: {_cacheHits}, Misses: {_cacheMisses}", "FtsSearchService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing FTS search cache: {ex.Message}", "FtsSearchService");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Prepares FTS5 query with proper escaping and operators.
        /// </summary>
        private string PrepareFtsQuery(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return string.Empty;

            try
            {
                // Escape special FTS5 characters
                string escaped = searchTerm.Replace("\"", "\"\"");

                // Split into terms for phrase and proximity queries
                string[] terms = TextNormalizationHelper.PrepareSearchTerms(searchTerm);

                if (terms.Length == 1)
                {
                    // Single term - use prefix matching
                    return $"\"{escaped}\"*";
                }
                else if (terms.Length > 1)
                {
                    // Multiple terms - use phrase query with some flexibility
                    string phraseQuery = $"\"{string.Join(" ", terms)}\"";
                    string proximityQuery = string.Join(" AND ", terms.Select(t => $"\"{t}\"*"));
                    
                    // Combine phrase and proximity queries
                    return $"({phraseQuery}) OR ({proximityQuery})";
                }

                return $"\"{escaped}\"*";
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error preparing FTS query for '{searchTerm}': {ex.Message}", "FtsSearchService");
                return $"\"{searchTerm}\"*";
            }
        }

        /// <summary>
        /// Populates FTS5 tables with current data.
        /// </summary>
        private async Task PopulateFtsTablesAsync(IDbConnection connection)
        {
            try
            {
                // Clear existing FTS data
                await connection.ExecuteAsync("DELETE FROM ActivityTypeBaseFts").ConfigureAwait(false);
                await connection.ExecuteAsync("DELETE FROM CraftTypeBaseFts").ConfigureAwait(false);

                // Populate ActivityTypeBase FTS
                const string populateActivityFtsSql = @"
                    INSERT INTO ActivityTypeBaseFts(rowid, Code, Description)
                    SELECT rowid, Code, Description FROM ActivityTypeBase";

                await connection.ExecuteAsync(populateActivityFtsSql).ConfigureAwait(false);

                // Populate CraftTypeBase FTS
                const string populateCraftFtsSql = @"
                    INSERT INTO CraftTypeBaseFts(rowid, Code, Description, Content, Secondary)
                    SELECT rowid, Code, Description, Content, Secondary FROM CraftTypeBase";

                await connection.ExecuteAsync(populateCraftFtsSql).ConfigureAwait(false);

                LoggingService.LogDebug("FTS5 tables populated with current data", "FtsSearchService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error populating FTS5 tables: {ex.Message}", "FtsSearchService");
                throw;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the service and its resources.
        /// </summary>
        public void Dispose()
        {
            try
            {
                _ftsCache?.Dispose();
                LoggingService.LogInfo($"FtsSearchService disposed. Final cache stats - Hits: {_cacheHits}, Misses: {_cacheMisses}", "FtsSearchService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error disposing FtsSearchService: {ex.Message}", "FtsSearchService");
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Represents an FTS5 search result with ranking and highlighting information.
    /// </summary>
    public class FtsSearchResult
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public string Content { get; set; }
        public string Secondary { get; set; }
        public double FtsRank { get; set; }
        public string HighlightedDescription { get; set; }
    }

    #endregion
}
