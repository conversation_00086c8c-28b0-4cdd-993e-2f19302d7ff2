using System;
using System.Collections.Generic;
using System.Linq;

namespace UFU2.Models
{
    /// <summary>
    /// Data transfer object for performance comparison results.
    /// Contains metrics comparing optimized vs legacy database operation performance.
    /// Used for validating Phase 2 database optimization improvements.
    /// </summary>
    public class PerformanceComparisonResult
    {
        /// <summary>
        /// Gets or sets the average execution time for optimized implementation in milliseconds.
        /// </summary>
        public double OptimizedAverageMs { get; set; }

        /// <summary>
        /// Gets or sets the average execution time for legacy implementation in milliseconds.
        /// </summary>
        public double LegacyAverageMs { get; set; }

        /// <summary>
        /// Gets or sets the performance improvement percentage.
        /// Positive values indicate optimized is faster than legacy.
        /// </summary>
        public double ImprovementPercentage { get; set; }

        /// <summary>
        /// Gets or sets the list of individual execution times for optimized implementation.
        /// </summary>
        public List<long> OptimizedTimes { get; set; } = new List<long>();

        /// <summary>
        /// Gets or sets the list of individual execution times for legacy implementation.
        /// </summary>
        public List<long> LegacyTimes { get; set; } = new List<long>();

        /// <summary>
        /// Gets or sets the number of test iterations performed.
        /// </summary>
        public int Iterations { get; set; }

        /// <summary>
        /// Gets or sets the client UID used for testing.
        /// </summary>
        public string ClientUID { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the timestamp when the test was performed.
        /// </summary>
        public DateTime TestTimestamp { get; set; }

        /// <summary>
        /// Gets the minimum execution time for optimized implementation.
        /// </summary>
        public long OptimizedMinMs => OptimizedTimes.Any() ? OptimizedTimes.Min() : 0;

        /// <summary>
        /// Gets the maximum execution time for optimized implementation.
        /// </summary>
        public long OptimizedMaxMs => OptimizedTimes.Any() ? OptimizedTimes.Max() : 0;

        /// <summary>
        /// Gets the minimum execution time for legacy implementation.
        /// </summary>
        public long LegacyMinMs => LegacyTimes.Any() ? LegacyTimes.Min() : 0;

        /// <summary>
        /// Gets the maximum execution time for legacy implementation.
        /// </summary>
        public long LegacyMaxMs => LegacyTimes.Any() ? LegacyTimes.Max() : 0;

        /// <summary>
        /// Gets whether the optimization target of 40-50% improvement was achieved.
        /// </summary>
        public bool TargetAchieved => ImprovementPercentage >= 40.0;

        /// <summary>
        /// Gets the performance improvement status description.
        /// </summary>
        public string ImprovementStatus
        {
            get
            {
                if (ImprovementPercentage >= 50.0)
                    return "Excellent - Exceeds target (50%+)";
                else if (ImprovementPercentage >= 40.0)
                    return "Good - Meets target (40-50%)";
                else if (ImprovementPercentage >= 20.0)
                    return "Moderate - Below target (20-40%)";
                else if (ImprovementPercentage > 0)
                    return "Minimal - Needs improvement (<20%)";
                else
                    return "Poor - No improvement or regression";
            }
        }

        /// <summary>
        /// Gets a formatted summary of the performance comparison.
        /// </summary>
        public string Summary => $"Optimized: {OptimizedAverageMs:F2}ms, Legacy: {LegacyAverageMs:F2}ms, Improvement: {ImprovementPercentage:F1}% ({ImprovementStatus})";

        /// <summary>
        /// Initializes a new instance of the PerformanceComparisonResult class.
        /// </summary>
        public PerformanceComparisonResult()
        {
            TestTimestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// Returns a detailed string representation of the performance comparison results.
        /// </summary>
        /// <returns>Detailed performance comparison string</returns>
        public override string ToString()
        {
            return $"Performance Comparison Results:\n" +
                   $"Client UID: {ClientUID}\n" +
                   $"Test Date: {TestTimestamp:yyyy-MM-dd HH:mm:ss} UTC\n" +
                   $"Iterations: {Iterations}\n" +
                   $"Optimized Average: {OptimizedAverageMs:F2}ms (Min: {OptimizedMinMs}ms, Max: {OptimizedMaxMs}ms)\n" +
                   $"Legacy Average: {LegacyAverageMs:F2}ms (Min: {LegacyMinMs}ms, Max: {LegacyMaxMs}ms)\n" +
                   $"Improvement: {ImprovementPercentage:F1}% - {ImprovementStatus}\n" +
                   $"Target Achieved: {(TargetAchieved ? "Yes" : "No")}";
        }

        /// <summary>
        /// Exports the performance data to a CSV-formatted string for analysis.
        /// </summary>
        /// <returns>CSV-formatted performance data</returns>
        public string ToCsv()
        {
            var csv = "Implementation,Iteration,ExecutionTimeMs\n";
            
            for (int i = 0; i < OptimizedTimes.Count; i++)
            {
                csv += $"Optimized,{i + 1},{OptimizedTimes[i]}\n";
            }
            
            for (int i = 0; i < LegacyTimes.Count; i++)
            {
                csv += $"Legacy,{i + 1},{LegacyTimes[i]}\n";
            }
            
            return csv;
        }
    }




}
