using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media.Imaging;
using UFU2.Common;

namespace UFU2.Converters
{
    /// <summary>
    /// Multi-value converter for profile images that handles both custom images and gender-based defaults.
    /// Takes two inputs: ProfileImageSource (custom) and Gender (for default).
    /// Returns custom image if available, otherwise returns gender-based default image.
    /// Follows UFU2 standards with proper error handling and Arabic localization support.
    /// OPTIMIZED: Enhanced caching, debouncing, and performance monitoring for Phase 2B UI optimizations.
    /// </summary>
    public class ProfileImageConverter : IMultiValueConverter
    {
        #region Private Fields

        // Cache for loaded images to improve performance
        private static BitmapImage? _femaleDefaultImage;
        private static BitmapImage? _maleDefaultImage;

        // Performance optimization: Cache last conversion result to avoid redundant processing
        private static readonly Dictionary<string, System.Windows.Media.ImageSource> _conversionCache = new();
        private static readonly object _cacheLock = new object();

        // Performance monitoring
        private static int _conversionCount = 0;
        private static int _cacheHitCount = 0;

        // Image paths following UFU2 resource structure
        private const string FemaleImagePath = "pack://application:,,,/Resources/female_default.jpg";
        private const string MaleImagePath = "pack://application:,,,/Resources/male_default.jpg";

        #endregion

        #region IMultiValueConverter Implementation

        /// <summary>
        /// Converts profile image source and gender to the appropriate image.
        /// </summary>
        /// <param name="values">Array containing [0] = ProfileImageSource, [1] = Gender</param>
        /// <param name="targetType">Target type (ImageSource)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">Culture info (not used)</param>
        /// <returns>Custom image if available, otherwise gender-based default image</returns>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                System.Threading.Interlocked.Increment(ref _conversionCount);

                // Check if we have the expected number of values
                if (values == null || values.Length < 2)
                {
                    return GetMaleDefaultImage();
                }

                // First value: Custom ProfileImageSource
                var customImageSource = values[0] as System.Windows.Media.ImageSource;

                // If custom image is provided, use it (no caching for custom images)
                if (customImageSource != null)
                {
                    return customImageSource;
                }

                // Second value: Gender for default image selection
                var genderValue = values[1];

                // Handle null or invalid gender input - default to male
                if (genderValue == null)
                {
                    return GetMaleDefaultImage();
                }

                // Convert gender value to integer for comparison
                int gender;
                if (genderValue is int intValue)
                {
                    gender = intValue;
                }
                else if (int.TryParse(genderValue.ToString(), out int parsedValue))
                {
                    gender = parsedValue;
                }
                else
                {
                    return GetMaleDefaultImage();
                }

                // Create cache key for gender-based images
                string cacheKey = $"gender_{gender}";

                // Check cache first for performance
                lock (_cacheLock)
                {
                    if (_conversionCache.TryGetValue(cacheKey, out var cachedImage))
                    {
                        System.Threading.Interlocked.Increment(ref _cacheHitCount);
                        return cachedImage;
                    }
                }

                // Map gender values to appropriate images
                // 0 = Male (ذكر), 1 = Female (أنثى) - following UFU2 ComboBox order
                var result = gender switch
                {
                    0 => GetMaleDefaultImage(),    // Male (ذكر)
                    1 => GetFemaleDefaultImage(),  // Female (أنثى)
                    _ => GetMaleDefaultImage()     // Default fallback to male
                };

                // Cache the result for future use
                lock (_cacheLock)
                {
                    _conversionCache[cacheKey] = result;

                    // Limit cache size to prevent memory issues
                    if (_conversionCache.Count > 10)
                    {
                        _conversionCache.Clear();
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in ProfileImageConverter: {ex.Message}", "ProfileImageConverter");
                return GetMaleDefaultImage(); // Safe fallback to male
            }
        }

        /// <summary>
        /// ConvertBack is not implemented as this is a one-way converter.
        /// </summary>
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ProfileImageConverter is a one-way converter");
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Gets the female default image with caching for performance.
        /// </summary>
        /// <returns>BitmapImage for female default profile image</returns>
        private static BitmapImage GetFemaleDefaultImage()
        {
            try
            {
                if (_femaleDefaultImage == null)
                {
                    _femaleDefaultImage = CreateBitmapImage(FemaleImagePath);
                    LoggingService.LogDebug("Female default image loaded and cached", "ProfileImageConverter");
                }
                return _femaleDefaultImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading female default image: {ex.Message}", "ProfileImageConverter");
                return CreateFallbackImage();
            }
        }

        /// <summary>
        /// Gets the male default image with caching for performance.
        /// </summary>
        /// <returns>BitmapImage for male default profile image</returns>
        private static BitmapImage GetMaleDefaultImage()
        {
            try
            {
                if (_maleDefaultImage == null)
                {
                    _maleDefaultImage = CreateBitmapImage(MaleImagePath);
                    LoggingService.LogDebug("Male default image loaded and cached", "ProfileImageConverter");
                }
                return _maleDefaultImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading male default image: {ex.Message}", "ProfileImageConverter");
                return GetFemaleDefaultImage(); // Fallback to female image
            }
        }

        /// <summary>
        /// Creates a BitmapImage from the specified URI path.
        /// </summary>
        /// <param name="imagePath">Pack URI path to the image resource</param>
        /// <returns>BitmapImage instance</returns>
        private static BitmapImage CreateBitmapImage(string imagePath)
        {
            var bitmap = new BitmapImage();
            bitmap.BeginInit();
            bitmap.UriSource = new Uri(imagePath, UriKind.Absolute);
            bitmap.CacheOption = BitmapCacheOption.OnLoad; // Load immediately and cache
            bitmap.CreateOptions = BitmapCreateOptions.IgnoreImageCache; // Ensure fresh load
            bitmap.EndInit();
            bitmap.Freeze(); // Make thread-safe and improve performance
            return bitmap;
        }

        /// <summary>
        /// Creates a minimal fallback image in case of complete failure.
        /// </summary>
        /// <returns>Empty BitmapImage as last resort fallback</returns>
        private static BitmapImage CreateFallbackImage()
        {
            try
            {
                // Create a minimal 145x127 transparent image as absolute fallback
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.DecodePixelWidth = 127;
                bitmap.DecodePixelHeight = 145;
                bitmap.EndInit();
                bitmap.Freeze();
                return bitmap;
            }
            catch
            {
                // If even this fails, return a new empty BitmapImage
                return new BitmapImage();
            }
        }

        #endregion

        #region Static Cleanup Methods

        /// <summary>
        /// Clears cached images to free memory when needed.
        /// Call this method when the application is shutting down or when memory cleanup is required.
        /// OPTIMIZED: Enhanced to clear both image and conversion caches.
        /// </summary>
        public static void ClearImageCache()
        {
            try
            {
                _femaleDefaultImage = null;
                _maleDefaultImage = null;

                lock (_cacheLock)
                {
                    _conversionCache.Clear();
                }

                LoggingService.LogDebug("Profile image cache cleared", "ProfileImageConverter");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing profile image cache: {ex.Message}", "ProfileImageConverter");
            }
        }

        /// <summary>
        /// Gets performance statistics for the ProfileImageConverter.
        /// Used for Phase 2B UI optimization monitoring.
        /// </summary>
        /// <returns>Performance statistics including conversion count and cache hit ratio</returns>
        public static (int ConversionCount, int CacheHitCount, double CacheHitRatio, int CacheSize) GetPerformanceStats()
        {
            lock (_cacheLock)
            {
                var conversionCount = _conversionCount;
                var cacheHitCount = _cacheHitCount;
                var cacheHitRatio = conversionCount > 0 ? (double)cacheHitCount / conversionCount : 0.0;
                var cacheSize = _conversionCache.Count;

                return (conversionCount, cacheHitCount, cacheHitRatio, cacheSize);
            }
        }

        /// <summary>
        /// Resets performance counters for testing and monitoring.
        /// </summary>
        public static void ResetPerformanceCounters()
        {
            System.Threading.Interlocked.Exchange(ref _conversionCount, 0);
            System.Threading.Interlocked.Exchange(ref _cacheHitCount, 0);
        }

        #endregion
    }
}