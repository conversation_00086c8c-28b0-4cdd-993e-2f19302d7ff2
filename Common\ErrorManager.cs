using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using UFU2.Common;
using UFU2.Common.Models;
using UFU2.Common.Utilities;
using UFU2.Services;

namespace UFU2.Common
{
    /// <summary>
    /// Enhanced error management utility for UFU2 application.
    /// Provides centralized error handling, logging, and user notification functionality.
    /// Integrates with existing LoggingService and follows UFU2 error handling patterns.
    /// Includes input validation, async support, and structured error context.
    /// </summary>
    public static class ErrorManager
    {
        #region Constants and Configuration

        /// <summary>
        /// Maximum message length to prevent UI overflow
        /// </summary>
        private const int MaxMessageLength = 500;

        /// <summary>
        /// Maximum title length to prevent UI overflow
        /// </summary>
        private const int MaxTitleLength = 100;

        /// <summary>
        /// Default timeout for async operations in milliseconds
        /// </summary>
        private const int DefaultAsyncTimeoutMs = 30000;

        #endregion

        #region Structured Error Context

        /// <summary>
        /// Structured error context for enhanced debugging and monitoring
        /// </summary>
        public class ErrorContext
        {
            public string Source { get; set; } = "UFU2";
            public string Operation { get; set; } = "Unknown";
            public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
            public DateTime Timestamp { get; set; } = DateTime.Now;
            public string UserAgent { get; set; } = Environment.MachineName;
            public long ElapsedMs { get; set; }
        }

        #endregion

        #region Input Validation

        /// <summary>
        /// Validates and sanitizes user message input
        /// </summary>
        /// <param name="message">The message to validate</param>
        /// <param name="parameterName">The parameter name for error reporting</param>
        /// <returns>Validated and truncated message</returns>
        private static string ValidateMessage(string message, string parameterName = "message")
        {
            if (string.IsNullOrEmpty(message))
            {
                LoggingService.LogWarning($"Empty {parameterName} provided to ErrorManager", "ErrorManager");
                return "رسالة خطأ غير محددة"; // "Unspecified error message" in Arabic
            }

            if (message.Length > MaxMessageLength)
            {
                LoggingService.LogWarning($"{parameterName} truncated from {message.Length} to {MaxMessageLength} characters", "ErrorManager");
                return message.Substring(0, MaxMessageLength - 3) + "...";
            }

            return message.Trim();
        }

        /// <summary>
        /// Validates and sanitizes title input
        /// </summary>
        /// <param name="title">The title to validate</param>
        /// <param name="defaultTitle">Default title if validation fails</param>
        /// <returns>Validated and truncated title</returns>
        private static string ValidateTitle(string? title, string defaultTitle)
        {
            if (string.IsNullOrEmpty(title))
            {
                return defaultTitle;
            }

            if (title.Length > MaxTitleLength)
            {
                LoggingService.LogWarning($"Title truncated from {title.Length} to {MaxTitleLength} characters", "ErrorManager");
                return title.Substring(0, MaxTitleLength - 3) + "...";
            }

            return title.Trim();
        }

        #endregion

        #region Service Error Handler Integration

        /// <summary>
        /// Executes an action with error handling and performance logging
        /// Integrated from ServiceErrorHandler for consolidation
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="serviceName">The name of the service</param>
        /// <param name="operationName">The name of the operation</param>
        /// <param name="errorMessage">The error message to log if an exception occurs</param>
        /// <param name="logLevel">The log level for errors</param>
        /// <returns>True if the operation succeeded, false otherwise</returns>
        public static bool ExecuteWithErrorHandling(
            Action action,
            string serviceName,
            string operationName,
            string errorMessage,
            LogLevel logLevel = LogLevel.Error)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                action();
                stopwatch.Stop();

                LoggingService.LogDebug(
                    $"{serviceName}.{operationName} completed in {stopwatch.ElapsedMilliseconds}ms",
                    serviceName);

                return true;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                var context = new ErrorContext
                {
                    Source = serviceName,
                    Operation = operationName,
                    ElapsedMs = stopwatch.ElapsedMilliseconds,
                    Properties = new Dictionary<string, object>
                    {
                        ["OriginalErrorMessage"] = errorMessage,
                        ["ExecutionTimeMs"] = stopwatch.ElapsedMilliseconds
                    }
                };

                LogExceptionWithContext(ex, logLevel, context);
                return false;
            }
        }

        /// <summary>
        /// Executes an async action with error handling and performance logging
        /// </summary>
        /// <param name="func">The async function to execute</param>
        /// <param name="serviceName">The name of the service</param>
        /// <param name="operationName">The name of the operation</param>
        /// <param name="errorMessage">The error message to log if an exception occurs</param>
        /// <param name="logLevel">The log level for errors</param>
        /// <returns>True if the operation succeeded, false otherwise</returns>
        public static async Task<bool> ExecuteWithErrorHandlingAsync(
            Func<Task> func,
            string serviceName,
            string operationName,
            string errorMessage,
            LogLevel logLevel = LogLevel.Error)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                await func();
                stopwatch.Stop();

                LoggingService.LogDebug(
                    $"{serviceName}.{operationName} completed in {stopwatch.ElapsedMilliseconds}ms",
                    serviceName);

                return true;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                var context = new ErrorContext
                {
                    Source = serviceName,
                    Operation = operationName,
                    ElapsedMs = stopwatch.ElapsedMilliseconds,
                    Properties = new Dictionary<string, object>
                    {
                        ["OriginalErrorMessage"] = errorMessage,
                        ["ExecutionTimeMs"] = stopwatch.ElapsedMilliseconds,
                        ["IsAsync"] = true
                    }
                };

                LogExceptionWithContext(ex, logLevel, context);
                return false;
            }
        }

        /// <summary>
        /// Executes a function with error handling and performance logging
        /// </summary>
        /// <type param name="T">The return type</type param>
        /// <param name="func">The function to execute</param>
        /// <param name="serviceName">The name of the service</param>
        /// <param name="operationName">The name of the operation</param>
        /// <param name="errorMessage">The error message to log if an exception occurs</param>
        /// <param name="defaultValue">The default value to return on error</param>
        /// <param name="logLevel">The log level for errors</param>
        /// <returns>The result of the function or the default value on error</returns>
        public static async Task<T> ExecuteWithErrorHandlingAsync<T>(
            Func<Task<T>> func,
            string serviceName,
            string operationName,
            string errorMessage,
            T defaultValue = default(T),
            LogLevel logLevel = LogLevel.Error)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                var result = await func();
                stopwatch.Stop();

                LoggingService.LogDebug(
                    $"{serviceName}.{operationName} completed in {stopwatch.ElapsedMilliseconds}ms",
                    serviceName);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                var context = new ErrorContext
                {
                    Source = serviceName,
                    Operation = operationName,
                    ElapsedMs = stopwatch.ElapsedMilliseconds,
                    Properties = new Dictionary<string, object>
                    {
                        ["OriginalErrorMessage"] = errorMessage,
                        ["ExecutionTimeMs"] = stopwatch.ElapsedMilliseconds,
                        ["IsAsync"] = true,
                        ["ReturnType"] = typeof(T).Name,
                        ["DefaultValue"] = defaultValue?.ToString() ?? "null"
                    }
                };

                LogExceptionWithContext(ex, logLevel, context);
                return defaultValue;
            }
        }

        #endregion

        #region Exception Logging

        /// <summary>
        /// Logs an exception with the specified log level and source
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="logLevel">The log level for the exception</param>
        /// <param name="source">The source component where the exception occurred</param>
        public static void LogException(Exception exception, LogLevel logLevel, string source = "UFU2")
        {
            var context = new ErrorContext
            {
                Source = source,
                Operation = "LogException"
            };

            LogExceptionWithContext(exception, logLevel, context);
        }

        /// <summary>
        /// Logs an exception with structured error context for enhanced debugging
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="logLevel">The log level for the exception</param>
        /// <param name="context">Structured error context</param>
        public static void LogExceptionWithContext(Exception exception, LogLevel logLevel, ErrorContext context)
        {
            if (exception == null)
            {
                LoggingService.LogWarning("Attempted to log null exception", context.Source);
                return;
            }

            var errorMessage = $"Exception in {context.Operation}: {exception.Message}";
            var stackTrace = $"Stack trace: {exception.StackTrace}";
            var contextInfo = $"Context - Source: {context.Source}, Operation: {context.Operation}, " +
                             $"Timestamp: {context.Timestamp:yyyy-MM-dd HH:mm:ss.fff}, " +
                             $"ElapsedMs: {context.ElapsedMs}, UserAgent: {context.UserAgent}";

            // Log context properties if any
            if (context.Properties.Count > 0)
            {
                var properties = string.Join(", ", context.Properties.Select(kvp => $"{kvp.Key}: {kvp.Value}"));
                contextInfo += $", Properties: [{properties}]";
            }

            switch (logLevel)
            {
                case LogLevel.Error:
                    LoggingService.LogError(errorMessage, context.Source);
                    LoggingService.LogError(contextInfo, context.Source);
                    LoggingService.LogError(stackTrace, context.Source);
                    break;
                case LogLevel.Warning:
                    LoggingService.LogWarning(errorMessage, context.Source);
                    LoggingService.LogWarning(contextInfo, context.Source);
                    LoggingService.LogWarning(stackTrace, context.Source);
                    break;
                case LogLevel.Info:
                    LoggingService.LogInfo(errorMessage, context.Source);
                    LoggingService.LogInfo(contextInfo, context.Source);
                    LoggingService.LogInfo(stackTrace, context.Source);
                    break;
                case LogLevel.Debug:
                    LoggingService.LogDebug(errorMessage, context.Source);
                    LoggingService.LogDebug(contextInfo, context.Source);
                    LoggingService.LogDebug(stackTrace, context.Source);
                    break;
                default:
                    LoggingService.LogError(errorMessage, context.Source);
                    LoggingService.LogError(contextInfo, context.Source);
                    LoggingService.LogError(stackTrace, context.Source);
                    break;
            }

            // Log inner exception if present
            if (exception.InnerException != null)
            {
                LoggingService.LogError($"Inner exception: {exception.InnerException.Message}", context.Source);
                LoggingService.LogError($"Inner stack trace: {exception.InnerException.StackTrace}", context.Source);
            }
        }

        #endregion

        #region User Error Display

        /// <summary>
        /// Shows a user-friendly error message dialog with input validation
        /// </summary>
        /// <param name="message">The error message to display to the user</param>
        /// <param name="title">The title of the error dialog</param>
        /// <param name="logSource">Optional source for logging the error display</param>
        public static void ShowUserError(string message, string title = "خطأ", string logSource = "ErrorManager")
        {
            try
            {
                // Validate and sanitize inputs
                var validatedMessage = ValidateMessage(message);
                var validatedTitle = ValidateTitle(title, "خطأ");

                LoggingService.LogInfo($"Displaying user error: {validatedTitle} - {validatedMessage}", logSource);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        validatedMessage,
                        validatedTitle,
                        MessageBoxButton.OK,
                        MessageBoxImage.Error,
                        MessageBoxResult.OK,
                        MessageBoxOptions.RtlReading); // Support for Arabic RTL text
                });
            }
            catch (Exception ex)
            {
                // Fallback logging if UI thread is not available
                LoggingService.LogError($"Failed to show user error dialog: {ex.Message}", logSource);

                // Try to show a basic console message as last resort
                Console.WriteLine($"ERROR: {title} - {message}");
            }
        }

        /// <summary>
        /// Shows a user-friendly warning message dialog with input validation
        /// </summary>
        /// <param name="message">The warning message to display to the user</param>
        /// <param name="title">The title of the warning dialog</param>
        /// <param name="logSource">Optional source for logging the warning display</param>
        public static void ShowUserWarning(string message, string title = "تحذير", string logSource = "ErrorManager")
        {
            try
            {
                // Validate and sanitize inputs
                var validatedMessage = ValidateMessage(message);
                var validatedTitle = ValidateTitle(title, "تحذير");

                LoggingService.LogInfo($"Displaying user warning: {validatedTitle} - {validatedMessage}", logSource);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        validatedMessage,
                        validatedTitle,
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning,
                        MessageBoxResult.OK,
                        MessageBoxOptions.RtlReading); // Support for Arabic RTL text
                });
            }
            catch (Exception ex)
            {
                // Fallback logging if UI thread is not available
                LoggingService.LogError($"Failed to show user warning dialog: {ex.Message}", logSource);

                // Try to show a basic console message as last resort
                Console.WriteLine($"WARNING: {title} - {message}");
            }
        }

        /// <summary>
        /// Shows a user-friendly information message dialog with input validation
        /// </summary>
        /// <param name="message">The information message to display to the user</param>
        /// <param name="title">The title of the information dialog</param>
        /// <param name="logSource">Optional source for logging the information display</param>
        public static void ShowUserInfo(string message, string title = "معلومات", string logSource = "ErrorManager")
        {
            try
            {
                // Validate and sanitize inputs
                var validatedMessage = ValidateMessage(message);
                var validatedTitle = ValidateTitle(title, "معلومات");

                LoggingService.LogInfo($"Displaying user info: {validatedTitle} - {validatedMessage}", logSource);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(
                        validatedMessage,
                        validatedTitle,
                        MessageBoxButton.OK,
                        MessageBoxImage.Information,
                        MessageBoxResult.OK,
                        MessageBoxOptions.RtlReading); // Support for Arabic RTL text
                });
            }
            catch (Exception ex)
            {
                // Fallback logging if UI thread is not available
                LoggingService.LogError($"Failed to show user info dialog: {ex.Message}", logSource);

                // Try to show a basic console message as last resort
                Console.WriteLine($"INFO: {title} - {message}");
            }
        }

        #endregion

        #region Combined Error Handling

        /// <summary>
        /// Logs an exception and shows a user-friendly error message
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the error dialog</param>
        /// <param name="logLevel">The log level for the exception</param>
        /// <param name="source">The source component where the exception occurred</param>
        public static void HandleError(Exception exception, string userMessage, string title = "خطأ", 
            LogLevel logLevel = LogLevel.Error, string source = "UFU2")
        {
            // Log the technical exception details
            LogException(exception, logLevel, source);
            
            // Show user-friendly message
            ShowUserError(userMessage, title, source);
        }

        /// <summary>
        /// Logs a warning and shows a user-friendly warning message
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the warning dialog</param>
        /// <param name="source">The source component where the exception occurred</param>
        public static void HandleWarning(Exception exception, string userMessage, string title = "تحذير", 
            string source = "UFU2")
        {
            // Log the technical exception details
            LogException(exception, LogLevel.Warning, source);
            
            // Show user-friendly message
            ShowUserWarning(userMessage, title, source);
        }

        #endregion

        #region Toast-Based User Notifications

        /// <summary>
        /// Shows a user-friendly error message as a toast notification with input validation
        /// </summary>
        /// <param name="message">The error message to display to the user</param>
        /// <param name="title">The title of the error notification (optional, defaults to Arabic "خطأ")</param>
        /// <param name="logSource">Optional source for logging the error display</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        /// <param name="operationId">Optional operation ID for error deduplication</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        public static void ShowUserErrorToast(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, string? operationId = null, int duration = 5000)
        {
            try
            {
                // Validate and sanitize inputs
                var validatedMessage = ValidateMessage(message);
                var validatedTitle = ValidateTitle(title, "خطأ");
                var validatedDetailMessage = string.IsNullOrEmpty(detailMessage) ? null : ValidateMessage(detailMessage, "detailMessage");

                LoggingService.LogInfo($"Displaying user error toast: {validatedTitle} - {validatedMessage}", logSource);

                if (ToastService.IsInitialized)
                {
                    ToastService.Error(validatedTitle, validatedMessage, duration, null, validatedDetailMessage);
                }
                else
                {
                    // Fallback to modal dialog if toast service is not available
                    ShowUserError(validatedMessage, validatedTitle, logSource);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show user error toast: {ex.Message}", logSource);
                // Fallback to modal dialog
                ShowUserError(message, title ?? "خطأ", logSource);
            }
        }

        /// <summary>
        /// Shows a user-friendly warning message as a toast notification
        /// </summary>
        /// <param name="message">The warning message to display to the user</param>
        /// <param name="title">The title of the warning notification (optional, defaults to Arabic "تحذير")</param>
        /// <param name="logSource">Optional source for logging the warning display</param>
        /// <param name="detailMessage">Optional detailed message for warning dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        public static void ShowUserWarningToast(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 5000)
        {
            try
            {
                var warningTitle = title ?? "تحذير";
                LoggingService.LogInfo($"Displaying user warning toast: {warningTitle} - {message}", logSource);

                if (ToastService.IsInitialized)
                {
                    ToastService.Warning(warningTitle, message, duration, null, detailMessage);
                }
                else
                {
                    // Fallback to modal dialog if toast service is not available
                    ShowUserWarning(message, warningTitle, logSource);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show user warning toast: {ex.Message}", logSource);
                // Fallback to modal dialog
                ShowUserWarning(message, title ?? "تحذير", logSource);
            }
        }

        /// <summary>
        /// Shows a user-friendly information message as a toast notification
        /// </summary>
        /// <param name="message">The information message to display to the user</param>
        /// <param name="title">The title of the information notification (optional, defaults to Arabic "معلومات")</param>
        /// <param name="logSource">Optional source for logging the information display</param>
        /// <param name="detailMessage">Optional detailed message for info dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        public static void ShowUserInfoToast(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 3000)
        {
            try
            {
                var infoTitle = title ?? "معلومات";
                LoggingService.LogInfo($"Displaying user info toast: {infoTitle} - {message}", logSource);

                if (ToastService.IsInitialized)
                {
                    ToastService.Info(infoTitle, message, duration, null, detailMessage);
                }
                else
                {
                    // Fallback to modal dialog if toast service is not available
                    ShowUserInfo(message, infoTitle, logSource);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show user info toast: {ex.Message}", logSource);
                // Fallback to modal dialog
                ShowUserInfo(message, title ?? "معلومات", logSource);
            }
        }

        /// <summary>
        /// Shows a user-friendly success message as a toast notification
        /// </summary>
        /// <param name="message">The success message to display to the user</param>
        /// <param name="title">The title of the success notification (optional, defaults to Arabic "نجح")</param>
        /// <param name="logSource">Optional source for logging the success display</param>
        /// <param name="detailMessage">Optional detailed message for success dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        public static void ShowUserSuccessToast(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 3000)
        {
            try
            {
                var successTitle = title ?? "نجح";
                LoggingService.LogInfo($"Displaying user success toast: {successTitle} - {message}", logSource);

                if (ToastService.IsInitialized)
                {
                    ToastService.Success(successTitle, message, duration, null, detailMessage);
                }
                else
                {
                    // Fallback to modal dialog if toast service is not available
                    ShowUserInfo(message, successTitle, logSource);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to show user success toast: {ex.Message}", logSource);
                // Fallback to modal dialog
                ShowUserInfo(message, title ?? "نجح", logSource);
            }
        }

        #endregion

        #region Async Toast-Based User Notifications

        /// <summary>
        /// Shows a user-friendly error message as a toast notification asynchronously
        /// Optimized for database-related error scenarios
        /// </summary>
        /// <param name="message">The error message to display to the user</param>
        /// <param name="title">The title of the error notification (optional, defaults to Arabic "خطأ")</param>
        /// <param name="logSource">Optional source for logging the error display</param>
        /// <param name="detailMessage">Optional detailed message for error dialogs</param>
        /// <param name="operationId">Optional operation ID for error deduplication</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task ShowUserErrorToastAsync(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, string? operationId = null, int duration = 5000)
        {
            await Task.Run(() => ShowUserErrorToast(message, title, logSource, detailMessage, operationId, duration));
        }

        /// <summary>
        /// Shows a user-friendly success message as a toast notification asynchronously
        /// Optimized for database-related success scenarios
        /// </summary>
        /// <param name="message">The success message to display to the user</param>
        /// <param name="title">The title of the success notification (optional, defaults to Arabic "نجح")</param>
        /// <param name="logSource">Optional source for logging the success display</param>
        /// <param name="detailMessage">Optional detailed message for success dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 3000)</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task ShowUserSuccessToastAsync(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 3000)
        {
            await Task.Run(() => ShowUserSuccessToast(message, title, logSource, detailMessage, duration));
        }

        /// <summary>
        /// Shows a user-friendly warning message as a toast notification asynchronously
        /// Optimized for database-related warning scenarios
        /// </summary>
        /// <param name="message">The warning message to display to the user</param>
        /// <param name="title">The title of the warning notification (optional, defaults to Arabic "تحذير")</param>
        /// <param name="logSource">Optional source for logging the warning display</param>
        /// <param name="detailMessage">Optional detailed message for warning dialogs</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task ShowUserWarningToastAsync(string message, string? title = null, string logSource = "ErrorManager",
            string? detailMessage = null, int duration = 5000)
        {
            await Task.Run(() => ShowUserWarningToast(message, title, logSource, detailMessage, duration));
        }

        /// <summary>
        /// Handles database-related errors with async toast notification
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the error notification</param>
        /// <param name="logLevel">The log level for the exception</param>
        /// <param name="source">The source component where the exception occurred</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task HandleDatabaseErrorToastAsync(Exception exception, string userMessage, string title = "خطأ في قاعدة البيانات",
            LogLevel logLevel = LogLevel.Error, string source = "UFU2", int duration = 5000)
        {
            // Create enhanced context for database errors
            var context = new ErrorContext
            {
                Source = source,
                Operation = "DatabaseOperation",
                Properties = new Dictionary<string, object>
                {
                    ["ErrorType"] = "Database",
                    ["ExceptionType"] = exception.GetType().Name,
                    ["UserMessage"] = userMessage,
                    ["IsAsync"] = true
                }
            };

            // Log the technical exception details with context
            LogExceptionWithContext(exception, logLevel, context);

            // Show user-friendly toast message asynchronously
            await ShowUserErrorToastAsync(userMessage, title, source, exception.Message, null, duration);
        }

        #endregion

        #region Combined Toast-Based Error Handling

        /// <summary>
        /// Logs an exception and shows a user-friendly error message as a toast notification
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the error notification</param>
        /// <param name="logLevel">The log level for the exception</param>
        /// <param name="source">The source component where the exception occurred</param>
        /// <param name="operationId">Optional operation ID for error deduplication</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        public static void HandleErrorToast(Exception exception, string userMessage, string title = "خطأ",
            LogLevel logLevel = LogLevel.Error, string source = "UFU2", string? operationId = null, int duration = 5000)
        {
            // Log the technical exception details
            LogException(exception, logLevel, source);

            // Determine error severity based on exception type and source
            var severity = DetermineErrorSeverity(exception, source, logLevel);

            // Check if toast should be displayed (deduplication logic)
            if (ErrorDeduplicationManager.ShouldDisplayToast(operationId, severity, source, exception, userMessage))
            {
                // Show user-friendly toast message
                ShowUserErrorToast(userMessage, title, source, exception.Message, operationId, duration);

                // Mark toast as displayed
                ErrorDeduplicationManager.MarkToastDisplayed(operationId, severity, source, userMessage);
            }
            else
            {
                LoggingService.LogInfo($"Error toast suppressed for operation {operationId}: {userMessage}", source);
            }
        }

        /// <summary>
        /// Logs a warning and shows a user-friendly warning message as a toast notification
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="userMessage">The user-friendly message to display</param>
        /// <param name="title">The title of the warning notification</param>
        /// <param name="source">The source component where the exception occurred</param>
        /// <param name="duration">Duration in milliseconds (default: 5000)</param>
        public static void HandleWarningToast(Exception exception, string userMessage, string title = "تحذير",
            string source = "UFU2", int duration = 5000)
        {
            // Log the technical exception details
            LogException(exception, LogLevel.Warning, source);

            // Show user-friendly toast message
            ShowUserWarningToast(userMessage, title, source, exception.Message, duration);
        }

        #endregion

        #region Error Deduplication Helpers

        /// <summary>
        /// Determines the error severity based on exception type, source, and log level.
        /// Used for error deduplication and prioritization.
        /// </summary>
        /// <param name="exception">The exception that occurred</param>
        /// <param name="source">The source component</param>
        /// <param name="logLevel">The log level</param>
        /// <returns>The determined error severity</returns>
        private static ErrorSeverity DetermineErrorSeverity(Exception exception, string source, LogLevel logLevel)
        {
            // Critical severity for system-level failures
            if (exception is OutOfMemoryException ||
                exception is StackOverflowException ||
                exception is AccessViolationException ||
                source.Contains("Database") && exception.Message.Contains("connection"))
            {
                return ErrorSeverity.Critical;
            }

            // High severity for data layer and UID generation failures
            if (source.Contains("DatabaseService") ||
                source.Contains("UIDGenerationService") ||
                exception is InvalidOperationException ||
                exception is ArgumentException)
            {
                return ErrorSeverity.High;
            }

            // Medium severity for service layer failures
            if (source.Contains("Service") && !source.Contains("DatabaseService"))
            {
                return ErrorSeverity.Medium;
            }

            // Low severity for UI and general failures
            return ErrorSeverity.Low;
        }

        #endregion
    }
}
