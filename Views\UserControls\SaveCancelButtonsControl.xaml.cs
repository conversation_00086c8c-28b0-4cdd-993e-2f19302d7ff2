using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using UFU2.Common;

namespace UFU2.Views.UserControls
{
    /// <summary>
    /// Reusable UserControl for standardized Save/Cancel button functionality across UFU2 dialogs.
    /// Provides configurable Arabic text, tooltips, styling, and supports both Command binding and Click events.
    /// Follows UFU2's MVVM architecture with MaterialDesign styling and Arabic RTL layout support.
    /// </summary>
    public partial class SaveCancelButtonsControl : UserControl
    {
        #region Constructors

        /// <summary>
        /// Initializes a new instance of the SaveCancelButtonsControl.
        /// </summary>
        public SaveCancelButtonsControl()
        {
            InitializeComponent();
            LoggingService.LogInfo("SaveCancelButtonsControl initialized", "SaveCancelButtonsControl");
        }

        #endregion

        #region Dependency Properties

        /// <summary>
        /// Command to execute when Save button is clicked.
        /// </summary>
        public static readonly DependencyProperty SaveCommandProperty =
            DependencyProperty.Register(nameof(SaveCommand), typeof(ICommand), typeof(SaveCancelButtonsControl), new PropertyMetadata(null));

        public ICommand SaveCommand
        {
            get => (ICommand)GetValue(SaveCommandProperty);
            set => SetValue(SaveCommandProperty, value);
        }

        /// <summary>
        /// Command to execute when Cancel button is clicked.
        /// </summary>
        public static readonly DependencyProperty CancelCommandProperty =
            DependencyProperty.Register(nameof(CancelCommand), typeof(ICommand), typeof(SaveCancelButtonsControl), new PropertyMetadata(null));

        public ICommand CancelCommand
        {
            get => (ICommand)GetValue(CancelCommandProperty);
            set => SetValue(CancelCommandProperty, value);
        }

        /// <summary>
        /// Text displayed on the Save button. Default: "حفظ"
        /// </summary>
        public static readonly DependencyProperty SaveTextProperty =
            DependencyProperty.Register(nameof(SaveText), typeof(string), typeof(SaveCancelButtonsControl), new PropertyMetadata("حفظ"));

        public string SaveText
        {
            get => (string)GetValue(SaveTextProperty);
            set => SetValue(SaveTextProperty, value);
        }

        /// <summary>
        /// Text displayed on the Cancel button. Default: "إلغاء"
        /// </summary>
        public static readonly DependencyProperty CancelTextProperty =
            DependencyProperty.Register(nameof(CancelText), typeof(string), typeof(SaveCancelButtonsControl), new PropertyMetadata("إلغاء"));

        public string CancelText
        {
            get => (string)GetValue(CancelTextProperty);
            set => SetValue(CancelTextProperty, value);
        }

        /// <summary>
        /// Tooltip text for the Save button. Default: "حفظ"
        /// </summary>
        public static readonly DependencyProperty SaveTooltipProperty =
            DependencyProperty.Register(nameof(SaveTooltip), typeof(string), typeof(SaveCancelButtonsControl), new PropertyMetadata("حفظ"));

        public string SaveTooltip
        {
            get => (string)GetValue(SaveTooltipProperty);
            set => SetValue(SaveTooltipProperty, value);
        }

        /// <summary>
        /// Tooltip text for the Cancel button. Default: "إلغاء التغييرات"
        /// </summary>
        public static readonly DependencyProperty CancelTooltipProperty =
            DependencyProperty.Register(nameof(CancelTooltip), typeof(string), typeof(SaveCancelButtonsControl), new PropertyMetadata("إلغاء التغييرات"));

        public string CancelTooltip
        {
            get => (string)GetValue(CancelTooltipProperty);
            set => SetValue(CancelTooltipProperty, value);
        }

        /// <summary>
        /// Whether the Save button is enabled. Default: true
        /// </summary>
        public static readonly DependencyProperty IsSaveEnabledProperty =
            DependencyProperty.Register(nameof(IsSaveEnabled), typeof(bool), typeof(SaveCancelButtonsControl), new PropertyMetadata(true));

        public bool IsSaveEnabled
        {
            get => (bool)GetValue(IsSaveEnabledProperty);
            set => SetValue(IsSaveEnabledProperty, value);
        }

        

        /// <summary>
        /// Style for the Save button. Default: PrimaryButtonStyle
        /// </summary>
        public static readonly DependencyProperty SaveButtonStyleProperty =
            DependencyProperty.Register(nameof(SaveButtonStyle), typeof(Style), typeof(SaveCancelButtonsControl), 
                new PropertyMetadata(null, OnSaveButtonStyleChanged));

        public Style SaveButtonStyle
        {
            get => (Style)GetValue(SaveButtonStyleProperty);
            set => SetValue(SaveButtonStyleProperty, value);
        }

        private static void OnSaveButtonStyleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SaveCancelButtonsControl control && e.NewValue == null)
            {
                // Set default style if null
                control.SetValue(SaveButtonStyleProperty, Application.Current.FindResource("PrimaryButtonStyle"));
            }
        }

        /// <summary>
        /// Style for the Cancel button. Default: SecondaryButtonStyle
        /// </summary>
        public static readonly DependencyProperty CancelButtonStyleProperty =
            DependencyProperty.Register(nameof(CancelButtonStyle), typeof(Style), typeof(SaveCancelButtonsControl), 
                new PropertyMetadata(null, OnCancelButtonStyleChanged));

        public Style CancelButtonStyle
        {
            get => (Style)GetValue(CancelButtonStyleProperty);
            set => SetValue(CancelButtonStyleProperty, value);
        }

        private static void OnCancelButtonStyleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SaveCancelButtonsControl control && e.NewValue == null)
            {
                // Set default style if null
                control.SetValue(CancelButtonStyleProperty, Application.Current.FindResource("SecondaryButtonStyle"));
            }
        }

        #endregion

        #region Layout Dependency Properties

        /// <summary>
        /// Margin for the Card container. Default: "7,3"
        /// </summary>
        public static readonly DependencyProperty CardMarginProperty =
            DependencyProperty.Register(nameof(CardMargin), typeof(Thickness), typeof(SaveCancelButtonsControl), new PropertyMetadata(new Thickness(7, 3, 7, 3)));

        public Thickness CardMargin
        {
            get => (Thickness)GetValue(CardMarginProperty);
            set => SetValue(CardMarginProperty, value);
        }

        /// <summary>
        /// Padding for the Card container. Default: "7"
        /// </summary>
        public static readonly DependencyProperty CardPaddingProperty =
            DependencyProperty.Register(nameof(CardPadding), typeof(Thickness), typeof(SaveCancelButtonsControl), new PropertyMetadata(new Thickness(7)));

        public Thickness CardPadding
        {
            get => (Thickness)GetValue(CardPaddingProperty);
            set => SetValue(CardPaddingProperty, value);
        }

        /// <summary>
        /// Padding for both buttons. Default: "15,0,15,0"
        /// </summary>
        public static readonly DependencyProperty ButtonPaddingProperty =
            DependencyProperty.Register(nameof(ButtonPadding), typeof(Thickness), typeof(SaveCancelButtonsControl), new PropertyMetadata(new Thickness(15,0,15,0)));

        public Thickness ButtonPadding
        {
            get => (Thickness)GetValue(ButtonPaddingProperty);
            set => SetValue(ButtonPaddingProperty, value);
        }

        /// <summary>
        /// Margin for the Cancel button. Default: "0,0,4,0"
        /// </summary>
        public static readonly DependencyProperty CancelButtonMarginProperty =
            DependencyProperty.Register(nameof(CancelButtonMargin), typeof(Thickness), typeof(SaveCancelButtonsControl), new PropertyMetadata(new Thickness(0, 0, 4, 0)));

        public Thickness CancelButtonMargin
        {
            get => (Thickness)GetValue(CancelButtonMarginProperty);
            set => SetValue(CancelButtonMarginProperty, value);
        }

        /// <summary>
        /// Margin for the Save button. Default: "4,0,0,0"
        /// </summary>
        public static readonly DependencyProperty SaveButtonMarginProperty =
            DependencyProperty.Register(nameof(SaveButtonMargin), typeof(Thickness), typeof(SaveCancelButtonsControl), new PropertyMetadata(new Thickness(4, 0, 0, 0)));

        public Thickness SaveButtonMargin
        {
            get => (Thickness)GetValue(SaveButtonMarginProperty);
            set => SetValue(SaveButtonMarginProperty, value);
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the Save button is clicked (for backward compatibility with Click event handlers).
        /// </summary>
        public event RoutedEventHandler SaveClick;

        /// <summary>
        /// Event raised when the Cancel button is clicked (for backward compatibility with Click event handlers).
        /// </summary>
        public event RoutedEventHandler CancelClick;

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the Save button click event.
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Save button clicked in SaveCancelButtonsControl", "SaveCancelButtonsControl");
                
                // Raise the SaveClick event for backward compatibility
                SaveClick?.Invoke(this, e);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in SaveButton_Click: {ex.Message}", "SaveCancelButtonsControl");
            }
        }

        /// <summary>
        /// Handles the Cancel button click event.
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Cancel button clicked in SaveCancelButtonsControl", "SaveCancelButtonsControl");
                
                // Raise the CancelClick event for backward compatibility
                CancelClick?.Invoke(this, e);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in CancelButton_Click: {ex.Message}", "SaveCancelButtonsControl");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Sets the default styles if they haven't been explicitly set.
        /// </summary>
        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();

            try
            {
                // Set default styles if not already set
                if (SaveButtonStyle == null)
                {
                    SaveButtonStyle = (Style)Application.Current.FindResource("PrimaryButtonStyle");
                }

                if (CancelButtonStyle == null)
                {
                    CancelButtonStyle = (Style)Application.Current.FindResource("SecondaryButtonStyle");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying template: {ex.Message}", "SaveCancelButtonsControl");
            }
        }

        #endregion
    }
}
