# UFU2 Project Structure

## Root Level Files
- **UFU2.sln** - Visual Studio solution file
- **UFU2.csproj** - Main project file with dependencies and build configuration
- **App.xaml/App.xaml.cs** - Application entry point and global resources
- **MainWindow.xaml/MainWindow.xaml.cs** - Main application window
- **AssemblyInfo.cs** - Assembly metadata

## Core Architecture Folders

### `/ViewModels`
MVVM ViewModels inheriting from BaseViewModel
- **BaseViewModel.cs** - Base class with INotifyPropertyChanged implementation
- Dialog ViewModels: AddActivityDialogViewModel, AddNotesDialogViewModel, etc.
- Main ViewModels: NewClientViewModel, ImageManagementViewModel, etc.

### `/Views`
WPF Views and UserControls
- **NewClientView.xaml/cs** - Main client registration view
- `/Dialogs` - Modal dialog views
- `/UserControls` - Reusable UI components
- `/NewClient` - Client-specific views

### `/Models`
Data models with INotifyPropertyChanged
- **ActivityModel.cs** - Business activity data
- **NoteModel.cs**, **PhoneNumberModel.cs** - Supporting data models
- Collection models for managing lists of data

### `/Services`
Business logic and data access services
- **DatabaseService.cs** - SQLite database operations
- **ServiceLocator.cs** - Dependency injection container
- **ThemeManager.cs** - UI theme management
- **ActivityTypeBaseService.cs** - Activity type data management

### `/Commands`
MVVM command implementations
- **RelayCommand.cs** - Generic command implementation with logging

### `/Common`
Shared utilities and infrastructure
- **LoggingService.cs** - Application logging with session management
- **ErrorManager.cs** - Error handling utilities
- `/Converters` - WPF value converters
- `/Extensions` - Extension methods
- `/Utilities` - Helper classes

## UI and Resources

### `/Resources`
Application assets and styling
- `/Styles` - XAML style definitions (Typography, Buttons, Cards, etc.)
- `/Themes` - Light/Dark theme definitions
- `/Tokens` - Design system tokens (spacing, colors)
- `/Font` - Custom fonts
- Images: logo.ico, logo.png, default profile images

### `/Converters`
WPF-specific value converters
- **GenderToImageConverter.cs** - Gender to profile image conversion
- Activity-related converters for UI binding

### `/Windows`
Additional application windows
- **ConfirmationWindow.xaml/cs** - Confirmation dialogs

## Data and Configuration

### `/Database`
Data files and schemas
- **activity_Type.json** - Algerian business activity codes (4000+ entries)

### `/Helpers`
Utility classes
- **CoordinateTransformHelper.cs** - Geographic coordinate transformations

## Build Artifacts
- `/bin` - Compiled binaries
- `/obj` - Build intermediate files
- `/.vs` - Visual Studio settings

## Naming Conventions
- **ViewModels**: End with "ViewModel" (e.g., NewClientViewModel)
- **Services**: End with "Service" (e.g., DatabaseService)
- **Models**: End with "Model" (e.g., ActivityModel)
- **Commands**: Use descriptive names with "Command" suffix
- **Converters**: End with "Converter" (e.g., GenderToImageConverter)

## File Organization Rules
- One class per file with matching filename
- XAML views paired with code-behind (.xaml.cs)
- Related functionality grouped in appropriate folders
- Shared utilities in `/Common` folder
- UI-specific code in `/Views`, `/Converters`, `/Resources`