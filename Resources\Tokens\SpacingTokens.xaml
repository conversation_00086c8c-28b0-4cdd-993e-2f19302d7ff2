<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!--
        ========================================
        UFU2 Centralized Spacing Token System
        ========================================
    -->

    <!--
        This ResourceDictionary contains all spacing tokens for the UFU2_0 application.
        Spacing tokens follow Material Design 3 spacing scale and provide consistent
        layout spacing throughout the application.
    -->

    <!--
        ========================================
        MATERIAL DESIGN 3 SPACING SCALE
        ========================================
    -->

    <!--  Base Spacing Units (4px grid system)  -->
    <system:Double x:Key="SpacingXXS">2</system:Double>
    <!--  2px  -->
    <system:Double x:Key="SpacingXS">4</system:Double>
    <!--  4px  -->
    <system:Double x:Key="SpacingS">8</system:Double>
    <!--  8px  -->
    <system:Double x:Key="SpacingM">12</system:Double>
    <!--  12px  -->
    <system:Double x:Key="SpacingL">16</system:Double>
    <!--  16px  -->
    <system:Double x:Key="SpacingXL">20</system:Double>
    <!--  20px  -->
    <system:Double x:Key="SpacingXXL">24</system:Double>
    <!--  24px  -->
    <system:Double x:Key="SpacingXXXL">32</system:Double>
    <!--  32px  -->
    <system:Double x:Key="SpacingHuge">40</system:Double>
    <!--  40px  -->
    <system:Double x:Key="SpacingMassive">48</system:Double>
    <!--  48px  -->

    <!--
        ========================================
        SEMANTIC SPACING TOKENS
        ========================================
    -->

    <!--  Component Internal Spacing  -->
    <system:Double x:Key="ComponentPaddingSmall">8</system:Double>
    <system:Double x:Key="ComponentPaddingMedium">16</system:Double>
    <system:Double x:Key="ComponentPaddingLarge">24</system:Double>
    <system:Double x:Key="ComponentPaddingXLarge">32</system:Double>

    <!--  Layout Spacing  -->
    <system:Double x:Key="LayoutSpacingTight">8</system:Double>
    <system:Double x:Key="LayoutSpacingNormal">16</system:Double>
    <system:Double x:Key="LayoutSpacingRelaxed">24</system:Double>
    <system:Double x:Key="LayoutSpacingLoose">32</system:Double>

    <!--  Content Spacing  -->
    <system:Double x:Key="ContentSpacingSmall">12</system:Double>
    <system:Double x:Key="ContentSpacingMedium">16</system:Double>
    <system:Double x:Key="ContentSpacingLarge">24</system:Double>

    <!--  Section Spacing  -->
    <system:Double x:Key="SectionSpacingSmall">16</system:Double>
    <system:Double x:Key="SectionSpacingMedium">24</system:Double>
    <system:Double x:Key="SectionSpacingLarge">32</system:Double>
    <system:Double x:Key="SectionSpacingXLarge">48</system:Double>

    <!--
        ========================================
        THICKNESS TOKENS
        ========================================
    -->

    <!--  Margin Thickness Values  -->
    <Thickness x:Key="MarginXS">4</Thickness>
    <Thickness x:Key="MarginS">8</Thickness>
    <Thickness x:Key="MarginM">12</Thickness>
    <Thickness x:Key="MarginL">16</Thickness>
    <Thickness x:Key="MarginXL">20</Thickness>
    <Thickness x:Key="MarginXXL">24</Thickness>
    <Thickness x:Key="MarginXXXL">32</Thickness>

    <!--  Padding Thickness Values  -->
    <Thickness x:Key="PaddingXS">4</Thickness>
    <Thickness x:Key="PaddingS">8</Thickness>
    <Thickness x:Key="PaddingM">12</Thickness>
    <Thickness x:Key="PaddingL">16</Thickness>
    <Thickness x:Key="PaddingXL">20</Thickness>
    <Thickness x:Key="PaddingXXL">24</Thickness>
    <Thickness x:Key="PaddingXXXL">32</Thickness>

    <!--  Component-Specific Thickness  -->
    <Thickness x:Key="ButtonPadding">16,8</Thickness>
    <Thickness x:Key="CardPadding">16</Thickness>
    <Thickness x:Key="CardPaddingLarge">24</Thickness>
    <Thickness x:Key="DialogPadding">24</Thickness>
    <Thickness x:Key="InputPadding">12,8</Thickness>
    <Thickness x:Key="ListItemPadding">16,12</Thickness>

    <!--
        ========================================
        BORDER RADIUS TOKENS
        ========================================
    -->

    <!--  Corner Radius Values  -->
    <system:Double x:Key="BorderRadiusNone">0</system:Double>
    <system:Double x:Key="BorderRadiusSmall">4</system:Double>
    <system:Double x:Key="BorderRadiusMedium">8</system:Double>
    <system:Double x:Key="BorderRadiusLarge">12</system:Double>
    <system:Double x:Key="BorderRadiusXLarge">16</system:Double>
    <system:Double x:Key="BorderRadiusRound">20</system:Double>
    <system:Double x:Key="BorderRadiusCircle">50</system:Double>

    <!--
        ========================================
        LEGACY SPACING VALUES (for backward compatibility)
        ========================================
    -->

    <!--  Common Legacy Spacing Values  -->
    <system:Double x:Key="Spacing0">0</system:Double>
    <system:Double x:Key="Spacing1">1</system:Double>
    <system:Double x:Key="Spacing2">2</system:Double>
    <system:Double x:Key="Spacing4">4</system:Double>
    <system:Double x:Key="Spacing5">5</system:Double>
    <system:Double x:Key="Spacing6">6</system:Double>
    <system:Double x:Key="Spacing8">8</system:Double>
    <system:Double x:Key="Spacing10">10</system:Double>
    <system:Double x:Key="Spacing12">12</system:Double>
    <system:Double x:Key="Spacing15">15</system:Double>
    <system:Double x:Key="Spacing16">16</system:Double>
    <system:Double x:Key="Spacing18">18</system:Double>
    <system:Double x:Key="Spacing20">20</system:Double>
    <system:Double x:Key="Spacing24">24</system:Double>
    <system:Double x:Key="Spacing25">25</system:Double>
    <system:Double x:Key="Spacing30">30</system:Double>
    <system:Double x:Key="Spacing32">32</system:Double>
    <system:Double x:Key="Spacing40">40</system:Double>
    <system:Double x:Key="Spacing48">48</system:Double>
    <system:Double x:Key="Spacing50">50</system:Double>
    <system:Double x:Key="Spacing60">60</system:Double>
    <system:Double x:Key="Spacing80">80</system:Double>

    <!--  Legacy Thickness Values  -->
    <Thickness x:Key="Margin0">0</Thickness>
    <Thickness x:Key="Margin5">5</Thickness>
    <Thickness x:Key="Margin10">10</Thickness>
    <Thickness x:Key="Margin15">15</Thickness>
    <Thickness x:Key="Margin20">20</Thickness>
    <Thickness x:Key="Margin25">25</Thickness>
    <Thickness x:Key="Margin30">30</Thickness>

    <Thickness x:Key="Padding0">0</Thickness>
    <Thickness x:Key="Padding5">5</Thickness>
    <Thickness x:Key="Padding10">10</Thickness>
    <Thickness x:Key="Padding15">15</Thickness>
    <Thickness x:Key="Padding20">20</Thickness>
    <Thickness x:Key="Padding25">25</Thickness>
    <Thickness x:Key="Padding30">30</Thickness>

</ResourceDictionary>
