using System;
using System.Collections.Generic;

namespace UFU2.Common.Models
{
    /// <summary>
    /// Represents the context of an error-prone operation for deduplication and correlation tracking.
    /// Used by the ErrorDeduplicationManager to prevent cascading error toasts while maintaining
    /// complete technical logging for debugging purposes.
    /// </summary>
    public class ErrorOperationContext
    {
        #region Properties

        /// <summary>
        /// Unique identifier for this operation instance.
        /// Generated using GUID to ensure uniqueness across concurrent operations.
        /// </summary>
        public string OperationId { get; set; } = string.Empty;

        /// <summary>
        /// The root operation type that initiated this context.
        /// Examples: "ClientCreation", "ActivityCreation", "ImageProcessing"
        /// </summary>
        public string RootOperation { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the operation started.
        /// Used for operation timeout and cleanup purposes.
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// The source component that initiated the root operation.
        /// Examples: "NewClientViewModel", "ImageManagementViewModel"
        /// </summary>
        public string InitiatingSource { get; set; } = string.Empty;

        /// <summary>
        /// Additional context properties for debugging and correlation.
        /// Can include operation-specific data like client names, file paths, etc.
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Indicates whether an error toast has already been displayed for this operation.
        /// Prevents multiple toasts from being shown for the same operation failure.
        /// </summary>
        public bool HasDisplayedToast { get; set; } = false;

        /// <summary>
        /// The severity level of the error toast that was displayed (if any).
        /// Used to determine if a higher severity error should override the displayed toast.
        /// </summary>
        public ErrorSeverity DisplayedSeverity { get; set; } = ErrorSeverity.None;

        /// <summary>
        /// The source component that displayed the error toast.
        /// Used for debugging and correlation purposes.
        /// </summary>
        public string DisplayedSource { get; set; } = string.Empty;

        /// <summary>
        /// The user-friendly message that was displayed in the toast.
        /// Stored for potential toast replacement scenarios.
        /// </summary>
        public string DisplayedMessage { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the error toast was displayed.
        /// Used for timing analysis and debugging.
        /// </summary>
        public DateTime? ToastDisplayTime { get; set; }

        /// <summary>
        /// List of all errors that occurred during this operation.
        /// Maintains complete error history for debugging while showing only one toast.
        /// </summary>
        public List<ErrorRecord> ErrorHistory { get; set; } = new List<ErrorRecord>();

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ErrorOperationContext class.
        /// </summary>
        /// <param name="operationId">Unique operation identifier</param>
        /// <param name="rootOperation">Root operation type</param>
        /// <param name="initiatingSource">Source component that started the operation</param>
        public ErrorOperationContext(string operationId, string rootOperation, string initiatingSource)
        {
            OperationId = operationId ?? throw new ArgumentNullException(nameof(operationId));
            RootOperation = rootOperation ?? throw new ArgumentNullException(nameof(rootOperation));
            InitiatingSource = initiatingSource ?? throw new ArgumentNullException(nameof(initiatingSource));
            StartTime = DateTime.Now;
        }

        /// <summary>
        /// Default constructor for serialization purposes.
        /// </summary>
        public ErrorOperationContext()
        {
            StartTime = DateTime.Now;
        }

        #endregion

        #region Methods

        /// <summary>
        /// Adds an error record to the operation's error history.
        /// </summary>
        /// <param name="exception">The exception that occurred</param>
        /// <param name="source">The source component where the error occurred</param>
        /// <param name="severity">The severity level of the error</param>
        /// <param name="userMessage">The user-friendly message (if any)</param>
        public void AddError(Exception exception, string source, ErrorSeverity severity, string userMessage = "")
        {
            var errorRecord = new ErrorRecord
            {
                Exception = exception,
                Source = source,
                Severity = severity,
                UserMessage = userMessage,
                Timestamp = DateTime.Now
            };

            ErrorHistory.Add(errorRecord);
        }

        /// <summary>
        /// Marks that an error toast has been displayed for this operation.
        /// </summary>
        /// <param name="severity">The severity of the displayed error</param>
        /// <param name="source">The source that displayed the toast</param>
        /// <param name="message">The message that was displayed</param>
        public void MarkToastDisplayed(ErrorSeverity severity, string source, string message)
        {
            HasDisplayedToast = true;
            DisplayedSeverity = severity;
            DisplayedSource = source;
            DisplayedMessage = message;
            ToastDisplayTime = DateTime.Now;
        }

        /// <summary>
        /// Determines if this operation has expired based on the configured timeout.
        /// </summary>
        /// <param name="timeoutSeconds">Timeout in seconds (default: 30)</param>
        /// <returns>True if the operation has expired</returns>
        public bool IsExpired(int timeoutSeconds = 30)
        {
            return DateTime.Now.Subtract(StartTime).TotalSeconds > timeoutSeconds;
        }

        /// <summary>
        /// Gets the duration of this operation in milliseconds.
        /// </summary>
        /// <returns>Operation duration in milliseconds</returns>
        public double GetDurationMs()
        {
            return DateTime.Now.Subtract(StartTime).TotalMilliseconds;
        }

        /// <summary>
        /// Gets a summary of this operation for logging purposes.
        /// </summary>
        /// <returns>Operation summary string</returns>
        public string GetSummary()
        {
            return $"Operation: {RootOperation}, ID: {OperationId}, Source: {InitiatingSource}, " +
                   $"Duration: {GetDurationMs():F0}ms, Errors: {ErrorHistory.Count}, " +
                   $"ToastDisplayed: {HasDisplayedToast}";
        }

        #endregion
    }

    /// <summary>
    /// Represents an individual error that occurred during an operation.
    /// </summary>
    public class ErrorRecord
    {
        /// <summary>
        /// The exception that occurred.
        /// </summary>
        public Exception Exception { get; set; } = null!;

        /// <summary>
        /// The source component where the error occurred.
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// The severity level of this error.
        /// </summary>
        public ErrorSeverity Severity { get; set; }

        /// <summary>
        /// The user-friendly message associated with this error.
        /// </summary>
        public string UserMessage { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when this error occurred.
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Defines the severity levels for error classification and prioritization.
    /// Higher values indicate more severe errors that should take priority in display.
    /// </summary>
    public enum ErrorSeverity
    {
        /// <summary>
        /// No error or unspecified severity.
        /// </summary>
        None = 0,

        /// <summary>
        /// Low severity - General operation failures, UI validation issues.
        /// </summary>
        Low = 1,

        /// <summary>
        /// Medium severity - Service layer business logic failures.
        /// </summary>
        Medium = 2,

        /// <summary>
        /// High severity - Data layer failures, UID generation, database operations.
        /// </summary>
        High = 3,

        /// <summary>
        /// Critical severity - System/infrastructure failures, database connection issues.
        /// </summary>
        Critical = 4
    }
}
