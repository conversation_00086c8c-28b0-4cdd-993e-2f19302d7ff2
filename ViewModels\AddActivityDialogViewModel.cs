using System;
using System.ComponentModel;
using System.Threading.Tasks;
using UFU2.Models;
using UFU2.Services;
using UFU2.Common;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for AddActivityDialog.
    /// <PERSON><PERSON> adding new activity codes to the ActivityTypeBase database.
    /// Follows UFU2 MVVM patterns with validation and database integration.
    ///
    /// BACKUP CREATED: AddActivityDialogViewModel.cs.backup - Original implementation before BaseViewModel refactoring
    /// REFACTORING STATUS: ✅ COMPLETED - All tasks 1.1.1-1.1.10 successfully implemented
    /// </summary>
    public class AddActivityDialogViewModel : BaseViewModel, IDataErrorInfo
    {
        #region Private Fields
        private readonly ActivityTypeBaseService? _activityTypeService;
        private string _activityCode = string.Empty;
        private string _activityDescription = string.Empty;
        private bool _isSaving = false;
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the AddActivityDialogViewModel class.
        /// </summary>
        /// <param name="activityCode">The activity code that was not found</param>
        public AddActivityDialogViewModel(string activityCode)
        {
            ActivityCode = activityCode ?? string.Empty;

            // Get ActivityTypeBaseService from ServiceLocator
            if (ServiceLocator.TryGetService<ActivityTypeBaseService>(out var service))
            {
                _activityTypeService = service;
            }
            else
            {
                LoggingService.LogWarning("ActivityTypeBaseService not available in AddActivityDialogViewModel", GetType().Name);
            }

            // Initialize BaseViewModel
            OnInitialize();

            LoggingService.LogInfo($"AddActivityDialogViewModel initialized for code: {ActivityCode}", GetType().Name);
        }
        #endregion

        #region Public Properties
        /// <summary>
        /// Gets the activity code that was not found in the database.
        /// </summary>
        public string ActivityCode
        {
            get => _activityCode;
            private set => SetProperty(ref _activityCode, value);
        }

        /// <summary>
        /// Gets or sets the activity description to be saved.
        /// </summary>
        public string ActivityDescription
        {
            get => _activityDescription;
            set
            {
                if (SetProperty(ref _activityDescription, value))
                {
                    OnPropertyChanged(nameof(CanSave));
                }
            }
        }

        /// <summary>
        /// Gets a value indicating whether the activity can be saved.
        /// </summary>
        public bool CanSave => !string.IsNullOrWhiteSpace(ActivityDescription) && 
                               !string.IsNullOrWhiteSpace(ActivityCode) && 
                               !_isSaving &&
                               string.IsNullOrEmpty(this[nameof(ActivityDescription)]);

        /// <summary>
        /// Gets a value indicating whether a save operation is in progress.
        /// </summary>
        public bool IsSaving
        {
            get => _isSaving;
            private set
            {
                if (SetProperty(ref _isSaving, value))
                {
                    OnPropertyChanged(nameof(CanSave));
                }
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Saves the new activity to the database.
        /// </summary>
        /// <returns>True if save was successful, false otherwise</returns>
        public async Task<bool> SaveActivityAsync()
        {
            if (!CanSave || _activityTypeService == null)
            {
                LoggingService.LogWarning("Cannot save activity: validation failed or service unavailable", GetType().Name);
                return false;
            }

            try
            {
                IsSaving = true;

                var activityType = new ActivityTypeBaseModel
                {
                    Code = ActivityCode,
                    Description = ActivityDescription.Trim()
                };

                var success = await _activityTypeService.InsertAsync(activityType);

                if (success)
                {
                    LoggingService.LogInfo($"Successfully saved new activity: {ActivityCode} - {ActivityDescription}", GetType().Name);
                }
                else
                {
                    LoggingService.LogError($"Failed to save activity: {ActivityCode}", GetType().Name);
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error saving activity {ActivityCode}: {ex.Message}", GetType().Name);
                return false;
            }
            finally
            {
                IsSaving = false;
            }
        }
        #endregion

        #region IDataErrorInfo Implementation
        /// <summary>
        /// Gets an error message indicating what is wrong with this object.
        /// </summary>
        public string Error => string.Empty;

        /// <summary>
        /// Gets the error message for the property with the given name.
        /// </summary>
        /// <param name="columnName">The name of the property whose error message to get</param>
        /// <returns>The error message for the property</returns>
        public string this[string columnName]
        {
            get
            {
                switch (columnName)
                {
                    case nameof(ActivityDescription):
                        if (string.IsNullOrWhiteSpace(ActivityDescription))
                            return "وصف النشاط مطلوب";
                        if (ActivityDescription.Length > 500)
                            return "وصف النشاط يجب أن يكون أقل من 500 حرف";
                        break;
                }
                return string.Empty;
            }
        }
        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes resources including cleanup of ActivityTypeBaseService reference.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // Note: _activityTypeService is readonly and managed by ServiceLocator
                    // No explicit disposal needed as ServiceLocator handles service lifecycle
                    // This method ensures proper disposal chain and logging

                    LoggingService.LogInfo("AddActivityDialogViewModel disposed", GetType().Name);
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error disposing AddActivityDialogViewModel: {ex.Message}", GetType().Name);
                }
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
