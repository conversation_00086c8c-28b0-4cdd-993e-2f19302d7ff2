using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using UFU2.Common.Converters;
using UFU2.Common.Extensions;
using UFU2.Models;
using UFU2.Views.Dialogs;
using MaterialDesignThemes.Wpf;
using UFU2.Common;
using UFU2.ViewModels;
using UFU2.Services;

namespace UFU2.Views.NewClient
{
    /// <summary>
    /// Interaction logic for NPersonalView.xaml
    /// A UserControl that provides a form for collecting personal information.
    /// UI PROTOTYPE VERSION - Removed all backend dependencies for design purposes.
    /// Integrates with UFU2 design system and follows MaterialDesign patterns.
    /// Supports Arabic RTL layout and proper accessibility features.
    /// Includes text formatting for phone numbers, dates, and uppercase names.
    /// </summary>
    public partial class NPersonalView : UserControl
    {
        #region Private Fields

        /// <summary>
        /// Phone numbers collection for managing multiple phone numbers.
        /// This collection persists data between dialog sessions.
        /// </summary>
        private PhoneNumbersCollectionModel _phoneNumbers = new PhoneNumbersCollectionModel();

        /// <summary>
        /// Flag to prevent circular synchronization between TextBox and collection.
        /// When true, synchronization events are ignored to prevent infinite loops.
        /// </summary>
        private bool _isSynchronizing = false;

        /// <summary>
        /// ViewModel for this view that provides validation and data binding.
        /// </summary>
        private NPersonalViewModel _viewModel = new NPersonalViewModel();

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the phone numbers collection.
        /// Used by the parent ViewModel to access phone number data.
        /// </summary>
        public PhoneNumbersCollectionModel PhoneNumbers => _phoneNumbers;

        /// <summary>
        /// Gets the ViewModel for this view.
        /// </summary>
        public NPersonalViewModel ViewModel => _viewModel;

        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the NPersonalView class.
        /// UI PROTOTYPE VERSION - Sets up formatting without backend dependencies.
        /// Configures real-time uppercase formatting for the NameFrTextBox control.
        /// Configures automatic date formatting for the BirthDateTextBox control.
        /// Configures phone number formatting for the PhoneNumberTextBox control.
        /// </summary>
        public NPersonalView()
        {
            InitializeComponent();

            // Set up the ViewModel and data binding
            DataContext = _viewModel;
            _viewModel.PhoneNumbersCollection = _phoneNumbers;

            // Attach Latin character validation with uppercase formatting to the NameFrTextBox
            // This restricts input to Latin characters only and provides immediate visual feedback
            TextBoxExtensions.AttachLatinCharacterValidation(NameFrTextBox);

            // Attach automatic date formatting to the BirthDateTextBox
            // This provides DD/MM/YYYY formatting with real-time and focus-loss triggers
            TextBoxExtensions.AttachDateFormatting(BirthDateTextBox);

            // Attach UFU2 phone number formatting to the PhoneNumberTextBox
            // This provides automatic formatting for 9-digit (XXX-XX-XX-XX) and 10-digit (XXXX-XX-XX-XX) numbers
            TextBoxExtensions.AttachPhoneNumberFormatting(PhoneNumberTextBox);

            // Subscribe to phone numbers collection changes to update the main TextBox
            _phoneNumbers.PropertyChanged += PhoneNumbers_PropertyChanged;
        }
        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the Click event for the AddPhoneNumbersButton to open the phone numbers management dialog.
        /// Opens a modal dialog for managing multiple phone numbers with bidirectional data synchronization.
        /// Validates phone number using ValidationService and follows UFU2 UX pattern for invalid input handling.
        /// For invalid input: clears the field and proceeds with dialog opening (does not block user workflow).
        /// </summary>
        private async void AddPhoneNumbersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoggingService.LogInfo("Opening phone numbers dialog", "NPersonalView");

                // Check if current phone number has content and validate it using ValidationService
                var currentPhoneNumber = _viewModel.PhoneNumber?.Trim();
                if (!string.IsNullOrEmpty(currentPhoneNumber))
                {
                    // Get ValidationService through ServiceLocator
                    var validationService = ServiceLocator.GetService<ValidationService>();
                    var validationError = validationService?.ValidatePhoneNumber(currentPhoneNumber);

                    if (!string.IsNullOrEmpty(validationError))
                    {
                        // Invalid phone number detected - follow UFU2 UX pattern
                        LoggingService.LogWarning($"Invalid phone number detected (less than 9 digits): {currentPhoneNumber}", "NPersonalView");

                        // Clear the invalid phone number from TextBox and ViewModel (UFU2 UX pattern)
                        PhoneNumberTextBox.Text = string.Empty;
                        _viewModel.PhoneNumber = string.Empty;

                        // Force sync to collection to ensure invalid phone number is removed
                        _viewModel.RefreshToCollection();

                        LoggingService.LogInfo("Invalid phone number cleared from TextBox, ViewModel, and collection - proceeding with dialog opening", "NPersonalView");

                        // Continue with dialog opening (do not block user workflow)
                    }
                    else
                    {
                        // Valid phone number - sync it to collection
                        SyncPhoneNumberToCollection();
                        LoggingService.LogInfo("Valid phone number synced to collection before opening dialog", "NPersonalView");
                    }
                }

                // Create the phone numbers dialog with current data
                var phoneDialog = new PhoneNumbersDialog(_phoneNumbers);

                // Show the dialog using the nested DialogHost
                var result = await DialogHost.Show(phoneDialog, "NewClientDialogHost");

                // Handle dialog result with immediate synchronization for dialog operations
                if (result is bool dialogResult && dialogResult)
                {
                    LoggingService.LogInfo("Phone numbers dialog saved - applying changes", "NPersonalView");

                    // Get dialog data and sync back to our collection immediately
                    // Dialog operations require immediate sync, not async for data consistency
                    var dialogPhoneNumbers = phoneDialog.GetPhoneNumbers();
                    SyncDialogDataToCollection(dialogPhoneNumbers);

                    // Force immediate sync from collection to ViewModel (bypass debouncing)
                    _viewModel.RefreshFromCollection();

                    // Update the main TextBox with the primary phone number
                    UpdateTextBoxFromCollection();

                    LoggingService.LogInfo($"Phone numbers updated successfully. Count: {_phoneNumbers.Count}", "NPersonalView");
                }
                else
                {
                    LoggingService.LogInfo("Phone numbers dialog cancelled - no changes made", "NPersonalView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in phone numbers dialog workflow: {ex.Message}", "NPersonalView");
            }
        }

        /// <summary>
        /// Handles property changes in the phone numbers collection.
        /// </summary>
        private void PhoneNumbers_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PhoneNumbersCollectionModel.PrimaryPhoneNumber) && !_isSynchronizing)
            {
                SyncCollectionToPhoneNumber();
            }
        }

        /// <summary>
        /// Handles the LostFocus event for the PhoneNumberTextBox to format the phone number.
        /// Applies UFU2 phone number formatting (XXXX-XX-XX-XX for 10 digits, XXX-XX-XX-XX for 9 digits).
        /// Also syncs the phone number to the collection.
        /// </summary>
        private void PhoneNumberTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                try
                {
                    // Use the PhoneNumberConverter to format the phone number
                    var converter = new PhoneNumberConverter();
                    var formattedNumber = converter.Convert(textBox.Text, typeof(string), null, System.Globalization.CultureInfo.CurrentCulture);

                    if (formattedNumber != null)
                    {
                        textBox.Text = formattedNumber.ToString();
                    }
                }
                catch (Exception)
                {
                    // Silently handle formatting errors in UI prototype
                    // In production, this would use proper error logging
                }
            }

            // Sync the formatted phone number to the collection
            SyncPhoneNumberToCollection();
        }

        /// <summary>
        /// Handles the LostFocus event for the NameFrTextBox to trigger duplicate client detection.
        /// Checks for existing clients with the same French name and shows duplicate detection dialog if found.
        /// </summary>
        private async void NameFrTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (sender is not TextBox textBox || string.IsNullOrWhiteSpace(textBox.Text))
            {
                return;
            }

            try
            {
                var nameFr = textBox.Text.Trim();
                LoggingService.LogDebug($"Checking for duplicate clients with NameFr: '{nameFr}'", "NPersonalView");

                // Get the duplicate detection service
                var duplicateDetectionService = ServiceLocator.GetService<DuplicateClientDetectionService>();
                if (duplicateDetectionService == null)
                {
                    LoggingService.LogWarning("DuplicateClientDetectionService not found in ServiceLocator", "NPersonalView");
                    return;
                }

                // Search for duplicate clients
                var duplicateClients = await duplicateDetectionService.FindDuplicateClientsAsync(nameFr);

                if (duplicateClients?.Any() == true)
                {
                    LoggingService.LogInfo($"Found {duplicateClients.Count} duplicate clients for '{nameFr}'", "NPersonalView");
                    await ShowDuplicateClientDetectionDialogAsync(nameFr, duplicateClients);
                }
                else
                {
                    LoggingService.LogDebug($"No duplicate clients found for '{nameFr}'", "NPersonalView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking for duplicate clients: {ex.Message}", "NPersonalView");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء البحث عن العملاء المكررين",
                    "خطأ في البحث",
                    LogLevel.Error,
                    "NPersonalView");
            }
        }

        /// <summary>
        /// Shows the duplicate client detection dialog and handles user selection.
        /// </summary>
        /// <param name="nameFr">The French name that was searched</param>
        /// <param name="duplicateClients">The list of duplicate clients found</param>
        private async Task ShowDuplicateClientDetectionDialogAsync(string nameFr, List<DuplicateClientData> duplicateClients)
        {
            try
            {
                // Create the duplicate client detection dialog
                var dialog = DuplicateClientDetectionDialog.Create(nameFr, duplicateClients);

                // Show the dialog using the nested DialogHost
                var result = await DialogHost.Show(dialog, "NewClientDialogHost");

                // Handle the dialog result
                if (dialog.DialogResult == true && dialog.SelectedClientData != null)
                {
                    LoggingService.LogInfo($"User selected existing client: {dialog.SelectedClientData.ClientUid}", "NPersonalView");
                    await LoadSelectedClientDataAsync(dialog.SelectedClientData);
                }
                else
                {
                    LoggingService.LogInfo("User chose to create new client", "NPersonalView");
                    // Continue with new client creation - no action needed
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing duplicate client detection dialog: {ex.Message}", "NPersonalView");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء عرض نافذة اختيار العميل",
                    "خطأ في العرض",
                    LogLevel.Error,
                    "NPersonalView");
            }
        }

        /// <summary>
        /// Loads the selected client's data into the NewClientView form.
        /// </summary>
        /// <param name="selectedClient">The selected duplicate client data</param>
        private async Task LoadSelectedClientDataAsync(DuplicateClientData selectedClient)
        {
            try
            {
                LoggingService.LogInfo($"Loading client data for: {selectedClient.ClientUid}", "NPersonalView");

                // Get the ViewModel to update the form fields
                if (DataContext is not NPersonalViewModel viewModel)
                {
                    LoggingService.LogWarning("NPersonalViewModel not found in DataContext", "NPersonalView");
                    return;
                }

                // Load specific client fields as required
                viewModel.NameAr = selectedClient.NameAr;
                viewModel.BirthDate = selectedClient.BirthDate;
                viewModel.BirthPlace = selectedClient.BirthPlace;
                viewModel.Gender = selectedClient.Gender;
                viewModel.Address = selectedClient.Address;
                viewModel.NationalId = selectedClient.NationalId;

                // Load primary phone number to the main phone field
                var primaryPhone = selectedClient.PrimaryPhoneNumber;
                if (!string.IsNullOrWhiteSpace(primaryPhone))
                {
                    viewModel.PhoneNumber = primaryPhone;
                }

                // Load ALL phone numbers into the PhoneNumbersDialog for comprehensive management
                if (selectedClient.PhoneNumbers?.Any() == true)
                {
                    await LoadAllPhoneNumbersAsync(selectedClient.PhoneNumbers);
                }

                // Set the editing state in the main NewClientViewModel
                var newClientViewModel = GetNewClientViewModelFromParent();
                if (newClientViewModel != null)
                {
                    newClientViewModel.SetEditingExistingClient(selectedClient);
                    LoggingService.LogInfo($"Set editing state for existing client in NewClientViewModel: {selectedClient.ClientUid}", "NPersonalView");
                }
                else
                {
                    LoggingService.LogWarning("Could not find NewClientViewModel to set editing state", "NPersonalView");
                }

                LoggingService.LogInfo($"Successfully loaded client data for: {selectedClient.ClientUid}", "NPersonalView");

                // Show success message
                ErrorManager.ShowUserSuccessToast(
                    $"تم تحميل بيانات العميل بنجاح\nمعرف العميل: {selectedClient.ClientUid}",
                    "تم التحميل",
                    "NPersonalView");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading selected client data: {ex.Message}", "NPersonalView");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تحميل بيانات العميل المحدد",
                    "خطأ في التحميل",
                    LogLevel.Error,
                    "NPersonalView");
            }
        }

        /// <summary>
        /// Loads all phone numbers from the selected client into the phone numbers collection.
        /// </summary>
        /// <param name="phoneNumbers">The phone numbers to load</param>
        private async Task LoadAllPhoneNumbersAsync(List<PhoneNumberData> phoneNumbers)
        {
            try
            {
                // Clear existing phone numbers
                _phoneNumbers.PhoneNumbers.Clear();

                // Add all phone numbers from the selected client
                foreach (var phoneData in phoneNumbers)
                {
                    var phoneModel = new PhoneNumberModel(phoneData.PhoneNumber, ConvertStringToPhoneType(phoneData.PhoneType))
                    {
                        IsPrimary = phoneData.IsPrimary
                    };

                    _phoneNumbers.PhoneNumbers.Add(phoneModel);
                }

                // Set the primary phone number if available
                var primaryPhone = phoneNumbers.FirstOrDefault(p => p.IsPrimary);
                if (primaryPhone != null)
                {
                    _phoneNumbers.SetPrimaryPhoneNumber(primaryPhone.PhoneNumber);
                }

                LoggingService.LogInfo($"Loaded {phoneNumbers.Count} phone numbers into collection", "NPersonalView");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading phone numbers: {ex.Message}", "NPersonalView");
                throw;
            }
        }

        /// <summary>
        /// Converts string phone type to PhoneType enum.
        /// </summary>
        /// <param name="phoneTypeString">The phone type as string</param>
        /// <returns>The corresponding PhoneType enum value</returns>
        private static PhoneType ConvertStringToPhoneType(string phoneTypeString)
        {
            return phoneTypeString switch
            {
                "Mobile" => PhoneType.Mobile,
                "Home" => PhoneType.Home,
                "Work" => PhoneType.Work,
                "Fax" => PhoneType.Fax,
                "Other" => PhoneType.Fax, // Map "Other" to "Fax" as fallback
                _ => PhoneType.Mobile
            };
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Synchronizes the phone number from the ViewModel to the phone numbers collection.
        /// </summary>
        public void SyncPhoneNumberToCollection()
        {
            if (_isSynchronizing)
                return;

            try
            {
                _isSynchronizing = true;
                _viewModel.RefreshToCollection();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error syncing phone number to collection: {ex.Message}", "NPersonalView");
            }
            finally
            {
                _isSynchronizing = false;
            }
        }

        /// <summary>
        /// Synchronizes the primary phone number from the collection to the ViewModel.
        /// </summary>
        private void SyncCollectionToPhoneNumber()
        {
            if (_isSynchronizing)
                return;

            try
            {
                _isSynchronizing = true;
                _viewModel.RefreshFromCollection();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error syncing collection to phone number: {ex.Message}", "NPersonalView");
            }
            finally
            {
                _isSynchronizing = false;
            }
        }

        /// <summary>
        /// Synchronizes phone numbers from dialog collection to the main collection.
        /// </summary>
        /// <param name="dialogCollection">The collection from the dialog</param>
        private void SyncDialogDataToCollection(PhoneNumbersCollectionModel dialogCollection)
        {
            if (_isSynchronizing)
                return;

            try
            {
                _isSynchronizing = true;

                _phoneNumbers.Clear();
                foreach (var phone in dialogCollection.PhoneNumbers)
                {
                    var clonedPhone = phone.Clone();
                    _phoneNumbers.AddPhoneNumber(clonedPhone);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error syncing dialog data to collection: {ex.Message}", "NPersonalView");
            }
            finally
            {
                _isSynchronizing = false;
            }
        }

        /// <summary>
        /// Forces an update of the TextBox from the collection.
        /// </summary>
        private void UpdateTextBoxFromCollection()
        {
            try
            {
                PhoneNumberTextBox.Text = _phoneNumbers.PrimaryPhoneNumber;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating TextBox from collection: {ex.Message}", "NPersonalView");
            }
        }

        /// <summary>
        /// Clears all phone number data.
        /// </summary>
        public void ClearPhoneNumbers()
        {
            try
            {
                _phoneNumbers.Clear();
                PhoneNumberTextBox.Text = string.Empty;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing phone numbers: {ex.Message}", "NPersonalView");
            }
        }

        #endregion

        #region Profile Image Event Handlers

        /// <summary>
        /// Handles the ImageEditRequested event from the ClientProfileImage control.
        /// Opens ImageManagementDialog in memory-only mode and integrates with NewClientViewModel.
        /// </summary>
        private async void ClientProfileImage_ImageEditRequested(object sender, EventArgs e)
        {
            try
            {
                LoggingService.LogDebug("Profile image edit requested", "NPersonalView");

                // Disable the profile image control during operation
                ClientProfileImage.IsEnabled = false;
                ClientProfileImage.IsLoading = true;

                // Get the NewClientViewModel from parent control's DataContext
                // NPersonalView has its own NPersonalViewModel, so we need to look at the parent
                var newClientViewModel = GetNewClientViewModelFromParent();
                if (newClientViewModel == null)
                {
                    LoggingService.LogError("NewClientViewModel not found in parent DataContext", "NPersonalView");
                    ErrorManager.ShowUserErrorToast(
                        "حدث خطأ في الوصول إلى بيانات العميل. يرجى المحاولة مرة أخرى.",
                        "خطأ في النظام"
                    );
                    return;
                }

                // Create ImageManagementDialog in memory-only mode
                var imageManagementDialog = new UFU2.Views.Dialogs.ImageManagementDialog();
                imageManagementDialog.SaveToProfileImagesDirectory = false; // Store in memory only

                LoggingService.LogInfo("Opening ImageManagementDialog in memory-only mode", "NPersonalView");

                // Show the dialog using MaterialDesign DialogHost
                var result = await MaterialDesignThemes.Wpf.DialogHost.Show(imageManagementDialog, "NewClientDialogHost");

                // Handle dialog result
                if (result is bool dialogResult && dialogResult)
                {
                    LoggingService.LogInfo("ImageManagementDialog completed successfully", "NPersonalView");

                    // Get the processed image from memory
                    var processedImage = imageManagementDialog.ProcessedImageInMemory;
                    if (processedImage != null)
                    {
                        // Determine original extension (default to jpg)
                        string originalExtension = "jpg"; // Default extension

                        // Set the profile image in the NewClientViewModel
                        newClientViewModel.SetProfileImage(processedImage, originalExtension);

                        // Update the ClientProfileImage control to display the new image
                        ClientProfileImage.UpdateProfileImage(processedImage);

                        LoggingService.LogInfo("Profile image updated successfully in NewClientViewModel", "NPersonalView");
                    }
                    else
                    {
                        LoggingService.LogWarning("No processed image available from ImageManagementDialog", "NPersonalView");
                        ErrorManager.ShowUserWarningToast(
                            "لم يتم العثور على الصورة المعالجة. يرجى المحاولة مرة أخرى.",
                            "تحذير"
                        );
                    }
                }
                else
                {
                    LoggingService.LogInfo("ImageManagementDialog was cancelled", "NPersonalView");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error handling profile image edit request: {ex.Message}", "NPersonalView");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح إدارة الصورة الشخصية. يرجى المحاولة مرة أخرى.",
                    "خطأ في إدارة الصورة",
                    LogLevel.Error,
                    "NPersonalView");
            }
            finally
            {
                // Re-enable the profile image control
                ClientProfileImage.IsEnabled = true;
                ClientProfileImage.IsLoading = false;
            }
        }

        #endregion

        #region Cleanup
        /// <summary>
        /// Handles the Unloaded event for cleanup.
        /// Unsubscribes from events and disposes resources to prevent memory leaks.
        /// </summary>
        private void NPersonalView_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Unsubscribe from phone numbers collection events
                if (_phoneNumbers != null)
                {
                    _phoneNumbers.PropertyChanged -= PhoneNumbers_PropertyChanged;
                }

                // Dispose the ViewModel to clean up timer and other resources
                _viewModel?.Dispose();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during NPersonalView cleanup: {ex.Message}", "NPersonalView");
            }
        }

        /// <summary>
        /// Gets the NewClientViewModel from the parent control's DataContext.
        /// This method traverses up the visual tree to find the NewClientView and access its DataContext.
        /// </summary>
        /// <returns>The NewClientViewModel if found, null otherwise</returns>
        private NewClientViewModel? GetNewClientViewModelFromParent()
        {
            try
            {
                // Method 1: Try to get parent through visual tree
                var parent = System.Windows.Media.VisualTreeHelper.GetParent(this);
                while (parent != null)
                {
                    if (parent is FrameworkElement frameworkElement)
                    {
                        if (frameworkElement.DataContext is NewClientViewModel viewModel)
                        {
                            LoggingService.LogDebug($"Found NewClientViewModel in parent: {frameworkElement.GetType().Name}", "NPersonalView");
                            return viewModel;
                        }
                    }
                    parent = System.Windows.Media.VisualTreeHelper.GetParent(parent);
                }

                // Method 2: Try to get parent through logical tree
                var logicalParent = System.Windows.LogicalTreeHelper.GetParent(this);
                while (logicalParent != null)
                {
                    if (logicalParent is FrameworkElement frameworkElement)
                    {
                        if (frameworkElement.DataContext is NewClientViewModel viewModel)
                        {
                            LoggingService.LogDebug($"Found NewClientViewModel in logical parent: {frameworkElement.GetType().Name}", "NPersonalView");
                            return viewModel;
                        }
                    }
                    logicalParent = System.Windows.LogicalTreeHelper.GetParent(logicalParent);
                }

                // Method 3: Try to find NewClientView by name in the visual tree
                var window = Window.GetWindow(this);
                if (window != null)
                {
                    var newClientView = FindVisualChild<NewClientView>(window);
                    if (newClientView?.DataContext is NewClientViewModel viewModel)
                    {
                        LoggingService.LogDebug("Found NewClientViewModel through NewClientView search", "NPersonalView");
                        return viewModel;
                    }
                }

                LoggingService.LogWarning("NewClientViewModel not found in any parent DataContext", "NPersonalView");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting NewClientViewModel from parent: {ex.Message}", "NPersonalView");
                return null;
            }
        }

        /// <summary>
        /// Helper method to find a child of a specific type in the visual tree.
        /// </summary>
        /// <typeparam name="T">The type of child to find</typeparam>
        /// <param name="parent">The parent element to search from</param>
        /// <returns>The first child of the specified type, or null if not found</returns>
        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                {
                    return result;
                }

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                {
                    return childOfChild;
                }
            }
            return null;
        }

        #endregion
    }
}
