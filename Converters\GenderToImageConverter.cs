using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media.Imaging;
using UFU2.Common;

namespace UFU2.Converters
{
    /// <summary>
    /// Converter for mapping gender selections to appropriate default profile images.
    /// Maps Male (0) to male_default.jpg, Female (1) to female_default.jpg, and null/unselected defaults to male image.
    /// Follows UFU2 standards with proper error handling and Arabic localization support.
    /// </summary>
    public class GenderToImageConverter : IValueConverter
    {
        #region Private Fields

        // Cache for loaded images to improve performance
        private static BitmapImage? _femaleDefaultImage;
        private static BitmapImage? _maleDefaultImage;

        // Image paths following UFU2 resource structure
        private const string FemaleImagePath = "pack://application:,,,/Resources/female_default.jpg";
        private const string MaleImagePath = "pack://application:,,,/Resources/male_default.jpg";

        #endregion

        #region IValueConverter Implementation

        /// <summary>
        /// Converts gender selection to appropriate default profile image.
        /// </summary>
        /// <param name="value">Gender value (0 = Male, 1 = Female, or SelectedIndex from ComboBox)</param>
        /// <param name="targetType">Target type (ImageSource)</param>
        /// <param name="parameter">Optional parameter (not used)</param>
        /// <param name="culture">Culture info (not used)</param>
        /// <returns>BitmapImage for the appropriate gender default image</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                // Handle null or invalid input - default to male
                if (value == null)
                {
                    LoggingService.LogDebug("Gender value is null, defaulting to male image", "GenderToImageConverter");
                    return GetMaleDefaultImage();
                }

                // Convert value to integer for gender comparison
                int genderValue;
                if (value is int intValue)
                {
                    genderValue = intValue;
                }
                else if (int.TryParse(value.ToString(), out int parsedValue))
                {
                    genderValue = parsedValue;
                }
                else
                {
                    LoggingService.LogDebug($"Unable to parse gender value '{value}', defaulting to male image", "GenderToImageConverter");
                    return GetMaleDefaultImage();
                }

                // Map gender values to appropriate images
                // 0 = Male (ذكر), 1 = Female (أنثى) - following UFU2 ComboBox order
                return genderValue switch
                {
                    0 => GetMaleDefaultImage(),    // Male (ذكر)
                    1 => GetFemaleDefaultImage(),  // Female (أنثى)
                    _ => GetMaleDefaultImage()     // Default fallback to male
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in GenderToImageConverter: {ex.Message}", "GenderToImageConverter");
                return GetMaleDefaultImage(); // Safe fallback to male
            }
        }

        /// <summary>
        /// ConvertBack is not implemented as this is a one-way converter.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("GenderToImageConverter is a one-way converter");
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Gets the female default image with caching for performance.
        /// </summary>
        /// <returns>BitmapImage for female default profile image</returns>
        private static BitmapImage GetFemaleDefaultImage()
        {
            try
            {
                if (_femaleDefaultImage == null)
                {
                    _femaleDefaultImage = CreateBitmapImage(FemaleImagePath);
                    LoggingService.LogDebug("Female default image loaded and cached", "GenderToImageConverter");
                }
                return _femaleDefaultImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading female default image: {ex.Message}", "GenderToImageConverter");
                return CreateFallbackImage();
            }
        }

        /// <summary>
        /// Gets the male default image with caching for performance.
        /// </summary>
        /// <returns>BitmapImage for male default profile image</returns>
        private static BitmapImage GetMaleDefaultImage()
        {
            try
            {
                if (_maleDefaultImage == null)
                {
                    _maleDefaultImage = CreateBitmapImage(MaleImagePath);
                    LoggingService.LogDebug("Male default image loaded and cached", "GenderToImageConverter");
                }
                return _maleDefaultImage;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading male default image: {ex.Message}", "GenderToImageConverter");
                return GetFemaleDefaultImage(); // Fallback to female image
            }
        }



        /// <summary>
        /// Creates a BitmapImage from the specified URI path.
        /// </summary>
        /// <param name="imagePath">Pack URI path to the image resource</param>
        /// <returns>BitmapImage instance</returns>
        private static BitmapImage CreateBitmapImage(string imagePath)
        {
            var bitmap = new BitmapImage();
            bitmap.BeginInit();
            bitmap.UriSource = new Uri(imagePath, UriKind.Absolute);
            bitmap.CacheOption = BitmapCacheOption.OnLoad; // Load immediately and cache
            bitmap.CreateOptions = BitmapCreateOptions.IgnoreImageCache; // Ensure fresh load
            bitmap.EndInit();
            bitmap.Freeze(); // Make thread-safe and improve performance
            return bitmap;
        }

        /// <summary>
        /// Creates a minimal fallback image in case of complete failure.
        /// </summary>
        /// <returns>Empty BitmapImage as last resort fallback</returns>
        private static BitmapImage CreateFallbackImage()
        {
            try
            {
                // Create a minimal 145x127 transparent image as absolute fallback
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.DecodePixelWidth = 127;
                bitmap.DecodePixelHeight = 145;
                bitmap.EndInit();
                bitmap.Freeze();
                return bitmap;
            }
            catch
            {
                // If even this fails, return a new empty BitmapImage
                return new BitmapImage();
            }
        }

        #endregion

        #region Static Cleanup Methods

        /// <summary>
        /// Clears cached images to free memory when needed.
        /// Call this method when the application is shutting down or when memory cleanup is required.
        /// </summary>
        public static void ClearImageCache()
        {
            try
            {
                _femaleDefaultImage = null;
                _maleDefaultImage = null;
                LoggingService.LogDebug("Gender image cache cleared", "GenderToImageConverter");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing gender image cache: {ex.Message}", "GenderToImageConverter");
            }
        }

        #endregion
    }
}
