using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows.Input;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the AddCraftTypeDialog providing MVVM data binding and command handling.
    /// Manages craft type creation workflow with validation and database integration.
    /// Supports adding missing craft types to the CraftTypeBase database.
    /// </summary>
    public class AddCraftTypeDialogViewModel : BaseViewModel
    {
        #region Private Fields

        private readonly CraftTypeBaseService? _craftTypeService;
        private readonly string _originalCode;
        private CraftTypeBaseModel _craftType;
        private bool _isLoading;
        private bool _canSave;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the craft type model being edited.
        /// </summary>
        public CraftTypeBaseModel CraftType
        {
            get => _craftType;
            set
            {
                if (SetProperty(ref _craftType, value))
                {
                    UpdateCanSave();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the dialog is in loading state.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    UpdateCanSave();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the save operation can be executed.
        /// </summary>
        public bool CanSave
        {
            get => _canSave;
            private set => SetProperty(ref _canSave, value);
        }

        /// <summary>
        /// Gets the formatted message to display to the user about the missing craft code.
        /// </summary>
        public string MissingCraftMessage =>
            string.IsNullOrWhiteSpace(_originalCode)
                ? "إضافة نوع حرفة جديد إلى قاعدة البيانات"
                : $"نشاط برمز {_originalCode} غير موجود. هل تريد اضافته؟";

        #endregion

        #region Commands

        /// <summary>
        /// Command for saving the new craft type to the database.
        /// </summary>
        public ICommand SaveCommand { get; }

        /// <summary>
        /// Command for canceling the dialog without saving.
        /// </summary>
        public ICommand CancelCommand { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the AddCraftTypeDialogViewModel class.
        /// </summary>
        /// <param name="craftCode">The craft code that was not found</param>
        public AddCraftTypeDialogViewModel(string craftCode)
        {
            _originalCode = craftCode ?? string.Empty;

            // Initialize craft type model with the missing code (or empty if none provided)
            _craftType = new CraftTypeBaseModel(_originalCode, string.Empty, string.Empty, string.Empty);

            // Set up property change notifications for validation
            _craftType.PropertyChanged += CraftType_PropertyChanged;

            // Initialize commands
            SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave, "SaveCraftType");
            CancelCommand = new RelayCommand(ExecuteCancel, CanExecuteCancel, "CancelDialog");

            // Get CraftTypeBaseService from ServiceLocator
            if (ServiceLocator.TryGetService<CraftTypeBaseService>(out var service))
            {
                _craftTypeService = service;
            }
            else
            {
                LoggingService.LogWarning("CraftTypeBaseService not available in AddCraftTypeDialogViewModel", GetType().Name);
            }

            // Initialize validation state
            UpdateCanSave();

            // Initialize BaseViewModel
            OnInitialize();

            LoggingService.LogInfo($"AddCraftTypeDialogViewModel initialized for code: {_originalCode}", GetType().Name);
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Handles property changes on the CraftType model to update validation state.
        /// </summary>
        private void CraftType_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(CraftTypeBaseModel.Code) || 
                e.PropertyName == nameof(CraftTypeBaseModel.Description))
            {
                UpdateCanSave();
            }
        }

        /// <summary>
        /// Updates the CanSave property based on validation rules.
        /// </summary>
        private void UpdateCanSave()
        {
            try
            {
                // Check if required fields are valid
                bool isCodeValid = !string.IsNullOrWhiteSpace(CraftType?.Code) &&
                                   CraftType.Code.Length == 9 && // Require exactly 9 characters (XX-XX-XXX)
                                   CraftCodeFormatter.IsValidCraftCodeFormat(CraftType.Code);

                bool isDescriptionValid = !string.IsNullOrWhiteSpace(CraftType?.Description) &&
                                          CraftType.Description.Trim().Length >= 3;

                // Update CanSave based on validation and loading state
                CanSave = isCodeValid && isDescriptionValid && !IsLoading;

                LoggingService.LogDebug($"CanSave updated: {CanSave} (Code: '{CraftType?.Code}', CodeValid: {isCodeValid}, Description: '{CraftType?.Description}', DescValid: {isDescriptionValid}, IsLoading: {IsLoading})", GetType().Name);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating CanSave: {ex.Message}", GetType().Name);
                CanSave = false;
            }
        }



        #endregion

        #region Command Methods

        /// <summary>
        /// Executes the save command to add the new craft type to the database.
        /// </summary>
        private async void ExecuteSave(object? parameter)
        {
            try
            {
                IsLoading = true;

                if (_craftTypeService == null)
                {
                    ErrorManager.ShowUserErrorToast(
                        "خدمة أنواع الحرف غير متوفرة",
                        "خطأ في النظام",
                        GetType().Name);
                    return;
                }

                LoggingService.LogInfo($"Attempting to save new craft type: {CraftType.Code} - {CraftType.Description}", GetType().Name);

                // Validate the model before saving
                if (!CraftType.IsValid())
                {
                    ErrorManager.ShowUserWarningToast(
                        "يرجى التأكد من صحة البيانات المدخلة",
                        "بيانات غير صحيحة",
                        GetType().Name);
                    return;
                }

                // Check for duplicate craft code before saving
                var existingCraft = await _craftTypeService.GetByCodeAsync(CraftType.Code);
                if (existingCraft != null)
                {
                    LoggingService.LogWarning($"Duplicate craft code detected: {CraftType.Code} already exists", GetType().Name);

                    ErrorManager.ShowUserWarningToast(
                        $"رمز الحرفة {CraftType.Code} موجود بالفعل. يرجى استخدام رمز مختلف",
                        "رمز مكرر",
                        GetType().Name);
                    return;
                }

                LoggingService.LogDebug($"No duplicate found for craft code: {CraftType.Code}, proceeding with save", GetType().Name);

                // Save to database
                bool success = await _craftTypeService.InsertAsync(CraftType);

                if (success)
                {
                    // Clear cache to refresh data
                    _craftTypeService.ClearCache();

                    LoggingService.LogInfo($"Successfully saved craft type: {CraftType.Code}", GetType().Name);

                    // Raise event to close dialog with success result
                    OnDialogResult(true, CraftType);
                }
                else
                {
                    ErrorManager.ShowUserErrorToast(
                        "فشل في حفظ نوع الحرفة. يرجى المحاولة مرة أخرى.",
                        "خطأ في الحفظ",
                        GetType().Name);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error saving craft type: {ex.Message}", GetType().Name);
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء حفظ نوع الحرفة. يرجى المحاولة مرة أخرى.",
                    "خطأ في الحفظ",
                    UFU2.Common.LogLevel.Error,
                    GetType().Name);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Determines whether the save command can be executed.
        /// </summary>
        private bool CanExecuteSave(object? parameter)
        {
            return CanSave && !IsLoading;
        }

        /// <summary>
        /// Executes the cancel command to close the dialog without saving.
        /// </summary>
        private void ExecuteCancel(object? parameter)
        {
            try
            {
                LoggingService.LogDebug("AddCraftTypeDialog cancelled", GetType().Name);
                
                // Raise event to close dialog with cancelled result
                OnDialogResult(false, null);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error cancelling dialog: {ex.Message}", GetType().Name);
            }
        }

        /// <summary>
        /// Determines whether the cancel command can be executed.
        /// </summary>
        private bool CanExecuteCancel(object? parameter)
        {
            return !IsLoading;
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the dialog should be closed with a result.
        /// </summary>
        public event Action<bool, CraftTypeBaseModel?>? DialogResultRequested;

        /// <summary>
        /// Raises the DialogResultRequested event.
        /// </summary>
        /// <param name="result">True if successful, false if cancelled</param>
        /// <param name="craftType">The created craft type or null if cancelled</param>
        private void OnDialogResult(bool result, CraftTypeBaseModel? craftType)
        {
            DialogResultRequested?.Invoke(result, craftType);
        }

        #endregion

        #region Overrides

        /// <summary>
        /// Called when the ViewModel is being disposed.
        /// </summary>
        protected override void OnDispose()
        {
            if (_craftType != null)
            {
                _craftType.PropertyChanged -= CraftType_PropertyChanged;
            }

            LoggingService.LogInfo("AddCraftTypeDialogViewModel disposed", GetType().Name);
            base.OnDispose();
        }

        #endregion
    }
}
