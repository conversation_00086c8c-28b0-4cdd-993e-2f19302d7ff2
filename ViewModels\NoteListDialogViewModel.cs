using System.Windows.Input;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the NoteListDialog providing MVVM data binding and command handling.
    /// Manages notes collection operations including adding, editing, and deleting notes.
    /// Supports empty state display and integrates with AddNotesDialog for note editing.
    ///
    /// REFACTORING STATUS: ✅ COMPLETED - IDisposable implementation enhanced (Task 2.5)
    /// BACKUP CREATED: NoteListDialogViewModel.cs.backup - Original implementation before IDisposable enhancement
    /// </summary>
    public class NoteListDialogViewModel : BaseViewModel
    {
        #region Private Fields

        private NotesCollectionModel _notes;

        // Track event subscriptions for proper disposal
        private bool _notesEventSubscribed = false;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the notes collection model.
        /// </summary>
        public NotesCollectionModel Notes
        {
            get => _notes;
            private set => SetProperty(ref _notes, value);
        }

        /// <summary>
        /// Gets whether the notes collection is empty for empty state display.
        /// </summary>
        public bool IsEmpty => Notes?.IsEmpty ?? true;

        /// <summary>
        /// Gets whether the notes collection has items.
        /// </summary>
        public bool HasNotes => Notes?.HasNotes ?? false;

        /// <summary>
        /// Gets the total number of notes.
        /// </summary>
        public int NotesCount => Notes?.Count ?? 0;

        #endregion

        #region Commands

        /// <summary>
        /// Command to add a new note.
        /// </summary>
        public RelayCommand AddNoteCommand { get; private set; }

        /// <summary>
        /// Command to edit an existing note.
        /// </summary>
        public RelayCommand<NoteModel> EditNoteCommand { get; private set; }

        /// <summary>
        /// Command to delete a note.
        /// </summary>
        public RelayCommand<NoteModel> DeleteNoteCommand { get; private set; }

        /// <summary>
        /// Command to close the dialog.
        /// </summary>
        public RelayCommand CloseCommand { get; private set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the NoteListDialogViewModel class.
        /// </summary>
        public NoteListDialogViewModel()
        {
            InitializeViewModel(new NotesCollectionModel());
        }

        /// <summary>
        /// Initializes a new instance of the NoteListDialogViewModel class with existing notes.
        /// </summary>
        /// <param name="existingNotes">Existing notes collection to manage</param>
        public NoteListDialogViewModel(NotesCollectionModel existingNotes)
        {
            InitializeViewModel(existingNotes ?? new NotesCollectionModel());
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes the ViewModel with the provided notes collection.
        /// </summary>
        /// <param name="notesCollection">The notes collection to use</param>
        private void InitializeViewModel(NotesCollectionModel notesCollection)
        {
            // Initialize notes collection
            Notes = notesCollection;

            // Subscribe to collection changes to update UI properties
            Notes.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(Notes.Count) ||
                    e.PropertyName == nameof(Notes.HasNotes) ||
                    e.PropertyName == nameof(Notes.IsEmpty))
                {
                    OnPropertyChanged(nameof(IsEmpty));
                    OnPropertyChanged(nameof(HasNotes));
                    OnPropertyChanged(nameof(NotesCount));
                }
            };
            _notesEventSubscribed = true;

            // Initialize commands
            InitializeCommands();

            // Initialize BaseViewModel
            OnInitialize();

            LoggingService.LogDebug($"NoteListDialogViewModel initialized with {Notes.Count} notes and IDisposable support", "NoteListDialogViewModel");
        }

        /// <summary>
        /// Initializes the commands for the ViewModel.
        /// </summary>
        private void InitializeCommands()
        {
            AddNoteCommand = new RelayCommand(
                execute: ExecuteAddNote,
                canExecute: _ => true,
                commandName: "AddNote"
            );

            EditNoteCommand = new RelayCommand<NoteModel>(
                execute: ExecuteEditNote,
                canExecute: CanExecuteEditNote,
                commandName: "EditNote"
            );

            DeleteNoteCommand = new RelayCommand<NoteModel>(
                execute: ExecuteDeleteNote,
                canExecute: CanExecuteDeleteNote,
                commandName: "DeleteNote"
            );

            CloseCommand = new RelayCommand(
                execute: ExecuteClose,
                canExecute: _ => true,
                commandName: "CloseDialog"
            );
        }

        #endregion

        #region Command Implementations

        /// <summary>
        /// Executes the add note command.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        private void ExecuteAddNote(object? parameter)
        {
            try
            {
                LoggingService.LogDebug("Add note command executed", "NoteListDialogViewModel");
                OnAddNoteRequested();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing add note command: {ex.Message}", "NoteListDialogViewModel");
            }
        }

        /// <summary>
        /// Determines whether the edit note command can be executed.
        /// </summary>
        /// <param name="note">The note to edit</param>
        /// <returns>True if the note is not null, false otherwise</returns>
        private bool CanExecuteEditNote(NoteModel? note)
        {
            return note != null;
        }

        /// <summary>
        /// Executes the edit note command.
        /// </summary>
        /// <param name="note">The note to edit</param>
        private void ExecuteEditNote(NoteModel? note)
        {
            try
            {
                if (note == null)
                {
                    LoggingService.LogWarning("Edit note command executed with null note", "NoteListDialogViewModel");
                    return;
                }

                LoggingService.LogDebug($"Edit note command executed for note ID: {note.Id}", "NoteListDialogViewModel");
                OnEditNoteRequested(note);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing edit note command: {ex.Message}", "NoteListDialogViewModel");
            }
        }

        /// <summary>
        /// Determines whether the delete note command can be executed.
        /// </summary>
        /// <param name="note">The note to delete</param>
        /// <returns>True if the note is not null, false otherwise</returns>
        private bool CanExecuteDeleteNote(NoteModel? note)
        {
            return note != null;
        }

        /// <summary>
        /// Executes the delete note command.
        /// </summary>
        /// <param name="note">The note to delete</param>
        private void ExecuteDeleteNote(NoteModel? note)
        {
            try
            {
                if (note == null)
                {
                    LoggingService.LogWarning("Delete note command executed with null note", "NoteListDialogViewModel");
                    return;
                }

                LoggingService.LogDebug($"Delete note command executed for note ID: {note.Id}", "NoteListDialogViewModel");
                OnDeleteNoteRequested(note);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing delete note command: {ex.Message}", "NoteListDialogViewModel");
            }
        }

        /// <summary>
        /// Executes the close command.
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        private void ExecuteClose(object? parameter)
        {
            try
            {
                LoggingService.LogInfo("Close dialog command executed", "NoteListDialogViewModel");
                OnCloseRequested();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing close command: {ex.Message}", "NoteListDialogViewModel");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Adds a new note to the collection.
        /// </summary>
        /// <param name="note">The note to add</param>
        public void AddNote(NoteModel note)
        {
            try
            {
                if (note == null)
                {
                    LoggingService.LogWarning("Attempted to add null note", "NoteListDialogViewModel");
                    return;
                }

                Notes.AddNote(note);
                LoggingService.LogInfo($"Note added successfully. Total notes: {Notes.Count}", "NoteListDialogViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding note: {ex.Message}", "NoteListDialogViewModel");
            }
        }

        /// <summary>
        /// Updates an existing note in the collection.
        /// </summary>
        /// <param name="updatedNote">The updated note data</param>
        public void UpdateNote(NoteModel updatedNote)
        {
            try
            {
                if (updatedNote == null)
                {
                    LoggingService.LogWarning("Attempted to update with null note", "NoteListDialogViewModel");
                    return;
                }

                var success = Notes.UpdateNote(updatedNote.Id, updatedNote.Content, updatedNote.Priority);
                if (success)
                {
                    LoggingService.LogDebug($"Note updated successfully. ID: {updatedNote.Id}", "NoteListDialogViewModel");
                }
                else
                {
                    LoggingService.LogWarning($"Failed to update note. ID: {updatedNote.Id}", "NoteListDialogViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating note: {ex.Message}", "NoteListDialogViewModel");
            }
        }

        /// <summary>
        /// Removes a note from the collection.
        /// </summary>
        /// <param name="note">The note to remove</param>
        public void RemoveNote(NoteModel note)
        {
            try
            {
                if (note == null)
                {
                    LoggingService.LogWarning("Attempted to remove null note", "NoteListDialogViewModel");
                    return;
                }

                var success = Notes.RemoveNote(note);
                if (success)
                {
                    LoggingService.LogDebug($"Note removed successfully. Total notes: {Notes.Count}", "NoteListDialogViewModel");
                }
                else
                {
                    LoggingService.LogWarning($"Failed to remove note. ID: {note.Id}", "NoteListDialogViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing note: {ex.Message}", "NoteListDialogViewModel");
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the user requests to add a new note.
        /// </summary>
        public event EventHandler? AddNoteRequested;

        /// <summary>
        /// Event raised when the user requests to edit a note.
        /// </summary>
        public event EventHandler<NoteModel>? EditNoteRequested;

        /// <summary>
        /// Event raised when the user requests to delete a note.
        /// </summary>
        public event EventHandler<NoteModel>? DeleteNoteRequested;

        /// <summary>
        /// Event raised when the user requests to close the dialog.
        /// </summary>
        public event EventHandler? CloseRequested;

        /// <summary>
        /// Raises the AddNoteRequested event.
        /// </summary>
        protected virtual void OnAddNoteRequested()
        {
            AddNoteRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Raises the EditNoteRequested event.
        /// </summary>
        /// <param name="note">The note to edit</param>
        protected virtual void OnEditNoteRequested(NoteModel note)
        {
            EditNoteRequested?.Invoke(this, note);
        }

        /// <summary>
        /// Raises the DeleteNoteRequested event.
        /// </summary>
        /// <param name="note">The note to delete</param>
        protected virtual void OnDeleteNoteRequested(NoteModel note)
        {
            DeleteNoteRequested?.Invoke(this, note);
        }

        /// <summary>
        /// Raises the CloseRequested event.
        /// </summary>
        protected virtual void OnCloseRequested()
        {
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes resources and cleans up event subscriptions, collections, and references.
        /// Implements enhanced IDisposable pattern with BaseViewModel integration.
        /// Prevents memory leaks from Notes PropertyChanged events and dialog event handlers.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    LoggingService.LogDebug("Starting disposal of NoteListDialogViewModel resources", GetType().Name);

                    // Unsubscribe from Notes PropertyChanged events using reflection-based cleanup
                    if (_notes != null && _notesEventSubscribed)
                    {
                        try
                        {
                            // Clear all event handlers for PropertyChanged using reflection
                            var propertyChangedField = typeof(NotesCollectionModel).GetField("PropertyChanged",
                                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                            if (propertyChangedField != null)
                            {
                                propertyChangedField.SetValue(_notes, null);
                            }
                            _notesEventSubscribed = false;
                            LoggingService.LogDebug("Notes PropertyChanged events unsubscribed successfully", GetType().Name);
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"Error unsubscribing from Notes PropertyChanged events: {ex.Message}", GetType().Name);
                        }
                    }

                    // Clear dialog event handlers to prevent memory leaks
                    try
                    {
                        AddNoteRequested = null;
                        EditNoteRequested = null;
                        DeleteNoteRequested = null;
                        CloseRequested = null;
                        LoggingService.LogDebug("Dialog event handlers cleared successfully", GetType().Name);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error clearing dialog event handlers: {ex.Message}", GetType().Name);
                    }

                    // Clear property references to aid garbage collection
                    // Set to a new empty instance instead of null to prevent NullReferenceException
                    _notes = new NotesCollectionModel();

                    LoggingService.LogDebug("NoteListDialogViewModel disposal completed successfully", GetType().Name);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error during NoteListDialogViewModel disposal: {ex.Message}", GetType().Name);
            }
            finally
            {
                // Always call base disposal to maintain inheritance chain
                base.Dispose(disposing);
            }
        }

        #endregion
    }
}
