using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace UFU2.Models
{
    /// <summary>
    /// Model class representing a single note with priority level and timestamps.
    /// Implements INotifyPropertyChanged for proper WPF data binding support.
    /// Supports priority-based categorization with color mapping for visual indicators.
    ///
    /// This model follows UFU2 architectural patterns with:
    /// - Property change notification for real-time UI updates
    /// - Note content validation and formatting support
    /// - Arabic RTL layout compatibility
    /// - MaterialDesign card integration
    /// - Priority-based visual categorization system
    /// </summary>
    public class NoteModel : INotifyPropertyChanged
    {
        #region Private Fields

        private string _id = string.Empty;
        private string _content = string.Empty;

        private int _priority = 0;
        private DateTime _createdDate = DateTime.Now;
        private DateTime _modifiedDate = DateTime.Now;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the unique identifier for the note.
        /// </summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// Gets or sets the content/text of the note.
        /// </summary>
        public string Content
        {
            get => _content;
            set
            {
                if (SetProperty(ref _content, value))
                {
                    ModifiedDate = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// Gets or sets an optional category for the note (e.g., General, Warning).
        /// </summary>
        public string? Category { get; set; }



        /// <summary>
        /// Gets or sets the priority level of the note.
        /// 0 = Normal (Green), 1 = Medium (Orange), 2 = High (Red).
        /// </summary>
        public int Priority
        {
            get => _priority;
            set
            {
                if (SetProperty(ref _priority, value))
                {
                    OnPropertyChanged(nameof(PriorityColorBrush));
                    OnPropertyChanged(nameof(PriorityColorName));
                    ModifiedDate = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// Gets or sets the creation date and time of the note.
        /// </summary>
        public DateTime CreatedDate
        {
            get => _createdDate;
            set
            {
                if (SetProperty(ref _createdDate, value))
                {
                    OnPropertyChanged(nameof(FormattedCreatedDate));
                }
            }
        }

        /// <summary>
        /// Gets or sets the last modification date and time of the note.
        /// </summary>
        public DateTime ModifiedDate
        {
            get => _modifiedDate;
            set
            {
                if (SetProperty(ref _modifiedDate, value))
                {
                    OnPropertyChanged(nameof(FormattedModifiedDate));
                    OnPropertyChanged(nameof(DisplayDate));
                }
            }
        }

        /// <summary>
        /// Gets the formatted creation date for display (DD/MM/YYYY : HH:mm:ss).
        /// </summary>
        public string FormattedCreatedDate => CreatedDate.ToString("dd/MM/yyyy : HH:mm:ss");

        /// <summary>
        /// Gets the formatted modification date for display (DD/MM/YYYY : HH:mm:ss).
        /// </summary>
        public string FormattedModifiedDate => ModifiedDate.ToString("dd/MM/yyyy : HH:mm:ss");

        /// <summary>
        /// Gets the display date (shows modified date if different from created date, otherwise created date).
        /// </summary>
        public string DisplayDate => ModifiedDate.Date != CreatedDate.Date ? FormattedModifiedDate : FormattedCreatedDate;

        /// <summary>
        /// Gets the color brush name for the priority level for XAML binding.
        /// </summary>
        public string PriorityColorBrush => GetPriorityColorBrush(Priority);

        /// <summary>
        /// Gets the color name for the priority level.
        /// </summary>
        public string PriorityColorName => GetPriorityColorName(Priority);

        /// <summary>
        /// Gets whether the note has valid content.
        /// </summary>
        public bool HasContent => !string.IsNullOrWhiteSpace(Content);

        /// <summary>
        /// Gets whether the note content is empty.
        /// </summary>
        public bool IsEmpty => string.IsNullOrWhiteSpace(Content);

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the NoteModel class with default values.
        /// </summary>
        public NoteModel()
        {
            Id = Guid.NewGuid().ToString();
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// Initializes a new instance of the NoteModel class with specified content.
        /// </summary>
        /// <param name="content">The note content</param>
        /// <param name="priority">The priority level (optional, defaults to 0)</param>
        public NoteModel(string content, int priority = 0)
        {
            Id = Guid.NewGuid().ToString();
            Content = content;
            Priority = priority;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Validates the note content.
        /// </summary>
        /// <returns>True if the note has valid content, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Content) && Content.Trim().Length > 0;
        }

        /// <summary>
        /// Creates a deep copy of the note.
        /// </summary>
        /// <returns>A new NoteModel instance with the same values</returns>
        public NoteModel Clone()
        {
            return new NoteModel
            {
                Id = Id,
                Content = Content,
                Priority = Priority,
                CreatedDate = CreatedDate,
                ModifiedDate = ModifiedDate
            };
        }

        /// <summary>
        /// Resets the note to default values while preserving the ID.
        /// </summary>
        public void Reset()
        {
            Content = string.Empty;
            Priority = 0; // Normal priority (Green)
            var now = DateTime.Now;
            CreatedDate = now;
            ModifiedDate = now;
        }

        /// <summary>
        /// Updates the note content and modification timestamp.
        /// </summary>
        /// <param name="newContent">The new content</param>
        /// <param name="newPriority">The new priority level (optional)</param>
        public void Update(string newContent, int? newPriority = null)
        {
            Content = newContent;
            if (newPriority.HasValue)
            {
                Priority = newPriority.Value;
            }
            ModifiedDate = DateTime.Now;
        }

        #endregion

        #region Static Methods

        /// <summary>
        /// Gets the color brush name for the specified priority level.
        /// </summary>
        /// <param name="priority">The priority level</param>
        /// <returns>The color brush name for XAML binding</returns>
        public static string GetPriorityColorBrush(int priority)
        {
            return priority switch
            {
                0 => "Green",     // Normal
                1 => "Orange",    // Medium
                2 => "Red",       // High
                _ => "Green"      // Default
            };
        }

        /// <summary>
        /// Gets the color name for the specified priority level.
        /// </summary>
        /// <param name="priority">The priority level</param>
        /// <returns>The Arabic color name</returns>
        public static string GetPriorityColorName(int priority)
        {
            return priority switch
            {
                0 => "أخضر",      // Green
                1 => "برتقالي",   // Orange
                2 => "أحمر",      // Red
                _ => "أخضر"       // Default
            };
        }

        /// <summary>
        /// Gets all available priority levels with their display names.
        /// </summary>
        /// <returns>Dictionary of priority levels and their Arabic display names</returns>
        public static Dictionary<int, string> GetAllPriorityLevels()
        {
            return new Dictionary<int, string>
            {
                { 0, "عادي" },      // Normal
                { 1, "متوسط" },     // Medium
                { 2, "عالي" }       // High
            };
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Sets the property value and raises PropertyChanged if the value has changed
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">New value to set</param>
        /// <param name="propertyName">Name of the property</param>
        /// <returns>True if the property value was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }


}
