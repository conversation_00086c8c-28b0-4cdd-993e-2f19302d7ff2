using System;
using System.Collections.Generic;
using System.Linq;
using UFU2.Services;

namespace UFU2.Models
{
    /// <summary>
    /// Business rule model for file check type validation and management.
    /// Defines which file check types are valid for different activity types.
    /// Provides centralized business logic for file check requirements.
    /// </summary>
    public static class FileCheckTypeRules
    {
        #region Constants

        /// <summary>
        /// All valid file check types in the system.
        /// </summary>
        public static readonly string[] AllFileCheckTypes = { "CAS", "NIF", "NIS", "RC", "ART", "AGR", "DEX" };

        /// <summary>
        /// Common file check types required for all activity types.
        /// </summary>
        public static readonly string[] CommonFileCheckTypes = { "CAS", "NIF", "NIS", "DEX" };

        /// <summary>
        /// File check types specific to commercial activities.
        /// </summary>
        public static readonly string[] CommercialSpecificTypes = { "RC" };

        /// <summary>
        /// File check types specific to craft activities.
        /// </summary>
        public static readonly string[] CraftSpecificTypes = { "ART" };

        /// <summary>
        /// File check types specific to professional activities.
        /// </summary>
        public static readonly string[] ProfessionalSpecificTypes = { "AGR" };

        #endregion

        #region Activity Type Constants

        /// <summary>
        /// Main commercial activity type.
        /// </summary>
        public const string MainCommercial = "MainCommercial";

        /// <summary>
        /// Secondary commercial activity type.
        /// </summary>
        public const string SecondaryCommercial = "SecondaryCommercial";

        /// <summary>
        /// Craft activity type.
        /// </summary>
        public const string Craft = "Craft";

        /// <summary>
        /// Professional activity type.
        /// </summary>
        public const string Professional = "Professional";

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the valid file check types for the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>List of valid file check types</returns>
        public static List<string> GetValidFileCheckTypes(string activityType)
        {
            if (string.IsNullOrWhiteSpace(activityType))
                return CommonFileCheckTypes.ToList();

            var commonTypes = CommonFileCheckTypes.ToList();

            return activityType switch
            {
                MainCommercial or SecondaryCommercial => commonTypes.Concat(CommercialSpecificTypes).ToList(),
                Craft => commonTypes.Concat(CraftSpecificTypes).ToList(),
                Professional => commonTypes.Concat(ProfessionalSpecificTypes).ToList(),
                _ => commonTypes
            };
        }

        /// <summary>
        /// Gets the required file check types for the specified activity type.
        /// These are the file check types that must be present for the activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>List of required file check types</returns>
        public static List<string> GetRequiredFileCheckTypes(string activityType)
        {
            // For now, all valid types are considered required
            // This can be modified in the future if some types become optional
            return GetValidFileCheckTypes(activityType);
        }

        /// <summary>
        /// Gets the optional file check types for the specified activity type.
        /// These are file check types that can be present but are not required.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>List of optional file check types</returns>
        public static List<string> GetOptionalFileCheckTypes(string activityType)
        {
            // Currently, no file check types are optional
            // This method is provided for future extensibility
            return new List<string>();
        }

        /// <summary>
        /// Validates whether a file check type is valid for the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckType">The file check type to validate</param>
        /// <returns>True if the file check type is valid for the activity type</returns>
        public static bool IsValidFileCheckType(string activityType, string fileCheckType)
        {
            if (string.IsNullOrWhiteSpace(fileCheckType))
                return false;

            var validTypes = GetValidFileCheckTypes(activityType);
            return validTypes.Contains(fileCheckType, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Validates whether all provided file check types are valid for the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckTypes">The file check types to validate</param>
        /// <returns>True if all file check types are valid for the activity type</returns>
        public static bool AreAllFileCheckTypesValid(string activityType, IEnumerable<string> fileCheckTypes)
        {
            if (fileCheckTypes == null)
                return true;

            var validTypes = GetValidFileCheckTypes(activityType);
            return fileCheckTypes.All(type => validTypes.Contains(type, StringComparer.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Gets the invalid file check types from the provided list for the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckTypes">The file check types to validate</param>
        /// <returns>List of invalid file check types</returns>
        public static List<string> GetInvalidFileCheckTypes(string activityType, IEnumerable<string> fileCheckTypes)
        {
            if (fileCheckTypes == null)
                return new List<string>();

            var validTypes = GetValidFileCheckTypes(activityType);
            return fileCheckTypes.Where(type => !validTypes.Contains(type, StringComparer.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// Gets the missing required file check types for the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="providedFileCheckTypes">The file check types that are provided</param>
        /// <returns>List of missing required file check types</returns>
        public static List<string> GetMissingRequiredFileCheckTypes(string activityType, IEnumerable<string> providedFileCheckTypes)
        {
            var requiredTypes = GetRequiredFileCheckTypes(activityType);
            var providedTypes = providedFileCheckTypes?.ToList() ?? new List<string>();

            return requiredTypes.Where(required => 
                !providedTypes.Contains(required, StringComparer.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// Validates file check types for the specified activity type and returns validation result.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <param name="fileCheckTypes">The file check types to validate</param>
        /// <param name="requireAllMandatory">Whether to require all mandatory file check types</param>
        /// <returns>Validation result with any errors</returns>
        public static ValidationResult ValidateFileCheckTypes(string activityType, IEnumerable<string> fileCheckTypes, bool requireAllMandatory = false)
        {
            var result = new ValidationResult();
            var providedTypes = fileCheckTypes?.ToList() ?? new List<string>();

            // Check for invalid file check types
            var invalidTypes = GetInvalidFileCheckTypes(activityType, providedTypes);
            if (invalidTypes.Any())
            {
                var invalidTypesMessage = string.Join(", ", invalidTypes);
                result.AddError("FileCheckTypes", $"أنواع فحص الملفات غير صحيحة لنوع النشاط '{GetActivityTypeDisplayName(activityType)}': {invalidTypesMessage}");
            }

            // Check for missing required file check types (only if requireAllMandatory is true)
            if (requireAllMandatory)
            {
                var missingTypes = GetMissingRequiredFileCheckTypes(activityType, providedTypes);
                if (missingTypes.Any())
                {
                    var missingTypesMessage = string.Join(", ", missingTypes.Select(GetFileCheckTypeDisplayName));
                    result.AddError("RequiredFileCheckTypes", $"أنواع فحص الملفات المطلوبة مفقودة لنوع النشاط '{GetActivityTypeDisplayName(activityType)}': {missingTypesMessage}");
                }
            }

            return result;
        }

        #endregion

        #region Display Name Methods

        /// <summary>
        /// Gets the Arabic display name for a file check type.
        /// </summary>
        /// <param name="fileCheckType">The file check type</param>
        /// <returns>Arabic display name</returns>
        public static string GetFileCheckTypeDisplayName(string fileCheckType)
        {
            return fileCheckType?.ToUpperInvariant() switch
            {
                "CAS" => "الضمان الاجتماعي (CAS)",
                "NIF" => "رقم التعريف الجبائي (NIF)",
                "NIS" => "رقم التعريف الإحصائي (NIS)",
                "RC" => "السجل التجاري (RC)",
                "ART" => "شهادة حرفي (ART)",
                "AGR" => "شهادة مهني (AGR)",
                "DEX" => "تصريح بالوجود (DEX)",
                _ => fileCheckType ?? "غير محدد"
            };
        }

        /// <summary>
        /// Gets the Arabic display name for an activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>Arabic display name</returns>
        public static string GetActivityTypeDisplayName(string activityType)
        {
            return activityType switch
            {
                MainCommercial => "تجاري رئيسي",
                SecondaryCommercial => "تجاري ثانوي",
                Craft => "حرفي",
                Professional => "مهني",
                _ => activityType ?? "غير محدد"
            };
        }

        /// <summary>
        /// Gets all available activity types with their Arabic display names.
        /// </summary>
        /// <returns>Dictionary of activity types and their display names</returns>
        public static Dictionary<string, string> GetAllActivityTypes()
        {
            return new Dictionary<string, string>
            {
                { MainCommercial, GetActivityTypeDisplayName(MainCommercial) },
                { SecondaryCommercial, GetActivityTypeDisplayName(SecondaryCommercial) },
                { Craft, GetActivityTypeDisplayName(Craft) },
                { Professional, GetActivityTypeDisplayName(Professional) }
            };
        }

        /// <summary>
        /// Gets all file check types with their Arabic display names.
        /// </summary>
        /// <returns>Dictionary of file check types and their display names</returns>
        public static Dictionary<string, string> GetAllFileCheckTypes()
        {
            return AllFileCheckTypes.ToDictionary(
                type => type,
                type => GetFileCheckTypeDisplayName(type)
            );
        }

        /// <summary>
        /// Gets file check types for the specified activity type with their Arabic display names.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>Dictionary of valid file check types and their display names</returns>
        public static Dictionary<string, string> GetFileCheckTypesForActivity(string activityType)
        {
            var validTypes = GetValidFileCheckTypes(activityType);
            return validTypes.ToDictionary(
                type => type,
                type => GetFileCheckTypeDisplayName(type)
            );
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Checks if the specified activity type is a commercial activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>True if the activity type is commercial</returns>
        public static bool IsCommercialActivity(string activityType)
        {
            return activityType == MainCommercial || activityType == SecondaryCommercial;
        }

        /// <summary>
        /// Checks if the specified activity type is a non-commercial activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>True if the activity type is non-commercial</returns>
        public static bool IsNonCommercialActivity(string activityType)
        {
            return activityType == Craft || activityType == Professional;
        }

        /// <summary>
        /// Gets the category of the specified activity type.
        /// </summary>
        /// <param name="activityType">The activity type</param>
        /// <returns>Activity category (Commercial, NonCommercial, or Unknown)</returns>
        public static string GetActivityCategory(string activityType)
        {
            if (IsCommercialActivity(activityType))
                return "Commercial";
            
            if (IsNonCommercialActivity(activityType))
                return "NonCommercial";
            
            return "Unknown";
        }

        /// <summary>
        /// Checks if the specified file check type is common to all activity types.
        /// </summary>
        /// <param name="fileCheckType">The file check type</param>
        /// <returns>True if the file check type is common to all activities</returns>
        public static bool IsCommonFileCheckType(string fileCheckType)
        {
            return CommonFileCheckTypes.Contains(fileCheckType, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if the specified file check type is specific to commercial activities.
        /// </summary>
        /// <param name="fileCheckType">The file check type</param>
        /// <returns>True if the file check type is specific to commercial activities</returns>
        public static bool IsCommercialSpecificFileCheckType(string fileCheckType)
        {
            return CommercialSpecificTypes.Contains(fileCheckType, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if the specified file check type is specific to craft activities.
        /// </summary>
        /// <param name="fileCheckType">The file check type</param>
        /// <returns>True if the file check type is specific to craft activities</returns>
        public static bool IsCraftSpecificFileCheckType(string fileCheckType)
        {
            return CraftSpecificTypes.Contains(fileCheckType, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if the specified file check type is specific to professional activities.
        /// </summary>
        /// <param name="fileCheckType">The file check type</param>
        /// <returns>True if the file check type is specific to professional activities</returns>
        public static bool IsProfessionalSpecificFileCheckType(string fileCheckType)
        {
            return ProfessionalSpecificTypes.Contains(fileCheckType, StringComparer.OrdinalIgnoreCase);
        }

        #endregion
    }
}