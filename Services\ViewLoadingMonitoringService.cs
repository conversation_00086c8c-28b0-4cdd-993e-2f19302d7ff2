using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using UFU2.Common;

namespace UFU2.Services
{
    /// <summary>
    /// View loading monitoring service for UFU2 application.
    /// Provides comprehensive monitoring and performance insights for view loading operations.
    /// Implements Phase 2C Task 3.3 requirements for view loading monitoring.
    /// </summary>
    public class ViewLoadingMonitoringService : IDisposable
    {
        #region Private Fields

        private readonly ConcurrentDictionary<string, ViewLoadingMetrics> _loadingMetrics;
        private readonly ConcurrentDictionary<string, List<ViewLoadingEvent>> _loadingEvents;
        private readonly Timer _reportingTimer;
        private readonly Timer _cleanupTimer;
        private bool _disposed = false;

        // Configuration
        private readonly TimeSpan _reportingInterval = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(1);
        private readonly int _maxEventsPerView = 100;
        private readonly TimeSpan _eventRetentionTime = TimeSpan.FromHours(24);

        // Performance targets from Phase 2C roadmap
        private const long TargetLoadingTimeMs = 150; // Target: 60-70% improvement from baseline
        private const double TargetBackgroundEfficiencyRatio = 0.7; // 70% of operations should be background

        // Performance tracking
        private static long _totalViewLoads = 0;
        private static long _fastViewLoads = 0;
        private static long _backgroundViewLoads = 0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ViewLoadingMonitoringService.
        /// </summary>
        public ViewLoadingMonitoringService()
        {
            _loadingMetrics = new ConcurrentDictionary<string, ViewLoadingMetrics>();
            _loadingEvents = new ConcurrentDictionary<string, List<ViewLoadingEvent>>();

            // Start reporting timer
            _reportingTimer = new Timer(
                GeneratePerformanceReport,
                null,
                _reportingInterval,
                _reportingInterval);

            // Start cleanup timer
            _cleanupTimer = new Timer(
                PerformCleanup,
                null,
                _cleanupInterval,
                _cleanupInterval);

            LoggingService.LogInfo("ViewLoadingMonitoringService initialized", "ViewLoadingMonitoringService");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Starts monitoring a view loading operation.
        /// </summary>
        /// <param name="viewId">Unique identifier for the view</param>
        /// <param name="viewType">Type of view being loaded</param>
        /// <param name="loadingType">Type of loading operation</param>
        /// <returns>Stopwatch for measuring loading time</returns>
        public Stopwatch StartViewLoading(string viewId, string viewType, ViewLoadingType loadingType)
        {
            var stopwatch = Stopwatch.StartNew();
            
            var loadingEvent = new ViewLoadingEvent
            {
                ViewId = viewId,
                ViewType = viewType,
                LoadingType = loadingType,
                StartTime = DateTime.UtcNow,
                Status = ViewLoadingStatus.InProgress
            };

            // Add to events list
            var events = _loadingEvents.GetOrAdd(viewId, _ => new List<ViewLoadingEvent>());
            lock (events)
            {
                events.Add(loadingEvent);
                // Keep only recent events
                if (events.Count > _maxEventsPerView)
                {
                    events.RemoveAt(0);
                }
            }

            // Initialize or update metrics
            var metrics = _loadingMetrics.GetOrAdd(viewId, _ => new ViewLoadingMetrics
            {
                ViewId = viewId,
                ViewType = viewType
            });

            metrics.IncrementTotalLoadAttempts();
            System.Threading.Interlocked.Increment(ref _totalViewLoads);

            LoggingService.LogDebug($"Started monitoring view loading: {viewId} ({viewType}, {loadingType})", "ViewLoadingMonitoringService");
            return stopwatch;
        }

        /// <summary>
        /// Completes monitoring of a view loading operation.
        /// </summary>
        /// <param name="viewId">View identifier</param>
        /// <param name="stopwatch">Stopwatch from StartViewLoading</param>
        /// <param name="success">Whether the loading was successful</param>
        /// <param name="errorMessage">Error message if loading failed</param>
        public void CompleteViewLoading(string viewId, Stopwatch stopwatch, bool success = true, string? errorMessage = null)
        {
            stopwatch.Stop();
            var loadingTimeMs = stopwatch.ElapsedMilliseconds;

            // Update the latest event
            if (_loadingEvents.TryGetValue(viewId, out var events))
            {
                lock (events)
                {
                    var latestEvent = events.LastOrDefault();
                    if (latestEvent != null)
                    {
                        latestEvent.EndTime = DateTime.UtcNow;
                        latestEvent.LoadingTimeMs = loadingTimeMs;
                        latestEvent.Status = success ? ViewLoadingStatus.Completed : ViewLoadingStatus.Failed;
                        latestEvent.ErrorMessage = errorMessage;
                    }
                }
            }

            // Update metrics
            if (_loadingMetrics.TryGetValue(viewId, out var metrics))
            {
                if (success)
                {
                    metrics.IncrementSuccessfulLoads();
                    
                    // Update timing statistics
                    var currentTotal = metrics.TotalLoadingTimeMs;
                    metrics.AddTotalLoadingTime(loadingTimeMs);

                    // Update average
                    var successCount = metrics.SuccessfulLoads;
                    metrics.AverageLoadingTimeMs = (double)(currentTotal + loadingTimeMs) / successCount;

                    // Track fast loads
                    if (loadingTimeMs <= TargetLoadingTimeMs)
                    {
                        metrics.IncrementFastLoads();
                        System.Threading.Interlocked.Increment(ref _fastViewLoads);
                    }

                    // Track background loads
                    var latestEvent = events?.LastOrDefault();
                    if (latestEvent?.LoadingType == ViewLoadingType.Background)
                    {
                        metrics.IncrementBackgroundLoads();
                        System.Threading.Interlocked.Increment(ref _backgroundViewLoads);
                    }
                }
                else
                {
                    metrics.IncrementFailedLoads();
                }

                metrics.LastLoadTime = DateTime.UtcNow;
            }

            LoggingService.LogDebug($"Completed view loading monitoring: {viewId} - {loadingTimeMs}ms (Success: {success})", "ViewLoadingMonitoringService");
        }

        /// <summary>
        /// Records a view loading event without timing.
        /// </summary>
        /// <param name="viewId">View identifier</param>
        /// <param name="viewType">Type of view</param>
        /// <param name="loadingType">Type of loading operation</param>
        /// <param name="status">Loading status</param>
        /// <param name="loadingTimeMs">Loading time in milliseconds</param>
        public void RecordViewLoadingEvent(string viewId, string viewType, ViewLoadingType loadingType, ViewLoadingStatus status, long loadingTimeMs = 0)
        {
            var loadingEvent = new ViewLoadingEvent
            {
                ViewId = viewId,
                ViewType = viewType,
                LoadingType = loadingType,
                StartTime = DateTime.UtcNow,
                EndTime = DateTime.UtcNow,
                LoadingTimeMs = loadingTimeMs,
                Status = status
            };

            var events = _loadingEvents.GetOrAdd(viewId, _ => new List<ViewLoadingEvent>());
            lock (events)
            {
                events.Add(loadingEvent);
                if (events.Count > _maxEventsPerView)
                {
                    events.RemoveAt(0);
                }
            }
        }

        /// <summary>
        /// Gets loading metrics for a specific view.
        /// </summary>
        /// <param name="viewId">View identifier</param>
        /// <returns>Loading metrics or null if not found</returns>
        public ViewLoadingMetrics? GetViewMetrics(string viewId)
        {
            return _loadingMetrics.TryGetValue(viewId, out var metrics) ? metrics : null;
        }

        /// <summary>
        /// Gets loading metrics for all tracked views.
        /// </summary>
        /// <returns>Dictionary of view IDs and their metrics</returns>
        public Dictionary<string, ViewLoadingMetrics> GetAllViewMetrics()
        {
            return new Dictionary<string, ViewLoadingMetrics>(_loadingMetrics);
        }

        /// <summary>
        /// Gets loading events for a specific view.
        /// </summary>
        /// <param name="viewId">View identifier</param>
        /// <returns>List of loading events</returns>
        public List<ViewLoadingEvent> GetViewLoadingEvents(string viewId)
        {
            if (_loadingEvents.TryGetValue(viewId, out var events))
            {
                lock (events)
                {
                    return new List<ViewLoadingEvent>(events);
                }
            }
            return new List<ViewLoadingEvent>();
        }

        /// <summary>
        /// Gets overall performance summary.
        /// </summary>
        /// <returns>Performance summary</returns>
        public ViewLoadingPerformanceSummary GetPerformanceSummary()
        {
            var allMetrics = GetAllViewMetrics();
            
            if (allMetrics.Count == 0)
            {
                return new ViewLoadingPerformanceSummary();
            }

            var totalLoads = _totalViewLoads;
            var fastLoads = _fastViewLoads;
            var backgroundLoads = _backgroundViewLoads;

            var averageLoadingTime = allMetrics.Values
                .Where(m => m.SuccessfulLoads > 0)
                .Average(m => m.AverageLoadingTimeMs);

            var successRate = allMetrics.Values.Sum(m => m.SuccessfulLoads) / 
                             (double)Math.Max(1, allMetrics.Values.Sum(m => m.TotalLoadAttempts));

            return new ViewLoadingPerformanceSummary
            {
                TotalViewsTracked = allMetrics.Count,
                TotalLoadAttempts = totalLoads,
                FastLoadRatio = totalLoads > 0 ? (double)fastLoads / totalLoads : 0.0,
                BackgroundLoadRatio = totalLoads > 0 ? (double)backgroundLoads / totalLoads : 0.0,
                AverageLoadingTimeMs = averageLoadingTime,
                SuccessRate = successRate,
                PerformanceScore = CalculatePerformanceScore(averageLoadingTime, fastLoads, totalLoads, backgroundLoads),
                TargetsMet = new ViewLoadingTargets
                {
                    LoadingTimeTarget = averageLoadingTime <= TargetLoadingTimeMs,
                    BackgroundEfficiencyTarget = (totalLoads > 0 ? (double)backgroundLoads / totalLoads : 0.0) >= TargetBackgroundEfficiencyRatio
                }
            };
        }

        /// <summary>
        /// Resets all monitoring data.
        /// </summary>
        public void ResetMonitoring()
        {
            _loadingMetrics.Clear();
            _loadingEvents.Clear();
            Interlocked.Exchange(ref _totalViewLoads, 0);
            Interlocked.Exchange(ref _fastViewLoads, 0);
            Interlocked.Exchange(ref _backgroundViewLoads, 0);

            LoggingService.LogInfo("View loading monitoring data reset", "ViewLoadingMonitoringService");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Calculates overall performance score.
        /// </summary>
        private double CalculatePerformanceScore(double avgLoadingTime, long fastLoads, long totalLoads, long backgroundLoads)
        {
            var timeScore = avgLoadingTime <= TargetLoadingTimeMs ? 40 : 
                           Math.Max(0, 40 - (avgLoadingTime - TargetLoadingTimeMs) / 10);
            
            var fastLoadScore = totalLoads > 0 ? (double)fastLoads / totalLoads * 30 : 0;
            
            var backgroundScore = totalLoads > 0 ? (double)backgroundLoads / totalLoads * 30 : 0;

            return Math.Min(100, timeScore + fastLoadScore + backgroundScore);
        }

        /// <summary>
        /// Generates periodic performance report.
        /// </summary>
        private void GeneratePerformanceReport(object? state)
        {
            try
            {
                var summary = GetPerformanceSummary();
                
                LoggingService.LogInfo($"View Loading Performance Report - " +
                    $"Views: {summary.TotalViewsTracked}, " +
                    $"Avg Load: {summary.AverageLoadingTimeMs:F1}ms, " +
                    $"Fast Ratio: {summary.FastLoadRatio:P1}, " +
                    $"Background Ratio: {summary.BackgroundLoadRatio:P1}, " +
                    $"Score: {summary.PerformanceScore:F1}", 
                    "ViewLoadingMonitoringService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error generating performance report: {ex.Message}", "ViewLoadingMonitoringService");
            }
        }

        /// <summary>
        /// Performs periodic cleanup of old data.
        /// </summary>
        private void PerformCleanup(object? state)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow - _eventRetentionTime;
                var cleanedEvents = 0;

                foreach (var kvp in _loadingEvents)
                {
                    var events = kvp.Value;
                    lock (events)
                    {
                        var initialCount = events.Count;
                        events.RemoveAll(e => e.StartTime < cutoffTime);
                        cleanedEvents += initialCount - events.Count;
                    }
                }

                if (cleanedEvents > 0)
                {
                    LoggingService.LogDebug($"Cleaned up {cleanedEvents} old loading events", "ViewLoadingMonitoringService");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during cleanup: {ex.Message}", "ViewLoadingMonitoringService");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the ViewLoadingMonitoringService.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _reportingTimer?.Dispose();
                _cleanupTimer?.Dispose();
                _disposed = true;

                LoggingService.LogInfo("ViewLoadingMonitoringService disposed", "ViewLoadingMonitoringService");
            }
        }

        #endregion
    }

    /// <summary>
    /// Loading metrics for a specific view.
    /// </summary>
    public class ViewLoadingMetrics
    {
        public string ViewId { get; set; } = string.Empty;
        public string ViewType { get; set; } = string.Empty;

        private long _totalLoadAttempts;
        private long _successfulLoads;
        private long _failedLoads;
        private long _fastLoads;
        private long _backgroundLoads;
        private long _totalLoadingTimeMs;

        public long TotalLoadAttempts => _totalLoadAttempts;
        public long SuccessfulLoads => _successfulLoads;
        public long FailedLoads => _failedLoads;
        public long FastLoads => _fastLoads;
        public long BackgroundLoads => _backgroundLoads;
        public long TotalLoadingTimeMs => _totalLoadingTimeMs;

        public double AverageLoadingTimeMs { get; set; }
        public DateTime LastLoadTime { get; set; }

        // Internal methods for updating metrics
        internal void IncrementTotalLoadAttempts() => Interlocked.Increment(ref _totalLoadAttempts);
        internal void IncrementSuccessfulLoads() => Interlocked.Increment(ref _successfulLoads);
        internal void IncrementFailedLoads() => Interlocked.Increment(ref _failedLoads);
        internal void IncrementFastLoads() => Interlocked.Increment(ref _fastLoads);
        internal void IncrementBackgroundLoads() => Interlocked.Increment(ref _backgroundLoads);
        internal void AddTotalLoadingTime(long timeMs) => Interlocked.Add(ref _totalLoadingTimeMs, timeMs);
    }

    /// <summary>
    /// Represents a view loading event.
    /// </summary>
    public class ViewLoadingEvent
    {
        public string ViewId { get; set; } = string.Empty;
        public string ViewType { get; set; } = string.Empty;
        public ViewLoadingType LoadingType { get; set; }
        public ViewLoadingStatus Status { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public long LoadingTimeMs { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Overall performance summary for view loading.
    /// </summary>
    public class ViewLoadingPerformanceSummary
    {
        public int TotalViewsTracked { get; set; }
        public long TotalLoadAttempts { get; set; }
        public double FastLoadRatio { get; set; }
        public double BackgroundLoadRatio { get; set; }
        public double AverageLoadingTimeMs { get; set; }
        public double SuccessRate { get; set; }
        public double PerformanceScore { get; set; }
        public ViewLoadingTargets TargetsMet { get; set; } = new ViewLoadingTargets();
    }

    /// <summary>
    /// Performance targets for view loading.
    /// </summary>
    public class ViewLoadingTargets
    {
        public bool LoadingTimeTarget { get; set; }
        public bool BackgroundEfficiencyTarget { get; set; }
    }

    /// <summary>
    /// Types of view loading operations.
    /// </summary>
    public enum ViewLoadingType
    {
        Immediate,
        Background,
        Lazy,
        Preload,
        OnDemand
    }

    /// <summary>
    /// Status of view loading operations.
    /// </summary>
    public enum ViewLoadingStatus
    {
        InProgress,
        Completed,
        Failed,
        Cancelled
    }
}
