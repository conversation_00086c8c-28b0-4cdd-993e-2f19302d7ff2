using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using UFU2.Common;
using UFU2.Common.Utilities;

namespace UFU2.Services
{
    /// <summary>
    /// Enhanced search service providing word frequency-based ranking, exact and partial matching
    /// for improved search accuracy. Focuses on word-level matching rather than fuzzy algorithms.
    /// Integrates with TextNormalizationHelper for consistent text processing.
    /// </summary>
    public class EnhancedSearchService
    {
        #region Private Fields

        private readonly IMemoryCache _searchCache;
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(10);
        private int _cacheHits = 0;
        private int _cacheMisses = 0;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the EnhancedSearchService class.
        /// </summary>
        public EnhancedSearchService()
        {
            _searchCache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 200,
                CompactionPercentage = 0.25
            });

            LoggingService.LogInfo("EnhancedSearchService initialized with fuzzy matching capabilities", "EnhancedSearchService");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Performs enhanced search with fuzzy matching and similarity scoring.
        /// Combines exact matches, partial matches, and fuzzy matches for comprehensive results.
        /// </summary>
        /// <typeparam name="T">Type of items to search</typeparam>
        /// <param name="items">Collection of items to search</param>
        /// <param name="searchTerm">Search term</param>
        /// <param name="textSelector">Function to extract searchable text from items</param>
        /// <param name="maxResults">Maximum number of results to return</param>
        /// <param name="minSimilarity">Minimum similarity score (0.0 to 1.0)</param>
        /// <returns>Ranked search results with similarity scores</returns>
        public async Task<List<SearchResult<T>>> SearchAsync<T>(
            IEnumerable<T> items,
            string searchTerm,
            Func<T, string> textSelector,
            int maxResults = 50,
            double minSimilarity = 0.3)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || items == null)
            {
                return new List<SearchResult<T>>();
            }

            try
            {
                string cacheKey = $"enhanced_search_{searchTerm.ToLowerInvariant()}_{maxResults}_{minSimilarity}";

                // Check cache first
                if (_searchCache.TryGetValue(cacheKey, out List<SearchResult<T>> cachedResults))
                {
                    _cacheHits++;
                    // Only log cache hits for complex searches or periodically
                    if (_cacheHits % 10 == 0 || searchTerm.Length > 20)
                    {
                        LoggingService.LogDebug($"Enhanced search cache performance: {_cacheHits} hits, {_cacheMisses} misses", "EnhancedSearchService");
                    }
                    return cachedResults;
                }

                _cacheMisses++;

                // Normalize search term
                string normalizedSearchTerm = TextNormalizationHelper.NormalizeForSearch(searchTerm);
                string[] searchTerms = TextNormalizationHelper.PrepareSearchTerms(searchTerm);

                // Generate conjunction variations for better Arabic text matching
                string[] searchVariations = TextNormalizationHelper.GenerateConjunctionVariations(searchTerm);

                var results = new List<SearchResult<T>>();

                await Task.Run(() =>
                {
                    foreach (var item in items)
                    {
                        string itemText = textSelector(item);
                        if (string.IsNullOrWhiteSpace(itemText))
                            continue;

                        string normalizedItemText = TextNormalizationHelper.NormalizeForSearch(itemText);

                        // Calculate similarity score using best match from search variations
                        double similarity = CalculateOverallSimilarityWithVariations(
                            normalizedSearchTerm, normalizedItemText, searchTerms, searchVariations);

                        if (similarity >= minSimilarity)
                        {
                            results.Add(new SearchResult<T>
                            {
                                Item = item,
                                SimilarityScore = similarity,
                                MatchType = DetermineMatchType(normalizedSearchTerm, normalizedItemText),
                                OriginalText = itemText,
                                NormalizedText = normalizedItemText
                            });
                        }
                    }

                    // Sort by similarity score (descending) and then by match type priority
                    results = results
                        .OrderByDescending(r => r.SimilarityScore)
                        .ThenBy(r => (int)r.MatchType)
                        .Take(maxResults)
                        .ToList();
                });

                // Cache the results
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = _cacheExpiration,
                    Size = 1
                };
                _searchCache.Set(cacheKey, results, cacheOptions);

                // Only log search completion for significant searches or periodically
                if (results.Count > 5 || searchTerm.Length > 15 || _cacheMisses % 5 == 0)
                {
                    LoggingService.LogDebug($"Enhanced search completed for '{searchTerm}': {results.Count} results", "EnhancedSearchService");
                }

                return results;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in enhanced search for '{searchTerm}': {ex.Message}", "EnhancedSearchService");
                return new List<SearchResult<T>>();
            }
        }

        // Fuzzy matching methods removed - now using word frequency-based ranking

        /// <summary>
        /// Clears the search cache to free memory.
        /// </summary>
        public void ClearCache()
        {
            try
            {
                _searchCache.Dispose();
                LoggingService.LogInfo($"Enhanced search cache cleared. Final stats - Hits: {_cacheHits}, Misses: {_cacheMisses}", "EnhancedSearchService");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing enhanced search cache: {ex.Message}", "EnhancedSearchService");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Calculates overall similarity using search variations for better Arabic conjunction matching.
        /// </summary>
        private double CalculateOverallSimilarityWithVariations(string searchTerm, string itemText,
            string[] searchTerms, string[] searchVariations)
        {
            double bestScore = 0.0;

            // Try each search variation and use the best score
            foreach (string variation in searchVariations)
            {
                string normalizedVariation = TextNormalizationHelper.NormalizeForSearch(variation);
                double score = CalculateOverallSimilarity(normalizedVariation, itemText, searchTerms);
                if (score > bestScore)
                {
                    bestScore = score;
                }
            }

            // Also try the original search term
            double originalScore = CalculateOverallSimilarity(searchTerm, itemText, searchTerms);
            return Math.Max(bestScore, originalScore);
        }

        /// <summary>
        /// Calculates overall similarity considering multiple factors with enhanced Arabic text support.
        /// Prioritizes results containing more matching terms and handles Arabic conjunctions properly.
        /// </summary>
        private double CalculateOverallSimilarity(string searchTerm, string itemText, string[] searchTerms)
        {
            // Exact match gets highest score
            if (string.Equals(searchTerm, itemText, StringComparison.OrdinalIgnoreCase))
                return 1.0;

            // Extract words from both search term and item text for better matching
            string[] searchWords = TextNormalizationHelper.ExtractArabicWords(searchTerm, false);
            string[] itemWords = TextNormalizationHelper.ExtractArabicWords(itemText, false);

            // Calculate comprehensive term matching score
            double termMatchScore = CalculateTermMatchingScore(searchWords, itemWords, searchTerms);
            if (termMatchScore > 0.5) // High confidence match
                return termMatchScore;

            // Contains match gets high score
            if (itemText.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                return 0.9;

            // Starts with match gets good score
            if (itemText.StartsWith(searchTerm, StringComparison.OrdinalIgnoreCase))
                return 0.85;

            // Multi-term matching with enhanced scoring
            if (searchTerms.Length > 1)
            {
                int matchingTerms = searchTerms.Count(term => itemText.Contains(term, StringComparison.OrdinalIgnoreCase));
                if (matchingTerms > 0)
                {
                    double termMatchRatio = (double)matchingTerms / searchTerms.Length;
                    // Boost score for higher term match ratios
                    double baseScore = 0.7 * termMatchRatio;
                    double bonusScore = matchingTerms >= 3 ? 0.15 : (matchingTerms == 2 ? 0.1 : 0.05);
                    return Math.Min(0.95, baseScore + bonusScore);
                }
            }

            // Word frequency-based matching (no fuzzy matching)
            // Use the enhanced term matching score as the primary ranking method
            double termMatchingScore = CalculateTermMatchingScore(searchWords, itemWords, searchTerms);

            return termMatchingScore;
        }

        /// <summary>
        /// Calculates comprehensive term matching score with Arabic text support.
        /// Prioritizes results containing more matching search terms.
        /// </summary>
        private double CalculateTermMatchingScore(string[] searchWords, string[] itemWords, string[] searchTerms)
        {
            if (searchWords.Length == 0 || itemWords.Length == 0)
                return 0.0;

            int exactMatches = 0;
            int partialMatches = 0;
            int totalSearchWords = searchWords.Length;

            foreach (string searchWord in searchWords)
            {
                bool foundExactMatch = false;
                bool foundPartialMatch = false;

                foreach (string itemWord in itemWords)
                {
                    // Check for exact match
                    if (string.Equals(searchWord, itemWord, StringComparison.OrdinalIgnoreCase))
                    {
                        exactMatches++;
                        foundExactMatch = true;
                        break;
                    }
                    // Check for partial match (contains)
                    else if (itemWord.Contains(searchWord, StringComparison.OrdinalIgnoreCase) ||
                             searchWord.Contains(itemWord, StringComparison.OrdinalIgnoreCase))
                    {
                        foundPartialMatch = true;
                    }
                }

                if (!foundExactMatch && foundPartialMatch)
                {
                    partialMatches++;
                }
            }

            // Calculate score based on matches
            double exactMatchRatio = (double)exactMatches / totalSearchWords;
            double partialMatchRatio = (double)partialMatches / totalSearchWords;
            double totalMatchRatio = exactMatchRatio + (partialMatchRatio * 0.7);

            // Boost score for high match ratios
            if (totalMatchRatio >= 0.8) return 0.95; // Very high match
            if (totalMatchRatio >= 0.6) return 0.85; // High match
            if (totalMatchRatio >= 0.4) return 0.75; // Good match
            if (totalMatchRatio >= 0.2) return 0.65; // Fair match

            return totalMatchRatio * 0.6; // Base score for low matches
        }

        // CalculateBestWordMatch method removed - using word frequency-based ranking instead

        /// <summary>
        /// Determines the type of match for result ranking.
        /// </summary>
        private MatchType DetermineMatchType(string searchTerm, string itemText)
        {
            if (string.Equals(searchTerm, itemText, StringComparison.OrdinalIgnoreCase))
                return MatchType.Exact;

            if (itemText.StartsWith(searchTerm, StringComparison.OrdinalIgnoreCase))
                return MatchType.StartsWith;

            if (itemText.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                return MatchType.Contains;

            return MatchType.Fuzzy;
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Represents a search result with similarity scoring and match type information.
    /// </summary>
    /// <typeparam name="T">Type of the search result item</typeparam>
    public class SearchResult<T>
    {
        public T Item { get; set; }
        public double SimilarityScore { get; set; }
        public MatchType MatchType { get; set; }
        public string OriginalText { get; set; }
        public string NormalizedText { get; set; }
    }

    /// <summary>
    /// Enumeration of match types for result ranking.
    /// </summary>
    public enum MatchType
    {
        Exact = 0,      // Highest priority
        StartsWith = 1,
        Contains = 2,
        Fuzzy = 3       // Lowest priority
    }

    #endregion
}
