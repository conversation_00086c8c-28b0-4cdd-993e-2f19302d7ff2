<UserControl
    x:Class="UFU2.Views.Dialogs.ActivityStatusUpdateDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    d:DesignHeight="400"
    d:DesignWidth="500"
    AutomationProperties.HelpText="Activity status update information dialog"
    AutomationProperties.ItemType="Dialog"
    AutomationProperties.Name="Activity Status Update Dialog"
    FlowDirection="RightToLeft"
    Focusable="True"
    IsTabStop="True"
    SnapsToDevicePixels="True"
    UseLayoutRounding="True"
    mc:Ignorable="d">

    <materialDesign:Card
        Width="270"
        Height="330"
        Padding="0"
        Style="{DynamicResource DialogBaseCardStyle}">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  Header section  -->
            <materialDesign:Card
                Grid.Row="0"
                Margin="0"
                Style="{DynamicResource HeaderCardStyle}">
                <TextBlock
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    Style="{StaticResource HeadlineStyle}"
                    Text="{Binding DialogHeader}" />
            </materialDesign:Card>

            <!--  Content section  -->
            <Grid Grid.Row="1" Margin="12">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  Update date input  -->
                <TextBox
                    x:Name="UpdateDateTextBox"
                    Grid.Row="0"
                    Margin="0,8"
                    materialDesign:HintAssist.Hint="تاريخ التحديث (DD/MM/YYYY)"
                    materialDesign:TextFieldAssist.HasClearButton="True"
                    materialDesign:TextFieldAssist.SuffixText="📅"
                    MaxLength="10"
                    Style="{StaticResource UnderlineTextBoxStyle}"
                    Text="{Binding UpdateDate, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                    ToolTip="أدخل تاريخ التحديث بصيغة DD/MM/YYYY أو xx/xx/yyyy للتاريخ الجزئي أو xx/xx/xxxx للتاريخ غير المعروف" />

                <!--  Update note input  -->

                <TextBox
                    x:Name="UpdateNoteTextBox"
                    Grid.Row="2"
                    Height="108"
                    materialDesign:HintAssist.Hint="ملاحظات التحديث (اختياري)"
                    materialDesign:HintAssist.HintHorizontalAlignment="Center"
                    materialDesign:TextFieldAssist.HasClearButton="True"
                    materialDesign:TextFieldAssist.TextBoxViewMargin="12,0"
                    materialDesign:TextFieldAssist.TextBoxViewVerticalAlignment="Top"
                    AcceptsTab="True"
                    MaxLength="500"
                    Style="{StaticResource BaseOutlinedTextBoxStyle}"
                    Text="{Binding UpdateNote, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                    TextWrapping="Wrap"
                    ToolTip="أدخل ملاحظات إضافية حول تحديث النشاط (حد أقصى 500 حرف)"
                    VerticalScrollBarVisibility="Auto" />
            </Grid>


            <!--  Action buttons section  -->
            <userControls:SaveCancelButtonsControl
                Grid.Row="2"
                CancelCommand="{Binding CancelCommand}"
                CancelTooltip="إلغاء التغييرات وإغلاق النافذة"
                SaveCommand="{Binding SaveCommand}"
                SaveTooltip="حفظ معلومات التحديث" />
        </Grid>
    </materialDesign:Card>
</UserControl>
