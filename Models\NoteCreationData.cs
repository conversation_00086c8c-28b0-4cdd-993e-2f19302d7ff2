using System;

namespace UFU2.Models
{
    /// <summary>
    /// Data transfer object for note creation operations.
    /// Contains all necessary information to create a new note with priority level.
    /// </summary>
    public class NoteCreationData
    {
        /// <summary>
        /// Gets or sets the content/text of the note.
        /// Supports Arabic RTL text input.
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the priority level of the note.
        /// 0 = Normal (Green), 1 = Medium (Orange), 2 = High (Red).
        /// Default is 0 (normal priority).
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// Initializes a new instance of the NoteCreationData class.
        /// </summary>
        public NoteCreationData()
        {
        }

        /// <summary>
        /// Initializes a new instance of the NoteCreationData class with specified content.
        /// </summary>
        /// <param name="content">The note content</param>
        /// <param name="priority">The priority level (optional, defaults to 0)</param>
        public NoteCreationData(string content, int priority = 0)
        {
            Content = content ?? string.Empty;
            Priority = priority;
        }

        /// <summary>
        /// Validates the note creation data.
        /// </summary>
        /// <returns>True if the note data is valid</returns>
        public bool IsValid()
        {
            // Content is required and must not be empty or whitespace
            if (string.IsNullOrWhiteSpace(Content))
                return false;

            // Content should have meaningful length (at least 1 character after trimming)
            if (Content.Trim().Length == 0)
                return false;

            // Priority should be valid (0, 1, or 2)
            if (Priority < 0 || Priority > 2)
                return false;

            // Priority should be non-negative
            if (Priority < 0)
                return false;

            return true;
        }

        /// <summary>
        /// Gets the Arabic display name for the priority level.
        /// </summary>
        public string PriorityDisplayName => GetPriorityDisplayName(Priority);

        /// <summary>
        /// Gets the color name for the priority level.
        /// </summary>
        public string PriorityColorName => GetPriorityColorName(Priority);

        /// <summary>
        /// Gets the color brush name for the priority level for XAML binding.
        /// </summary>
        public string PriorityColorBrush => GetPriorityColorBrush(Priority);

        /// <summary>
        /// Creates a copy of the note creation data.
        /// </summary>
        /// <returns>A new NoteCreationData instance with the same values</returns>
        public NoteCreationData Clone()
        {
            return new NoteCreationData
            {
                Content = this.Content,
                Priority = this.Priority
            };
        }

        /// <summary>
        /// Returns a string representation of the note creation data.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            var truncatedContent = Content.Length > 50 ? Content.Substring(0, 50) + "..." : Content;
            return $"{PriorityDisplayName} - {truncatedContent}";
        }

        /// <summary>
        /// Gets the Arabic display name for the specified priority level.
        /// </summary>
        /// <param name="priority">The priority level</param>
        /// <returns>The Arabic display name</returns>
        public static string GetPriorityDisplayName(int priority)
        {
            return priority switch
            {
                0 => "عادي",      // Normal
                1 => "متوسط",     // Medium
                2 => "عالي",      // High
                _ => "عادي"       // Default to normal
            };
        }

        /// <summary>
        /// Gets the color name for the specified priority level.
        /// </summary>
        /// <param name="priority">The priority level</param>
        /// <returns>The color name</returns>
        public static string GetPriorityColorName(int priority)
        {
            return priority switch
            {
                0 => "أخضر",      // Green
                1 => "برتقالي",   // Orange
                2 => "أحمر",      // Red
                _ => "أخضر"       // Default to green
            };
        }

        /// <summary>
        /// Gets the color brush name for the specified priority level.
        /// </summary>
        /// <param name="priority">The priority level</param>
        /// <returns>The color brush name for XAML binding</returns>
        public static string GetPriorityColorBrush(int priority)
        {
            return priority switch
            {
                0 => "Green",     // Normal priority
                1 => "Orange",    // Medium priority
                2 => "Red",       // High priority
                _ => "Green"      // Default to green
            };
        }
    }

    /// <summary>
    /// Data transfer object for complete note data retrieval.
    /// Contains all note information including timestamps.
    /// </summary>
    public class NoteData
    {
        /// <summary>
        /// Gets or sets the activity UID this note belongs to.
        /// </summary>
        public string ActivityUid { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the content/text of the note.
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the priority level of the note.
        /// 0 = Normal (Green), 1 = Medium (Orange), 2 = High (Red).
        /// </summary>
        public int Priority { get; set; } = 0;
        /// <summary>
        /// Gets or sets the note category (optional).
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Gets or sets the note created date (for compatibility with older models, optional).
        /// </summary>
        public string? CreatedDate { get; set; }


        /// <summary>
        /// Gets or sets the creation timestamp.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the last update timestamp.
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Gets the formatted creation date for display (DD/MM/YYYY : HH:mm:ss).
        /// </summary>
        public string FormattedCreatedDate => CreatedAt.ToString("dd/MM/yyyy : HH:mm:ss");

        /// <summary>
        /// Gets the formatted modification date for display (DD/MM/YYYY : HH:mm:ss).
        /// </summary>
        public string FormattedModifiedDate => UpdatedAt.ToString("dd/MM/yyyy : HH:mm:ss");

        /// <summary>
        /// Gets the display date (shows modified date if different from created date, otherwise created date).
        /// </summary>
        public string DisplayDate => UpdatedAt.Date != CreatedAt.Date ? FormattedModifiedDate : FormattedCreatedDate;

        /// <summary>
        /// Gets the Arabic display name for the priority level.
        /// </summary>
        public string PriorityDisplayName => NoteCreationData.GetPriorityDisplayName(Priority);

        /// <summary>
        /// Gets the color name for the priority level.
        /// </summary>
        public string PriorityColorName => NoteCreationData.GetPriorityColorName(Priority);

        /// <summary>
        /// Gets the color brush name for the priority level for XAML binding.
        /// </summary>
        public string PriorityColorBrush => NoteCreationData.GetPriorityColorBrush(Priority);

        /// <summary>
        /// Gets whether the note has valid content.
        /// </summary>
        public bool HasContent => !string.IsNullOrWhiteSpace(Content);

        /// <summary>
        /// Gets whether the note content is empty.
        /// </summary>
        public bool IsEmpty => string.IsNullOrWhiteSpace(Content);

        /// <summary>
        /// Initializes a new instance of the NoteData class.
        /// </summary>
        public NoteData()
        {
            CreatedAt = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// Creates a copy of the note data.
        /// </summary>
        /// <returns>A new NoteData instance with the same values</returns>
        public NoteData Clone()
        {
            return new NoteData
            {
                ActivityUid = this.ActivityUid,
                Content = this.Content,
                Priority = this.Priority,
                CreatedAt = this.CreatedAt,
                UpdatedAt = this.UpdatedAt
            };
        }

        /// <summary>
        /// Returns a string representation of the note data.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            var truncatedContent = Content.Length > 50 ? Content.Substring(0, 50) + "..." : Content;
            return $"{PriorityDisplayName} - {truncatedContent} ({DisplayDate})";
        }

    }
}